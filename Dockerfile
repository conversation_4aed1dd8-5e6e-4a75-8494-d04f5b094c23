ARG JAVA_BASE_IMAGE
FROM ${JAVA_BASE_IMAGE}

ENV https_proxy=
ENV http_proxy=

# Add a volume pointing to /tmp
VOLUME /tmp

# Make port 5555 available to the world outside this container
EXPOSE 5555

ARG JAVA_OPTS
ENV JAVA_OPTS=$JAVA_OPTS
ENV APP_NAME='FIPC-App-Config'

RUN mkdir -p /usr/app/appconfig/logs
RUN mkdir -p /usr/app/appconfig/lib
RUN mkdir -p /usr/app/tbacutoffdatereports
RUN mkdir -p /usr/app/certificates
RUN mkdir -p /usr/app/commonutillogs

COPY src/main/resources/application.yml /usr/app/appconfig/lib/
COPY src/main/resources/logback-spring.xml /usr/app/appconfig/lib/
COPY src/main/resources/stv-panel.xml /usr/app/appconfig/lib/

WORKDIR /usr/app/appconfig/lib/
ADD ./newrelic/newrelic.jar .
ADD ./newrelic/newrelic.yml .
#ADD ./version.txt/ .

# Add the application's jar to the container
COPY target/fipc-app-config-0.0.1-SNAPSHOT.jar fipc-app-config-0.0.1-SNAPSHOT.jar

ENTRYPOINT exec java -javaagent:/usr/app/appconfig/lib/newrelic.jar $JAVA_OPTS -jar -Djavax.net.ssl.trustStore=/usr/app/certificates/ssl.keystore -Djavax.net.ssl.trustStorePassword=FIPC@2022 -Dnewrelic.license_key="${NEW_RELIC_LICENSE_KEY}" -Dnewrelic.config.app_name="${NEW_RELIC_ENV}_${APP_NAME}" -Dspring.profiles.active="${SPRING_PROFILES_ACTIVE}" fipc-app-config-0.0.1-SNAPSHOT.jar
