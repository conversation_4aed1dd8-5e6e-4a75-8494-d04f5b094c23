package com.wipro.fipc.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.entity.TemplateReportLayOut;
import com.wipro.fipc.model.LabellingAssociation;
import com.wipro.fipc.model.LabellingReportRecord;
import com.wipro.fipc.model.TemplateReportLayout;
import com.wipro.fipc.model.TemplateReportModel;
import com.wipro.fipc.model.TemplateReportRequest;

//@RunWith(MockitoJUnitRunner.class)
public class TemplateReportServiceImplTest {

//	@InjectMocks
//	private TemplateReportServiceImpl iTemplateReportServiceImpl;
//	
//	@Mock
//	TemplateReportUploadDao templateReportUploadDao;
//
//	private TemplateReportServiceImpl mock2;
//
//	@Before
//	public void setUp() {
//		mock2 = mock(TemplateReportServiceImpl.class);
//	}
//
//	@Test
//	public void testSaveTemplateLayoutRequest() {
//		TemplateReportServiceImpl mock2 = mock(TemplateReportServiceImpl.class);
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//		when(mock2.saveTemplate(any(MockMultipartFile.class), any(TemplateReportRequest.class)))
//				.thenReturn("data saved");
//
//		assertEquals("data saved", mock2.saveTemplate(file, new TemplateReportRequest()));
//	}
//
//	@Test
//	public void test() throws IOException {
//		TemplateReportServiceImpl mock2 = mock(TemplateReportServiceImpl.class);
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//		when(mock2.addTemplateReport(any(MockMultipartFile.class), any(TemplateReportRequest.class))).thenReturn(100l);
//		mock2.addTemplateReport(file, new TemplateReportRequest());
//		// assertEquals(100l, (Long)new Long(templateReport));
//
//		// assertEquals(, );
//
//	}
//
//	@Test
//	public void testGetTemplateLayout() throws URISyntaxException {
//		TemplateReportServiceImpl mock2 = mock(TemplateReportServiceImpl.class);
//		TemplateReportLayOut reportLayOut = new TemplateReportLayOut();
//		reportLayOut.setId(100l);
//		// reportLayOut.setActiveFlag("T");
//		reportLayOut.setTemplateReportName("report");
//		List<TemplateReportLayOut> list = new ArrayList<>();
//
//		list.add(reportLayOut);
//
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//		when(mock2.getTemplateLayout(any(MockMultipartFile.class), any(Long.class))).thenReturn(list);
//		assertEquals("100", mock2.getTemplateLayout(file, 10l).get(0).getId().toString());
//	}
//
//	@Test
//	public void testUpdateTemplateData() {
//		TemplateReportServiceImpl mock2 = mock(TemplateReportServiceImpl.class);
//		TemplateReportLayOut reportLayOut = new TemplateReportLayOut();
//		reportLayOut.setId(100l);
//		// reportLayOut.setActiveFlag("T");
//		reportLayOut.setTemplateReportName("report");
//		List<TemplateReportLayOut> list = new ArrayList<>();
//
//		list.add(reportLayOut);
//
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//		when(mock2.updateTemplateData(any(MockMultipartFile.class), any(TemplateReportRequest.class), anyString(),
//				anyString())).thenReturn("data updated");
//		assertEquals("data updated", mock2.updateTemplateData(file, new TemplateReportRequest(), "", ""));
//	}
//
//	@Test
//	public void testGetDbTemplate() {
//		TemplateReportServiceImpl mock2 = mock(TemplateReportServiceImpl.class);
//		TemplateReportRequest reportLayOut = new TemplateReportRequest();
//		reportLayOut.setId(100l);
//		// reportLayOut.setActiveFlag("T");
//		reportLayOut.setTemplateReportName("report");
//
//		Long id = 100l;
//		when(mock2.getDbTemplate(anyLong())).thenReturn(reportLayOut);
//		assertEquals("100", mock2.getDbTemplate(id).getId().toString());
//	}
//
//	@Test
//	public void testGetTemplateLayoutForLabelling() throws JsonProcessingException, URISyntaxException {
//		TemplateReportServiceImpl mock2 = mock(TemplateReportServiceImpl.class);
//		TemplateReportLayout templateReportLayout = new TemplateReportLayout();
//		templateReportLayout.setId("100");
//		templateReportLayout.setFiledType("report");
//		List<TemplateReportLayout> list = new ArrayList<>();
//		list.add(templateReportLayout);
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//		when(mock2.getTemplateLayoutForLabelling(any(MockMultipartFile.class))).thenReturn(list);
//
//		assertEquals("report", mock2.getTemplateLayoutForLabelling(file).get(0).getFiledType());
//	}
//
//	@Test
//	public void testGetLabellingReportForFile() throws JsonProcessingException, URISyntaxException {
//		LabellingReportRecord labelingReport = new LabellingReportRecord();
//		List<String> list = new ArrayList<>();
//		list.add("report");
//		list.add("id");
//		LabellingAssociation labellingAssociation = new LabellingAssociation();
//		labellingAssociation.setAssociation(list);
//		List<LabellingAssociation> labellingAssociationList = new ArrayList<>();
//		labellingAssociationList.add(labellingAssociation);
//		labelingReport.setLabellingAssociation(labellingAssociationList);
//
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//		when(mock2.getLabellingReportForFile(any(MockMultipartFile.class))).thenReturn(labelingReport);
//
//		assertEquals("id",
//				mock2.getLabellingReportForFile(file).getLabellingAssociation().get(0).getAssociation().get(1));
//	}
//
//	@Test
//	public void testGetTemplateLayoutRequest() {
//
//		TemplateReportLayOut layout = new TemplateReportLayOut();
//		layout.setActiveFlag('F');
//		layout.setId(100L);
//		List<TemplateReportLayOut> list = new ArrayList<>();
//		list.add(layout);
//
//		TemplateReportRequest trquest = new TemplateReportRequest();
//		when(mock2.getTemplateLayoutRequest(anyLong(), any(TemplateReportRequest.class), anyList())).thenReturn(list);
//
//		assertEquals('F', mock2.getTemplateLayoutRequest(100l, trquest, list).get(0).getActiveFlag());
//
//	}
//
//	@Test
//	public void testUpdateTemplate() {
//
//		TemplateReportLayOut reportLayOut = new TemplateReportLayOut();
//		reportLayOut.setId(100l);
//		// reportLayOut.setActiveFlag("T");
//		reportLayOut.setTemplateReportName("report");
//		List<TemplateReportLayOut> list = new ArrayList<>();
//
//		list.add(reportLayOut);
//
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//
//		when(mock2.updateTemplate(any(MockMultipartFile.class), any(TemplateReportRequest.class)))
//				.thenReturn("updated template");
//
//		assertEquals("updated template", mock2.updateTemplate(file, new TemplateReportRequest()));
//
//	}
//
//	@Test
//	public void testGetUpdatedTemplateLayout() {
//		TemplateReportLayOut layOut = new TemplateReportLayOut();
//		layOut.setActiveFlag('F');
//		layOut.setIdentifier("Report Table1");
//		List<TemplateReportLayOut> list = new ArrayList<>();
//		list.add(layOut);
//
//		MockMultipartFile file = new MockMultipartFile("file", "hello.txt", MediaType.TEXT_PLAIN_VALUE,
//				"Hello, World!".getBytes());
//		when(mock2.getUpdatedTemplateLayout(any(MockMultipartFile.class), anyLong())).thenReturn(list);
//		assertEquals('F', mock2.getUpdatedTemplateLayout(file, 100l).get(0).getActiveFlag());
//	}
//
//	@Test
//	public void testUpdateTemplateLayout() throws URISyntaxException {
//		TemplateReportLayOut layOut = new TemplateReportLayOut();
//		layOut.setActiveFlag('F');
//		layOut.setIdentifier("Report Table1");
//		List<TemplateReportLayOut> list = new ArrayList<>();
//		list.add(layOut);
//		when(mock2.updateTemplateLayout(anyLong(), anyList())).thenReturn(true);
//
//		assertTrue(mock2.updateTemplateLayout(100l, list));
//
//	}
//
//	@Test
//	public void testSaveLabellingReportData() {
//		when(mock2.saveLabellingReportData(any(TemplateReportModel.class))).thenReturn("data saved");
//
//		assertEquals("data saved", mock2.saveLabellingReportData(new TemplateReportModel()));
//	}
//
//	@Test
//	public void testDeleteTemplate() {
//		when(mock2.deleteTemplate(anyLong())).thenReturn("success");
//		assertEquals("success", mock2.deleteTemplate(100l));
//
//	}
//
//	@Test
//	public void testGetLabellingReportUpload() {
//		TemplateReportRequest value = new TemplateReportRequest();
//		TemplateReportModel model = new TemplateReportModel();
//		value.setActiveFlag('F');
//		value.setType("report");
//		when(mock2.getLabellingReportUpload(any(TemplateReportModel.class))).thenReturn(value);
//
//		assertEquals("report", mock2.getLabellingReportUpload(model).getType());
//
//	}
//
//	@Test
//	public void testGetLabellingReportLayout() throws JsonProcessingException {
//		TemplateReportLayOut layOut = new TemplateReportLayOut();
//		layOut.setActiveFlag('F');
//		layOut.setId(100l);
//		List<TemplateReportLayOut> list = new ArrayList<>();
//		list.add(layOut);
//		when(mock2.getLabellingReportLayout(anyLong(), any(TemplateReportModel.class))).thenReturn(list);
//
//		assertEquals('F', mock2.getLabellingReportLayout(100l, new TemplateReportModel()).get(0).getActiveFlag());
//
//	}
//
//	@Test
//	public void testGetLabellingReportData() throws IOException {
//		when(mock2.getLabellingReportData(anyLong())).thenReturn("Getting data");
//
//		assertEquals("Getting data", mock2.getLabellingReportData(100l));
//
//	}
//
//	@Test
//	public void testGetTemplateLayoutWithId() throws JsonProcessingException {
//		List<TemplateReportLayOut> value = new ArrayList<>();
//		TemplateReportLayOut reportLaout = new TemplateReportLayOut();
//		reportLaout.setActiveFlag('F');
//
//		value.add(reportLaout);
//
//		when(mock2.getTemplateLayout(anyLong(), anyLong())).thenReturn("success");
//
//		assertEquals("success", mock2.getTemplateLayout(100l, 100l));
//	}
//
//	@Test
//	public void testGetLabellingReport() throws IOException {
//		TemplateReportModel value = new TemplateReportModel();
//		value.setActiveFlag('T');
//		value.setBuId(100);
//		when(mock2.getLabellingReport(anyLong())).thenReturn(value);
//		assertEquals('T', mock2.getLabellingReport(100l).getActiveFlag());
//	}
//
//	@Test
//	public void testGetWhitelistName() throws IOException {
//		when(mock2.getWhitelistName(anyLong())).thenReturn("Getting WhiteList name");
//
//		assertEquals("Getting WhiteList name", mock2.getWhitelistName(100l));
//	}
//
//	@Test
//	public void testGetWhitelistId() {
//		when(mock2.getWhitelistId(anyString(), anyString())).thenReturn(100l);
//
//		assertEquals(100, mock2.getWhitelistId("100", "200"));
//	}
//
//	@Test
//	public void testGetSSNFromWhitelist() throws IOException {
//		when(mock2.getSSNFromWhitelist(anyLong())).thenReturn("Getting SSN");
//
//		assertEquals("Getting SSN", mock2.getSSNFromWhitelist(100l));
//	}
//
//	@Test
//	public void testGetSSNList() {
//		when(mock2.getSSNList(anyLong(), anyString())).thenReturn("Getting SSN list");
//		assertEquals("Getting SSN list", mock2.getSSNList(100l, "1000"));
//	}
//
//	@Test
//	public void testUpdateLabellingReportData() {
//		when(mock2.updateLabellingReportData(anyLong(), any(TemplateReportModel.class))).thenReturn("Success");
//		assertEquals("Success", mock2.updateLabellingReportData(100l, new TemplateReportModel()));
//	}

}
