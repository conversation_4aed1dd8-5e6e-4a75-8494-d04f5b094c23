package com.wipro.fipc.service.impl;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.common.TemplateReportLayOutDao;
import com.wipro.fipc.dao.common.TemplateReportUploadDao;
import com.wipro.fipc.entity.TemplateReportLayOut;
import com.wipro.fipc.pojo.TemplateReportUploadPojo;
import com.wipro.fipc.service.impl.TemplateReportServiceImpl;

@RunWith(MockitoJUnitRunner.class)
public class addUpdateTemplateTest {
	

	@InjectMocks
	TemplateReportServiceImpl templateReportServiceImpl;
	
	@Mock
	GenericDao<TemplateReportLayOut> genericDao;
	
	@Mock
	private BaseDao<TemplateReportLayOut> dao;
	
	@Mock
	private TemplateReportLayOutDao templateReportLayOutDao;
	
	@Mock
	private TemplateReportUploadDao templateReportUploadDao;
	
	@Test
	public void testAddTemplateLayout() {
		TemplateReportLayOut reportLayOut=new TemplateReportLayOut();
		reportLayOut.setId(100l);
		reportLayOut.setTemplateReportName("report");
		List<TemplateReportLayOut> list= new ArrayList<>();
		list.add(reportLayOut);
		when(templateReportServiceImpl.addTemplateLayout(anyList())).thenReturn(list);
		Long expected=100l;
		Long actual = genericDao.saveAllTempalteConfig(list).get(0).getId();
		assertEquals(expected,actual);
		assertEquals("report", genericDao.saveAllTempalteConfig(list).get(0).getTemplateReportName());
	}
	
	@Test
	public void testgetAllTemplates() {
		List<TemplateReportUploadPojo> value=new ArrayList<>();
		TemplateReportUploadPojo reportUpload = new TemplateReportUploadPojo();
		reportUpload.setActiveFlag('F');
		reportUpload.setClientId("111");
		
		value.add(reportUpload);
		
		when(templateReportServiceImpl.getAllTemplates()).thenReturn(value);
		
		assertEquals('F', templateReportUploadDao.getListOfData().get(0).getActiveFlag());
	}
	@Test
	public void testGetTemplateForClient() {
		List<TemplateReportUploadPojo> value=new ArrayList<>();
		TemplateReportUploadPojo reportUpload = new TemplateReportUploadPojo();
		reportUpload.setActiveFlag('F');
		reportUpload.setClientId("111");
		
		value.add(reportUpload);
		
		when(templateReportServiceImpl.getTemplateForClient(anyString())).thenReturn(value);
		
		assertEquals('F', templateReportUploadDao.getallDataWithAllClients("100").get(0).getActiveFlag());
	}
	
}
