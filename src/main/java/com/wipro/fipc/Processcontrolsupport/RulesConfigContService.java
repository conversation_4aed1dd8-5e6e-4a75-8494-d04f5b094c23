package com.wipro.fipc.Processcontrolsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.filelayout.RulesConfig;

@Service
public class RulesConfigContService {

	@Autowired
	GenericDao<RulesConfig> genericDao;

	protected static final String RULES_CONFIG = "RULES_CONFIG";
	protected static final String LAYOUT_SCHEMA = "layout_rule";

	public List<RulesConfig> findByColumn(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn", "Getting RulesConfig columnValues: " + columnValue);

		return genericDao.findByColumn(RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, columnName, columnValue);
	}
}
