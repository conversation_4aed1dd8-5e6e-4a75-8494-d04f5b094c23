package com.wipro.fipc.Processcontrolsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.tba.TbaUpdateConfig;

@Service
public class TbaUpdateConfigContService {

	@Autowired
	GenericDao<TbaUpdateConfig> genericDao;


	protected static final String TBA_UPDATE_CONFIG = "tba_update_config";
	protected static final String SCHEMA = "tba";

//  pass panel id as a parameter
	public List<TbaUpdateConfig> findByColumn(@PathVariable String columnName, @PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn", "TbaUpdateConfig   {}: " + columnName);
		return genericDao.findByColumn(TbaUpdateConfig.class, SCHEMA, TBA_UPDATE_CONFIG, columnName, columnValue);
	}
}
