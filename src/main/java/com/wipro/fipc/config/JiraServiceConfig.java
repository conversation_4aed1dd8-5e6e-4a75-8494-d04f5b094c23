package com.wipro.fipc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

@Data
@Component
@ConfigurationProperties(ignoreInvalidFields = true, ignoreUnknownFields = true)
public class JiraServiceConfig {

	@Value("${JIRA_BASE_URL}")
	private String jiraBaseUrl;
	
	@Value("${JIRA_CREDENTIALS}")
	private String jiraCredentials;
	
	@Value("${secretkey.path}")
	private String seceretKeyPath;
	
	@Value("${appName}")
	private String appName;

	@Value("${apiKey}")
	private String apiKey;

}
