package com.wipro.fipc.config;

import static org.jsoup.parser.Parser.unescapeEntities;

import java.io.IOException;

import org.owasp.html.HtmlPolicyBuilder;
import org.owasp.html.PolicyFactory;
import org.springframework.boot.jackson.JsonComponent;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;

@JsonComponent
public class DefaultJsonDeserializer extends JsonDeserializer<String> implements ContextualDeserializer {

	public static final PolicyFactory POLICY_FACTORY = new HtmlPolicyBuilder().allowElements("a", "p")
			.allowUrlProtocols("https").allowAttributes("class").onElements("p").toFactory();

	@Override
	public String deserialize(JsonParser parser, DeserializationContext ctxt) throws IOException {
		String value = parser.getValueAsString();
		if (!StringUtils.isEmpty(value)) {
			String originalWithUnescaped = unescapeUntilNoHtmlEntityFound(value);
			if (!unescapeEntities(POLICY_FACTORY.sanitize(originalWithUnescaped), true).equals(value)) {
				LoggerUtil.log(getClass(), Level.INFO, "method", "Invalied Input Data");
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid input data");
			}
		}
		return value;
	}

	@Override
	public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property)
			throws JsonMappingException {
		return this;
	}

	private String unescapeUntilNoHtmlEntityFound(final String value) {
		String unescaped = unescapeEntities(value, true);
		if (!unescaped.equals(value))
			return unescapeUntilNoHtmlEntityFound(unescaped);
		else
			return unescaped;
	}

}
