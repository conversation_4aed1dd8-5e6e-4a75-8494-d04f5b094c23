package com.wipro.fipc.config;

import java.util.NoSuchElementException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.config.model.ActivityMappings;

@Configuration
public class ExtractClientAndPanelData {

	@Autowired
	private ActivityMappings activityMappings;

	/**
	 * This method will return activityId if panelId and clientId found in
	 * configuration.
	 * 
	 * @param activityId
	 * @param clientId
	 * @param panelId
	 * @return
	 */
	public Integer getConfiguredActivity(Integer activityId, Integer clientId, Integer panelId) {
		Integer filteredActivityId = -1;

		try {
			if (activityId != null) {
				filteredActivityId = activityMappings
						.getMappings().stream().filter(act -> activityId.equals(act.getActivityId())
								&& act.getPanelIds().contains(panelId) && act.getClientIds().contains(clientId))
						.findAny().get().getActivityId();
			}
		} catch (NoSuchElementException nsee) {
			LoggerUtil.log(this.getClass(), Level.INFO, "isActivityValid", "activityId {0} configuration not found",
					activityId);
		}
		return filteredActivityId;
	}

}
