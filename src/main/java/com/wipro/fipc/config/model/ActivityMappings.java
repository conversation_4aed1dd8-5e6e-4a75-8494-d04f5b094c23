/**
 * This is activity configured composition
 * iterable through Activity
 */
package com.wipro.fipc.config.model;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix="activity")
public class ActivityMappings {
	
	private List<Activity> mappings;

	public List<Activity> getMappings() {
		return mappings;
	}

	public void setMappings(List<Activity> mappings) {
		this.mappings = mappings;
	}
}
