package com.wipro.fipc.config;

import org.dozer.DozerBeanMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import com.google.gson.Gson;
import com.google.gson.JsonParser;

@Configuration
public class ApplicationConfig {
	
	@Bean
	public DozerBeanMapper dozerBean(){
		return new DozerBeanMapper();
	}
	
	@Bean
	public RestTemplate restTemplateBean(){
		return new RestTemplate();
	}
	
	
	@Bean
	public JsonParser getJsonParser(){
		return new JsonParser();
	}

	@Bean
	public Gson getGson(){
		return new Gson();
	}
}
