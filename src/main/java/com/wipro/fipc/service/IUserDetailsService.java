package com.wipro.fipc.service;

import java.util.List;

import com.wipro.fipc.pojo.User;

public interface IUserDetailsService {

	String updateUserRoleConfig(String updatedBy,List<String> adid);
	List<String> getAllReportees(String adid,String role);
	
	String createUserByAdmin(User user);
	String updateUserByAdmin(User user);
	String deleteUserByAdmin(User user);
	List<User> getUserDetailsByAdmin(String adid);
	String updateUserSession(User user);
	
}
