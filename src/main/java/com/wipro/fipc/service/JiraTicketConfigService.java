package com.wipro.fipc.service;

import java.util.List;

import org.springframework.web.bind.annotation.PathVariable;

import com.wipro.fipc.model.ConfigStatusBO;
import com.wipro.fipc.model.EpicTypes;
import com.wipro.fipc.model.JiraApplicableClients;
import com.wipro.fipc.model.JiraIssueType;
import com.wipro.fipc.model.JiraProject;
import com.wipro.fipc.model.JiraTicketTaskConfig;
import com.wipro.fipc.model.JiraUserDetails;
import com.wipro.fipc.model.generated.ConfigStatusApproveBO;

public interface JiraTicketConfigService {

	public JiraTicketTaskConfig getByColumnJiraTikcetTask(@PathVariable String columnName,
			@PathVariable String columnValue);

	public List<String> newcreateUpdateJiraTicketConfig(JiraTicketTaskConfig jiraTicketTaskConfig, String appName,
			String sessionToken, String pjmId);

	public List<String> deleteJiraTicketConfig(JiraTicketTaskConfig ticketCreationUpdateConfig, String appName,
			String sessionToken, String pjmId);

	public String updateJiraConfigStatus(ConfigStatusBO configStatusBO, String appName, String sessionToken);

	public String updateApproveJiraConfigStatus(ConfigStatusApproveBO configStatusApproveBO, String appName,
			String sessionToken);

	public List<JiraProject> getProjectList(String businessUnit);

	public List<JiraApplicableClients> getApplicableClients(String key, String issueId, String businessUnit);

	public List<JiraIssueType> getIssueType(String key, String businessUnit);

	public List<JiraUserDetails> getUserDetails(String emailId, String businessUnit);

	public List<EpicTypes> getJiraEpicType(String key, String issueId, String businessUnit);

	public String getCustomFields(String projectKey, String issueId, String businessUnit);

}
