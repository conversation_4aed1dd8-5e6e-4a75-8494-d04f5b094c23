package com.wipro.fipc.service;

import java.util.List;

import com.wipro.fipc.model.EventHistoryUI;
import com.wipro.fipc.model.generated.EventHistoryMaster;

public interface ITbaEventHistoryService {
	
	
	public String saveEventHistInqConfigWithOutDuplicates(List<EventHistoryUI> tbaEventList);
	
	
	public String modifyEventHistInqConfigWithOutDuplicates(EventHistoryUI eventHistoryUI);
	
	public List<EventHistoryUI> getEventHistInqConfigByPJMID(String pjmID);
	
	public List<EventHistoryMaster> getEventHistoryMasterByClientID(String clientId);

	String deleteEventHistoryConfig(String entiryList);

	public String saveEventHistInqConfigWithOutDuplicates(List<EventHistoryUI> tbaList, String appName,
			String sessionToken);

	public String modifyEventHistInqConfigWithOutDuplicates(EventHistoryUI tbaList, String appName,
			String sessionToken);

	public String deleteEventHistoryConfig(List<EventHistoryUI> entity, String appName, String sessionToken);
	
	
}
