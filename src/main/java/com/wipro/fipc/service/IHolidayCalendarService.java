package com.wipro.fipc.service;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.wipro.fipc.model.HolidayCalendarResponse;

public interface IHolidayCalendarService {

	Object newputHolidayRequests(String request, String clientCode)
			throws JsonParseException, JsonMappingException, IOException;

	List<HolidayCalendarResponse> autoPopulateHolidayCalendarByYear(String clientCode, String selectedYear)
			throws ParseException;
	
	void getTemplate(String clientCode, HttpServletResponse holidayCalendar) throws IOException, ParseException;

}