package com.wipro.fipc.service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

import org.apache.poi.ss.usermodel.Workbook;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.google.gson.JsonParseException;
import com.wipro.fipc.exception.StatsMoniterCustomException;
import com.wipro.fipc.pojo.AllocationModel;
import com.wipro.fipc.pojo.AllocationResponse;
import com.wipro.fipc.pojo.AllocationUpdate;

public interface UIService {
	
	String fetchRequestList(String pjmId, String frequency);
	
	String createBarChart(String type, int frequency, String processJobMappingId);
		
    String createPieChart( String pjmId ,int frequency);
    
    public Workbook getTemplate(String idJson)throws StatsMoniterCustomException;
    public Workbook getTemplateData(String idJson)throws StatsMoniterCustomException;
    
    String fetchFilterRequestList(String requestBody) throws JsonGenerationException, JsonMappingException, IOException;
    
    String getBarChart(String requestBody) throws JsonParseException, JsonMappingException, IOException;
    
    String getPieChart(String requestBody) throws JsonParseException, JsonMappingException, IOException;
    
     AllocationResponse getTaskDetails(String adid);
    
    List<AllocationModel> updateRequestQueue(List<AllocationUpdate> request) throws URISyntaxException;
	
    AllocationResponse getModifyClient(String clientid,String clientCode);
}
