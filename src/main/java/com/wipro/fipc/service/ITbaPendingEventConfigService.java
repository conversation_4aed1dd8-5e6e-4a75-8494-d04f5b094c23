package com.wipro.fipc.service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.generated.TbaPendingEvent;

public interface ITbaPendingEventConfigService {

	String createTbaPendingEventConfig(List<TbaPendingEvent> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException;

	String updatePendingEventConfig(List<TbaPendingEvent> entity, String appName, String sessionToken);

	String getPendingEventDetails(String pjmId);

	String deletePendingEventConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken);

	String getPendingEventMasterDetails(String clientId);
}
