package com.wipro.fipc.service;

import java.io.IOException;
import java.util.List;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.entity.batch.KsdFileDetails;
import com.wipro.fipc.model.LayoutRequest;
import com.wipro.fipc.model.ReportSheet;

public interface IFileLayoutService {

	public Workbook getLayoutFile(String processJobMappingId, String fileName) throws IOException;

	public String createLayoutdata(LayoutRequest request);

	public String createLayoutdataNew(LayoutRequest request, String appName, String sessionToken, String action)
			throws JsonProcessingException;

	public String updateLayoutData(LayoutRequest request);

	public String updateLayoutDataNew(LayoutRequest request, String appName, String sessionToken);

	public String deleteFiles(String pjmId, String fileName, String adid);

	public String deleteFilesNew(String pjmId, String fileName, String appName, String sessionToken);

	public String getLayoutDetails(String pjmId, String fileName);

	public String getMfFieldNamesByFieldType(String processJobMappingId, String fieldType);

	public String getFileNames(String pjmId);

	public String getNames(String pjmId, String fileType);

	public String getRecIds(String pjmId, String fileName);

	public String getAllRecordIdentifier();

	public String getLayoutDetails(String pjmId);

	public String getAllFileNames(String pjmId);

	// july 10 req
	public String createInputReport(ReportSheet request);

	public String createInputReportNew(ReportSheet request, String appName, String sessionToken, String action)
			throws JsonProcessingException;

	public String updateInputReport(ReportSheet request);

	public String getAllMultiReport(String pjmId, String fileName);

	public String getDataBaseConfigMasterData(String pjmId);

	public String deleteFilesData(List<KsdFileDetails> ksdFileDetailsList);

	public String getLatestKsdFileDetailsByPjmId(String pjmId);
	
	public Workbook getTrustCodeMappingTemplate();

	public String parseTrustCodeMappingReport(MultipartFile file, String clientId) throws Exception;
	
	public String getTrustCodeMappingValues(String clientId, String clientName);

}