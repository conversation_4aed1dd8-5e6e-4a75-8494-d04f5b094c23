package com.wipro.fipc.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaWelfareConfigDto;

public interface TbaWelfareConfigService {

	String createTbaWelfareInquiryConfig(List<TbaWelfareConfigDto> entity, String appName, String sessionToken) throws JsonProcessingException;

	String updateTbaWelfareInquiryConfig(List<TbaWelfareConfigDto> entity, String appName, String sessionToken) throws JsonProcessingException;

	String deleteTbaWelfareInquiryConfig(List<CommonDeleteDTO> entity, String appName, String sessionToken);

	String getTbaWelfareInquiryConfig(String string, String pjmId) throws IllegalAccessException, InvocationTargetException, JsonProcessingException;

}
