package com.wipro.fipc.service;

import java.util.List;

import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.EmailPlaceHolder;

public interface INotificationService {
	
	public String getDetails(String columnName, String columnValue);
	public String deleteDetails(String deleteRequest);  
	public String createUpdate(Object commonRequest, String sessionToken);
	public String deleteNotificationData(String notifType, String entity);
	public String deleteNotificationDataVaptNew(String notifType, List<CommonDeleteDTO> entity,String appName, String sessionToken);
    public String createUpdateNotification(Object request, String sessionToken);
	public String createUpdateNotificationViptNew(Object request,String appName, String sessionToken);
	public String updateNotification(Object request, String sessionToken);
	public String updateNotificationVaptNew(Object request,String appName, String sessionToken);
	public List<EmailPlaceHolder> getEmailPlaceHolder();
	
}