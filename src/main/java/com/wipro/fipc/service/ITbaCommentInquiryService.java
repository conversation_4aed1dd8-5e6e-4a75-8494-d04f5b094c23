package com.wipro.fipc.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaCommentInquiry;

@Service
public interface ITbaCommentInquiryService {

	String saveWithOutDuplicates(List<TbaCommentInquiry> commentList);
	
	String modifyWithOutDuplicates(List<TbaCommentInquiry> commentList);
	
	List<TbaCommentInquiry> getTbaCommentData(@PathVariable String columnName,@PathVariable String columnValue,boolean updateFlag); 
	
	String deleteTbaCommentConfig(String entiryList);

	String saveWithOutDuplicates(List<TbaCommentInquiry> commentList, String appName, String sessionToken);

	String modifyWithOutDuplicates(List<TbaCommentInquiry> commentList, String appName, String sessionToken);

	String deleteTbaCommentConfig(List<CommonDeleteDTO> entity, String appName, String sessionToken);
	
}
