package com.wipro.fipc.service;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.pojo.tba.TbaInquiryConfigDto;

public interface TbaInquiryConfigService {

	String getTbaInquiryJsonKey( String columnValue);

	String createTbaInquiryConfig(List<TbaInquiryConfigDto> entit, String appName, String sessionTokeny)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException;

	Object getTbaInquiryConfig(String columnName, String columnValue) throws JsonProcessingException, IOException, IllegalAccessException, InvocationTargetException;

	String updateTbaInquiryConfig(List<TbaInquiryConfigDto> entity, String appName, String sessionTokeny)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException;

	String getAllTbaInquiryConfig();

	String getTbaProcessInquiry(int clientId) throws URISyntaxException, IOException;

	Object getTbaMetadata(int panelId, int clientId);

	String deleteInquiryEventConfig(List<CommonDeleteDTO> entity, String appName, String sessionToken)
			throws URISyntaxException;

	String getTbaInquiryJsonKeyByDataFilter(String parNM, String dataFilter) throws JsonProcessingException;

}
