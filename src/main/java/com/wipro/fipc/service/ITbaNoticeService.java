package com.wipro.fipc.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaNoticeInquiryConfig;
import com.wipro.fipc.model.generated.TbaNoticeInqConfig;

@Service
public interface ITbaNoticeService {

	String getNoticeMaster( String columnName, String columnValue);
	
	List<TbaNoticeInqConfig> getTbaNoticeData( String columnName, String columnValue);
	
	 String saveNotice(List<TbaNoticeInquiryConfig> tbaNoticeList, String appName, String sessionToken);
	 
	 String deleteNoticeConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken);
}
