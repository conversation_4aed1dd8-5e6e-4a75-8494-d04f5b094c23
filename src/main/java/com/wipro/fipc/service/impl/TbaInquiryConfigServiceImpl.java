package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;


import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;



import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaInquiryConfigs;
import com.wipro.fipc.dao.tba.TbaInquiryJsonKeyDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaInquiryConfig;
import com.wipro.fipc.entity.tba.TbaInquiryJsonKey;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.TbaMetaDataDto;
import com.wipro.fipc.model.generated.TbaMetaData;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.pojo.tba.TbaInquiryConfigDto;
import com.wipro.fipc.service.TbaInquiryConfigService;
import com.wipro.fipc.tba.service.InquiryConfigService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class TbaInquiryConfigServiceImpl implements TbaInquiryConfigService {

	@Autowired
	Environment env;

	@Autowired
	TbaInquiryConfigs tbaInquiryConfigServices;

	@Autowired
	InquiryConfigService inquiryConfigService;

	@Autowired
	private TbaInquiryJsonKeyDao inquiryJsonKeyDao;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	/* Get TbaProcessInquiry Details */
	@Override
	public String getTbaProcessInquiry(int clientId) throws URISyntaxException, IOException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaProcessInquiry>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,clientId=" + clientId);

		List<com.wipro.fipc.entity.tba.TbaProcessInquiry> mydata = inquiryConfigService.getInquiryNames(clientId);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String processInquiry = objectMapper.writeValueAsString(mydata);
		return processInquiry;

	}

	/* GetTbaInquiryJsonKey Details */
	@Override
	public String getTbaInquiryJsonKey(String columnValue) {
		String res = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaInquiryJsonKey>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET par_NM " + columnValue);

		List<TbaInquiryJsonKey> mydata = inquiryJsonKeyDao.findByParNMAndFilterFlag(columnValue, new Character('F'));
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaInquiryJsonKey", "Exception : " + e.getMessage());

		}
		return res;
	}

	@Override
	public String getTbaInquiryJsonKeyByDataFilter(String parNM, String dataFilter) throws JsonProcessingException {
		String res = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaInquiryJsonKeyByDataFilter>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET");
		List<TbaInquiryJsonKey> inquiryJsonKeydata = inquiryJsonKeyDao.getTbaInquiryJsonKeyByDataFilter(parNM,
				dataFilter.charAt(0));
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		res = objectMapper.writeValueAsString(inquiryJsonKeydata);
		return res;
	}

	/* Get TbaMetadata Details */
	@Override
	public Object getTbaMetadata(int panelId, int clientId) {
		String res = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaMetadata>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + ",panelId=" + panelId
						+ ",clientId=" + clientId);
		List<TbaMetaDataDto> tbaMetaDataDtoList = new ArrayList<>();
		List<com.wipro.fipc.entity.tba.TbaMetaData> mydata = inquiryConfigService.getTbaMetaData(panelId, clientId);
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e1) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaMetaData", "Exception : " + e1.getMessage());

			e1.printStackTrace();

		}

		try {

			List<TbaMetaData> tbaMetaDataList = Arrays.asList(new ObjectMapper().readValue(res, TbaMetaData[].class));
			Iterator<TbaMetaData> ite = tbaMetaDataList.iterator();
			while (ite.hasNext()) {
				tbaMetaDataDtoList.add(this.convertMetaDataToMetaDataDto(ite.next()));
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaMetadata", "Exception : ", e.getMessage());
			return e.getMessage();
		}
		return tbaMetaDataDtoList;
	}

	private TbaMetaDataDto convertMetaDataToMetaDataDto(TbaMetaData tbaMetaData) {
		TbaMetaDataDto tbaMetaDataDto = new TbaMetaDataDto();

		tbaMetaDataDto.setClientId(tbaMetaData.getClientId());
		if (tbaMetaData.getCreatedBy() != null) {
			tbaMetaDataDto.setCreatedBy(tbaMetaData.getCreatedBy());
		}
		if (tbaMetaData.getCreatedDate() != null) {
			tbaMetaDataDto.setCreatedDate(tbaMetaData.getCreatedDate());
		}
		tbaMetaDataDto.setId(tbaMetaData.getId());
		if (tbaMetaData.getMetaData() != null) {
			tbaMetaDataDto.setMetaData(tbaMetaData.getMetaData());
		}
		tbaMetaDataDto.setPanelId(tbaMetaData.getPanelId());
		if (tbaMetaData.getUpdatedBy() != null) {
			tbaMetaDataDto.setUpdatedBy(tbaMetaData.getUpdatedBy());
		}
		if (tbaMetaData.getUpdatedDate() != null) {
			tbaMetaDataDto.setUpdatedDate(tbaMetaData.getUpdatedDate());
		}

		if (tbaMetaData.getColumnValue() != null) {
			String columnValue = tbaMetaData.getColumnValue();
			if (columnValue.contains(",")) {
				tbaMetaDataDto.setColumnValue(columnValue.split(","));
			} else {
				String[] colArr = new String[1];
				colArr[0] = columnValue;
				tbaMetaDataDto.setColumnValue(colArr);
			}
		} else {
			String[] colArr = new String[1];
			colArr[0] = "";
			tbaMetaDataDto.setColumnValue(colArr);
		}

		if (tbaMetaData.getRowValue() != null) {
			String rowValue = tbaMetaData.getRowValue();
			if (rowValue.contains(",")) {
				tbaMetaDataDto.setRowValue(rowValue.split(","));
			} else {
				String[] rowArr = new String[1];
				rowArr[0] = rowValue;
				tbaMetaDataDto.setRowValue(rowArr);
			}
		} else {
			String[] rowArr = new String[1];
			rowArr[0] = "";
			tbaMetaDataDto.setRowValue(rowArr);
		}

		return tbaMetaDataDto;
	}

	/* Create TbaInquiryConfig Object */
	@Override
	public String createTbaInquiryConfig(List<TbaInquiryConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaInquiryConfig", "adid>>>>>>>>>>>>>>>>>>>>>>>> : " + adId);
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaInquiryConfig",
				"entity list size>>>>>>>>>>>>>>>>>>>>>>>> : " + entity.size());
		entity.stream().forEach(s -> {
			s.setCreatedDate(new Date());
			s.setCreatedBy(adId);
			s.setActiveFlag("T");
			s.setUpdatedDate(new Date());
			s.setUpdatedBy(adId);
			LoggerUtil.log(this.getClass(), Level.INFO, "createTbaPendingEventConfig>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Save" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});

		List<CommonResRowBO> mydata = tbaInquiryConfigServices.saveIfNotDuplicate(entity);
		String res = new ObjectMapper().writeValueAsString(mydata);
		ResponseDto response = new ResponseDto();
		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		return objectMapper.writeValueAsString(response);

	}

	/* Get TbaInquiryConfig Detals */

	@Override
	public Object getTbaInquiryConfig(String columnName, String columnValue) throws IllegalAccessException, InvocationTargetException, JsonParseException, JsonMappingException, IOException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaInquiryConfig>>>>>>>>>>>", "Method Type= "
				+ HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + columnName + "=" + columnValue);

		List<com.wipro.fipc.entity.tba.TbaInquiryConfig> mydata = inquiryConfigService.findByColumn(columnName,
				columnValue);

		List<TbaInquiryConfigDto> tbaConfigDtoList = new ArrayList<>();

	
		for (TbaInquiryConfig tbaInquiryConfig : mydata) {
			ObjectMapper mapper = new ObjectMapper();
			TbaInquiryConfigDto configDto = new TbaInquiryConfigDto();

			BeanUtils.copyProperties(configDto, tbaInquiryConfig);

			if (tbaInquiryConfig.getEffFromDate() != null) {

				configDto.setEffFromDate(mapper.readValue(tbaInquiryConfig.getEffFromDate(), Object.class));

			} else 
				tbaInquiryConfig.setEffFromDate("");
			if (tbaInquiryConfig.getEffToDate() != null) {
				configDto.setEffToDate(mapper.readValue(tbaInquiryConfig.getEffToDate(), Object.class));
			} else 
				tbaInquiryConfig.setEffToDate("");
			if(tbaInquiryConfig.getConditionJson() !=null&& !tbaInquiryConfig.getConditionJson().equals("null") && !tbaInquiryConfig.getConditionJson().equals("[]")) {
			configDto.setConditionJson(mapper.readValue(tbaInquiryConfig.getConditionJson(), Object.class));
			}else {
				configDto.setConditionJson(new ArrayList<>());
			}
			
			tbaConfigDtoList.add(configDto);
		}
		

		return tbaConfigDtoList;
	}

	/* Update the TbaInquiryConfig Details */
	@Override
	public String updateTbaInquiryConfig(List<TbaInquiryConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException {

		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		entity.stream().forEach(s -> {
			com.wipro.fipc.entity.tba.TbaInquiryConfig tba = inquiryConfigService.findById(s.getId());

			if (tba != null) {
				s.setCreatedBy(tba.getCreatedBy());
				s.setCreatedDate(tba.getCreatedDate());
			}
			s.setActiveFlag("T");

			s.setUpdatedDate(new Date());
			s.setUpdatedBy(adId);
			LoggerUtil.log(this.getClass(), Level.INFO, "updateTbaInquiryConfig>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
							+ s.getProcessJobMapping().getId() + ",ADID: " + adId);
		});

		ResponseDto response = new ResponseDto();
		List<CommonResRowBO> mydata = tbaInquiryConfigServices.saveIfNotDuplicate(entity);
		String res = objectMapper.writeValueAsString(mydata);
		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		return objectMapper.writeValueAsString(response);
	}

	/* Get All TbaInquiryConfig */
	@Override
	public String getAllTbaInquiryConfig() {

		String res = "";
		List<com.wipro.fipc.entity.tba.TbaInquiryConfig> mydata = inquiryConfigService.list();
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "getAllTbaInquiryConfig", "Exception : " + e.getMessage());
		}
		return res;
	}

	/* Delete Inquiry Event Config */
	@Override
	public String deleteInquiryEventConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken)
			throws URISyntaxException {

		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		entities.stream().forEach(s -> {
			s.setUpdatedBy(adId);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteInquiryEventConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});
		List<com.wipro.fipc.entity.tba.TbaInquiryConfig> newLayout = new ArrayList<>();

		for (CommonDeleteDTO req : entities) {

			com.wipro.fipc.entity.tba.TbaInquiryConfig obj = new com.wipro.fipc.entity.tba.TbaInquiryConfig();
			try {
				obj.setId(Long.parseLong(req.getId()));
				obj.setProcessJobMapping(
						new ObjectMapper().convertValue(req.getProcessJobMapping(), ProcessJobMapping.class));
				obj.setUpdatedBy(req.getUpdatedBy());
				obj.setUpdatedDate(req.getUpdatedDate());
				obj.setActiveFlag(req.getActiveFlag());

			} catch (Exception e) {

				LoggerUtil.log(this.getClass(), Level.ERROR, "deleteInquiryEventConfig",
						"Exception : " + e.getMessage());

			}
			newLayout.add(obj);
		}

		List<CommonRowBO> mydata = inquiryConfigService.deletemultiplerows(0, newLayout);
		String res = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "deleteInquiryEventConfig", "Exception : " + e.getMessage());


		}

		return res;
	}

}
