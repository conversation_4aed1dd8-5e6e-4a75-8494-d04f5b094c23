package com.wipro.fipc.service.impl;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.wipro.fipc.common.beans.Credentials;
import com.wipro.fipc.common.beans.Payload;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.config.JiraServiceConfig;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.KsdConfigDao;
import com.wipro.fipc.dao.filelayout.ProcessFeatureConfigDao;
import com.wipro.fipc.dao.jira.JiraTaskUpdateConfigDao;
import com.wipro.fipc.dao.jira.JiraTicketCreationConfigDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.batch.KsdConfig;
import com.wipro.fipc.entity.jira.JiraTaskUpdateConfig;
import com.wipro.fipc.entity.jira.JiraTicketCreationConfig;
import com.wipro.fipc.model.ConfigStatusBO;
import com.wipro.fipc.model.EpicTypes;
import com.wipro.fipc.model.JiraApplicableClients;
import com.wipro.fipc.model.JiraIssueType;
import com.wipro.fipc.model.JiraProject;
import com.wipro.fipc.model.JiraTaskNameBO;
import com.wipro.fipc.model.JiraTaskUpdateConfigCustom;
import com.wipro.fipc.model.JiraTicketCreationConfigCustom;
import com.wipro.fipc.model.JiraTicketTaskConfig;
import com.wipro.fipc.model.JiraUserDetails;
import com.wipro.fipc.model.JiraWatcher;
import com.wipro.fipc.model.generated.ConfigStatusApproveBO;
import com.wipro.fipc.service.JiraTicketConfigService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class JiraTicketConfigServiceImpl implements JiraTicketConfigService {

	@Autowired
	JiraServiceConfig jiraServiceConfig;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	KsdConfigDao ksdConfigDao;

	@Autowired
	ProcessFeatureConfigDao processFeatureConfigDao;

	@Autowired
	JiraTicketCreationConfigDao jiraTicketCreationConfigDao;

	@Autowired
	JiraTaskUpdateConfigDao JirataskUpdateConfigDao;

	@Autowired
	GenericDao<com.wipro.fipc.entity.jira.JiraTicketCreationConfig> jiraTicketDaoGeneric;

	@Autowired
	GenericDao<com.wipro.fipc.entity.jira.JiraTaskUpdateConfig> JiraUpdateDaoGeneric;

	public static final String FALSE = "false";
	protected static final String JIRA_SCHEMA = "jira";
	protected static final String TICKET_CREATION_CONFIG = "ticket_creation_config";
	protected static final String TASK_UPDATE_CONFIG = "task_update_config";
	private final String APP_NAME = "appName";
	private final String API_KEY = "apiKey";

	private List<JiraTicketCreationConfig> getByColumnJiraTicketConfig(String columnName, String columnValue) {
		try {
			List<JiraTicketCreationConfig> myList = new ArrayList<>();

			List<com.wipro.fipc.entity.jira.JiraTicketCreationConfig> responseData = jiraTicketDaoGeneric.findByColumn(
					com.wipro.fipc.entity.jira.JiraTicketCreationConfig.class, JIRA_SCHEMA, TICKET_CREATION_CONFIG,
					columnName, columnValue);

			ObjectMapper mapper = new ObjectMapper();
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

			for (com.wipro.fipc.entity.jira.JiraTicketCreationConfig data : responseData) {
				JiraTicketCreationConfig myList1 = mapper.convertValue(data, JiraTicketCreationConfig.class);
				myList.add(myList1);
			}

			return myList;

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "JiraTicketConfigServiceImpl", "getByColumnJiraTicketConfig: ",
					e);
		}

		return new ArrayList<>();
	}

	private List<JiraTaskUpdateConfig> getByColumnJiraTaskConfig(String columnName, String columnValue) {
		try {

			List<JiraTaskUpdateConfig> myList = new ArrayList<>();

			List<com.wipro.fipc.entity.jira.JiraTaskUpdateConfig> responseData = JiraUpdateDaoGeneric.findByColumn(
					com.wipro.fipc.entity.jira.JiraTaskUpdateConfig.class, JIRA_SCHEMA, TASK_UPDATE_CONFIG, columnName,
					columnValue);
			ObjectMapper mapper = new ObjectMapper();
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

			for (com.wipro.fipc.entity.jira.JiraTaskUpdateConfig data : responseData) {
				JiraTaskUpdateConfig myList1 = mapper.convertValue(data, JiraTaskUpdateConfig.class);
				myList.add(myList1);
			}
			return myList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "JiraTicketConfigServiceImpl", "getByColumnJiraTaskConfig: ",
					e);
		}
		return new ArrayList<>();
	}

	private JiraTaskNameBO getjiraTaskNameFromKsdConfig(String columnName, String columnValue) {
		JiraTaskNameBO jiraTaskNameBO = new JiraTaskNameBO();
		try {
			long pjmId = Long.parseLong(columnValue);
			KsdConfig responseOfKsdConfig = ksdConfigDao.getKsdConfigdata(pjmId);
			if (responseOfKsdConfig != null) {
				String jiraTaskName = responseOfKsdConfig.getJiraTaskName();
				jiraTaskName = jiraTaskName != null ? jiraTaskName.trim() : jiraTaskName;
				String jiraTaskType = responseOfKsdConfig.getJiraTaskType();
				jiraTaskType = jiraTaskType != null ? jiraTaskType.trim() : jiraTaskType;
				jiraTaskNameBO.setJiraTaskName(jiraTaskName);
				jiraTaskNameBO.setJiraTaskType(jiraTaskType);
				jiraTaskNameBO.setJiraProjectType(responseOfKsdConfig.getJiraProjectType());
				LoggerUtil.log(this.getClass(), Level.INFO, "getjiraTaskNameFromKsdConfig",
						"JiraTaskName :  " + jiraTaskName + " ," + "JiraTaskType : " + jiraTaskType);
			}
			jiraTaskNameBO.setProcessJobMappingId(pjmId);
			getConfigStatusFromProcessFeatureConfig(jiraTaskNameBO, columnName, columnValue);
			return jiraTaskNameBO;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "JiraTicketConfigServiceImpl", "getjiraTaskNameFromKsdConfig ",
					e);
		}

		return jiraTaskNameBO;
	}

	public void getConfigStatusFromProcessFeatureConfig(JiraTaskNameBO jiraTaskNameBO, String columnName,
			String columnValue) {

		String configStatusResp = processFeatureConfigDao.getConfigStatus(Long.parseLong(columnValue));

		LoggerUtil.log(this.getClass(), Level.INFO, "getConfigStatusFromProcessFeatureConfig",
				"configStatusResp  :  " + configStatusResp);
		if (configStatusResp != null && !configStatusResp.equalsIgnoreCase(""))
			jiraTaskNameBO.setConfigStatus(configStatusResp);
		else
			LoggerUtil.log(this.getClass(), Level.INFO, "getConfigStatusFrmProcessFeatureConfig",
					"Config status unavailable!");
	}

	@Override
	public JiraTicketTaskConfig getByColumnJiraTikcetTask(@PathVariable String columnName,
			@PathVariable String columnValue) {

		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigServiceImpl",
				"getByColumnJiraTikcetTask method started...");
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		JiraTicketTaskConfig ticketCreationUpdateConfigResp = new JiraTicketTaskConfig();
		try {
			List<JiraTicketCreationConfig> listTicketCreationConfigResp = getByColumnJiraTicketConfig(columnName,
					columnValue);
			List<JiraTaskUpdateConfig> listcreateTaskUpdate = getByColumnJiraTaskConfig(columnName, columnValue);

			JiraTaskNameBO jiraTaskNameBO = getjiraTaskNameFromKsdConfig(columnName, columnValue);

			List<JiraTicketCreationConfigCustom> listTicketCreationConfigCustom = new ArrayList<>();
			List<JiraTaskUpdateConfigCustom> listJiraTaskUpdateConfigCustom = new ArrayList<>();

			listTicketCreationConfigCustom = getTicketCreationConfigResponse(listTicketCreationConfigResp,
					listTicketCreationConfigCustom, mapper);

			listJiraTaskUpdateConfigCustom = getTicketUpdateConfigResponse(listcreateTaskUpdate,
					listJiraTaskUpdateConfigCustom, mapper);

			ticketCreationUpdateConfigResp.setListJiraTicketCreationConfigCustom(listTicketCreationConfigCustom);
			ticketCreationUpdateConfigResp.setListJiraTaskUpdateConfigCustom(listJiraTaskUpdateConfigCustom);
			ticketCreationUpdateConfigResp.setJiraTaskNameBO(jiraTaskNameBO);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "JiraTicketConfigServiceImpl", "getByColumnJiraTikcetTask ",
					e);
		}
		return ticketCreationUpdateConfigResp;
	}

	private List<JiraTicketCreationConfigCustom> getTicketCreationConfigResponse(
			List<JiraTicketCreationConfig> listTicketCreationConfigResp,
			List<JiraTicketCreationConfigCustom> listTicketCreationConfigCustom, ObjectMapper mapper) throws JsonMappingException, JsonProcessingException {

		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigServiceImpl",
				"getTicketCreationConfigResponse method started...");

		for (JiraTicketCreationConfig request : listTicketCreationConfigResp) {
			JiraTicketCreationConfigCustom createConfigCustom = new JiraTicketCreationConfigCustom();
			BeanUtils.copyProperties(request, createConfigCustom);
			createConfigCustom.setProcessJobMapping(
					mapper.convertValue(request.getProcessJobMapping(), com.wipro.fipc.model.ProcessJobMapping.class));

			String attachment = request.getAttachment();
			if (StringUtils.hasText(attachment)) {
				String[] attachmentUI = attachment.split(",");
				createConfigCustom.setAttachment(attachmentUI);
			} else
				createConfigCustom.setAttachment(new String[0]);
			
			if (StringUtils.hasText(request.getJiraWatchers()))
				createConfigCustom.setJiraWatchers(getJiraWatcherJson(request.getJiraWatchers(), mapper));
			

			listTicketCreationConfigCustom.add(createConfigCustom);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigServiceImpl",
				"getTicketCreationConfigResponse method end");
		return listTicketCreationConfigCustom;

	}

	private List<JiraWatcher> getJiraWatcherJson(String jiraWatchers, ObjectMapper mapper)
			throws JsonMappingException, JsonProcessingException {
		return mapper.readValue(jiraWatchers, new TypeReference<List<JiraWatcher>>() {
		});
	}

	private List<JiraTaskUpdateConfigCustom> getTicketUpdateConfigResponse(
			List<JiraTaskUpdateConfig> listcreateMaestroTaskUpdate,
			List<JiraTaskUpdateConfigCustom> listJiraTaskUpdateConfigCustom, ObjectMapper mapper) throws JsonMappingException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigServiceImpl",
				"getTicketUpdateConfigResponse method started...");

		for (JiraTaskUpdateConfig request : listcreateMaestroTaskUpdate) {
			JiraTaskUpdateConfigCustom updateConfigCustom = new JiraTaskUpdateConfigCustom();
			updateConfigCustom.setId(request.getId());
			updateConfigCustom.setIssueType(request.getIssueType());
			updateConfigCustom.setNewComments(request.getNewComments());
			updateConfigCustom.setActiveFlag(request.getActiveFlag());
			updateConfigCustom.setProcessJobMapping(
					mapper.convertValue(request.getProcessJobMapping(), com.wipro.fipc.model.ProcessJobMapping.class));

			String attachment = request.getAttachment();
			if (StringUtils.hasText(attachment)) {
				String[] attachmentUI = attachment.split(",");
				updateConfigCustom.setAttachment(attachmentUI);
			} else
				updateConfigCustom.setAttachment(new String[0]);

			String interestedParties = request.getInterestedParties();
			if (StringUtils.hasText(interestedParties)) {
				String[] interestedPartiesUI = interestedParties.split(",");
				updateConfigCustom.setInterestedParties(interestedPartiesUI);
			} else
				updateConfigCustom.setInterestedParties(new String[0]);
			
			if (StringUtils.hasText(request.getJiraWatchers()))
				updateConfigCustom.setJiraWatchers(getJiraWatcherJson(request.getJiraWatchers(), mapper));

			listJiraTaskUpdateConfigCustom.add(updateConfigCustom);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigServiceImpl",
				"getTicketUpdateConfigResponse method end");
		return listJiraTaskUpdateConfigCustom;
	}

	@Override
	public List<String> newcreateUpdateJiraTicketConfig(JiraTicketTaskConfig ticketCreationUpdateConfig, String appName,
			String sessionToken, String pjmId) {

		List<String> listCeateUpdateTicketTaskResp = new ArrayList<>();
		try {
			List<JiraTicketCreationConfigCustom> listTicketCreationConfigCustomReq = ticketCreationUpdateConfig
					.getListJiraTicketCreationConfigCustom();
			List<JiraTaskUpdateConfigCustom> listJiraUpdateConfigCustomReq = ticketCreationUpdateConfig
					.getListJiraTaskUpdateConfigCustom();
			JiraTaskNameBO jiraTaskNameBOReq = ticketCreationUpdateConfig.getJiraTaskNameBO();

			for (JiraTicketCreationConfigCustom ticketCreationConfigCustom : listTicketCreationConfigCustomReq) {

				ticketCreationConfigCustom.setActiveFlag("T");

			}

			for (JiraTaskUpdateConfigCustom taskUpdateConfigCustom : listJiraUpdateConfigCustomReq) {
				taskUpdateConfigCustom.setActiveFlag("T");

			}

			listCeateUpdateTicketTaskResp = createUpdateJiraResp(listTicketCreationConfigCustomReq,
					listJiraUpdateConfigCustomReq, jiraTaskNameBOReq);
			return listCeateUpdateTicketTaskResp;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "newcreateUpdateJiraTicketConfig", "Error1 " + e);
		}
		return listCeateUpdateTicketTaskResp;
	}

	@Override
	public List<String> deleteJiraTicketConfig(JiraTicketTaskConfig ticketCreationUpdateConfig, String appName,
			String sessionToken, String pjmId) {

		List<String> listCeateUpdateTicketTaskResp = new ArrayList<>();
		try {
			List<JiraTicketCreationConfigCustom> listTicketCreationConfigCustomReq = ticketCreationUpdateConfig
					.getListJiraTicketCreationConfigCustom();
			List<JiraTaskUpdateConfigCustom> listJiraUpdateConfigCustomReq = ticketCreationUpdateConfig
					.getListJiraTaskUpdateConfigCustom();
			JiraTaskNameBO maestroTaskNameBOReq = ticketCreationUpdateConfig.getJiraTaskNameBO();

			for (JiraTicketCreationConfigCustom ticketCreationConfigCustom : listTicketCreationConfigCustomReq) {

				ticketCreationConfigCustom.setActiveFlag("F");

			}

			for (JiraTaskUpdateConfigCustom taskUpdateConfigCustom : listJiraUpdateConfigCustomReq) {

				taskUpdateConfigCustom.setActiveFlag("F");
			}

			listCeateUpdateTicketTaskResp = createUpdateJiraResp(listTicketCreationConfigCustomReq,
					listJiraUpdateConfigCustomReq, maestroTaskNameBOReq);
			return listCeateUpdateTicketTaskResp;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "JiraTicketConfigServiceImpl", "deleteJiraTicketConfig ", e);
		}
		return listCeateUpdateTicketTaskResp;
	}

	public String getCurrentTimeStamp() {
		Date date = new Date();
		Timestamp ts = new Timestamp(date.getTime());
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return formatter.format(ts);
	}

	private List<String> createUpdateJiraResp(
			List<com.wipro.fipc.model.JiraTicketCreationConfigCustom> listTicketCreationConfigReq,
			List<com.wipro.fipc.model.JiraTaskUpdateConfigCustom> listTaskUpdateConfigReq,
			JiraTaskNameBO jiraTaskNameBOReq) {
		boolean createTicketConfigRespFlag = false;
		boolean updateTicketConfigRespFlag = false;
		boolean taskNameBORespFlag = false;
		boolean jiraTaskNameConfigBORespFlag = false;
		String strCreateTicketConfigResp = "";
		String strUpdateTicketConfigResp = "";
		String strTaskNameBoResp = "";
		String strJiraTaskNameConfigBOResp = "";
		List<String> listCreatTicketTaskConfigResp = new ArrayList<>();
		List<JiraTicketCreationConfig> listofTickectCreationConfigEnt = new ArrayList();
		List<JiraTaskUpdateConfig> listTaskUpdateConfigReqEnt = new ArrayList();
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

			if (!CollectionUtils.isEmpty(listTicketCreationConfigReq)) {
				listofTickectCreationConfigEnt = getJiraTicketCreateConfig(listTicketCreationConfigReq,
						listofTickectCreationConfigEnt, objectMapper);
				createTicketConfigRespFlag = createupdateJiraTicketConfigList(listofTickectCreationConfigEnt);
			}
			if (!CollectionUtils.isEmpty(listTaskUpdateConfigReq)) {
				listTaskUpdateConfigReqEnt = getJiraTaskUpdateConfig(listTaskUpdateConfigReq,
						listTaskUpdateConfigReqEnt, objectMapper);
				updateTicketConfigRespFlag = createJiraTaskUpdateConfig(listTaskUpdateConfigReqEnt);
			}
			if (jiraTaskNameBOReq != null)
				taskNameBORespFlag = updateTaskNameAndTaskTypeInKsdConfig(jiraTaskNameBOReq);

			strCreateTicketConfigResp = "Ticket_Creatinon_Config :" + createTicketConfigRespFlag;
			strUpdateTicketConfigResp = "Task_update_Config :" + updateTicketConfigRespFlag;
			strTaskNameBoResp = "Ksd_Config_JiraTaskName :" + taskNameBORespFlag;
			strJiraTaskNameConfigBOResp = "Task_Update_config_JiraTaskName:" + jiraTaskNameConfigBORespFlag;

			listCreatTicketTaskConfigResp.add(strCreateTicketConfigResp);
			listCreatTicketTaskConfigResp.add(strUpdateTicketConfigResp);
			listCreatTicketTaskConfigResp.add(strTaskNameBoResp);
			listCreatTicketTaskConfigResp.add(strJiraTaskNameConfigBOResp);
			return listCreatTicketTaskConfigResp;

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "createUpdateJiraResp", "Error " + e);
		}
		return new ArrayList<>();
	}

	private List<JiraTicketCreationConfig> getJiraTicketCreateConfig(
			List<JiraTicketCreationConfigCustom> listofTickectCreationConfigCustom,
			List<JiraTicketCreationConfig> listofTickectCreationConfigEnt, ObjectMapper objectMapper) throws JsonProcessingException {

		for (JiraTicketCreationConfigCustom request : listofTickectCreationConfigCustom) {
			JiraTicketCreationConfig createConfig = new JiraTicketCreationConfig();
			BeanUtils.copyProperties(request, createConfig);
			createConfig.setAttachment(String.join(",", request.getAttachment()));
			createConfig.setProcessJobMapping(
					objectMapper.convertValue(request.getProcessJobMapping(), ProcessJobMapping.class));
			
			if (!CollectionUtils.isEmpty(request.getJiraWatchers()))
				createConfig.setJiraWatchers(convertJiraWatcherToJson(request.getJiraWatchers(), objectMapper));

			listofTickectCreationConfigEnt.add(createConfig);

		}

		return listofTickectCreationConfigEnt;
	}

	private List<JiraTaskUpdateConfig> getJiraTaskUpdateConfig(List<JiraTaskUpdateConfigCustom> listTaskUpdateConfigReq,
			List<JiraTaskUpdateConfig> listTaskUpdateConfigReqEnt, ObjectMapper objectMapper) throws JsonProcessingException {

		for (JiraTaskUpdateConfigCustom request : listTaskUpdateConfigReq) {
			JiraTaskUpdateConfig updateConfig = new JiraTaskUpdateConfig();
			updateConfig.setId(request.getId());
			updateConfig.setIssueType(request.getIssueType());
			updateConfig.setNewComments(request.getNewComments());
			updateConfig.setAttachment(String.join(",", request.getAttachment()));
			updateConfig.setActiveFlag(request.getActiveFlag());
			updateConfig.setInterestedParties(String.join(",", request.getInterestedParties()));
			updateConfig.setProcessJobMapping(
					objectMapper.convertValue(request.getProcessJobMapping(), ProcessJobMapping.class));

			if (!CollectionUtils.isEmpty(request.getJiraWatchers()))
				updateConfig.setJiraWatchers(convertJiraWatcherToJson(request.getJiraWatchers(), objectMapper));
			listTaskUpdateConfigReqEnt.add(updateConfig);

		}

		return listTaskUpdateConfigReqEnt;
	}

	private String convertJiraWatcherToJson(List<JiraWatcher> jiraWatchers, ObjectMapper objectMapper) throws JsonProcessingException {
		return objectMapper.writeValueAsString(jiraWatchers);
	}

	public boolean updateTaskNameAndTaskTypeInKsdConfig(JiraTaskNameBO jiraTaskNameBOReq) {

		boolean taskNameBORespFlag = false;
		try {
			int updateCount = ksdConfigDao.updateJiraTaskNameAndTaskType(jiraTaskNameBOReq.getProcessJobMappingId(),
					jiraTaskNameBOReq.getJiraTaskName(), jiraTaskNameBOReq.getJiraTaskType(),
					jiraTaskNameBOReq.getJiraProjectType(), jiraTaskNameBOReq.getUpdatedBy());

			taskNameBORespFlag = updateCount > 0 ? true : false;
			return taskNameBORespFlag;
		} catch (Exception e) {
			taskNameBORespFlag = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateJiraTaskNameKsdConfig", "Error " + e);
		}
		return taskNameBORespFlag;
	}

	@Override
	public String updateJiraConfigStatus(ConfigStatusBO configStatusBO, String appName, String sessionToken) {
		String updateConfigStatusResp = FALSE;

		try {

			String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
			configStatusBO.setUpdatedBy(adID);

			processFeatureConfigDao.modifyConfigStatus(configStatusBO.getProcessJobMappingId(),
					configStatusBO.getConfigStatus(), configStatusBO.getUpdatedBy());

			updateConfigStatusResp = "true";
			return updateConfigStatusResp;
		} catch (Exception e) {
			updateConfigStatusResp = FALSE;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateConfigStatus", "Unable to update JiraConfigStatus" + e);
		}

		return updateConfigStatusResp;
	}

	@Override
	public String updateApproveJiraConfigStatus(ConfigStatusApproveBO configStatusApproveBO, String appName,
			String sessionToken) {
		String updateConfigStatusResp = FALSE;

		try {

			String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
			configStatusApproveBO.setApprovedBy(adId);

			processFeatureConfigDao.modifyApprovedConfigStatus(configStatusApproveBO.getProcessJobMappingId(),
					configStatusApproveBO.getConfigStatus(), configStatusApproveBO.getApprovedBy());
			updateConfigStatusResp = "true";

			return updateConfigStatusResp;
		} catch (Exception e) {
			updateConfigStatusResp = FALSE;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateApproveConfigStatus",
					"Unable to approve the config status!", e);
		}

		return updateConfigStatusResp;

	}

	public boolean createupdateJiraTicketConfigList(List<JiraTicketCreationConfig> listTicketCreationConfig) {
		boolean booleanCreateTicketDBResp = false;
		try {

			jiraTicketCreationConfigDao.saveAll(listTicketCreationConfig);
			booleanCreateTicketDBResp = true;
			return booleanCreateTicketDBResp;

		} catch (Exception e) {
			booleanCreateTicketDBResp = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, "createupdateJiraTicketConfigList",
					"Unable to update createConfig " + e);
		}

		return booleanCreateTicketDBResp;
	}

	public boolean createJiraTaskUpdateConfig(List<JiraTaskUpdateConfig> listTaskUpdateConfig) {

		boolean booleanCreatTaskDBResp = false;

		try {

			JirataskUpdateConfigDao.saveAll(listTaskUpdateConfig);
			booleanCreatTaskDBResp = true;
			return booleanCreatTaskDBResp;

		} catch (Exception e) {
			booleanCreatTaskDBResp = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, "createJiraTaskUpdateConfig",
					"Unable to update updateConfig " + e);
		}

		return booleanCreatTaskDBResp;
	}

	@Override
	public List<JiraProject> getProjectList(String businessUnit) {
		String jiraBaseUrl = getJiraBaseUrl();
		String url = jiraBaseUrl.concat("project");

		HttpEntity<String> entity = populateInputObject(businessUnit);
		ResponseEntity<String> response = new RestTemplate().exchange(url, HttpMethod.GET, entity, String.class);

		ArrayList<JiraProject> projectList = new ArrayList<>();
		JsonArray projectArray = new Gson().fromJson(response.getBody(), JsonArray.class);
		for (int i = 0; i < projectArray.size(); i++) {
			String key = projectArray.get(i).getAsJsonObject().get("key").getAsString().trim();
			String name = projectArray.get(i).getAsJsonObject().get("name").getAsString().trim();

			projectList.add(new JiraProject(key, name));
		}
		return projectList;
	}

	@Override
	public List<JiraApplicableClients> getApplicableClients(String key, String issueId, String businessUnit) {

		String jiraBaseUrl = getJiraBaseUrl();
		String applicableClientUrl = "issue".concat("/").concat("createmeta").concat("/").concat(key).concat("/")
				.concat("issuetypes").concat("/").concat(issueId);
		String url = jiraBaseUrl.concat(applicableClientUrl);

		HttpEntity<String> entity = populateInputObject(businessUnit);
		ResponseEntity<String> response = new RestTemplate().exchange(url, HttpMethod.GET, entity, String.class);

		ArrayList<JiraApplicableClients> applicableClientList = new ArrayList<>();
		JsonObject jsonObj = new Gson().fromJson(response.getBody(), JsonObject.class);
		JsonArray fildsArray = jsonObj.get("fields").getAsJsonArray();

		for (int i = 0; i < fildsArray.size(); i++) {
			String name = fildsArray.get(i).getAsJsonObject().get("name").getAsString();
			if (name.equals("Applicable Clients")) {
				JsonArray asJsonArray = fildsArray.get(i).getAsJsonObject().get("allowedValues").getAsJsonArray();
				for (int j = 0; j < asJsonArray.size(); j++) {
					String id = asJsonArray.get(j).getAsJsonObject().get("id").getAsString();
					String value = asJsonArray.get(j).getAsJsonObject().get("value").getAsString();
					applicableClientList.add(new JiraApplicableClients(id, value));
				}
			}
		}
		return applicableClientList;
	}

	@Override
	public List<JiraIssueType> getIssueType(String key, String businessUnit) {
		String jiraBaseUrl = getJiraBaseUrl();

		String url = jiraBaseUrl.concat("issue").concat("/").concat("createmeta").concat("/").concat(key).concat("/")
				.concat("issuetypes");

		HttpEntity<String> entity = populateInputObject(businessUnit);
		ResponseEntity<String> response = new RestTemplate().exchange(url, HttpMethod.GET, entity, String.class);

		List<JiraIssueType> issueTypeList = new ArrayList<>();

		JsonObject jsonObj = new Gson().fromJson(response.getBody(), JsonObject.class);
		JsonArray issueTypes = jsonObj.get("issueTypes").getAsJsonArray();

		for (int i = 0; i < issueTypes.size(); i++) {
			boolean subtask = issueTypes.get(i).getAsJsonObject().get("subtask").getAsBoolean();
			if (!subtask) {
				String id = issueTypes.get(i).getAsJsonObject().get("id").getAsString();
				String name = issueTypes.get(i).getAsJsonObject().get("name").getAsString();
				issueTypeList.add(new JiraIssueType(id, name));
			}

		}

		return issueTypeList;
	}

	@Override
	public List<JiraUserDetails> getUserDetails(String emailId, String businessUnit) {

		String url = jiraServiceConfig.getJiraBaseUrl().concat("user").concat("/").concat("search?query=")
				.concat(emailId);

		HttpEntity<String> entity = populateInputObject(businessUnit);
		ResponseEntity<String> response = new RestTemplate().exchange(url, HttpMethod.GET, entity, String.class);

		List<JiraUserDetails> userDetails = new ArrayList<>();
		JsonArray jsonObj = new Gson().fromJson(response.getBody(), JsonArray.class);

		for (int i = 0; i < jsonObj.size(); i++) {
			String asString = jsonObj.get(i).getAsJsonObject().toString();
			if (asString.contains("emailAddress")) {
				String accountId = jsonObj.get(i).getAsJsonObject().get("accountId").getAsString();
				String emailAddress = jsonObj.get(i).getAsJsonObject().get("emailAddress").getAsString();
				String displayName = jsonObj.get(i).getAsJsonObject().get("displayName").getAsString();
				String active = jsonObj.get(i).getAsJsonObject().get("active").getAsString();
				userDetails.add(new JiraUserDetails(accountId, emailAddress, displayName, active));
			}
		}

		return userDetails;

	}

	@Override
	public List<EpicTypes> getJiraEpicType(String key, String issueId, String businessUnit) {

		String jiraBaseUrl = getJiraBaseUrl();
		String applicableClientUrl = "issue".concat("/").concat("createmeta").concat("/").concat(key).concat("/")
				.concat("issuetypes").concat("/").concat(issueId);
		String url = jiraBaseUrl.concat(applicableClientUrl);

		HttpEntity<String> entity = populateInputObject(businessUnit);
		ResponseEntity<String> response = new RestTemplate().exchange(url, HttpMethod.GET, entity, String.class);

		ArrayList<EpicTypes> epicTypeList = new ArrayList<>();
		JsonObject jsonObj = new Gson().fromJson(response.getBody(), JsonObject.class);
		JsonArray fildsArray = jsonObj.get("fields").getAsJsonArray();

		for (int i = 0; i < fildsArray.size(); i++) {
			String name = fildsArray.get(i).getAsJsonObject().get("name").getAsString();
			if (name.equals("Epic Type")) {
				JsonArray asJsonArray = fildsArray.get(i).getAsJsonObject().get("allowedValues").getAsJsonArray();
				for (int j = 0; j < asJsonArray.size(); j++) {
					String id = asJsonArray.get(j).getAsJsonObject().get("id").getAsString();
					String value = asJsonArray.get(j).getAsJsonObject().get("value").getAsString();
					epicTypeList.add(new EpicTypes(id, value));
				}
			}
		}
		return epicTypeList;

	}

	private HttpEntity<String> populateInputObject(String businessUnit) {
		Credentials credentials = fetchCredentials(jiraServiceConfig.getJiraCredentials(), businessUnit);
		String authorization = "Basic ".concat(credentials.getJiraApiToken());
		String accept = "application/json";

		HttpHeaders requestHeaders = new HttpHeaders();
		requestHeaders.add("Accept", accept);
		requestHeaders.add("Authorization", authorization);
		requestHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<String> entity = new HttpEntity<String>("parameters", requestHeaders);

		return entity;

	}

	private Credentials fetchCredentials(String jiraCredential, String businessUnit) {
		LoggerUtil.log(this.getClass(), Level.INFO, "fetchCredentials",
				"method start -> orchCredential: " + jiraCredential);
		String uri = jiraServiceConfig.getSeceretKeyPath();
		String appName = jiraServiceConfig.getAppName();
		String apiKey = jiraServiceConfig.getApiKey();

		Payload payload = new Payload();
		payload.setRacfID(jiraCredential);
		payload.setAppNameConfig(appName);
		payload.setBusinessUnit(businessUnit);

		HttpHeaders headers = new HttpHeaders();
		headers.add(APP_NAME, appName);
		headers.add(API_KEY, apiKey);
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Payload> request = new HttpEntity<Payload>(payload, headers);

		ResponseEntity<Credentials> response = new RestTemplate().exchange(uri, HttpMethod.POST, request,
				Credentials.class);
		Credentials credentials = null;
		if (response != null) {
			credentials = response.getBody();
			LoggerUtil.log(this.getClass(), Level.INFO, "fetchCredentials", "credential value received successfully");
		} else
			LoggerUtil.log(this.getClass(), Level.INFO, "fetchCredentials", "credential value unavailable");

		return credentials;
	}

	private String getJiraBaseUrl() {
		return jiraServiceConfig.getJiraBaseUrl();
	}
	
	@Override
	public String getCustomFields(String projectKey, String issueId, String businessUnit) {
		return getCustomFieldsValue(projectKey, issueId, businessUnit);
	}

	private String getCustomFieldsValue(String key, String issueId, String businessUnit) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getCustomFieldsValue", "method start...");

		String jiraBaseUrl = getJiraBaseUrl();
		StringBuilder jiraCustomFieldsUrl = new StringBuilder();
		jiraCustomFieldsUrl.append("issue").append("/").append("createmeta").append("/").append(key).append("/").append("issuetypes").append("/").append(issueId);
		String url = jiraBaseUrl.concat(jiraCustomFieldsUrl.toString());
		
		HttpEntity<String> entity = populateInputObject(businessUnit);
		ResponseEntity<String> response = new RestTemplate().exchange(url, HttpMethod.GET, entity, String.class);

		JsonObject customFieldsJson = new JsonObject();
		JsonObject jsonObj = new Gson().fromJson(response.getBody(), JsonObject.class);
		JsonArray fieldsArray = jsonObj.get(HolmesAppConstants.FIELDS).getAsJsonArray();

		fieldsArray.forEach(fieldElement -> {
			JsonObject field = fieldElement.getAsJsonObject();
			String name = field.get(HolmesAppConstants.NAME).getAsString();
			if (name.equals(HolmesAppConstants.APPLICABLE_CLIENTS))
				updateCustomFields(HolmesAppConstants.APPLICABLE_CLIENTS_KEY, field, customFieldsJson);
			else if (businessUnit.startsWith(HolmesAppConstants.HWS) && name.equals(HolmesAppConstants.PROCESS))
				updateCustomFields(HolmesAppConstants.PROCESS_KEY, field, customFieldsJson);
			else if (businessUnit.startsWith(HolmesAppConstants.HWS) && name.equals(HolmesAppConstants.IMPACTED_SHARED_GROUPS))
				updateCustomFields(HolmesAppConstants.IMPACTED_SHARED_GROUPS_KEY, field, customFieldsJson);

		});
		String customFieldsValue = new Gson().toJson(customFieldsJson);
		LoggerUtil.log(this.getClass(), Level.INFO, "getCustomFieldsValue",
				"JiraTicketConfigServiceImpl , getCustomFieldsValue end on : " + System.currentTimeMillis());

		return customFieldsValue;
	}

	private JsonObject updateCustomFields(String key, JsonObject field, JsonObject customFieldsJson) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateCustomFields", "method start...");
		JsonArray jsonArray = field.getAsJsonObject().get(HolmesAppConstants.ALLOWED_VALUE).getAsJsonArray();
		JsonArray customFieldJsonArray = new JsonArray();
		jsonArray.forEach(JsonElement -> {
			JsonObject jsonObjectItem = JsonElement.getAsJsonObject();
			JsonObject jsonObject = new JsonObject();
			jsonObject.addProperty(HolmesAppConstants.ID, jsonObjectItem.get(HolmesAppConstants.ID).getAsString());
			jsonObject.addProperty(HolmesAppConstants.VALUE, jsonObjectItem.get(HolmesAppConstants.VALUE).getAsString());
			customFieldJsonArray.add(jsonObject);
		});
		customFieldsJson.add(key, customFieldJsonArray);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateCustomFields",
				"JiraTicketConfigServiceImpl , updateCustomFields end for key: " + key + " on: "
						+ System.currentTimeMillis());
		return customFieldsJson;
	}

}