package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFHyperlink;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.Hours;
import org.joda.time.Minutes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.AuditDao;
import com.wipro.fipc.dao.ClientDetailsDao;
import com.wipro.fipc.dao.ProcessDao;
import com.wipro.fipc.dao.batch.DailyTaskReportDao;
import com.wipro.fipc.dao.tba.ProcessJobMappingDao;
import com.wipro.fipc.entity.batch.DailyTaskReport;
import com.wipro.fipc.exception.StatsMoniterCustomException;
import com.wipro.fipc.pojo.AllocationModel;
import com.wipro.fipc.pojo.AllocationResponse;
import com.wipro.fipc.pojo.AllocationUpdate;
import com.wipro.fipc.pojo.AllocationUpdateBO;
import com.wipro.fipc.pojo.AnalystList;
import com.wipro.fipc.pojo.ClientDetailsList;
import com.wipro.fipc.service.UIService;

@Service
public class UIServiceImpl implements UIService {

	private static final String DAILY = "Daily";
	private static final String WEEKLY = "Weekly";
	private static final String MONTHLY = "Monthly";

	private static final String EXECUTING = "Executing";
	private static final String HUMANINLOOP = "HumanInLoop";
	private static final String PROCESSED = "Processed";

	private static final String HHMM = "HH:mm";
	private static final String GETTASKDETAILS = "getTaskDetails";
	private static final String AND = "&";
	private static final String COLUMNVALUE = "column_value=";
	private static final String PLUGINNAME = "pluginName";
	private static final String ZONEID = "America/Chicago";
	private static final String CLIENTNAME = "clientName";
	private static final String CREATEBARCHART = "createBarChart";
	private static final String CREATETIMESTAMP = "createTimestamp";
	private static final String ENDTIMESTAMP = "endTimestamp";
	private static final String FREQUENCY = "frequency";
	private static final String GETBYID = "getById";
	private static final String PROCESSNAME = "processName";
	private static final String STARTTIMESTAMP = "startTimestamp";
	private static final String STATUS = "status";
	private static final String METHOD = "method";
	private static final String CALIBRI = "Calibri";
	private static final String PROCESSTYPE = "processType";
	private static final String UPDATETASK = "updateTaskDetails";
	private static final String URL = "url";
	private static final String MODIFYCLIENT = "modifyclient";
	private static final String GETWITHOUTTIME = "getWithoutTime";
	private static final String EFTSUBJECT = "eft_subj";

	@Autowired
	Environment env;

	@Autowired
	private Gson gson;

	@Autowired
	RestTemplate restTemplate;

	@Autowired
	JsonParser parser;

	@Autowired
	private DailyTaskReportDao dailyTaskReportDao;

	@Autowired
	private ClientDetailsDao clientDetailsDao;

	@Autowired
	private ProcessDao processDao;

	@Autowired
	private AuditDao auditDao;

	@Autowired
	private ProcessJobMappingDao processJobMappingDao;

	public String fetchRequestList(String pjmId, String frequency) {
		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);
		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.systemDefault());
		today = today.plusMinutes(1);
		JsonArray respArr = callRest(today, pjmId, frequency);
		JsonArray resp = new JsonArray();

		if (frequency.equalsIgnoreCase(DAILY)) {
			resp = freqData(today, 0, 7, 1, frequency, respArr);
		} else if (frequency.equalsIgnoreCase(WEEKLY)) {
			resp = freqData(today, 0, 4, 0, frequency, respArr);
		} else if (frequency.equalsIgnoreCase(MONTHLY)) {
			resp = freqData(today, 0, 12, 0, frequency, respArr);
		}

		JsonObject respObj = fetchFilterRequestListRespObj(resp);

		return respObj.toString();
	}

	private JsonArray freqData(ZonedDateTime end, int loop, int maxLoop, int minus, String frequency,
			JsonArray respArr) {
		JsonArray res = new JsonArray();
		ZonedDateTime start = null;
		String freq = "";
		String day = "";

		if (frequency.equalsIgnoreCase(DAILY)) {
			start = end.minusDays(minus);
			end = end.minusNanos(1);
			freq = start.format(DateTimeFormatter.ISO_LOCAL_DATE);
			day = start.getDayOfWeek().name();
		} else if (frequency.equalsIgnoreCase(WEEKLY)) {
			start = end.minusWeeks(minus).with(DayOfWeek.MONDAY);
			end = end.minusNanos(1);
			freq = setStartEndDates(end, start);
		} else {
			start = end.minusMonths(minus).withDayOfMonth(1);
			freq = start.getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH) + " " + start.getYear();
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "freqData", freq);
		LoggerUtil.log(this.getClass(), Level.INFO, "freqData",
				start.format(DateTimeFormatter.ISO_DATE_TIME) + " " + end.format(DateTimeFormatter.ISO_DATE_TIME));
		++loop;
		int pending = 0;
		int progress = 0;
		int processed = 0;
		int notStarted = 0;
		JsonObject statsObj = new JsonObject();
		for (JsonElement elem : respArr) {
			JsonObject obj = elem.getAsJsonObject();
			boolean checkFlag = getCheckFlag(end, start, obj);
			if (checkFlag) {

				String status = obj.get(STATUS).getAsString();
				if (status.equalsIgnoreCase("New")) {
					++progress;
				} else if (status.equalsIgnoreCase(EXECUTING)) {
					++progress;
				} else if (status.equalsIgnoreCase("MQWAIT")) {
					++progress;
				} else if (status.equalsIgnoreCase(PROCESSED)) {
					++processed;
				} else if (status.equalsIgnoreCase(HUMANINLOOP)) {
					++pending;
				} else if (status.equalsIgnoreCase("Waiting")) {
					++progress;
				} else if (status.toLowerCase().contains("fail")) {
					++pending;
				}
			}
		}
		statsObj.addProperty("Freq", freq);
		statsObj.addProperty("Day", day);
		statsObj.addProperty("New", notStarted);
		statsObj.addProperty(EXECUTING, progress);
		statsObj.addProperty(PROCESSED, processed);
		statsObj.addProperty(HUMANINLOOP, pending);
		res.add(statsObj);
		if (loop < maxLoop) {
			res.addAll(freqData(start, loop, maxLoop, 1, frequency, respArr));
		}
		return res;
	}

	private boolean getCheckFlag(ZonedDateTime end, ZonedDateTime start, JsonObject obj) {
		long timeStamp = obj.get(CREATETIMESTAMP).getAsLong();
		return timeStamp >= start.toInstant().toEpochMilli() && timeStamp <= end.toInstant().toEpochMilli();
	}

	private String setStartEndDates(ZonedDateTime end, ZonedDateTime start) {
		String freq;
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		Date startDate = new Date();
		Date endDate = new Date();
		try {
			startDate = formatter.parse(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
			endDate = formatter.parse(end.format(DateTimeFormatter.ISO_LOCAL_DATE));
		} catch (ParseException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", e.toString());
		}
		DateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
		String startDateFreq = dateFormat.format(startDate);
		String endDateFre = dateFormat.format(endDate);
		freq = startDateFreq.subSequence(0, startDateFreq.length() - 5) + " to "
				+ endDateFre.subSequence(0, endDateFre.length() - 5);
		return freq;
	}

	public JsonArray callRest(ZonedDateTime today, String pjmid, String frequency) {

		long startDay = 0;
		if (frequency.equalsIgnoreCase(DAILY)) {
			final ZonedDateTime last7Days = today.minusDays(7);
			startDay = last7Days.toInstant().toEpochMilli();
		} else if (frequency.equalsIgnoreCase(WEEKLY)) {
			final ZonedDateTime last4Weeks = today.minusWeeks(3).with(DayOfWeek.MONDAY);
			startDay = last4Weeks.toInstant().toEpochMilli();
		} else if (frequency.equalsIgnoreCase(MONTHLY)) {
			final ZonedDateTime last1Year = today.minusYears(1).withDayOfMonth(1);
			startDay = last1Year.toInstant().toEpochMilli();
		}

		long end = today.toInstant().toEpochMilli();

		String resp = null;

		return parser.parse(resp).getAsJsonArray();

	}

	@Override
	public String createBarChart(String type, int frequency, String pjmid) {

		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);
		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.systemDefault());
		today = today.plusMinutes(1);

		final ZonedDateTime lastNDays = today.minusDays(frequency);

		LoggerUtil.log(this.getClass(), Level.INFO, CREATEBARCHART, today.format(DateTimeFormatter.ISO_DATE_TIME) + " "
				+ lastNDays.format(DateTimeFormatter.ISO_DATE_TIME));

		String response = null;

		JsonArray respArr = parser.parse(response).getAsJsonArray();
		gson = new GsonBuilder().create();

		HashMap<String, HashMap<String, Integer>> map = new HashMap<>();

		for (JsonElement elem : respArr) {
			JsonObject obj = elem.getAsJsonObject();

			String reqType = "";
			if (type.equals(CLIENTNAME)) {
				reqType = obj.get(CLIENTNAME).getAsString();
			} else {
				reqType = obj.get(PROCESSTYPE).getAsString();
			}

			String statusValue = obj.get(STATUS).getAsString();

			if (!statusValue.equalsIgnoreCase("New")) {
				if (map.containsKey(reqType)) {
					HashMap<String, Integer> innerMap = map.get(reqType);
					if (innerMap.containsKey(statusValue)) {
						innerMap.put(statusValue, innerMap.get(statusValue) + 1);
					} else {
						innerMap.put(statusValue, 1);
					}
					map.put(reqType, innerMap);
				} else {
					HashMap<String, Integer> innerMap = new HashMap<>();
					innerMap.put(statusValue, 1);
					map.put(reqType, innerMap);
				}
			}
		}

		JsonArray retArr = new JsonArray();
		createAndGetBarChart(type, map, retArr);

		return gson.toJson(retArr);
	}

	public String createPieChart(String pjmId, int frequency) {

		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);
		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.systemDefault());
		today = today.plusMinutes(1);

		final ZonedDateTime lastNDays = today.minusDays(frequency);
		long start = lastNDays.toInstant().toEpochMilli();
		long end = today.toInstant().toEpochMilli();

		String response = null;

		today = today.minusMinutes(1);
		end = today.toInstant().toEpochMilli();

		JsonArray createRespArr = parser.parse(response).getAsJsonArray();
		gson = new Gson();

		HashMap<String, Integer> map = new HashMap<>();
		int total = 0;

		for (JsonElement elem : createRespArr) {
			JsonObject respObj = elem.getAsJsonObject();

			long pieChartTimeStamp = respObj.get(CREATETIMESTAMP).getAsLong();

			try {
				SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy");
				Date createDate = format.parse(format.format(pieChartTimeStamp));
				Date curDate = format.parse(format.format(end));

				DateTime dt1 = new DateTime(createDate);
				DateTime dt2 = new DateTime(curDate);

				int diff = Days.daysBetween(dt1, dt2).getDays();

				String pieChartKey = "Day " + ++diff;

				if (map.containsKey(pieChartKey)) {
					map.put(pieChartKey, map.get(pieChartKey) + 1);
				} else {
					map.put(pieChartKey, 1);
				}
				++total;
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "createPieChart", e.toString());
			}
		}

		JsonArray pieChartArr = new JsonArray();
		for (Map.Entry<String, Integer> entryMap : map.entrySet()) {
			String keyField = entryMap.getKey();
			float val = (float) map.get(keyField);
			int percen = 0;
			if (total > 0)
				percen = Math.round((val / total) * 100);

			JsonObject retObj = new JsonObject();
			retObj.addProperty("Day", keyField);
			retObj.addProperty("Count", map.get(keyField));
			retObj.addProperty("Percentage", percen);
			pieChartArr.add(retObj);
		}
		return gson.toJson(pieChartArr);
	}

	@Override
	public Workbook getTemplate(String idString) throws StatsMoniterCustomException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplate", idString);
		if (idString == null) {
			throw new StatsMoniterCustomException("Input from UI is null");
		}

		JsonObject idJson = parser.parse(idString).getAsJsonObject();
		String frequency = idJson.get(FREQUENCY).getAsString();
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "Frequency from UI: " + frequency);

		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);

		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.of(ZONEID));
		today = today.plusMinutes(1);
		JsonArray respArr = callRestByPassingJson(today, idJson, frequency, parser);
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "Size of array from dbservice: " + respArr.size());

		XSSFWorkbook workbook = new XSSFWorkbook();
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String sheetName = sdf.format(date);
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "Creating Dashboard Excel................");
		XSSFSheet jobDetailsSheet = workbook.createSheet(sheetName);

		XSSFCellStyle headingStyle = workbook.createCellStyle();
		headingStyle.setAlignment(HorizontalAlignment.CENTER);

		headingStyle.setFillBackgroundColor(IndexedColors.AQUA.getIndex());
		headingStyle.setFillPattern(FillPatternType.FINE_DOTS);
		headingStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		headingStyle.setBorderRight(BorderStyle.THIN);
		headingStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		headingStyle.setBorderTop(BorderStyle.THIN);
		headingStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		headingStyle.setBorderLeft(BorderStyle.THIN);
		headingStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());

		XSSFFont font = workbook.createFont();
		font.setFontName(CALIBRI);
		font.setBold(true);
		font.setColor(IndexedColors.BLACK.getIndex());
		headingStyle.setFont(font);

		XSSFCellStyle valueStyle = workbook.createCellStyle();
		valueStyle.setAlignment(HorizontalAlignment.LEFT);

		valueStyle.setBorderBottom(BorderStyle.THIN);
		valueStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		valueStyle.setBorderRight(BorderStyle.THIN);
		valueStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		valueStyle.setBorderTop(BorderStyle.THIN);
		valueStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		valueStyle.setBorderLeft(BorderStyle.THIN);
		valueStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());

		XSSFFont valueFont = workbook.createFont();
		valueFont.setFontName(CALIBRI);
		valueFont.setBold(false);
		valueFont.setColor(IndexedColors.BLACK.getIndex());
		valueStyle.setFont(valueFont);

		XSSFRow row;
		XSSFCell cell;

		int rowCount = 0;

		row = jobDetailsSheet.createRow(rowCount);
		cell = row.createCell(row.getLastCellNum() + 1);
		cell.setCellValue("Business Unit");
		cell.setCellStyle(headingStyle);

		addStringValueForExcelSheet(row, "Business Operations", headingStyle);
		addStringValueForExcelSheet(row, "Market", headingStyle);
		addStringValueForExcelSheet(row, "Ksd Name", headingStyle);
		addStringValueForExcelSheet(row, "Process", headingStyle);
		addStringValueForExcelSheet(row, "Process Type", headingStyle);
		addStringValueForExcelSheet(row, "Client Name/Id", headingStyle);
		addStringValueForExcelSheet(row, "Task Id", headingStyle);
		addStringValueForExcelSheet(row, "Processed By", headingStyle);
		addStringValueForExcelSheet(row, "Frequency", headingStyle);
		addStringValueForExcelSheet(row, "Job Name", headingStyle);
		addStringValueForExcelSheet(row, "EFT/Subject", headingStyle);
		addStringValueForExcelSheet(row, "Receive/Job Run Date", headingStyle);
		addStringValueForExcelSheet(row, "Status", headingStyle);
		addStringValueForExcelSheet(row, "Job Status", headingStyle);
		addStringValueForExcelSheet(row, "BOT ID", headingStyle);
		addStringValueForExcelSheet(row, "SLA", headingStyle);

		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
		DateTimeFormatter centralFormatter = DateTimeFormatter.ofPattern("MMM-dd-yyyy hh:mm a");
		ZonedDateTime dateTime;
		LocalDateTime ldt;
		ZonedDateTime utcZonedDateTime;
		ZonedDateTime centralZonedDateTime;
		String excelDate;
		String setStatus = null;

		for (JsonElement elem : respArr) {
			JsonObject elemObject = elem.getAsJsonObject();
			String status = elemObject.get(STATUS).getAsString();
			String botId = "";
			String maestroURL = env.getProperty("maestroTask");
			row = jobDetailsSheet.createRow(++rowCount);

			cell = row.createCell(row.getLastCellNum() + 1);
			cell.setCellValue(elemObject.get("businessUnit").getAsString());
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get("businessOps").getAsString());
			cell.setCellStyle(valueStyle);

			String tower = elemObject.get("tower") != JsonNull.INSTANCE ? elemObject.get("tower").getAsString() : "";
			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(tower);
			cell.setCellStyle(valueStyle);

			getKsdProsType(valueStyle, row, elemObject);

			setTaskId(workbook, row, elemObject, maestroURL);

			cell = row.createCell(row.getLastCellNum());
			if (status.equalsIgnoreCase("New") || status.equalsIgnoreCase(EXECUTING)
					|| status.equalsIgnoreCase("Waiting") || status.equalsIgnoreCase("MQWAIT")) {
				setStatus = "In Progress";
			} else if (status.equalsIgnoreCase(PROCESSED)) {
				setStatus = "Complete";
				setProcessByifStatuscomplted(cell, elemObject);
			} else if (status.equalsIgnoreCase(HUMANINLOOP) || status.toLowerCase().contains("fail")) {
				setStatus = "Pending";
				setProcessbyIfStatuspending(cell, elemObject);
				botId = elemObject.get(PLUGINNAME) != JsonNull.INSTANCE ? elemObject.get(PLUGINNAME).getAsString() : "";
				String tempBot = elemObject.get("botId") != JsonNull.INSTANCE ? elemObject.get("botId").getAsString()
						: "";
				botId = botId + "/" + tempBot;
			}

			cell.setCellStyle(valueStyle);

			setFrequency(valueStyle, row, elemObject);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get("jobName").getAsString());
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get(EFTSUBJECT).getAsString());
			cell.setCellStyle(valueStyle);

			dateTime = ZonedDateTime.parse(elemObject.get("createDateTime").getAsString(), formatter);

			ldt = LocalDateTime.of(dateTime.getYear(), dateTime.getMonth(), dateTime.getDayOfMonth(),
					dateTime.getHour(), dateTime.getMinute());
			utcZonedDateTime = ldt.atZone(ZoneId.of("UTC"));

			centralZonedDateTime = utcZonedDateTime.withZoneSameInstant(ZoneId.of(ZONEID));

			excelDate = centralFormatter.format(centralZonedDateTime);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(excelDate);
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(setStatus);
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get(STATUS).getAsString());
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(botId);
			cell.setCellStyle(valueStyle);

			setSlaInExcel(valueStyle, row, elemObject);

		}
		LoggerUtil.log(getClass(), Level.INFO, "", "Ending Dashboard Excel Creation................");
		return workbook;
	}

	@Override
	public Workbook getTemplateData(String idString) throws StatsMoniterCustomException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplate", idString);
		if (idString == null) {
			throw new StatsMoniterCustomException("Input from UI is null");
		}

		JsonObject idJson = parser.parse(idString).getAsJsonObject();

		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);

		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.of(ZONEID));
		today = today.plusMinutes(1);
		JsonArray respArr = callRestByPassingJsonData(today, idJson, parser);
		System.out.println("size od array" + respArr.size());
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "Size of array from dbservice: " + respArr.size());

		XSSFWorkbook workbook = new XSSFWorkbook();
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String sheetName = sdf.format(date);
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "Creating Dashboard Excel................");
		XSSFSheet jobDetailsSheet = workbook.createSheet(sheetName);

		XSSFCellStyle headingStyle = workbook.createCellStyle();
		headingStyle.setAlignment(HorizontalAlignment.CENTER);

		headingStyle.setFillBackgroundColor(IndexedColors.AQUA.getIndex());
		headingStyle.setFillPattern(FillPatternType.FINE_DOTS);
		headingStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		headingStyle.setBorderRight(BorderStyle.THIN);
		headingStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		headingStyle.setBorderTop(BorderStyle.THIN);
		headingStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		headingStyle.setBorderLeft(BorderStyle.THIN);
		headingStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());

		XSSFFont font = workbook.createFont();
		font.setFontName(CALIBRI);
		font.setBold(true);
		font.setColor(IndexedColors.BLACK.getIndex());
		headingStyle.setFont(font);

		XSSFCellStyle valueStyle = workbook.createCellStyle();
		valueStyle.setAlignment(HorizontalAlignment.LEFT);

		valueStyle.setBorderBottom(BorderStyle.THIN);
		valueStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		valueStyle.setBorderRight(BorderStyle.THIN);
		valueStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		valueStyle.setBorderTop(BorderStyle.THIN);
		valueStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		valueStyle.setBorderLeft(BorderStyle.THIN);
		valueStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());

		XSSFFont valueFont = workbook.createFont();
		valueFont.setFontName(CALIBRI);
		valueFont.setBold(false);
		valueFont.setColor(IndexedColors.BLACK.getIndex());
		valueStyle.setFont(valueFont);

		XSSFRow row;
		XSSFCell cell;

		int rowCount = 0;

		row = jobDetailsSheet.createRow(rowCount);
		cell = row.createCell(row.getLastCellNum() + 1);
		cell.setCellValue("Business Unit");
		cell.setCellStyle(headingStyle);

		addStringValueForExcelSheet(row, "Business Operations", headingStyle);
		addStringValueForExcelSheet(row, "Market", headingStyle);
		addStringValueForExcelSheet(row, "Ksd Name", headingStyle);
		addStringValueForExcelSheet(row, "Process", headingStyle);
		addStringValueForExcelSheet(row, "Process Type", headingStyle);
		addStringValueForExcelSheet(row, "Client Name/Id", headingStyle);
		addStringValueForExcelSheet(row, "Task Id", headingStyle);
		addStringValueForExcelSheet(row, "Processed By", headingStyle);
		addStringValueForExcelSheet(row, "Frequency", headingStyle);
		addStringValueForExcelSheet(row, "Job Name", headingStyle);
		addStringValueForExcelSheet(row, "EFT/Subject", headingStyle);
		addStringValueForExcelSheet(row, "Receive/Job Run Date", headingStyle);
		addStringValueForExcelSheet(row, "Status", headingStyle);
		addStringValueForExcelSheet(row, "Job Status", headingStyle);
		addStringValueForExcelSheet(row, "BOT ID", headingStyle);
		addStringValueForExcelSheet(row, "SLA", headingStyle);

		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
		DateTimeFormatter centralFormatter = DateTimeFormatter.ofPattern("MMM-dd-yyyy hh:mm a");
		ZonedDateTime dateTime;
		LocalDateTime ldt;
		ZonedDateTime utcZonedDateTime;
		ZonedDateTime centralZonedDateTime;
		String excelDate;
		String setStatus = null;

		for (JsonElement elem : respArr) {
			JsonObject elemObject = elem.getAsJsonObject();
			String status = elemObject.get(STATUS).getAsString();
			String botId = "";
			String maestroURL = env.getProperty("maestroTask");
			row = jobDetailsSheet.createRow(++rowCount);

			cell = row.createCell(row.getLastCellNum() + 1);
			cell.setCellValue(elemObject.get("businessUnit").getAsString());
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get("businessOps").getAsString());
			cell.setCellStyle(valueStyle);

			String tower = elemObject.has("tower") ? getExcelEntryAfterNullAndEmptyCheck(elemObject.get("tower")) : "";
			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(tower);
			cell.setCellStyle(valueStyle);

			getKsdProsType(valueStyle, row, elemObject);

			setTaskId(workbook, row, elemObject, maestroURL);

			cell = row.createCell(row.getLastCellNum());
			if (status.equalsIgnoreCase("New") || status.equalsIgnoreCase(EXECUTING)
					|| status.equalsIgnoreCase("Waiting") || status.equalsIgnoreCase("MQWAIT")) {
				setStatus = "In Progress";
			} else if (status.equalsIgnoreCase(PROCESSED)) {
				setStatus = "Complete";
				setProcessByifStatuscomplted(cell, elemObject);
			} else if (status.equalsIgnoreCase(HUMANINLOOP) || status.toLowerCase().contains("fail")) {
				setStatus = "Pending";
				setProcessbyIfStatuspending(cell, elemObject);
				botId = elemObject.get(PLUGINNAME) != JsonNull.INSTANCE ? elemObject.get(PLUGINNAME).getAsString() : "";
				String tempBot = elemObject.get("botId") != JsonNull.INSTANCE ? elemObject.get("botId").getAsString()
						: "";
				botId = botId + "/" + tempBot;
			}

			cell.setCellStyle(valueStyle);

			setFrequency(valueStyle, row, elemObject);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get("jobName").getAsString());
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get(EFTSUBJECT).getAsString());
			cell.setCellStyle(valueStyle);

			dateTime = ZonedDateTime.parse(elemObject.get("createDateTime").getAsString(), formatter);

			ldt = LocalDateTime.of(dateTime.getYear(), dateTime.getMonth(), dateTime.getDayOfMonth(),
					dateTime.getHour(), dateTime.getMinute());
			utcZonedDateTime = ldt.atZone(ZoneId.of("UTC"));

			centralZonedDateTime = utcZonedDateTime.withZoneSameInstant(ZoneId.of(ZONEID));

			excelDate = centralFormatter.format(centralZonedDateTime);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(excelDate);
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(setStatus);
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(elemObject.get(STATUS).getAsString());
			cell.setCellStyle(valueStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(botId);
			cell.setCellStyle(valueStyle);

			setSlaInExcel(valueStyle, row, elemObject);

		}
		LoggerUtil.log(getClass(), Level.INFO, "", "Ending Dashboard Excel Creation................");
		return workbook;
	}

	private String getExcelEntryAfterNullAndEmptyCheck(JsonElement jsonElement) {
		String entryResp;
		entryResp = jsonElement != JsonNull.INSTANCE ? jsonElement.getAsString() : "";
		return entryResp;
	}

	private void setSlaInExcel(XSSFCellStyle valueStyle, XSSFRow row, JsonObject elemObject) {
		XSSFCell cell;
		SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");

		String sla = getSla(elemObject, format);
		String charSla = String.valueOf(sla);
		format = new SimpleDateFormat(HHMM);
		try {
			Date dt = format.parse(charSla);
			String finalSla = format.format(dt) + " " + "hrs";
			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(finalSla);
			cell.setCellStyle(valueStyle);
		} catch (ParseException e) {
			e.printStackTrace();
		}
	}

	public String getSla(JsonObject obj, SimpleDateFormat format) {

		String status = obj.get(STATUS).getAsString();
		long createDate = obj.get(CREATETIMESTAMP).getAsLong();
		int slaHrs = obj.get("sla").getAsInt();
		if (!status.equalsIgnoreCase("processed")) {
			Date curDate = null;
			try {
				curDate = format.parse(format.format(System.currentTimeMillis()));

			} catch (ParseException e) {

				LoggerUtil.log(this.getClass(), Level.ERROR, "getSla ", "", e.getMessage());
			}
			DateTime dt1 = new DateTime(createDate);
			DateTime dt2 = new DateTime(curDate);
			int diffHrs = Hours.hoursBetween(dt1, dt2).getHours();
			int minDiffrence = Minutes.minutesBetween(dt1, dt2).getMinutes() % 60;

			int hoursValue;
			int minValue;
			if (diffHrs < 0) {
				hoursValue = ~(diffHrs - 1);
			} else {
				hoursValue = diffHrs;
			}
			if (minDiffrence < 0) {
				minValue = ~(minDiffrence - 1);
			} else {
				minValue = minDiffrence;
			}

			String dateStart = slaHrs + ":00";
			String dateStop = hoursValue + ":" + minValue;
			SimpleDateFormat format1 = new SimpleDateFormat(HHMM);
			long diff = 0;
			try {
				Date d1 = format1.parse(dateStart);
				Date d2 = format1.parse(dateStop);

				// Get msec from each, and subtract.
				diff = d1.getTime() - d2.getTime();
			} catch (ParseException e) {

				LoggerUtil.log(this.getClass(), Level.ERROR, "getSla ", "", e.getMessage());
			}

			long diffMinutes = diff / (60 * 1000) % 60;
			long diffHours = diff / (60 * 60 * 1000);
			return diffHours + ":" + diffMinutes;
		} else {
			return " ";
		}
	}

	private void addStringValueForExcelSheet(XSSFRow row, String string, XSSFCellStyle valueStyle) {

		XSSFCell cell;
		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(string);
		cell.setCellStyle(valueStyle);
	}

	private void setFrequency(XSSFCellStyle valueStyle, XSSFRow row, JsonObject elemObject) {
		String frequency;
		XSSFCell cell;
		frequency = elemObject.get(FREQUENCY) != JsonNull.INSTANCE ? elemObject.get(FREQUENCY).getAsString() : "";
		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(frequency);
		cell.setCellStyle(valueStyle);
	}

	private void setProcessbyIfStatuspending(XSSFCell cell, JsonObject elemObject) {
		cell.setCellValue(
				elemObject.get("assignee") != JsonNull.INSTANCE ? elemObject.get("assignee").getAsString() : "");
	}

	private void setProcessByifStatuscomplted(XSSFCell cell, JsonObject elemObject) {

		String processedBy = (elemObject.get(PLUGINNAME) != JsonNull.INSTANCE ? elemObject.get(PLUGINNAME).getAsString()
				: "");
		cell.setCellValue(processedBy + " BOT");
	}

	private void setTaskId(XSSFWorkbook workbook, XSSFRow row, JsonObject elemObject, String maestroURL) {
		XSSFCell cell;
		String taskId;
		cell = row.createCell(row.getLastCellNum());

		taskId = elemObject.get("taskId") != JsonNull.INSTANCE ? elemObject.get("taskId").getAsString() : "";
		if (!taskId.isEmpty()) {
			taskId = maestroURL + taskId;

		}
		cell.setCellValue(taskId);
		XSSFHyperlink link = workbook.getCreationHelper().createHyperlink(HyperlinkType.URL);
		link.setAddress(taskId);
		cell.setHyperlink(link);
		Font valueFont1 = workbook.createFont();
		valueFont1.setFontName(CALIBRI);
		valueFont1.setBold(false);
		valueFont1.setColor(IndexedColors.BLUE.getIndex());
		valueFont1.setUnderline(Font.U_SINGLE);
		XSSFCellStyle valueStyle1 = workbook.createCellStyle();
		valueStyle1.setFont(valueFont1);
		cell.setCellStyle(valueStyle1);
	}

	private void getKsdProsType(XSSFCellStyle valueStyle, XSSFRow row, JsonObject elemObject) {
		XSSFCell cell;
		String combinedClient;
		String ksdName;
		String processType;
		ksdName = elemObject.get("ksdName") != JsonNull.INSTANCE ? elemObject.get("ksdName").getAsString() : "";
		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(ksdName);
		cell.setCellStyle(valueStyle);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(elemObject.get(PROCESSNAME).getAsString());
		cell.setCellStyle(valueStyle);

		processType = elemObject.get(PROCESSTYPE) != JsonNull.INSTANCE ? elemObject.get(PROCESSTYPE).getAsString() : "";
		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(processType);
		cell.setCellStyle(valueStyle);

		combinedClient = elemObject.get(CLIENTNAME).getAsString() + "/" + elemObject.get("clientId").getAsString();
		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(combinedClient);
		cell.setCellStyle(valueStyle);
	}

	public JsonArray callRestByPassingJson(ZonedDateTime today, JsonObject idJson, String frequency,
			JsonParser parser) {
		JsonObject inputDbJson = idJson;
		long start = 0;
		if (frequency.equalsIgnoreCase(DAILY)) {
			final ZonedDateTime last7Days = today.minusDays(7);
			start = last7Days.toInstant().toEpochMilli();
		} else if (frequency.equalsIgnoreCase(WEEKLY)) {
			final ZonedDateTime last4Weeks = today.minusWeeks(3).with(DayOfWeek.MONDAY);
			start = last4Weeks.toInstant().toEpochMilli();
		} else if (frequency.equalsIgnoreCase(MONTHLY)) {

			final ZonedDateTime previoucurrentmonth = today.minusMonths(1).withDayOfMonth(1);
			start = previoucurrentmonth.toInstant().toEpochMilli();
		}

		ZonedDateTime endZonedDateTime = today.minusNanos(1);
		long end = endZonedDateTime.toInstant().toEpochMilli();
		inputDbJson.remove(FREQUENCY);
		inputDbJson.addProperty(STARTTIMESTAMP, start);
		inputDbJson.addProperty(ENDTIMESTAMP, end);

		LoggerUtil.log(getClass(), Level.INFO, "", "startTimestamp: " + start);
		LoggerUtil.log(getClass(), Level.INFO, "", "endTimestamp: " + end);
		String resp = null;
		return parser.parse(resp).getAsJsonArray();

	}

	public JsonArray callRestByPassingJsonData(ZonedDateTime today, JsonObject idJson, JsonParser parser) {

		String resp = null;

		return parser.parse(resp).getAsJsonArray();

	}

	@Override
	public String fetchFilterRequestList(String requestBody)
			throws JsonGenerationException, JsonMappingException, IOException {
		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);
		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.of(ZONEID));
		today = today.plusMinutes(1);

		JsonObject jsonObject;
		JsonObject json = parser.parse(requestBody).getAsJsonObject();
		jsonObject = parser.parse(requestBody).getAsJsonObject();
		JsonArray respArr = callFilterRest(today, jsonObject);
		JsonArray resp = new JsonArray();
		if (json.get(FREQUENCY).getAsString().equalsIgnoreCase(DAILY)) {
			resp = freqData(today, 0, 7, 1, json.get(FREQUENCY).getAsString(), respArr);
		} else if (json.get(FREQUENCY).getAsString().equalsIgnoreCase(WEEKLY)) {
			resp = freqData(today, 0, 4, 0, json.get(FREQUENCY).getAsString(), respArr);
		} else if (json.get(FREQUENCY).getAsString().equalsIgnoreCase(MONTHLY)) {

			resp = freqData(today, 0, 2, 0, json.get(FREQUENCY).getAsString(), respArr);
		}
		JsonObject respObj = fetchFilterRequestListRespObj(resp);

		return respObj.toString();
	}

	private JsonObject fetchFilterRequestListRespObj(JsonArray resp) {
		int totalInProgress = 0;
		int totalHumanInLoop = 0;
		int totalCompleted = 0;
		int total = 0;
		for (JsonElement elem : resp) {
			JsonObject obj = elem.getAsJsonObject();
			totalInProgress += obj.get(EXECUTING).getAsInt();
			totalHumanInLoop += obj.get(HUMANINLOOP).getAsInt();
			totalCompleted += obj.get(PROCESSED).getAsInt();
		}

		total = totalInProgress + totalHumanInLoop + totalCompleted;

		JsonObject respObj = new JsonObject();
		respObj.addProperty("total", total);
		respObj.addProperty("totalInProgress", totalInProgress);
		respObj.addProperty("totalHumanInLoop", totalHumanInLoop);
		respObj.addProperty("totalCompleted", totalCompleted);
		respObj.add("data", resp);
		return respObj;
	}

	@Override
	public String getBarChart(String requestBody) throws JsonParseException, JsonMappingException, IOException {

		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);
		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.of(ZONEID));
		today = today.plusMinutes(1);
		JsonParser jsonParser = new JsonParser();
		JsonObject jsonObject = jsonParser.parse(requestBody).getAsJsonObject();
		String type = jsonObject.get("type").getAsString();
		LoggerUtil.log(this.getClass(), Level.INFO, "getBarChart", today.format(DateTimeFormatter.ISO_DATE_TIME) + " ");

		JsonArray asJsonArray = jsonObject.get("clientId").getAsJsonArray();
		List<String> clientId = IntStream.range(0, asJsonArray.size()).mapToObj(asJsonArray::get).map(Object::toString)
				.collect(Collectors.toList());

		JsonArray asJsonArray2 = jsonObject.get("processId").getAsJsonArray();
		List<String> processId1 = IntStream.range(0, asJsonArray2.size()).mapToObj(asJsonArray2::get)
				.map(Object::toString).collect(Collectors.toList());
		List<Long> processId = new ArrayList<>();
		for (String long1 : processId1) {
			processId.add(Long.parseLong(long1));

		}

		List<String> clientNames = clientDetailsDao.getClientDetails(clientId);

		List<String> processNames = processDao.findByIds(processId);

		gson = new GsonBuilder().create();
		HashMap<String, HashMap<String, Integer>> map = new HashMap<>();

		String reqType = "";
		if (type.equals(CLIENTNAME)) {
			for (String clientName : clientNames) {
				reqType = clientName;
			}
			String status = "Success";
			if (status.toLowerCase().contains("fail")) {
				status = "Failed";
			}
			if (map.containsKey(reqType)) {
				HashMap<String, Integer> innerMap = map.get(reqType);
				if (innerMap.containsKey(status)) {
					innerMap.put(status, innerMap.get(status) + 1);
				} else {
					innerMap.put(status, 1);
				}
				map.put(reqType, innerMap);
			} else {
				HashMap<String, Integer> innerMap = new HashMap<>();
				innerMap.put(status, 1);
				map.put(reqType, innerMap);
			}

		} else if (type.equals(PROCESSNAME)) {
			for (String processName : processNames) {
				reqType = processName;
				String status = "Success";
				if (status.toLowerCase().contains("fail")) {
					status = "Failed";
				}
				if (map.containsKey(reqType)) {
					HashMap<String, Integer> innerMap = map.get(reqType);
					if (innerMap.containsKey(status)) {
						innerMap.put(status, innerMap.get(status) + 1);
					} else {
						innerMap.put(status, 1);
					}
					map.put(reqType, innerMap);
				} else {
					HashMap<String, Integer> innerMap = new HashMap<>();
					innerMap.put(status, 1);
					map.put(reqType, innerMap);
				}
			}

		}

		JsonArray retArr = new JsonArray();
		createAndGetBarChart(type, map, retArr);

		return retArr.toString();
	}

	private void createAndGetBarChart(String type, HashMap<String, HashMap<String, Integer>> map, JsonArray retArr) {
		for (Map.Entry<String, HashMap<String, Integer>> entry : map.entrySet()) {
			String str = entry.getKey();
			JsonObject retObj = new JsonObject();
			retObj.addProperty(type, str);
			HashMap<String, Integer> innerMap = map.get(str);
			for (Map.Entry<String, Integer> innerEntry : innerMap.entrySet()) {
				String inner = innerEntry.getKey();
				retObj.addProperty(inner, innerMap.get(inner));
			}
			retArr.add(retObj);
		}
	}

	public JsonArray callFilterRest(ZonedDateTime today, JsonObject jsonRequest)
			throws JsonGenerationException, JsonMappingException, IOException {
		long start = 0;
		JsonObject jsonObject = jsonRequest;
		if (jsonRequest.get(FREQUENCY).getAsString().equalsIgnoreCase(DAILY)) {
			final ZonedDateTime last7Days = today.minusDays(7);
			start = last7Days.toInstant().toEpochMilli();
		} else if (jsonRequest.get(FREQUENCY).getAsString().equalsIgnoreCase(WEEKLY)) {
			final ZonedDateTime last4Weeks = today.minusWeeks(3).with(DayOfWeek.MONDAY);
			start = last4Weeks.toInstant().toEpochMilli();
		} else if (jsonRequest.get(FREQUENCY).getAsString().equalsIgnoreCase(MONTHLY)) {

			final ZonedDateTime previouscurrentmonth = today.minusMonths(1).withDayOfMonth(1);
			start = previouscurrentmonth.toInstant().toEpochMilli();
		}
		jsonObject.remove(FREQUENCY);
		ZonedDateTime endZonedDateTime = today.minusNanos(1);
		long end = endZonedDateTime.toInstant().toEpochMilli();
		jsonObject.addProperty(STARTTIMESTAMP, start);
		jsonObject.addProperty(ENDTIMESTAMP, end);

		String businessUnit = jsonObject.get("businessUnit").getAsString();
		List<String> clientId = Arrays
				.asList(new ObjectMapper().readValue(jsonObject.get("clientId").getAsString(), String[].class));
		List<Long> processId = Arrays
				.asList(new ObjectMapper().readValue(jsonObject.get("processId").getAsString(), Long[].class));

		List<DailyTaskReport> dailyTaskReport = dailyTaskReportDao.getDailyTaskReport(businessUnit, clientId,
				processId);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String resp = objectMapper.writeValueAsString(dailyTaskReport);
		return parser.parse(resp).getAsJsonArray();

	}

	@Override
	public String getPieChart(String requestBody) throws JsonParseException, JsonMappingException, IOException {

		final ZonedDateTime input = ZonedDateTime.now();
		LocalDateTime localDateTime = LocalDateTime.of(input.getYear(), input.getMonthValue(), input.getDayOfMonth(),
				23, 59);
		ZonedDateTime today = ZonedDateTime.of(localDateTime, ZoneId.of(ZONEID));
		today = today.plusMinutes(1);
		JsonParser jsonParser = new JsonParser();
		JsonObject jsonObject = jsonParser.parse(requestBody).getAsJsonObject();
		final ZonedDateTime lastNDays = today.minusDays(jsonObject.get(FREQUENCY).getAsLong());
		ZonedDateTime copuToday = today;
		long start = lastNDays.toInstant().toEpochMilli();
		long end = today.toInstant().toEpochMilli();
		ZonedDateTime endZonedDateTime = copuToday.minusNanos(1);
		long endTimestamp = endZonedDateTime.toInstant().toEpochMilli();

		jsonObject.remove(FREQUENCY);
		jsonObject.addProperty(STARTTIMESTAMP, start);
		jsonObject.addProperty(ENDTIMESTAMP, endTimestamp);

		List<String> clientId = new ArrayList<>();
		JsonArray asJsonArray3 = jsonObject.get("clientId").getAsJsonArray();
		for (JsonElement jsonElement : asJsonArray3) {
			clientId.add(jsonElement.getAsString());
		}

		List<Long> pjmIds = processJobMappingDao.getPjmIdsByClientCode(clientId);

		List<Long> timeStamp2 = auditDao.getTimeStamp(pjmIds);

		gson = new Gson();

		HashMap<String, Integer> map = new HashMap<>();
		int total = 0;

		for (Long timeStamp : timeStamp2) {

			try {
				SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy");
				Date createDate = format.parse(format.format(timeStamp));
				Date curDate = format.parse(format.format(end));
				DateTime dt1 = new DateTime(createDate);
				DateTime dt2 = new DateTime(curDate);

				int diff = Days.daysBetween(dt1, dt2).getDays();

				String key = "Day " + diff;

				if (map.containsKey(key)) {
					map.put(key, map.get(key) + 1);
				} else {
					map.put(key, 1);
				}
				++total;
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "createPieChart", e.toString());
			}
		}

		JsonArray retArr = new JsonArray();
		for (Map.Entry<String, Integer> entry : map.entrySet()) {
			String key = entry.getKey();
			float val = (float) map.get(key);
			int perc = 0;
			if (total > 0)
				perc = Math.round((val / total) * 100);

			JsonObject retObj = new JsonObject();
			retObj.addProperty("Day", key);
			retObj.addProperty("Count", map.get(key));
			retObj.addProperty("Percentage", perc);
			retArr.add(retObj);
		}

		return gson.toJson(retArr);

	}

	@Override
	public AllocationResponse getTaskDetails(String adid) {

		ResponseEntity<String> response = null;
		String url = env.getProperty(URL) + env.getProperty(GETTASKDETAILS) + adid;
		List<ClientDetailsList> clientList = new ArrayList<>();
		List<AllocationModel> requestList = new ArrayList<>();
		List<AnalystList> analystList = new ArrayList<>();
		response = restTemplate.getForEntity(url, String.class);
		JsonObject json = parser.parse(response.getBody()).getAsJsonObject();
		JsonArray clientArray = json.get("clientDetailsList").getAsJsonArray();
		for (int i = 0; i < clientArray.size(); i++) {
			ClientDetailsList clientObject = gson.fromJson(clientArray.get(i).getAsJsonObject(),
					ClientDetailsList.class);
			clientList.add(clientObject);
		}

		return allocationResponseDet(clientList, requestList, analystList, json);
	}

	@Override
	public List<AllocationModel> updateRequestQueue(List<AllocationUpdate> request) throws URISyntaxException {

		List<AllocationUpdateBO> boList = new ArrayList<>();

		String url = "";
		ResponseEntity<String> response;
		if (request != null) {

			for (AllocationUpdate object : request) {
				AllocationUpdateBO boObject = new AllocationUpdateBO();
				boObject.setId(object.getId());
				boObject.setPreviousAssignee(object.getPreviousAssignee());
				boObject.setCurrentAssignee(object.getNewAssignee());
				boObject.setAssignedBy(object.getAssignedBy());
				boList.add(boObject);
			}
		}

		url = env.getProperty(URL) + env.getProperty(UPDATETASK);
		URI uri = new URI(url);
		List<AllocationModel> modelList = new ArrayList<>();

		HttpHeaders headers = new HttpHeaders();
		headers.set("Content-Type", "application/json");

		HttpEntity<List<AllocationUpdateBO>> dbRequest = new HttpEntity<>(boList, headers);

		response = restTemplate.postForEntity(uri, dbRequest, String.class);
		JsonArray array = parser.parse(response.getBody()).getAsJsonArray();
		for (int i = 0; i < array.size(); i++) {
			AllocationModel modelDO = gson.fromJson(array.get(i).getAsJsonObject(), AllocationModel.class);
			JsonObject allocation = array.get(i).getAsJsonObject();
			modelDO.setEftSubject(allocation.get(EFTSUBJECT).getAsString());
			modelList.add(modelDO);
		}
		return modelList;
	}

	@Override
	public AllocationResponse getModifyClient(String clientid, String clientCode) {

		String response = null;
		List<ClientDetailsList> clientList = new ArrayList<>();
		List<AllocationModel> requestList = new ArrayList<>();
		List<AnalystList> analystList = new ArrayList<>();
		JsonObject json = parser.parse(response).getAsJsonObject();

		return allocationResponseDet(clientList, requestList, analystList, json);

	}

	private AllocationResponse allocationResponseDet(List<ClientDetailsList> clientList,
			List<AllocationModel> requestList, List<AnalystList> analystList, JsonObject json) {
		JsonArray requestQueueArray = json.get("requestQueueList").getAsJsonArray();
		for (int i = 0; i < requestQueueArray.size(); i++) {
			AllocationModel requestQueue = gson.fromJson(requestQueueArray.get(i).getAsJsonObject(),
					AllocationModel.class);
			JsonObject allocation = requestQueueArray.get(i).getAsJsonObject();
			requestQueue.setEftSubject(allocation.get(EFTSUBJECT).getAsString());
			requestList.add(requestQueue);
		}

		JsonArray analystArray = json.get("analystList").getAsJsonArray();
		for (int i = 0; i < analystArray.size(); i++) {
			AnalystList analyst = gson.fromJson(analystArray.get(i).getAsJsonObject(), AnalystList.class);
			analystList.add(analyst);
		}
		AllocationResponse responseJson = new AllocationResponse();
		responseJson.setClientDetailsList(clientList);
		responseJson.setRequestQueueList(requestList);
		responseJson.setAnalystList(analystList);
		return responseJson;
	}

}
