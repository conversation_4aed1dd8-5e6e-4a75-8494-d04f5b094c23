package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaUpdateActivityDao;
import com.wipro.fipc.dao.tba.TbaUpdateJsonKeyDao;
import com.wipro.fipc.dao.tba.TbaUpdateMetadataDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.generated.TbaPendingEvent;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.ITbaPendingEventConfigService;
import com.wipro.fipc.tba.service.PendingEventTbaService;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;

@Service
public class TbaPendingEventconfigServiceImpl implements ITbaPendingEventConfigService {
	@Autowired
	Environment env;

	@Autowired
	Gson gson;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;
	@Autowired
	PendingEventTbaService pendingEventTbaService;

	@Autowired
	private TbaUpdateActivityDao tbaUpdateActivityDao;

	@Autowired
	private TbaUpdateMetadataDao tbaUpdateMetadataDao;

	@Autowired
	private TbaUpdateJsonKeyDao tbaUpdateJsonKeyDao;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	/* Create Tba PendingEventConfig and Save */
	@Override
	public String createTbaPendingEventConfig(List<TbaPendingEvent> pendingEventDto, String appName,
			String sessionToken) throws URISyntaxException, IOException {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		for (TbaPendingEvent tbaPendingEvent : pendingEventDto) {
			tbaPendingEvent.setCreatedDate(new Date());
			tbaPendingEvent.setUpdatedDate(new Date());
			tbaPendingEvent.setCreatedBy(adId);
			tbaPendingEvent.setUpdatedBy(adId);
			tbaPendingEvent.setActiveFlag("T");
			if (tbaPendingEvent.getJsonKeyId() != null) {
				String tbaFieldName = tbaUpdateJsonKeyDao
						.findTbaFieldNameById(Long.parseLong(tbaPendingEvent.getJsonKeyId()));
				if (tbaFieldName != null)
					tbaPendingEvent.setTbaFieldName(tbaFieldName);
			}

			if (!StringUtils.equalsIgnoreCase("Y", tbaPendingEvent.getManualFlag())) {
				if (StringUtils.isNotBlank(tbaPendingEvent.getPanelId())
						&& StringUtils.isNotBlank(tbaPendingEvent.getClientId())
						&& StringUtils.isNotBlank(tbaPendingEvent.getMetaData())) {
					// transId
					getUpdateMetaData(tbaPendingEvent);

					// classId
					if (tbaPendingEvent.getActivityId() != null && tbaPendingEvent.getActivityId() != 0) {
						getUpdateActivity(tbaPendingEvent);
					}
				}
				if (StringUtils.equalsIgnoreCase(tbaPendingEvent.getIdentifyFlag(), "event")) {
					tbaPendingEvent.setParNm("pendingevent");
				}
			}

			LoggerUtil.log(this.getClass(), Level.INFO, "createTbaPendingEventConfig>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Save" + ",PJMID = "
							+ tbaPendingEvent.getProcessJobMapping().getId() + ",ADID: " + adId);
		}
		List<com.wipro.fipc.entity.tba.TbaPendingEvent> newLayout = new ArrayList<>();

		for (TbaPendingEvent req : pendingEventDto) {
			com.wipro.fipc.entity.tba.TbaPendingEvent tbaPendingEvent = new com.wipro.fipc.entity.tba.TbaPendingEvent();
			tbaPendingEvent.setUpdatedDate(new Date());
			tbaPendingEvent.setActiveFlag('T');
			tbaPendingEvent.setUpdatedBy(req.getUpdatedBy());
			tbaPendingEvent.setCreatedDate(req.getCreatedDate());
			tbaPendingEvent.setCreatedBy(req.getCreatedBy());

			if (req.getProcessJobMapping() != null) {
				ProcessJobMapping processJobMapping = new ProcessJobMapping();
				com.wipro.fipc.model.generated.ProcessJobMapping processJobMapping2 = req.getProcessJobMapping();
				processJobMapping.setId(processJobMapping2.getId());
				tbaPendingEvent.setProcessJobMapping(processJobMapping);
			}

			if (req.getProcessJobMapping() != null && null != req.getProcessJobMapping().getClientDetails()) {
				String clientCode = req.getProcessJobMapping().getClientDetails().getClientCode();
				tbaPendingEvent.setClientId(clientCode);
			}

			if (req.getTbaFieldName() != null)
				tbaPendingEvent.setTbaFieldName(req.getTbaFieldName());

			if (req.getBaseKey() != null)
				tbaPendingEvent.setBaseKey(req.getBaseKey());
			else
				tbaPendingEvent.setBaseKey("");

			if (req.getSubKey() != null)
				tbaPendingEvent.setSubKey(req.getSubKey());
			else
				tbaPendingEvent.setSubKey("");

			if (req.getJsonKey() != null)
				tbaPendingEvent.setJsonKey(req.getJsonKey());
			else
				tbaPendingEvent.setJsonKey("");

			if (req.getMetaData() != null) {
				tbaPendingEvent.setMetaData(req.getMetaData());
			} else {
				tbaPendingEvent.setMetaData("");
			}
			tbaPendingEvent.setPanelId(req.getPanelId());
			if (req.getTransId() != null) {
				tbaPendingEvent.setTransId(req.getTransId());
			}

			if (req.getClassId() != null)
				tbaPendingEvent.setClassId(req.getClassId());

			if (req.getPanelDisc() != null)
				tbaPendingEvent.setPanelDisc(req.getPanelDisc());
			if (req.getPanelId() != null)
				tbaPendingEvent.setPanelId(req.getPanelId());

			tbaPendingEvent.setEventName(req.getEventName());
			tbaPendingEvent.setParNm(req.getParNm());
			tbaPendingEvent.setSequence(req.getSequence());
			tbaPendingEvent.setIdentifier(req.getIdentifier());

			if (req.getEventLongDesc() != null)
				tbaPendingEvent.setEventLongDesc(req.getEventLongDesc());

			if (req.getPendgEvntDefName() != null)
				tbaPendingEvent.setPendgEvntDefName(req.getPendgEvntDefName());

			if (req.getIdentifyFlag() != null)
				tbaPendingEvent.setIdentifyFlag(req.getIdentifyFlag());
			if (req.getManualFlag() != null)
				tbaPendingEvent.setManualFlag(req.getManualFlag().charAt(0));
			
			tbaPendingEvent.setCriticalEdits(req.getCriticalEdits());
			tbaPendingEvent.setCriticalEdits(req.getCriticalEdits());

			newLayout.add(tbaPendingEvent);
		}
		ResponseDto response = new ResponseDto();
		List<CommonResRowBO> mydata = pendingEventTbaService.saveIfNotDuplicate(newLayout);
		int size = mydata.size();
		if (size == 1) {
			if (mydata.get(0).getStatus().equals("Failure")) {
				response.setData(objectMapper.writeValueAsString(mydata));
				response.setStatus(HolmesAppConstants.FAILED);
				response.setMessage("Records are Duplipcate.");

				return objectMapper.writeValueAsString(response);
			} else {
				response.setData(objectMapper.writeValueAsString(mydata));
				response.setStatus(HolmesAppConstants.SUCCESS);
				response.setMessage("Records Saved Successfully.");

				return objectMapper.writeValueAsString(response);
			}

		} else {
			if (mydata.get(0).getStatus().equals("Failure") && mydata.get(1).getStatus().equals("Failure")) {
				response.setData(objectMapper.writeValueAsString(mydata));
				response.setStatus(HolmesAppConstants.FAILED);
				response.setMessage("Records are Duplipcate.");

				return objectMapper.writeValueAsString(response);
			} else {
				response.setData(objectMapper.writeValueAsString(mydata));
				response.setStatus(HolmesAppConstants.SUCCESS);
				response.setMessage("Records Saved Successfully.");

				return objectMapper.writeValueAsString(response);
			}

		}
	}

	/* Get TbaPendingEvent */

	private void getUpdateActivity(TbaPendingEvent tbaPendingEvent) {

		com.wipro.fipc.entity.tba.TbaUpdateActivity activity = tbaUpdateActivityDao.findByActivityIdAndPanelId(
				Integer.valueOf(tbaPendingEvent.getActivityId()), Integer.valueOf(tbaPendingEvent.getPanelId()),
				customBeanUtils.checkForClientCode(tbaPendingEvent.getClientId()));
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaPendingEventConfig5", "clasId: ", activity.getClassId());
		tbaPendingEvent.setClassId(String.valueOf(activity.getClassId()));
	}

	/* Get UpdateMetaData */

	private void getUpdateMetaData(TbaPendingEvent tbaPendingEvent) throws URISyntaxException, IOException {

		String encodedMetaData = tbaPendingEvent.getMetaData();

		if (tbaPendingEvent.getMetaData().equals("") || tbaPendingEvent.getMetaData() == null) {
			encodedMetaData = "EMPTY";
		}

		com.wipro.fipc.entity.tba.TbaUpdateMetaData metaData = tbaUpdateMetadataDao.findByMetadataAndPanelId(
				Integer.valueOf(tbaPendingEvent.getPanelId()), encodedMetaData,
				customBeanUtils.checkForClientCode(tbaPendingEvent.getClientId()));

		if (metaData != null) {
			LoggerUtil.log(this.getClass(), Level.INFO, "createTbaPendingEventConfig3", "transid: ",
					metaData.getTransId());
			tbaPendingEvent.setTransId(metaData.getTransId());
		}

	}

	/* Update Pending Event object */
	@Override
	@Transactional
	public String updatePendingEventConfig(List<TbaPendingEvent> entity, String appName, String sessionToken) {
		HttpHeaders headers = new HttpHeaders();
		headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.CONTENT_TYPE_VALUE);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
			for (TbaPendingEvent tbaPendingEvent : entity) {
				com.wipro.fipc.entity.tba.TbaPendingEvent ob = pendingEventTbaService.findById(tbaPendingEvent.getId());
				String resp = objectMapper.writeValueAsString(ob);

				TbaPendingEvent config = null;
				if (StringUtils.isNotBlank(resp)) {
					config = new ObjectMapper().readValue(resp, TbaPendingEvent.class);
					tbaPendingEvent.setId(config.getId());
					tbaPendingEvent.setCreatedBy(config.getCreatedBy());
					tbaPendingEvent.setCreatedDate(config.getCreatedDate());
					tbaPendingEvent.setManualFlag(config.getManualFlag());
					if (tbaPendingEvent.getJsonKeyId() != null) {
						String tbaFieldName = tbaUpdateJsonKeyDao
								.findTbaFieldNameById(Long.parseLong(tbaPendingEvent.getJsonKeyId()));
						if (tbaFieldName != null)
							tbaPendingEvent.setTbaFieldName(tbaFieldName);
					}
				}
				tbaPendingEvent.setUpdatedDate(new Date());
				tbaPendingEvent.setUpdatedBy(adId);
				tbaPendingEvent.setActiveFlag("T");

				if (!StringUtils.equalsIgnoreCase("Y", tbaPendingEvent.getManualFlag())) {

					if (StringUtils.isNotBlank(tbaPendingEvent.getPanelId())
							&& StringUtils.isNotBlank(tbaPendingEvent.getClientId())) {

						// transId

						getUpdateMetaData(tbaPendingEvent);

						// classId
						if (tbaPendingEvent.getActivityId() != null && tbaPendingEvent.getActivityId() != 0) {
							getUpdateActivity(tbaPendingEvent);
						}

					}

				}
				LoggerUtil.log(this.getClass(), Level.INFO, "updatePendingEventConfig>>>>>>>>",
						"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
								+ tbaPendingEvent.getProcessJobMapping().getId() + ",ADID: " + adId);
			}
			List<com.wipro.fipc.entity.tba.TbaPendingEvent> newLayout = new ArrayList<>();
			for (TbaPendingEvent req : entity) {
				com.wipro.fipc.entity.tba.TbaPendingEvent tbaPendingEvent = new com.wipro.fipc.entity.tba.TbaPendingEvent();
				tbaPendingEvent.setId(req.getId());
				tbaPendingEvent.setUpdatedDate(new Date());
				tbaPendingEvent.setActiveFlag('T');
				tbaPendingEvent.setUpdatedBy(req.getUpdatedBy());
				tbaPendingEvent.setCreatedDate(req.getCreatedDate());
				tbaPendingEvent.setCreatedBy(req.getCreatedBy());

				if (req.getProcessJobMapping() != null) {
					ProcessJobMapping processJobMapping = new ProcessJobMapping();
					com.wipro.fipc.model.generated.ProcessJobMapping processJobMapping2 = req.getProcessJobMapping();
					processJobMapping.setId(processJobMapping2.getId());
					tbaPendingEvent.setProcessJobMapping(processJobMapping);
				}

				if (req.getProcessJobMapping() != null && null != req.getProcessJobMapping().getClientDetails()) {
					String clientCode = req.getProcessJobMapping().getClientDetails().getClientCode();
					tbaPendingEvent.setClientId(clientCode);
				}

				if (req.getTbaFieldName() != null)
					tbaPendingEvent.setTbaFieldName(req.getTbaFieldName());

				if (req.getBaseKey() != null)
					tbaPendingEvent.setBaseKey(req.getBaseKey());
				else
					tbaPendingEvent.setBaseKey("");

				if (req.getSubKey() != null)
					tbaPendingEvent.setSubKey(req.getSubKey());
				else
					tbaPendingEvent.setSubKey("");

				if (req.getJsonKey() != null)
					tbaPendingEvent.setJsonKey(req.getJsonKey());
				else
					tbaPendingEvent.setJsonKey("");

				if (req.getMetaData() != null) {
					tbaPendingEvent.setMetaData(req.getMetaData());
				} else {
					tbaPendingEvent.setMetaData("");
				}
				tbaPendingEvent.setPanelId(req.getPanelId());
				if (req.getTransId() != null) {
					tbaPendingEvent.setTransId(req.getTransId());
				}

				if (req.getClassId() != null)
					tbaPendingEvent.setClassId(req.getClassId());

				if (req.getPanelDisc() != null)
					tbaPendingEvent.setPanelDisc(req.getPanelDisc());
				if (req.getPanelId() != null)
					tbaPendingEvent.setPanelId(req.getPanelId());

				tbaPendingEvent.setEventName(req.getEventName());
				tbaPendingEvent.setParNm(req.getParNm());
				tbaPendingEvent.setSequence(req.getSequence());
				tbaPendingEvent.setIdentifier(req.getIdentifier());

				if (req.getEventLongDesc() != null)
					tbaPendingEvent.setEventLongDesc(req.getEventLongDesc());

				if (req.getPendgEvntDefName() != null)
					tbaPendingEvent.setPendgEvntDefName(req.getPendgEvntDefName());

				if (req.getIdentifyFlag() != null)
					tbaPendingEvent.setIdentifyFlag(req.getIdentifyFlag());
				if (req.getManualFlag() != null)
					tbaPendingEvent.setManualFlag(req.getManualFlag().charAt(0));

				tbaPendingEvent.setProcessMultipleOccurrences(req.getProcessMultipleOccurrences());
				tbaPendingEvent.setCriticalEdits(req.getCriticalEdits());
				newLayout.add(tbaPendingEvent);
			}
			List<CommonResRowBO> mydata = pendingEventTbaService.saveIfNotDuplicate(newLayout);
			String responsedata = objectMapper.writeValueAsString(mydata);
			ResponseDto response = new ResponseDto();
			response.setData(responsedata);
			response.setStatus("success");
			response.setMessage("Records Updated Successfully.");
			return objectMapper.writeValueAsString(response);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updatePendingEventConfig1",
					"updatePendingEventConfig Exception : " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}

	}

	/* Get PendingEvent Details */
	@Override
	public String getPendingEventDetails(String pjmId) {
		String res = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getPendingEventDetails>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,PjmId=" + pjmId);

		List<String> columnName1 = new ArrayList<>();
		columnName1.add("process_job_mapping_id");
		columnName1.add("active_flag");

		List<String> columnCondition1 = new ArrayList<>();
		columnCondition1.add(HolmesAppConstants.EQUAL);
		columnCondition1.add(HolmesAppConstants.EQUAL);

		List<String> columnValue1 = new ArrayList<>();
		columnValue1.add(pjmId);

		columnValue1.add("T");

		List<com.wipro.fipc.entity.tba.TbaPendingEvent> mydata = pendingEventTbaService
				.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		return res;
	}

	/* Get Pending EventMasterDetails */

	@Override
	public String getPendingEventMasterDetails(String clientId) {

		try {
			int clientID = customBeanUtils.checkForClientCode(clientId);
			List<com.wipro.fipc.entity.TbaPendingEventMaster> mydata = pendingEventTbaService
					.getTbaPendingEventMasterData(clientID);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String masterData = objectMapper.writeValueAsString(mydata);
			return masterData;

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updatePendingEventConfig",
					"updatePendingEventConfig Exception : ", e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	/* Delete PendingEventConfig Details */
	@Override
	public String deletePendingEventConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken) {

		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		entities.stream().forEach(s -> {
			s.setUpdatedBy(adId);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deletePendingEventConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});
		List<com.wipro.fipc.entity.tba.TbaPendingEvent> newLayout = new ArrayList<>();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		for (CommonDeleteDTO req : entities) {

			com.wipro.fipc.entity.tba.TbaPendingEvent obj = null;
			try {
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				obj = objectMapper.convertValue(req, com.wipro.fipc.entity.tba.TbaPendingEvent.class);
			} catch (Exception e) {
				e.printStackTrace();
			}
			newLayout.add(obj);
		}

		List<CommonRowBO> mydata = pendingEventTbaService.deletemultiplerows(1, newLayout);
		String res = null;
		try {

			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		return res;

	}

}