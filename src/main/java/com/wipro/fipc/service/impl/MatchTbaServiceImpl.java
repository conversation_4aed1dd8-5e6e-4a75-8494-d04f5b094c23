package com.wipro.fipc.service.impl;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.Processcontrolsupport.RulesConfigContService;
import com.wipro.fipc.Processcontrolsupport.TbaUpdateConfigContService;
import com.wipro.fipc.SourceTBAMatchsupport.RulesDefinitionController;
import com.wipro.fipc.SourceTBAMatchsupport.TbaMatchConfigContService;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaMatchConfig;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.fileLayoutsupport.LayoutConfigContService;
import com.wipro.fipc.fileLayoutsupport.TbaInquiryConfigContService1;
import com.wipro.fipc.fileLayoutsupport.TbaNoticeInqConfigContService;
import com.wipro.fipc.model.MatchTba;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.MatchTbaService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class MatchTbaServiceImpl implements MatchTbaService {

	@Autowired
	Environment env;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	LayoutConfigContService objLayoutConfigContService;

	@Autowired
	TbaInquiryConfigContService1 objTbaInquiryConfigContService1;

	@Autowired
	TbaNoticeInqConfigContService objTbaNoticeInqConfigContService;

	@Autowired
	RulesDefinitionController objRulesDefinitionController;

	@Autowired
	TbaUpdateConfigContService objTbaUpdateConfigContService;

	@Autowired
	TbaMatchConfigContService objTbaMatchConfigContService;

	@Autowired
	RulesConfigContService objRulesConfigContService;

	private static final String UNABLE_TO_CONNECT = "Unable to connect to DB Service due to {} \n";
	private static final String GETRESULTVARIABLE = "getResultVariable()";
	private static final String ACTIONS = "actions";
	public static final String PJM_ID = "pjmID:";
	private static final String ADID2 = " ADID =";
	Gson gson = new Gson();

	public String getFileNameandField(String columnName, String columnValue) {

		JsonArray responseFieldList = new JsonArray();

		try {
			List<String> columnName1 = new ArrayList();
			columnName1.add(columnName);
			columnName1.add("record_type");
			columnName1.add("active_flag");
			columnName1.add("mf_field_name");

			List<String> columnCondition1 = new ArrayList();
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);

			List<String> columnValue1 = new ArrayList();
			columnValue1.add(columnValue);
			columnValue1.add("Detail Record");
			columnValue1.add("T");
			columnValue1.add("filler");

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> findByMultiColumnCondition = objLayoutConfigContService
					.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);

			for (com.wipro.fipc.entity.layoutrule.LayoutConfig layoutConfig : findByMultiColumnCondition) {
				JsonObject reqField = new JsonObject();
				reqField.addProperty("fileName", layoutConfig.getFileName());
				reqField.addProperty("mfFieldName", layoutConfig.getMfFieldName());
				reqField.addProperty("mfFieldNameWoutSpace", layoutConfig.getMfFieldWoutSpace());
				responseFieldList.add(reqField);
			}

		} catch (Exception e) {
			e.printStackTrace();
			LoggerUtil.log(MatchTbaServiceImpl.class, Level.ERROR, "GET", UNABLE_TO_CONNECT + e.getMessage());
		}
		return responseFieldList.toString();
	}

	public String getTbaField(String columnName, String columnValue) {
		String tbaFieldForMatchTba = "";

		JsonArray responseTba = new JsonArray();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {

			List<com.wipro.fipc.entity.tba.TbaInquiryConfig> findByColumn = objTbaInquiryConfigContService1
					.findByColumn(columnName, columnValue);

			for (com.wipro.fipc.entity.tba.TbaInquiryConfig tbaInquiryConfig : findByColumn) {
				JsonObject obj = new JsonObject();
				obj.addProperty("defName", tbaInquiryConfig.getInquiryDefName());
				obj.addProperty("fieldName", tbaInquiryConfig.getTbaFieldName());
				responseTba.add(obj);
			}
			List<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> findByColumn2 = objTbaNoticeInqConfigContService
					.findByColumn(columnName, columnValue);
			tbaFieldForMatchTba = objectMapper.writeValueAsString(findByColumn2);

			Iterator<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> ite = findByColumn2.iterator();
			while (ite.hasNext()) {
				com.wipro.fipc.entity.tba.TbaNoticeInqConfig config = ite.next();
				JsonObject obj = new JsonObject();
				obj.addProperty("defName", config.getInquiryDefName());
				obj.addProperty("fieldName", config.getTba_field_name());
				responseTba.add(obj);
			}
		} catch (Exception e) {
			e.printStackTrace();
			LoggerUtil.log(MatchTbaServiceImpl.class, Level.ERROR, "GET", UNABLE_TO_CONNECT + e.getMessage());
		}
		return responseTba.toString();
	}

	public List<String> getRule(String columnName, String columnValue) {
		return Collections.emptyList();
	}

	public String getRule(Long processJobMappingId, String fileName) {

		String fname = "";
		fileName = fileName.replace("+", "PLUS");
		fileName = fileName.replace("%", "PRCNT");
		fileName = fileName.replace("&", "AMPNT");
		fileName = fileName.replace("#", "HASH");
		fileName = fileName.replace("'", "''");
		fname = fileName.replace(" ", "%20");

		String ruleForMatchTba = "";

		try {

			List<String> requiredDetailsBy = objRulesDefinitionController.getRequiredDetailsBy(processJobMappingId,
					fname);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			ruleForMatchTba = objectMapper.writeValueAsString(requiredDetailsBy);

		} catch (Exception e) {
			LoggerUtil.log(MatchTbaServiceImpl.class, Level.ERROR, "GET", UNABLE_TO_CONNECT + e.getMessage());
		}
		return ruleForMatchTba;
	}

	public List<String> getMismatch(String columnName, String columnValue) {
		String mismatchActionForMatchTba = "";

		List<String> mismatchRes = new ArrayList<>();
		try {
			List<com.wipro.fipc.entity.tba.TbaUpdateConfig> findByColumn = objTbaUpdateConfigContService
					.findByColumn(columnName, columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			mismatchActionForMatchTba = objectMapper.writeValueAsString(findByColumn);
			for (com.wipro.fipc.entity.tba.TbaUpdateConfig tbaUpdateConfig : findByColumn) {
				mismatchRes.add(tbaUpdateConfig.getUpdateName());
			}

		} catch (Exception e) {
			LoggerUtil.log(MatchTbaServiceImpl.class, Level.ERROR, "GET", UNABLE_TO_CONNECT + e.getMessage());
		}
		return mismatchRes;
	}

	public String deleteMatchTba(String id) {

		String response = null;
		try {
			response = "Status : Operation Not Supported";
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteMatchTba ", UNABLE_TO_CONNECT + exception.getMessage());
		}
		return response;
	}

	public String createMatchTbaRecord(String request) {

		String response = "";
		try {

			List<com.wipro.fipc.entity.tba.TbaMatchConfig> ehicList = Arrays.asList(
					new ObjectMapper().readValue(request, com.wipro.fipc.entity.tba.TbaMatchConfig[].class));
			List<CommonResRowBO> saveIfNotDuplicate = objTbaMatchConfigContService.saveIfNotDuplicate(ehicList);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			response = objectMapper.writeValueAsString(saveIfNotDuplicate);

		} catch (Exception e) {
			e.printStackTrace();
			response = "Failed";
		}

		return response;
	}

	public String getDetailsTbaMatchConfig(String columnName, String columnValue) {
		String infoTbaMatchConfig = "";
		try {
			List<com.wipro.fipc.entity.tba.TbaMatchConfig> findByColumn = objTbaMatchConfigContService
					.findByColumn(columnName, columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			infoTbaMatchConfig = objectMapper.writeValueAsString(findByColumn);

		} catch (Exception e) {
			LoggerUtil.log(MatchTbaServiceImpl.class, Level.ERROR, "GET", UNABLE_TO_CONNECT + e.getMessage());
			infoTbaMatchConfig = "FAILED";
		}

		return infoTbaMatchConfig;
	}

	public List<MatchTba> getDetailsTbaMatchConfigDetails(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDetailsTba_Match_Config()",
				"getDetailsTba_Match_Config()--starts:");
		String infoTbaMatchConfig = "";
		try {
			List<MatchTba> matchTbaList = new ArrayList<>();

			List<com.wipro.fipc.entity.tba.TbaMatchConfig> findByColumn = objTbaMatchConfigContService
					.findByColumn(columnName, columnValue);

			for (com.wipro.fipc.entity.tba.TbaMatchConfig tbaMatchConfig : findByColumn) {
				MatchTba matchTba = pcConfigToPcConversion.apply(tbaMatchConfig);
				matchTbaList.add(matchTba);
			}
			LoggerUtil.log(this.getClass(), Level.INFO, "getDetailsTba_Match_Config()",
					"getDetailsTba_Match_Config()--ends.");
			return matchTbaList;

		} catch (Exception e) {
			LoggerUtil.log(MatchTbaServiceImpl.class, Level.ERROR, "GET", UNABLE_TO_CONNECT + e.getMessage());
			throw new BusinessException(e.getMessage());
		}

	}

	private Function<TbaMatchConfig, MatchTba> pcConfigToPcConversion = (
			com.wipro.fipc.entity.tba.TbaMatchConfig tbaMatchConfig) -> {
		MatchTba tb = new MatchTba();
		if (tbaMatchConfig.getActions() != null) {
			tb.setActions(gson.fromJson(tbaMatchConfig.getActions(), Object.class));
		}
		if (tbaMatchConfig.getActiveFlag() != '\0') {
			tb.setActiveFlag(tbaMatchConfig.getActiveFlag() + "");
		}

		if (tbaMatchConfig.getCorrectiveAction() != null) {
			tb.setCorrectiveAction(tbaMatchConfig.getCorrectiveAction());
		}
		if (tbaMatchConfig.getCreatedBy() != null) {
			tb.setCreatedBy(tbaMatchConfig.getCreatedBy());
		}
		if (tbaMatchConfig.getFileName() != null) {
			tb.setFileName(tbaMatchConfig.getFileName());
		}

		if (tbaMatchConfig.getInquiryDefName() != null) {
			tb.setInquiryDefName(tbaMatchConfig.getInquiryDefName());
		}

		if (tbaMatchConfig.getMfFieldName() != null) {
			tb.setMfFieldName(tbaMatchConfig.getMfFieldName());
		}

		if (tbaMatchConfig.getMfFieldWoutSpace() != null) {
			tb.setMfFieldWoutSpace(tbaMatchConfig.getMfFieldWoutSpace());
		}

		if (tbaMatchConfig.getProcessJobMapping() != null) {
			tb.setProcessJobMapping(tbaMatchConfig.getProcessJobMapping());
		}

		if (tbaMatchConfig.getReportIdentifier() != null) {
			tb.setReportIdentifier(tbaMatchConfig.getReportIdentifier());
		}

		if (tbaMatchConfig.getResultField() != null) {
			tb.setResultField(tbaMatchConfig.getResultField());
		}

		if (tbaMatchConfig.getTbaFieldName() != null) {
			tb.setTbaFieldName(tbaMatchConfig.getTbaFieldName());
		}

		if (tbaMatchConfig.getRuleName() != null) {
			tb.setRuleName(tbaMatchConfig.getRuleName());
		}

		if (tbaMatchConfig.getCreatedDate() != null) {
			tb.setCreatedDate(tbaMatchConfig.getCreatedDate());
		}
		if (tbaMatchConfig.getId() != null) {
			tb.setId(tbaMatchConfig.getId());
		}
		if (tbaMatchConfig.getIdentifier() != null) {
			tb.setIdentifier(tbaMatchConfig.getIdentifier());
		}

		if (tbaMatchConfig.getUpdatedBy() != null) {
			tb.setUpdatedBy(tbaMatchConfig.getUpdatedBy());
		}
		if (tbaMatchConfig.getUpdatedDate() != null) {
			tb.setUpdatedDate(tbaMatchConfig.getUpdatedDate());
		}
		if (tbaMatchConfig.getFileNameDest() != null) {
			tb.setFileNameDest(tbaMatchConfig.getFileNameDest());
		}
		if (tbaMatchConfig.getMatchType() != null) {
			tb.setMatchType(tbaMatchConfig.getMatchType());
		}
		if (tbaMatchConfig.getMfFieldNameDest() != null) {
			tb.setMfFieldNameDest(tbaMatchConfig.getMfFieldNameDest());
		}
		if (tbaMatchConfig.getMfFieldWoutSpaceDest() != null) {
			tb.setMfFieldWoutSpaceDest(tbaMatchConfig.getMfFieldWoutSpaceDest());
		}
		if (tbaMatchConfig.getReportIdentifierDest() != null) {
			tb.setReportIdentifierDest(tbaMatchConfig.getReportIdentifierDest());
		}

		if (tbaMatchConfig.getFileNameWoutSpace() != null) {
			tb.setFileNameWoutSpace(tbaMatchConfig.getFileNameWoutSpace());
		}
		if (tbaMatchConfig.getFileNameDestWoutSpace() != null) {
			tb.setFileNameWoutSpaceDest(tbaMatchConfig.getFileNameDestWoutSpace());
		}
		if (tbaMatchConfig.getSheetName() != null) {
			tb.setSheetName(tbaMatchConfig.getSheetName());
		}

		if (tbaMatchConfig.getSheetNameWoutSpace() != null) {
			tb.setSheetNameWoutSpace(tbaMatchConfig.getSheetNameWoutSpace());
		}

		if (tbaMatchConfig.getSheetNameDest() != null) {
			tb.setSheetNameDest(tbaMatchConfig.getSheetNameDest());
		}
		if (tbaMatchConfig.getSheetNameDestWoutSpace() != null) {
			tb.setSheetNameDestWoutSpace(tbaMatchConfig.getSheetNameDestWoutSpace());
		}
		if (tbaMatchConfig.getPptVerifyTba() != null) {
			tb.setPptVerifyTba(tbaMatchConfig.getPptVerifyTba());
		}
		return tb;
	};

	@Override
	public Object submit(String action, List<MatchTba> mtList, String appName, String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "MatchTbaServiceImpl-->submit()-->starts:");
		String response = "";
		ResponseDto responseDto = new ResponseDto();
		String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "ADID:" + adID);
		try {
			List<TbaMatchConfig> newmtConfigList = new ArrayList<>();
			Set<MatchTba> mtConfigSet = new HashSet<>(mtList);
			Iterator<MatchTba> ite = mtConfigSet.iterator();
			while (ite.hasNext()) {
				MatchTba mt = ite.next();
				MatchTba mtConfigDbData = null;
				TbaMatchConfig mtConfig = pcToConfigConversion.apply(mt);

				if (action.equals("Modify") || action.equals("Delete")) {

					mtConfigDbData = getDetailsTbaMatchConfigDetails("id", mtConfig.getId().toString()).get(0);
				}

				if (action.equals("Save")) {
					mtConfig.setActiveFlag('T');
					mtConfig.setCreatedDate(new Date());
					mtConfig.setCreatedBy(adID);
					mtConfig.setUpdatedDate(new Date());
					mtConfig.setUpdatedBy(adID);
					LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "mtConfig inside save loop");
					LoggerUtil.log(this.getClass(), Level.INFO, "submit() -> Match_TBA ",
							PJM_ID + mt.getProcessJobMapping().getId() + ADID2 + adID + " action = CREATE");
				} else if (action.equals("Modify")) {
					mtConfig.setActiveFlag('T');
					mtConfig.setCreatedDate(mtConfigDbData.getCreatedDate());
					mtConfig.setCreatedBy(mtConfigDbData.getCreatedBy());
					mtConfig.setUpdatedDate(new Date());
					mtConfig.setUpdatedBy(adID);
					LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "mtConfig inside Modify loop");
					LoggerUtil.log(this.getClass(), Level.INFO, "submit() -> Match_TBA ",
							PJM_ID + mt.getProcessJobMapping().getId() + ADID2 + adID + " action = MODIFY");
				} else if (action.equals("Delete")) {
					mtConfig.setActiveFlag('F');
					mtConfig.setCreatedDate(mtConfigDbData.getCreatedDate());
					mtConfig.setCreatedBy(mtConfigDbData.getCreatedBy());
					mtConfig.setUpdatedDate(new Date());
					mtConfig.setUpdatedBy(adID);
					LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "mtConfig inside delete loop");
					LoggerUtil.log(this.getClass(), Level.INFO, "submit() -> Match_TBA ",
							PJM_ID + mt.getProcessJobMapping().getId() + ADID2 + adID + " action = CREATE");
				}
				newmtConfigList.add(mtConfig);
			}

			List<CommonResRowBO> saveIfNotDuplicate = objTbaMatchConfigContService.saveIfNotDuplicate(newmtConfigList);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			response = objectMapper.writeValueAsString(saveIfNotDuplicate);
			JsonParser parser = new JsonParser();
			JsonArray respArr = parser.parse(response).getAsJsonArray();

			for (JsonElement elem : respArr) {
				JsonObject obj = elem.getAsJsonObject();
				JsonObject data = obj.get("data").getAsJsonObject();
				String actions = data.get(ACTIONS).getAsString();
				JsonArray jsonArray = new JsonParser().parse(actions).getAsJsonArray();
				data.remove(ACTIONS);
				data.add(ACTIONS, jsonArray);
				responseDto.setData(respArr.toString());
				responseDto.setStatus("success");
				if (newmtConfigList.get(0).getId() == null && newmtConfigList.get(0).getActiveFlag() == 'T') {
					responseDto.setMessage("Records Saved Successfully.");
				} else if (newmtConfigList.get(0).getId() != null && newmtConfigList.get(0).getActiveFlag() == 'T') {
					responseDto.setMessage("Modified Successfully.");
				} else if (newmtConfigList.get(0).getActiveFlag() == 'F') {
					responseDto.setMessage("Deleted Successfully.");
				}

			}

			LoggerUtil.log(this.getClass(), Level.INFO, "getDetailsTba_Match_Config()",
					"getDetailsTba_Match_Config()--ends.");
			return responseDto;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "submit", "Failed to save :" + e);
			throw new BusinessException("Failed to save data");
		}

	}

	@Override
	public Object getResultVariable(String columnName, String columnValue, String ruleName) {
		LoggerUtil.log(this.getClass(), Level.INFO, GETRESULTVARIABLE,
				"MatchTbaServiceImpl-->getResultVariable()-->starts:");
		try {
			JsonParser parser = new JsonParser();

			List<com.wipro.fipc.entity.filelayout.RulesConfig> findByColumn = objRulesConfigContService
					.findByColumn(columnName, columnValue);
			List<String> newVarResultList = new ArrayList<>();
			for (com.wipro.fipc.entity.filelayout.RulesConfig ruleConfig : findByColumn) {
				List<com.wipro.fipc.entity.common.RulesDefinition> rulesDefinitionList = ruleConfig
						.getRulesDefinitions();
				for (com.wipro.fipc.entity.common.RulesDefinition rulesDefinition : rulesDefinitionList) {
					if (rulesDefinition.getRuleName().equals(ruleName)) {
						String request = rulesDefinition.getVarResultJson();
						JsonArray jsonArr = parser.parse(request).getAsJsonArray();
						for (int i = 0; i < jsonArr.size(); i++) {
							JsonObject obj = jsonArr.get(i).getAsJsonObject();
							newVarResultList.add(obj.get("varResultName").getAsString());
						}
					}
				}
			}
			LoggerUtil.log(this.getClass(), Level.INFO, "getRule()",
					"MatchTbaServiceImpl-->getResultVariable()--ends.");
			return newVarResultList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getRule()",
					"MatchtbaServiceImpl-->getResultVariable()--Exception: " + e.getMessage());
			if (e.getMessage().contains("Not a JSON")) {
				return "[]";
			}
			throw new BusinessException(e.getMessage());
		}
	}

	@Override
	public String deleteMatchTbaRecords(String entityList) throws URISyntaxException {
		String response = "";
		try {
			List<com.wipro.fipc.entity.tba.TbaMatchConfig> entities = Arrays.asList(
					new ObjectMapper().readValue(entityList, com.wipro.fipc.entity.tba.TbaMatchConfig[].class));
			List<CommonRowBO> deletemultiplerows = objTbaMatchConfigContService.deletemultiplerows(1, entities);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			response = objectMapper.writeValueAsString(deletemultiplerows);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	/***
	 * Function to convert our own model(ProcessControl) to Model generated
	 * class(ProcessControlConfig)
	 */
	private Function<MatchTba, com.wipro.fipc.entity.tba.TbaMatchConfig> pcToConfigConversion = (
			MatchTba pc) -> {
		com.wipro.fipc.entity.tba.TbaMatchConfig pcConfig = new com.wipro.fipc.entity.tba.TbaMatchConfig();

		if (pc.getActions() != null) {
			pcConfig.setActions(gson.toJson(pc.getActions()));
		}
		if (pc.getActiveFlag() != null) {
			pcConfig.setActiveFlag(pc.getActiveFlag().charAt(0));
		}
		if (pc.getCorrectiveAction() != null) {
			pcConfig.setCorrectiveAction(pc.getCorrectiveAction());
		}
		if (pc.getCreatedBy() != null) {
			pcConfig.setCreatedBy(pc.getCreatedBy());
		}
		if (pc.getCreatedDate() != null) {
			pcConfig.setCreatedDate(pc.getCreatedDate());
		}
		if (pc.getFileName() != null) {
			pcConfig.setFileName(pc.getFileName());
		}
		if (pc.getId() != null) {
			pcConfig.setId(pc.getId());
		}
		if (pc.getIdentifier() != null) {
			pcConfig.setIdentifier(pc.getIdentifier());
		}
		if (pc.getInquiryDefName() != null) {
			pcConfig.setInquiryDefName(pc.getInquiryDefName());
		}
		if (pc.getMfFieldName() != null) {
			pcConfig.setMfFieldName(pc.getMfFieldName());
		}
		if (pc.getMfFieldWoutSpace() != null) {
			pcConfig.setMfFieldWoutSpace(pc.getMfFieldWoutSpace());
		}
		if (pc.getProcessJobMapping() != null) {
			pcConfig.setProcessJobMapping(pc.getProcessJobMapping());
		}
		if (pc.getReportIdentifier() != null) {
			pcConfig.setReportIdentifier(pc.getReportIdentifier());
		}
		if (pc.getResultField() != null) {
			pcConfig.setResultField(pc.getResultField());
		}
		if (pc.getRuleName() != null) {
			pcConfig.setRuleName(pc.getRuleName());
		}
		if (pc.getTbaFieldName() != null) {
			pcConfig.setTbaFieldName(pc.getTbaFieldName());
		}
		if (pc.getUpdatedBy() != null) {
			pcConfig.setUpdatedBy(pc.getUpdatedBy());
		}
		if (pc.getUpdatedDate() != null) {
			pcConfig.setUpdatedDate(pc.getUpdatedDate());
		}
		if (pc.getFileNameDest() != null) {
			pcConfig.setFileNameDest(pc.getFileNameDest());
		}
		if (pc.getMatchType() != null) {
			pcConfig.setMatchType(pc.getMatchType());
		}
		if (pc.getMfFieldNameDest() != null) {
			pcConfig.setMfFieldNameDest(pc.getMfFieldNameDest());
		}
		if (pc.getMfFieldWoutSpaceDest() != null) {
			pcConfig.setMfFieldWoutSpaceDest(pc.getMfFieldWoutSpaceDest());
		}
		if (pc.getReportIdentifierDest() != null) {
			pcConfig.setReportIdentifierDest(pc.getReportIdentifierDest());
		}

		if (pc.getFileNameWoutSpace() != null) {
			pcConfig.setFileNameWoutSpace(pc.getFileNameWoutSpace());
		}
		if (pc.getFileNameWoutSpaceDest() != null) {
			pcConfig.setFileNameDestWoutSpace(pc.getFileNameWoutSpaceDest());
		}
		if (pc.getSheetName() != null) {
			pcConfig.setSheetName(pc.getSheetName());
		}
		if (pc.getSheetNameWoutSpace() != null) {
			pcConfig.setSheetNameWoutSpace(pc.getSheetNameWoutSpace());
		}
		if (pc.getSheetNameDest() != null) {
			pcConfig.setSheetNameDest(pc.getSheetNameDest());
		}
		if (pc.getSheetNameDestWoutSpace() != null) {
			pcConfig.setSheetNameDestWoutSpace(pc.getSheetNameDestWoutSpace());
		}
		if (pc.getPptVerifyTba() != null) {
			pcConfig.setPptVerifyTba(pc.getPptVerifyTba());
		}
		return pcConfig;
	};

	@Override
	public Object updateMatchTba(List<MatchTba> mtList) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateMatchTba()",
				"MatchTbaServiceImpl-->updateMatchTba()-->starts:");
		String response = "";
		ResponseDto responseDto = new ResponseDto();
		try {
			List<com.wipro.fipc.entity.tba.TbaMatchConfig> newmtConfigList = new ArrayList<>();
			Set<MatchTba> mtConfigSet = new HashSet<>(mtList);
			Iterator<MatchTba> ite = mtConfigSet.iterator();
			while (ite.hasNext()) {
				MatchTba mt = ite.next();
				com.wipro.fipc.entity.tba.TbaMatchConfig mtConfig = pcToConfigConversion.apply(mt);

				if (mt.getId() == null) {
					mtConfig.setCreatedDate(new Date());
				} else {
					mtConfig.setUpdatedDate(new Date());
				}
				newmtConfigList.add(mtConfig);
			}
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String data1 = objectMapper.writeValueAsString(newmtConfigList);
			List<com.wipro.fipc.entity.tba.TbaMatchConfig> ehicList = Arrays.asList(
					new ObjectMapper().readValue(data1, com.wipro.fipc.entity.tba.TbaMatchConfig[].class));
			List<CommonResRowBO> saveIfNotDuplicate = objTbaMatchConfigContService.saveIfNotDuplicate(ehicList);
			response = objectMapper.writeValueAsString(saveIfNotDuplicate);
			JsonParser parser = new JsonParser();
			JsonArray respArr = parser.parse(response).getAsJsonArray();

			for (JsonElement elem : respArr) {
				JsonObject obj = elem.getAsJsonObject();
				JsonObject data = obj.get("data").getAsJsonObject();
				String actions = data.get(ACTIONS).getAsString();
				JsonArray jsonArray = new JsonParser().parse(actions).getAsJsonArray();
				data.remove(ACTIONS);
				data.add(ACTIONS, jsonArray);
				responseDto.setData(respArr.toString());
				responseDto.setStatus("success");
				if (newmtConfigList.get(0).getId() == null && newmtConfigList.get(0).getActiveFlag() == 'T') {
					responseDto.setMessage("Records Saved Successfully.");
				} else if (newmtConfigList.get(0).getId() != null && newmtConfigList.get(0).getActiveFlag() == 'T') {
					responseDto.setMessage("Modified Successfully.");
				} else if (newmtConfigList.get(0).getActiveFlag() == 'F') {
					responseDto.setMessage("Deleted Successfully.");
				}

			}

			LoggerUtil.log(this.getClass(), Level.INFO, "updateMatchTba()", "updateMatchTba()--ends.");
			return responseDto;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateMatchTba", "Failed to update :" + e);
			throw new BusinessException("Failed to update data");
		}

	}

}