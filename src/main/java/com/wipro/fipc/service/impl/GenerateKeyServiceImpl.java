package com.wipro.fipc.service.impl;

import org.springframework.stereotype.Service;

import com.google.gson.JsonObject;
import com.wipro.fipc.model.Generatekey;
import com.wipro.fipc.service.IGenerateKeyService;
import com.wipro.fipc.utils.SecretUtils;

@Service
public class GenerateKeyServiceImpl implements IGenerateKeyService{


	@Override
	public String encryptValue(Generatekey entity) {
		JsonObject obj = new JsonObject();
		String encryptedValue="";
		String secretId=entity.getKey();
		String value=entity.getValue();
		if(!(secretId.isEmpty()) && !(value.isEmpty()) && !(secretId.equals("")) && !(value.equals(""))){
			 encryptedValue=SecretUtils.encrypt(value,secretId);
			 obj.addProperty("encryptedValue", encryptedValue);
		}else{
			obj.addProperty("status", "Encryption failed");
			obj.addProperty("messages", "value not empty");
			obj.addProperty("encryptedValue", encryptedValue);
		}
		
		return obj.toString();
	}

	@Override
	public String decrptValue(Generatekey entity) {
		JsonObject obj = new JsonObject();
		String originalValue="";
		String secretId=entity.getKey();
		String value=entity.getValue(); 
		if(!(secretId.isEmpty()) && !(value.isEmpty()) && !(secretId.equals("")) && !(value.equals(""))){
			originalValue=SecretUtils.decrypt(value, secretId);
			if(originalValue == null){
				obj.addProperty("status", "failed");
				 obj.addProperty("message", "Please Provide proper Value information");
				 obj.addProperty("originalValues", originalValue);
			}else{
			 obj.addProperty("originalValue", originalValue);
			}
		}else{
			obj.addProperty("Status", "failed");
			obj.addProperty("message", "Value not empty");
			obj.addProperty("originalValue", originalValue);
		}
		
		return obj.toString();
	}
	
	

}
