package com.wipro.fipc.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.dozer.DozerBeanMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.tba.EventInquiryConfig;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommentInquiryBo;
import com.wipro.fipc.model.Conditions;
import com.wipro.fipc.model.GenerateDroolsJson;
import com.wipro.fipc.model.GenerateDroolsResult;
import com.wipro.fipc.model.HistoryInquiryBO;
import com.wipro.fipc.model.LayoutRecord;
import com.wipro.fipc.model.NoticeInquiryBO;
import com.wipro.fipc.model.OperationJson;
import com.wipro.fipc.model.OutputReportBo;
import com.wipro.fipc.model.PendingEventBo;
import com.wipro.fipc.model.RulesRequest;
import com.wipro.fipc.model.TbaEventInquiryConfigBO;
import com.wipro.fipc.model.TbaInquiryConfigBO;
import com.wipro.fipc.model.TbaUpdateConfigBo;
import com.wipro.fipc.model.TemplateReportLayOutBO;
import com.wipro.fipc.model.generated.EventHistInqConfig;
import com.wipro.fipc.model.generated.LayoutConfig;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.RulesDefinition;
import com.wipro.fipc.model.generated.TbaCommentInqConfig;
import com.wipro.fipc.model.generated.TbaInquiryConfig;
import com.wipro.fipc.model.generated.TbaNoticeInqConfig;
import com.wipro.fipc.model.generated.TbaPendingEvent;
import com.wipro.fipc.model.generated.TbaUpdateConfig;
import com.wipro.fipc.model.generated.TemplateReportLayOut;
import com.wipro.fipc.model.generated.ValidationType;
import com.wipro.fipc.service.IFileLayoutService;

import net.logstash.logback.encoder.org.apache.commons.lang3.ArrayUtils;

@Component
public class RuleEngineDOConverter {
	private static final String VALUE_APP_NAME = "valueAppName";
	
	private static final String APP_NAME = "appName";
	
	private static final String VALUE_SHEET_NAME_FOR_FILTER = "valueSheetNameForFilter";

	private static final String SHEET_NAME_FOR_FILTER = "sheetNameForFilter";

	private static final String MF_FIELD_NAME = "mfFieldName";

	private static final String UPDATED_DATE = "updatedDate";

	private static final String UPDATED_BY = "updatedBy";

	private static final String TOTAL = "total";

	private static final String FILE_NAME = "fileName";

	private static final String DATA_FORMAT = "dataFormat";

	private static final String DATA_ELEMENT_WOUT_SPACE = "dataElementWoutSpace";

	private static final String DATA_ELEMENT = "dataElement";

	private static final String CREATED_DATE = "createdDate";

	private static final String CREATED_BY = "createdBy";

	private static final String CHILD_ELEMENT = "childElement";

	private static final String CELL_VALUE = "cellValue";

	private static final String CELL_NAME = "cellName";

	private static final String ADD_ON = "addOn";

	private static final String ACTIVE_FLAG = "activeFlag";

	private static final String MF_FIELD_WOUT_SPACE = "mfFieldWoutSpace";

	private static final String CLOSE_ROUND_BRACKET = "closeRoundBracket";

	private static final String OPEN_ROUND_BRACKET = "openRoundBracket";

	private static final String VALUE_RESULT_VARIABLE = "valueResultVariable";

	private static final String RESULT_VARIABLE_RADIO = "resultVariableRadio";

	private static final String RESULT_VARIABLE = "resultVariable";

	private static final String VALUE_SHEET_NAME = "valueSheetName";

	private static final String VALUE_APP_NAME_WITHOUT_SPACE = "valueAppNameWithoutSpace";

	private static final String SHEET_NAME = "sheetName";

	private static final String APP_NAME_WITHOUT_SPACE = "appNameWithoutSpace";

	private static final String RECORD_IDENTIFIER = "recordIdentifier";

	private static final String DATE_INTERVAL = "dateInterval";

	private static final String DATE_FREQUENCY = "dateFrequency";

	private static final String DATE_PERIOD = "datePeriod";

	private static final String VALUE_FIELD_TYPE = "valueFieldType";

	private static final String VALUE_RECORD_IDENTIFIER = "valueRecordIdentifier";

	private static final String VALUE = "value";

	private static final String RADIO = "radio";

	private static final String OPERATOR = "operator";

	private static final String CONDITION = "condition";

	private static final String FIELD_TYPE = "fieldType";

	private static final String FIELD = "field";

	private static final String CONDITION_NAME = "conditionName";
	private static final String OPERATION_WITH_BENEFICIARY ="operationWithBeneficiary";

	private static final String CONDITIONS2 = "conditions";
	private static final String BENEFICIARIES_CONDITIONS = "beneficiaryConditions";

	private static final String VAR_ARITH_EQUAL_OP = "varArithEqualOp";

	private static final String VARIABLE_NAME = "variableName";
	private static final String SOURCE_AGGREGATE = "sourceAggregateOperator";
	private static final String DEST_AGGREGATE = "destAggregateOperator";
	private static final String SOURCE_FIELD = "sourceFieldType";
	private static final String DEST_FIELD = "destFieldType";
	protected static final String RULES_CONFIG = "RULES_CONFIG";
	public static final String LOGGER = "Unable to connect to DB Service due to :";
	protected static final String LAYOUT_CONFIG = "LAYOUT_CONFIG";
	protected static final String LAYOUT_SCHEMA = "layout_rule";
	public static final String MULTICOLUMN = "find By MultiColumn Condition";
	public static final String INLAYOUTAFTTERREPLACE = "In  LayoutConfig After Replace: ";

	@Autowired
	Gson gson;

	@Autowired
	DozerBeanMapper mapper;

	@Autowired
	IFileLayoutService layoutService;

	@Autowired
	Environment env;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	GenericDao<com.wipro.fipc.entity.layoutrule.LayoutConfig> genericDao;

	public RulesConfig convertBizRuleRequestToDO(RulesRequest request) {
		RulesConfig rulesConfig = new RulesConfig();

		setRuleConfig(request, rulesConfig);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = setRuleDef(request);
		rulesDef.setConversionType(request.getConversionType());
		rulesDef.setConditionJson(getJsonWithoutSpace(request).toString().getBytes(StandardCharsets.UTF_8));
		rulesDef.setJsonWoutName(null);
		rulesDef.setJson(null);
		ValidationType valTypeObj = new ValidationType();
		valTypeObj.setId(request.getValidationTypeId());
		rulesDef.setValidationType(valTypeObj);

		rulesDef.setVarOperationJson(gson.toJson(request.getVarOpJson()));
		rulesDef.setVarOperationJsonWoutSpace(getVarOperationJsonWoutSpace(request).toString());
		rulesDef.setVarResultJson(gson.toJson(request.getVarResultJson()));
		rulesDef.setVarResultJsonWoutSpace(getVarResultJson(request));

		list.add(rulesDef);
		rulesConfig.setRulesDefinitions(list);
		return rulesConfig;

	}

	private RulesDefinition setRuleDef(RulesRequest request) {
		RulesDefinition rulesDef = new RulesDefinition();
		rulesDef.setCreatedBy(request.getCreatedBy());
		rulesDef.setActiveFlag("T");
		rulesDef.setCreatedDate(new Date());
		rulesDef.setUpdatedBy(request.getUpdatedBy());
		rulesDef.setUpdatedDate(new Date());
		rulesDef.setRuleName(request.getRuleName());
		rulesDef.setFileName(request.getApplicationType());
		return rulesDef;
	}

	private void setRuleConfig(RulesRequest request, RulesConfig rulesConfig) {
		rulesConfig.setActive("True");
		rulesConfig.setActiveFlag("T");
		rulesConfig.setStartDate(new Date());
		rulesConfig.setEndDate(new Date());
		rulesConfig.setCreatedBy(request.getCreatedBy());
		rulesConfig.setCreatedDate(new Date());
		rulesConfig.setUpdatedBy(request.getUpdatedBy());
		rulesConfig.setUpdatedDate(new Date());
		ProcessJobMapping processJobMapping = new ProcessJobMapping();
		processJobMapping.setId(request.getProcessJobMappingId());
		rulesConfig.setProcessJobMapping(processJobMapping);
	}

	public String getVarResultJson(RulesRequest request) {
		JsonParser parser = new JsonParser();
		JsonElement varResult = parser.parse(gson.toJson(request.getVarResultJson()));
		JsonArray rulesrequest = (JsonArray) varResult;
		List<GenerateDroolsResult> resultList = new ArrayList<>();

		for (JsonElement element : rulesrequest) {
			getIneerResultJson(resultList, element);
		}
		return gson.toJson(resultList);
	}

	private void getIneerResultJson(List<GenerateDroolsResult> resultList, JsonElement element) {
		JsonObject objInner = element.getAsJsonObject();
		GenerateDroolsResult drresult = new GenerateDroolsResult();
		drresult.setVarResultName(objInner.has("varResultName") ? objInner.get("varResultName").getAsString() : "");
		drresult.setVarResultArithEqualOp(
				objInner.has("varResultArithEqualOp") ? objInner.get("varResultArithEqualOp").getAsString() : "");
		drresult.setVarResultArithOp(
				objInner.has("varResultArithOp") ? objInner.get("varResultArithOp").getAsString() : "");
		drresult.setVarResultValueOne(
				objInner.has("varResultValueOne") ? objInner.get("varResultValueOne").getAsString() : "");
		drresult.setVarResultValueTwo(
				objInner.has("varResultValueTwo") ? objInner.get("varResultValueTwo").getAsString() : "");
		resultList.add(drresult);
	}

	public List<String> getVarOperationJsonWoutSpace(RulesRequest request) {
		List<String> genDrlJsonList = new ArrayList<>();
		JsonParser parser = new JsonParser();
		JsonElement varOpJson = parser.parse(gson.toJson(request.getVarOpJson()));
		return setInnerVarOpJson(genDrlJsonList, varOpJson);

	}

	private List<String> setInnerVarOpJson(List<String> genDrlJsonList, JsonElement varOpJson) {
		JsonArray rulesrequest = (JsonArray) varOpJson;
		for (JsonElement object2 : rulesrequest) {
			GenerateDroolsJson generateDroolsJson = new GenerateDroolsJson();
			JsonObject obj = object2.getAsJsonObject();
			List<OperationJson> variableRowOpList = new ArrayList<>();
			JsonArray variableRowOp = obj.get("variableRowOp").getAsJsonArray();
			for (JsonElement element : variableRowOp) {
				getInnerVarOpJson(variableRowOpList, element);
			}
			generateDroolsJson.setVariableName(obj.has(VARIABLE_NAME) ? obj.get(VARIABLE_NAME).getAsString() : "");
			generateDroolsJson.setVariableRowOp(variableRowOpList);
			generateDroolsJson
					.setVarArithEqualOp(obj.has(VAR_ARITH_EQUAL_OP) ? obj.get(VAR_ARITH_EQUAL_OP).getAsString() : "");
			genDrlJsonList.add(gson.toJson(generateDroolsJson));
		}
		return genDrlJsonList;
	}

	private void getInnerVarOpJson(List<OperationJson> variableRowOpList, JsonElement element) {
		JsonObject objInner = element.getAsJsonObject();
		OperationJson operationJson = new OperationJson();

		operationJson.setVarApplication(
				objInner.has("varApplication") ? objInner.get("varApplication").getAsString()
						: "");
		operationJson.setVarArithOp(objInner.has("varArithOp") ? objInner.get("varArithOp").getAsString() : "");
		operationJson.setVarField(objInner.has("varField") ? objInner.get("varField").getAsString() : "");
		operationJson.setVarRecordIdentifier(
				objInner.has("varRecordIdentifier") ? objInner.get("varRecordIdentifier").getAsString() : "");
		operationJson.setVarRadio(objInner.has("varRadio") ? objInner.get("varRadio").getAsString() : "");
		operationJson.setVarSheetName(objInner.has("varSheetName") ? objInner.get("varSheetName").getAsString() : "");
		operationJson.setVarTextStaticValue(
				objInner.has("varTextStaticValue") ? objInner.get("varTextStaticValue").getAsString() : "");
		operationJson
				.setVarDatePeriod(objInner.has("varDatePeriod") ? objInner.get("varDatePeriod").getAsString() : "");
		operationJson.setVarDateFrequency(
				objInner.has("varDateFrequency") ? objInner.get("varDateFrequency").getAsString() : "");
		operationJson.setVarDateInterval(
				objInner.has("varDateInterval") ? objInner.get("varDateInterval").getAsString() : "");
		operationJson.setVarFieldType(objInner.has("varFieldType") ? objInner.get("varFieldType").getAsString() : "");
		operationJson.setVarOpenRoundBracket(
				objInner.has("varOpenRoundBracket") ? objInner.get("varOpenRoundBracket").getAsString() : "");
		operationJson.setVarCloseRoundBracket(
				objInner.has("varCloseRoundBracket") ? objInner.get("varCloseRoundBracket").getAsString() : "");
		operationJson.setVarReuseVariable(
				objInner.has("varReuseVariable") ? objInner.get("varReuseVariable").getAsString() : "");
		operationJson.setVarGroupByArray(
				objInner.has("varGroupByArray") ? readVarGroupByArray(objInner.getAsJsonArray("varGroupByArray"))
						: new String[0]);
		operationJson.setVarSheetNameForFilter(
				objInner.has("varSheetNameForFilter") ? objInner.get("varSheetNameForFilter").getAsString() : "");
	
		variableRowOpList.add(operationJson);
	}

	private String[] readVarGroupByArray(JsonArray varGroupByArray) {
		String[] test = new String[varGroupByArray.size()];
		for (int i = 0; i < varGroupByArray.size(); i++) {
			test[i] = varGroupByArray.get(i).getAsString();
		}

		return test;
	}

	public List<String> getJsonWithoutSpace(RulesRequest request) {

		List<String> genDrlJsonList = new ArrayList<>();

		JsonParser parser = new JsonParser();
		JsonArray rulesrequest = (JsonArray) parser.parse(gson.toJson(request.getJson()));
		return setInnerJsonWoutSpace(genDrlJsonList, rulesrequest);

	}

	private List<String> setInnerJsonWoutSpace(List<String> genDrlJsonList, JsonArray rulesrequest) {
		for (JsonElement object2 : rulesrequest) {
			GenerateDroolsJson generateDroolsJson = new GenerateDroolsJson();
			JsonObject obj = object2.getAsJsonObject();

			String uniqueValueField = null;
			List<Conditions> condList = new ArrayList<>();
			JsonArray conditions = obj.get(CONDITIONS2).getAsJsonArray();
			for (JsonElement element : conditions) {
				getInnerConditionJson(uniqueValueField, condList, element);
			}
			generateDroolsJson.setConditions(condList);
			if (obj.has(BENEFICIARIES_CONDITIONS) && !obj.get(BENEFICIARIES_CONDITIONS).isJsonNull()) {
				uniqueValueField = null;
				condList = new ArrayList<>();
				JsonArray beneficiaryConditions = obj.get(BENEFICIARIES_CONDITIONS).getAsJsonArray();
				for (JsonElement element : beneficiaryConditions) {
					getInnerConditionJson(uniqueValueField, condList, element);
				}
				generateDroolsJson.setBeneficiaryConditions(condList);
			}
			generateDroolsJson.setConditionName(obj.has(CONDITION_NAME) ? obj.get(CONDITION_NAME).getAsString() : "");
			generateDroolsJson.setOperationWithBeneficiary(obj.has(OPERATION_WITH_BENEFICIARY) ? obj.get(OPERATION_WITH_BENEFICIARY).getAsString() : "");
			genDrlJsonList.add(gson.toJson(generateDroolsJson));
		}
		return genDrlJsonList;
	}

	private void getInnerConditionJson(String uniqueValueField, List<Conditions> condList, JsonElement element) {
		String uniqueFeild = null;
		JsonObject objInner = element.getAsJsonObject();
		Conditions cond = new Conditions();
		cond.setField(getValueOrEmptyString(objInner, FIELD));
		cond.setFieldType(getValueOrEmptyString(objInner, FIELD_TYPE));
		uniqueValueField = setConditionJsonFields(uniqueValueField, objInner, cond);
		cond.setAppName(getValueOrEmptyString(objInner, APP_NAME));
		cond.setSheetName(getValueOrEmptyString(objInner, SHEET_NAME));
		cond.setValueAppName(getValueOrEmptyString(objInner, VALUE_APP_NAME));
		cond.setValueSheetName(getValueOrEmptyString(objInner, VALUE_SHEET_NAME));
		cond.setResultVariable(getValueOrEmptyString(objInner, RESULT_VARIABLE));
		cond.setResultVariableRadio(getValueOrEmptyString(objInner, RESULT_VARIABLE_RADIO));
		if (!(objInner.get(RECORD_IDENTIFIER).getAsString()).isEmpty()) {
			uniqueFeild = objInner.get(RECORD_IDENTIFIER).getAsString() + "_" + objInner.get(FIELD).getAsString();
		} else {
			uniqueFeild = objInner.has(FIELD) ? objInner.get(FIELD).getAsString() : "";
		}
		cond.setUniqueField(uniqueFeild);
		cond.setUniqueValueField(uniqueValueField);
		cond.setValueResultVariable(getValueOrEmptyString(objInner, VALUE_RESULT_VARIABLE));
		cond.setOpenRoundBracket(getValueOrEmptyString(objInner, OPEN_ROUND_BRACKET));
		cond.setCloseRoundBracket(getValueOrEmptyString(objInner, CLOSE_ROUND_BRACKET));
		cond.setSheetNameForFilter(getValueOrEmptyString(objInner,SHEET_NAME_FOR_FILTER));
		cond.setValueSheetNameForFilter(getValueOrEmptyString(objInner,VALUE_SHEET_NAME_FOR_FILTER));
		cond.setSourceAggregateOperator(getValueOrEmptyString(objInner, SOURCE_AGGREGATE));
		cond.setDestAggregateOperator(getValueOrEmptyString(objInner, DEST_AGGREGATE));
		cond.setSourceFieldType(getValueOrEmptyString(objInner, SOURCE_FIELD));
		cond.setDestFieldType(getValueOrEmptyString(objInner,DEST_FIELD ));
		condList.add(cond);
	}

	private String setConditionJsonFields(String uniqueValueField, JsonObject objInner, Conditions cond) {
		cond.setCondition(getValueOrEmptyString(objInner, CONDITION));
		cond.setOperator(getValueOrEmptyString(objInner, OPERATOR));
		cond.setRadio(getValueOrEmptyString(objInner, RADIO));
		if ((objInner.get(RADIO).getAsString()).equalsIgnoreCase(FIELD)) {
			uniqueValueField = getUniqueValueFeild(objInner, cond);
		} else {
			cond.setValue(getValueOrEmptyString(objInner, VALUE));
		}
		cond.setValueFieldType(getValueOrEmptyString(objInner, VALUE_FIELD_TYPE));
		cond.setDatePeriod(getValueOrEmptyString(objInner, DATE_PERIOD));
		cond.setDateFrequency(getValueOrEmptyString(objInner, DATE_FREQUENCY));
		cond.setDateInterval(getValueOrEmptyString(objInner, DATE_INTERVAL));
		cond.setRecordIdentifier(getValueOrEmptyString(objInner, RECORD_IDENTIFIER));
		cond.setValueRecordIdentifier(getValueOrEmptyString(objInner, VALUE_RECORD_IDENTIFIER));
		return uniqueValueField;
	}

	private String getValueOrEmptyString(JsonObject object, String fieldName) {
		return object.has(fieldName) ? object.get(fieldName).getAsString() : "";
	}

	private List<GenerateDroolsResult> createEmptytVarResultJson() {
		List<GenerateDroolsResult> resultList = new ArrayList<>();
		GenerateDroolsResult drresult = new GenerateDroolsResult();
		drresult.setVarResultName("");
		drresult.setVarResultArithEqualOp("");
		drresult.setVarResultArithOp("");
		drresult.setVarResultValueOne("");
		drresult.setVarResultValueTwo("");
		resultList.add(drresult);
		return resultList;
	}

	private List<OperationJson> createEmptyOpJson() {
		List<OperationJson> operationJsonList = new ArrayList<>();
		OperationJson operationJson = new OperationJson();
		operationJson.setVarApplication("");
		operationJson.setVarArithOp("");
		operationJson.setVarDateFrequency("");
		operationJson.setVarDateInterval("");
		operationJson.setVarDatePeriod("");
		operationJson.setVarField("");
		operationJson.setVarFieldType("");
		operationJson.setVarRadio("");
		operationJson.setVarRecordIdentifier("");
		operationJson.setVarSheetName("");
		operationJson.setVarTextStaticValue("");
		operationJsonList.add(operationJson);
		return operationJsonList;
	}

	public RulesRequest convertCreateResponse(String response) {

		Gson g = new Gson();
		RulesConfig dbObj = g.fromJson(response, RulesConfig.class);
		RulesRequest rulesRequest = new RulesRequest();

		rulesRequest.setProcessJobMappingId(dbObj.getProcessJobMapping().getId());
		rulesRequest.setRuleId(dbObj.getId());
		rulesRequest.setActive(dbObj.getActive());

		for (RulesDefinition ruleDef : dbObj.getRulesDefinitions()) {
			setRuleDefResponse(rulesRequest, ruleDef);
		}

		return rulesRequest;
	}
	
	public RulesRequest convertCreateResponse(com.wipro.fipc.entity.filelayout.RulesConfig responseRulesConfig) {

		RulesConfig dbObj = new RulesConfig();
        List<RulesDefinition> rulesDefinitions = new ArrayList<>();
        for(com.wipro.fipc.entity.common.RulesDefinition entity : responseRulesConfig.getRulesDefinitions()) {
        	RulesDefinition rd = new RulesDefinition();
        	Long validationid=entity.getValidationType().getId();
        	BeanUtils.copyProperties(entity, rd);
        	ValidationType validationType=new ValidationType();
        	validationType.setId(validationid);
        	rd.setValidationType(validationType);
        	
        	rulesDefinitions.add(rd);
        }
        
		BeanUtils.copyProperties(responseRulesConfig, dbObj);
		dbObj.setRulesDefinitions(rulesDefinitions);
		RulesRequest rulesRequest = new RulesRequest();
		rulesRequest.setProcessJobMappingId(responseRulesConfig.getProcessJobMapping().getId());
		rulesRequest.setRuleId(dbObj.getId());
		rulesRequest.setActive(dbObj.getActive());

		for (RulesDefinition ruleDef : dbObj.getRulesDefinitions()) {
			setRuleDefResponse(rulesRequest, ruleDef);
		}

		return rulesRequest;
	}


	private void setRuleDefResponse(RulesRequest rulesRequest, RulesDefinition ruleDef) {
		setInnerRuleDef(rulesRequest, ruleDef);
		rulesRequest.setVarOpJson(gson.fromJson(ruleDef.getVarOperationJson(), Object.class));
		rulesRequest.setVarOperationJsonWoutSpace(gson.fromJson(ruleDef.getVarOperationJsonWoutSpace(), Object.class));
		rulesRequest.setVarResultJson(gson.fromJson(ruleDef.getVarResultJson(), Object.class));
		rulesRequest.setVarResultJsonWoutSpace(gson.fromJson(ruleDef.getVarResultJsonWoutSpace(), Object.class));
		rulesRequest.setUpdatedBy(ruleDef.getUpdatedBy());
		rulesRequest.setUpdatedDate(ruleDef.getUpdatedDate());
	}

	private void setInnerRuleDef(RulesRequest rulesRequest, RulesDefinition ruleDef) {
		rulesRequest.setConversionType(ruleDef.getConversionType());
		rulesRequest.setApplicationType(ruleDef.getFileName());
	
		if(!ArrayUtils.isEmpty(ruleDef.getConditionJson())) {
			String conditionJson = new String(ruleDef.getConditionJson());
        rulesRequest.setJsonWoutName(gson.fromJson(conditionJson, Object.class));
    	rulesRequest.setJson(gson.fromJson(conditionJson, Object.class));
		}else {
	        rulesRequest.setJsonWoutName(gson.fromJson(ruleDef.getJsonWoutName(), Object.class));
	    	rulesRequest.setJson(gson.fromJson(ruleDef.getJson(), Object.class));
		}
		rulesRequest.setPrimaryFieldWoutSpace(ruleDef.getPrimaryFieldWoutSpace());
		rulesRequest.setPrimaryFieldName(ruleDef.getPrimaryFieldName());
		rulesRequest.setRuleName(ruleDef.getRuleName());
		rulesRequest.setValidationTypeId(ruleDef.getValidationType().getId());
		rulesRequest.setActiveFlag(ruleDef.getActiveFlag());
		rulesRequest.setCreatedBy(ruleDef.getCreatedBy());
		rulesRequest.setCreatedDate(ruleDef.getCreatedDate());
	}

	public List<RulesRequest> convertBusinessDOToJson(List<RulesConfig> requestDOList) {

		List<RulesRequest> rulesRequestsList = new ArrayList<>();
		RulesRequest rulesRequest = null;

		for (RulesConfig rulesConfigDO : requestDOList) {
			rulesRequest = new RulesRequest();

			rulesRequest.setProcessJobMappingId(rulesConfigDO.getProcessJobMapping().getId());
			rulesRequest.setRuleId(rulesConfigDO.getId());
			rulesRequest.setActive(rulesConfigDO.getActive());

			for (RulesDefinition ruleDef : rulesConfigDO.getRulesDefinitions()) {

				setInnerRuleDef(rulesRequest, ruleDef);
				getNotNullValuesForGetApi(rulesRequest, ruleDef);
				rulesRequest.setUpdatedBy(ruleDef.getUpdatedBy());
				rulesRequest.setUpdatedDate(ruleDef.getUpdatedDate());
			}
			rulesRequestsList.add(rulesRequest);
		}
		return rulesRequestsList;

	}

	private void getNotNullValuesForGetApi(RulesRequest rulesRequest, RulesDefinition ruleDef) {
		if (ruleDef.getVarOperationJson() == null || ruleDef.getVarOperationJson().equals("[]")) {
			rulesRequest.setVarOpJson(createEmptyOpJson());
		} else {
			rulesRequest.setVarOpJson(gson.fromJson(ruleDef.getVarOperationJson(), Object.class));
		}
		if (ruleDef.getVarOperationJsonWoutSpace() == null || ruleDef.getVarOperationJsonWoutSpace().equals("[]")) {
			rulesRequest.setVarOperationJsonWoutSpace(createEmptyOpJson());
		} else {
			rulesRequest
					.setVarOperationJsonWoutSpace(gson.fromJson(ruleDef.getVarOperationJsonWoutSpace(), Object.class));
		}

		if (ruleDef.getVarResultJson() == null || ruleDef.getVarResultJson().equals("[]")) {
			rulesRequest.setVarResultJson(createEmptytVarResultJson());
		} else {
			rulesRequest.setVarResultJson(gson.fromJson(ruleDef.getVarResultJson(), Object.class));

		}
		if (ruleDef.getVarResultJsonWoutSpace() == null || ruleDef.getVarResultJsonWoutSpace().equals("[]")) {
			rulesRequest.setVarResultJsonWoutSpace(createEmptytVarResultJson());
		} else {
			rulesRequest.setVarResultJsonWoutSpace(gson.fromJson(ruleDef.getVarResultJsonWoutSpace(), Object.class));
		}
	}

	public List<RulesRequest> convertDOListToBizRules(List<RulesConfig> requestDOList) {

		List<RulesRequest> rulesRequestsList = new ArrayList<>();
		RulesRequest rulesRequest = null;

		for (RulesConfig rulesConfigDO : requestDOList) {
			rulesRequest = new RulesRequest();
			rulesRequest.setProcessJobMappingId(rulesConfigDO.getProcessJobMapping().getId());
			rulesRequest.setRuleId(rulesConfigDO.getId());
			rulesRequest.setActive(rulesConfigDO.getActive());

			for (RulesDefinition ruleDef : rulesConfigDO.getRulesDefinitions()) {

				setRuleDefResponse(rulesRequest, ruleDef);
			}
			rulesRequestsList.add(rulesRequest);
		}
		return rulesRequestsList;
	}

	public RulesConfig convertTransformationRuleRequestToDO(RulesRequest request) {
		RulesConfig rulesConfig = new RulesConfig();

		setRuleConfig(request, rulesConfig);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = setRuleDef(request);

		rulesDef.setPrimaryFieldName(request.getPrimaryFieldName());
		rulesDef.setConversionType(request.getConversionType());
		rulesDef.setConditionJson(gson.toJson(request.getJson()).getBytes());
		rulesDef.setJson(null);
		rulesDef.setJsonWoutName(null);
		rulesDef.setPrimaryFieldWoutSpace(request.getPrimaryFieldWoutSpace());
		ValidationType valTypeObj = new ValidationType();
		valTypeObj.setId(request.getValidationTypeId());
		rulesDef.setValidationType(valTypeObj);
		list.add(rulesDef);
		rulesConfig.setRulesDefinitions(list);
		return rulesConfig;

	}


	public RulesConfig convertTransRuleRequestForDateToDO(RulesRequest request) {
		String fileName = request.getApplicationType();
		String response = null;
		RulesConfig config = null;

		List<String> columnNames = new ArrayList<>();
		columnNames.add("process_job_mapping_id");
		columnNames.add("mf_Field_Name");
		columnNames.add("file_name");
		columnNames.add("record_type");
		columnNames.add("active_flag");
		List<String> columnConditions = new ArrayList<>();
		columnConditions.add("eq");
		columnConditions.add("eq");
		columnConditions.add("eq");
		columnConditions.add("eq");
		columnConditions.add("eq");
		List<String> columnValues = new ArrayList<>();
		columnValues.add(String.valueOf(request.getProcessJobMappingId()));
		columnValues.add("Filler");
		columnValues.add(fileName);
		columnValues.add("Detail Record");
		columnValues.add("T");
		try {

			List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
					columnConditions, columnValues);

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> layoutConfig = genericDao.findByMultiColumnCondition(
					com.wipro.fipc.entity.layoutrule.LayoutConfig.class, LAYOUT_SCHEMA, LAYOUT_CONFIG,
					columnConditionParams);
			ObjectMapper objectMapper=new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = objectMapper.writeValueAsString(layoutConfig);
			JsonParser parser = new JsonParser();
			JsonArray obj = parser.parse(response).getAsJsonArray();
			for (JsonElement elm : obj) {
				JsonObject jsonObject = elm.getAsJsonObject();
				String feild = "Date";
				String feildName = jsonObject.get(MF_FIELD_WOUT_SPACE).isJsonNull() ? ""
						: jsonObject.get(MF_FIELD_WOUT_SPACE).getAsString();
				List<RulesDefinition> rulesDefinitionJson = new ArrayList<>();
				RulesDefinition definition = new RulesDefinition();
				if (feild != null && feild.equals("Date")
						&& request.getPrimaryFieldWoutSpace().equalsIgnoreCase(feildName)) {
					config = new RulesConfig();
					setRuleConfig(request, config);
					definition.setPrimaryFieldName(jsonObject.get(MF_FIELD_NAME).isJsonNull() ? ""
							: jsonObject.get(MF_FIELD_NAME).getAsString());
					definition.setPrimaryFieldWoutSpace(jsonObject.get(MF_FIELD_WOUT_SPACE).isJsonNull() ? ""
							: jsonObject.get(MF_FIELD_WOUT_SPACE).getAsString());
					definition.setConversionType(request.getConversionType());
					definition.setCreatedDate(new Date());
					definition.setActiveFlag("T");
					definition.setCreatedBy(request.getCreatedBy());
					definition.setFileName(request.getApplicationType());
					definition.setConditionJson(gson.toJson(request.getJson()).getBytes(StandardCharsets.UTF_8));
					definition.setJson(null);
					definition.setJsonWoutName(null);
					definition.setRuleName(request.getRuleName());
					definition.setUpdatedBy(request.getUpdatedBy());
					definition.setUpdatedDate(new Date());
					ValidationType valTypeObj = new ValidationType();
					valTypeObj.setId(request.getValidationTypeId());
					definition.setValidationType(valTypeObj);
					rulesDefinitionJson.add(definition);
					config.setRulesDefinitions(rulesDefinitionJson);
				}
			}
		} catch (Exception e) {
			throw new BusinessException("error in convertTransRuleRequestForDateToDO : ", e.getCause());
		}
		return config;
	}

	private String getFileNameForDateTransRule(String fileName) {
		if (fileName.contains("Prev")) {
			fileName = fileName.substring(5);
		}
		try {
			fileName = fileName.replace("+", "PLUS");
			fileName = fileName.replace("%", "PRCNT");
			fileName = fileName.replace("&", "AMPNT");
			fileName = fileName.replace("#", "HASH");
			fileName = fileName.replace("'", "''");
			fileName = URLEncoder.encode(fileName, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new BusinessException("convertTransRuleRequestForDateToDO:  file name is not proper : ",
					e.getCause());
		}
		return fileName;
	}

	public RulesConfig updateByRuleIdConvertor(RulesRequest rulesRequest, RulesConfig config, String ruleId) {

		setValuesForUpdateRuleConfig(rulesRequest, config, ruleId);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = new RulesDefinition();

		setValuesForUpdateRulesDef(rulesRequest, config, rulesDef);
		rulesDef.setPrimaryFieldWoutSpace(rulesRequest.getPrimaryFieldName());
		rulesDef.setConversionType(rulesRequest.getConversionType());
		rulesDef.setUpdatedBy(rulesRequest.getUpdatedBy());
		rulesDef.setUpdatedDate(new Date());
		rulesDef.setJsonWoutName(getJsonWithoutSpace(rulesRequest).toString());
		rulesDef.setJson(gson.toJson(rulesRequest.getJson()));

		ValidationType valTypeObj = new ValidationType();
		valTypeObj.setId(rulesRequest.getValidationTypeId());
		rulesDef.setValidationType(valTypeObj);

		rulesDef.setVarOperationJson(gson.toJson(rulesRequest.getVarOpJson()));
		rulesDef.setVarOperationJsonWoutSpace(getVarOperationJsonWoutSpace(rulesRequest).toString());
		rulesDef.setVarResultJson(gson.toJson(rulesRequest.getVarResultJson()));
		rulesDef.setVarResultJsonWoutSpace(getVarResultJson(rulesRequest));

		list.add(rulesDef);
		config.setRulesDefinitions(list);

		return config;
	}

	private void setValuesForUpdateRulesDef(RulesRequest rulesRequest, RulesConfig config, RulesDefinition rulesDef) {
		for (RulesDefinition def : config.getRulesDefinitions()) {
			rulesDef.setCreatedBy(def.getCreatedBy());
			rulesDef.setCreatedDate(def.getCreatedDate());

			rulesDef.setId(def.getId());
		}
		rulesDef.setRuleName(rulesRequest.getRuleName());
		rulesDef.setActiveFlag("T");
		rulesDef.setFileName(rulesRequest.getApplicationType());
		rulesDef.setPrimaryFieldName(rulesRequest.getPrimaryFieldName());
	}

	private void setValuesForUpdateRuleConfig(RulesRequest rulesRequest, RulesConfig config, String ruleId) {
		if (String.valueOf(config.getId()) != null) {
			config.setId(Long.parseLong(ruleId));
		}

		config.setActive("True");
		config.setActiveFlag("T");
		config.setCreatedBy(config.getCreatedBy());
		config.setCreatedDate(config.getCreatedDate());
		config.setEndDate(config.getEndDate());

		ProcessJobMapping processJobMapping = new ProcessJobMapping();
		processJobMapping.setId(rulesRequest.getProcessJobMappingId());
		config.setProcessJobMapping(processJobMapping);

		config.setStartDate(config.getStartDate());
		config.setUpdatedBy(rulesRequest.getUpdatedBy());
		config.setUpdatedDate(new Date());
	}

	public RulesConfig updateTransByRuleIdConvertor(RulesRequest rulesRequest, RulesConfig config, String ruleId) {

		setValuesForUpdateRuleConfig(rulesRequest, config, ruleId);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = new RulesDefinition();

		setValuesForUpdateRulesDef(rulesRequest, config, rulesDef);
		rulesDef.setPrimaryFieldWoutSpace(rulesRequest.getPrimaryFieldWoutSpace());
		rulesDef.setConversionType(rulesRequest.getConversionType());
		rulesDef.setUpdatedBy(rulesRequest.getUpdatedBy());
		rulesDef.setUpdatedDate(new Date());
		rulesDef.setJsonWoutName(gson.toJson(rulesRequest.getJson()));
		rulesDef.setJson(gson.toJson(rulesRequest.getJson()));

		ValidationType valTypeObj = new ValidationType();
		valTypeObj.setId(rulesRequest.getValidationTypeId());
		rulesDef.setValidationType(valTypeObj);
		list.add(rulesDef);
		config.setRulesDefinitions(list);

		return config;
	}

	public Object convertHistoryInquiry(EventHistInqConfig response) {
		HistoryInquiryBO historyInquiryBO = new HistoryInquiryBO();
		historyInquiryBO.setActiveFlag(response.getActiveFlag());
		historyInquiryBO.setActLongDesc(response.getActLongDesc());
		historyInquiryBO.setCreatedBy(response.getCreatedBy());
		historyInquiryBO.setCreatedDate(response.getCreatedDate());
		historyInquiryBO.setEffFromDate(response.getEffFromDate());
		historyInquiryBO.setEffToDate(response.getEffToDate());
		historyInquiryBO.setInquiryDefName(response.getEventHistDefName());
		historyInquiryBO.setEventName(response.getEventName());
		historyInquiryBO.setId(response.getId());
		historyInquiryBO.setPjmId(response.getProcessJobMappingId());
		historyInquiryBO.setUpdatedBy(response.getUpdatedBy());
		historyInquiryBO.setUpdatedDate(response.getUpdatedDate());
		historyInquiryBO.setIdentifier("");
		historyInquiryBO.setJsonKey(response.getJsonKey());
		historyInquiryBO.setFieldType(response.getFiledType());
		historyInquiryBO.setParNm(response.getParNm());
		historyInquiryBO.setTbaFiledName(response.getTbaFiledName());
		historyInquiryBO.setActivityId(response.getActivityId());
		historyInquiryBO.setSheetName("");
		historyInquiryBO.setSheetNameWoutSpace("");
		return historyInquiryBO;
	}

	public Object convertPendingEvent(TbaPendingEvent config) {
		PendingEventBo pendingEventBo = new PendingEventBo();
		pendingEventBo.setActiveFlag(config.getActiveFlag());
		pendingEventBo.setActivityId(config.getActivityId());
		pendingEventBo.setCreatedBy(config.getCreatedBy());
		pendingEventBo.setCreatedDate(config.getCreatedDate());
		pendingEventBo.setEventLongDesc(config.getEventLongDesc());
		pendingEventBo.setEventName(config.getEventName());
		pendingEventBo.setId(config.getId());
		pendingEventBo.setIdentifier(config.getIdentifier());
		pendingEventBo.setInquiryDefName(config.getPendgEvntDefName());
		pendingEventBo.setJsonKey(config.getJsonKey());
		pendingEventBo.setParNm(config.getParNm());
		pendingEventBo.setPjmId(config.getProcessJobMapping().getId());
		pendingEventBo.setTbaFieldName(config.getTbaFieldName());
		pendingEventBo.setUpdatedBy(config.getUpdatedBy());
		pendingEventBo.setUpdatedDate(config.getUpdatedDate());
		pendingEventBo.setSheetName("");
		pendingEventBo.setSheetNameWoutSpace("");
		return pendingEventBo;
	}

	public Object convertCommentInquiry(TbaCommentInqConfig config) {
		CommentInquiryBo commentInquiryBO = new CommentInquiryBo();
		commentInquiryBO.setActiveFlag(config.getActiveFlag());
		commentInquiryBO.setCreatedBy(config.getCreatedBy());
		commentInquiryBO.setCreatedDate(config.getCreatedDate());
		commentInquiryBO.setEftFromDate(config.getEftFromDate());
		commentInquiryBO.setEftToDate(config.getEftToDate());
		commentInquiryBO.setFieldType(config.getFieldType());
		commentInquiryBO.setId(config.getId());
		commentInquiryBO.setIdentifier("");
		commentInquiryBO.setJsonKey(config.getJsonKey());
		commentInquiryBO.setInquiryDefName(config.getInquiryDefName());
		commentInquiryBO.setParNM(config.getParNM());
		commentInquiryBO.setProcessJobMappingId(config.getProcessJobMapping().getId());
		commentInquiryBO.setSubJsonKey(config.getSubJsonKey());
		commentInquiryBO.setTbaFieldName(config.getTbaFieldName());
		commentInquiryBO.setUpdatedBy(config.getUpdatedBy());
		commentInquiryBO.setUpdatedDate(config.getUpdatedDate());
		commentInquiryBO.setSheetName("");
		commentInquiryBO.setSheetNameWoutSpace("");
		return commentInquiryBO;
	}

	public Object convertOutputReport(JsonObject report, String sheetName, String sheetNameWoutSpace) {

		OutputReportBo outputReportBo = new OutputReportBo();
		outputReportBo.setActiveFlag(getValueOrEmptyStringForOutputReport(report, ACTIVE_FLAG));
		outputReportBo.setAddOn(getValueOrEmptyStringForOutputReport(report, ADD_ON));
		outputReportBo.setCellName(getValueOrEmptyStringForOutputReport(report, CELL_NAME));
		outputReportBo.setCellValue(getValueOrEmptyStringForOutputReport(report, CELL_VALUE));
		outputReportBo.setChildElement(getValueOrEmptyStringForOutputReport(report, CHILD_ELEMENT));
		outputReportBo.setCreatedBy(getValueOrEmptyStringForOutputReport(report, CREATED_BY));
		outputReportBo.setCreatedDate(getValueOrEmptyStringForOutputReport(report, CREATED_DATE));
		outputReportBo.setMfFieldName(getValueOrEmptyStringForOutputReport(report, DATA_ELEMENT));
		outputReportBo.setMfFieldWoutSpace(getValueOrEmptyStringForOutputReport(report, DATA_ELEMENT_WOUT_SPACE));
		outputReportBo.setDataFormat(getValueOrEmptyStringForOutputReport(report, DATA_FORMAT));
		outputReportBo.setFileName(getValueOrEmptyStringForOutputReport(report, FILE_NAME));
		outputReportBo.setId(report.get("id").getAsLong());
		JsonObject obj = report.get("processJobMapping").getAsJsonObject();
		outputReportBo.setProcessJobMappingId(obj.get("id").getAsLong());
		outputReportBo.setRecordIdentifier(getValueOrEmptyStringForOutputReport(report, RECORD_IDENTIFIER));
		outputReportBo.setTotal(getValueOrEmptyStringForOutputReport(report, TOTAL));
		outputReportBo.setUpdatedBy(getValueOrEmptyStringForOutputReport(report, UPDATED_BY));
		outputReportBo.setUpdatedDate(getValueOrEmptyStringForOutputReport(report, UPDATED_DATE));
		outputReportBo.setSheetName(sheetName);
		outputReportBo.setSheetNameWoutSpace(sheetNameWoutSpace);
		return outputReportBo;
	}

	private String getValueOrEmptyStringForOutputReport(JsonObject object, String fieldName) {
		return (object.has(fieldName) && !object.get(fieldName).isJsonNull()) ? object.get(fieldName).getAsString()
				: "";
	}
	
	public Object converEventInquiryConfig(EventInquiryConfig eventInquiryConfig) {
		TbaEventInquiryConfigBO tbaEventInquiryConfigBO = new TbaEventInquiryConfigBO();
		BeanUtils.copyProperties(eventInquiryConfig, tbaEventInquiryConfigBO);
		return tbaEventInquiryConfigBO;
	}
	
	public Object converTbaInquiryConfig(TbaInquiryConfig config) {
		TbaInquiryConfigBO tbaInquiryConfigBO = new TbaInquiryConfigBO();
		tbaInquiryConfigBO.setActiveFlag(config.getActiveFlag());
		tbaInquiryConfigBO.setCreatedBy(config.getCreatedBy());
		tbaInquiryConfigBO.setCreatedDate(config.getCreatedDate());
		tbaInquiryConfigBO.setFieldType(config.getFieldType());
		tbaInquiryConfigBO.setId(config.getId());
		tbaInquiryConfigBO.setIdentifier(config.getIdentifier());
		tbaInquiryConfigBO.setInquiryDefName(config.getInquiryDefName());
		tbaInquiryConfigBO.setInquiryName(config.getInquiryName());
		tbaInquiryConfigBO.setJsonKey(config.getJsonKey());
		tbaInquiryConfigBO.setMetaData(config.getMetaData());
		tbaInquiryConfigBO.setPanelId(config.getPanelId());
		tbaInquiryConfigBO.setParNM(config.getParNM());
		tbaInquiryConfigBO.setProcessJobMapping(config.getProcessJobMapping().getId());
		tbaInquiryConfigBO.setRecordIdentifier(config.getRecordIdentifier());
		tbaInquiryConfigBO.setSheetName("");
		tbaInquiryConfigBO.setSheetNameWoutSpace("");
		tbaInquiryConfigBO.setSubJsonKey(config.getSubJsonKey());
		tbaInquiryConfigBO.setTbaFieldName(config.getTbaFieldName());
		tbaInquiryConfigBO.setUpdatedBy(config.getUpdatedBy());
		tbaInquiryConfigBO.setUpdatedDate(config.getUpdatedDate());
		return tbaInquiryConfigBO;
	}

	public Object convertNoticeInquiry(TbaNoticeInqConfig config) {
		NoticeInquiryBO noticeInquiryBO = new NoticeInquiryBO();
		noticeInquiryBO.setActiveFlag(config.getActiveFlag());
		noticeInquiryBO.setClientId(config.getClientId());
		noticeInquiryBO.setCreatedBy(config.getCreatedBy());
		noticeInquiryBO.setCreatedDate(config.getCreatedDate());
		noticeInquiryBO.setFieldType(config.getFieldType());
		noticeInquiryBO.setId(config.getId());
		noticeInquiryBO.setIdentifier(config.getIdentifier());
		noticeInquiryBO.setInquiryDefName(config.getInquiryDefName());
		noticeInquiryBO.setJsonKey(config.getJsonKey());
		noticeInquiryBO.setMetadata(config.getMetadata());
		noticeInquiryBO.setNoticeId(config.getNoticeId());
		noticeInquiryBO.setNoticeName(config.getNoticeName());
		noticeInquiryBO.setParNm(config.getParNm());
		noticeInquiryBO.setProcessJobMappingId(config.getProcessJobMappingId());
		noticeInquiryBO.setRecordIdentifier(config.getRecordIdentifier());
		noticeInquiryBO.setSubJsonKey(config.getSubJsonKey());
		noticeInquiryBO.setTbaFieldName(config.getTbaFieldName());
		noticeInquiryBO.setUpdatedBy(config.getUpdatedBy());
		noticeInquiryBO.setUpdatedDate(config.getUpdatedDate());
		noticeInquiryBO.setSheetName("");
		noticeInquiryBO.setSheetNameWoutSpace("");
		return noticeInquiryBO;
	}

	public Object convertLayoutConfig(LayoutConfig config) {
		LayoutRecord layoutRecord = new LayoutRecord();
		layoutRecord.setActiveFlag(config.getActiveFlag());
		layoutRecord.setCreatedBy(config.getCreatedBy());
		layoutRecord.setCreatedDate(config.getCreatedDate());
		layoutRecord.setMfFieldName(config.getMfFieldName());
		layoutRecord.setFileNameWoutSpace(config.getFileNameWoutSpace());
		layoutRecord.setFormat(config.getFieldType());
		layoutRecord.setId(config.getId().toString());
		layoutRecord.setLength(config.getLength().toString());
		layoutRecord.setMfFieldWoutSpace(config.getMfFieldWoutSpace());
		layoutRecord.setRecordIdentifier(config.getRecordIdentifier());
		layoutRecord.setRecordIdentifierVal(config.getRecordIdentifierVal());
		layoutRecord.setRecordType(config.getRecordType());
		layoutRecord.setSheetName(config.getSheetName());
		layoutRecord.setSheetNameWoutSpace(config.getSheetNameWoutSpace());
		layoutRecord.setStartPosition(config.getStartPos());
		layoutRecord.setUpdatedBy(config.getUpdatedBy());
		layoutRecord.setUpdatedDate(config.getUpdatedDate());
		return layoutRecord;

	}

	public Object convertUpdateConfig(TbaUpdateConfig config) {
		TbaUpdateConfigBo tbaUpdateConfigBo = new TbaUpdateConfigBo();
		tbaUpdateConfigBo.setId(config.getProcessJobMapping().getId());
		tbaUpdateConfigBo.setActiveFlag(config.getActiveFlag());
		tbaUpdateConfigBo.setTbaFieldName(config.getTbaFieldName());
		tbaUpdateConfigBo.setBasicInfo(config.getBasicInfo());
		tbaUpdateConfigBo.setEventName(config.getEventName());
		tbaUpdateConfigBo.setActivityId(config.getActivityId());
		tbaUpdateConfigBo.setPanelId(config.getPanelId());
		tbaUpdateConfigBo.setClassId(config.getClassId());
		tbaUpdateConfigBo.setJsonKey(config.getJsonKey());
		tbaUpdateConfigBo.setBaseKey(config.getBaseKey());
		tbaUpdateConfigBo.setSubKey(config.getSubKey());
		tbaUpdateConfigBo.setMetadata(config.getMetaData());
		tbaUpdateConfigBo.setTransId(config.getTransId());
		tbaUpdateConfigBo.setValue(config.getValue());
		tbaUpdateConfigBo.setCreatedBy(config.getCreatedBy());
		tbaUpdateConfigBo.setCreatedDate(config.getCreatedDate());
		tbaUpdateConfigBo.setUpdatedBy(config.getUpdatedBy());
		tbaUpdateConfigBo.setUpdatedDate(config.getUpdatedDate());
		tbaUpdateConfigBo.setParNm(config.getParNm());
		tbaUpdateConfigBo.setSequence(config.getSequence());
		tbaUpdateConfigBo.setRecordIdentifier(config.getRecordIdentifier());
		tbaUpdateConfigBo.setIdentifier(config.getIdentifier());
		tbaUpdateConfigBo.setRerunFlag(config.getRerunFlag());
		tbaUpdateConfigBo.setAddManualFlag(config.getAddManualFlag());
		tbaUpdateConfigBo.setActLngDesc(config.getActLngDesc());
		tbaUpdateConfigBo.setSheetName("");
		tbaUpdateConfigBo.setSheetNameWoutSpace("");
		tbaUpdateConfigBo.setFieldType("");
		tbaUpdateConfigBo.setInquiryDefName(config.getUpdateName());
		return tbaUpdateConfigBo;
	}

	public RulesConfig updateJsonWoutSpace(RulesConfig config, String ruleId) {

		setRuleConfigForDataCleanUpAPI(config, ruleId);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = new RulesDefinition();

		for (RulesDefinition def : config.getRulesDefinitions()) {
			rulesDef.setJsonWoutName(getMultiJsonWithoutSpace(def).toString());
			setRulesDefForDataCleanupAPI(rulesDef, def);
		}

		list.add(rulesDef);
		config.setRulesDefinitions(list);

		return config;
	}

	private void setRulesDefForDataCleanupAPI(RulesDefinition rulesDef, RulesDefinition def) {
		rulesDef.setCreatedBy(def.getCreatedBy());
		rulesDef.setCreatedDate(def.getCreatedDate());

		rulesDef.setId(def.getId());

		rulesDef.setRuleName(def.getRuleName());
		rulesDef.setActiveFlag(def.getActiveFlag());
		rulesDef.setFileName(def.getFileName());
		rulesDef.setPrimaryFieldName(def.getPrimaryFieldName());
		rulesDef.setPrimaryFieldWoutSpace(def.getPrimaryFieldName());
		rulesDef.setConversionType(def.getConversionType());
		rulesDef.setUpdatedBy(def.getUpdatedBy());
		rulesDef.setUpdatedDate(def.getUpdatedDate());

		rulesDef.setJson(def.getJson());

		ValidationType valTypeObj = new ValidationType();
		valTypeObj.setId(def.getValidationType().getId());
		rulesDef.setValidationType(valTypeObj);

		rulesDef.setVarOperationJson(def.getVarOperationJson());
		rulesDef.setVarOperationJsonWoutSpace(getMultiVarOperationJsonWoutSpace(def).toString());
		rulesDef.setVarResultJson(def.getVarResultJson());
		rulesDef.setVarResultJsonWoutSpace(getMultiVarResultJson(def));
	}

	private void setRuleConfigForDataCleanUpAPI(RulesConfig config, String ruleId) {
		config.setId(Long.parseLong(ruleId));
		config.setActive(config.getActive());
		config.setActiveFlag(config.getActiveFlag());
		config.setCreatedBy(config.getCreatedBy());
		config.setCreatedDate(config.getCreatedDate());
		config.setEndDate(config.getEndDate());

		ProcessJobMapping processJobMapping = new ProcessJobMapping();
		processJobMapping.setId(config.getProcessJobMapping().getId());
		config.setProcessJobMapping(processJobMapping);

		config.setStartDate(config.getStartDate());
		config.setUpdatedBy(config.getUpdatedBy());
		config.setUpdatedDate(config.getUpdatedDate());
	}

	public String getMultiVarResultJson(RulesDefinition request) {
		JsonParser parser = new JsonParser();
		JsonElement varResult = parser.parse(request.getVarResultJson());
		JsonArray rulesrequest = (JsonArray) varResult;
		List<GenerateDroolsResult> resultList = new ArrayList<>();

		for (JsonElement element : rulesrequest) {
			getIneerResultJson(resultList, element);
		}
		return gson.toJson(resultList);
	}

	public List<String> getMultiVarOperationJsonWoutSpace(RulesDefinition request) {
		List<String> genDrlJsonList = new ArrayList<>();
		JsonParser parser = new JsonParser();
		JsonElement varOpJson = parser.parse(request.getVarOperationJson());
		return setInnerVarOpJson(genDrlJsonList, varOpJson);

	}

	public List<String> getMultiJsonWithoutSpace(RulesDefinition request) {

		List<String> genDrlJsonList = new ArrayList<>();

		JsonParser parser = new JsonParser();
		JsonArray rulesrequest = (JsonArray) parser.parse(request.getJson());
		return setInnerJsonWoutSpace(genDrlJsonList, rulesrequest);

	}

	private List<String> getFileName(Long pjmId, RulesDefinition request) {
		GenerateDroolsJson generateDroolsJson = null;
		List<String> genDrlJsonList = new ArrayList<>();
				String response = null;
		try {
			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> responseLayoutConfig = genericDao.findByColumn(
					com.wipro.fipc.entity.layoutrule.LayoutConfig.class, "layout_rule", "LAYOUT_CONFIG",
					"process_job_mapping_id", String.valueOf(pjmId));

			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = ow.writeValueAsString(responseLayoutConfig);

			JsonParser parser = new JsonParser();
			JsonArray obj1 = parser.parse(response).getAsJsonArray();

			JsonArray rulesrequest = (JsonArray) parser.parse(request.getJson());
			for (JsonElement object2 : rulesrequest) {
				generateDroolsJson = new GenerateDroolsJson();
				JsonObject obj = object2.getAsJsonObject();

				List<Conditions> condList = new ArrayList<>();
				JsonArray conditions = obj.get(CONDITIONS2).getAsJsonArray();
				for (JsonElement element : conditions) {
					getByFileNameInnerCondJson(obj1, condList, element);
				}
				generateDroolsJson.setConditionName(getValueOrEmptyString(obj, CONDITION_NAME));
				generateDroolsJson.setConditions(condList);
				genDrlJsonList.add(gson.toJson(generateDroolsJson));
			}
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "layout_rule", LOGGER + exception.getMessage());
			throw new BusinessException("layout_rule failed : " + exception.getMessage());
		}
		return genDrlJsonList;

	}

	private void getByFileNameInnerCondJson(JsonArray obj1, List<Conditions> condList, JsonElement element) {
		String uniqueValueField = null;
		JsonObject objInner = element.getAsJsonObject();
		Conditions cond = new Conditions();
		cond.setField(getValueOrEmptyString(objInner, FIELD));
		String fileName = "";
		String fileNameWoutSpace = "";
		String feildType = "";

		for (JsonElement elm : obj1) {
			JsonObject jsonObject = elm.getAsJsonObject();
			if (objInner.get(FIELD).getAsString().equalsIgnoreCase(jsonObject.get(MF_FIELD_WOUT_SPACE).getAsString())
					|| objInner.get(FIELD).getAsString()
							.equalsIgnoreCase(jsonObject.get(MF_FIELD_NAME).getAsString())) {
				fileName = jsonObject.get(FILE_NAME).isJsonNull() ? "" : jsonObject.get(FILE_NAME).getAsString();

				fileNameWoutSpace = jsonObject.get("fileNameWoutSpace").isJsonNull() ? ""
						: jsonObject.get("fileNameWoutSpace").getAsString();

				feildType = jsonObject.get(FIELD_TYPE).isJsonNull() ? "" : jsonObject.get(FIELD_TYPE).getAsString();
			}
		}
		setFieldType(objInner, cond, feildType);
		uniqueValueField = setConditionJsonFields(uniqueValueField, objInner, cond);

		String appName = getValueOrEmptyString(objInner, "appName");

		setAppName(objInner, cond, fileName, fileNameWoutSpace, appName);
		cond.setSheetName(getValueOrEmptyString(objInner, SHEET_NAME));
		cond.setValueAppName(getValueOrEmptyString(objInner, VALUE_APP_NAME_WITHOUT_SPACE));
		cond.setValueSheetName(getValueOrEmptyString(objInner, VALUE_SHEET_NAME));
		cond.setResultVariable(getValueOrEmptyString(objInner, RESULT_VARIABLE));
		cond.setResultVariableRadio(getValueOrEmptyString(objInner, RESULT_VARIABLE_RADIO));
		setUniqueField(objInner, cond);
		cond.setUniqueValueField(uniqueValueField);
		cond.setValueResultVariable(getValueOrEmptyString(objInner, VALUE_RESULT_VARIABLE));
		cond.setOpenRoundBracket(getValueOrEmptyString(objInner, OPEN_ROUND_BRACKET));
		cond.setCloseRoundBracket(getValueOrEmptyString(objInner, CLOSE_ROUND_BRACKET));
		condList.add(cond);
	}

	private void setUniqueField(JsonObject objInner, Conditions cond) {
		String uniqueFeild = null;
		if (!(objInner.get(RECORD_IDENTIFIER).getAsString()).isEmpty()) {
			uniqueFeild = objInner.get(RECORD_IDENTIFIER).getAsString() + "_" + objInner.get(FIELD).getAsString();
		} else {
			uniqueFeild = getValueOrEmptyString(objInner, FIELD);
		}
		cond.setUniqueField(uniqueFeild);
	}

	private void setAppName(JsonObject objInner, Conditions cond, String fileName, String fileNameWoutSpace,
			String appName) {
		if (appName.equals(fileName)) {
			cond.setAppName(fileNameWoutSpace);
		} else {
			cond.setAppName(getValueOrEmptyString(objInner, APP_NAME_WITHOUT_SPACE));
		}
	}

	private void setFieldType(JsonObject objInner, Conditions cond, String feildType) {
		String feildObj = getValueOrEmptyString(objInner, FIELD_TYPE);
		if (feildObj.isEmpty()) {
			cond.setFieldType(feildType);
		} else {
			cond.setFieldType(getValueOrEmptyString(objInner, FIELD_TYPE));
		}
	}

	private String getUniqueValueFeild(JsonObject objInner, Conditions cond) {
		String uniqueValueField;
		cond.setValue(objInner.has(VALUE) ? objInner.get(VALUE).getAsString() : "");
		if (!(objInner.get(VALUE_RECORD_IDENTIFIER).getAsString()).isEmpty()) {
			uniqueValueField = objInner.get(VALUE_RECORD_IDENTIFIER).getAsString() + "_"
					+ objInner.get(VALUE).getAsString();
		} else {
			uniqueValueField = objInner.has(VALUE) ? objInner.get(VALUE).getAsString() : "";
		}
		return uniqueValueField;
	}

	public Object convertExternalReport(TemplateReportLayOut config) {

		TemplateReportLayOutBO templateReportLayOutBO = new TemplateReportLayOutBO();
		templateReportLayOutBO.setActiveFlag(config.getActiveFlag());
		templateReportLayOutBO.setCreatedBy(config.getCreatedBy());
		templateReportLayOutBO.setCreatedDate(config.getCreatedDate());
		templateReportLayOutBO.setFiledType(config.getFiledType());
		templateReportLayOutBO.setId(config.getId());
		templateReportLayOutBO.setRecordIdentifier(config.getIdentifier());
		templateReportLayOutBO.setIdentifierId(config.getIdentifierId());
		templateReportLayOutBO.setMfFieldName(config.getDataElement());
		templateReportLayOutBO.setSequence(config.getSequence());
		templateReportLayOutBO.setSheetName(config.getSheetName());
		templateReportLayOutBO.setFileName(config.getTemplateReportName());
		templateReportLayOutBO.setUpdatedBy(config.getUpdatedBy());
		templateReportLayOutBO.setUpdatedDate(config.getUpdatedDate());
		templateReportLayOutBO.setIdentifierWs(config.getIdentifierWs());
		templateReportLayOutBO.setSheetNameWoutSpace(config.getSheetNameWs());
		templateReportLayOutBO.setMfFieldWoutSpace(config.getDataElementWs());
		templateReportLayOutBO.setFileNameWoutSpace(config.getTemplateReportNameWs());
		return templateReportLayOutBO;
	}

	public RulesConfig updateFileName(RulesConfig config, String ruleId) {

		setRuleConfigForDataCleanUpAPI(config, ruleId);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = new RulesDefinition();

		for (RulesDefinition def : config.getRulesDefinitions()) {
			rulesDef.setJsonWoutName(getFileName(config.getProcessJobMapping().getId(), def).toString());
			setRulesDefForDataCleanupAPI(rulesDef, def);
		}

		list.add(rulesDef);
		config.setRulesDefinitions(list);

		return config;
	}

	public RulesConfig updateByRuleIdConvertorNew(RulesRequest rulesRequest, RulesConfig config, String ruleId,
			String adid) {

		setValuesForUpdateRuleConfigNew(rulesRequest, config, ruleId, adid);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = new RulesDefinition();

		setValuesForUpdateRulesDef(rulesRequest, config, rulesDef);
		rulesDef.setPrimaryFieldWoutSpace(rulesRequest.getPrimaryFieldName());
		rulesDef.setConversionType(rulesRequest.getConversionType());
		rulesDef.setUpdatedBy(adid);
		rulesDef.setUpdatedDate(new Date());
		rulesDef.setJsonWoutName(null);
		rulesDef.setJson(null);
		String string=getJsonWithoutSpace(rulesRequest).toString();
		rulesDef.setConditionJson(string.getBytes());
		ValidationType valTypeObj = new ValidationType();
		valTypeObj.setId(rulesRequest.getValidationTypeId());
		rulesDef.setValidationType(valTypeObj);

		rulesDef.setVarOperationJson(gson.toJson(rulesRequest.getVarOpJson()));
		rulesDef.setVarOperationJsonWoutSpace(getVarOperationJsonWoutSpace(rulesRequest).toString());
		rulesDef.setVarResultJson(gson.toJson(rulesRequest.getVarResultJson()));
		rulesDef.setVarResultJsonWoutSpace(getVarResultJson(rulesRequest));

		list.add(rulesDef);
		config.setRulesDefinitions(list);

		return config;
	}

	private void setValuesForUpdateRuleConfigNew(RulesRequest rulesRequest, RulesConfig config, String ruleId,
			String adid) {
		if (String.valueOf(config.getId()) != null) {
			config.setId(Long.parseLong(ruleId));
		}

		config.setActive("True");
		config.setActiveFlag("T");
		config.setCreatedBy(config.getCreatedBy());
		config.setCreatedDate(config.getCreatedDate());
		config.setEndDate(config.getEndDate());

		ProcessJobMapping processJobMapping = new ProcessJobMapping();
		processJobMapping.setId(rulesRequest.getProcessJobMappingId());
		config.setProcessJobMapping(processJobMapping);

		config.setStartDate(config.getStartDate());
		config.setUpdatedBy(adid);
		config.setUpdatedDate(new Date());
	}

	public RulesConfig updateTransByRuleIdConvertorNew(RulesRequest rulesRequest, RulesConfig config, String ruleId,
			String adid) {

		setValuesForUpdateRuleConfigNew(rulesRequest, config, ruleId, adid);

		List<RulesDefinition> list = new ArrayList<>();
		RulesDefinition rulesDef = new RulesDefinition();

		setValuesForUpdateRulesDef(rulesRequest, config, rulesDef);
		rulesDef.setPrimaryFieldWoutSpace(rulesRequest.getPrimaryFieldWoutSpace());
		rulesDef.setConversionType(rulesRequest.getConversionType());
		rulesDef.setUpdatedBy(adid);
		rulesDef.setUpdatedDate(new Date());
		rulesDef.setJsonWoutName(null);
		rulesDef.setJson(null);
		String string=rulesRequest.getJson().toString();
		rulesDef.setConditionJson(string.getBytes());
		ValidationType valTypeObj = new ValidationType();
		valTypeObj.setId(rulesRequest.getValidationTypeId());
		rulesDef.setValidationType(valTypeObj);
		list.add(rulesDef);
		config.setRulesDefinitions(list);

		return config;
	}

}
