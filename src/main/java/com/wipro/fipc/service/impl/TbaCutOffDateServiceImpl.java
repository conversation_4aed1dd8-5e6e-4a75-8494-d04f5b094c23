/**
 * 
 */
package com.wipro.fipc.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.ClientDetailsDao;
import com.wipro.fipc.dao.TbacutoffDateDao;
import com.wipro.fipc.entity.TbaCutoffDate;
import com.wipro.fipc.pojo.tba.TbaCutoffDateResponse;
import com.wipro.fipc.service.TbaCutOffDateService;
import com.wipro.fipc.utils.CustomBeanUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class TbaCutOffDateServiceImpl implements TbaCutOffDateService {
	
	@Autowired
	private TbacutoffDateDao tbacutoffDateDao;
	
	@Autowired
	private ClientDetailsDao clientDetailsDao;
	
	@Autowired
	Environment env;
	
	@Autowired
	CustomBeanUtils customBeanUtils;
	
	private static final DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("M/d/yyyy");
	private static final DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

	@Override
	public void readExcelSheet()  throws IOException{			
		
		List<TbaCutoffDate> tbaCutOffDate = new ArrayList<>();
		FileInputStream file = null;
		try {
			final String inputReportDownloadServiceUrl = env.getProperty("download.tba.cutoffdate.url");
			LoggerUtil.log(getClass(), Level.INFO, "readExcelSheet",
					"inputReportDownloadServiceUrl path: " + inputReportDownloadServiceUrl);
			RestTemplate restTemplate = new RestTemplate();
			String efsPath = restTemplate.getForObject(inputReportDownloadServiceUrl, String.class);
			LoggerUtil.log(getClass(), Level.INFO, "in readExcelSheet method ", "efs path : " + efsPath);
			file = new FileInputStream(new File(efsPath));
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "readExcelSheet", "  Error in calling Input report download service: " + e.getMessage());
			throw e;
		}
		LoggerUtil.log(getClass(), Level.INFO, "readExcelSheet", "File path : " + file);
		try (Workbook workbook = WorkbookFactory.create(file)) {
			int numberOfSheets = workbook.getNumberOfSheets();
			DataFormatter fmt = new DataFormatter();
			for (int sheetIdx = 0; sheetIdx < numberOfSheets; sheetIdx++) {
				Sheet sheet = workbook.getSheetAt(sheetIdx);
				Row headerRow = sheet.getRow(0);
				Map<String, String> map = getHeaderValue(headerRow);
				int totalRows = sheet.getPhysicalNumberOfRows();
				for (int row = 2; row < totalRows; row++) {
					
						String end = fmt.formatCellValue(sheet.getRow(row).getCell(1));
						String start = fmt.formatCellValue(sheet.getRow(row).getCell(0));
						// start and end both are common for all client in a single row.
						for (int i = 2; i < sheet.getRow(row).getPhysicalNumberOfCells(); i++) {
							TbaCutoffDate tba = new TbaCutoffDate();
							tba.setClientCode(map.get(fmt.formatCellValue(sheet.getRow(0).getCell(i))));
							tba.setMonth(changeDatePattern(end));
							tba.setTbaCutffDate(changeDatePattern(fmt.formatCellValue(sheet.getRow(row).getCell(i))));
							tba.setStartDate(changeDatePattern(start));
							tba.setActiveFlag('T');
							tba.setCreatedBy("Admin");
							tba.setCreatedDate(new Date());
							if (StringUtils.hasText(tba.getClientCode()))
								tbaCutOffDate.add(tba);
						}					
				}
			}			
			tbacutoffDateDao.saveAll(tbaCutOffDate);
			LoggerUtil.log(getClass(), Level.INFO, "readExcelSheet", "List size  : " + tbaCutOffDate.size());
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "readExcelSheet",
					"  Error while reading Excel sheet: " + e.getMessage());
			throw e;
		}
	}
	
	private Map<String, String> getHeaderValue(Row headerRow){
	    Map<String, String> headers = new HashMap<>();
        Iterator<Cell> headerCells = headerRow.cellIterator();
        DataFormatter fmt = new DataFormatter();
        for(int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
            Cell cell = (Cell) headerCells.next();
            String headerColumn = fmt.formatCellValue(cell);
            String clientCode = null;
            if(StringUtils.hasText(headerColumn)) {
              clientCode = clientDetailsDao.findByClientName(headerColumn);
            headers.put(headerColumn, clientCode);
            }
            LoggerUtil.log(getClass(), Level.INFO, "getHeaderValue", "Header of the Excel Sheet : " + headers);
        }
		return headers;
	}
	
	private String changeDatePattern(String inputDate) {
		LoggerUtil.log(getClass(), Level.INFO, "inside changeDatePattern method", "inputDate : " + inputDate);
		LocalDate convertedDate = LocalDate.parse(inputDate, inputFormatter);
		String date = convertedDate.format(outputFormatter);
		LoggerUtil.log(getClass(), Level.INFO, "inside changeDatePattern method", "converted Date : " + date);
		return date;
	}


	@Override
	public List<TbaCutoffDateResponse> getTbaCutOffDate(String clientCode) {
		List<TbaCutoffDateResponse> tbaCutOffDates = new ArrayList<>();
		try {
		//	String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientCode));
			List<String> conditions = new ArrayList<>();
			conditions.add(HolmesAppConstants.PREVIOUS_MONTH);
			conditions.add(HolmesAppConstants.NEXT_MONTH);
			TbaCutoffDateResponse response = null;

			String tbaCutOffDate = null;
			LocalDate current = LocalDate.now();
			for (String criteria : conditions) {
				if (HolmesAppConstants.PREVIOUS_MONTH.equals(criteria))
					tbaCutOffDate = tbacutoffDateDao.findPreviousTbaCutoffDate(current, clientCode);
				else if (HolmesAppConstants.NEXT_MONTH.equals(criteria))
					tbaCutOffDate = tbacutoffDateDao.findNextTbaCutoffDate(current, clientCode);

				LoggerUtil.log(this.getClass(), Level.INFO, "inside getTbaCutOffDates method",
						"date critera - " + current.toString() + ", tbaCutOffDate - " + tbaCutOffDate);
				response = new TbaCutoffDateResponse();
				response.setCutOffText(criteria);
				response.setCutOffValue(tbaCutOffDate);
				tbaCutOffDates.add(response);
			}
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTbaCutOffDate ",
					" Exception Occurred while fetching tba cut off date : " + e.getMessage());
		}
		return tbaCutOffDates;
	}	
}