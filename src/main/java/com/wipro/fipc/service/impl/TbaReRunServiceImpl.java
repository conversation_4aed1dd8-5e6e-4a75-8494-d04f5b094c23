package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaConfigDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaRerunEventMaster;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.TbaUpdateConfigDto;
import com.wipro.fipc.model.generated.TbaUpdateConfig;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.ITbaReRunService;
import com.wipro.fipc.tba.service.TbarerunService;
import com.wipro.fipc.tba.service.UpdateConfigService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class TbaReRunServiceImpl implements ITbaReRunService {

	@Autowired
	TbaConfigDao tbaConfigDao;
	@Autowired
	UpdateConfigService updateConfigService;
	@Autowired
	TbarerunService tbaRerunService;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	/* Save TbaUpdateConfigDto details */
	@Override
	public String saveReRun(List<TbaUpdateConfigDto> tbaList, String appName, String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "TbaReRunServiceImpl-->submit()-->starts:");
		ResponseEntity<String> res = null;
		HttpHeaders headers = new HttpHeaders();
		headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.CONTENT_TYPE_VALUE);
		try {
			String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
			Iterator<TbaUpdateConfigDto> ite = tbaList.iterator();
			while (ite.hasNext()) {
				TbaUpdateConfigDto dto = ite.next();
				if (dto.getId() != null) {
					com.wipro.fipc.entity.tba.TbaUpdateConfig rerun = updateConfigService
							.findById(dto.getId());
					ObjectMapper objectMapper = new ObjectMapper();
					objectMapper.configure(
							com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
					String resp = objectMapper.writeValueAsString(rerun);

					TbaUpdateConfig config = null;
					if (StringUtils.isNotBlank(resp)) {
						config = new ObjectMapper().readValue(resp, TbaUpdateConfig.class);
						dto.setAddManualFlag(config.getAddManualFlag());
						dto.setRerunFlag(config.getRerunFlag());
						dto.setCreatedBy(config.getCreatedBy());
						dto.setCreatedDate(config.getCreatedDate());
					}

					dto.setUpdatedDate(new Date());
					dto.setUpdatedBy(adId);
					dto.setActiveFlag("T");

					LoggerUtil.log(this.getClass(), Level.INFO, "saveReRun>>>>>>>>",
							"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
									+ dto.getProcessJobMappingId() + ",ADID: " + adId);

				} else {
					dto.setCreatedDate(new Date());
					dto.setCreatedBy(adId);
					dto.setActiveFlag("T");
					dto.setUpdatedDate(new Date());
					dto.setUpdatedBy(adId);

					LoggerUtil.log(this.getClass(), Level.INFO, "saveReRun>>>>>>>>",
							"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Create" + ",PJMID = "
									+ dto.getProcessJobMappingId() + ",ADID: " + adId);
				}
			}
			List<com.wipro.fipc.pojo.tba.TbaUpdateConfigDto> newLayout = new ArrayList<>();
			for (TbaUpdateConfigDto req : tbaList) {

				String convertedString = null;
				com.wipro.fipc.pojo.tba.TbaUpdateConfigDto TbaUpdateConfigDtoobj = null;
				try {

					ObjectMapper objectMapper = new ObjectMapper();
					objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
					TbaUpdateConfigDtoobj = objectMapper.convertValue(req,
							com.wipro.fipc.pojo.tba.TbaUpdateConfigDto.class);
				} catch (Exception e) {
					e.printStackTrace();
				}
				newLayout.add(TbaUpdateConfigDtoobj);
			}
			List<CommonResRowBO> mydata = tbaConfigDao.saveIfNotDuplicate(newLayout);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String resp = objectMapper.writeValueAsString(mydata);

			LoggerUtil.log(this.getClass(), Level.INFO, "submit()2", "TbaReRunServiceImpl-->submit()-->ends:");

			ResponseDto response = new ResponseDto();
			response.setData(resp);
			response.setStatus("success");
			response.setMessage("Records Saved/Updated Successfully.");
			return objectMapper.writeValueAsString(response);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "submit()3",
					"TbaReRunServiceImpl-->submit()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}

	}

	/* GetTbaReRunData Using coloumn name and Coloumn value */

	@Override
	public List<TbaUpdateConfigDto> getTbaReRunData(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaReRunData()",
				"TbaReRunServiceImpl-->getTbaReRunData()-->starts:");
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaReRunData>>>>>>>>>>>", "Method Type= "
				+ HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + columnName + "=" + columnValue);
		try {

			List<com.wipro.fipc.entity.tba.TbaUpdateConfig> mydata = tbaRerunService.findByColumnByKSD(columnName,
					columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String res = objectMapper.writeValueAsString(mydata);
			List<TbaUpdateConfig> tbaConfigList = Arrays
					.asList(new ObjectMapper().readValue(res, TbaUpdateConfig[].class));
			tbaConfigList = tbaConfigList.stream().filter(obj -> obj.getRerunFlag().equals("Y"))
					.collect(Collectors.toList());

			List<TbaUpdateConfigDto> configDtos = new ArrayList<>();

			for (TbaUpdateConfig tbaUpdateConfig2 : tbaConfigList) {
				TbaUpdateConfigDto eventConfig = new TbaUpdateConfigDto();
				BeanUtils.copyProperties(eventConfig, tbaUpdateConfig2);
				configDtos.add(eventConfig);
			}

			LoggerUtil.log(this.getClass(), Level.INFO, "getTbaReRunData()1",
					"TbaReRunServiceImpl-->getTbaReRunData()--ends.");
			return configDtos;
		} catch (IllegalAccessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateConfig()", "IllegalAccessException: " + e);
		} catch (InvocationTargetException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateConfig()1", "InvocationTargetException: " + e);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaReRunData()2",
					"TbaReRunServiceImpl-->getTbaReRunData()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
		return new ArrayList<>();
	}

	/* Get EventHistoryMaster */

	@Override
	public String getEventHistoryMaster(String columnName, String columnValue) throws JsonProcessingException {

		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistoryMaster()",
				"TbaReRunServiceImpl-->getEventHistoryMaster()-->starts:");
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistoryMaster>>>>>>>>>>>", "Method Type= "
				+ HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + columnName + "=" + columnValue);

		List<TbaRerunEventMaster> mydata = tbaRerunService.findRecordByColumn(columnName, columnValue);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String res1 = objectMapper.writeValueAsString(mydata);

		return res1;

	}

	/* Delete ReRunConfig Details */
	@Override
	public String deleteReRunConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken) {
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		entities.stream().forEach(s -> {
			s.setUpdatedBy(adId);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteReRunConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});
		List<com.wipro.fipc.entity.tba.TbaUpdateConfig> newLayout = new ArrayList<>();
		for (CommonDeleteDTO req : entities) {

			String convertedString = null;
			try {
				convertedString = objectMapper.writeValueAsString(req);
			} catch (JsonProcessingException e1) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "", e1.getMessage());
			}
			com.wipro.fipc.entity.tba.TbaUpdateConfig obj = null;
			try {
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				obj = objectMapper.readValue(convertedString,
						com.wipro.fipc.entity.tba.TbaUpdateConfig.class);
			} catch (IOException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "", e.getMessage());
			}
			newLayout.add(obj);
		}
		List<CommonRowBO> mydata = updateConfigService.deletemultiplerows(1, newLayout);
		String res = null;
		try {
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", e.getMessage());
		}

		return res;
	}

}
