package com.wipro.fipc.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.common.TemplateReportLayOutDao;
import com.wipro.fipc.dao.tba.ProcessJobMappingDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.TemplateReportLayOut;
import com.wipro.fipc.entity.batch.KsdFileDetails;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.filelayout.ProcessFeatureConfig;
import com.wipro.fipc.entity.layoutrule.LayoutConfig;
import com.wipro.fipc.entity.tba.EventHistInqConfig;
import com.wipro.fipc.entity.tba.EventInquiryConfig;
import com.wipro.fipc.entity.tba.TbaCommentInqConfig;
import com.wipro.fipc.entity.tba.TbaInquiryConfig;
import com.wipro.fipc.entity.tba.TbaNoticeInqConfig;
import com.wipro.fipc.entity.tba.TbaPendingEvent;
import com.wipro.fipc.entity.tba.TbaTransSubTransMapping;
import com.wipro.fipc.entity.tba.TbaUpdateConfig;
import com.wipro.fipc.model.TbaCommonReport;
import com.wipro.fipc.model.TbaSubTransMapping;

@Service
public class TbaCommonServiceImpl {

	@Autowired
	GenericDao<EventInquiryConfig> tbaEventInquiryConfigDao;
	
	@Autowired
	GenericDao<TbaInquiryConfig> tbaInquiryConfigDao;

	@Autowired
	GenericDao<EventHistInqConfig> eventHistInqConfigDao;

	@Autowired
	GenericDao<TbaNoticeInqConfig> tbaNoticeInqConfigDao;

	@Autowired
	GenericDao<TbaCommentInqConfig> tbaCommentInqConfigDao;

	@Autowired
	GenericDao<TbaPendingEvent> tbaPendingEventDao;

	@Autowired
	GenericDao<TbaUpdateConfig> tbaUpdateConfigDao;

	@Autowired
	GenericDao<LayoutConfig> layoutConfigDao;

	@Autowired
	GenericDao<KsdFileDetails> ksdFileDetailsDao;

	@Autowired
	GenericDao<KsdOutPutFileDetails> ksdOutPutFileDetailsDao;

	@Autowired
	GenericDao<ProcessFeatureConfig> processFeatureConfigDao;

	@Autowired
	TemplateReportLayOutDao templateReportLayOutDao;
	
	@Autowired
	private ProcessJobMappingDao processJobMappingDao;
	
	@Autowired
	private GenericDao<TbaTransSubTransMapping> tbaSubTransDao;

	public List<TbaCommonReport> getListOfData(String pjmid) {
		
		ProcessJobMapping processJobMapping = processJobMappingDao.finaByProcessJobMappingId(Long.valueOf(pjmid));
		String clientCode = processJobMapping.getClientDetails().getClientCode();
		
		Set<String> panelIdSet = new HashSet<>();
		Set<String> transIdSet = new HashSet<>();
		List<TbaCommonReport> tbaCommon = new ArrayList<>();
		List<EventInquiryConfig> tbaEventInquiryConfigList = tbaEventInquiryConfigDao.findByColumn(EventInquiryConfig.class, "tba",
				"EVENT_INQUIRY_CONFIG", "process_job_mapping_id", pjmid);
		tbaEventInquiryConfigList.forEach(TbaInquiryConfig -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			
			String panelId = String.valueOf(TbaInquiryConfig.getPanelId());
			if (StringUtils.hasText(panelId)
					&& StringUtils.hasText(TbaInquiryConfig.getTransId())) {

				panelIdSet.add(panelId);
				transIdSet.add(TbaInquiryConfig.getTransId());
			}
			tbaCommonReport.setPanelId(TbaInquiryConfig.getPanelId());
			tbaCommonReport.setTransId(TbaInquiryConfig.getTransId());
			tbaCommonReport.setId(TbaInquiryConfig.getId());
			tbaCommonReport.setProcessJobMappingId(TbaInquiryConfig.getProcessJobMapping().getId());
			tbaCommonReport.setFileName("TBA");
			tbaCommonReport.setFileNameWoutSpace("TBA");
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType(TbaInquiryConfig.getFieldType());
			tbaCommonReport.setMfFieldName(TbaInquiryConfig.getEventInquiryDefName());
			tbaCommonReport.setMfFieldWoutSpace("");
			tbaCommonReport.setRecordIdentifier(
					"NA".equals(TbaInquiryConfig.getRecordIdentifier()) ? "" : TbaInquiryConfig.getRecordIdentifier());
			tbaCommonReport.setSheetName("");
			tbaCommonReport.setSheetNameWoutSpace("");
			tbaCommon.add(tbaCommonReport);
		});
		
		if (!CollectionUtils.isEmpty(panelIdSet) && !CollectionUtils.isEmpty(transIdSet)) {
			List<TbaTransSubTransMapping> tbaSubTransMappingList = getTbaSubTransMapping(clientCode, panelIdSet, transIdSet);
			if (!CollectionUtils.isEmpty(tbaSubTransMappingList)) {
				tbaCommon.forEach(tbaCommonReport -> {
					String reportPanelId = String.valueOf(tbaCommonReport.getPanelId());
					String reportTransId = tbaCommonReport.getTransId();
					if (StringUtils.hasText(reportPanelId) && (StringUtils.hasText(reportTransId))) {
						List<TbaSubTransMapping> subTransMappingList = tbaSubTransMappingList.stream()
								.filter(mapping ->
									reportPanelId.equals(String.valueOf(mapping.getPanelId())) &&
									reportTransId.equals(mapping.getTransId())
								)
								.map(mapping -> TbaSubTransMapping.builder().subTransCode(mapping.getSubTransCode())
										.subTransCodeDesc(String.join(HolmesAppConstants.SEPARATOR,
												mapping.getSubTransCode(),mapping.getSubTransCodeDesc()))
										.build())
								.collect(Collectors.toList());
						tbaCommonReport.setTbaSubTransMapping(subTransMappingList);
					}
				});
			}
		}

		List<TbaInquiryConfig> tbaInquiryConfigList = tbaInquiryConfigDao.findByColumn(TbaInquiryConfig.class, "tba",
				"TBA_INQUIRY_CONFIG", "process_job_mapping_id", pjmid);
		tbaInquiryConfigList.forEach(TbaInquiryConfig -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			tbaCommonReport.setId(TbaInquiryConfig.getId());
			tbaCommonReport.setProcessJobMappingId(TbaInquiryConfig.getProcessJobMapping().getId());
			tbaCommonReport.setFileName("TBA");
			tbaCommonReport.setFileNameWoutSpace("TBA");
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType(TbaInquiryConfig.getFieldType());
			tbaCommonReport.setMfFieldName(TbaInquiryConfig.getInquiryDefName());
			tbaCommonReport.setMfFieldWoutSpace("");
			tbaCommonReport.setRecordIdentifier(
					"NA".equals(TbaInquiryConfig.getIdentifier()) ? "" : TbaInquiryConfig.getIdentifier());
			tbaCommonReport.setSheetName("");
			tbaCommonReport.setSheetNameWoutSpace("");
			tbaCommon.add(tbaCommonReport);
		});
		List<EventHistInqConfig> eventHistInqConfigList = eventHistInqConfigDao.findByColumn(EventHistInqConfig.class,
				"tba", "EVENT_HIST_INQ_CONFIG", "process_job_mapping_id", pjmid);
		eventHistInqConfigList.forEach(eventHistInqConfig -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			tbaCommonReport.setId(eventHistInqConfig.getId());
			tbaCommonReport.setProcessJobMappingId(eventHistInqConfig.getProcessJobMappingId());
			tbaCommonReport.setFileName("TBA");
			tbaCommonReport.setFileNameWoutSpace("TBA");
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType(eventHistInqConfig.getFiledType());
			tbaCommonReport.setMfFieldName(eventHistInqConfig.getEventHistDefName());
			tbaCommonReport.setMfFieldWoutSpace("");
			tbaCommonReport.setRecordIdentifier("");
			tbaCommonReport.setSheetName("");
			tbaCommonReport.setSheetNameWoutSpace("");
			tbaCommon.add(tbaCommonReport);
		});
		List<TbaNoticeInqConfig> tbaNoticeInqConfigList = tbaNoticeInqConfigDao.findByColumn(TbaNoticeInqConfig.class,
				"tba", "TBA_NOTICE_INQ_CONFIG", "process_job_mapping_id", pjmid);
		tbaNoticeInqConfigList.forEach(tbaNoticeInqConfig -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			tbaCommonReport.setId(tbaNoticeInqConfig.getId());
			tbaCommonReport.setProcessJobMappingId(tbaNoticeInqConfig.getProcessJobMappingId());
			tbaCommonReport.setFileName("TBA");
			tbaCommonReport.setFileNameWoutSpace("TBA");
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType(tbaNoticeInqConfig.getFieldType());
			tbaCommonReport.setMfFieldName(tbaNoticeInqConfig.getInquiryDefName());
			tbaCommonReport.setMfFieldWoutSpace("");
			tbaCommonReport.setRecordIdentifier(tbaNoticeInqConfig.getRecordIdentifier());
			tbaCommonReport.setSheetName("");
			tbaCommonReport.setSheetNameWoutSpace("");
			tbaCommon.add(tbaCommonReport);
		});
		List<TbaCommentInqConfig> tbaCommentInqConfigList = tbaCommentInqConfigDao.findByColumn(
				TbaCommentInqConfig.class, "tba", "TBA_COMMENT_INQ_CONFIG", "process_job_mapping_id", pjmid);
		tbaCommentInqConfigList.stream()
		.filter(tbaCommentInqConfig -> !tbaCommentInqConfig.getUpdateFlag())
		.forEach(tbaCommentInqConfig -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			tbaCommonReport.setId(tbaCommentInqConfig.getId());
			tbaCommonReport.setProcessJobMappingId(tbaCommentInqConfig.getProcessJobMapping().getId());
			tbaCommonReport.setFileName("TBA");
			tbaCommonReport.setFileNameWoutSpace("TBA");
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType(tbaCommentInqConfig.getFieldType());
			tbaCommonReport.setMfFieldName(tbaCommentInqConfig.getInquiryDefName());
			tbaCommonReport.setMfFieldWoutSpace("");
			tbaCommonReport.setRecordIdentifier("");
			tbaCommonReport.setSheetName("");
			tbaCommonReport.setSheetNameWoutSpace("");
			tbaCommon.add(tbaCommonReport);
		});
		List<TbaPendingEvent> tbaPendingEventList = tbaPendingEventDao.findByColumn(TbaPendingEvent.class, "tba",
				"PENDING_EVENT_INQ_CONFIG", "process_job_mapping_id", pjmid);
		tbaPendingEventList.forEach(tbaPendingEvent -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			tbaCommonReport.setId(tbaPendingEvent.getId());
			tbaCommonReport.setProcessJobMappingId(tbaPendingEvent.getProcessJobMapping().getId());
			tbaCommonReport.setFileName("TBA");
			tbaCommonReport.setFileNameWoutSpace("TBA");
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType("");
			tbaCommonReport.setMfFieldName(tbaPendingEvent.getPendgEvntDefName());
			tbaCommonReport.setMfFieldWoutSpace("");
			tbaCommonReport.setRecordIdentifier(tbaPendingEvent.getIdentifier());
			tbaCommonReport.setSheetName("");
			tbaCommonReport.setSheetNameWoutSpace("");
			tbaCommon.add(tbaCommonReport);
		});
		List<TbaUpdateConfig> tbaUpdateConfigList = tbaUpdateConfigDao.findByColumn(TbaUpdateConfig.class, "tba",
				"TBA_UPDATE_CONFIG", "process_job_mapping_id", pjmid);
		tbaUpdateConfigList.forEach(tbaUpdateConfig -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			tbaCommonReport.setId(tbaUpdateConfig.getId());
			tbaCommonReport.setProcessJobMappingId(tbaUpdateConfig.getProcessJobMapping().getId());
			tbaCommonReport.setFileName("TBA");
			tbaCommonReport.setFileNameWoutSpace("TBA");
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType("");
			tbaCommonReport.setMfFieldName(tbaUpdateConfig.getUpdateName());
			tbaCommonReport.setMfFieldWoutSpace("");
			tbaCommonReport.setRecordIdentifier(tbaUpdateConfig.getRecordIdentifier());
			tbaCommonReport.setSheetName("");
			tbaCommonReport.setSheetNameWoutSpace("");
			tbaCommonReport.setSubMetaDataId(tbaUpdateConfig.getSubMetaDataId());
			tbaCommonReport.setSubMetaData(tbaUpdateConfig.getSubMetaData());
			tbaCommon.add(tbaCommonReport);
		});
		ColumnConditionParam columnConditionParam = new ColumnConditionParam();
		List<ColumnConditionParam> columnConditionParamList = new ArrayList<>();
		columnConditionParam.setColName("process_job_mapping_id");
		columnConditionParam.setColValue(pjmid);
		columnConditionParam.setColCondion("eq");
		columnConditionParamList.add(columnConditionParam);
		columnConditionParam = new ColumnConditionParam();
		columnConditionParam.setColCondion("eq");
		columnConditionParam.setColName("active_flag");
		columnConditionParam.setColValue("T");
		columnConditionParamList.add(columnConditionParam);
		List<LayoutConfig> layoutConfigList = layoutConfigDao.findByMultiColumnCondition(LayoutConfig.class,
				"layout_rule", "LAYOUT_CONFIG", columnConditionParamList);
		layoutConfigList.forEach(layoutConfig -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			TbaCommonReport tbaCommonReportClone = new TbaCommonReport();
			tbaCommonReport.setId(layoutConfig.getId());
			tbaCommonReport.setProcessJobMappingId(layoutConfig.getProcessJobMappingConfig().getId());
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType(layoutConfig.getFieldType());
			tbaCommonReport.setMfFieldName(layoutConfig.getMfFieldName());
			tbaCommonReport.setMfFieldWoutSpace(layoutConfig.getMfFieldWoutSpace());
			tbaCommonReport.setRecordIdentifier(layoutConfig.getRecordIdentifier());
			tbaCommonReport.setSheetName(layoutConfig.getSheetName());
			tbaCommonReport.setSheetNameWoutSpace(layoutConfig.getSheetNameWoutSpace());
			tbaCommonReport.setFileFormatType(layoutConfig.getRecordType());
			ColumnConditionParam columnConditionParams = new ColumnConditionParam();
			columnConditionParams.setColCondion("eq");
			columnConditionParams.setColName("file_name");
			columnConditionParams.setColValue(layoutConfig.getFileName().replaceAll("'", "''"));
			columnConditionParamList.add(columnConditionParams);
			List<KsdFileDetails> ksdFileDetailsList = ksdFileDetailsDao.findByMultiColumnCondition(KsdFileDetails.class,
					"emails_scheduler", "KSD_FILE_DETAILS", columnConditionParamList);
			columnConditionParamList.remove(2);
			if (ksdFileDetailsList.size() > 0 && ksdFileDetailsList.get(0).getPrevReportFileName() != null) {
				try {
					tbaCommonReportClone = (TbaCommonReport) tbaCommonReport.clone();
				} catch (CloneNotSupportedException e) {
					e.printStackTrace();
				}
				tbaCommonReportClone.setFileName(ksdFileDetailsList.get(0).getPrevReportFileName());
				tbaCommonReportClone.setFileNameWoutSpace(ksdFileDetailsList.get(0).getPrevReportFileNameWs());
				tbaCommon.add(tbaCommonReportClone);
				tbaCommonReport.setFileName(layoutConfig.getFileName());
				tbaCommonReport.setFileNameWoutSpace(layoutConfig.getFileNameWoutSpace());
			} else {
				tbaCommonReport.setFileName(layoutConfig.getFileName());
				tbaCommonReport.setFileNameWoutSpace(layoutConfig.getFileNameWoutSpace());
			}
			tbaCommon.add(tbaCommonReport);
		});
		List<KsdOutPutFileDetails> ksdOutPutFileDetailsList = ksdOutPutFileDetailsDao.findByColumn(
				KsdOutPutFileDetails.class, "layout_rule", "KSD_OUTPUT_FILE_DETAILS", "process_job_mapping_id", pjmid);
		ksdOutPutFileDetailsList.forEach(ksdOutPutFileDetails -> {

			ksdOutPutFileDetails.getOutputReports().forEach(outputReports -> {
				TbaCommonReport tbaCommonReport = new TbaCommonReport();
				TbaCommonReport tbaCommonReportClone = new TbaCommonReport();
				tbaCommonReport.setProcessJobMappingId(ksdOutPutFileDetails.getProcessJobMapping().getId());
				tbaCommonReport.setSheetName(ksdOutPutFileDetails.getSheetName());
				tbaCommonReport.setSheetNameWoutSpace(ksdOutPutFileDetails.getSheetNameWoutSpace());
				tbaCommonReport.setId(outputReports.getId());
				tbaCommonReport.setFileNameWoutSpace("");
				tbaCommonReport.setFileType("");
				tbaCommonReport.setFieldType("");
				tbaCommonReport.setMfFieldName(outputReports.getDataElement());
				tbaCommonReport.setMfFieldWoutSpace(outputReports.getDataElementWoutSpace());
				tbaCommonReport.setRecordIdentifier(outputReports.getRecordIdentifier());
				if (ksdOutPutFileDetails.getPrevReportFileName() != null) {
					try {
						tbaCommonReportClone = (TbaCommonReport) tbaCommonReport.clone();
					} catch (CloneNotSupportedException e) {
						e.printStackTrace();
					}
					tbaCommonReportClone.setFileName(ksdOutPutFileDetails.getPrevReportFileName());
					tbaCommon.add(tbaCommonReportClone);
					tbaCommonReport.setFileName(outputReports.getFileName());
				} else {
					tbaCommonReport.setFileName(outputReports.getFileName());
				}
				tbaCommon.add(tbaCommonReport);
			});

		});

		List<ProcessFeatureConfig> processFeatureConfigList = processFeatureConfigDao.findByColumn(
				ProcessFeatureConfig.class, "common", "PROCESS_FEATURE_CONFIG", "process_job_mapping_id", pjmid);
		List<TemplateReportLayOut> templateReportLayOutList = new ArrayList<>();
		processFeatureConfigList.forEach(processFeatureConfig -> {
			Matcher m = Pattern.compile(".*\"externalReports\":([^]]+)").matcher(processFeatureConfig.getPhaseNames());
			if (m.find()) {
				Matcher regexMatcher = Pattern.compile("\"fileName\":\"([^\"]+)").matcher(m.group(1));
				while (regexMatcher.find()) {
					for (int i = 1; i <= regexMatcher.groupCount(); i++) {
						List<TemplateReportLayOut> templateReportLayOut = templateReportLayOutDao
								.getListOfData(processFeatureConfig.getClientCode(), regexMatcher.group(i));
						templateReportLayOutList.addAll(templateReportLayOut);
					}
				}
			}
		});

		templateReportLayOutList.forEach(templateReportLayOut -> {
			TbaCommonReport tbaCommonReport = new TbaCommonReport();
			tbaCommonReport.setId(templateReportLayOut.getId());
			tbaCommonReport.setProcessJobMappingId(Long.parseLong(pjmid));
			tbaCommonReport.setFileName(templateReportLayOut.getTemplateReportName());
			tbaCommonReport.setFileNameWoutSpace(templateReportLayOut.getTemplateReportNameWs());
			tbaCommonReport.setFileType("");
			tbaCommonReport.setFieldType(templateReportLayOut.getFiledType());
			tbaCommonReport.setMfFieldName(templateReportLayOut.getDataElement());
			tbaCommonReport.setMfFieldWoutSpace(templateReportLayOut.getDataElementWs());
			tbaCommonReport.setRecordIdentifier(templateReportLayOut.getIdentifier());
			tbaCommonReport.setSheetName(templateReportLayOut.getSheetName());
			tbaCommonReport.setSheetNameWoutSpace(templateReportLayOut.getSheetNameWs());
			tbaCommon.add(tbaCommonReport);
		});
		return tbaCommon;
	}

	private List<TbaTransSubTransMapping> getTbaSubTransMapping(String clientCode, Set<String> panelIdSet, Set<String> trasnIdSet) {
		LoggerUtil.log(getClass(), Level.INFO, "getTbaSubTransMapping", "getTbaSubTransMapping method start.");

		String panelIdParam = "'" + String.join("','", panelIdSet) + "'";
		String transIdParam = "'" + String.join("','", trasnIdSet) + "'";

		List<TbaTransSubTransMapping> tbaSubTransMappingList = null;
		try {
			tbaSubTransMappingList = tbaSubTransDao.findByMultiColumn(TbaTransSubTransMapping.class, "tba",
					"tba_trans_sub_trans_mapping", clientCode, panelIdParam, transIdParam);
			
			LoggerUtil.log(getClass(), Level.INFO, "getTbaSubTransMapping",
					"size of tbaSubTransMappingList: " + tbaSubTransMappingList.size());

		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTbaSubTransMapping", e.getMessage());
		}

		return tbaSubTransMappingList;
	}

}