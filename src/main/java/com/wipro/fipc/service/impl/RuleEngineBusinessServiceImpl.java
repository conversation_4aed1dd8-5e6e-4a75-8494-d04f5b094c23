package com.wipro.fipc.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.DBServiceDataImpl;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.MatchConditionRepository;
import com.wipro.fipc.dao.ProcessControlConfigRepo;
import com.wipro.fipc.dao.RulesConfigRepo;
import com.wipro.fipc.dao.RulesDefinitionRepository;
import com.wipro.fipc.dao.TbaMatchConfigRepo;
import com.wipro.fipc.dao.common.TemplateReportLayOutDao;
import com.wipro.fipc.dao.restservice.GenericService;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.CommonRowBO;
import com.wipro.fipc.entity.common.MatchConditions;
import com.wipro.fipc.entity.common.RulesDefinition;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.filelayout.ProcessControlConfig;
import com.wipro.fipc.entity.tba.EventInquiryConfig;
import com.wipro.fipc.entity.tba.TbaMatchConfig;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.RulesRequest;
import com.wipro.fipc.model.generated.EventHistInqConfig;
import com.wipro.fipc.model.generated.LayoutConfig;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.TbaCommentInqConfig;
import com.wipro.fipc.model.generated.TbaCommonReport;
import com.wipro.fipc.model.generated.TbaInquiryConfig;
import com.wipro.fipc.model.generated.TbaNoticeInqConfig;
import com.wipro.fipc.model.generated.TbaPendingEvent;
import com.wipro.fipc.model.generated.TbaUpdateConfig;
import com.wipro.fipc.model.generated.TemplateReportLayOut;
import com.wipro.fipc.model.generated.ValidationType;
import com.wipro.fipc.service.IRuleEngineBusinessService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class RuleEngineBusinessServiceImpl implements IRuleEngineBusinessService {

	private static final String ADID2 = " ADID =";

	private static final String RULE_NAME = "ruleName = ";

	private static final String CALLING_DB_SERVICE = "Calling DB service: ";

	private static final String SHEET_NAME_WOUT_SPACE = "sheetNameWoutSpace";

	private static final String SHEET_NAME = "sheetName";

	protected static final String RULES_CONFIG = "RULES_CONFIG";
	protected static final String LAYOUT_SCHEMA = "layout_rule";

	protected static final String LAYOUT_CONFIG = "LAYOUT_CONFIG";
	public static final String MULTICOLUMN = "find By MultiColumn Condition";
	public static final String INLAYOUTAFTTERREPLACE = "In  LayoutConfig After Replace: ";

	protected static final String TBA_INQUIRY_CONFIG = "tba_inquiry_config";
	protected static final String SCHEMA = "tba";
	protected static final String EVENT_HIST_INQ_CONFIG = "EVENT_HIST_INQ_CONFIG";
	protected static final String TBA_NOTICE_INQ_CONFIG = "TBA_NOTICE_INQ_CONFIG";
	protected static final String TBA_COMMENT_INQ_CONFIG = "TBA_COMMENT_INQ_CONFIG";
	protected static final String EVENT_INQUIRY_CONFIG = "event_inquiry_config";
	
	@Autowired
	RuleEngineDOConverter doConverter;

	@Autowired
	Environment env;

	@Autowired
	JsonParser parser;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	GenericDao<com.wipro.fipc.entity.filelayout.RulesConfig> genericDao;

	@Autowired
	GenericService<com.wipro.fipc.entity.filelayout.RulesConfig> genericService;

	@Autowired
	MatchConditionRepository matchConditionService;

	@Autowired
	private TbaCommonServiceImpl tbaCommonServiceImpl;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.EventInquiryConfig> genericDaoTbaEventInquiry;
	
	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.TbaInquiryConfig> genericDaoTbaInquiry;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.EventHistInqConfig> genericDaoTbaEventHistory;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> genericDaoTbaNoticeInquiry;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.TbaCommentInqConfig> genericDaoTbaCommentInquiry;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.TbaPendingEvent> genericDaoTbaPendingEvent;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.TbaUpdateConfig> genericDaoTbaUpdateConfig;

	@Autowired
	GenericDao<com.wipro.fipc.entity.layoutrule.LayoutConfig> genericDaoLayoutConfig;

	@Autowired
	
	private DBServiceDataImpl dbServiceData;

	@Autowired
	GenericDao<KsdOutPutFileDetails> genericDaoKsdOutputFileDetails;

	@Autowired
	TemplateReportLayOutDao templateReportLayOutDao;

	@Autowired
	TbaMatchConfigRepo tbaMatchConfigRepo;

	@Autowired
	ProcessControlConfigRepo processControlConfigRepo;

	@Autowired
	RulesDefinitionRepository rulesDefinitionRepo;

	@Autowired
	RulesConfigRepo rulesConfigRepo;

	public static final String LOGGER = "Unable to connect to DB Service due to :";
	public static final String DB_RULE_URL = "db.rule.url";
	public static final String CONTENT_TYPE = "Content-Type";
	public static final String APPLICATION = "application/json";
	public static final String GET_RULEID_URL = "db.rule.getByRuleConfigId.url";
	public static final String UPDATE_LOGGER = "updateBusinessRulesByRuleId";
	public static final String INFO_LOGGER = CALLING_DB_SERVICE;
	public static final String ERROR_LOGGER = "update by rule id failed : ";
	public static final String PJM_ID = "pjmID:";
	protected static final String KSD_OUTPUT_FILE_DETAILS = "KSD_OUTPUT_FILE_DETAILS";
	protected static final String OUT_SCHEMA = "layout_rule";

	@Override
	public RulesRequest createBusinessRules(RulesRequest rulesRequest) {
		RulesRequest response = null;

		try {
			LoggerUtil.log(getClass(), Level.INFO, "createBusinessRules",
					"Inside createBusinessRules method, Calling DB service : ");

			RulesConfig rulesConfigDO = doConverter.convertBizRuleRequestToDO(rulesRequest);

			ObjectMapper mapper = new ObjectMapper();
			com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
					com.wipro.fipc.entity.filelayout.RulesConfig.class);
			com.wipro.fipc.entity.filelayout.RulesConfig responseRulesConfig = genericService.create(rulesConfig);
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = doConverter.convertCreateResponse(responseRulesConfig);

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "createBusinessRules", LOGGER + exception.getMessage());
			throw new BusinessException("creation of rule failed : " + exception.getMessage());
		}
		return response;
	}

	@Override
	public RulesRequest updateBusinessRulesByRuleId(RulesRequest rulesRequest, String pjmId, String ruleId) {
		JsonParser parser = new JsonParser();
		String getResponse = null;
		RulesRequest response = null;
		RulesConfig config = null;
		Gson gson = new Gson();

		try {

			List<com.wipro.fipc.entity.filelayout.RulesConfig> ruleConfigData = genericDao.findByColumn(
					com.wipro.fipc.entity.filelayout.RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, "id", ruleId);
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			getResponse = ow.writeValueAsString(ruleConfigData);
			JsonArray arr = parser.parse(getResponse).getAsJsonArray();
			for (int i = 0; i < arr.size(); i++) {
				config = gson.fromJson(arr.get(i).getAsJsonObject(), RulesConfig.class);
			}

			RulesConfig rulesConfigDO = doConverter.updateByRuleIdConvertor(rulesRequest, config, ruleId);

			ObjectMapper mapper = new ObjectMapper();
			com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
					com.wipro.fipc.entity.filelayout.RulesConfig.class);

			com.wipro.fipc.entity.filelayout.RulesConfig responseUpdateData = genericService
					.update(Long.valueOf(ruleId), rulesConfig);
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			String updateData = mapper.writeValueAsString(responseUpdateData);
			response = doConverter.convertCreateResponse(updateData);

		} catch (Exception exception) {

			LoggerUtil.log(getClass(), Level.ERROR, UPDATE_LOGGER, LOGGER + exception.getMessage());
			throw new BusinessException(ERROR_LOGGER + exception.getMessage());
		}

		return response;

	}

	@Override
	public String deleteBusinessRule(String id) {
		String response = null;
		try {
			boolean deleteResponse = genericDao.deleteByColumn(com.wipro.fipc.entity.filelayout.RulesConfig.class,
					LAYOUT_SCHEMA, RULES_CONFIG, "id", id);

			response = String.valueOf(deleteResponse);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteBusinessRule", LOGGER + exception.getMessage());
			throw new BusinessException("delete by ruleId failed : " + exception.getMessage());
		}
		return response;
	}

	@Override
	public String deleteAllBusinessRule(String processJobMappingId) {
		String response = null;
		try {
			boolean deleteResponse = genericDao.deleteByColumn(com.wipro.fipc.entity.filelayout.RulesConfig.class,
					LAYOUT_SCHEMA, RULES_CONFIG, "process_job_mapping_id", processJobMappingId);
			response = String.valueOf(deleteResponse);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteAllBusinessRule", LOGGER + exception.getMessage());
			throw new BusinessException("deletion of rule failed : " + exception.getMessage());
		}
		return response;
	}

	@Override
	public List<RulesRequest> getByPjmId(String id) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getByPjmId>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,pjmId= " + id);
		return getDataById("process_job_mapping_id", id);
	}

	@Override
	public List<RulesRequest> getByRuleConfigId(String id) {
		return getDataById("id", id);
	}

	private List<RulesRequest> getDataById(String column, String id) {
		List<RulesRequest> rulesRequestJson = new ArrayList<>();
		List<RulesConfig> rulesConfigJson = new ArrayList<>();

		try {
			LoggerUtil.log(getClass(), Level.INFO, "getByRuleConfigId", "Calling DB service for get by rule id : ");
			List<com.wipro.fipc.entity.filelayout.RulesConfig> responseData = genericDao.findByColumn(
					com.wipro.fipc.entity.filelayout.RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, column, id);
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			 List<com.wipro.fipc.entity.common.RulesDefinition> rulesDefinitions2;
		        for(com.wipro.fipc.entity.filelayout.RulesConfig entity : responseData) {
		        	RulesConfig config =new RulesConfig();
		        	ProcessJobMapping processJobMapping=new ProcessJobMapping();
		        	processJobMapping.setId(entity.getProcessJobMapping().getId());
		        	com.wipro.fipc.model.generated.RulesDefinition rd = new com.wipro.fipc.model.generated.RulesDefinition();
		        	 rulesDefinitions2 =new ArrayList<>();
		        	 rulesDefinitions2=entity.getRulesDefinitions();
		        	 List<com.wipro.fipc.model.generated.RulesDefinition> rulesDefinitions23 = new ArrayList<>();
		        	 Long validationid;
		        	  for(com.wipro.fipc.entity.common.RulesDefinition entity2 : rulesDefinitions2) {
		        		 validationid=entity2.getValidationType().getId();
		        			BeanUtils.copyProperties(entity2, rd);
		        			ValidationType validationType=new ValidationType();
				        	validationType.setId(validationid);
		        			rd.setValidationType(validationType);
		        			rulesDefinitions23.add(rd);
		        	  }
		        	
		        	BeanUtils.copyProperties(entity, config);
		        	
		      
		        	config.setProcessJobMapping(processJobMapping);
		        	config.setRulesDefinitions(rulesDefinitions23);
		        	rulesConfigJson.add(config);
		        }

			rulesRequestJson = doConverter.convertBusinessDOToJson(rulesConfigJson);
		} catch (Exception exception) {

			LoggerUtil.log(getClass(), Level.ERROR, "getByRuleConfigId", LOGGER + exception.getMessage());
			throw new BusinessException("getByRuleId failed : " + exception.getMessage());
		}
		return rulesRequestJson;
	}
	
	@Override
	public List<Object> getAllConditions() {
		String response = null;
		JsonParser parser = new JsonParser();
		Gson gson = new Gson();
		List<Object> objList = new ArrayList<>();
		try {
			LoggerUtil.log(getClass(), Level.INFO, "getAllConditions", "Calling DB service for conditions: ");
			LoggerUtil.log(this.getClass(), Level.INFO, "getAllConditions>>>>>>>",
					"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET ");

			List<MatchConditions> matchConditionsResponse = matchConditionService.list();
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = ow.writeValueAsString(matchConditionsResponse);

			JsonArray arr = parser.parse(response).getAsJsonArray();
			for (int i = 0; i < arr.size(); i++) {
				Object object = gson.fromJson(arr.get(i).getAsJsonObject(), Object.class);
				objList.add(object);
			}
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllConditions", LOGGER + exception.getMessage());
			throw new BusinessException("error in getAllConditions method : " + exception.getMessage());
		}
		return objList;
	}

	@Override
	public String updateJsonWoutSpace(String ruleId) {

		String response = null;
		try {
			RulesConfig config = getDataFromDB(ruleId);

			RulesConfig rulesConfigDO = doConverter.updateJsonWoutSpace(config, ruleId);

			ObjectMapper mapper = new ObjectMapper();
			com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
					com.wipro.fipc.entity.filelayout.RulesConfig.class);

			com.wipro.fipc.entity.filelayout.RulesConfig updateData = genericService.update(Long.valueOf(ruleId),
					rulesConfig);
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = mapper.writeValueAsString(updateData);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, UPDATE_LOGGER, LOGGER + exception.getMessage());
			throw new BusinessException(ERROR_LOGGER + exception.getMessage());
		}

		return response;
	}

	private RulesConfig getDataFromDB(String id) {
		RulesConfig config = null;
		Gson gson = new Gson();
		JsonParser parser = new JsonParser();
		String getResponse = null;

		LoggerUtil.log(getClass(), Level.INFO, UPDATE_LOGGER, INFO_LOGGER);
		List<com.wipro.fipc.entity.filelayout.RulesConfig> responseData = genericDao.findByColumn(
				com.wipro.fipc.entity.filelayout.RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, "id", id);
		ObjectMapper ow = new ObjectMapper();
		ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			getResponse = ow.writeValueAsString(responseData);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray arr = parser.parse(getResponse).getAsJsonArray();
		for (int i = 0; i < arr.size(); i++) {
			config = gson.fromJson(arr.get(i).getAsJsonObject(), RulesConfig.class);
		}
		return config;
	}

	@Override
	public String updateFileName(String ruleId) {

		String response = null;
		try {
			RulesConfig config = getDataFromDB(ruleId);
			RulesConfig rulesConfigDO = doConverter.updateFileName(config, ruleId);

			ObjectMapper mapper = new ObjectMapper();
			com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
					com.wipro.fipc.entity.filelayout.RulesConfig.class);

			com.wipro.fipc.entity.filelayout.RulesConfig updateData = genericService.update(Long.valueOf(ruleId),
					rulesConfig);
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = mapper.writeValueAsString(updateData);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, UPDATE_LOGGER, LOGGER + exception.getMessage());
			throw new BusinessException(ERROR_LOGGER + exception.getMessage());
		}

		return response;
	}

	@Override
	public List<Object> getAllData(String pjmId) {
		String response = null;
		JsonParser parser = new JsonParser();
		Gson gson = new Gson();
		List<Object> objList = new ArrayList<>();
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllData>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,pjmId= " + pjmId);
		try {

			List<com.wipro.fipc.model.TbaCommonReport> responseTbaCommonData = tbaCommonServiceImpl
					.getListOfData(pjmId);
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = ow.writeValueAsString(responseTbaCommonData);
			JsonArray arr = parser.parse(response).getAsJsonArray();
			for (int i = 0; i < arr.size(); i++) {
				TbaCommonReport config = gson.fromJson(arr.get(i).getAsJsonObject(), TbaCommonReport.class);
				objList.add(config);
			}

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllData", LOGGER + exception.getMessage());
			throw new BusinessException("getAllData failed : " + exception.getMessage());
		}
		return objList;

	}

	@Override
	public String deleteRuleConfig(String entity) throws URISyntaxException {
		String response = null;
		HttpHeaders headers = new HttpHeaders();
		headers.set(CONTENT_TYPE, APPLICATION);
		headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));

		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		JsonArray entities = parser.parse(entity).getAsJsonArray();
		String updatedBy = entities.get(0).getAsJsonObject().get("updatedBy").getAsString();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getAsJsonObject().get("id").getAsLong());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getAsJsonObject().get("id").getAsLong(),
					messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getAsJsonObject().get("id").getAsLong(),
					messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(com.wipro.fipc.entity.filelayout.RulesConfig.class, LAYOUT_SCHEMA,
				RULES_CONFIG, ids, updatedBy)) {
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			try {
				response = ow.writeValueAsString(deleteMultipleRowsSuccess);
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			return response;
		} else {
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			try {
				response = ow.writeValueAsString(deleteMultipleRowsFailure);
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			return response;
		}
	}

	@Override
	public List<Object> getByFileName(String fileName, String pjmId) {
		if (fileName.contains("Prev")) {
			fileName = fileName.substring(5);
		}
		String outputReportName = fileName;
		String fname = fileName;
		String id = pjmId;

		try {
			fileName = fileName.replace("+", "PLUS");
			fileName = fileName.replace("&", "AMPNT");
			fileName = fileName.replace("'", "''");
			fileName = URLEncoder.encode(fileName, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new BusinessException("file name conversion failed : " + e.getMessage());
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "getByFileName>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,pjmId= " + pjmId);
		List<String> column_name = new ArrayList<>();
		column_name.add("process_job_mapping_id");
		column_name.add("mf_Field_Name");
		column_name.add("file_name");
		column_name.add("record_type");
		column_name.add("active_flag");

		List<String> column_conditions = new ArrayList<>();
		column_conditions.add("eq");
		column_conditions.add("ntlk");
		column_conditions.add("eq");
		column_conditions.add("eq");
		column_conditions.add("eq");

		List<String> column_value = new ArrayList<>();
		column_value.add(pjmId);
		column_value.add("filler");
		column_value.add(fileName);
		column_value.add("Detail Record");
		column_value.add("T");

		fname = fname.replaceAll("\\s", "%20");
		id = id.replaceAll("\\s", "%20");
		JsonParser parser = new JsonParser();
		List<Object> responseList = new ArrayList<>();
		Gson gson = new Gson();
		try {
			if (HolmesAppConstants.TBA.equals(fileName)) {

				getTBAData(pjmId, parser, responseList, gson);

			} else {
				responseList = getInputOutputExternalData(pjmId, outputReportName, column_name, column_conditions,
						column_value, parser, responseList, gson);

			}
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", LOGGER + exception.getMessage());
			throw new BusinessException("error in getByFileName method : " + exception.getMessage());
		}
		return responseList;

	}

	private List<Object> getInputOutputExternalData(String pjmId, String outputReportName, List<String> column_name,
			List<String> column_conditions, List<String> column_value, JsonParser parser, List<Object> responseList,
			Gson gson) {
		String noticeResponse = null;
		String response = null;
		LoggerUtil.log(getClass(), Level.INFO, "getByFileName", CALLING_DB_SERVICE);
		for (int j = 0; j < column_value.size(); j++) {
			String columnValue = column_value.get(j);
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, "In  LayoutConfig Before Replace: " + columnValue);

			String remove1 = "PLUS";
			String remove2 = "PRCNT";
			String remove3 = "AMPNT";
			if (columnValue.contains(remove1))
				columnValue = columnValue.replaceAll("PLUS", "+");
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

			if (columnValue.contains(remove2))
				columnValue = columnValue.replaceAll("PRCNT", "%");
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

			if (columnValue.contains(remove3))
				columnValue = columnValue.replaceAll("AMPNT", "&");
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

			column_value.set(j, columnValue);
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

		}

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(column_name,
				column_conditions, column_value);
		List<ColumnConditionParam> columnConditionParamList = new ArrayList<>();
		ObjectMapper mapper = new ObjectMapper();
		for (ColumnConditionParam valueColumnConditionParam : columnConditionParams) {
			ColumnConditionParam columnConditionParam = mapper.convertValue(valueColumnConditionParam,
					ColumnConditionParam.class);
			columnConditionParamList.add(columnConditionParam);
		}
		List<com.wipro.fipc.entity.layoutrule.LayoutConfig> responseMultiColumnCondition = genericDaoLayoutConfig
				.findByMultiColumnCondition(com.wipro.fipc.entity.layoutrule.LayoutConfig.class, LAYOUT_SCHEMA,
						LAYOUT_CONFIG, columnConditionParamList);
		try {
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = mapper.writeValueAsString(responseMultiColumnCondition);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		if (!response.equals("[]")) {
			JsonArray arr = parser.parse(response).getAsJsonArray();
			for (int i = 0; i < arr.size(); i++) {
				LayoutConfig config = gson.fromJson(arr.get(i).getAsJsonObject(), LayoutConfig.class);
				responseList.add(doConverter.convertLayoutConfig(config));
			}
		}
		if (responseList.isEmpty()) {
			responseList = getoutputReport(pjmId, outputReportName);
		}
		if (responseList.isEmpty()) {
			List<com.wipro.fipc.entity.TemplateReportLayOut> responseNotice = templateReportLayOutDao
					.getListOfData(pjmId, outputReportName);
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			try {
				noticeResponse = mapper.writeValueAsString(responseNotice);
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			JsonArray arr = parser.parse(noticeResponse).getAsJsonArray();
			for (int i = 0; i < arr.size(); i++) {
				TemplateReportLayOut config = gson.fromJson(arr.get(i).getAsJsonObject(), TemplateReportLayOut.class);
				responseList.add(doConverter.convertExternalReport(config));
			}
		}
		return responseList;
	}

	private void getTBAData(String pjmId, JsonParser parser, List<Object> responseList, Gson gson) {
		String res = null;
		String noticeResponse = null;
		String commentInq = null;
		String tbaUpdateConfig = null;
		String response = null;
		String pendingEvent = null;
		LoggerUtil.log(getClass(), Level.INFO, "getByFileName", "Calling DB service for getting file name : ");
		List<com.wipro.fipc.entity.tba.EventInquiryConfig> responseTbaEventInquiry = genericDaoTbaEventInquiry.findByColumn(
				com.wipro.fipc.entity.tba.EventInquiryConfig.class, SCHEMA, EVENT_INQUIRY_CONFIG, "process_job_mapping_id",
				pjmId);
		ObjectMapper owEI = new ObjectMapper();
		owEI.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			response = owEI.writeValueAsString(responseTbaEventInquiry);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTBAData not getting responseTbaEventInquiry", "Exception : " + e.getMessage());
		}
		JsonArray arrEventInquiry = parser.parse(response).getAsJsonArray();
		for (int i = 0; i < arrEventInquiry.size(); i++) {
			EventInquiryConfig config = gson.fromJson(arrEventInquiry.get(i).getAsJsonObject(), EventInquiryConfig.class);
			responseList.add(doConverter.converEventInquiryConfig(config));
		}
		
		List<com.wipro.fipc.entity.tba.TbaInquiryConfig> responseTbaInquiry = genericDaoTbaInquiry.findByColumn(
				com.wipro.fipc.entity.tba.TbaInquiryConfig.class, SCHEMA, TBA_INQUIRY_CONFIG, "process_job_mapping_id",
				pjmId);
		ObjectMapper ow = new ObjectMapper();
		ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			response = ow.writeValueAsString(responseTbaInquiry);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray arr = parser.parse(response).getAsJsonArray();
		for (int i = 0; i < arr.size(); i++) {
			TbaInquiryConfig config = gson.fromJson(arr.get(i).getAsJsonObject(), TbaInquiryConfig.class);
			responseList.add(doConverter.converTbaInquiryConfig(config));
		}
		List<com.wipro.fipc.entity.tba.EventHistInqConfig> responseTbaEventHistory = genericDaoTbaEventHistory
				.findByColumn(com.wipro.fipc.entity.tba.EventHistInqConfig.class, SCHEMA, EVENT_HIST_INQ_CONFIG,
						"process_job_mapping_id", pjmId);
		ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			res = ow.writeValueAsString(responseTbaEventHistory);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray array = parser.parse(res).getAsJsonArray();
		for (int i = 0; i < array.size(); i++) {
			EventHistInqConfig config = gson.fromJson(array.get(i).getAsJsonObject(), EventHistInqConfig.class);
			responseList.add(doConverter.convertHistoryInquiry(config));
		}

		List<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> responseTbaNoticeInq = genericDaoTbaNoticeInquiry
				.findByColumn(com.wipro.fipc.entity.tba.TbaNoticeInqConfig.class, SCHEMA, TBA_NOTICE_INQ_CONFIG,
						"process_job_mapping_id", pjmId);
		ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			noticeResponse = ow.writeValueAsString(responseTbaNoticeInq);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray arrNotice = parser.parse(noticeResponse).getAsJsonArray();
		for (int i = 0; i < arrNotice.size(); i++) {
			TbaNoticeInqConfig config = gson.fromJson(arrNotice.get(i).getAsJsonObject(), TbaNoticeInqConfig.class);
			responseList.add(doConverter.convertNoticeInquiry(config));
		}
		List<com.wipro.fipc.entity.tba.TbaCommentInqConfig> responseTbaCommentInq = genericDaoTbaCommentInquiry
				.findByColumn(com.wipro.fipc.entity.tba.TbaCommentInqConfig.class, SCHEMA, TBA_COMMENT_INQ_CONFIG,
						"process_job_mapping_id", pjmId);
		ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			commentInq = ow.writeValueAsString(responseTbaCommentInq);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray arrComment = parser.parse(commentInq).getAsJsonArray();
		for (int i = 0; i < arrComment.size(); i++) {
			TbaCommentInqConfig config = gson.fromJson(arrComment.get(i).getAsJsonObject(), TbaCommentInqConfig.class);
			responseList.add(doConverter.convertCommentInquiry(config));
		}
		List<com.wipro.fipc.entity.tba.TbaPendingEvent> responseTbaPendingEvent = genericDaoTbaPendingEvent
				.findByColumn(com.wipro.fipc.entity.tba.TbaPendingEvent.class, SCHEMA, "PENDING_EVENT_INQ_CONFIG",
						"process_job_mapping_id", pjmId);
		ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			pendingEvent = ow.writeValueAsString(responseTbaPendingEvent);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray arrPending = parser.parse(pendingEvent).getAsJsonArray();
		for (int i = 0; i < arrPending.size(); i++) {
			TbaPendingEvent config = gson.fromJson(arrPending.get(i).getAsJsonObject(), TbaPendingEvent.class);
			responseList.add(doConverter.convertPendingEvent(config));
		}
		List<com.wipro.fipc.entity.tba.TbaUpdateConfig> responseTbaUpdateConfig = genericDaoTbaUpdateConfig
				.findByColumn(com.wipro.fipc.entity.tba.TbaUpdateConfig.class, SCHEMA, "tba_update_config",
						"process_job_mapping_id", pjmId);
		ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			tbaUpdateConfig = ow.writeValueAsString(responseTbaUpdateConfig);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray arrUpdate = parser.parse(tbaUpdateConfig).getAsJsonArray();
		for (int i = 0; i < arrUpdate.size(); i++) {
			TbaUpdateConfig config = gson.fromJson(arrUpdate.get(i).getAsJsonObject(), TbaUpdateConfig.class);
			responseList.add(doConverter.convertUpdateConfig(config));
		}
	}

	private List<Object> getoutputReport(String pjmId, String fileName) {
		try {
			fileName = fileName.replace("'", "''");
			fileName = URLEncoder.encode(fileName, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new BusinessException("getByFileName:  file name is not proper : ", e.getCause());
		}

		List<String> column_name = new ArrayList<>();
		column_name.add("process_job_mapping_id");
		column_name.add("active_flag");
		column_name.add("file_name");

		List<String> column_conditions = new ArrayList<>();
		column_conditions.add("eq");
		column_conditions.add("eq");
		column_conditions.add("eq");

		List<String> column_value = new ArrayList<>();
		column_value.add(pjmId);
		column_value.add("T");
		column_value.add(fileName);

		String response = null;
		JsonParser parser = new JsonParser();
		List<Object> responseList = new ArrayList<>();
		String sheetName = null;
		String sheetNameWoutSpace = null;
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(column_name,
				column_conditions, column_value);
		List<ColumnConditionParam> columnConditionParamList = new ArrayList<>();
		ObjectMapper mapper = new ObjectMapper();

		for (ColumnConditionParam valueColumnConditionParam : columnConditionParams) {
			ColumnConditionParam columnConditionParam = mapper.convertValue(valueColumnConditionParam,
					ColumnConditionParam.class);
			columnConditionParamList.add(columnConditionParam);
		}

		List<KsdOutPutFileDetails> responseKsdFileOutputDetails = genericDaoKsdOutputFileDetails
				.findByMultiColumnCondition(KsdOutPutFileDetails.class, OUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS,
						columnConditionParamList);
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			response = mapper.writeValueAsString(responseKsdFileOutputDetails);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		JsonArray ksdRes = parser.parse(response).getAsJsonArray();

		for (int i = 0; i < ksdRes.size(); i++) {
			JsonObject obj = ksdRes.get(i).getAsJsonObject();
			if (obj.has(SHEET_NAME)) {
				sheetName = getSheetName(obj);
			} else {
				sheetName = "";
			}
			if (obj.has(SHEET_NAME_WOUT_SPACE)) {
				sheetNameWoutSpace = getSheetNamWoutSpace(obj);
			} else {
				sheetNameWoutSpace = "";
			}
			if (obj.has("outputReports")) {

				JsonArray reportList = obj.get("outputReports").getAsJsonArray();
				for (int j = 0; j < reportList.size(); j++) {
					JsonObject output = reportList.get(j).getAsJsonObject();
					responseList.add(doConverter.convertOutputReport(output, sheetName, sheetNameWoutSpace));
				}
			}
		}
		return responseList;
	}

	private String getSheetNamWoutSpace(JsonObject obj) {
		String sheetNameWoutSpace;
		if (!obj.get(SHEET_NAME_WOUT_SPACE).isJsonNull()) {
			sheetNameWoutSpace = obj.get(SHEET_NAME_WOUT_SPACE).getAsString() == null ? ""
					: obj.get(SHEET_NAME_WOUT_SPACE).getAsString();
		} else {
			sheetNameWoutSpace = "";
		}
		return sheetNameWoutSpace;
	}

	private String getSheetName(JsonObject obj) {
		String sheetName;
		if (!obj.get(SHEET_NAME).isJsonNull()) {
			sheetName = obj.get(SHEET_NAME).getAsString() == null ? "" : obj.get(SHEET_NAME).getAsString();
		} else {
			sheetName = "";
		}
		return sheetName;
	}

	@Override
	public RulesRequest createBusinessRulesData(RulesRequest rulesRequest, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		rulesRequest.setCreatedBy(adid);
		rulesRequest.setUpdatedBy(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "createBusinessRules() -> RULES ",
				PJM_ID + rulesRequest.getProcessJobMappingId() + RULE_NAME + rulesRequest.getRuleName() + ADID2 + adid
						+ " action = CREATE");
		return createBusinessRules(rulesRequest);
	}

	@Override
	public RulesRequest updateBusinessRulesByRuleIdData(RulesRequest rulesRequest, String pjmId, String ruleId,
			String appName, String sessionToken) {
		
		RulesRequest response = null;
		List<RulesRequest> rulesRequestJson = new ArrayList<>();
		List<RulesConfig> rulesConfigJson = new ArrayList<>();

		try {
			LoggerUtil.log(getClass(), Level.INFO, UPDATE_LOGGER, INFO_LOGGER);
			List<com.wipro.fipc.entity.filelayout.RulesConfig> ruleConfigData = genericDao.findByColumn(
					com.wipro.fipc.entity.filelayout.RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, "id", ruleId);
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			 List<com.wipro.fipc.entity.common.RulesDefinition> rulesDefinitions2;
		        for(com.wipro.fipc.entity.filelayout.RulesConfig entity : ruleConfigData) {
		        	RulesConfig configJson =new RulesConfig();
		        	ProcessJobMapping processJobMapping=new ProcessJobMapping();
		        	processJobMapping.setId(entity.getProcessJobMapping().getId());
		        	com.wipro.fipc.model.generated.RulesDefinition rd = new com.wipro.fipc.model.generated.RulesDefinition();
		        	 rulesDefinitions2 =new ArrayList<>();
		        	 rulesDefinitions2=entity.getRulesDefinitions();
		        	 List<com.wipro.fipc.model.generated.RulesDefinition> rulesDefinitions23 = new ArrayList<>();
		        	 Long validationid;
		        	  for(com.wipro.fipc.entity.common.RulesDefinition entity2 : rulesDefinitions2) {
		        		 validationid=entity2.getValidationType().getId();
		        			BeanUtils.copyProperties(entity2, rd);
		        			ValidationType validationType=new ValidationType();
				        	validationType.setId(validationid);
		        			rd.setValidationType(validationType);
		        			rulesDefinitions23.add(rd);
		        	  }
		        	
		        	BeanUtils.copyProperties(entity, configJson);
		        	
		      
		        	configJson.setProcessJobMapping(processJobMapping);
		        	configJson.setRulesDefinitions(rulesDefinitions23);
		        	rulesConfigJson.add(configJson);
		        }
		        
			rulesRequestJson = doConverter.convertBusinessDOToJson(rulesConfigJson);		
			String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		
			LoggerUtil.log(this.getClass(), Level.INFO, "updateByRuleId() -> RULES ",
					PJM_ID + rulesRequest.getProcessJobMappingId() + RULE_NAME + rulesRequest.getRuleName() + ADID2
							+ adid + " action = UPDATE" + " Rule_Id =" + rulesRequest.getRuleId());

			RulesConfig rulesConfigDO = doConverter.updateByRuleIdConvertorNew(rulesRequest, rulesConfigJson.get(0), ruleId, adid);

			ObjectMapper mapper = new ObjectMapper();
			com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
					com.wipro.fipc.entity.filelayout.RulesConfig.class);

			com.wipro.fipc.entity.filelayout.RulesConfig updateDataResponse = genericService
					.update(Long.valueOf(ruleId), rulesConfig);
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			response = doConverter.convertCreateResponse(updateDataResponse);
		} catch (Exception exception) {
			exception.printStackTrace();
			LoggerUtil.log(getClass(), Level.ERROR, UPDATE_LOGGER, LOGGER + exception.getMessage());
			throw new BusinessException(ERROR_LOGGER + exception.getMessage());
		}

		return response;
	}
	
	@Override
	public String deleteRuleLatest(String ruleName, String pjmId, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteRuleLatest() -> RULES ",
				PJM_ID + pjmId + RULE_NAME + ruleName + ADID2 + adid + " action = DELETE");

		String response = null;
		try {
			List<TbaMatchConfig> tbaMatch = tbaMatchConfigRepo.getrulesdeleteTba(Long.valueOf(pjmId), ruleName);
			if (tbaMatch.size() > 0) {
				tbaMatchConfigRepo.updateByRulesTbaMatchConfigs(Long.valueOf(pjmId), ruleName, adid);
			}

			List<ProcessControlConfig> processControlConfigs = processControlConfigRepo
					.getRulesdeleteByPControl(Long.valueOf(pjmId), ruleName);
			if (processControlConfigs.size() > 0) {
				processControlConfigRepo.updateByRulesPControlConfigs(Long.valueOf(pjmId), ruleName, adid);
			}

			List<RulesDefinition> rulesDefinitions = rulesDefinitionRepo
					.getRulesdeletionRuleDifinition(Long.valueOf(pjmId), ruleName);
			if (rulesDefinitions.size() > 0) {
				rulesDefinitionRepo.updateRulesDetailsByRulesDififnitions(Long.valueOf(pjmId), ruleName, adid);
				rulesConfigRepo.rulesdelete(ruleName, Long.valueOf(pjmId), adid);
			}
			response = String.valueOf(true);
		} catch (Exception exception) {
			response = String.valueOf(false);
			LoggerUtil.log(getClass(), Level.ERROR, "deleteRuleLatest",
					"rule deletion failed : " + exception.getMessage());
			throw new BusinessException("rule deletion failed : " + exception.getMessage());
		}
		return response;
	}

}