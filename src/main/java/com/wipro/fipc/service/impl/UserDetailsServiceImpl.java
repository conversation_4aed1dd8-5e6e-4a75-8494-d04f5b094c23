package com.wipro.fipc.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BusinessUnitDao;
import com.wipro.fipc.dao.common.RoleConfigDao;
import com.wipro.fipc.dao.common.UserDetailsDao;
import com.wipro.fipc.entity.common.UserDetails;
import com.wipro.fipc.pojo.User;
import com.wipro.fipc.service.IUserDetailsService;

@Service
public class UserDetailsServiceImpl implements IUserDetailsService {

	@Autowired
	UserDetailsDao userDetailsDao;

	@Autowired
	BusinessUnitDao buDao;

	@Autowired
	RoleConfigDao roleDao;

	@PersistenceContext
	EntityManager entityManager;

	private String status = "failure";

	@Override
	@Transactional
	public String updateUserRoleConfig(String updatedBy, List<String> adid) {

		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserRoleConfig", "update Role Config to false started on : "+System.currentTimeMillis());

		if (userDetailsDao.existsByAdidInAndActiveFlag(adid, 'T')) {
			userDetailsDao.updateData(updatedBy, adid);
			userDetailsDao.updateManger(updatedBy, adid);
			LoggerUtil.log(this.getClass(), Level.INFO, "updateUserRoleConfig",
					"getAllReportees  ended with : " + System.currentTimeMillis());
			return "RoleConfig updated";
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, "updateUserRoleConfig",
					"updateUserRoleConfig  ended with : " + System.currentTimeMillis());
			return "RoleConfig up to date";
		}

	}

	public List<String> getAllReportees(String adid, String role) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllReportees", "reportees for manager adid " + adid+" stared on : "+System.currentTimeMillis());
		List<String> adidList = new ArrayList<>();
		adidList.add(adid);
		if (null != role && role.equalsIgnoreCase("Manager")) {
			adidList = userDetailsDao.getAllReporteeAdid(adid);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllReportees",
				"getAllReportees  ended with : " + System.currentTimeMillis());
		return adidList;
	}

	public String createUserByAdmin(User user) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createUserByAdmin", "create User By Admin stared on : "+System.currentTimeMillis());
		UserDetails userdetails = new UserDetails();
		BeanUtils.copyProperties(user, userdetails, "id", "updatedDate");
		userdetails.setActiveFlag('T');
		userDetailsDao.save(userdetails);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserByAdmin",
				"updateUserByAdmin ended with : " + System.currentTimeMillis());
		return "Success";
	}

	public String updateUserByAdmin(User user) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserByAdmin",
				"updateUserByAdmin started on : " + System.currentTimeMillis());
		UserDetails userdetails = userDetailsDao.findById(user.getId()).get();
		if (!userdetails.getAdid().isEmpty()) {
			userdetails.setAdid(user.getAdid());
			userdetails.setRole(user.getRole());
			userdetails.setType(user.getRole());
			userdetails.setUpdatedBy(user.getUpdatedBy());
			userDetailsDao.save(userdetails);
			return "Sucess";
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserByAdmin",
				"updateUserByAdmin ended with : " + System.currentTimeMillis());
		return "Failure";
	}

	@Transactional
	public String deleteUserByAdmin(User user) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteUserByAdmin",
				"deleteUserByAdmin started on : " + System.currentTimeMillis());
		UserDetails userdetails = userDetailsDao.findById(user.getId()).get();
		if (!userdetails.getAdid().isEmpty()) {
			userdetails.setActiveFlag('F');
			userdetails.setUpdatedBy(user.getUpdatedBy());
			userDetailsDao.save(userdetails);
			userDetailsDao.updateManger(user.getUpdatedBy(), Arrays.asList(user.getAdid()));
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteUserByAdmin",
					"deleteUserByAdmin ended with : " + System.currentTimeMillis());
			return "Sucess";
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteUserByAdmin",
				"deleteUserByAdmin ended with : " + System.currentTimeMillis());
		return "Failure";
	}

	public List<User> getUserDetailsByAdmin(String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getUserDetailsByAdmin",
				"getUserDetailsByAdmin started on : " + System.currentTimeMillis());
	 List<User> users = userDetailsDao.findByCreatedByAndActiveFlag(adid);
	 LoggerUtil.log(this.getClass(), Level.INFO, "getUserDetailsByAdmin",
				"getUserDetailsByAdmin return the Users size of : " +users.size());
		LoggerUtil.log(this.getClass(), Level.INFO, "getUserDetailsByAdmin",
				"getUserDetailsByAdmin ended with : " + System.currentTimeMillis());
		return users;
	}

	public String updateUserSession(User user) {
		LoggerUtil.log(this.getClass(), Level.INFO, "UpdateUserSession",
				"UpdateUserSession started on : " + System.currentTimeMillis());
		this.status = "failure";
		userDetailsDao.findByAdid(user.getAdid()).forEach(userData -> {
			userData.setSessionStatus(user.getSessionStatus());
			userData.setSessionDate(user.getSessionDate());
			userDetailsDao.save(userData);
			this.status = "Success";
		});
		LoggerUtil.log(this.getClass(), Level.INFO, "updatedUserSession",
				"updatedUserSession ended with : " + System.currentTimeMillis());
		return status;
	}

}
