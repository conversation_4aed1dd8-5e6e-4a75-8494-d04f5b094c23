package com.wipro.fipc.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.KsdConfigDao;
import com.wipro.fipc.dao.KsdFileDetailsDao;
import com.wipro.fipc.dao.batch.ClientConfigJobsDao;
import com.wipro.fipc.dao.batch.JobScheduleDao;
import com.wipro.fipc.dao.batch.NotificationMailConfigDao;
import com.wipro.fipc.dao.batch.NotificationReportConfigDao;
import com.wipro.fipc.dao.filelayout.KsdOutPutFileDetailsDao;
import com.wipro.fipc.dao.filelayout.OutputReportDao;
import com.wipro.fipc.dao.filelayout.ParticipantRecordIdentifierDao;
import com.wipro.fipc.dao.filelayout.ProcessControlConfigDao;
import com.wipro.fipc.dao.filelayout.ProcessFeatureConfigDao;
import com.wipro.fipc.dao.filelayout.ReportDataCleanseDao;
import com.wipro.fipc.dao.filelayout.ReportFormatDao;
import com.wipro.fipc.dao.filelayout.ReportPivotDao;
import com.wipro.fipc.dao.filelayout.RulesConfigDao;
import com.wipro.fipc.dao.filelayout.RulesDefinitionDao;
import com.wipro.fipc.dao.filelayout.TaskUpdateConfigDao;
import com.wipro.fipc.dao.filelayout.TbaMatchConfigDao;
import com.wipro.fipc.dao.jira.JiraTaskUpdateConfigDao;
import com.wipro.fipc.dao.jira.JiraTicketCreationConfigDao;
import com.wipro.fipc.dao.layoutrule.LayoutConfigDao;
import com.wipro.fipc.dao.maestro.TicketCreationConfigDao;
import com.wipro.fipc.dao.tba.EventHistInqConfigDao;
import com.wipro.fipc.dao.tba.NoticeServiceDao;
import com.wipro.fipc.dao.tba.PendingEventDao;
import com.wipro.fipc.dao.tba.TbaCommentInqConfigDao;
import com.wipro.fipc.dao.tba.TbaEventInquiryConfigDao;
import com.wipro.fipc.dao.tba.TbaInquiryConfigDao;
import com.wipro.fipc.dao.tba.TbaUpdateConfigDao;
import com.wipro.fipc.entity.ProcessFeatureConfigCopyJob;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.filelayout.OutputReport;
import com.wipro.fipc.entity.filelayout.ParticipantRecordIdentifier;
import com.wipro.fipc.entity.filelayout.ProcessFeatureConfig;
import com.wipro.fipc.entity.filelayout.ReportDataCleanse;
import com.wipro.fipc.entity.filelayout.ReportFormat;
import com.wipro.fipc.entity.filelayout.ReportPivot;
import com.wipro.fipc.entity.filelayout.RulesConfig;
import com.wipro.fipc.exception.processconfiguration.DBServiceException;
import com.wipro.fipc.exception.processconfiguration.InvalidInputException;
import com.wipro.fipc.exception.processconfiguration.ResourceNotFoundException;
import com.wipro.fipc.model.CustomProcessConfigurationUI;
import com.wipro.fipc.model.ModelApiResponse;
import com.wipro.fipc.model.ProcessConfiguration;
import com.wipro.fipc.pojo.CustomPJMDto;
import com.wipro.fipc.service.AllBotsConfigService;
import com.wipro.fipc.service.ProcessConfigurationService;

@Service
public class AllBotsConfigServiceImpl implements AllBotsConfigService {

	@Autowired
	JobScheduleDao jobScheduleDao;

	@Autowired
	ClientConfigJobsDao clientConfigJobsDao;

	@Autowired
	LayoutConfigDao layoutConfigDao;

	@Autowired
	ProcessFeatureConfigDao processFeatureConfigDao1;

	@Autowired
	TbaInquiryConfigDao tbaInquiryConfigDao;

	@Autowired
	TbaUpdateConfigDao tbaUpdateConfigDao;

	@Autowired
	TaskUpdateConfigDao taskUpdateConfigDao;

	@Autowired
	NotificationReportConfigDao notificationReportConfigDao;

	@Autowired
	ProcessControlConfigDao processControlConfigDao;

	@Autowired
	TbaMatchConfigDao tbaMatchConfigDao;

	@Autowired
	RulesDefinitionDao rulesDefinitionDao1;

	@Autowired
	RulesConfigDao rulesConfigDao1;

	@Autowired
	TicketCreationConfigDao ticketCreationConfigDao;

	@Autowired
	KsdFileDetailsDao ksdFileDetailsDao;

	@Autowired
	KsdConfigDao ksdConfigDao;

	@Autowired
	NotificationMailConfigDao notificationMailConfigDao;

	@Autowired
	KsdOutPutFileDetailsDao ksdOutPutFileDetailsDao;

	@Autowired
	OutputReportDao outputReportDao;

	@Autowired
	ReportDataCleanseDao reportDataCleanseDao;

	@Autowired
	ReportFormatDao reportFormatDao;

	@Autowired
	ReportPivotDao reportPivotDao;

	@Autowired
	EventHistInqConfigDao eventHistInqConfigDao;

	@Autowired
	PendingEventDao tbaPendingEventDao;

	@Autowired
	TbaCommentInqConfigDao tbaCommentInqConfigDao;

	@Autowired
	NoticeServiceDao tbaNoticeInqConfigDao;

	@Autowired
	ParticipantRecordIdentifierDao participantRecordIdentifierDao;

	@Autowired
	Environment env;

	@Autowired
	private BaseDao<RulesConfig> ruleBaseDao;

	@Autowired
	ProcessFeatureConfigDao processFeatureConfigDao;

	@Autowired
	ProcessConfigurationService processConfigService;

	@Autowired
	EntityManager entityManager;

	@Autowired
	private RulesConfigDao rulesConfigDao;
	@Autowired
	private RulesDefinitionDao rulesDefinitionDao;

	@Autowired
	private GenericDao<RulesConfig> genericDao;

	@Autowired
	private GenericDao<ProcessFeatureConfig> ProcessConfigGenericDao;

	@Autowired
	private GenericDao<KsdOutPutFileDetails> genericKsdOutputDao;

	@Autowired
	private BaseDao<KsdOutPutFileDetails> baseKsdOutputDao;

	@Autowired
	Gson gson;
	
	@Autowired
	private JiraTaskUpdateConfigDao jiraTaskUpdateDao;
	
	@Autowired
	private JiraTicketCreationConfigDao jiraTicketCreationDao;
	
	@Autowired
	TbaEventInquiryConfigDao tbaEventInquiryConfigDao;
	
	protected static final String PROCESS_FEATURE_CONFIG = "PROCESS_FEATURE_CONFIG";
	protected static final String COMMON_SCHEMA = "common";
	protected static final String KSD_OUTPUT_FILE_DETAILS = "KSD_OUTPUT_FILE_DETAILS";
	protected static final String RULES_CONFIG = "RULES_CONFIG";
	protected static final String LAYOUT_SCHEMA = "layout_rule";
	protected static final String COPY_JOB_PFC = "copyJobPFC";
	public static final String COLUMN_NAME = "process_job_mapping_id";
	public static final String KSD_MASTER_COLUMN_NAME = "primary_job_name";
	public static final String DELETE_ALL_CONFIGS = "deleteAllConfigs";
	public static final String COPY_JOB_TO_ALL_CONFIG_DETAILS = "copyJobToAllConfigsDetails";
	public static final String GET_AND_INSERT_PROCESS_FETAURE_CONFIG = "getAndInsertProcessFeatureConfig";
	public static final String METHOD_NAME = "methodName";
	public static final String CHECK_DUPLICATE_RECORD_IN_PFC = "checkDuplicateRecordsInPFC";
	public static final String GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS = "getAndInsertKsdOutputFileDetails";
	public static final String GET_KSD_OUTPUT_FILE_DETAILS = "getKsdOutputFileDetails";
	public static final String INSERT_KSD_OUTPUT_FILE_DETAILS = "insertKsdOutPutFileDetails";
	public static final String GET_AND_INSERT_RULES_CONFIG = "getAndInsertRulesConfig";
	public static final String GET_RULES_CONFIG_DETAILS = "getRulesConfigDetails";

	@Override
	public ModelApiResponse deleteAllConfigs(String updatedBy, String inputPJMID) throws NumberFormatException {
		LoggerUtil.log(this.getClass(), Level.INFO, DELETE_ALL_CONFIGS,
				"Deleting all configs for processJobMappingID and adid: " + inputPJMID + ", " + updatedBy);

		Long processJobMappingId;
		if (inputPJMID == null || inputPJMID.isEmpty() || updatedBy == null || updatedBy.isEmpty())
			throw new InvalidInputException(
					"Invalid Input processJobMappingID or adid: " + inputPJMID + ", " + updatedBy);

		processJobMappingId = Long.parseLong(inputPJMID);
		tbaEventInquiryConfigDao.commonupdate(updatedBy, processJobMappingId);
		tbaInquiryConfigDao.commonupdate(updatedBy, processJobMappingId);
		processFeatureConfigDao.commonupdate(updatedBy, processJobMappingId);
		tbaUpdateConfigDao.commonupdate(updatedBy, processJobMappingId);
		jobScheduleDao.commonupdate(updatedBy, processJobMappingId);
		layoutConfigDao.commonupdate(updatedBy, processJobMappingId);
		clientConfigJobsDao.commonupdate(updatedBy, processJobMappingId);
		notificationMailConfigDao.commonupdate(updatedBy, processJobMappingId);
		ksdConfigDao.commonupdate(updatedBy, processJobMappingId);
		ksdFileDetailsDao.commonupdate(updatedBy, processJobMappingId);
		ticketCreationConfigDao.commonupdate(updatedBy, processJobMappingId);
		rulesConfigDao.commonupdate(updatedBy, processJobMappingId);
		rulesDefinitionDao.commonupdate(updatedBy, processJobMappingId);
		tbaMatchConfigDao.commonupdate(updatedBy, processJobMappingId);
		processControlConfigDao.commonupdate(updatedBy, processJobMappingId);
		notificationReportConfigDao.commonupdate(updatedBy, processJobMappingId);
		taskUpdateConfigDao.commonupdate(updatedBy, processJobMappingId);
		eventHistInqConfigDao.commonupdate(updatedBy, processJobMappingId);
		tbaPendingEventDao.commonupdate(updatedBy, processJobMappingId);
		tbaCommentInqConfigDao.commonupdate(updatedBy, processJobMappingId);
		tbaNoticeInqConfigDao.commonupdate(updatedBy, processJobMappingId);
		ksdOutPutFileDetailsDao.commonupdate(updatedBy, processJobMappingId);
		outputReportDao.commonupdate(updatedBy, processJobMappingId);
		participantRecordIdentifierDao.commonupdate(updatedBy, processJobMappingId);
		reportDataCleanseDao.commonupdate(updatedBy, processJobMappingId);
		reportFormatDao.commonupdate(updatedBy, processJobMappingId);
		reportPivotDao.commonupdate(updatedBy, processJobMappingId);
		jiraTicketCreationDao.commonupdate(processJobMappingId);
		jiraTaskUpdateDao.commonupdate(processJobMappingId);
		return new ModelApiResponse("SUCCESS", "All records are  marked as deleted successfully");
	}

	// ----------------------CopyJob
	// Functionality----------------------------------------------------
	@Transactional
	@Override

	public ProcessConfiguration copyJobToAllConfigsDetails(CustomProcessConfigurationUI processConfigurationUI)
			throws IllegalAccessException, InvocationTargetException {
		Long startedTime = System.currentTimeMillis();
		LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_TO_ALL_CONFIG_DETAILS,
				"copyJobToAllConfigsDetails started: " + startedTime);

		CustomPJMDto customPJMDtoCopied = processConfigService
				.getProcessJobMappingById(processConfigurationUI.getPjmIdCopied());

		processConfigurationUI.setEftSubjectCopied(customPJMDtoCopied.getEftSubject());
		processConfigurationUI.setJobCopied(customPJMDtoCopied.getJobName());

		CustomPJMDto customPJMDto = processConfigService
				.getProcessJobMappingById(processConfigurationUI.getProcessJobMappingId());
		if (customPJMDto != null) {
			processConfigurationUI.setBusinessOpsName(customPJMDto.getBusinessOpsName());
			processConfigurationUI.setBusinessUnitName(customPJMDto.getBusinessUnitName());
			processConfigurationUI.setClientCode(customPJMDto.getClientCode());
			processConfigurationUI.setClientName(customPJMDto.getClientName());
			processConfigurationUI.setEftSubject(customPJMDto.getEftSubject());
			processConfigurationUI.setJobName(customPJMDto.getJobName());
			processConfigurationUI.setKsdName(customPJMDto.getKsdName());
			processConfigurationUI.setProcessJobMappingId(customPJMDto.getProcessJobMappingId());
			processConfigurationUI.setProcessName(customPJMDto.getProcessName());
			processConfigurationUI.setProcessType(customPJMDto.getProcessType());

		}
		ProcessConfiguration processConfiguration = new ProcessConfiguration();
		BeanUtils.copyProperties(processConfiguration, processConfigurationUI);
		Long newPjmId = processConfigurationUI.getProcessJobMappingId();
		Integer duplicateCheck = checkDuplicateRecordsInPFC(newPjmId);

		if (duplicateCheck == 1) {
			processConfiguration.setMessage("Duplicate");
			processConfiguration.setUpdatedBy(processConfiguration.getCreatedBy());
			processConfiguration.setUpdatedDate(new Date());
			return processConfiguration;

		}

		ProcessFeatureConfigCopyJob processFeatureConfig = new ProcessFeatureConfigCopyJob();
		BeanUtils.copyProperties(processFeatureConfig, processConfiguration);
		ProcessJobMapping pfcId = new ProcessJobMapping();
		pfcId.setId(newPjmId);
		processFeatureConfig.setProcessJobMapping(pfcId);
		processFeatureConfig.setPjmIdCopied(processConfigurationUI.getPjmIdCopied());
		processFeatureConfig.setJobCopied(processConfigurationUI.getJobCopied());
		processFeatureConfig.setEftSubjectCopied(processConfigurationUI.getEftSubjectCopied());
		boolean copyResponse = genericDao.copyJobPFC(processFeatureConfig);

		if (Boolean.TRUE.equals(copyResponse)) {
			getAndInsertRulesConfig(processConfigurationUI);
			getAndInsertKsdOutputFileDetails(processConfigurationUI);
			List<ProcessFeatureConfig> processFeatureConfigs = getProcessConfigDetails(COLUMN_NAME, newPjmId);
			if ((null != (processFeatureConfigs) && !processFeatureConfigs.isEmpty())) {
				processConfiguration.setId(processFeatureConfigs.get(0).getId());
				processConfiguration.setPhaseNames(processFeatureConfigs.get(0).getPhaseNames());
				processConfiguration.setUpdatedBy(processConfiguration.getCreatedBy());
				processConfiguration.setUpdatedDate(processFeatureConfigs.get(0).getCreatedDate());
			}

			processConfiguration.setConfigStatus("In-progress");
			processConfiguration.setMessage("Success");

			LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME,
					"copyJobToAllConfigsDetails for CustomProcessConfigurationUI is copied successfully");

		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME,
					"copyJobToAllConfigsDetails for CustomProcessConfigurationUI is not copied");

		}
		LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_TO_ALL_CONFIG_DETAILS,
				"Total time taken to get copied in ms:" + (System.currentTimeMillis() - startedTime));
		return processConfiguration;
	}

	int checkDuplicateRecordsInPFC(long pjmID) {
		LoggerUtil.log(this.getClass(), Level.INFO, CHECK_DUPLICATE_RECORD_IN_PFC,
				"checkDuplicateRecordsInPFC method ");
		int result = -1;
		result = processFeatureConfigDao.checkForDuplicates(pjmID);
		LoggerUtil.log(this.getClass(), Level.INFO, CHECK_DUPLICATE_RECORD_IN_PFC,
				"checkDuplicateRecordsInPFC GET call response value : {}", result);
		return result;
	}

	public void getOutputReports(KsdOutPutFileDetails ksdOutPutFileDetailsdb,
			CustomProcessConfigurationUI processConfigurationUI, ProcessJobMapping pfcId) {
		List<OutputReport> outputReportList = ksdOutPutFileDetailsdb.getOutputReports();
		if (!outputReportList.isEmpty()) {
			for (OutputReport outputReport : outputReportList) {
				outputReport.setId(null);
				outputReport.setCreatedDate(new Date());
				outputReport.setUpdatedDate(null);
				outputReport.setCreatedBy(processConfigurationUI.getCreatedBy());
				outputReport.setUpdatedBy(null);
				outputReport.setProcessJobMapping(pfcId);
			}
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
					"To copy, outputReport data of required file_detail_id is not present"
							+ ksdOutPutFileDetailsdb.getId());
		}

	}

	public void getReportFormat(List<ReportFormat> reportFormatList,
			CustomProcessConfigurationUI processConfigurationUI, ProcessJobMapping pfcId) {
		for (ReportFormat reportFormat : reportFormatList) {
			reportFormat.setId(null);
			reportFormat.setCreatedDate(new Date());
			reportFormat.setUpdatedDate(null);
			reportFormat.setCreatedBy(processConfigurationUI.getCreatedBy());
			reportFormat.setUpdatedBy(null);
			reportFormat.setProcessJobMapping(pfcId);
		}
	}

	public void getReportDataCleanse(List<ReportDataCleanse> reportDataCleanseList,
			CustomProcessConfigurationUI processConfigurationUI, ProcessJobMapping pfcId) {
		for (ReportDataCleanse reportDataCleanse : reportDataCleanseList) {
			reportDataCleanse.setId(null);
			reportDataCleanse.setCreatedDate(new Date());
			reportDataCleanse.setUpdatedDate(null);
			reportDataCleanse.setCreatedBy(processConfigurationUI.getCreatedBy());
			reportDataCleanse.setUpdatedBy(null);
			reportDataCleanse.setProcessJobMapping(pfcId);
		}
	}

	public void getReportPivot(List<ReportPivot> reportPivotList, CustomProcessConfigurationUI processConfigurationUI,
			ProcessJobMapping pfcId) {
		for (ReportPivot reportPivot : reportPivotList) {
			reportPivot.setId(null);
			reportPivot.setCreatedDate(new Date());
			reportPivot.setUpdatedDate(null);
			reportPivot.setCreatedBy(processConfigurationUI.getCreatedBy());
			reportPivot.setUpdatedBy(null);
			reportPivot.setProcessJobMapping(pfcId);
		}
	}

	public void getParticipantRecordIdentifiers(List<ParticipantRecordIdentifier> participantRecordIdentifierList,
			CustomProcessConfigurationUI processConfigurationUI, ProcessJobMapping pfcId) {
		for (ParticipantRecordIdentifier participantRecordIdentifier : participantRecordIdentifierList) {
			participantRecordIdentifier.setId(null);
			participantRecordIdentifier.setCreatedDate(new Date());
			participantRecordIdentifier.setUpdatedDate(null);
			participantRecordIdentifier.setCreatedBy(processConfigurationUI.getCreatedBy());
			participantRecordIdentifier.setUpdatedBy(null);
			participantRecordIdentifier.setProcessJobMapping(pfcId);
		}
	}

	public void ksdIndenti(CustomProcessConfigurationUI processConfigurationUI,
			List<KsdOutPutFileDetails> ksdOutPutFileDetails, List<KsdOutPutFileDetails> ksdOutPutFileDetailsTobeSaved) {
		for (KsdOutPutFileDetails ksdOutPutFileDetailsdb : ksdOutPutFileDetails) {
			ProcessJobMapping pfcId = new ProcessJobMapping();
			pfcId.setId(processConfigurationUI.getProcessJobMappingId());

			LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
					"KsdOutputFileDetails id: " + ksdOutPutFileDetailsdb.getId());
			List<ParticipantRecordIdentifier> participantRecordIdentifierList = ksdOutPutFileDetailsdb
					.getParticipantRecordIdentifiers();
			if (!participantRecordIdentifierList.isEmpty()) {
				getParticipantRecordIdentifiers(participantRecordIdentifierList, processConfigurationUI, pfcId);
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
						"To copy, participantRecordIdentifier data of required file_detail_id is not present"
								+ ksdOutPutFileDetailsdb.getId());
			}

			getOutputReports(ksdOutPutFileDetailsdb, processConfigurationUI, pfcId);

			List<ReportFormat> reportFormatList = ksdOutPutFileDetailsdb.getReportFormat();
			if (!reportFormatList.isEmpty()) {
				getReportFormat(reportFormatList, processConfigurationUI, pfcId);
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
						"To copy, reportFormat data of required file_detail_id is not present"
								+ ksdOutPutFileDetailsdb.getId());
			}

			List<ReportDataCleanse> reportDataCleanseList = ksdOutPutFileDetailsdb.getReportDataCleanse();
			if (!reportDataCleanseList.isEmpty()) {
				getReportDataCleanse(reportDataCleanseList, processConfigurationUI, pfcId);
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
						"To copy, reportDataCleanse data of required file_detail_id is not present"
								+ ksdOutPutFileDetailsdb.getId());
			}

			List<ReportPivot> reportPivotList = ksdOutPutFileDetailsdb.getReportPivot();
			if (!reportPivotList.isEmpty()) {
				getReportPivot(reportPivotList, processConfigurationUI, pfcId);
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
						"To copy, reportPivot data of required file_detail_id is not present"
								+ ksdOutPutFileDetailsdb.getId());
			}
			ksdOutPutFileDetailsdb.setId(null);
			ksdOutPutFileDetailsdb.setProcessJobMapping(pfcId);
			ksdOutPutFileDetailsdb.setCreatedBy(processConfigurationUI.getCreatedBy());
			ksdOutPutFileDetailsdb.setUpdatedBy(null);
			ksdOutPutFileDetailsdb.setUpdatedDate(null);
			ksdOutPutFileDetailsdb.setCreatedDate(new Date());
			ksdOutPutFileDetailsdb.setParticipantRecordIdentifiers(participantRecordIdentifierList);
			ksdOutPutFileDetailsdb.setOutputReports(ksdOutPutFileDetailsdb.getOutputReports());
			ksdOutPutFileDetailsdb.setReportFormat(reportFormatList);
			ksdOutPutFileDetailsdb.setReportDataCleanse(reportDataCleanseList);
			ksdOutPutFileDetailsdb.setReportPivot(reportPivotList);
			ksdOutPutFileDetailsTobeSaved.add(ksdOutPutFileDetailsdb);
		}
	}

	public void getAndInsertKsdOutputFileDetails(CustomProcessConfigurationUI processConfigurationUI) {
		LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
				"Copying to KsdOutputFileDetails started at: " + System.currentTimeMillis());
		Long oldPjmId = processConfigurationUI.getPjmIdCopied();
		List<KsdOutPutFileDetails> ksdOutPutFileDetailsEnt = getKsdOutputFileDetails(COLUMN_NAME, oldPjmId);
		List<KsdOutPutFileDetails> ksdOutPutFileDetails = Arrays
				.asList(new ObjectMapper().convertValue(ksdOutPutFileDetailsEnt, KsdOutPutFileDetails[].class));
		for (int i = 0; i < ksdOutPutFileDetailsEnt.size(); i++) {
			List<ReportFormat> reportFormat = ksdOutPutFileDetailsEnt.get(i).getReportFormat();
			for (int j = 0; j < reportFormat.size(); j++) {
				ksdOutPutFileDetails.get(i).getReportFormat().get(j).setFont(reportFormat.get(j).getFont());
				ksdOutPutFileDetails.get(i).getReportFormat().get(j).setBorder(reportFormat.get(j).getBorder());
				ksdOutPutFileDetails.get(i).getReportFormat().get(j).setAlignment(reportFormat.get(j).getAlignment());
				ksdOutPutFileDetails.get(i).getReportFormat().get(j)
						.setPatternFill(reportFormat.get(j).getPatternFill());
			}
		}
		List<KsdOutPutFileDetails> ksdOutPutFileDetailsTobeSaved = new ArrayList<>();
		if (!ksdOutPutFileDetails.isEmpty()) {
			ksdIndenti(processConfigurationUI, ksdOutPutFileDetails, ksdOutPutFileDetailsTobeSaved);
			if (!ksdOutPutFileDetailsTobeSaved.isEmpty()) {
				insertKsdOutPutFileDetails(ksdOutPutFileDetailsTobeSaved);
			}
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
					"To copy, KsdOutputFileDetails record of selected jobname is not present"
							+ processConfigurationUI.getJobName());
		}
		LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_KSD_OUPUT_FILE_DETAILS,
				"Copying to KsdOutputFileDetails ended at: " + System.currentTimeMillis());
	}

	public List<KsdOutPutFileDetails> getKsdOutputFileDetails(String columnName, Long columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, GET_KSD_OUTPUT_FILE_DETAILS,
				"getting KsdOutPutFileDetails  started at: " + System.currentTimeMillis());
		List<KsdOutPutFileDetails> ksdOutputdetalisList = genericKsdOutputDao.findByColumn(KsdOutPutFileDetails.class,
				LAYOUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS, columnName, columnValue.toString());
		LoggerUtil.log(this.getClass(), Level.INFO, GET_KSD_OUTPUT_FILE_DETAILS,
				"getting KsdOutPutFileDetails  done size of : " + ksdOutputdetalisList.size());
		return ksdOutputdetalisList;
	}

	public void insertKsdOutPutFileDetails(List<KsdOutPutFileDetails> ksdOutPutFileDetailsTobeSaved) {
		LoggerUtil.log(this.getClass(), Level.INFO, INSERT_KSD_OUTPUT_FILE_DETAILS,
				"inside insertKsdOutPutFileDetails mthod for  inserting new KsdOutPutFileDetails started: "
						+ System.currentTimeMillis());
		baseKsdOutputDao.saveAll(ksdOutPutFileDetailsTobeSaved);
		LoggerUtil.log(this.getClass(), Level.INFO, INSERT_KSD_OUTPUT_FILE_DETAILS,
				" inserted new KsdOutPutFileDetails successfully and end at: " + System.currentTimeMillis());
	}

	public void getAndInsertRulesConfig(CustomProcessConfigurationUI processConfigurationUI) {

		LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_RULES_CONFIG,
				"Copying to RulesConfig started at: " + System.currentTimeMillis());
		Long oldPjmId = processConfigurationUI.getPjmIdCopied();
		List<RulesConfig> ruleConfigsEnt = getRulesConfigDetails(COLUMN_NAME, oldPjmId);
		List<com.wipro.fipc.model.generated.RulesConfig> ruleConfigs = new ArrayList();
		try {
			for (RulesConfig ruleConfigEnt : ruleConfigsEnt) {
				com.wipro.fipc.model.generated.RulesConfig ruleConfigsobj = new com.wipro.fipc.model.generated.RulesConfig();
				ruleConfigsobj = new ObjectMapper().convertValue(ruleConfigEnt,
						com.wipro.fipc.model.generated.RulesConfig.class);
				ruleConfigs.add(ruleConfigsobj);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		List<com.wipro.fipc.model.generated.RulesConfig> rulesConfigTobeSaved = new ArrayList<>();
		if (!ruleConfigs.isEmpty()) {
			for (com.wipro.fipc.model.generated.RulesConfig ruleConfigdb : ruleConfigs) {
				List<com.wipro.fipc.model.generated.RulesDefinition> rulesDefinitionList;
				rulesDefinitionList = ruleConfigdb.getRulesDefinitions();
				if (!rulesDefinitionList.isEmpty()) {
					for (com.wipro.fipc.model.generated.RulesDefinition rulesDefinition : rulesDefinitionList) {
						rulesDefinition.setId(null);
						rulesDefinition.setCreatedDate(new Date());
						rulesDefinition.setUpdatedDate(null);
						rulesDefinition.setCreatedBy(processConfigurationUI.getCreatedBy());
						rulesDefinition.setUpdatedBy(null);
					}
				} else {
					LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_RULES_CONFIG,
							"To copy, rules definition data of required rule config id is not present"
									+ ruleConfigdb.getId());
				}

				ruleConfigdb.setId(null);
				com.wipro.fipc.model.generated.ProcessJobMapping pfcId = new com.wipro.fipc.model.generated.ProcessJobMapping();
				pfcId.setId(processConfigurationUI.getProcessJobMappingId());
				ruleConfigdb.setProcessJobMapping(pfcId);
				ruleConfigdb.setCreatedBy(processConfigurationUI.getCreatedBy());
				ruleConfigdb.setUpdatedBy(null);
				ruleConfigdb.setUpdatedDate(null);
				ruleConfigdb.setCreatedDate(new Date());
				ruleConfigdb.setRulesDefinitions(rulesDefinitionList);

				rulesConfigTobeSaved.add(ruleConfigdb);
			}
			if (!rulesConfigTobeSaved.isEmpty()) {
				List<RulesConfig> ruleent = new ArrayList();
				for (com.wipro.fipc.model.generated.RulesConfig rulesConfigMod : rulesConfigTobeSaved) {
					RulesConfig ruleobj = new RulesConfig();
					ruleobj = new ObjectMapper().convertValue(rulesConfigMod, RulesConfig.class);
					ruleent.add(ruleobj);
				}
				insertRuleConfigDetails(ruleent);
			}
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_RULES_CONFIG,
					"To copy, rules config record of selected jobname is not present"
							+ processConfigurationUI.getJobName());
		}
		LoggerUtil.log(this.getClass(), Level.INFO, GET_AND_INSERT_RULES_CONFIG,
				"Copying to RulesConfig ended at: " + System.currentTimeMillis());
	}

	public List<ProcessFeatureConfig> getProcessConfigDetails(String columnName, Long columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessConfigDetails",
				"for getting ProcessFeatureConfig List by pjmId:" + columnValue.toString() + " started at : "
						+ System.currentTimeMillis());
		List<ProcessFeatureConfig> response = null;
		response = ProcessConfigGenericDao.findByColumn(ProcessFeatureConfig.class, COMMON_SCHEMA,
				PROCESS_FEATURE_CONFIG, columnName, columnValue.toString());
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessConfigDetails",
				" getting ProcessFeatureConfig List size of : " + response.size() + " and end at : "
						+ System.currentTimeMillis());
		return response;
	}

	public List<RulesConfig> getRulesConfigDetails(String columnName, Long columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getRulesConfigDetails", "for getting RulesConfig List by pjmId:"
				+ columnValue.toString() + " started at : " + System.currentTimeMillis());
		List<RulesConfig> response = genericDao.findByColumn(RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, columnName,
				columnValue.toString());
		LoggerUtil.log(this.getClass(), Level.INFO, "getRulesConfigDetails", " getting RulesConfig List size of :"
				+ response.size() + " and end at : " + System.currentTimeMillis());
		return response;
	}

	// Insert Call
	public void insertRuleConfigDetails(List<RulesConfig> ruleConfig) {
		LoggerUtil.log(this.getClass(), Level.INFO, "insertRuleConfigDetails",
				"inside insertRuleConfigDetails for inserting the RuleConfigDetails started at : "
						+ System.currentTimeMillis());
		ruleBaseDao.saveAll(ruleConfig);
		LoggerUtil.log(this.getClass(), Level.INFO, "insertRuleConfigDetails",
				"inserted RuleConfigDetails successfully end at : " + System.currentTimeMillis());
	}

	public void getExceptionDetails(Exception exception) {
		String msg = exception.getMessage();

		LoggerUtil.log(this.getClass(), Level.ERROR, "getExceptionDetails", "Message from exception:", msg);
		if (msg.contains("NotFound: 404")) {
			msg = "Corresponding resouce/url not found in DBService";
			throw new ResourceNotFoundException(msg);
		}

		if (msg.contains("Connection refused: connect")) {
			msg = "DBService is down. Please contact administrator";
		}

		if (msg.contains("InternalServerError: 500")) {
			msg = "Output from DBservice is broken";
		}

		throw new DBServiceException(msg);

	}
}
