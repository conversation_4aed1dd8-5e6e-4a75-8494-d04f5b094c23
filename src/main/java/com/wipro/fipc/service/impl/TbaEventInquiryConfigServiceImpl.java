package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaEventInquiryConfigs;
import com.wipro.fipc.dao.tba.TbaUpdateJsonKeyDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.TbaUpdateJsonKey;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.EventInquiryConfig;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.pojo.tba.TbaEventInquiryConfigDto;
import com.wipro.fipc.service.TbaEventInquiryConfigService;
import com.wipro.fipc.tba.service.EventInquiryConfigService;
import com.wipro.fipc.tba.service.UpdateConfigService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class TbaEventInquiryConfigServiceImpl implements TbaEventInquiryConfigService{
	
	@Autowired
	Environment env;

	@Autowired
	TbaEventInquiryConfigs tbaEventInquiryConfigs;

	@Autowired
	EventInquiryConfigService eventInquiryConfigService;

	@Autowired
	@Qualifier("tbaUpdateJsonKeyDao")
	TbaUpdateJsonKeyDao tbaUpdateJsonKeyDao;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;
	
	@Autowired
	UpdateConfigService updateConfigService; 

	@Override
	public String createTbaEventInquiryConfig(List<TbaEventInquiryConfigDto> entity, String appName,
			String sessionToken)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaEventInquiryConfig", "adid>>>>>>>>>>>>>>>>>>>>>>>> : " + adId);
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaEventInquiryConfig",
				"entity list size>>>>>>>>>>>>>>>>>>>>>>>> : " + entity.size());
		entity.stream().forEach(s -> {
			s.setCreatedDate(new Date());
			s.setCreatedBy(adId);
			s.setActiveFlag("T");
			s.setUpdatedDate(new Date());
			s.setUpdatedBy(adId);
			LoggerUtil.log(this.getClass(), Level.INFO, "createTbaPendingEventConfig>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Save" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});

		List<CommonResRowBO> mydata = tbaEventInquiryConfigs.saveIfNotDuplicate(entity);
		String res = new ObjectMapper().writeValueAsString(mydata);
		ResponseDto response = new ResponseDto();
		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		return objectMapper.writeValueAsString(response);
	}

	@Override
	public String getTbaEventInquiryJsonKey(String columnValue) {
		String res = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEventInquiryJsonKey>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET par_NM " + columnValue);

		List<TbaUpdateJsonKey> mydata = tbaUpdateJsonKeyDao.getTbaInquiryJsonKeyByDataFilter(columnValue);
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaEventInquiryJsonKey", "Exception : " + e.getMessage());

		}
		return res;
	}

	@Override
	public Object getTbaEventInquiryConfig(String columnName, String columnValue)
			throws JsonProcessingException, IOException, IllegalAccessException, InvocationTargetException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEventInquiryConfig>>>>>>>>>>>", "Method Type= "
				+ HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + columnName + "=" + columnValue);

		List<com.wipro.fipc.entity.tba.EventInquiryConfig> mydata = eventInquiryConfigService.findByColumn(columnName,
				columnValue);

		List<TbaEventInquiryConfigDto> tbaConfigDtoList = new ArrayList<>();

	
		for (EventInquiryConfig eventInquiryConfig : mydata) {
			ObjectMapper mapper = new ObjectMapper();
			TbaEventInquiryConfigDto configDto = new TbaEventInquiryConfigDto();
			BeanUtils.copyProperties(configDto, eventInquiryConfig);
			if (eventInquiryConfig.getEffFromDate() != null) {
				configDto.setEffFromDate(mapper.readValue(eventInquiryConfig.getEffFromDate(), Object.class));
			} else 
				eventInquiryConfig.setEffFromDate("");
			if (eventInquiryConfig.getEffToDate() != null) {
				configDto.setEffToDate(mapper.readValue(eventInquiryConfig.getEffToDate(), Object.class));
			} else 
				eventInquiryConfig.setEffToDate("");
			tbaConfigDtoList.add(configDto);
		}
		return tbaConfigDtoList;

	}

	@Override
	public String updateTbaEventInquiryConfig(List<TbaEventInquiryConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException {
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		entity.stream().forEach(s -> {
			com.wipro.fipc.entity.tba.EventInquiryConfig tba = eventInquiryConfigService.findById(s.getId());

			if (tba != null) {
				s.setCreatedBy(tba.getCreatedBy());
				s.setCreatedDate(tba.getCreatedDate());
			}
			s.setActiveFlag("T");

			s.setUpdatedDate(new Date());
			s.setUpdatedBy(adId);
			LoggerUtil.log(this.getClass(), Level.INFO, "updateTbaEventInquiryConfig>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
							+ tba.getProcessJobMapping().getId() + ",ADID: " + adId);
		});

		ResponseDto response = new ResponseDto();
		List<CommonResRowBO> mydata = tbaEventInquiryConfigs.saveIfNotDuplicate(entity);
		String res = objectMapper.writeValueAsString(mydata);
		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		return objectMapper.writeValueAsString(response);

	}

	@Override
	public String getAllTbaEventInquiryConfig() {
		String res = "";
		List<com.wipro.fipc.entity.tba.EventInquiryConfig> mydata = eventInquiryConfigService.list();
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "getAllTbaEventInquiryConfig", "Exception : " + e.getMessage());
		}
		return res;
	}

	@Override
	public String deleteInquiryEventConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken)
			throws URISyntaxException {
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		entities.stream().forEach(s -> {
			s.setUpdatedBy(adId);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteInquiryEventConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});
		List<com.wipro.fipc.entity.tba.EventInquiryConfig> newLayout = new ArrayList<>();

		for (CommonDeleteDTO req : entities) {

			com.wipro.fipc.entity.tba.EventInquiryConfig obj = new com.wipro.fipc.entity.tba.EventInquiryConfig();
			try {
				obj.setId(Long.parseLong(req.getId()));
				obj.setProcessJobMapping(
						new ObjectMapper().convertValue(req.getProcessJobMapping(), ProcessJobMapping.class));
				obj.setUpdatedBy(req.getUpdatedBy());
				obj.setUpdatedDate(req.getUpdatedDate());
				obj.setActiveFlag(req.getActiveFlag());

			} catch (Exception e) {

				LoggerUtil.log(this.getClass(), Level.ERROR, "deleteInquiryEventConfig",
						"Exception : " + e.getMessage());

			}
			newLayout.add(obj);
		}

		List<CommonRowBO> mydata = eventInquiryConfigService.deletemultiplerows(0, newLayout);
		String res = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "deleteInquiryEventConfig", "Exception : " + e.getMessage());


		}

		return res;

	}
	@Override
	public String getTbaEventUpdateProcess(int clientId, boolean check) throws URISyntaxException, IOException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaProcessInquiry>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,clientId=" + clientId);
		Set<com.wipro.fipc.pojo.tba.TbaUpdateProcessDto> mydata = updateConfigService.getUpdateInquiryNames(clientId,check);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String res = objectMapper.writeValueAsString(mydata);

		return res;
	}
}
