package com.wipro.fipc.service.impl;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.batch.NotificationMailConfigDao;
import com.wipro.fipc.dao.batch.NotificationReportConfigDao;
import com.wipro.fipc.dao.filelayout.KsdOutPutFileDetailsDao;
import com.wipro.fipc.dao.filelayout.OutputReportDao;
import com.wipro.fipc.dao.filelayout.ParticipantRecordIdentifierDao;
import com.wipro.fipc.dao.filelayout.ProcessControlConfigDao;
import com.wipro.fipc.dao.filelayout.ReportDataCleanseDao;
import com.wipro.fipc.dao.filelayout.ReportFormatDao;
import com.wipro.fipc.dao.filelayout.ReportPivotDao;
import com.wipro.fipc.dao.filelayout.RulesConfigDao;
import com.wipro.fipc.dao.filelayout.RulesDefinitionDao;
import com.wipro.fipc.dao.filelayout.TaskUpdateConfigDao;
import com.wipro.fipc.dao.filelayout.TbaMatchConfigDao;
import com.wipro.fipc.dao.maestro.TicketCreationConfigDao;
import com.wipro.fipc.dao.tba.TbaUpdateConfigDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.common.NotificationMailConfig;
import com.wipro.fipc.entity.common.NotificationReportConfig;
import com.wipro.fipc.entity.common.RulesDefinition;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.filelayout.OutputReport;
import com.wipro.fipc.entity.filelayout.ParticipantRecordIdentifier;
import com.wipro.fipc.entity.filelayout.ProcessControlConfig;
import com.wipro.fipc.entity.filelayout.TaskUpdateConfig;
import com.wipro.fipc.entity.tba.TbaMatchConfig;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;
import com.wipro.fipc.model.CustomClientBusinessOps;
import com.wipro.fipc.model.LayoutRecord;
import com.wipro.fipc.model.ReadOutputReportDto;
import com.wipro.fipc.model.ReportMultiSheet;
import com.wipro.fipc.model.ReportSheet;
import com.wipro.fipc.model.WriteOutputReportDto;
import com.wipro.fipc.service.IFileLayoutService;
import com.wipro.fipc.service.IOutputReportService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class OutputReportServiceImpl implements IOutputReportService {

	static Logger logger = LoggerFactory.getLogger(OutputReportServiceImpl.class);

	protected static final String COMMON_SOFTDELETE_NOTIFICATION_MAIL_CONFIG = "commonSoftdeleletNotificationMailConfig";
	protected static final String COMMON_SOFTDELETE_NOTIFICATION_REPORT_CONFIG = "commonSoftdeleteNotificationReportConfig";
	protected static final String COMMON_SOFTDELETE_TASK_UPDATE_CONFIG = "commonSoftdeleteTaskUpdateConfig";
	protected static final String COMMON_SOFTDELETE_TICKET_CREATION_CONFIG = "commonSoftdeleletTicketCreationConfig";
	protected static final String COMMON_SOFTDELETE_TBA_MATCH_CONFIG = "commonSoftdeleteTbaMatchConfig";
	protected static final String COMMON_SOFTDELETE_PROCESS_CONTROL_CONFIG = "commonSoftdeleteProcessControlConfig";
	protected static final String COMMON_SOFTDELETE_RULE_DEFINITION_CONFIG = "commonSoftdeleteRuleDifinition";
	protected static final String COMMON_SOFTDELETE_KSD_OUTPUT = "commonSoftdeleteksdoutput";

	public static final String STATUS = "status";
	public static final String MESSAGE = "message";
	public static final String SUCCESS = "success";
	public static final String FAILED = "failed";
	public static final String CONTENT_TYPE = "Content-Type";
	public static final String GET_KSD_OUTPUT_FILE_DETAILS = "getKsdOutPutFileDetails";
	public static final String PJM_ID = "pjmID:";
	public static final String COLUMN_VALUE = "&column_value=";

	protected static final String KSD_OUTPUT_FILE_DETAILS = "KSD_OUTPUT_FILE_DETAILS";
	protected static final String OUT_SCHEMA = "layout_rule";

	protected static final String OUTPUT_REPORT = "OUTPUT_REPORT";

	@Autowired
	Environment env;

	@Autowired
	JsonParser parser;

	@Autowired
	Gson gson;

	@Autowired
	private BaseDao<KsdOutPutFileDetails> ksdDao;

	@Autowired
	IFileLayoutService fileLayoutService;

	@Autowired
	CustomClientBusinessOps customClientBusinessOps;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	GenericDao<KsdOutPutFileDetails> KsdGenericDao;

	@Autowired
	GenericDao<OutputReport> genricOutputReportDao;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	TbaUpdateConfigDao tbaUpdateConfigDao;

	@Autowired
	TaskUpdateConfigDao taskUpdateConfigDao;
	@Autowired
	KsdOutPutFileDetailsDao ksdOutPutFileDetailsDao;

	@Autowired
	OutputReportDao outputReportDao;

	@Autowired
	ReportDataCleanseDao reportDataCleanseDao;
	@Autowired
	ParticipantRecordIdentifierDao participantRecordIdentifierDao;

	@Autowired
	ReportFormatDao reportFormatDao;

	@Autowired
	ReportPivotDao reportPivotDao;
	@Autowired
	ProcessControlConfigDao processControlConfigDao;

	@Autowired
	TbaMatchConfigDao tbaMatchConfigDao;

	@Autowired
	RulesDefinitionDao rulesDefinitionDao;

	@Autowired
	RulesConfigDao rulesConfigDao;

	@Autowired
	TicketCreationConfigDao ticketCreationConfigDao;

	@Autowired
	NotificationMailConfigDao notificationMailConfigDao;

	@Autowired
	NotificationReportConfigDao notificationReportConfigDao;

	@Autowired
	private BaseDao<TaskUpdateConfig> taskUpdateConDao;
	@Autowired
	private BaseDao<TicketCreationConfig> ticketCreationDao;

	@Autowired
	private BaseDao<NotificationMailConfig> notificationdao;
	@Autowired
	private BaseDao<NotificationReportConfig> notificationReportdao;

	@Override
	public String saveKsdOutPutFileDetails(String pjmId, String fileName, WriteOutputReportDto uiEntity, String appName,
			String sessionToken) throws URISyntaxException {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		Date date = new Date();
		for (com.wipro.fipc.model.KsdOutPutFileDetails ksd : uiEntity.getUiJson()) {
			ksd.setCreatedBy(adid);
			ksd.setUpdatedBy(adid);
			ksd.setUpdatedDate(new Date());
			ksd.setActiveFlag("T");
			ksd.getOutputReports().stream().forEach(output -> {
				output.setCreatedBy(adid);
				output.setUpdatedBy(adid);
				output.setUpdatedDate(date);
			});
			ksd.getParticipantRecordIdentifiers().stream().forEach(rec -> {
				rec.setCreatedBy(adid);
				rec.setUpdatedBy(adid);
				rec.setUpdatedDate(date);
			});
			ksd.getReportDataCleanse().stream().forEach(data -> {
				data.setCreatedBy(adid);
				data.setUpdatedBy(adid);
				data.setUpdatedDate(date);
			});
			ksd.getReportFormat().stream().forEach(format -> {
				format.setCreatedBy(adid);
				format.setUpdatedBy(adid);
				format.setUpdatedDate(date);
			});
			ksd.getReportPivot().stream().forEach(pivot -> {
				pivot.setCreatedBy(adid);
				pivot.setUpdatedBy(adid);
				pivot.setUpdatedDate(date);
			});
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "saveKsdOutPutFileDetails() -> OUTPUT REPORT ", PJM_ID + pjmId,
				"fileName = " + fileName + " ADID =" + adid);
		return saveInputReportAndOutputReportData(pjmId, fileName, uiEntity);
	}

	@Override
	public String updateKsdOutPutFileDetails(WriteOutputReportDto uiEntity, String appName, String sessionToken)
			throws URISyntaxException {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		for (com.wipro.fipc.model.KsdOutPutFileDetails ksd : uiEntity.getUiJson()) {
			com.wipro.fipc.model.KsdOutPutFileDetails dbData = getDbData(ksd.getId());
			ksd.setCreatedBy(dbData.getCreatedBy());
			ksd.setCreatedDate(dbData.getCreatedDate());
			ksd.setUpdatedBy(adid);
			ksd.setActiveFlag("T");
			for (com.wipro.fipc.model.OutputReport output : ksd.getOutputReports()) {
				if (output.getId() != null) {
					com.wipro.fipc.model.OutputReport dbOutput = getDbOutputReport(dbData, output.getId());
					if (dbOutput != null) {
						output.setCreatedBy(dbOutput.getCreatedBy());
						output.setCreatedDate(dbOutput.getCreatedDate());
					}
					output.setUpdatedBy(adid);
				} else {
					output.setCreatedBy(adid);
					output.setUpdatedBy(adid);
					output.setUpdatedDate(new Date());
				}
			}
			for (com.wipro.fipc.model.ParticipantRecordIdentifier rec : ksd.getParticipantRecordIdentifiers()) {
				if (rec.getId() != null) {
					com.wipro.fipc.model.ParticipantRecordIdentifier dbRecord = getDbRecord(dbData, rec.getId());
					if (dbRecord != null) {
						rec.setCreatedBy(dbRecord.getCreatedBy());
						rec.setCreatedDate(dbRecord.getCreatedDate());
					}
					rec.setUpdatedBy(adid);
				} else {
					rec.setCreatedBy(adid);
					rec.setUpdatedBy(adid);
					rec.setUpdatedDate(new Date());
				}
			}
			for (com.wipro.fipc.model.ReportDataCleanse data : ksd.getReportDataCleanse()) {
				if (data.getId() != null) {
					com.wipro.fipc.model.ReportDataCleanse dbReport = getDbReport(dbData, data.getId());
					if (dbReport != null) {
						data.setCreatedBy(dbReport.getCreatedBy());
						data.setCreatedDate(dbReport.getCreatedDate());
					}
					data.setUpdatedBy(adid);
				} else {
					data.setCreatedBy(adid);
					data.setUpdatedBy(adid);
					data.setUpdatedDate(new Date());
				}
			}
			for (com.wipro.fipc.model.ReportFormat format : ksd.getReportFormat()) {
				if (format.getId() != null) {
					com.wipro.fipc.model.ReportFormat dbFormat = getDbFormat(dbData, format.getId());
					if (dbFormat != null) {
						format.setCreatedBy(dbFormat.getCreatedBy());
						format.setCreatedDate(dbFormat.getCreatedDate());
					}
					format.setUpdatedBy(adid);
				} else {
					format.setCreatedBy(adid);
					format.setUpdatedBy(adid);
					format.setUpdatedDate(new Date());
				}
			}
			for (com.wipro.fipc.model.ReportPivot pivot : ksd.getReportPivot()) {
				if (pivot.getId() != null) {
					com.wipro.fipc.model.ReportPivot dbPivot = getDbPivot(dbData, pivot.getId());
					if (dbPivot != null) {
						pivot.setCreatedBy(dbPivot.getCreatedBy());
						pivot.setCreatedDate(dbPivot.getCreatedDate());
					}
					pivot.setUpdatedBy(adid);
				} else {
					pivot.setCreatedBy(adid);
					pivot.setUpdatedBy(adid);
					pivot.setUpdatedDate(new Date());
				}
			}
			LoggerUtil.log(this.getClass(), Level.INFO, "updateKsdOutPutFileDetails() -> OUTPUT REPORT ",
					PJM_ID + ksd.getProcessJobMapping().getId().toString() + " ADID =" + adid + " action = UPDATE"
							+ " ID =" + ksd.getId());
		}
		return updateKsdOutPutFileDetails(uiEntity);
	}

	private com.wipro.fipc.model.KsdOutPutFileDetails getDbData(Long id) {

		List<String> columnName = new ArrayList<>();
		columnName.add("id");
		List<String> columnCondition = new ArrayList<>();
		columnCondition.add("eq");
		List<String> columnValue = new ArrayList<>();
		columnValue.add(String.valueOf(id));

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnName,
				columnCondition, columnValue);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting KsdOutPutFileDetails in  columnConditionParams: " + columnConditionParams);

		List<KsdOutPutFileDetails> findByMultiColumnCondition = KsdGenericDao.findByMultiColumnCondition(
				KsdOutPutFileDetails.class, OUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS, columnConditionParams);

		com.wipro.fipc.model.KsdOutPutFileDetails ksd = new com.wipro.fipc.model.KsdOutPutFileDetails();
		try {
			BeanUtils.copyProperties(ksd, findByMultiColumnCondition);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}
		return ksd;
	}

	private com.wipro.fipc.model.OutputReport getDbOutputReport(com.wipro.fipc.model.KsdOutPutFileDetails dbData,
			long id) {
		com.wipro.fipc.model.OutputReport res = null;
		for (com.wipro.fipc.model.OutputReport db : dbData.getOutputReports()) {
			if (db.getId() == id) {
				res = db;
			}
		}
		return res;
	}

	private com.wipro.fipc.model.ParticipantRecordIdentifier getDbRecord(
			com.wipro.fipc.model.KsdOutPutFileDetails dbData, long id) {
		com.wipro.fipc.model.ParticipantRecordIdentifier res = null;
		for (com.wipro.fipc.model.ParticipantRecordIdentifier db : dbData.getParticipantRecordIdentifiers()) {
			if (db.getId() == id) {
				res = db;
			}
		}
		return res;
	}

	private com.wipro.fipc.model.ReportDataCleanse getDbReport(com.wipro.fipc.model.KsdOutPutFileDetails dbData,
			long id) {
		com.wipro.fipc.model.ReportDataCleanse res = null;
		for (com.wipro.fipc.model.ReportDataCleanse db : dbData.getReportDataCleanse()) {
			if (db.getId() == id) {
				res = db;
			}
		}
		return res;
	}

	private com.wipro.fipc.model.ReportFormat getDbFormat(com.wipro.fipc.model.KsdOutPutFileDetails dbData, long id) {
		com.wipro.fipc.model.ReportFormat res = null;
		for (com.wipro.fipc.model.ReportFormat db : dbData.getReportFormat()) {
			if (db.getId() == id) {
				res = db;
			}
		}
		return res;
	}

	private com.wipro.fipc.model.ReportPivot getDbPivot(com.wipro.fipc.model.KsdOutPutFileDetails dbData, long id) {
		com.wipro.fipc.model.ReportPivot res = null;
		for (com.wipro.fipc.model.ReportPivot db : dbData.getReportPivot()) {
			if (db.getId() == id) {
				res = db;
			}
		}
		return res;
	}

	@Override
	public String deleteOutputReport(KsdOutPutFileDetails entity, String appName, String sessionToken)
			throws URISyntaxException {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		gson = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").create();
		entity.setUpdatedBy(adid);
		entity.setActiveFlag('T');
		entity.setUpdatedDate(new Date());
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReport() -> OUTPUT REPORT ", "ID = " + entity.getId()
				+ " ADID =" + adid + "PJMID =" + entity.getProcessJobMapping().getId().toString());
		return deleteOutputReport(entity);
	}

	@Override
	public String saveInputReportAndOutputReportData(String pjmId, String fileName, WriteOutputReportDto entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveInputReportAndOutputReportData : ", PJM_ID + pjmId,
				"fileName:" + fileName);
		boolean flag = false;
		try {
			List<com.wipro.fipc.model.KsdOutPutFileDetails> outPutFileDetails = entity.getUiJson();
			for (com.wipro.fipc.model.KsdOutPutFileDetails ksdOutPutFileDetails : outPutFileDetails) {
				ksdOutPutFileDetails.getOutputReports().stream().forEach(o -> o.setCreatedDate(new Date()));
				ksdOutPutFileDetails.getParticipantRecordIdentifiers().stream()
						.forEach(o -> o.setCreatedDate(new Date()));
				ksdOutPutFileDetails.getReportFormat().stream().forEach(o -> o.setCreatedDate(new Date()));
				ksdOutPutFileDetails.getReportDataCleanse().stream().forEach(o -> o.setCreatedDate(new Date()));
				ksdOutPutFileDetails.getReportPivot().stream().forEach(o -> o.setCreatedDate(new Date()));
				ksdOutPutFileDetails.setCreatedDate(new Date());
				KsdOutPutFileDetails ksd = new KsdOutPutFileDetails();
				ksd = new ObjectMapper().convertValue(ksdOutPutFileDetails, KsdOutPutFileDetails.class);
				for (int i = 0; i < ksdOutPutFileDetails.getReportFormat().size(); i++) {
					ksd.getReportFormat().get(i).setFont(ksdOutPutFileDetails.getReportFormat().get(i).getFont());
					ksd.getReportFormat().get(i).setBorder(ksdOutPutFileDetails.getReportFormat().get(i).getBorder());
					ksd.getReportFormat().get(i)
							.setAlignment(ksdOutPutFileDetails.getReportFormat().get(i).getAlignment());
					ksd.getReportFormat().get(i)
							.setPatternFill(ksdOutPutFileDetails.getReportFormat().get(i).getPatternFill());
				}
				String extension = FilenameUtils.getExtension(ksd.getFileName());
				if ("csv".equalsIgnoreCase(extension) && StringUtils.isEmpty(ksd.getSheetName())) {
					ksd.setSheetName("Sheet1");
					ksd.setSheetNameWoutSpace("Sheet1");
				}
				ksdOutPutFileDetailsDao.deletePreviousFile(ksd.getUpdatedBy(), Long.valueOf(pjmId), ksd.getFileType());
				outputReportDao.commonupdate(ksd.getUpdatedBy(), Long.valueOf(pjmId));
				reportDataCleanseDao.commonupdate(ksd.getUpdatedBy(), Long.valueOf(pjmId));
				reportFormatDao.commonupdate(ksd.getUpdatedBy(), Long.valueOf(pjmId));
				reportPivotDao.commonupdate(ksd.getUpdatedBy(), Long.valueOf(pjmId));
				participantRecordIdentifierDao.commonupdate(ksd.getUpdatedBy(), Long.valueOf(pjmId));
				ksdDao.save(ksd);
				flag = true;
				LoggerUtil.log(this.getClass(), Level.INFO, "saveInputReportAndOutputReportData : ", PJM_ID + pjmId,
						"ksdOutPutFileDetails saved sucessfully");
			}
		} catch (Exception e) {
			flag = false;
			LoggerUtil.log(this.getClass(), Level.INFO, "saveInputReportAndOutputReportData : ", PJM_ID + pjmId,
					"ksdOutPutFileDetails not  saved sucessfully");
		}

		JsonObject jobj = new JsonObject();
		if (flag) {
			jobj.addProperty(STATUS, SUCCESS);
			jobj.addProperty(MESSAGE, "Records saved Successfully.");
		} else {
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Records not saved Successfully.");
		}

		return jobj.toString();
	}

	@Override
	public String updateKsdOutPutFileDetails(WriteOutputReportDto entity) {
		boolean flag = false;
		try {
			List<com.wipro.fipc.model.KsdOutPutFileDetails> outPutFileDetails = entity.getUiJson();
			for (com.wipro.fipc.model.KsdOutPutFileDetails ksdOutPutFileDetails : outPutFileDetails) {
				KsdOutPutFileDetails ksd = new KsdOutPutFileDetails();
				ksd = new ObjectMapper().convertValue(ksdOutPutFileDetails, KsdOutPutFileDetails.class);
				for (int i = 0; i < ksdOutPutFileDetails.getReportFormat().size(); i++) {
					ksd.getReportFormat().get(i).setFont(ksdOutPutFileDetails.getReportFormat().get(i).getFont());
					ksd.getReportFormat().get(i).setBorder(ksdOutPutFileDetails.getReportFormat().get(i).getBorder());
					ksd.getReportFormat().get(i)
							.setAlignment(ksdOutPutFileDetails.getReportFormat().get(i).getAlignment());
					ksd.getReportFormat().get(i)
							.setPatternFill(ksdOutPutFileDetails.getReportFormat().get(i).getPatternFill());
				}

				ksdDao.save(ksd);
				flag = true;
				LoggerUtil.log(this.getClass(), Level.INFO, "updateKsdOutPutFileDetails : ",
						"ksdOutPutFileDetails updated sucessfully");
			}
		} catch (Exception e) {
			flag = false;
			LoggerUtil.log(this.getClass(), Level.INFO, "updateKsdOutPutFileDetails : ",
					"ksdOutPutFileDetails not updated sucessfully");
		}

		JsonObject jobj = new JsonObject();
		if (flag) {
			jobj.addProperty(STATUS, SUCCESS);
			jobj.addProperty(MESSAGE, "Records updated Successfully.");
		} else {
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Records not updated Successfully.");
		}

		return jobj.toString();
	}

	public KsdOutPutFileDetails getKsdOutPutFileDetails(KsdOutPutFileDetails ksdOutPutFileDetails) {

		ksdOutPutFileDetails.getOutputReports().stream().forEach(o -> {
			if (o.getId() != null) {
				o.setUpdatedDate(new Date());
			} else {
				o.setCreatedDate(new Date());
			}
		});

		ksdOutPutFileDetails.getParticipantRecordIdentifiers().stream().forEach(o -> {
			if (o.getId() != null) {
				o.setUpdatedDate(new Date());
			} else {
				o.setCreatedDate(new Date());
			}
		});

		ksdOutPutFileDetails.getReportFormat().stream().forEach(o -> {
			if (o.getId() != null) {
				o.setUpdatedDate(new Date());
			} else {
				o.setCreatedDate(new Date());
			}
		});

		ksdOutPutFileDetails.getReportDataCleanse().stream().forEach(o -> {
			if (o.getId() != null) {
				o.setUpdatedDate(new Date());
			} else {
				o.setCreatedDate(new Date());
			}
		});

		ksdOutPutFileDetails.getReportPivot().stream().forEach(o -> {
			if (o.getId() != null) {
				o.setUpdatedDate(new Date());
			} else {
				o.setCreatedDate(new Date());
			}
		});

		ksdOutPutFileDetails.setUpdatedDate(new Date());

		return ksdOutPutFileDetails;
	}

	@Override
	public String getKsdOutPutFileDetails(String pjmId, String fileName)
			throws JsonProcessingException, UnsupportedEncodingException {
		fileName = fileName.replace("'", "''");
		LoggerUtil.log(this.getClass(), Level.INFO, GET_KSD_OUTPUT_FILE_DETAILS + " : ", PJM_ID + pjmId,
				" fileName" + fileName);

		List<String> columnName = new ArrayList<>();
		columnName.add("process_job_mapping_id");
		columnName.add("file_name");
		columnName.add("active_flag");
		List<String> columnCondition = new ArrayList<>();
		columnCondition.add("eq");
		columnCondition.add("eq");
		columnCondition.add("eq");
		List<String> columnValue = new ArrayList<>();
		columnValue.add(pjmId);
		columnValue.add(fileName);
		columnValue.add("T");

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnName,
				columnCondition, columnValue);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting ksdoutputdetails in  columnConditionParams: " + columnConditionParams);

		List<KsdOutPutFileDetails> findByMultiColumnCondition = KsdGenericDao.findByMultiColumnCondition(
				KsdOutPutFileDetails.class, OUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS, columnConditionParams);
		ReadOutputReportDto dto = new ReadOutputReportDto();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		dto.setUiJson(objectMapper.writeValueAsString(findByMultiColumnCondition));
		return objectMapper.writeValueAsString(dto);
	}

	@Override
	public String getKsdOutPutFileDetailsBySheetName(String pjmId, String sheetName) throws JsonProcessingException {
		List<String> columnName = new ArrayList<>();
		columnName.add("process_job_mapping_id");
		columnName.add("sheet_name");
		columnName.add("active_flag");
		List<String> columnCondition = new ArrayList<>();
		columnCondition.add("eq");
		columnCondition.add("eq");
		columnCondition.add("eq");
		List<String> columnValue = new ArrayList<>();
		columnValue.add(pjmId);
		columnValue.add(sheetName);
		columnValue.add("T");

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnName,
				columnCondition, columnValue);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting KsdOutputFileDetails in  columnConditionParams: " + columnConditionParams);

		List<KsdOutPutFileDetails> findByMultiColumnCondition = KsdGenericDao.findByMultiColumnCondition(
				KsdOutPutFileDetails.class, OUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS, columnConditionParams);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(findByMultiColumnCondition);
	}

	@Override
	public String getInputFileDetailsByFileName(String pjmId, String inpFileName) throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getInputFileDetailsByFileName : ",
				PJM_ID + pjmId + "   fileName" + inpFileName);

		try {
			String fileName= inpFileName.substring(inpFileName.indexOf(':')+1,inpFileName.length());
			
			String inputReport = fileLayoutService.getAllMultiReport(pjmId, fileName);

			ReportSheet details = gson.fromJson(inputReport, ReportSheet.class);

			List<ReportMultiSheet> ksdFileJson = details.getKsdFileJson();

			List<com.wipro.fipc.model.generated.KsdOutPutFileDetails> list = new ArrayList<>();

			for (ReportMultiSheet reportMultiSheet : ksdFileJson) {
				com.wipro.fipc.model.generated.KsdOutPutFileDetails ksdOutPutFileDetails = new com.wipro.fipc.model.generated.KsdOutPutFileDetails();
				com.wipro.fipc.model.generated.ProcessJobMapping processJobMapping = new com.wipro.fipc.model.generated.ProcessJobMapping();
				processJobMapping.setId(Long.valueOf(reportMultiSheet.getProcessJobMappingId()));
				ksdOutPutFileDetails.setActiveFlag("T");
				ksdOutPutFileDetails.setSheetName(reportMultiSheet.getSheetName());
				ksdOutPutFileDetails.setProcessJobMapping(processJobMapping);
				ksdOutPutFileDetails.setFileName(reportMultiSheet.getFileName());
				ksdOutPutFileDetails.setSheetNameWoutSpace(reportMultiSheet.getSheetNameWoutSpace());
				ksdOutPutFileDetails.setFileNameWoutSpace(reportMultiSheet.getFileNameWoutSpace());
				ksdOutPutFileDetails.setFileType("Output Report");
				ksdOutPutFileDetails.setSender(reportMultiSheet.getSender());
				ksdOutPutFileDetails.setMinThreshold(StringUtils.isNotBlank(reportMultiSheet.getThresholdMin())
						? Integer.valueOf(reportMultiSheet.getThresholdMin())
						: Integer.valueOf(0));
				ksdOutPutFileDetails.setMaxThreshold(StringUtils.isNotBlank(reportMultiSheet.getThresholdMax())
						? Integer.valueOf(reportMultiSheet.getThresholdMax())
						: Integer.valueOf(0));
				ksdOutPutFileDetails.setPptIdentifier(reportMultiSheet.getPptIdentifier());
				ksdOutPutFileDetails.setPptIdentifierType(reportMultiSheet.getIdentifier());
				ksdOutPutFileDetails.setVariation(StringUtils.isNotBlank(reportMultiSheet.getVariance())
						? Integer.valueOf(reportMultiSheet.getVariance())
						: Integer.valueOf(0));
				ksdOutPutFileDetails.setDelimiter(reportMultiSheet.getDelimiter());
				ksdOutPutFileDetails.setSubject(reportMultiSheet.getSubject());
				ksdOutPutFileDetails.setDateFormat(reportMultiSheet.getDateFormat());

				List<LayoutRecord> detailRecords = reportMultiSheet.getDetailRecords();
				List<com.wipro.fipc.model.generated.OutputReport> reports = new ArrayList<>();
				for (LayoutRecord layoutRecord : detailRecords) {
					com.wipro.fipc.model.generated.OutputReport outputReport = new com.wipro.fipc.model.generated.OutputReport();
					outputReport.setDataElement(layoutRecord.getDataElement());
					outputReport.setDataFormat(layoutRecord.getFormat());
					outputReport.setRecordIdentifier(layoutRecord.getRecordIdentifier());
					outputReport.setActiveFlag("T");
					outputReport.setAddOn("");
					outputReport.setCellName("");
					outputReport.setCellValue("");
					outputReport.setChildElement("");
					outputReport.setCreatedBy(layoutRecord.getCreatedBy());
					outputReport.setCreatedDate(layoutRecord.getCreatedDate());
					outputReport.setUpdatedBy(layoutRecord.getUpdatedBy());
					outputReport.setUpdatedDate(layoutRecord.getUpdatedDate());
					outputReport.setDataElementWoutSpace("");
					outputReport.setTotal("");
					reports.add(outputReport);
				}

				ksdOutPutFileDetails.setOutputReports(reports);
				ksdOutPutFileDetails.setParticipantRecordIdentifiers(new ArrayList<>());
				ksdOutPutFileDetails.setReportDataCleanse(new ArrayList<>());
				ksdOutPutFileDetails.setReportFormat(new ArrayList<>());
				ksdOutPutFileDetails.setReportPivot(new ArrayList<>());
				list.add(ksdOutPutFileDetails);

			}

			ReadOutputReportDto dto = new ReadOutputReportDto();
			dto.setUiJson(gson.toJson(list));
			LoggerUtil.log(this.getClass(), Level.INFO, "getInputFileDetailsByFileName : ",
					"getting InputFileDetailsByFileName  sucessfully");
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			return objectMapper.writeValueAsString(dto);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.INFO, "getInputFileDetailsByFileName : ",
					"Not getting InputFileDetailsByFileName ");
			return "Error";
		}
	}

	@Override
	public String getKsdOutPutFileDetails(String pjmId) throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdOutPutFileDetails : ", PJM_ID + pjmId);
		List<String> columnName = new ArrayList<>();
		columnName.add("process_job_mapping_id");
		columnName.add("active_flag");
		List<String> columnCondition = new ArrayList<>();
		columnCondition.add("eq");
		columnCondition.add("eq");
		List<String> columnValue = new ArrayList<>();
		columnValue.add(pjmId);
		columnValue.add("T");

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnName,
				columnCondition, columnValue);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting KsdOutputFileDetails in  columnConditionParams: " + columnConditionParams);

		List<KsdOutPutFileDetails> findByMultiColumnCondition = KsdGenericDao.findByMultiColumnCondition(
				KsdOutPutFileDetails.class, OUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS, columnConditionParams);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(findByMultiColumnCondition);
	}

	@Override
	public String getOutputReport(String pjmId, String fileName)
			throws UnsupportedEncodingException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getOutPutFileDetails : ",
				PJM_ID + pjmId + "   fileName" + fileName);
		fileName = fileName.replace("'", "''");
		fileName = URLEncoder.encode(fileName, "UTF-8");
		List<String> columnName = new ArrayList<>();
		columnName.add("process_job_mapping_id");
		columnName.add("file_name");
		columnName.add("active_flag");
		List<String> columnCondition = new ArrayList<>();
		columnCondition.add("eq");
		columnCondition.add("eq");
		columnCondition.add("eq");
		List<String> columnValue = new ArrayList<>();
		columnValue.add(pjmId);
		columnValue.add(fileName);
		columnValue.add("T");

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnName,
				columnCondition, columnValue);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting OutputReport in  columnConditionParams: " + columnConditionParams);

		List<OutputReport> findByMultiColumnCondition = genricOutputReportDao
				.findByMultiColumnCondition(OutputReport.class, OUT_SCHEMA, OUTPUT_REPORT, columnConditionParams);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(findByMultiColumnCondition);
	}

	@Override
	public String deleteOutPutFileDetails(long process_job_mapping_id, String file_name, String adid)
			throws UnsupportedEncodingException {
		boolean response = false;
		String fname = file_name;
		file_name = file_name.replace(" ", "%20");
		file_name = file_name.replace("'", "%27");
		file_name = file_name.replace("#", "%23");
		file_name = file_name.replace("+", "%2B");
		file_name = file_name.replace("@", "%40");
		file_name = file_name.replace("$", "%24");
		file_name = file_name.replace("^", "%5E");
		file_name = file_name.replace("&", "%26");
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutPutFileDetails method",
				"calling db api to delete OutPutFileDetails for pjmId " + process_job_mapping_id + " and filename "
						+ file_name);

		try {
			commonSoftdeleletNotificationMailConfig(process_job_mapping_id, file_name, adid);
			commonSoftdeleletNotificationReportConfig(process_job_mapping_id, file_name, adid);
			commonSoftdeleletTaskUpdateConfig(process_job_mapping_id, file_name, adid);
			commonSoftdeleletTicketCreationConfig(process_job_mapping_id, file_name, adid);
			commonSoftdeleteTbaMatchConfig(process_job_mapping_id, file_name, adid);
			commonSoftdeleletProcessControleConfig(process_job_mapping_id, file_name, adid);
			commonSoftdeleteRuleDifinition(process_job_mapping_id, file_name, adid);
			commonSoftdeleteksdoutput(process_job_mapping_id, file_name, adid);
			response = true;
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutPutFileDetails()",
					"OutPutFileDetails deleted successfully.");
		} catch (Exception e) {
			response = false;
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutPutFileDetails()",
					"OutPutFileDetails not deleted successfully.");
		}

		JsonObject jobj = new JsonObject();
		if (response) {
			jobj.addProperty(STATUS, SUCCESS);
			jobj.addProperty(MESSAGE, fname + " deleted successfully.");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutPutFileDetails()",
					"OutPutFileDetails deleted successfully.");
		} else {
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, fname + " is not deleted.");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutPutFileDetails()",
					"OutPutFileDetails are not deleted.");
		}
		return jobj.toString();
	}

	@Override
	public String getDistinctFileNames(String pjmId) throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDistinctFileNames method",
				"calling db api to get FileDetails for pjmId " + pjmId);

		List<KsdOutPutFileDetails> ksdList = new ArrayList<>();
		List<KsdOutPutFileDetails> response = null;

		List<String> columnName = new ArrayList<>();
		columnName.add("process_job_mapping_id");
		columnName.add("active_flag");
		List<String> columnCondition = new ArrayList<>();
		columnCondition.add("eq");
		columnCondition.add("eq");
		List<String> columnValue = new ArrayList<>();
		columnValue.add(pjmId);
		columnValue.add("T");

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnName,
				columnCondition, columnValue);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting KsdOutPutFileDetails in  columnConditionParams: " + columnConditionParams);

		response = KsdGenericDao.getDistinctFileNames(KsdOutPutFileDetails.class, OUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS,
				columnConditionParams);

		LoggerUtil.log(this.getClass(), Level.INFO, "getDistinctFileNames()", "db api call is successful");
		boolean flag = false;
		List<String> fileList = new ArrayList<>();
		for (KsdOutPutFileDetails ksd : response) {
			if (fileList.contains(ksd.getFileName())) {
				flag = true;
			}
			if (!flag) {
				KsdOutPutFileDetails ksdoutput = new KsdOutPutFileDetails();
				ksdoutput.setFileName(ksd.getFileName());
				ksdoutput.setFileNameWoutSpace(ksd.getFileNameWoutSpace());
				ksdoutput.setProcessJobMapping(ksd.getProcessJobMapping());
				ksdoutput.setId(ksd.getId());
				ksdList.add(ksdoutput);
				fileList.add(ksd.getFileName());

			}
			flag = false;
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getDistinctFileNames()", "response is " + ksdList);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(ksdList);

	}

	@Override
	public String updateOutPutReportDetails(List<OutputReport> entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "update OutPutReportDetails Calling DB service: ", "");

		entity.stream().forEach(o -> o.setUpdatedDate(new Date()));

		boolean response = false;
		try {
			outputReportDao.saveAll(entity);
			response = true;
			LoggerUtil.log(this.getClass(), Level.INFO, "updateOutPutReportDetails()",
					"OutPutFileDetails updated successfully.");

		} catch (Exception e) {
			response = false;
			LoggerUtil.log(this.getClass(), Level.INFO, "updateOutPutReportDetails()",
					"OutPutFileDetails not updated successfully.");
		}

		JsonObject jobj = new JsonObject();
		if (response) {
			jobj.addProperty(STATUS, SUCCESS);
			jobj.addProperty(MESSAGE, "Report is saved successfully.");
			LoggerUtil.log(this.getClass(), Level.INFO, "updateOutPutReportDetails()",
					"OutPutFileDetails saved successfully.");
			return jobj.toString();
		}
		jobj.addProperty(STATUS, FAILED);
		jobj.addProperty(MESSAGE, "Report is not saved.");
		LoggerUtil.log(this.getClass(), Level.INFO, "updateOutPutReportDetails()",
				"OutPutFileDetails are not deleted.");
		return jobj.toString();
	}

	@Override
	public String updateParticipantRecordIdentifierDetails(List<ParticipantRecordIdentifier> entity) {
		boolean response;

		entity.stream().forEach(o -> o.setUpdatedDate(new Date()));

		try {
			participantRecordIdentifierDao.saveAll(entity);
			response = true;
			LoggerUtil.log(this.getClass(), Level.INFO, "updateParticipantRecordIdentifierDetails()",
					"ParticipantRecordIdentifierDetails updated successfully.");
		} catch (Exception e) {
			response = false;
			LoggerUtil.log(this.getClass(), Level.INFO, "updateParticipantRecordIdentifierDetails()",
					"ParticipantRecordIdentifierDetails not updated successfully.");
		}

		JsonObject jobj = new JsonObject();
		if (response) {
			jobj.addProperty(STATUS, SUCCESS);
			jobj.addProperty(MESSAGE, "Report is saved successfully.");
			LoggerUtil.log(this.getClass(), Level.INFO, "updateParticipantRecordIdentifierDetails()",
					"ParticipantRecordIdentifierDetails saved successfully.");
			return jobj.toString();
		}
		jobj.addProperty(STATUS, FAILED);
		jobj.addProperty(MESSAGE, "Report is not saved.");
		LoggerUtil.log(this.getClass(), Level.INFO, "updateParticipantRecordIdentifierDetails()",
				"ParticipantRecordIdentifierDetails are not saved.");
		return jobj.toString();

	}

	@Override
	public String deleteOutputReport(KsdOutPutFileDetails entity) throws URISyntaxException {
		boolean response;
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReport method", "Calling DB service: {0}: ", "");
		JsonObject jobj = new JsonObject();

		long process_job_mapping_id;
		String adid;
		String file_name;
		try {
			process_job_mapping_id = entity.getProcessJobMapping().getId();
			file_name = entity.getFileName();
			adid = entity.getUpdatedBy();

			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_MAIL_CONFIG,
					"Get commonSoftdeleletNotificationMailConfig of filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_MAIL_CONFIG,
					"Get commonSoftdeleletNotificationMailConfig of process_job_mapping_id: " + process_job_mapping_id);
			commonSoftdeleletNotificationMailConfig(process_job_mapping_id, file_name, adid);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_REPORT_CONFIG,
					"Get commonSoftdeleletNotificationReportConfig of filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_REPORT_CONFIG,
					"Get commonSoftdeleletNotificationReportConfig of process_job_mapping_id: "
							+ process_job_mapping_id);
			commonSoftdeleletNotificationReportConfig(process_job_mapping_id, file_name, adid);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TASK_UPDATE_CONFIG,
					"Get commonSoftdeleletTaskUpdateConfig  filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TASK_UPDATE_CONFIG,
					"Get commonSoftdeleletTaskUpdateConfig process_job_mapping_id: " + process_job_mapping_id);
			commonSoftdeleletTaskUpdateConfig(process_job_mapping_id, file_name, adid);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TICKET_CREATION_CONFIG,
					"Get commonSoftdeleletTicketCreationConfig filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TICKET_CREATION_CONFIG,
					"Get commonSoftdeleletTicketCreationConfig process_job_mapping_id: " + process_job_mapping_id);
			commonSoftdeleletTicketCreationConfig(process_job_mapping_id, file_name, adid);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TBA_MATCH_CONFIG,
					"Get commonSoftdeleteTbaMatchConfig filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TBA_MATCH_CONFIG,
					"Get commonSoftdeleteTbaMatchConfig process_job_mapping_id: " + process_job_mapping_id);
			commonSoftdeleteTbaMatchConfig(process_job_mapping_id, file_name, adid);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_PROCESS_CONTROL_CONFIG,
					"Get commonSoftdeleletProcessControleConfig filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_PROCESS_CONTROL_CONFIG,
					"Get commonSoftdeleletProcessControleConfig process_job_mapping_id: " + process_job_mapping_id);
			commonSoftdeleletProcessControleConfig(process_job_mapping_id, file_name, adid);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_RULE_DEFINITION_CONFIG,
					"Get commonSoftdeleteRuleDifinition filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_RULE_DEFINITION_CONFIG,
					"Get commonSoftdeleteRuleDifinition process_job_mapping_id: " + process_job_mapping_id);
			commonSoftdeleteRuleDifinition(process_job_mapping_id, file_name, adid);

			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_KSD_OUTPUT,
					"Get commonSoftdeleteksdoutput filename: " + file_name);
			LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_KSD_OUTPUT,
					"Get commonSoftdeleteksdoutput process_job_mapping_id: " + process_job_mapping_id);
			commonSoftdeleteksdoutput(process_job_mapping_id, file_name, adid);

			response = true;
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReport",
					"OutPutFileDetails deleted successfully.");
		} catch (Exception e) {
			response = false;
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReport",
					"OutPutFileDetails not deleted successfully.");
		}

		if (response) {
			jobj.addProperty(STATUS, SUCCESS);
			jobj.addProperty(MESSAGE, "Report is deleted successfully.");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReport",
					"OutPutFileDetails deleted successfully.");
			return jobj.toString();
		}
		jobj.addProperty(STATUS, FAILED);
		jobj.addProperty(MESSAGE, "Report is not deleted.");
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReport()", "OutPutFileDetails are not deleted.");
		return jobj.toString();
	}

	public void commonSoftdeleletNotificationMailConfig(long processJobMappingId, String filename, String adid) {
		List<NotificationMailConfig> notificationMailConfigs = notificationMailConfigDao
				.editfileName(processJobMappingId, filename);
		for (NotificationMailConfig notificationMailConfig : notificationMailConfigs) {
			String attchmentName = notificationMailConfig.getAttachmentName();
			String finalList = getstreamData(attchmentName, filename);
			notificationMailConfig.setAttachmentName(finalList);
			notificationdao.save(notificationMailConfig);
		}
	}

	public void commonSoftdeleletNotificationReportConfig(long processJobMappingId, String filename, String adid) {
		List<NotificationReportConfig> notificationReportConfigs = notificationReportConfigDao
				.editfileName(processJobMappingId, filename);
		for (NotificationReportConfig notificationReportConfig : notificationReportConfigs) {
			String attchmentName = notificationReportConfig.getReportName();
			String finalList = getstreamData(attchmentName, filename);
			notificationReportConfig.setReportName(finalList);
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletNotificationReportConfig",
					"Getting notificationReportConfig Id: " + notificationReportConfig.getId());

			notificationReportdao.save(notificationReportConfig);
		}

	}

	public void commonSoftdeleletTaskUpdateConfig(long processJobMappingId, String filename, String adid) {
		List<TaskUpdateConfig> taskUpdateConfigs = taskUpdateConfigDao.editfileName(processJobMappingId, filename);
		LoggerUtil.log(this.getClass(), Level.INFO, "TaskUpdateConfig",
				"Getting TaskUpdateConfig processJobMappingId: " + processJobMappingId);
		for (TaskUpdateConfig taskUpdateConfig : taskUpdateConfigs) {
			String attchmentName = taskUpdateConfig.getAttachment();
			String unsecuredAttachment = taskUpdateConfig.getUnsecuredAttachment();
			List<String> attchmentNameList = new ArrayList<>();
			List<String> unsecuredAttachmentList = new ArrayList<>();
			if (attchmentName != null) {
				attchmentName = attchmentName.replaceAll("\\[", "").replaceAll("\\]", "");
				attchmentNameList = Stream.of(attchmentName.split(","))
						.collect(Collectors.toCollection(ArrayList::new));
			}
			if (unsecuredAttachment != null) {
				unsecuredAttachment = unsecuredAttachment.replaceAll("\\[", "").replaceAll("\\]", "");
				unsecuredAttachmentList = Stream.of(unsecuredAttachment.split(","))
						.collect(Collectors.toCollection(ArrayList::new));
			}
			filename = '"' + filename + '"';
			LoggerUtil.log(this.getClass(), Level.INFO, "getstreamData", "Remove  filename: " + filename);
			if (attchmentName != null || unsecuredAttachment != null) {
				if (attchmentName.contains(filename) || unsecuredAttachment.contains(filename)) {
					attchmentNameList.remove(filename);
					unsecuredAttachmentList.remove(filename);
				}
			}
			String attchmentNameLists = String.join(",", attchmentNameList);
			String unsecuredAttachmentLists = String.join(",", unsecuredAttachmentList);
			String attachment = attchmentNameLists.toString();
			String unsecuredAttachments = unsecuredAttachmentLists.toString();
			taskUpdateConfig.setAttachment(attachment);
			taskUpdateConfig.setUnsecuredAttachment(unsecuredAttachments);
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletTaskUpdateConfig",
					"Getting taskUpdateConfig finalList: " + attachment + unsecuredAttachments);
			taskUpdateConDao.save(taskUpdateConfig);
		}
	}

	public void commonSoftdeleletTicketCreationConfig(long processJobMappingId, String filename, String adid) {
		List<TicketCreationConfig> ticketCreationConfigs = ticketCreationConfigDao.editfileName(processJobMappingId,
				filename);
		LoggerUtil.log(this.getClass(), Level.INFO, "TicketCreationConfig",
				"Getting TicketCreationConfig processJobMappingId: " + processJobMappingId);
		for (TicketCreationConfig ticketCreationConfig : ticketCreationConfigs) {
			String attchmentName = ticketCreationConfig.getAttachment();
			String unsecuredAttachment = ticketCreationConfig.getUnsecuredAttachment();
			attchmentName = attchmentName.replaceAll("\\[", "").replaceAll("\\]", "");
			unsecuredAttachment = unsecuredAttachment.replaceAll("\\[", "").replaceAll("\\]", "");
			List<String> attchmentNameList = Stream.of(attchmentName.split(","))
					.collect(Collectors.toCollection(ArrayList::new));
			List<String> unsecuredAttachmentList = Stream.of(unsecuredAttachment.split(","))
					.collect(Collectors.toCollection(ArrayList::new));
			filename = '"' + filename + '"';
			LoggerUtil.log(this.getClass(), Level.INFO, "getstreamData", "Remove  filename: " + filename);
			if (attchmentName != null || unsecuredAttachment != null) {
				if (attchmentName.contains(filename) || unsecuredAttachment.contains(filename)) {
					attchmentNameList.remove(filename);
					unsecuredAttachmentList.remove(filename);
				}
			}
			String attchmentNameLists = String.join(",", attchmentNameList);
			String unsecuredAttachmentLists = String.join(",", unsecuredAttachmentList);
			String attachment = attchmentNameLists.toString();
			String unsecuredAttachments = unsecuredAttachmentLists.toString();
			ticketCreationConfig.setAttachment(attachment);
			ticketCreationConfig.setUnsecuredAttachment(unsecuredAttachments);
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletTicketCreation",
					"Getting TicketCreationConfig finalList: " + attachment + unsecuredAttachments);
			ticketCreationDao.save(ticketCreationConfig);
		}
	}

	@Transactional
	public void commonSoftdeleteTbaMatchConfig(long processJobMappingId, String filename, String adid) {
		List<TbaMatchConfig> tbaMatch = tbaMatchConfigDao.getRequiredDetailsByTba(processJobMappingId, filename);
		if (tbaMatch.size() > 0) {
			tbaMatchConfigDao.updateByTbaMatchConfigs(adid, processJobMappingId, filename);
		}
	}

	@Transactional
	public void commonSoftdeleletProcessControleConfig(long processJobMappingId, String filename, String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteRuleDifinition",
				"Getting ProcessControleConfig adid: " + adid);
		List<ProcessControlConfig> processControlConfigs = processControlConfigDao
				.getRequiredDetailsByPControl(processJobMappingId);
		if (processControlConfigs.size() > 0) {
			processControlConfigDao.updateByPControlConfigs(adid, processJobMappingId);
		}
	}

	@Transactional
	public void commonSoftdeleteRuleDifinition(long processJobMappingId, String filename, String adid) {

		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteRuleDifinition",
				"Getting rulesDifinition adid: " + adid);
		List<RulesDefinition> rulesDefinitions = rulesDefinitionDao
				.getRequiredDetailsByRuleDifinition(processJobMappingId, filename);
		if (rulesDefinitions.size() > 0) {
			rulesDefinitionDao.updateRequiredDetailsByRulesDififnitions(adid, processJobMappingId, filename);
			rulesConfigDao.commondelete(adid, filename, processJobMappingId);
			rulesConfigDao.commonupdate(adid, processJobMappingId);
		}

	}

	@Transactional
	public void commonSoftdeleteksdoutput(long processJobMappingId, String file_name, String adid) {

		participantRecordIdentifierDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "participantRecordIdentifierDao",
				"Get participantRecordIdentifierDao of filename: " + file_name + adid);
		ksdOutPutFileDetailsDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "ksdOutPutFileDetailsDao",
				"Get ksdOutPutFileDetailsDao of filename: " + file_name + adid);
		outputReportDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "outputReportDao",
				" Get outputReportDao of filename: " + file_name + adid);
		reportFormatDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "reportFormatDao",
				"Get reportFormatDao of filename: " + file_name + adid);
		reportDataCleanseDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "reportDataCleanseDao",
				"Get reportDataCleanseDao of filename: " + file_name + adid);
		reportPivotDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "reportPivotDao",
				"Get reportPivotDao of filename: " + file_name + adid);
	}

	public String getstreamData(String attchmentName, String filename) {
		attchmentName = attchmentName.replaceAll("\\[", "").replaceAll("\\]", "");
		List<String> list = Stream.of(attchmentName.split(",")).collect(Collectors.toCollection(ArrayList::new));
		filename = '"' + filename + '"';
		LoggerUtil.log(this.getClass(), Level.INFO, "getstreamData", "Remove  filename: " + filename);
		list = list.stream().map(String::trim).collect(Collectors.toList());
		if (list.contains(filename)) {
			list.remove(filename);
		}
		String finalList = list.toString();
		return finalList;

	}

}
