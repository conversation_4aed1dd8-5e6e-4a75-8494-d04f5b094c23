package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaCommentInqConfigDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.TbaCommentInquiry;
import com.wipro.fipc.model.generated.TbaCommentInqConfig;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.ITbaCommentInquiryService;
import com.wipro.fipc.tba.service.DbHelperService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class TbaCommentInquiryServiceImpl implements ITbaCommentInquiryService {

	private static final String SPACE_SEPERATOR = " ";
	private static List<com.wipro.fipc.entity.tba.TbaCommentInqConfig> tbamyList;

	@Autowired
	private TbaEventHistoryServiceImpl historyServiceImpl;

	@Autowired
	private Gson gson;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	private DbHelperService dbHelperService;
	
	@Autowired
	TbaCommentInqConfigDao tbaCommentInqiryDao;

	/* Save CommentInquiry Details */

	@Override
	public String saveWithOutDuplicates(List<TbaCommentInquiry> commentList, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		commentList.stream().forEach(s -> {
			s.setCreatedBy(adid);
			s.setCreatedDate(new Date());
			s.setActiveFlag("T");
			s.setUpdatedBy(adid);
			s.setUpdatedDate(new Date());
			LoggerUtil.log(this.getClass(), Level.INFO, "saveWithOutDuplicates>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Save" + ",PJMID = "
							+ s.getProcessJobMapping().getId() + ",ADID: " + adid);
		});
		return saveWithOutDuplicates(commentList);
	}

	/* Save Modify Comment Details */

	@Override
	public String modifyWithOutDuplicates(List<TbaCommentInquiry> commentList, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		commentList.stream().forEach(s -> {
			TbaCommentInquiry dbData = getTbaCommentData("Id", s.getId().toString(), s.getUpdateFlag()).get(0);
			s.setUpdatedBy(adid);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("T");
			String jsonKeyNameAndParName = dbHelperService.getJsonKeyNameAndParName(s.getParNM(), s.getTbaFieldName(), 'F');
			s.setJsonKey(jsonKeyNameAndParName);
			s.setCreatedBy(dbData.getCreatedBy());
			s.setCreatedDate(dbData.getCreatedDate());
			LoggerUtil.log(this.getClass(), Level.INFO, "modifyWithOutDuplicates>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
							+ s.getProcessJobMapping().getId() + ",ADID: " + adid);
		});
		return modifyWithOutDuplicates(commentList);
	}

	/* Delete Common Delete Dto */

	@Override
	public String deleteTbaCommentConfig(List<CommonDeleteDTO> entity, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		gson = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").create();
		entity.stream().forEach(s -> {
			s.setUpdatedBy(adid);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteTbaCommentConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adid);
		});
		return deleteTbaCommentConfig(gson.toJson(entity));
	}

	/* Save Without Duplicates Comment Inquiry */

	@Override
	public String saveWithOutDuplicates(List<TbaCommentInquiry> commentList) {
		String res = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "save()", "TbaCommentInquiryServiceImpl-->save()-->starts:");
		HttpHeaders headers = new HttpHeaders();
		headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.APPLICATION_JSON);
		List<com.wipro.fipc.entity.tba.TbaCommentInqConfig> commentInqConfigList = new ArrayList<>();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		List<TbaCommentInqConfig> commentInqConfigList1 = new ArrayList<>();
		for (TbaCommentInquiry commentInq : commentList) {
			TbaCommentInqConfig commentInqConfig = setTbaCommentInqConfig(commentInq);
			commentInqConfig.setUpdatedDate(new Date());
			commentInqConfigList1.add(commentInqConfig);
		


			
			com.wipro.fipc.entity.tba.TbaCommentInqConfig obj = 
new com.wipro.fipc.entity.tba.TbaCommentInqConfig();
			try {
				obj=objectMapper.convertValue(commentInqConfig, com.wipro.fipc.entity.tba.TbaCommentInqConfig.class);

			} catch (Exception e) {
				
				e.printStackTrace();
			}
			commentInqConfigList.add(obj);
		}
		List<CommonResRowBO> saveIfNotDuplicate = dbHelperService.saveIfNotDuplicate(commentInqConfigList);

		try {
			res = objectMapper.writeValueAsString(saveIfNotDuplicate);
		} catch (JsonProcessingException e1) {
			
			e1.printStackTrace();
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "save()1", "TbaCommentInquiryServiceImpl-->save()-->ends.");
		ResponseDto response = new ResponseDto();
		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		try {
			return objectMapper.writeValueAsString(response);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "saveWithOutDuplicates", "saveWithOutDuplicates Exception : ",
					e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	/* Save Modify WithOutDuplicates */

	@Override
	public String modifyWithOutDuplicates(List<TbaCommentInquiry> commentList) {
		LoggerUtil.log(this.getClass(), Level.INFO, "modify()", "TbaCommentInquiryServiceImpl-->modify()-->starts:");
		String res = "";
		HttpHeaders headers = new HttpHeaders();
		headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.APPLICATION_JSON);
		List<com.wipro.fipc.entity.tba.TbaCommentInqConfig> commentInqConfigList = new ArrayList<>();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		List<TbaCommentInqConfig> commentInqConfigList1 = new ArrayList<>();
		for (TbaCommentInquiry commentInq : commentList) {
			TbaCommentInqConfig commentInqConfig = setTbaCommentInqConfig(commentInq);
			commentInqConfig.setUpdatedDate(new Date());
			commentInqConfigList1.add(commentInqConfig);
		
		
			com.wipro.fipc.entity.tba.TbaCommentInqConfig obj = new com.wipro.fipc.entity.tba.TbaCommentInqConfig();
			try {
				obj=objectMapper.convertValue(commentInqConfig, com.wipro.fipc.entity.tba.TbaCommentInqConfig.class);
			} catch (Exception e) {
				
				e.printStackTrace();
			}
			commentInqConfigList.add(obj);
		}
		List<CommonResRowBO> saveIfNotDuplicate = dbHelperService.saveIfNotDuplicate(commentInqConfigList);

		try {
			res = objectMapper.writeValueAsString(saveIfNotDuplicate);
		} catch (JsonProcessingException e1) {
			
			e1.printStackTrace();
		}
		ResponseDto response = new ResponseDto();

		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Updated Successfully.");
		LoggerUtil.log(this.getClass(), Level.INFO, "modify()1", "TbaCommentInquiryServiceImpl-->modify()-->ends.");

		try {
			return objectMapper.writeValueAsString(response);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "modifyWithOutDuplicates",
					"modifyWithOutDuplicates Exception : ", e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	/* Save the TbaCommentInqConfig */

	private TbaCommentInqConfig setTbaCommentInqConfig(TbaCommentInquiry commentInq) {
		LoggerUtil.log(this.getClass(), Level.INFO, "setTbaCommentInqConfig()",
				"setting comment Inquiry Config from model class starts:");
		TbaCommentInqConfig commentInqConfig = new TbaCommentInqConfig();
		try {
			BeanUtils.copyProperties(commentInqConfig, commentInq);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "setTbaCommentInqConfig()1", "IllegalAccessException: " + e);
		} catch (InvocationTargetException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "setTbaCommentInqConfig()2",
					"InvocationTargetException: " + e);
		}
		commentInqConfig.setFieldType(commentInq.getFieldType());
		commentInqConfig.setTbaFieldName(commentInq.getTbaFieldName());
		if (!commentInq.getUpdateFlag()) {
		String fromDate = commentInq.getEftFromDate().getDatePeriod() + SPACE_SEPERATOR
				+ commentInq.getEftFromDate().getDateFrequency() + SPACE_SEPERATOR
				+ commentInq.getEftFromDate().getDateInterval();
		commentInqConfig.setEftFromDate(fromDate);
		String toDate = commentInq.getEftToDate().getDatePeriod() + SPACE_SEPERATOR
				+ commentInq.getEftToDate().getDateFrequency() + SPACE_SEPERATOR
				+ commentInq.getEftToDate().getDateInterval();
		commentInqConfig.setEftToDate(toDate);
		}
		
		LoggerUtil.log(this.getClass(), Level.INFO, "setTbaCommentInqConfig()3",
				"etting comment Inquiry Config from model class ends.");
		return commentInqConfig;
	}

	/* Fetch the TbaCommentData */
	@Override
	public List<TbaCommentInquiry> getTbaCommentData(String columnName, String columnValue ,boolean updateFlag) {
		String res = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaCommentData()",
				"TbaCommentInqConfig-->getTbaCommentData()-->starts:");
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaCommentData>>>>>>>>>>>", "Method Type= "
				+ HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + columnName + "=" + columnValue);
		try {
			
			List<com.wipro.fipc.entity.tba.TbaCommentInqConfig> tbaCommentInqConfigs = dbHelperService.findByColumnTba(columnName,
					columnValue,updateFlag);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(tbaCommentInqConfigs);
			List<TbaCommentInqConfig> commentInqList = Arrays
					.asList(new ObjectMapper().readValue(res, TbaCommentInqConfig[].class));
			List<TbaCommentInquiry> commentList = this.setTbaCommentInquiry(commentInqList);
			LoggerUtil.log(this.getClass(), Level.INFO, "getTbaCommentData()1",
					"TbaCommentInqConfig-->getTbaCommentData()--ends.");
			return commentList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaCommentData()2",
					"TbaCommentInqConfig-->getTbaCommentData()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}


	/* setTbaCommentInquiry */

	public List<TbaCommentInquiry> setTbaCommentInquiry(List<TbaCommentInqConfig> commentInqConfigList) {
		LoggerUtil.log(this.getClass(), Level.INFO, "setTbaCommentInquiry()", "setting tba comment from config starts");
		List<TbaCommentInquiry> commentInqList = new ArrayList<>();
		for (TbaCommentInqConfig commentInqConfig : commentInqConfigList) {
			TbaCommentInquiry obj = null;
			obj = this.getTbaCommentInquiry(commentInqConfig);
			if(commentInqConfig.getEftFromDate()!=null)
			obj.setEftFromDate(historyServiceImpl.splitEffectiveDateBySpace(commentInqConfig.getEftFromDate()));
			if(commentInqConfig.getEftToDate()!=null)
			obj.setEftToDate(historyServiceImpl.splitEffectiveDateBySpace(commentInqConfig.getEftToDate()));
			commentInqList.add(obj);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "setTbaCommentInquiry()", "setting tba comment from config ends");
		return commentInqList;
	}

	/* Get TbaCommentInquiry */

	private TbaCommentInquiry getTbaCommentInquiry(TbaCommentInqConfig commentInqConfig) {
		TbaCommentInquiry commentInq = new TbaCommentInquiry();
		if (commentInqConfig.getActiveFlag() != null) {
			commentInq.setActiveFlag(commentInqConfig.getActiveFlag());
		}
		if (commentInqConfig.getCreatedBy() != null) {
			commentInq.setCreatedBy(commentInqConfig.getCreatedBy());
		}
		if (commentInqConfig.getCreatedDate() != null) {
			commentInq.setCreatedDate(commentInqConfig.getCreatedDate());
		}
		if (commentInqConfig.getFieldType() != null) {
			commentInq.setFieldType(commentInqConfig.getFieldType());
		}
		if (commentInqConfig.getId() != null) {
			commentInq.setId(commentInqConfig.getId());
		}
		if (commentInqConfig.getInquiryDefName() != null) {
			commentInq.setInquiryDefName(commentInqConfig.getInquiryDefName());
		}
		if (commentInqConfig.getJsonKey() != null) {
			commentInq.setJsonKey(commentInqConfig.getJsonKey());
		}
		if (commentInqConfig.getParNM() != null) {
			commentInq.setParNM(commentInqConfig.getParNM());
		}
		if (commentInqConfig.getProcessJobMapping() != null) {
			commentInq.setProcessJobMapping(commentInqConfig.getProcessJobMapping());
		}
		if (commentInqConfig.getSubJsonKey() != null) {
			commentInq.setSubJsonKey(commentInqConfig.getSubJsonKey());
		}
		if (commentInqConfig.getTbaFieldName() != null) {
			commentInq.setTbaFieldName(commentInqConfig.getTbaFieldName());
		}
		if (commentInqConfig.getUpdatedBy() != null) {
			commentInq.setUpdatedBy(commentInqConfig.getUpdatedBy());
		}
		if (commentInqConfig.getUpdatedDate() != null) {
			commentInq.setUpdatedDate(commentInqConfig.getUpdatedDate());
		}

		return commentInq;
	}

	/* Delete Tba CommentConfig */

	@Override
	public String deleteTbaCommentConfig(String entityList) {

		String res = "";
		ObjectMapper mapper = new ObjectMapper();

		try {
			com.wipro.fipc.entity.tba.TbaCommentInqConfig[] obj = mapper.readValue(entityList,
					com.wipro.fipc.entity.tba.TbaCommentInqConfig[].class);
			tbamyList = new ArrayList<com.wipro.fipc.entity.tba.TbaCommentInqConfig>(Arrays.asList(obj));

		} catch (IOException e1) {
			e1.printStackTrace();
		}

		List<CommonRowBO> r = dbHelperService.deletemultiplerows(1, tbamyList);
		try {
			ObjectMapper objectMapper=new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			res = objectMapper.writeValueAsString(r);
		} catch (JsonProcessingException e) {
			
			throw new BusinessException("URL is NULL");

		}
		return res;

	}
}
