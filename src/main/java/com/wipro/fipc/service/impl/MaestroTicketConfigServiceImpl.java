package com.wipro.fipc.service.impl;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.GenericRestService;
import com.wipro.fipc.dao.KsdConfigDao;
import com.wipro.fipc.dao.MaestroTaskUpdateConfigRepo;
import com.wipro.fipc.dao.MaestroTicketConfigRepo;
import com.wipro.fipc.dao.filelayout.ProcessFeatureConfigDao;
import com.wipro.fipc.dao.filelayout.TaskUpdateConfigDao;
import com.wipro.fipc.dao.maestro.TicketCreationConfigDao;
import com.wipro.fipc.entity.batch.KsdConfig;
import com.wipro.fipc.entity.filelayout.TaskUpdateConfig;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;
import com.wipro.fipc.model.ConfigStatusBO;
import com.wipro.fipc.model.MaestroTaskNameBO;
import com.wipro.fipc.model.MaestroTicketTaskConfig;
import com.wipro.fipc.model.TaskUpdateConfigCustom;
import com.wipro.fipc.model.TicketCreationConfigCustom;
import com.wipro.fipc.model.generated.ConfigStatusApproveBO;
import com.wipro.fipc.service.MaestroTicketConfigService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class MaestroTicketConfigServiceImpl implements MaestroTicketConfigService {
	@Autowired
	Environment env;
	@Autowired
	GenericDao<com.wipro.fipc.entity.maestro.TicketCreationConfig> ticketdaoGeneric;
	@Autowired
	MaestroTaskUpdateConfigRepo genericUpdate;
	@Autowired
	MaestroTicketConfigRepo generisRestdao;
	@Autowired
	GenericDao<com.wipro.fipc.entity.filelayout.TaskUpdateConfig> taskdaoGeneric;
	@Autowired
	CommonGetAdId commonGetUpdatedBy;
	@Autowired
	KsdConfigDao ksdConfigDao;

	GenericRestService<TicketCreationConfig> genericService = new GenericRestService();

	@Autowired
	TicketCreationConfigDao ticketCreationConfigDao;

	GenericRestService<TaskUpdateConfig> genericService1 = new GenericRestService();

	@Autowired
	ProcessFeatureConfigDao processFeatureConfigDao;

	@Autowired
	TaskUpdateConfigDao taskUpdateConfigDao;

	public static final String FALSE = "false";

	@Override
	public List<TicketCreationConfig> getAllMaestroTicketConfig() {
		List<TicketCreationConfig> ticketCreationConfigData = new ArrayList<>();
		try {

			List<com.wipro.fipc.entity.maestro.TicketCreationConfig> responseticketCreation = generisRestdao.list();
			ObjectMapper mapper = new ObjectMapper();

			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			for (com.wipro.fipc.entity.maestro.TicketCreationConfig data : responseticketCreation) {
				TicketCreationConfig myList1 = mapper.convertValue(data, TicketCreationConfig.class);
				ticketCreationConfigData.add(myList1);
			}

			return ticketCreationConfigData;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "MaestroTicketConfigServiceImpl30",
					"getAllMaestroTicketConfig  :  " + e);
		}

		return new ArrayList<>();
	}

	@Override
	public List<TaskUpdateConfig> getAllMaestroTaskUpdateClosureConfig() {
		List<TaskUpdateConfig> taskUpdateConfigData = new ArrayList<>();
		try {

			List<com.wipro.fipc.entity.filelayout.TaskUpdateConfig> responseticketCreation = genericUpdate.list();
			ObjectMapper mapper = new ObjectMapper();
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			for (com.wipro.fipc.entity.filelayout.TaskUpdateConfig data : responseticketCreation) {
				TaskUpdateConfig myList1 = mapper.convertValue(data, TaskUpdateConfig.class);
				taskUpdateConfigData.add(myList1);
			}
			return taskUpdateConfigData;

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "MaestroTicketConfigServiceImpl31",
					"getAllMaestroTaskUpdateClosureConfig  :  " + e);
		}

		return new ArrayList<>();
	}

	protected static final String TICKET_CREATION_CONFIG = "ticket_creation_config";
	protected static final String MAESTRO_SCHEMA = "maestro";

	@Override
	public List<TicketCreationConfig> getByColumnMaestroTicketConfig(String columnName, String columnValue) {
		try {
			List<TicketCreationConfig> myList = new ArrayList<>();

			List<com.wipro.fipc.entity.maestro.TicketCreationConfig> responseData = ticketdaoGeneric.findByColumn(
					com.wipro.fipc.entity.maestro.TicketCreationConfig.class, MAESTRO_SCHEMA, TICKET_CREATION_CONFIG,
					columnName, columnValue);

			ObjectMapper mapper = new ObjectMapper();
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

			for (com.wipro.fipc.entity.maestro.TicketCreationConfig data : responseData) {
				TicketCreationConfig myList1 = mapper.convertValue(data, TicketCreationConfig.class);
				myList.add(myList1);
			}

			return myList;

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "MaestroTicketConfigServiceImpl32",
					"getByColumnMaestroTicketConfig  :  " + e);
		}

		return new ArrayList<>();
	}

	protected static final String TASK_UPDATE_CONFIG = "task_update_config";

	@Override
	public List<TaskUpdateConfig> getByColumnMaestroTaskConfig(String columnName, String columnValue) {
		try {

			List<TaskUpdateConfig> myList = new ArrayList<>();

			List<com.wipro.fipc.entity.filelayout.TaskUpdateConfig> responseData = taskdaoGeneric.findByColumn(
					com.wipro.fipc.entity.filelayout.TaskUpdateConfig.class, MAESTRO_SCHEMA, TASK_UPDATE_CONFIG,
					columnName, columnValue);
			ObjectMapper mapper = new ObjectMapper();
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

			for (com.wipro.fipc.entity.filelayout.TaskUpdateConfig data : responseData) {
				TaskUpdateConfig myList1 = mapper.convertValue(data, TaskUpdateConfig.class);
				myList.add(myList1);
			}
			return myList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "MaestroTicketConfigServiceImpl33",
					"getByColumnMaestroTaskConfig  :  " + e);
		}
		return new ArrayList<>();
	}

	public MaestroTaskNameBO getMaestroTaskNameFrmKsdConfig(String columnName, String columnValue) {
		MaestroTaskNameBO maestroTaskNameBO = new MaestroTaskNameBO();
		try {
			long pjmId = Long.parseLong(columnValue);
			KsdConfig responseOfKsdConfig = ksdConfigDao.getKsdConfigdata(pjmId);
			if(responseOfKsdConfig!=null) {
			String maestroTaskName = responseOfKsdConfig.getMaestroTaskName();
			maestroTaskName=maestroTaskName!=null?maestroTaskName.trim():maestroTaskName;
			String maestroTaskType = responseOfKsdConfig.getMaestroTaskType();
			maestroTaskType=maestroTaskType!=null?maestroTaskType.trim():maestroTaskType;
			maestroTaskNameBO.setMaestroTaskName(maestroTaskName);
			maestroTaskNameBO.setMaestroTaskType(maestroTaskType);
			LoggerUtil.log(this.getClass(), Level.INFO, "getMaestroTaskNameFrmKsdConfig",
					"MaestroTaskName :  " + maestroTaskName + " ," + "MestroTaskType : " + maestroTaskType);
			}
			maestroTaskNameBO.setProcessJobMappingId(pjmId);
			getConfigStatusFrmProcessFeatureConfig(maestroTaskNameBO, columnName, columnValue);
			return maestroTaskNameBO;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "MaestroTicketConfigServiceImpl1",
					"getMaestroTaskNameFrmKsdConfig " + e);
		}

		return maestroTaskNameBO;
	}

	public void getConfigStatusFrmProcessFeatureConfig(MaestroTaskNameBO maestroTaskNameBO, String columnName,
			String columnValue) {
		

		String configStatusResp = processFeatureConfigDao.getConfigStatus(Long.parseLong(columnValue));

		LoggerUtil.log(this.getClass(), Level.INFO, "getMaestroTaskNameFrmKsdConfig5",
				"configStatusResp  :  " + configStatusResp);
		if (configStatusResp != null && !configStatusResp.equalsIgnoreCase("")) {

			maestroTaskNameBO.setConfigStatus(configStatusResp);

		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, "getMaestroTaskNameFrmKsdConfig6",
					"str configStatus is not in process_feature_config ");

		}

	}

	@Override
	public MaestroTicketTaskConfig getByColumnMaestroTikcetTask(@PathVariable String columnName,
			@PathVariable String columnValue) {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		MaestroTicketTaskConfig ticketCreationUpdateConfigResp = new MaestroTicketTaskConfig();
		try {
			List<TicketCreationConfig> listTicketCreationConfigResp = getByColumnMaestroTicketConfig(columnName,
					columnValue);
			List<TaskUpdateConfig> listcreateMaestroTaskUpdateClosureResp = getByColumnMaestroTaskConfig(columnName,
					columnValue);

			MaestroTaskNameBO maestroTaskNameBO = getMaestroTaskNameFrmKsdConfig(columnName, columnValue);

			List<TicketCreationConfigCustom> listTicketCreationConfigCustom = new ArrayList<>();
			List<TaskUpdateConfigCustom> listTaskUpdateConfigCustom = new ArrayList<>();

			for (TicketCreationConfig ticketCreationConfig : listTicketCreationConfigResp) {
				String attachment = ticketCreationConfig.getAttachment();
				String unsecuredAttachment = ticketCreationConfig.getUnsecuredAttachment();
				String interestedFields = ticketCreationConfig.getInterestedFields();
				ticketCreationConfig.setAttachment(null);
				ticketCreationConfig.setUnsecuredAttachment(null);
				ticketCreationConfig.setInterestedFields(null);
				String res1 = mapper.writeValueAsString(ticketCreationConfig);
				mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
				TicketCreationConfigCustom ticketCreationConfigCustom = mapper.readValue(res1,
						TicketCreationConfigCustom.class);
				ticketCreationConfig.setAttachment(attachment);
				ticketCreationConfig.setUnsecuredAttachment(unsecuredAttachment);
				ticketCreationConfig.setInterestedFields(interestedFields);

				beanUpdateForTicketConfigResp(ticketCreationConfig, ticketCreationConfigCustom);
				listTicketCreationConfigCustom.add(ticketCreationConfigCustom);
			}

			for (TaskUpdateConfig taskUpdateConfig : listcreateMaestroTaskUpdateClosureResp) {
				String attachment = taskUpdateConfig.getAttachment();
				String unsecuredAttachment = taskUpdateConfig.getUnsecuredAttachment();
				String interestedFields = taskUpdateConfig.getInterestedFields();
				taskUpdateConfig.setAttachment(null);
				taskUpdateConfig.setUnsecuredAttachment(null);
				taskUpdateConfig.setInterestedFields(null);
				String res2 = mapper.writeValueAsString(taskUpdateConfig);
				TaskUpdateConfigCustom taskUpdateConfigCustom = mapper.readValue(res2, TaskUpdateConfigCustom.class);
				taskUpdateConfig.setAttachment(attachment);
				taskUpdateConfig.setUnsecuredAttachment(unsecuredAttachment);
				taskUpdateConfig.setInterestedFields(interestedFields);

				beanUpdateForTaskConfigResp(taskUpdateConfig, taskUpdateConfigCustom);

				listTaskUpdateConfigCustom.add(taskUpdateConfigCustom);
			}

			ticketCreationUpdateConfigResp.setListTicketCreationConfigCustom(listTicketCreationConfigCustom);
			ticketCreationUpdateConfigResp.setListTaskUpdateConfigCustom(listTaskUpdateConfigCustom);
			ticketCreationUpdateConfigResp.setMaestroTaskNameBO(maestroTaskNameBO);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "MaestroTicketConfigServiceImpl1",
					"getByColumnMaestroTikcetTask " + e);
		}
		return ticketCreationUpdateConfigResp;

	}

	@Override
	public MaestroTicketTaskConfig getAllMaestroTicketTask() {
		try {
			List<TicketCreationConfig> listTicketCreationConfigResp = getAllMaestroTicketConfig();
			List<TaskUpdateConfig> listcreateMaestroTaskUpdateClosureResp = getAllMaestroTaskUpdateClosureConfig();
			List<TicketCreationConfigCustom> listTicketCreationConfigCustom = new ArrayList<>();
			List<TaskUpdateConfigCustom> listTaskUpdateConfigCustom = new ArrayList<>();

			for (TicketCreationConfig ticketCreationConfig : listTicketCreationConfigResp) {

				TicketCreationConfigCustom ticketCreationConfigCustom = new TicketCreationConfigCustom();
				BeanUtils.copyProperties(ticketCreationConfigCustom, ticketCreationConfig);
				beanUpdateForTicketConfigResp(ticketCreationConfig, ticketCreationConfigCustom);
				listTicketCreationConfigCustom.add(ticketCreationConfigCustom);
			}

			for (TaskUpdateConfig taskUpdateConfig : listcreateMaestroTaskUpdateClosureResp) {

				TaskUpdateConfigCustom taskUpdateConfigCustom = new TaskUpdateConfigCustom();
				BeanUtils.copyProperties(taskUpdateConfigCustom, taskUpdateConfig);
				beanUpdateForTaskConfigResp(taskUpdateConfig, taskUpdateConfigCustom);
				listTaskUpdateConfigCustom.add(taskUpdateConfigCustom);
			}

			MaestroTicketTaskConfig ticketCreationUpdateConfigResp = new MaestroTicketTaskConfig();
			ticketCreationUpdateConfigResp.setListTicketCreationConfigCustom(listTicketCreationConfigCustom);
			ticketCreationUpdateConfigResp.setListTaskUpdateConfigCustom(listTaskUpdateConfigCustom);

			return ticketCreationUpdateConfigResp;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "MaestroTicketConfigServiceImpl34",
					"getAllMaestroTikcetTaskImpl " + e);
		}
		return null;
	}

	public List<String> createUpdateMaestroTicketConfig(MaestroTicketTaskConfig ticketCreationUpdateConfig) {

		List<String> listCeateUpdateTicketTaskResp = new ArrayList<>();
		try {
			List<TicketCreationConfigCustom> listTicketCreationConfigCustomReq = ticketCreationUpdateConfig
					.getListTicketCreationConfigCustom();
			List<TaskUpdateConfigCustom> listcreateMaestroTaskUpdateClosureCustomReq = ticketCreationUpdateConfig
					.getListTaskUpdateConfigCustom();
			MaestroTaskNameBO maestroTaskNameBOReq = ticketCreationUpdateConfig.getMaestroTaskNameBO();

			List<com.wipro.fipc.model.generated.TicketCreationConfig> listTicketCreationConfigReq = new ArrayList<>();
			List<com.wipro.fipc.model.generated.TaskUpdateConfig> listTaskUpdateConfigReq = new ArrayList<>();

			for (TicketCreationConfigCustom ticketCreationConfigCustom : listTicketCreationConfigCustomReq) {

				com.wipro.fipc.model.generated.TicketCreationConfig ticketCreationConfig = new com.wipro.fipc.model.generated.TicketCreationConfig();
				BeanUtils.copyProperties(ticketCreationConfig, ticketCreationConfigCustom);
				beanUpdateForTicketConfigReq(ticketCreationConfigCustom, ticketCreationConfig);

				listTicketCreationConfigReq.add(ticketCreationConfig);
			}

			for (TaskUpdateConfigCustom taskUpdateConfigCustom : listcreateMaestroTaskUpdateClosureCustomReq) {

				com.wipro.fipc.model.generated.TaskUpdateConfig taskUpdateConfig = new com.wipro.fipc.model.generated.TaskUpdateConfig();
				BeanUtils.copyProperties(taskUpdateConfig, taskUpdateConfigCustom);
				beanUpdateForTaskConfigReq(taskUpdateConfigCustom, taskUpdateConfig);
				listTaskUpdateConfigReq.add(taskUpdateConfig);
			}

			listCeateUpdateTicketTaskResp = createUpdateMaestroAppConfigImplRestp(listTicketCreationConfigReq,
					listTaskUpdateConfigReq, maestroTaskNameBOReq);
			LoggerUtil.log(this.getClass(), Level.INFO, "createUpdateMaestroAppConfigImpl",
					"listCeateUpdateTicketTaskResp :  " + listCeateUpdateTicketTaskResp);
			return listCeateUpdateTicketTaskResp;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "createUpdateMaestroTicketConfigImpl", "Error1 " + e);
		}
		return listCeateUpdateTicketTaskResp;
	}

	// new vapt post api
	public List<String> newcreateUpdateMaestroTicketConfig(MaestroTicketTaskConfig ticketCreationUpdateConfig,
			String appName, String sessionToken, String pjmId) {

		List<String> listCeateUpdateTicketTaskResp = new ArrayList<>();
		try {
			List<TicketCreationConfigCustom> listTicketCreationConfigCustomReq = ticketCreationUpdateConfig
					.getListTicketCreationConfigCustom();
			List<TaskUpdateConfigCustom> listcreateMaestroTaskUpdateClosureCustomReq = ticketCreationUpdateConfig
					.getListTaskUpdateConfigCustom();
			MaestroTaskNameBO maestroTaskNameBOReq = ticketCreationUpdateConfig.getMaestroTaskNameBO();

			List<com.wipro.fipc.model.generated.TicketCreationConfig> listTicketCreationConfigReq = new ArrayList<>();
			List<com.wipro.fipc.model.generated.TaskUpdateConfig> listTaskUpdateConfigReq = new ArrayList<>();

			for (TicketCreationConfigCustom ticketCreationConfigCustom : listTicketCreationConfigCustomReq) {

				com.wipro.fipc.model.generated.TicketCreationConfig ticketCreationConfig = new com.wipro.fipc.model.generated.TicketCreationConfig();
				BeanUtils.copyProperties(ticketCreationConfig, ticketCreationConfigCustom);

				String adid = commonGetUpdatedBy.getADID(appName, sessionToken);

				if (ticketCreationConfig.getId() == null || ticketCreationConfig.getId() == 0) {
					LoggerUtil.log(this.getClass(), Level.INFO, "newcreateUpdateMaestroTicketConfig method -> MAESTRO ",
							"pjmid = " + pjmId + " adid = " + adid + "action is CREATE");
					ticketCreationConfig.setCreatedBy(adid);
					ticketCreationConfig.setUpdatedBy(adid);
					ticketCreationConfig.setUpdatedDate(new Date());
				} else {

					TicketCreationConfig dbData = getByColumnMaestroTicketConfig("id",
							ticketCreationConfig.getId().toString()).get(0);
					LoggerUtil.log(this.getClass(), Level.INFO, "newcreateUpdateMaestroTicketConfig method -> MAESTRO ",
							"pjmid = " + pjmId + " adid = " + adid + "action is UPDATE" + " ID ="
									+ ticketCreationConfig.getId().toString());
					ticketCreationConfig.setCreatedBy(dbData.getCreatedBy());
					ticketCreationConfig.setCreatedDate(dbData.getCreatedDate());
					ticketCreationConfig.setUpdatedBy(adid);
				}
				ticketCreationConfig.setActiveFlag("T");

				beanUpdateForTicketConfigReq(ticketCreationConfigCustom, ticketCreationConfig);

				listTicketCreationConfigReq.add(ticketCreationConfig);
			}

			for (TaskUpdateConfigCustom taskUpdateConfigCustom : listcreateMaestroTaskUpdateClosureCustomReq) {

				com.wipro.fipc.model.generated.TaskUpdateConfig taskUpdateConfig = new com.wipro.fipc.model.generated.TaskUpdateConfig();
				BeanUtils.copyProperties(taskUpdateConfig, taskUpdateConfigCustom);
				String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
				if (taskUpdateConfig.getId() == null || taskUpdateConfig.getId() == 0) {
					LoggerUtil.log(this.getClass(), Level.INFO,
							"newcreateUpdateMaestroTicketConfig method -> for MAESTRO ",
							"pjmid = " + pjmId + " adid = " + adid + "action = CREATE" + " maestroTaskName ="
									+ taskUpdateConfig.getMaestroTaskName());
					taskUpdateConfig.setCreatedBy(adid);
					taskUpdateConfig.setUpdatedBy(adid);
					taskUpdateConfig.setUpdatedDate(new Date());
				} else {
					LoggerUtil.log(this.getClass(), Level.INFO,
							"newcreateUpdateMaestroTicketConfig method -> for MAESTRO ", "pjmid = " + pjmId + " adid = "
									+ adid + "action = UPDATE " + "ID =" + taskUpdateConfig.getId().toString());

					TaskUpdateConfig dbData = getByColumnMaestroTaskConfig("id", taskUpdateConfig.getId().toString())
							.get(0);
					taskUpdateConfig.setCreatedBy(dbData.getCreatedBy());
					taskUpdateConfig.setCreatedDate(dbData.getCreatedDate());
					taskUpdateConfig.setUpdatedBy(adid);
				}

				taskUpdateConfig.setActiveFlag("T");
				beanUpdateForTaskConfigReq(taskUpdateConfigCustom, taskUpdateConfig);
				listTaskUpdateConfigReq.add(taskUpdateConfig);
			}

			listCeateUpdateTicketTaskResp = createUpdateMaestroAppConfigImplRestp(listTicketCreationConfigReq,
					listTaskUpdateConfigReq, maestroTaskNameBOReq);
			return listCeateUpdateTicketTaskResp;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "createUpdateMaestroTicketConfigImpl", "Error1 " + e);
		}
		return listCeateUpdateTicketTaskResp;
	}

	@Override
	public List<String> deleteMaestroTicketConfig(MaestroTicketTaskConfig ticketCreationUpdateConfig, String appName,
			String sessionToken, String pjmId) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);

		List<String> listCeateUpdateTicketTaskResp = new ArrayList<>();
		try {
			List<TicketCreationConfigCustom> listTicketCreationConfigCustomReq = ticketCreationUpdateConfig
					.getListTicketCreationConfigCustom();
			List<TaskUpdateConfigCustom> listcreateMaestroTaskUpdateClosureCustomReq = ticketCreationUpdateConfig
					.getListTaskUpdateConfigCustom();
			MaestroTaskNameBO maestroTaskNameBOReq = ticketCreationUpdateConfig.getMaestroTaskNameBO();

			List<com.wipro.fipc.model.generated.TicketCreationConfig> listTicketCreationConfigReq = new ArrayList<>();
			List<com.wipro.fipc.model.generated.TaskUpdateConfig> listTaskUpdateConfigReq = new ArrayList<>();

			for (TicketCreationConfigCustom ticketCreationConfigCustom : listTicketCreationConfigCustomReq) {
				com.wipro.fipc.model.generated.TicketCreationConfig ticketCreationConfig = new com.wipro.fipc.model.generated.TicketCreationConfig();
				BeanUtils.copyProperties(ticketCreationConfig, ticketCreationConfigCustom);
				LoggerUtil.log(this.getClass(), Level.INFO, "deleteMaestroTicketConfig method -> for MAESTRO ",
						"pjmid = " + pjmId + " adid = " + adid + "action = DELETE " + "ID ="
								+ ticketCreationConfig.getId().toString());

				TicketCreationConfig dbData = getByColumnMaestroTicketConfig("id",
						ticketCreationConfig.getId().toString()).get(0);
				ticketCreationConfig.setCreatedBy(dbData.getCreatedBy());
				ticketCreationConfig.setCreatedDate(dbData.getCreatedDate());
				ticketCreationConfig.setUpdatedBy(adid);

				ticketCreationConfig.setActiveFlag("F");

				beanUpdateForTicketConfigReq(ticketCreationConfigCustom, ticketCreationConfig);

				listTicketCreationConfigReq.add(ticketCreationConfig);
			}

			for (TaskUpdateConfigCustom taskUpdateConfigCustom : listcreateMaestroTaskUpdateClosureCustomReq) {

				com.wipro.fipc.model.generated.TaskUpdateConfig taskUpdateConfig = new com.wipro.fipc.model.generated.TaskUpdateConfig();
				BeanUtils.copyProperties(taskUpdateConfig, taskUpdateConfigCustom);
				LoggerUtil.log(this.getClass(), Level.INFO, "deleteMaestroTicketConfig method -> for MAESTRO ",
						"pjmid = " + pjmId + " adid = " + adid + "action = DELETE " + "ID ="
								+ taskUpdateConfig.getId().toString());

				TaskUpdateConfig dbData = getByColumnMaestroTaskConfig("id", taskUpdateConfig.getId().toString())
						.get(0);
				taskUpdateConfig.setCreatedBy(dbData.getCreatedBy());
				taskUpdateConfig.setCreatedDate(dbData.getCreatedDate());
				taskUpdateConfig.setUpdatedBy(adid);

				taskUpdateConfig.setActiveFlag("F");
				beanUpdateForTaskConfigReq(taskUpdateConfigCustom, taskUpdateConfig);
				listTaskUpdateConfigReq.add(taskUpdateConfig);
			}

			listCeateUpdateTicketTaskResp = createUpdateMaestroAppConfigImplRestp(listTicketCreationConfigReq,
					listTaskUpdateConfigReq, maestroTaskNameBOReq);
			return listCeateUpdateTicketTaskResp;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "deleteMaestroTicketConfig", "Error1 " + e);
		}
		return listCeateUpdateTicketTaskResp;
	}

	private void beanUpdateForTaskConfigReq(TaskUpdateConfigCustom taskUpdateConfigCustom,
			com.wipro.fipc.model.generated.TaskUpdateConfig taskUpdateConfig) {
		try {
			String[] attachmentUI = taskUpdateConfigCustom.getAttachment();
			LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl3",
					"beanUpdateForTaskConfigReq attachmentUI req :  " + attachmentUI);

			if (attachmentUI != null) {

				String attachmentDB = String.join(",", attachmentUI);
				LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl4",
						"beanUpdateForTaskConfigReq attachmentDB req :  " + attachmentDB);
				taskUpdateConfig.setAttachment(attachmentDB);
			}
			String[] interestedFieldsUI = taskUpdateConfigCustom.getInterestedFields();
			LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl5",
					"beanUpdateForTaskConfigReq interestedFieldsUI req :  " + interestedFieldsUI);

			if (interestedFieldsUI != null) {

				String interestedFieldsDB = String.join(",", interestedFieldsUI);
				LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl6",
						"beanUpdateForTaskConfigReq interestedFieldsDB req :  " + interestedFieldsDB);
				taskUpdateConfig.setInterestedFields(interestedFieldsDB);
			}
			String[] unsecuredAttachmentUI = taskUpdateConfigCustom.getUnsecuredAttachment();
			LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl7",
					"beanUpdateForTaskConfigReq unsecuredAttachmentUI req :  " + unsecuredAttachmentUI);

			if (unsecuredAttachmentUI != null) {

				String unsecuredAttachmentDB = String.join(",", unsecuredAttachmentUI);
				taskUpdateConfig.setUnsecuredAttachment(unsecuredAttachmentDB);
			}
			if (taskUpdateConfig.getId() == null || taskUpdateConfig.getId() == 0) {
				taskUpdateConfig.setCreatedDate(new Date());
			} else {
				taskUpdateConfig.setUpdatedDate(new Date());
			}

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "beanUpdateForTaskConfigReq", "Error2 " + e);
		}

	}

	private void beanUpdateForTicketConfigReq(TicketCreationConfigCustom ticketCreationConfigCustom,
			com.wipro.fipc.model.generated.TicketCreationConfig ticketCreationConfig) {

		try {
			String[] attachmentUI = ticketCreationConfigCustom.getAttachment();

			if (attachmentUI != null) {

				String attachmentDB = String.join(",", attachmentUI);
				ticketCreationConfig.setAttachment(attachmentDB);
			}
			String[] interestedFieldsUI = ticketCreationConfigCustom.getInterestedFields();
			if (interestedFieldsUI != null) {

				String interestedFieldsDB = String.join(",", interestedFieldsUI);
				ticketCreationConfig.setInterestedFields(interestedFieldsDB);
			}

			String[] unsecuredAttachmentUI = ticketCreationConfigCustom.getUnsecuredAttachment();
			if (unsecuredAttachmentUI != null) {

				String unsecuredAttachmentDB = String.join(",", unsecuredAttachmentUI);
				ticketCreationConfig.setUnsecuredAttachment(unsecuredAttachmentDB);
			}

			if (ticketCreationConfig.getId() == null || ticketCreationConfig.getId() == 0) {
				ticketCreationConfig.setCreatedDate(new Date());
			} else {
				ticketCreationConfig.setUpdatedDate(new Date());
			}

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR,
					"MaestroTicketConfigServiceImpl : beanUpdateForTicketConfigReq", "Error3 " + e);
		}

	}

	private void beanUpdateForTaskConfigResp(TaskUpdateConfig taskUpdateConfig,
			TaskUpdateConfigCustom taskUpdateConfigCustom) {
		try {

			String attachmentDBStr = taskUpdateConfig.getAttachment();
			if (attachmentDBStr != null && !attachmentDBStr.equals("")) {
				String[] attachmentUI = attachmentDBStr.split(",");
				LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl16",
						"beanUpdateForTaskConfigResp attachmentUI resp :  " + attachmentUI);
				taskUpdateConfigCustom.setAttachment(attachmentUI);
			} else
				taskUpdateConfigCustom.setAttachment(new String[0]);
			String interestedFieldsDBStr = taskUpdateConfig.getInterestedFields();
			if (interestedFieldsDBStr != null && !interestedFieldsDBStr.equals("")) {
				String[] interestedFieldsUI = interestedFieldsDBStr.split(",");
				LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl18",
						"beanUpdateForTaskConfigResp interestedFieldsUI resp :  " + interestedFieldsUI);
				taskUpdateConfigCustom.setInterestedFields(interestedFieldsUI);
			} else
				taskUpdateConfigCustom.setInterestedFields(new String[0]);
			String unsecuredAttachmentDBStr = taskUpdateConfig.getUnsecuredAttachment();
			if (unsecuredAttachmentDBStr != null && !unsecuredAttachmentDBStr.equals("")) {
				String[] unsecuredAttachmentUI = unsecuredAttachmentDBStr.split(",");
				LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigServiceImpl20",
						"beanUpdateForTaskConfigResp unsecuredAttachmentUI resp :  " + unsecuredAttachmentUI);
				taskUpdateConfigCustom.setUnsecuredAttachment(unsecuredAttachmentUI);
			} else
				taskUpdateConfigCustom.setUnsecuredAttachment(new String[0]);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "beanUpdateForTaskConfigResp", "Error4 " + e);
		}

	}

	private void beanUpdateForTicketConfigResp(TicketCreationConfig ticketCreationConfig,
			TicketCreationConfigCustom ticketCreationConfigCustom) {
		try {

			String attachmentDBStr = ticketCreationConfig.getAttachment();
			if (attachmentDBStr != null && !attachmentDBStr.equals("")) {
				String[] attachmentUI = attachmentDBStr.split(",");
				ticketCreationConfigCustom.setAttachment(attachmentUI);
			} else
				ticketCreationConfigCustom.setAttachment(new String[0]);
			String interestedFieldsDBStr = ticketCreationConfig.getInterestedFields();
			if (interestedFieldsDBStr != null && !interestedFieldsDBStr.equals("")) {
				String[] interestedFieldsUI = interestedFieldsDBStr.split(",");
				ticketCreationConfigCustom.setInterestedFields(interestedFieldsUI);
			} else
				ticketCreationConfigCustom.setInterestedFields(new String[0]);

			String unsecuredAttachmentDBStr = ticketCreationConfig.getUnsecuredAttachment();
			if (unsecuredAttachmentDBStr != null && !unsecuredAttachmentDBStr.equals("")) {
				String[] unsecuredAttachmentUI = unsecuredAttachmentDBStr.split(",");
				ticketCreationConfigCustom.setUnsecuredAttachment(unsecuredAttachmentUI);
			} else
				ticketCreationConfigCustom.setUnsecuredAttachment(new String[0]);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR,
					"MaestroTicketConfigServiceImpl :beanUpdateForTicketConfigResp", "Error5 " + e);
		}

	}

	public String getCurrentTimeStamp() {
		Date date = new Date();
		Timestamp ts = new Timestamp(date.getTime());
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return formatter.format(ts);
	}

	private List<String> createUpdateMaestroAppConfigImplRestp(
			List<com.wipro.fipc.model.generated.TicketCreationConfig> listTicketCreationConfigReq,
			List<com.wipro.fipc.model.generated.TaskUpdateConfig> listTaskUpdateConfigReq,
			MaestroTaskNameBO maestroTaskNameBOReq) {
		boolean booleanCreateTicketConfigResp = false;
		boolean booleancreateTaskConfigResp = false;
		boolean booleanmaestroTaskNameBOResp = false;
		boolean booleanmaestroTaskNameConfigBOResp = false;
		String strCreateTicketConfigResp = "";
		String strcreateTaskConfigResp = "";
		String strupdateMaestroTaskNameKsdResp = "";
		String strupdateMaestroTaskNameTaskResp = "";
		List<String> listCreatTicketTaskConfigResp = new ArrayList<>();
		List<TicketCreationConfig> listTicketCreationConfigReqEnt = new ArrayList();
		List<TaskUpdateConfig> listTaskUpdateConfigReqEnt = new ArrayList();
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			listTicketCreationConfigReqEnt = Arrays
					.asList(objectMapper.convertValue(listTicketCreationConfigReq, TicketCreationConfig[].class));
			listTaskUpdateConfigReqEnt = Arrays
					.asList(objectMapper.convertValue(listTaskUpdateConfigReq, TaskUpdateConfig[].class));
			if (listTicketCreationConfigReq != null && !listTicketCreationConfigReq.isEmpty()) {
				booleanCreateTicketConfigResp = createupdateMaestroTicketConfigList(listTicketCreationConfigReqEnt);
			}

			if (listTaskUpdateConfigReq != null && !listTaskUpdateConfigReq.isEmpty()) {
				booleancreateTaskConfigResp = createMaestroTaskUpdateClosureConfig(listTaskUpdateConfigReqEnt);
			}
			if (maestroTaskNameBOReq != null) {
				booleanmaestroTaskNameBOResp = updateMaestroTaskNameKsdConfig(maestroTaskNameBOReq);
			}
			if (maestroTaskNameBOReq != null) {
				booleanmaestroTaskNameConfigBOResp = updateMaestroTaskNameTaskUpdateConfig(maestroTaskNameBOReq);
			}

			strCreateTicketConfigResp = "Ticket_Creatinon_Config :" + booleanCreateTicketConfigResp;
			strcreateTaskConfigResp = "Task_update_Config :" + booleancreateTaskConfigResp;
			strupdateMaestroTaskNameKsdResp = "Ksd_Config_MaestroTaskName :" + booleanmaestroTaskNameBOResp;
			strupdateMaestroTaskNameTaskResp = "Task_Update_config_MaestroTaskName:"
					+ booleanmaestroTaskNameConfigBOResp;

			listCreatTicketTaskConfigResp.add(strCreateTicketConfigResp);
			listCreatTicketTaskConfigResp.add(strcreateTaskConfigResp);
			listCreatTicketTaskConfigResp.add(strupdateMaestroTaskNameKsdResp);
			listCreatTicketTaskConfigResp.add(strupdateMaestroTaskNameTaskResp);
			return listCreatTicketTaskConfigResp;

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "createUpdateMaestroAppConfigImplRestp6", "Error6 " + e);
		}
		return new ArrayList<>();
	}

	public boolean updateMaestroTaskNameKsdConfig(MaestroTaskNameBO maestroTaskNameBOReq) {

		boolean booleanMaestroTaskNameKsdDBResp = false;
		try {
			int updateCount=ksdConfigDao.updateMaestroTaskName(maestroTaskNameBOReq.getProcessJobMappingId(),
					maestroTaskNameBOReq.getMaestroTaskName(), maestroTaskNameBOReq.getMaestroTaskType(),
					maestroTaskNameBOReq.getUpdatedBy());

			booleanMaestroTaskNameKsdDBResp =updateCount>0?true:false;
			return booleanMaestroTaskNameKsdDBResp;
		} catch (Exception e) {
			booleanMaestroTaskNameKsdDBResp = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateMaestroTaskNameKsdConfig", "Error7 " + e);
		}
		return booleanMaestroTaskNameKsdDBResp;

	}

	public boolean updateMaestroTaskNameTaskUpdateConfig(MaestroTaskNameBO maestroTaskNameBOReq) {
		boolean booleanMaestroTaskNameTaskDBResp = false;
		try {
			int updateCount=taskUpdateConfigDao.updateTaskUpdateName(maestroTaskNameBOReq.getProcessJobMappingId(),
					maestroTaskNameBOReq.getMaestroTaskName(), maestroTaskNameBOReq.getUpdatedBy());

			booleanMaestroTaskNameTaskDBResp =updateCount>0?true:false;
			return booleanMaestroTaskNameTaskDBResp;
		} catch (Exception e) {
			booleanMaestroTaskNameTaskDBResp = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateMaestroTaskNameTaskUpdateConfig", "Error " + e);
		}
		return booleanMaestroTaskNameTaskDBResp;

	}

	public String updateConfigStatus(ConfigStatusBO configStatusBO) {
		String updateConfigStatusResp = FALSE;

		try {

			processFeatureConfigDao.modifyConfigStatus(configStatusBO.getProcessJobMappingId(),
					configStatusBO.getConfigStatus(), configStatusBO.getUpdatedBy());

			updateConfigStatusResp = "true";
			return updateConfigStatusResp;
		} catch (Exception e) {
			updateConfigStatusResp = FALSE;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateConfigStatus", "Error8 " + e);
		}

		return updateConfigStatusResp;

	}

	public String updatemaestroConfigStatus(ConfigStatusBO configStatusBO, String appName, String sessionToken) {
		String updateConfigStatusResp = FALSE;

		try {

			String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
			configStatusBO.setUpdatedBy(adID);

			processFeatureConfigDao.modifyConfigStatus(configStatusBO.getProcessJobMappingId(),
					configStatusBO.getConfigStatus(), configStatusBO.getUpdatedBy());

			updateConfigStatusResp = "true";
			return updateConfigStatusResp;
		} catch (Exception e) {
			updateConfigStatusResp = FALSE;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateConfigStatus", "Error8 " + e);
		}

		return updateConfigStatusResp;

	}

	public String updateApproveConfigStatus(ConfigStatusApproveBO configStatusApproveBO) {
		String updateConfigStatusResp = FALSE;

		try {
			processFeatureConfigDao.modifyApprovedConfigStatus(configStatusApproveBO.getProcessJobMappingId(),
					configStatusApproveBO.getConfigStatus(), configStatusApproveBO.getApprovedBy());

			updateConfigStatusResp = "true";
			return updateConfigStatusResp;
		} catch (Exception e) {
			updateConfigStatusResp = FALSE;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateApproveConfigStatus", "Error9 " + e);
		}

		return updateConfigStatusResp;

	}

	public String updateApprovemaestroConfigStatus(ConfigStatusApproveBO configStatusApproveBO, String appName,
			String sessionToken) {
		String updateConfigStatusResp = FALSE;

		try {

			String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
			configStatusApproveBO.setApprovedBy(adID);

			processFeatureConfigDao.modifyApprovedConfigStatus(configStatusApproveBO.getProcessJobMappingId(),
					configStatusApproveBO.getConfigStatus(), configStatusApproveBO.getApprovedBy());
			updateConfigStatusResp = "true";

			return updateConfigStatusResp;
		} catch (Exception e) {
			updateConfigStatusResp = FALSE;
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateApproveConfigStatus", "Error9 " + e);
		}

		return updateConfigStatusResp;

	}

	@Override
	public boolean createupdateMaestroTicketConfigList(List<TicketCreationConfig> listTicketCreationConfig) {
		boolean booleanCreateTicketDBResp = false;
		try {

			ticketCreationConfigDao.saveAll(listTicketCreationConfig);
			booleanCreateTicketDBResp = true;
			return booleanCreateTicketDBResp;

		} catch (Exception e) {
			booleanCreateTicketDBResp = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, "createupdateMaestroTicketConfig3", "Error10 " + e);
		}

		return booleanCreateTicketDBResp;
	}

	@Override
	public boolean createMaestroTaskUpdateClosureConfig(List<TaskUpdateConfig> listTaskUpdateConfig) {

		boolean booleanCreatTaskDBResp = false;

		try {


			taskUpdateConfigDao.saveAll(listTaskUpdateConfig);
			booleanCreatTaskDBResp = true;
			return booleanCreatTaskDBResp;

		} catch (Exception e) {
			booleanCreatTaskDBResp = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, "createMaestroTaskUpdateClosureConfig3", "Error11 " + e);
		}

		return booleanCreatTaskDBResp;
	}

	@Override
	public String deleteByIdMaestroTicketConfig(Long id) {

		Map<String, Long> params = new HashMap<>();
		params.put("id", id);

		genericService.delete(id);

		return "Status : Success";

	}

}
