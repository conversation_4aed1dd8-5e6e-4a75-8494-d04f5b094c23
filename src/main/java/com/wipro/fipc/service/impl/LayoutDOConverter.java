package com.wipro.fipc.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.LayoutRecord;
import com.wipro.fipc.model.LayoutRequest;
import com.wipro.fipc.model.ReportMultiSheet;
import com.wipro.fipc.model.ReportSheet;
import com.wipro.fipc.model.generated.KsdFileDetails;
import com.wipro.fipc.model.generated.LayoutConfig;
import com.wipro.fipc.model.generated.ProcessJobMapping;

@Component
public class LayoutDOConverter {

	@Autowired
	JsonParser parser;

	@Autowired
	Gson gson;

	public static final String FILENAME = "filename";

	public List<LayoutConfig> convertLayoutRequestToDO(List<LayoutRecord> layoutRecordsList,
			LayoutRequest layoutRequest) {

		List<LayoutConfig> layoutConfigDOList = new ArrayList<>();

		for (int i = 0; i < layoutRecordsList.size(); i++) {

			LayoutConfig record = new LayoutConfig();

			record.setFileName(layoutRequest.getFileName());
			String element = layoutRecordsList.get(i).getDataElement();
			String cleanelement = cleanTextContent(element);
			record.setMfFieldName(cleanelement);
			record.setActiveFlag(layoutRecordsList.get(i).getActiveFlag());

			convertLayoutRequestToDODet(layoutRecordsList, i, record);

			if (layoutRecordsList.get(i).getSheetNameWoutSpace() != null) {
				record.setSheetNameWoutSpace(layoutRecordsList.get(i).getSheetNameWoutSpace());
			} else {
				record.setSheetNameWoutSpace("");
			}
			record.setRecordFormat(layoutRecordsList.get(i).getFormat());
			record.setRecordIdentifier(layoutRecordsList.get(i).getRecordIdentifier());
			ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
			processJobMappingConfig.setId(Long.parseLong(layoutRequest.getProcessJobMappingId()));
			record.setProcessJobMappingConfig(processJobMappingConfig);
			record.setCreatedBy(layoutRequest.getCreatedBy());
			record.setUpdatedBy(layoutRequest.getUpdatedBy());
			record.setUpdatedDate(new Date());
			convertLayoutRequestToDODetIf(layoutRequest, record);

			String fieldType = HolmesAppConstants.TEXT;
			String formatType = "";
			String lengthOfDataElementAsString = "0";
			int sizeOfDate = 0;
			int startOfLength = 0;
			int endOfLength = 0;
			if ((layoutRecordsList.get(i).getFormat() != null) && (layoutRecordsList.get(i).getFormat().length() > 0)) {
				formatType = layoutRecordsList.get(i).getFormat().substring(0, 1);
				startOfLength = layoutRecordsList.get(i).getFormat().indexOf("(");
				endOfLength = layoutRecordsList.get(i).getFormat().indexOf(")");
				if (startOfLength == endOfLength) {
					endOfLength++;

				}
				lengthOfDataElementAsString = layoutRecordsList.get(i).getFormat().substring(startOfLength + 1,
						endOfLength);
			}
			if (layoutRequest.getDateFormat() != null) {
				sizeOfDate = layoutRequest.getDateFormat().length();
			}

			int lengthOfDataElement = 0;
			String dataElement = layoutRecordsList.get(i).getDataElement().toLowerCase();
			try {
				lengthOfDataElement = Integer.parseInt(lengthOfDataElementAsString);
			} catch (NumberFormatException nfe) {
				fieldType = "Text";
			}

			// checking for leading sign
			fieldType = convertLayoutRequestToDODetElse(layoutRecordsList, i, fieldType, formatType, sizeOfDate,
					lengthOfDataElement, dataElement);

			fieldType = convertLayoutRequestToDODetElseif(record, fieldType);
			record.setFieldType(fieldType);
			layoutConfigDOList.add(record);

		}

		return layoutConfigDOList;
	}

	private void convertLayoutRequestToDODetIf(LayoutRequest layoutRequest, LayoutConfig record) {
		if ((layoutRequest.getCreatedDate() != null) && (layoutRequest.getCreatedDate().toString().length() > 0)) {
			record.setCreatedDate(layoutRequest.getCreatedDate());
		} else {
			record.setCreatedDate(new Date());
		}
	}

	private String convertLayoutRequestToDODetElseif(LayoutConfig record, String fieldType) {
		if ((record.getRecordFormat().toLowerCase().contains("yy"))
				&& (record.getRecordFormat().toLowerCase().contains("mm"))
				|| record.getRecordFormat().equalsIgnoreCase("date")) {
			fieldType = HolmesAppConstants.DATE;
		} else if (record.getRecordFormat().equalsIgnoreCase("number")) {
			fieldType = HolmesAppConstants.NUMBER;
		}
		return fieldType;
	}

	private String convertLayoutRequestToDODetElse(List<LayoutRecord> layoutRecordsList, int i, String fieldType,
			String formatType, int sizeOfDate, int lengthOfDataElement, String dataElement) {
		if ((formatType.equalsIgnoreCase("s")) && (layoutRecordsList.get(i).getFormat().length() > 1)) {
			formatType = layoutRecordsList.get(i).getFormat().substring(1, 2);
		}

		if (formatType.equalsIgnoreCase("9")) {
			fieldType = HolmesAppConstants.NUMBER;
			if ((lengthOfDataElement == sizeOfDate) && ((dataElement.contains("date")) || (dataElement.startsWith("dt"))
					|| (dataElement.endsWith("dt")))) {
				fieldType = HolmesAppConstants.DATE;
			}

		} else if (formatType.equalsIgnoreCase("X")
				&& ((lengthOfDataElement == sizeOfDate) && ((dataElement.contains("date"))
						|| (dataElement.startsWith("dt")) || (dataElement.endsWith("dt"))))) {
			fieldType = HolmesAppConstants.DATE;

		}
		return fieldType;
	}

	private void convertLayoutRequestToDODet(List<LayoutRecord> layoutRecordsList, int i, LayoutConfig record) {
		if (layoutRecordsList.get(i).getId() != null) {
			record.setId(Long.parseLong(layoutRecordsList.get(i).getId()));
		}

		if (layoutRecordsList.get(i).getMfFieldWoutSpace() != null) {
			record.setMfFieldWoutSpace(layoutRecordsList.get(i).getMfFieldWoutSpace());
		} else {
			record.setMfFieldWoutSpace("NA");
		}

		if (layoutRecordsList.get(i).getStartPosition() != null) {
			record.setStartPos(layoutRecordsList.get(i).getStartPosition());
		}
		if (layoutRecordsList.get(i).getLength() != null) {
			record.setLength(Integer.parseInt(layoutRecordsList.get(i).getLength()));
		}

		if (layoutRecordsList.get(i).getRecordType() != null) {
			record.setRecordType(layoutRecordsList.get(i).getRecordType());
		} else {
			record.setRecordType("Detail Record");
		}
		if (layoutRecordsList.get(i).getRecordIdentifierVal() != null) {
			record.setRecordIdentifierVal(layoutRecordsList.get(i).getRecordIdentifierVal());
		}
		// Add Laeblling Report fields

		if (layoutRecordsList.get(i).getFieldTemplate() != null) {
			String fieldTemplat = layoutRecordsList.get(i).getFieldTemplate().toString();

			record.setFieldTemplate(fieldTemplat);
		} else {
			record.setFieldTemplate("");
		}
		if (layoutRecordsList.get(i).getValueDetails() != null) {
			String valueDetails = layoutRecordsList.get(i).getValueDetails().toString();
			record.setValueDetails(valueDetails);
		} else {
			record.setValueDetails("");
		}

		// newly added for sheet details
		if (layoutRecordsList.get(i).getSheetName() != null) {
			record.setSheetName(layoutRecordsList.get(i).getSheetName());
		}
		if (layoutRecordsList.get(i).getFileNameWoutSpace() != null) {
			record.setFileNameWoutSpace(layoutRecordsList.get(i).getFileNameWoutSpace());
		} else {
			record.setFileNameWoutSpace("");
		}

		if (StringUtils.hasText(layoutRecordsList.get(i).getPreFilter())) {
			record.setPreFilter(layoutRecordsList.get(i).getPreFilter());
		}

		if (StringUtils.hasText(layoutRecordsList.get(i).getPreFilterOperator())) {
			record.setPreFilterOperator(layoutRecordsList.get(i).getPreFilterOperator());
		}
		
		if (StringUtils.hasText(layoutRecordsList.get(i).getAmountFormat())) {
			record.setAmountFormat(layoutRecordsList.get(i).getAmountFormat());
		}
		
		if (StringUtils.hasText(layoutRecordsList.get(i).getFormat()))
			record.fieldType(layoutRecordsList.get(i).getFormat());
	}

	public String convertDOToLayout(String layoutDBResponse, String ksdFileDetailsRespnse) {

		String layoutResponse = "";

		JsonArray jsonLayoutConfig = (JsonArray) parser.parse(layoutDBResponse);
		JsonArray jsonKsdFileDetails = (JsonArray) parser.parse(ksdFileDetailsRespnse);

		JsonObject parentJson = new JsonObject();
		JsonArray jarray = new JsonArray();
		JsonObject jobj = (JsonObject) jsonLayoutConfig.get(0);

		if (jobj.has(HolmesAppConstants.CREATED_BY) || jobj.has(HolmesAppConstants.UPDATED_BY) || jobj.has(FILENAME)
				|| jobj.has(HolmesAppConstants.PJMID)) {

			parentJson.add(HolmesAppConstants.PROCESSJOBMAPPINGID, jobj
					.get(HolmesAppConstants.PROCESS_JOB_MAPPING_CONFIG).getAsJsonObject().get(HolmesAppConstants.ID));
		}

		convertDOToLayoutDet(jsonLayoutConfig, jarray);

		for (JsonElement ksdElement : jsonKsdFileDetails) {

			JsonObject obj = ksdElement.getAsJsonObject();

			String whitelistSSNvalues = "";
			JsonArray whiteListssnJsonArray = new JsonArray();

			if (!(obj.get(HolmesAppConstants.WHITE_LIST_SSN).isJsonNull())) {
				whitelistSSNvalues = obj.get(HolmesAppConstants.WHITE_LIST_SSN).getAsString();
			}
			if ((!whitelistSSNvalues.isEmpty()) && whitelistSSNvalues.length() > 1) {
				whiteListssnJsonArray = (JsonArray) parser.parse(whitelistSSNvalues);

			}

			String useLableingRpt = "";
			JsonArray useLableingRptJsonArray = new JsonArray();
			if ((!(obj.get(HolmesAppConstants.USE_LABELLING_RPT).isJsonNull()))) {
				useLableingRpt = obj.get(HolmesAppConstants.USE_LABELLING_RPT).getAsString();
				useLableingRptJsonArray = (JsonArray) parser.parse(useLableingRpt);
			}

			parentJson.add(HolmesAppConstants.FILE_NAME, obj.get(HolmesAppConstants.FILE_NAME));
			parentJson.add(HolmesAppConstants.FILE_FORMAT, obj.get(HolmesAppConstants.FILE_FORMAT_TYPE));
			parentJson.add(HolmesAppConstants.SUBJECT, obj.get(HolmesAppConstants.SUBJ));
			parentJson.add(HolmesAppConstants.SENDER, obj.get(HolmesAppConstants.SENDR));
			parentJson.add(HolmesAppConstants.THRESHOLD_MIN, obj.get(HolmesAppConstants.MIN_THRESHOLD));
			parentJson.add(HolmesAppConstants.THRESHOLD_MAX, obj.get(HolmesAppConstants.MAX_THRESHOLD));
			parentJson.add(HolmesAppConstants.VARIANCE, obj.get(HolmesAppConstants.VARIATION));
			parentJson.add(HolmesAppConstants.DELIMETER, obj.get(HolmesAppConstants.DELIMETER));
			parentJson.add(HolmesAppConstants.PPT_IDENTIFIER, obj.get(HolmesAppConstants.PPTIDENTIFIER));
			parentJson.add(HolmesAppConstants.IDENTIFIER, obj.get(HolmesAppConstants.PPTIDENTIFIERTYPE));
			parentJson.add(HolmesAppConstants.FILETYPE, obj.get(HolmesAppConstants.FILETYPE));
			parentJson.add(HolmesAppConstants.DATE_FORMAT, obj.get(HolmesAppConstants.DATE_FORMAT));
			parentJson.add(HolmesAppConstants.CREATED_BY, obj.get(HolmesAppConstants.CREATED_BY));
			parentJson.add(HolmesAppConstants.UPDATED_BY, obj.get(HolmesAppConstants.UPDATED_BY));
			parentJson.add(HolmesAppConstants.ID, obj.get(HolmesAppConstants.ID));
			parentJson.add(HolmesAppConstants.CREATED_DATE, obj.get(HolmesAppConstants.CREATED_DATE));
			parentJson.add(HolmesAppConstants.UPDATED_DATE, obj.get(HolmesAppConstants.UPDATED_DATE));
			parentJson.add(HolmesAppConstants.RECORD_IDENTIFIER_COL, obj.get(HolmesAppConstants.RECORD_IDENTIFIER_COL));
			parentJson.add(HolmesAppConstants.SHEET_NAME_CONSTANT, obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT));
			parentJson.add(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT,
					obj.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT));
			parentJson.add(HolmesAppConstants.FILE_NAME_WOUT_SPACE, obj.get(HolmesAppConstants.FILE_NAME_WOUT_SPACE));
			parentJson.add(HolmesAppConstants.ACTIVEFLAG, obj.get(HolmesAppConstants.ACTIVEFLAG));
			parentJson.add(HolmesAppConstants.SOURCE, obj.get(HolmesAppConstants.SOURCE));
			parentJson.add("path", obj.get("path"));
			parentJson.add("tool", obj.get("tool"));
			parentJson.add(HolmesAppConstants.DOMAIN, obj.get(HolmesAppConstants.DOMAIN));
			parentJson.add(HolmesAppConstants.QUERY_JCL_NAME, obj.get(HolmesAppConstants.QUERY_JCL_NAME));
			parentJson.add(HolmesAppConstants.DATE_GENERATED, obj.get(HolmesAppConstants.DATE_GENERATED));
			parentJson.add(HolmesAppConstants.DATE_FREQUENCY, obj.get(HolmesAppConstants.DATE_FREQUENCY));
			parentJson.add(HolmesAppConstants.DATE_PERIOD, obj.get(HolmesAppConstants.DATE_PERIOD));
			parentJson.add(HolmesAppConstants.DATE_INTERVAL, obj.get(HolmesAppConstants.DATE_INTERVAL));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME, obj.get(HolmesAppConstants.PREV_REPORT_FILE_NAME));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS,
					obj.get(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS));
			parentJson.add(HolmesAppConstants.SUB_FOLDER, obj.get(HolmesAppConstants.SUB_FOLDER));
			parentJson.add(HolmesAppConstants.ACTION, obj.get(HolmesAppConstants.ACTION));
			parentJson.add(HolmesAppConstants.USE_LABELLING_RPT, useLableingRptJsonArray);
			parentJson.add(HolmesAppConstants.CLIENT, obj.get(HolmesAppConstants.CLIENT));
			parentJson.add(HolmesAppConstants.DATABASE, obj.get(HolmesAppConstants.DATABASE));
			parentJson.add(HolmesAppConstants.SQL_QUERY, obj.get(HolmesAppConstants.SQL_QUERY));
			parentJson.add(HolmesAppConstants.RECORD_CNT_CHECK, obj.get(HolmesAppConstants.RECORD_CNT_CHECK));
			parentJson.add(HolmesAppConstants.WHITE_LIST_SSN, whiteListssnJsonArray);
			parentJson.add(HolmesAppConstants.SAMPLING_COUNT, obj.get(HolmesAppConstants.SAMPLING_COUNT));
			parentJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER, obj.get(HolmesAppConstants.BENE_PPT_IDENTIFIER));
			parentJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE, obj.get(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE));
			parentJson.add(HolmesAppConstants.VERIFY_FILE_DATE_DETAIL_RECORD, obj.get(HolmesAppConstants.VERIFY_FILE_DATE_DETAIL_RECORD));
		}

		parentJson.add("json", jarray);
		layoutResponse = parentJson.toString();
		return layoutResponse;

	}

	private void convertDOToLayoutDet(JsonArray jsonLayoutConfig, JsonArray jarray) {
		for (JsonElement layoutElement : jsonLayoutConfig) {

			JsonObject obj = layoutElement.getAsJsonObject();
			JsonObject childJson = new JsonObject();
			JsonArray valueDetailsJsonArray = new JsonArray();
			JsonArray fieldTemplateJsonArray = new JsonArray();
			String valueDetails = "";
			String fieldTemplate = "";
			// Add Labelling Related code
			if (!(obj.get(HolmesAppConstants.VALUE_DETAILS).isJsonNull())) {
				valueDetails = obj.get(HolmesAppConstants.VALUE_DETAILS).getAsString();
			}
			if (!(obj.get(HolmesAppConstants.FIELD_TEMPLATE).isJsonNull())) {
				fieldTemplate = obj.get(HolmesAppConstants.FIELD_TEMPLATE).getAsString();
			}
			if ((!valueDetails.isEmpty()) && valueDetails.length() > 1) {
				String valDet = valueDetails;
				valDet = valDet.substring(1, (valDet.length() - 1));
				valDet = valDet.replace("[", "[\"");
				valDet = valDet.replace("]", "\"]");
				valDet = "[".concat(valDet).concat("]");

				valueDetailsJsonArray = (JsonArray) parser.parse(valDet);

			}

			fieldTemplateJsonArray = convertDOToLayoutDetFieldtemp(fieldTemplateJsonArray, fieldTemplate);

			JsonElement activeFlagIndicator = obj.get(HolmesAppConstants.ACTIVEFLAG);
			String activeFlagString = activeFlagIndicator.toString().replace("\\", "");
			if (activeFlagString.contains(HolmesAppConstants.ACTIVE_FLAG_VALUE)) {
				childJson.add(HolmesAppConstants.ID, obj.get(HolmesAppConstants.ID));
				childJson.add(HolmesAppConstants.RECORDTYPE, obj.get(HolmesAppConstants.RECORDTYPE));
				childJson.add(HolmesAppConstants.DATA_ELEMENT, obj.get(HolmesAppConstants.MFFIELD_NAME));
				childJson.add(HolmesAppConstants.START_POSITION, obj.get(HolmesAppConstants.STARTPOS));
				childJson.add(HolmesAppConstants.LENGTH, obj.get(HolmesAppConstants.LENGTH));
				childJson.add(HolmesAppConstants.RECORD_IDENTIFIER, obj.get(HolmesAppConstants.RECORD_IDENTIFIER));
				childJson.add(HolmesAppConstants.FORMAT, obj.get(HolmesAppConstants.RECORD_FORMAT));
				childJson.add(HolmesAppConstants.MFFIELDWOUTSPACE, obj.get(HolmesAppConstants.MFFIELDWOUTSPACE));
				childJson.add(HolmesAppConstants.ACTIVEFLAG, obj.get(HolmesAppConstants.ACTIVEFLAG));
				childJson.add(HolmesAppConstants.RECORD_IDENTIFIER_VAL,
						obj.get(HolmesAppConstants.RECORD_IDENTIFIER_VAL));
				childJson.add(HolmesAppConstants.FIELD_TEMPLATE, fieldTemplateJsonArray);
				childJson.add(HolmesAppConstants.VALUE_DETAILS, valueDetailsJsonArray);
				childJson.add(HolmesAppConstants.PRE_FILTER, obj.get(HolmesAppConstants.PRE_FILTER));
				childJson.add(HolmesAppConstants.PRE_FILTER_OPERATOR, obj.get(HolmesAppConstants.PRE_FILTER_OPERATOR));
				childJson.add(HolmesAppConstants.AMOUNT_FORMAT, obj.get(HolmesAppConstants.AMOUNT_FORMAT));
				childJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER, obj.get(HolmesAppConstants.BENE_PPT_IDENTIFIER));
				childJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE, obj.get(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE));

				jarray.add(childJson);
			}
		}
	}

	private JsonArray convertDOToLayoutDetFieldtemp(JsonArray fieldTemplateJsonArray, String fieldTemplate) {
		if (!fieldTemplate.isEmpty() && fieldTemplate.length() > 1) {

			fieldTemplate = fieldTemplate.replace(" ", "%20");
			fieldTemplateJsonArray = (JsonArray) parser.parse(fieldTemplate);
			for (int j1 = 0; j1 < fieldTemplateJsonArray.size(); j1++) {
				JsonElement jele = fieldTemplateJsonArray.get(j1);
				String arrElement = jele.toString().replace("%20", " ");
				if (arrElement.substring(1, 2).equalsIgnoreCase(" ")) {

					String arrElement1 = arrElement.substring(0, 1);
					String arrElement2 = arrElement.substring(2, (arrElement.length()));
					arrElement = arrElement1.concat(arrElement2);

				}
				fieldTemplateJsonArray.set(j1, parser.parse(arrElement));
			}

		}
		return fieldTemplateJsonArray;
	}

	public String convertDOToKsd(String ksdFileDetailsResponse) {

		String ksdResponse = "";
		JsonArray jsonKsdFileDetails = (JsonArray) parser.parse(ksdFileDetailsResponse);
		JsonObject parentJson = new JsonObject();
		JsonArray jarray = new JsonArray();

		parentJson.addProperty(HolmesAppConstants.PROCESSJOBMAPPINGID, "");
		JsonObject childJson = new JsonObject();

		childJson.addProperty(HolmesAppConstants.RECORDTYPE, "");
		childJson.addProperty(HolmesAppConstants.DATA_ELEMENT, "");
		childJson.addProperty(HolmesAppConstants.START_POSITION, "");
		childJson.addProperty(HolmesAppConstants.LENGTH, "");
		childJson.addProperty(HolmesAppConstants.RECORD_IDENTIFIER, "");
		childJson.addProperty(HolmesAppConstants.FORMAT, "");
		childJson.addProperty("loginIdentifier", "");
		childJson.addProperty(HolmesAppConstants.MFFIELDWOUTSPACE, "");
		childJson.addProperty(HolmesAppConstants.RECORD_IDENTIFIER_VAL, "");
		childJson.addProperty(HolmesAppConstants.FIELD_TEMPLATE, "");
		childJson.addProperty(HolmesAppConstants.VALUE_DETAILS, "");
		childJson.addProperty(HolmesAppConstants.PRE_FILTER, "");
		childJson.addProperty(HolmesAppConstants.PRE_FILTER_OPERATOR, "");
		childJson.addProperty(HolmesAppConstants.AMOUNT_FORMAT, "");
		childJson.addProperty(HolmesAppConstants.BENE_PPT_IDENTIFIER, "");
		childJson.addProperty(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE, "");


		jarray.add(childJson);

		if (jsonKsdFileDetails.size() > 0) {
			for (JsonElement ksdElement : jsonKsdFileDetails) {
				JsonObject obj = ksdElement.getAsJsonObject();

				String whitelistSSNvalues = "";
				JsonArray whiteListssnJsonArray = new JsonArray();

				if (!(obj.get(HolmesAppConstants.WHITE_LIST_SSN).isJsonNull())) {
					whitelistSSNvalues = obj.get(HolmesAppConstants.WHITE_LIST_SSN).getAsString();
				}
				if ((!whitelistSSNvalues.isEmpty()) && whitelistSSNvalues.length() > 1) {
					whiteListssnJsonArray = (JsonArray) parser.parse(whitelistSSNvalues);
				}

				String useLableingRpt = "";
				JsonArray useLableingRptJsonArray = new JsonArray();
				if ((!(obj.get(HolmesAppConstants.USE_LABELLING_RPT).isJsonNull()))) {
					useLableingRpt = obj.get(HolmesAppConstants.USE_LABELLING_RPT).getAsString();
					useLableingRptJsonArray = (JsonArray) parser.parse(useLableingRpt);
				}

				addPropertyNullCheck(HolmesAppConstants.FILE_NAME, obj);

				convertDOToKsdIf(parentJson, obj);

				addPropertyNullCheck(HolmesAppConstants.DELIMETER, obj);

				addPropertyNullCheck(HolmesAppConstants.FILETYPE, obj);

				addPropertyNullCheck(HolmesAppConstants.DATE_FORMAT, obj);

				parentJson.addProperty(HolmesAppConstants.IDENTIFIER,
						obj.get(HolmesAppConstants.PPTIDENTIFIERTYPE).isJsonNull() ? ""
								: obj.get(HolmesAppConstants.PPTIDENTIFIERTYPE).getAsString());

				addPropertyNullCheck(HolmesAppConstants.CREATED_BY, obj);

				addPropertyNullCheck(HolmesAppConstants.UPDATED_BY, obj);

				addPropertyNullCheck(HolmesAppConstants.CREATED_DATE, obj);

				addPropertyNullCheck(HolmesAppConstants.PROCESSJOBMAPPINGID, obj);

				addPropertyNullCheck(HolmesAppConstants.ID, obj);

				addPropertyNullCheck(HolmesAppConstants.UPDATED_DATE, obj);

				addPropertyNullCheck(HolmesAppConstants.RECORD_IDENTIFIER_COL, obj);

				addPropertyNullCheck(HolmesAppConstants.SOURCE, obj);

				addPropertyNullCheck("path", obj);

				addPropertyNullCheck("tool", obj);

				addPropertyNullCheck(HolmesAppConstants.DOMAIN, obj);

				addPropertyNullCheck(HolmesAppConstants.QUERY_JCL_NAME, obj);

				addPropertyNullCheck(HolmesAppConstants.DATE_GENERATED, obj);

				addPropertyNullCheck(HolmesAppConstants.DATE_FREQUENCY, obj);

				addPropertyNullCheck(HolmesAppConstants.DATE_PERIOD, obj);

				addPropertyNullCheck(HolmesAppConstants.DATE_INTERVAL, obj);

				addPropertyNullCheck(HolmesAppConstants.PREV_REPORT_FILE_NAME, obj);

				addPropertyNullCheck(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS, obj);

				addPropertyNullCheck(HolmesAppConstants.SUB_FOLDER, obj);

				addPropertyNullCheck(HolmesAppConstants.ACTION, obj);

				addPropertyNullCheck(HolmesAppConstants.CLIENT, obj);

				addPropertyNullCheck(HolmesAppConstants.DATABASE, obj);

				addPropertyNullCheck(HolmesAppConstants.SQL_QUERY, obj);
				addPropertyNullCheck(HolmesAppConstants.RECORD_CNT_CHECK, obj);
				// newly added

				addPropertyNullCheck(HolmesAppConstants.SHEET_NAME_CONSTANT, obj);

				addPropertyNullCheck(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT, obj);

				addPropertyNullCheck(HolmesAppConstants.FILE_NAME_WOUT_SPACE, obj);

				addPropertyNullCheck(HolmesAppConstants.ACTIVEFLAG, obj);

				parentJson.add(HolmesAppConstants.USE_LABELLING_RPT, useLableingRptJsonArray);
				parentJson.add(HolmesAppConstants.WHITE_LIST_SSN, whiteListssnJsonArray);

			}
		} else {

			parentJson.addProperty(HolmesAppConstants.FILE_NAME, "");
			parentJson.addProperty(HolmesAppConstants.FILE_FORMAT, "");
			parentJson.addProperty(HolmesAppConstants.SUBJECT, "");
			parentJson.addProperty(HolmesAppConstants.SENDER, "");
			parentJson.addProperty(HolmesAppConstants.THRESHOLD_MIN, "");
			parentJson.addProperty(HolmesAppConstants.THRESHOLD_MAX, "");
			parentJson.addProperty(HolmesAppConstants.VARIANCE, "");
			parentJson.addProperty(HolmesAppConstants.DELIMETER, "");
			parentJson.addProperty(HolmesAppConstants.FILETYPE, "");
			parentJson.addProperty(HolmesAppConstants.DATE_FORMAT, "");
			parentJson.addProperty(HolmesAppConstants.PPT_IDENTIFIER, "");
			parentJson.addProperty(HolmesAppConstants.IDENTIFIER, "");
			parentJson.addProperty(HolmesAppConstants.CREATED_BY, "");
			parentJson.addProperty("UpdatedBy", "");
			parentJson.addProperty(HolmesAppConstants.ID, "");
			parentJson.addProperty(HolmesAppConstants.CREATED_DATE, "");
			parentJson.addProperty(HolmesAppConstants.UPDATED_DATE, "");
			parentJson.addProperty(HolmesAppConstants.RECORD_IDENTIFIER_COL, "");
			parentJson.addProperty(HolmesAppConstants.SHEET_NAME_CONSTANT, "");
			parentJson.addProperty(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT, "");
			parentJson.addProperty(HolmesAppConstants.FILE_NAME_WOUT_SPACE, "");
			parentJson.addProperty(HolmesAppConstants.SOURCE, "");
			parentJson.addProperty("path", "");
			parentJson.addProperty("tool", "");
			parentJson.addProperty(HolmesAppConstants.DOMAIN, "");
			parentJson.addProperty(HolmesAppConstants.QUERY_JCL_NAME, "");
			parentJson.addProperty(HolmesAppConstants.DATE_GENERATED, "");
			parentJson.addProperty(HolmesAppConstants.DATE_FREQUENCY, "");
			parentJson.addProperty(HolmesAppConstants.DATE_PERIOD, "");
			parentJson.addProperty(HolmesAppConstants.DATE_INTERVAL, "");
			parentJson.addProperty(HolmesAppConstants.PREV_REPORT_FILE_NAME, "");
			parentJson.addProperty(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS, "");
			parentJson.addProperty(HolmesAppConstants.SUB_FOLDER, "");
			parentJson.addProperty(HolmesAppConstants.ACTION, "");
			parentJson.addProperty(HolmesAppConstants.USE_LABELLING_RPT, "");
			parentJson.addProperty(HolmesAppConstants.CLIENT, "");
			parentJson.addProperty(HolmesAppConstants.DATABASE, "");
			parentJson.addProperty(HolmesAppConstants.SQL_QUERY, "");
			parentJson.addProperty(HolmesAppConstants.RECORD_CNT_CHECK, "");
			parentJson.addProperty(HolmesAppConstants.WHITE_LIST_SSN, "");
		}

		parentJson.add("json", jarray);
		ksdResponse = parentJson.toString();
		return ksdResponse;
	}

	private void convertDOToKsdIf(JsonObject parentJson, JsonObject obj) {
		parentJson.addProperty(HolmesAppConstants.PPT_IDENTIFIER,
				obj.get(HolmesAppConstants.PPTIDENTIFIER).isJsonNull() ? ""
						: obj.get(HolmesAppConstants.PPTIDENTIFIER).getAsString());

		parentJson.addProperty(HolmesAppConstants.FILE_FORMAT,
				obj.get(HolmesAppConstants.FILE_FORMAT_TYPE).isJsonNull() ? ""
						: obj.get(HolmesAppConstants.FILE_FORMAT_TYPE).getAsString());
		parentJson.addProperty(HolmesAppConstants.SUBJECT,
				obj.get(HolmesAppConstants.SUBJ).isJsonNull() ? "" : obj.get(HolmesAppConstants.SUBJ).getAsString());

		parentJson.addProperty(HolmesAppConstants.SENDER,
				obj.get(HolmesAppConstants.SENDR).isJsonNull() ? "" : obj.get(HolmesAppConstants.SENDR).getAsString());

		parentJson.addProperty(HolmesAppConstants.THRESHOLD_MIN,
				obj.get(HolmesAppConstants.MIN_THRESHOLD).isJsonNull() ? ""
						: obj.get(HolmesAppConstants.MIN_THRESHOLD).getAsString());

		parentJson.addProperty(HolmesAppConstants.THRESHOLD_MAX,
				obj.get(HolmesAppConstants.MAX_THRESHOLD).isJsonNull() ? ""
						: obj.get(HolmesAppConstants.MAX_THRESHOLD).getAsString());

		parentJson.addProperty(HolmesAppConstants.VARIANCE, obj.get(HolmesAppConstants.VARIATION).isJsonNull() ? ""
				: obj.get(HolmesAppConstants.VARIATION).getAsString());
	}

	private void addPropertyNullCheck(String key, JsonObject jsonElement) {
		jsonElement.addProperty(key, jsonElement.get(key).isJsonNull() ? "" : jsonElement.get(key).getAsString());

	}

	public KsdFileDetails getKsdFileDetails(List<LayoutRecord> layoutRecordsList, LayoutRequest layoutrequest) {

		KsdFileDetails ksdFileDetail = new KsdFileDetails();

		ksdFileDetail.setActiveFlag(layoutRecordsList.get(0).getActiveFlag());
		ksdFileDetail.setCreatedBy(layoutrequest.getCreatedBy());
		ksdFileDetail.setUpdatedBy(layoutrequest.getUpdatedBy());
		ksdFileDetail.setFileName(layoutrequest.getFileName());

		getKsdFileDetailsDet(layoutrequest, ksdFileDetail);
		if (layoutrequest.getAction() != null) {
			ksdFileDetail.setAction(layoutrequest.getAction());
		}
		if (layoutrequest.getClient() != null) {
			ksdFileDetail.setClient(layoutrequest.getClient());
		}
		if (layoutrequest.getDatabase() != null) {
			ksdFileDetail.setDatabase(layoutrequest.getDatabase());
		}
		if (layoutrequest.getSqlQuery() != null) {
			ksdFileDetail.setSqlQuery(layoutrequest.getSqlQuery());
		}
		if (layoutrequest.getRecordCntCheck() != null) {
			ksdFileDetail.setRecordCntCheck(layoutrequest.getRecordCntCheck());
		}
		// Add LabelingReport field
		if (layoutrequest.getUseLabellingRpt() != null) {
			ksdFileDetail.setUseLabellingRpt(gson.toJson(layoutrequest.getUseLabellingRpt()));
		}

		// newly added for sheet
		if (layoutrequest.getSheetName() != null) {
			ksdFileDetail.setSheetName(layoutrequest.getSheetName());
		}

		if (layoutrequest.getFileNameWoutSpace() != null) {
			ksdFileDetail.setFileNameWoutSpace(layoutrequest.getFileNameWoutSpace());
		} else {
			ksdFileDetail.setFileNameWoutSpace("");
		}
		if (layoutrequest.getSheetNameWoutSpace() != null) {
			ksdFileDetail.setSheetNameWoutSpace(layoutrequest.getSheetNameWoutSpace());
		} else {
			ksdFileDetail.setSheetNameWoutSpace("");
		}
		String pptItentifier = layoutrequest.getPptIdentifier();
		String cleanPPtIdentifier = cleanTextContent(pptItentifier);
		ksdFileDetail.setPptidentifier(cleanPPtIdentifier);
		ksdFileDetail.setPptidentifierType(layoutrequest.getIdentifier());
		ksdFileDetail.setDateFormat(layoutrequest.getDateFormat());
		ksdFileDetail.setFileType(layoutrequest.getFileType());
		ksdFileDetail.setActiveFlag("T");
		ksdFileDetail.setWhitelistSSN("");

		Date date = new Date();

		ksdFileDetail.setCreatedDate(date);
		if ((layoutrequest.getCreatedDate() != null) && (layoutrequest.getCreatedDate().toString().length() > 0)) {
			ksdFileDetail.setCreatedDate(layoutrequest.getCreatedDate());
		} else {
			ksdFileDetail.setCreatedDate(new Date());
		}

		ksdFileDetail.setUpdatedDate(date);
		if (layoutrequest.getFileType().equalsIgnoreCase("Report")) {
			ksdFileDetail.setSendr(layoutrequest.getSender());
			ksdFileDetail.setSubj(layoutrequest.getSubject());
		}

		ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
		processJobMappingConfig.setId(Long.parseLong(layoutrequest.getProcessJobMappingId()));
		ksdFileDetail.setProcessJobMapping(processJobMappingConfig);

		return ksdFileDetail;
	}

	private void getKsdFileDetailsDet(LayoutRequest layoutrequest, KsdFileDetails ksdFileDetail) {
		if (layoutrequest.getRecordIdentifierCol() != null) {
			ksdFileDetail.setRecordIdentifierCol(layoutrequest.getRecordIdentifierCol());
		}
		if (layoutrequest.getThresholdMin() != null) {
			ksdFileDetail.setMinThreshold((layoutrequest.getThresholdMin()));
		}
		if (layoutrequest.getThresholdMax() != null) {
			ksdFileDetail.setMaxThreshold((layoutrequest.getThresholdMax()));
		}
		if (layoutrequest.getVariance() != null) {
			ksdFileDetail.setVariation((layoutrequest.getVariance()));
		}

		if (layoutrequest.getFileFormat() != null) {
			ksdFileDetail.setFileFormatType(layoutrequest.getFileFormat());
		}
		if (layoutrequest.getDelimiter() != null) {
			ksdFileDetail.setDelimiter(layoutrequest.getDelimiter());
		}
		// Add Workbanch req
		if (layoutrequest.getSource() != null) {
			ksdFileDetail.setSource(layoutrequest.getSource());
		}
		if (layoutrequest.getPath() != null) {
			ksdFileDetail.setPath(layoutrequest.getPath());
		}
		if (layoutrequest.getTool() != null) {
			ksdFileDetail.setTool(layoutrequest.getTool());
		}
		if (layoutrequest.getDomain() != null) {
			ksdFileDetail.setDomain(layoutrequest.getDomain());
		}
		if (layoutrequest.getQueryJCLName() != null) {
			ksdFileDetail.setQueryJCLName(layoutrequest.getQueryJCLName());
		}
		getKsdFileDetailsDetKsd(layoutrequest, ksdFileDetail);
	}

	private void getKsdFileDetailsDetKsd(LayoutRequest layoutrequest, KsdFileDetails ksdFileDetail) {
		if (layoutrequest.getDateGenerated() != null) {
			ksdFileDetail.setDateGenerated(layoutrequest.getDateGenerated());
		}
		if (layoutrequest.getDateFrequency() != null) {
			ksdFileDetail.setDateFrequency(layoutrequest.getDateFrequency());
		}
		if (layoutrequest.getDatePeriod() != null) {
			ksdFileDetail.setDatePeriod(layoutrequest.getDatePeriod());
		}
		if (layoutrequest.getDateInterval() != null) {
			ksdFileDetail.setDateInterval(layoutrequest.getDateInterval());
		}
		// new report fields add
		if (layoutrequest.getPrevReportFileName() != null) {
			ksdFileDetail.setPrevReportFileName(layoutrequest.getPrevReportFileName());
		}
		if (layoutrequest.getPrevReportFileNameWs() != null) {
			ksdFileDetail.setPrevReportFileNameWs(layoutrequest.getPrevReportFileNameWs());
		}
		if (layoutrequest.getSubfolder() != null) {
			ksdFileDetail.setSubfolder(layoutrequest.getSubfolder());
		}
	}

	public String cleanTextContent(String element) {
		// removes off all non-ASCII characters
		element = element.replaceAll("[^\\x00-\\x7F]", "");

		// removes all the ASCII control characters
		element = element.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "");

		// removes non-printable characters from Unicode
		element = element.replaceAll("\\p{C}", "");

		return element.trim();
	}

	public String convertDOTofileNames(String ksdFileDetailsResponse) {

		JsonArray jsonKsdFileDetails = (JsonArray) parser.parse(ksdFileDetailsResponse);

		return jsonKsdFileDetails.toString();
	}
	// jul 10 req
	// testing purpose for Input Report for Multiple Sheets
	// from line 444 till end

	public List<KsdFileDetails> ksdFileDetailsforMultiSheet(ReportSheet request) {

		List<KsdFileDetails> ksdList = new ArrayList<>();
		for (int i = 0; i < request.getKsdFileJson().size(); i++) {

			KsdFileDetails ksdFileDetail = new KsdFileDetails();
			ReportMultiSheet request1 = request.getKsdFileJson().get(i);
			LoggerUtil.log(getClass(), Level.INFO, "method",
					"Inside KSDfiledetails method, printing each object: " + request1.toString());

			ksdFileDetail.setActiveFlag(request1.getActiveFlag());

			ksdFileDetail.setCreatedBy(request1.getCreatedBy());
			ksdFileDetail.setUpdatedBy(request1.getUpdatedBy());
			ksdFileDetail.setFileName(request1.getFileName());

			ksdFileDetailsforMultiSheetDet(ksdFileDetail, request1);

			String pptItentifier = request1.getPptIdentifier();
			String cleanPPtIdentifier = cleanTextContent(pptItentifier);
			ksdFileDetail.setPptidentifier(cleanPPtIdentifier);
			ksdFileDetail.setPptidentifierType(request1.getIdentifier());
			ksdFileDetail.setDateFormat(request1.getDateFormat());
			ksdFileDetail.setFileType(request1.getFileType());

			Date date = new Date();

			ksdFileDetail.setCreatedDate(date);
			ksdFileDetailsforMultiSheetDetMulti(ksdFileDetail, request1, date);
			ksdFileDetailsforMultiSheetDetMultiSheet(ksdFileDetail, request1);
			// sheetName also has to be added
			ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
			processJobMappingConfig.setId(Long.parseLong(request1.getProcessJobMappingId()));
			ksdFileDetail.setProcessJobMapping(processJobMappingConfig);

			if (request1.getSheetName() != null) {
				ksdFileDetail.setSheetName(request1.getSheetName());
			}

			if (request1.getFileNameWoutSpace() != null) {
				ksdFileDetail.setFileNameWoutSpace(request1.getFileNameWoutSpace());
			} else {
				ksdFileDetail.setFileNameWoutSpace("");
			}

			if (request1.getId() != null) {
				ksdFileDetail.setId(Long.parseLong(request1.getId()));
			}
			if (request1.getSheetNameWoutSpace() != null) {
				ksdFileDetail.setSheetNameWoutSpace(request1.getSheetNameWoutSpace());
			} else {
				ksdFileDetail.setSheetNameWoutSpace("");
			}

			ksdList.add(ksdFileDetail);

		}
		return ksdList;
	}

	private void ksdFileDetailsforMultiSheetDetMultiSheet(KsdFileDetails ksdFileDetail, ReportMultiSheet request1) {
		if (request1.getDateFrequency() != null) {
			ksdFileDetail.setDateFrequency(request1.getDateFrequency());
		}
		if (request1.getDatePeriod() != null) {
			ksdFileDetail.setDatePeriod(request1.getDatePeriod());
		}
		if (request1.getDateInterval() != null) {
			ksdFileDetail.setDateInterval(request1.getDateInterval());
		}
		// new Report fields Add
		if (request1.getPrevReportFileName() != null) {
			ksdFileDetail.setPrevReportFileName(request1.getPrevReportFileName());
		}
		if (request1.getPrevReportFileNameWs() != null) {
			ksdFileDetail.setPrevReportFileNameWs(request1.getPrevReportFileNameWs());
		}
		if (request1.getSubfolder() != null) {
			ksdFileDetail.setSubfolder(request1.getSubfolder());
		}
		if (request1.getAction() != null) {
			ksdFileDetail.setAction(request1.getAction());
		}

		// Add Labeling Report field
		if (request1.getUseLabellingRpt() != null) {
			ksdFileDetail.setUseLabellingRpt(gson.toJson(request1.getUseLabellingRpt()));
		}
		if (request1.getClient() != null) {
			ksdFileDetail.setClient(request1.getClient());
		}
		if (request1.getDatabase() != null) {
			ksdFileDetail.setDatabase(request1.getDatabase());
		}
		if (request1.getSqlQuery() != null) {
			ksdFileDetail.setSqlQuery(request1.getSqlQuery());

		}
		if (request1.getRecordCntCheck() != null) {
			ksdFileDetail.setRecordCntCheck(request1.getRecordCntCheck());
		}
	}

	private void ksdFileDetailsforMultiSheetDetMulti(KsdFileDetails ksdFileDetail, ReportMultiSheet request1,
			Date date) {
		if ((request1.getCreatedDate() != null) && (request1.getCreatedDate().toString().length() > 0)) {
			ksdFileDetail.setCreatedDate(request1.getCreatedDate());
		} else {
			ksdFileDetail.setCreatedDate(new Date());
		}

		ksdFileDetail.setUpdatedDate(date);

		if (request1.getSubject() != null) {
			ksdFileDetail.setSubj(request1.getSubject());
		}
		if (request1.getSender() != null) {
			ksdFileDetail.setSendr(request1.getSender());
		}

		if (request1.getSource() != null) {
			ksdFileDetail.setSource(request1.getSource());
		}
		if (request1.getPath() != null) {
			ksdFileDetail.setPath(request1.getPath());
		}
		if (request1.getTool() != null) {
			ksdFileDetail.setTool(request1.getTool());
		}
		if (request1.getDomain() != null) {
			ksdFileDetail.setDomain(request1.getDomain());
		}
		if (request1.getQueryJCLName() != null) {
			ksdFileDetail.setQueryJCLName(request1.getQueryJCLName());
		}
		if (request1.getDateGenerated() != null) {
			ksdFileDetail.setDateGenerated(request1.getDateGenerated());
		}
	}

	private void ksdFileDetailsforMultiSheetDet(KsdFileDetails ksdFileDetail, ReportMultiSheet request1) {
		if (request1.getRecordIdentifierCol() != null) {
			ksdFileDetail.setRecordIdentifierCol(request1.getRecordIdentifierCol());
		}
		if (request1.getThresholdMin() != null) {
			ksdFileDetail.setMinThreshold((request1.getThresholdMin()));
		}
		if (request1.getThresholdMax() != null) {
			ksdFileDetail.setMaxThreshold((request1.getThresholdMax()));
		}
		if (request1.getVariance() != null) {
			ksdFileDetail.setVariation((request1.getVariance()));
		}

		if (request1.getFileFormat() != null) {
			ksdFileDetail.setFileFormatType(request1.getFileFormat());
		}
		if (request1.getDelimiter() != null) {
			ksdFileDetail.setDelimiter(request1.getDelimiter());
		}
		if (request1.getWhitelistSSN() != null) {
			List<String> whiteSsnList = request1.getWhitelistSSN();
			String encrySsnList = encryptData(whiteSsnList, request1.getProcessJobMappingId());

			ksdFileDetail.setWhitelistSSN(encrySsnList);
		} else {
			ksdFileDetail.setWhitelistSSN("");
		}
	}

	public List<LayoutConfig> layoutDetailsForMultiSheet(List<List<LayoutRecord>> layoutRecordsList,
			ReportSheet request) {

		List<LayoutConfig> layoutConfigDOList = new ArrayList<>();

		int j = 0;
		for (List<LayoutRecord> list1 : layoutRecordsList) {
			for (int i = 0; i < list1.size(); i++) {

				LayoutConfig record = new LayoutConfig();

				record.setFileName(request.getKsdFileJson().get(0).getFileName());
				String element = list1.get(i).getDataElement();
				String cleanelement = cleanTextContent(element);
				record.setMfFieldName(cleanelement);
				layoutDetailsForMultiSheetDetMul(request, j, list1, i, record);

				layoutDetailsForMultiSheetDetMulS(list1, i, record);

				record.setRecordFormat(list1.get(i).getFormat());
				record.setRecordIdentifier(list1.get(i).getRecordIdentifier());
				ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
				processJobMappingConfig.setId(Long.parseLong(request.getKsdFileJson().get(0).getProcessJobMappingId()));
				record.setProcessJobMappingConfig(processJobMappingConfig);
				record.setCreatedBy(request.getKsdFileJson().get(j).getCreatedBy());
				record.setUpdatedBy(request.getKsdFileJson().get(j).getUpdatedBy());
				record.setUpdatedDate(new Date());
				layoutDetailsForMultiSheetDetMulSh(request, j, record);
				layoutDetailsForMultiSheetDetMulShee(request, j, record);

				String fieldType = HolmesAppConstants.TEXT;
				String formatType = "";
				String lengthOfDataElementAsString = "0";
				int sizeOfDate = 0;
				int startOfLength = 0;
				int endOfLength = 0;
				if ((list1.get(i).getFormat() != null) && (list1.get(i).getFormat().length() > 0)) {
					formatType = list1.get(i).getFormat().substring(0, 1);
					startOfLength = list1.get(i).getFormat().indexOf("(");
					endOfLength = list1.get(i).getFormat().indexOf(")");
					if (startOfLength == endOfLength) {
						endOfLength++;

					}
					lengthOfDataElementAsString = list1.get(i).getFormat().substring(startOfLength + 1, endOfLength);
				}
				sizeOfDate = layoutDetailsForMultiSheetDetMulSheet(request, j, sizeOfDate);

				int lengthOfDataElement = 0;
				String dataElement = list1.get(i).getDataElement().toLowerCase();
				try {
					lengthOfDataElement = Integer.parseInt(lengthOfDataElementAsString);
				} catch (NumberFormatException nfe) {
					fieldType = HolmesAppConstants.TEXT;
				}

				// checking for leading sign
				fieldType = layoutDetailsForMultiSheetDetMulShe(list1, i, record, fieldType, formatType, sizeOfDate,
						lengthOfDataElement, dataElement);

				record.setFieldType(fieldType);

				layoutConfigDOList.add(record);

			}
			j++;

		}

		return layoutConfigDOList;
	}

	private int layoutDetailsForMultiSheetDetMulSheet(ReportSheet request, int j, int sizeOfDate) {
		if (request.getKsdFileJson().get(j).getDateFormat() != null) {
			sizeOfDate = request.getKsdFileJson().get(j).getDateFormat().length();
		}
		return sizeOfDate;
	}

	private void layoutDetailsForMultiSheetDetMulShee(ReportSheet request, int j, LayoutConfig record) {
		if (request.getKsdFileJson().get(j).getSheetNameWoutSpace() != null) {
			record.setSheetNameWoutSpace(request.getKsdFileJson().get(j).getSheetNameWoutSpace());

		} else {
			record.setSheetNameWoutSpace("");
		}
	}

	private String layoutDetailsForMultiSheetDetMulShe(List<LayoutRecord> list1, int i, LayoutConfig record,
			String fieldType, String formatType, int sizeOfDate, int lengthOfDataElement, String dataElement) {
		if ((formatType.equalsIgnoreCase("s")) && (list1.get(i).getFormat().length() > 1)) {
			formatType = list1.get(i).getFormat().substring(1, 2);
		}

		if (formatType.equalsIgnoreCase("9")) {
			fieldType = HolmesAppConstants.NUMBER;
			if ((lengthOfDataElement == sizeOfDate) && ((dataElement.contains("date")) || (dataElement.startsWith("dt"))
					|| (dataElement.endsWith("dt")))) {
				fieldType = HolmesAppConstants.DATE;
			}

		} else if (formatType.equalsIgnoreCase("X")
				&& ((lengthOfDataElement == sizeOfDate) && ((dataElement.contains("date"))
						|| (dataElement.startsWith("dt")) || (dataElement.endsWith("dt"))))) {
			fieldType = HolmesAppConstants.DATE;

		}

		fieldType = convertLayoutRequestToDODetElseif(record, fieldType);
		return fieldType;
	}

	private void layoutDetailsForMultiSheetDetMulSh(ReportSheet request, int j, LayoutConfig record) {
		if ((request.getKsdFileJson().get(j).getCreatedDate() != null)
				&& (request.getKsdFileJson().get(j).getCreatedDate().toString().length() > 0)) {
			record.setCreatedDate(request.getKsdFileJson().get(j).getCreatedDate());
		} else {
			record.setCreatedDate(new Date());
		}

		// added newly for sheet
		if (request.getKsdFileJson().get(j).getPptIdentifier() != null) {
			record.setPptIdentifier(request.getKsdFileJson().get(j).getPptIdentifier());
		}
		if (request.getKsdFileJson().get(j).getSheetName() != null) {
			record.setSheetName(request.getKsdFileJson().get(j).getSheetName());
		}
		if (request.getKsdFileJson().get(j).getFileNameWoutSpace() != null) {
			record.setFileNameWoutSpace(request.getKsdFileJson().get(j).getFileNameWoutSpace());
		} else {
			record.setFileNameWoutSpace("");
		}
	}

	private void layoutDetailsForMultiSheetDetMulS(List<LayoutRecord> list1, int i, LayoutConfig record) {
		if (list1.get(i).getRecordType() != null) {
			record.setRecordType(list1.get(i).getRecordType());
		} else {
			record.setRecordType("Detail Record");
		}
		if (list1.get(i).getRecordIdentifierVal() != null) {
			record.setRecordIdentifierVal(list1.get(i).getRecordIdentifierVal());
		}
		// Add Labeling fields

		if (list1.get(i).getFieldTemplate() != null) {
			String fieldTemplate = list1.get(i).getFieldTemplate().toString();
			record.setFieldTemplate(fieldTemplate);
		} else {
			record.setFieldTemplate("");
		}
		if (list1.get(i).getValueDetails() != null) {
			String valueDetails = list1.get(i).getValueDetails().toString();
			record.setValueDetails(valueDetails);
		} else {
			record.setValueDetails("");
		}
	}

	private void layoutDetailsForMultiSheetDetMul(ReportSheet request, int j, List<LayoutRecord> list1, int i,
			LayoutConfig record) {
		if (request.getKsdFileJson().get(j).getActiveFlag().equalsIgnoreCase("T")
				&& list1.get(i).getActiveFlag().equalsIgnoreCase("F")) {
			record.setActiveFlag(list1.get(i).getActiveFlag());
		} else {
			record.setActiveFlag(request.getKsdFileJson().get(j).getActiveFlag());
		}

		if (list1.get(i).getId() != null) {
			record.setId(Long.parseLong(list1.get(i).getId()));
		}

		if (list1.get(i).getMfFieldWoutSpace() != null) {
			record.setMfFieldWoutSpace(list1.get(i).getMfFieldWoutSpace());
		} else {
			record.setMfFieldWoutSpace("NA");
		}

		if (list1.get(i).getStartPosition() != null) {
			record.setStartPos(list1.get(i).getStartPosition());
		}
		if (list1.get(i).getLength() != null) {
			record.setLength(Integer.parseInt(list1.get(i).getLength()));
		}
	}

	public String convertDOToLayoutMultiSheet(String layoutDBResponse, String ksdDBResponse, String pjmId) {

		JsonArray jsonLayoutConfig = (JsonArray) parser.parse(layoutDBResponse);
		JsonArray jsonKsdFileDetails = (JsonArray) parser.parse(ksdDBResponse);

		JsonArray parentArray = new JsonArray();

		JsonObject finalJson = new JsonObject();
		for (int i = 0; i < jsonKsdFileDetails.size(); i++) {
			JsonElement ksdElement = jsonKsdFileDetails.get(i);
			JsonArray detailRecord = new JsonArray();
			JsonObject parentJson = new JsonObject();
			JsonObject obj1 = ksdElement.getAsJsonObject();

			convertDOToLayoutMultiSheetDet(jsonLayoutConfig, detailRecord, obj1);

			if (obj1.has(HolmesAppConstants.CREATED_BY) || obj1.has(HolmesAppConstants.UPDATED_BY) || obj1.has(FILENAME)
					|| obj1.has(HolmesAppConstants.PJMID)) {

				parentJson.add(HolmesAppConstants.PROCESSJOBMAPPINGID,
						obj1.get(HolmesAppConstants.PROCESSJOBMAPPINGID));
			}
			String whitelistSSNvalues = "";
			JsonArray whiteListssnJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.WHITE_LIST_SSN).isJsonNull()))
					&& obj1.get(HolmesAppConstants.WHITE_LIST_SSN).toString().length() > 3) {
				whitelistSSNvalues = obj1.get(HolmesAppConstants.WHITE_LIST_SSN).getAsString();
				String decryptedValues = decrypt(whitelistSSNvalues, pjmId);
				whitelistSSNvalues = decryptedValues;

			}
			if (!whitelistSSNvalues.isEmpty() && whitelistSSNvalues.length() > 1) {
				whiteListssnJsonArray = (JsonArray) parser.parse(whitelistSSNvalues);

			}

			String useLableingRpt = "";
			JsonArray useLableingRptJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.USE_LABELLING_RPT).isJsonNull()))) {
				useLableingRpt = obj1.get(HolmesAppConstants.USE_LABELLING_RPT).getAsString();
				useLableingRptJsonArray = (JsonArray) parser.parse(useLableingRpt);
			}

			parentJson.add(HolmesAppConstants.FILE_NAME, obj1.get(HolmesAppConstants.FILE_NAME));
			parentJson.add(HolmesAppConstants.FILE_FORMAT, obj1.get(HolmesAppConstants.FILE_FORMAT_TYPE));
			parentJson.add(HolmesAppConstants.SUBJECT, obj1.get(HolmesAppConstants.SUBJ));
			parentJson.add(HolmesAppConstants.SENDER, obj1.get(HolmesAppConstants.SENDR));
			parentJson.add(HolmesAppConstants.SOURCE, obj1.get(HolmesAppConstants.SOURCE));
			parentJson.add("path", obj1.get("path"));
			parentJson.add(HolmesAppConstants.THRESHOLD_MIN, obj1.get(HolmesAppConstants.MIN_THRESHOLD));
			parentJson.add(HolmesAppConstants.THRESHOLD_MAX, obj1.get(HolmesAppConstants.MAX_THRESHOLD));
			parentJson.add(HolmesAppConstants.VARIANCE, obj1.get(HolmesAppConstants.VARIATION));
			parentJson.add(HolmesAppConstants.DELIMETER, obj1.get(HolmesAppConstants.DELIMETER));
			parentJson.add(HolmesAppConstants.PPT_IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIER));
			parentJson.add(HolmesAppConstants.IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIERTYPE));
			parentJson.add(HolmesAppConstants.FILETYPE, obj1.get(HolmesAppConstants.FILETYPE));
			parentJson.add(HolmesAppConstants.DATE_FORMAT, obj1.get(HolmesAppConstants.DATE_FORMAT));
			parentJson.add(HolmesAppConstants.CREATED_BY, obj1.get(HolmesAppConstants.CREATED_BY));
			parentJson.add(HolmesAppConstants.UPDATED_BY, obj1.get(HolmesAppConstants.UPDATED_BY));
			parentJson.add(HolmesAppConstants.ID, obj1.get(HolmesAppConstants.ID));
			parentJson.add(HolmesAppConstants.CREATED_DATE, obj1.get(HolmesAppConstants.CREATED_DATE));
			parentJson.add(HolmesAppConstants.UPDATED_DATE, obj1.get(HolmesAppConstants.UPDATED_DATE));
			parentJson.add(HolmesAppConstants.RECORD_IDENTIFIER_COL,
					obj1.get(HolmesAppConstants.RECORD_IDENTIFIER_COL));
			parentJson.add(HolmesAppConstants.SHEET_NAME_CONSTANT, obj1.get(HolmesAppConstants.SHEET_NAME_CONSTANT));
			parentJson.add(HolmesAppConstants.ACTIVEFLAG, obj1.get(HolmesAppConstants.ACTIVEFLAG));

			parentJson.addProperty(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT,
					obj1.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT).isJsonNull() ? ""
							: obj1.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT).getAsString());
			parentJson.addProperty(HolmesAppConstants.FILE_NAME_WOUT_SPACE,
					obj1.get(HolmesAppConstants.FILE_NAME_WOUT_SPACE).isJsonNull() ? ""
							: obj1.get(HolmesAppConstants.FILE_NAME_WOUT_SPACE).getAsString());
			parentJson.add("tool", obj1.get("tool"));
			parentJson.add(HolmesAppConstants.DOMAIN, obj1.get(HolmesAppConstants.DOMAIN));
			parentJson.add(HolmesAppConstants.QUERY_JCL_NAME, obj1.get(HolmesAppConstants.QUERY_JCL_NAME));
			parentJson.add(HolmesAppConstants.DATE_GENERATED, obj1.get(HolmesAppConstants.DATE_GENERATED));
			parentJson.add(HolmesAppConstants.DATE_FREQUENCY, obj1.get(HolmesAppConstants.DATE_FREQUENCY));
			parentJson.add(HolmesAppConstants.DATE_PERIOD, obj1.get(HolmesAppConstants.DATE_PERIOD));
			parentJson.add(HolmesAppConstants.DATE_INTERVAL, obj1.get(HolmesAppConstants.DATE_INTERVAL));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS));
			parentJson.add(HolmesAppConstants.SUB_FOLDER, obj1.get(HolmesAppConstants.SUB_FOLDER));
			parentJson.add(HolmesAppConstants.ACTION, obj1.get(HolmesAppConstants.ACTION));
			parentJson.add(HolmesAppConstants.USE_LABELLING_RPT, useLableingRptJsonArray);
			parentJson.add(HolmesAppConstants.CLIENT, obj1.get(HolmesAppConstants.CLIENT));
			parentJson.add(HolmesAppConstants.DATABASE, obj1.get(HolmesAppConstants.DATABASE));
			parentJson.add(HolmesAppConstants.SQL_QUERY, obj1.get(HolmesAppConstants.SQL_QUERY));
			parentJson.add(HolmesAppConstants.RECORD_CNT_CHECK, obj1.get(HolmesAppConstants.RECORD_CNT_CHECK));
			parentJson.add(HolmesAppConstants.WHITE_LIST_SSN, whiteListssnJsonArray);
			parentJson.add(HolmesAppConstants.DETAILRECORDS, detailRecord);
			// DynRepNameCHanges
			parentJson.add(HolmesAppConstants.APPEND_NAME_FLAG, obj1.get(HolmesAppConstants.APPEND_NAME_FLAG));
			parentJson.add(HolmesAppConstants.FILE_NAME_TEMPLATE, obj1.get(HolmesAppConstants.FILE_NAME_TEMPLATE));
			parentJson.add(HolmesAppConstants.PRIMARYFILE, obj1.get(HolmesAppConstants.PRIMARYFILE));
			parentJson.add(HolmesAppConstants.SAMPLING_COUNT, obj1.get(HolmesAppConstants.SAMPLING_COUNT));
			parentJson.add(HolmesAppConstants.VERIFYEMAILONLY, obj1.get(HolmesAppConstants.VERIFYEMAILONLY));
			
			parentJson.addProperty(HolmesAppConstants.EMAIL_SEARCH_BY,
					obj1.get(HolmesAppConstants.EMAIL_SEARCH_BY).isJsonNull() ? HolmesAppConstants.EMAIL_SUBJECT
							: obj1.get(HolmesAppConstants.EMAIL_SEARCH_BY).getAsString());
			parentJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER, obj1.get(HolmesAppConstants.BENE_PPT_IDENTIFIER));
			parentJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE, obj1.get(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE));
			
			parentArray.add(parentJson);
		}

		finalJson.add(HolmesAppConstants.KSDFILEJSON, parentArray);
		return finalJson.toString();

	}

	private void convertDOToLayoutMultiSheetDet(JsonArray jsonLayoutConfig, JsonArray detailRecord, JsonObject obj1) {
		for (int j = 0; j < jsonLayoutConfig.size(); j++) {
			JsonElement layoutElement = jsonLayoutConfig.get(j);
			JsonObject obj = layoutElement.getAsJsonObject();

			// Add Labelling Related Code
			String valueDetails = "";
			String fieldTemplate = "";
			JsonArray valueDetailsJsonArray = new JsonArray();
			JsonArray fieldTemplateJsonArray = new JsonArray();

			if (!(obj.get(HolmesAppConstants.VALUE_DETAILS).isJsonNull())) {
				valueDetails = obj.get(HolmesAppConstants.VALUE_DETAILS).getAsString();
			}
			if (!(obj.get(HolmesAppConstants.FIELD_TEMPLATE).isJsonNull())) {
				fieldTemplate = obj.get(HolmesAppConstants.FIELD_TEMPLATE).getAsString();
			}
			if ((!valueDetails.isEmpty()) && valueDetails.length() > 1) {
				String valDet = valueDetails;
				valDet = valDet.substring(1, (valDet.length() - 1));
				valDet = valDet.replace("[", "[\"");
				valDet = valDet.replace("]", "\"]");
				valDet = "[".concat(valDet).concat("]");

				valueDetailsJsonArray = (JsonArray) parser.parse(valDet);

			}
			fieldTemplateJsonArray = convertDOToLayoutDetFieldtemp(fieldTemplateJsonArray, fieldTemplate);

			if (((obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT).isJsonNull()
					|| obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT).getAsString().isEmpty())
					&& (obj1.get(HolmesAppConstants.SHEET_NAME_CONSTANT).isJsonNull()
							|| obj1.get(HolmesAppConstants.SHEET_NAME_CONSTANT).getAsString().isEmpty()))
					|| (obj1.get(HolmesAppConstants.SHEET_NAME_CONSTANT).getAsString()
							.equalsIgnoreCase(obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT).getAsString())))

			{
				JsonObject childJson = new JsonObject();

				childJson.add(HolmesAppConstants.ID, obj.get(HolmesAppConstants.ID));
				childJson.add(HolmesAppConstants.RECORDTYPE, obj.get(HolmesAppConstants.RECORDTYPE));
				childJson.add(HolmesAppConstants.DATA_ELEMENT, obj.get(HolmesAppConstants.MFFIELD_NAME));
				childJson.add(HolmesAppConstants.START_POSITION, obj.get(HolmesAppConstants.STARTPOS));
				childJson.add(HolmesAppConstants.LENGTH, obj.get(HolmesAppConstants.LENGTH));
				childJson.add(HolmesAppConstants.RECORD_IDENTIFIER, obj.get(HolmesAppConstants.RECORD_IDENTIFIER));
				childJson.add(HolmesAppConstants.FORMAT, obj.get(HolmesAppConstants.RECORD_FORMAT));
				childJson.add(HolmesAppConstants.MFFIELDWOUTSPACE, obj.get(HolmesAppConstants.MFFIELDWOUTSPACE));
				childJson.add(HolmesAppConstants.ACTIVEFLAG, obj.get(HolmesAppConstants.ACTIVEFLAG));
				childJson.add(HolmesAppConstants.RECORD_IDENTIFIER_VAL,
						obj.get(HolmesAppConstants.RECORD_IDENTIFIER_VAL));
				childJson.add(HolmesAppConstants.FIELD_TEMPLATE, fieldTemplateJsonArray);
				childJson.add(HolmesAppConstants.VALUE_DETAILS, valueDetailsJsonArray);
				childJson.add(HolmesAppConstants.PRE_FILTER, obj.get(HolmesAppConstants.PRE_FILTER));
				childJson.add(HolmesAppConstants.PRE_FILTER_OPERATOR, obj.get(HolmesAppConstants.PRE_FILTER_OPERATOR));
//				childJson.add(HolmesAppConstants.PRE_FILTER_DATE_TYPE, obj.get(HolmesAppConstants.PRE_FILTER_DATE_TYPE));
//				childJson.add(HolmesAppConstants.PRE_FILTER_DATE_NUMBER, obj.get(HolmesAppConstants.PRE_FILTER_DATE_NUMBER));
//				childJson.add(HolmesAppConstants.PRE_FILTER_DATE_FREQUENCY, obj.get(HolmesAppConstants.PRE_FILTER_DATE_FREQUENCY));
				detailRecord.add(childJson);
			}
		}
	}

	// testing purpose not used now
	public String convertDOToKsdMultiSheet(String layoutDBResponse, String ksdDBResponse, String pjmId) {
		JsonArray jsonLayoutConfig = (JsonArray) parser.parse(layoutDBResponse);
		JsonArray jsonKsdFileDetails = (JsonArray) parser.parse(ksdDBResponse);

		JsonArray parentArray = new JsonArray();

		JsonObject finalJson = new JsonObject();
		for (int i = 0; i < jsonKsdFileDetails.size(); i++) {
			JsonElement ksdElement = jsonKsdFileDetails.get(i);
			JsonArray detailRecord = new JsonArray();
			JsonObject parentJson = new JsonObject();
			JsonObject obj1 = ksdElement.getAsJsonObject();

			convertDOToKsdMultiSheetDet(jsonLayoutConfig, detailRecord, obj1);

			if (obj1.has(HolmesAppConstants.CREATED_BY) || obj1.has(HolmesAppConstants.UPDATED_BY) || obj1.has(FILENAME)
					|| obj1.has(HolmesAppConstants.PJMID)) {

				parentJson.add(HolmesAppConstants.PROCESSJOBMAPPINGID,
						obj1.get(HolmesAppConstants.PROCESSJOBMAPPINGID));
			}

			String whitelistSSNvalues = "";
			JsonArray whiteListssnJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.WHITE_LIST_SSN).isJsonNull()))
					&& (obj1.get(HolmesAppConstants.WHITE_LIST_SSN).toString().length() > 3)) {
				whitelistSSNvalues = obj1.get(HolmesAppConstants.WHITE_LIST_SSN).getAsString();
				String decryptedValues = decrypt(whitelistSSNvalues, pjmId);
				whitelistSSNvalues = decryptedValues;

			}
			if (!whitelistSSNvalues.isEmpty() && whitelistSSNvalues.length() > 1) {
				whiteListssnJsonArray = (JsonArray) parser.parse(whitelistSSNvalues);

			}

			String useLableingRpt = "";
			JsonArray useLableingRptJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.USE_LABELLING_RPT).isJsonNull()))) {
				useLableingRpt = obj1.get(HolmesAppConstants.USE_LABELLING_RPT).getAsString();
				useLableingRptJsonArray = (JsonArray) parser.parse(useLableingRpt);
			}

			parentJson.add(HolmesAppConstants.FILE_NAME, obj1.get(HolmesAppConstants.FILE_NAME));
			parentJson.add(HolmesAppConstants.FILE_FORMAT, obj1.get(HolmesAppConstants.FILE_FORMAT_TYPE));
			parentJson.add(HolmesAppConstants.SUBJECT, obj1.get(HolmesAppConstants.SUBJ));
			parentJson.add(HolmesAppConstants.SENDER, obj1.get(HolmesAppConstants.SENDR));
			parentJson.add(HolmesAppConstants.SOURCE, obj1.get(HolmesAppConstants.SOURCE));
			parentJson.add("path", obj1.get("path"));
			parentJson.add(HolmesAppConstants.THRESHOLD_MIN, obj1.get(HolmesAppConstants.MIN_THRESHOLD));
			parentJson.add(HolmesAppConstants.THRESHOLD_MAX, obj1.get(HolmesAppConstants.MAX_THRESHOLD));
			parentJson.add(HolmesAppConstants.VARIANCE, obj1.get(HolmesAppConstants.VARIATION));
			parentJson.add(HolmesAppConstants.DELIMETER, obj1.get(HolmesAppConstants.DELIMETER));
			parentJson.add(HolmesAppConstants.PPT_IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIER));
			parentJson.add(HolmesAppConstants.IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIERTYPE));
			parentJson.add(HolmesAppConstants.FILETYPE, obj1.get(HolmesAppConstants.FILETYPE));
			parentJson.add(HolmesAppConstants.DATE_FORMAT, obj1.get(HolmesAppConstants.DATE_FORMAT));
			parentJson.add(HolmesAppConstants.CREATED_BY, obj1.get(HolmesAppConstants.CREATED_BY));
			parentJson.add(HolmesAppConstants.UPDATED_BY, obj1.get(HolmesAppConstants.UPDATED_BY));
			parentJson.add(HolmesAppConstants.ID, obj1.get(HolmesAppConstants.ID));
			parentJson.add(HolmesAppConstants.CREATED_DATE, obj1.get(HolmesAppConstants.CREATED_DATE));
			parentJson.add(HolmesAppConstants.UPDATED_DATE, obj1.get(HolmesAppConstants.UPDATED_DATE));
			parentJson.add(HolmesAppConstants.RECORD_IDENTIFIER_COL,
					obj1.get(HolmesAppConstants.RECORD_IDENTIFIER_COL));
			parentJson.add("tool", obj1.get("tool"));
			parentJson.add(HolmesAppConstants.DOMAIN, obj1.get(HolmesAppConstants.DOMAIN));
			parentJson.add(HolmesAppConstants.QUERY_JCL_NAME, obj1.get(HolmesAppConstants.QUERY_JCL_NAME));
			parentJson.add(HolmesAppConstants.DATE_GENERATED, obj1.get(HolmesAppConstants.DATE_GENERATED));
			parentJson.add(HolmesAppConstants.DATE_FREQUENCY, obj1.get(HolmesAppConstants.DATE_FREQUENCY));
			parentJson.add(HolmesAppConstants.DATE_PERIOD, obj1.get(HolmesAppConstants.DATE_PERIOD));
			parentJson.add(HolmesAppConstants.DATE_INTERVAL, obj1.get(HolmesAppConstants.DATE_INTERVAL));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS));
			parentJson.add(HolmesAppConstants.SUB_FOLDER, obj1.get(HolmesAppConstants.SUB_FOLDER));
			parentJson.add(HolmesAppConstants.ACTION, obj1.get(HolmesAppConstants.ACTION));
			parentJson.add(HolmesAppConstants.USE_LABELLING_RPT, useLableingRptJsonArray);
			parentJson.add(HolmesAppConstants.CLIENT, obj1.get(HolmesAppConstants.CLIENT));
			parentJson.add(HolmesAppConstants.DATABASE, obj1.get(HolmesAppConstants.DATABASE));
			parentJson.add(HolmesAppConstants.SQL_QUERY, obj1.get(HolmesAppConstants.SQL_QUERY));
			parentJson.add(HolmesAppConstants.RECORD_CNT_CHECK, obj1.get(HolmesAppConstants.RECORD_CNT_CHECK));
			parentJson.add(HolmesAppConstants.WHITE_LIST_SSN, whiteListssnJsonArray);
			parentJson.add(HolmesAppConstants.DETAILRECORDS, detailRecord);
			// DynRepNameChanges
			parentJson.add(HolmesAppConstants.APPEND_NAME_FLAG, obj1.get(HolmesAppConstants.APPEND_NAME_FLAG));
			parentJson.add(HolmesAppConstants.FILE_NAME_TEMPLATE, obj1.get(HolmesAppConstants.FILE_NAME_TEMPLATE));
			parentJson.add(HolmesAppConstants.PRIMARYFILE, obj1.get(HolmesAppConstants.PRIMARYFILE));
			parentJson.add(HolmesAppConstants.VERIFYEMAILONLY, obj1.get(HolmesAppConstants.VERIFYEMAILONLY));
			parentJson.addProperty(HolmesAppConstants.EMAIL_SEARCH_BY,
					obj1.get(HolmesAppConstants.EMAIL_SEARCH_BY).isJsonNull() ? HolmesAppConstants.EMAIL_SUBJECT
							: obj1.get(HolmesAppConstants.EMAIL_SEARCH_BY).getAsString());
			parentJson.add(HolmesAppConstants.SAMPLING_COUNT, obj1.get(HolmesAppConstants.SAMPLING_COUNT));
			parentJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER, obj1.get(HolmesAppConstants.BENE_PPT_IDENTIFIER));
			parentJson.add(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE, obj1.get(HolmesAppConstants.BENE_PPT_IDENTIFIER_TYPE));
			parentArray.add(parentJson);
		}

		finalJson.add(HolmesAppConstants.KSDFILEJSON, parentArray);
		return finalJson.toString();

	}

	private void convertDOToKsdMultiSheetDet(JsonArray jsonLayoutConfig, JsonArray detailRecord, JsonObject obj1) {
		for (int j = 0; j < jsonLayoutConfig.size(); j++) {
			JsonElement layoutElement = jsonLayoutConfig.get(j);
			JsonObject obj = layoutElement.getAsJsonObject();
			// Add Labelling Related Code
			String valueDetails = "";
			String fieldTemplate = "";
			JsonArray valueDetailsJsonArray = new JsonArray();
			JsonArray fieldTemplateJsonArray = new JsonArray();
			if (!(obj.get(HolmesAppConstants.VALUE_DETAILS).isJsonNull())) {
				valueDetails = obj.get(HolmesAppConstants.VALUE_DETAILS).getAsString();
			}
			if (!(obj.get(HolmesAppConstants.FIELD_TEMPLATE).isJsonNull())) {
				fieldTemplate = obj.get(HolmesAppConstants.FIELD_TEMPLATE).getAsString();
			}
			if (!valueDetails.isEmpty() && valueDetails.length() > 1) {
				String valDet = valueDetails;
				valDet = valDet.substring(1, (valDet.length() - 1));
				valDet = valDet.replace("[", "[\"");
				valDet = valDet.replace("]", "\"]");
				valDet = "[".concat(valDet).concat("]");

				valueDetailsJsonArray = (JsonArray) parser.parse(valDet);

			}
			fieldTemplateJsonArray = convertDOToLayoutDetFieldtemp(fieldTemplateJsonArray, fieldTemplate);
			if (obj1.get(HolmesAppConstants.UPDATED_BY).getAsString()
					.equalsIgnoreCase(obj.get(HolmesAppConstants.UPDATED_BY).getAsString())) {
				JsonObject childJson = new JsonObject();

				String activeFlagString = obj.get(HolmesAppConstants.ACTIVEFLAG).getAsString();
				if (activeFlagString.contains(HolmesAppConstants.ACTIVE_FLAG_VALUE)) {

					childJson.add(HolmesAppConstants.ID, obj.get(HolmesAppConstants.ID));
					childJson.add(HolmesAppConstants.RECORDTYPE, obj.get(HolmesAppConstants.RECORDTYPE));
					childJson.add(HolmesAppConstants.DATA_ELEMENT, obj.get(HolmesAppConstants.MFFIELD_NAME));
					childJson.add(HolmesAppConstants.START_POSITION, obj.get(HolmesAppConstants.STARTPOS));
					childJson.add(HolmesAppConstants.LENGTH, obj.get(HolmesAppConstants.LENGTH));
					childJson.add(HolmesAppConstants.RECORD_IDENTIFIER, obj.get(HolmesAppConstants.RECORD_IDENTIFIER));
					childJson.add(HolmesAppConstants.FORMAT, obj.get(HolmesAppConstants.RECORD_FORMAT));
					childJson.add(HolmesAppConstants.MFFIELDWOUTSPACE, obj.get(HolmesAppConstants.MFFIELDWOUTSPACE));
					childJson.add(HolmesAppConstants.ACTIVEFLAG, obj.get(HolmesAppConstants.ACTIVEFLAG));
					childJson.add(HolmesAppConstants.RECORD_IDENTIFIER_VAL,
							obj.get(HolmesAppConstants.RECORD_IDENTIFIER_VAL));
					childJson.add(HolmesAppConstants.FIELD_TEMPLATE, fieldTemplateJsonArray);
					childJson.add(HolmesAppConstants.VALUE_DETAILS, valueDetailsJsonArray);
					childJson.add(HolmesAppConstants.PRE_FILTER, obj.get(HolmesAppConstants.PRE_FILTER));
					childJson.add(HolmesAppConstants.PRE_FILTER_OPERATOR, obj.get(HolmesAppConstants.PRE_FILTER_OPERATOR));
//					childJson.add(HolmesAppConstants.PRE_FILTER_DATE_TYPE, obj.get(HolmesAppConstants.PRE_FILTER_DATE_TYPE));
//					childJson.add(HolmesAppConstants.PRE_FILTER_DATE_NUMBER, obj.get(HolmesAppConstants.PRE_FILTER_DATE_NUMBER));
//					childJson.add(HolmesAppConstants.PRE_FILTER_DATE_FREQUENCY, obj.get(HolmesAppConstants.PRE_FILTER_DATE_FREQUENCY));
					detailRecord.add(childJson);
				}

			}
		}
	}

	private String encryptData(List<String> whiteSsn, String secretId) {
		List<String> encreptVaues = new ArrayList<>();
		for (int i = 0; i < whiteSsn.size(); i++) {
			String witeList = whiteSsn.get(i);
			String encryptedValue = com.wipro.fipc.utils.SecretUtils.encrypt(witeList, secretId);
			encreptVaues.add(encryptedValue);
		}

		return encreptVaues.toString();
	}

	private String decrypt(String whiteSsnList, String secretId) {

		whiteSsnList = whiteSsnList.replace("[", "[\"");
		whiteSsnList = whiteSsnList.replace("]", "\"]");
		whiteSsnList = whiteSsnList.replace(",", "\",");
		whiteSsnList = whiteSsnList.replace(" ", " \"");

		JsonArray encrArray = (JsonArray) parser.parse(whiteSsnList);
		List<String> decryptValues = new ArrayList<>();
		for (int i = 0; i < encrArray.size(); i++) {

			String witeList = encrArray.get(i).getAsString();
			String decrypt = com.wipro.fipc.utils.SecretUtils.decrypt(witeList, secretId);
			decryptValues.add(decrypt);
		}

		return decryptValues.toString();
	}

	public String convertDOToLayoutMultiSheetLatest(String layoutDBResponse, String ksdDBResponse, String pjmId) {

		JsonArray jsonLayoutConfig = (JsonArray) parser.parse(layoutDBResponse);
		JsonArray jsonKsdFileDetails = (JsonArray) parser.parse(ksdDBResponse);

		JsonArray parentArray = new JsonArray();

		JsonObject finalJson = new JsonObject();
		for (int i = 0; i < jsonKsdFileDetails.size(); i++) {
			JsonElement ksdElement = jsonKsdFileDetails.get(i);
			JsonArray detailRecord = new JsonArray();
			JsonObject parentJson = new JsonObject();
			JsonObject obj1 = ksdElement.getAsJsonObject();

			convertDOToLayoutMultiSheetDet(jsonLayoutConfig, detailRecord, obj1);

			if (obj1.has(HolmesAppConstants.CREATED_BY) || obj1.has(HolmesAppConstants.UPDATED_BY) || obj1.has(FILENAME)
					|| obj1.has(HolmesAppConstants.PJMID)) {

				parentJson.add(HolmesAppConstants.PROCESSJOBMAPPINGID, obj1.get("processJobMappingId"));
			}
			String whitelistSSNvalues = "";
			JsonArray whiteListssnJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.WHITE_LIST_SSN).isJsonNull()))
					&& obj1.get(HolmesAppConstants.WHITE_LIST_SSN).toString().length() > 3) {
				whitelistSSNvalues = obj1.get(HolmesAppConstants.WHITE_LIST_SSN).getAsString();
				String decryptedValues = decrypt(whitelistSSNvalues, pjmId);
				whitelistSSNvalues = decryptedValues;

			}
			if (!whitelistSSNvalues.isEmpty() && whitelistSSNvalues.length() > 1) {
				whiteListssnJsonArray = (JsonArray) parser.parse(whitelistSSNvalues);

			}

			String useLableingRpt = "";
			JsonArray useLableingRptJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.USE_LABELLING_RPT).isJsonNull()))) {
				useLableingRpt = obj1.get(HolmesAppConstants.USE_LABELLING_RPT).getAsString();
				useLableingRptJsonArray = (JsonArray) parser.parse(useLableingRpt);
			}

			parentJson.add(HolmesAppConstants.FILE_NAME, obj1.get(HolmesAppConstants.FILE_NAME));
			parentJson.add(HolmesAppConstants.FILE_FORMAT, obj1.get(HolmesAppConstants.FILE_FORMAT_TYPE));
			parentJson.add(HolmesAppConstants.SUBJECT, obj1.get(HolmesAppConstants.SUBJ));
			parentJson.add(HolmesAppConstants.SENDER, obj1.get(HolmesAppConstants.SENDR));
			parentJson.add(HolmesAppConstants.SOURCE, obj1.get(HolmesAppConstants.SOURCE));
			parentJson.add("path", obj1.get("path"));
			parentJson.add(HolmesAppConstants.THRESHOLD_MIN, obj1.get(HolmesAppConstants.MIN_THRESHOLD));
			parentJson.add(HolmesAppConstants.THRESHOLD_MAX, obj1.get(HolmesAppConstants.MAX_THRESHOLD));
			parentJson.add(HolmesAppConstants.VARIANCE, obj1.get(HolmesAppConstants.VARIATION));
			parentJson.add(HolmesAppConstants.DELIMETER, obj1.get(HolmesAppConstants.DELIMETER));
			parentJson.add(HolmesAppConstants.PPT_IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIER));
			parentJson.add(HolmesAppConstants.IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIERTYPE));
			parentJson.add(HolmesAppConstants.FILETYPE, obj1.get(HolmesAppConstants.FILETYPE));
			parentJson.add(HolmesAppConstants.DATE_FORMAT, obj1.get(HolmesAppConstants.DATE_FORMAT));
			parentJson.add(HolmesAppConstants.CREATED_BY, obj1.get(HolmesAppConstants.CREATED_BY));
			parentJson.add(HolmesAppConstants.UPDATED_BY, obj1.get(HolmesAppConstants.UPDATED_BY));
			parentJson.add(HolmesAppConstants.ID, obj1.get(HolmesAppConstants.ID));
			parentJson.add(HolmesAppConstants.CREATED_DATE, obj1.get(HolmesAppConstants.CREATED_DATE));
			parentJson.add(HolmesAppConstants.UPDATED_DATE, obj1.get(HolmesAppConstants.UPDATED_DATE));
			parentJson.add(HolmesAppConstants.RECORD_IDENTIFIER_COL,
					obj1.get(HolmesAppConstants.RECORD_IDENTIFIER_COL));
			parentJson.add(HolmesAppConstants.SHEET_NAME_CONSTANT, obj1.get(HolmesAppConstants.SHEET_NAME_CONSTANT));
			parentJson.add(HolmesAppConstants.ACTIVEFLAG, obj1.get(HolmesAppConstants.ACTIVEFLAG));

			parentJson.addProperty(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT,
					obj1.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT).isJsonNull() ? ""
							: obj1.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT).getAsString());
			parentJson.addProperty(HolmesAppConstants.FILE_NAME_WOUT_SPACE,
					obj1.get(HolmesAppConstants.FILE_NAME_WOUT_SPACE).isJsonNull() ? ""
							: obj1.get(HolmesAppConstants.FILE_NAME_WOUT_SPACE).getAsString());
			parentJson.add("tool", obj1.get("tool"));
			parentJson.add(HolmesAppConstants.DOMAIN, obj1.get(HolmesAppConstants.DOMAIN));
			parentJson.add(HolmesAppConstants.QUERY_JCL_NAME, obj1.get(HolmesAppConstants.QUERY_JCL_NAME));
			parentJson.add(HolmesAppConstants.DATE_GENERATED, obj1.get(HolmesAppConstants.DATE_GENERATED));
			parentJson.add(HolmesAppConstants.DATE_FREQUENCY, obj1.get(HolmesAppConstants.DATE_FREQUENCY));
			parentJson.add(HolmesAppConstants.DATE_PERIOD, obj1.get(HolmesAppConstants.DATE_PERIOD));
			parentJson.add(HolmesAppConstants.DATE_INTERVAL, obj1.get(HolmesAppConstants.DATE_INTERVAL));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS));
			parentJson.add(HolmesAppConstants.SUB_FOLDER, obj1.get(HolmesAppConstants.SUB_FOLDER));
			parentJson.add(HolmesAppConstants.ACTION, obj1.get(HolmesAppConstants.ACTION));
			parentJson.add(HolmesAppConstants.USE_LABELLING_RPT, useLableingRptJsonArray);
			parentJson.add(HolmesAppConstants.CLIENT, obj1.get(HolmesAppConstants.CLIENT));
			parentJson.add(HolmesAppConstants.DATABASE, obj1.get(HolmesAppConstants.DATABASE));
			parentJson.add(HolmesAppConstants.SQL_QUERY, obj1.get(HolmesAppConstants.SQL_QUERY));
			parentJson.add(HolmesAppConstants.RECORD_CNT_CHECK, obj1.get(HolmesAppConstants.RECORD_CNT_CHECK));
			parentJson.add(HolmesAppConstants.WHITE_LIST_SSN, whiteListssnJsonArray);
			parentJson.add("detailRecords", detailRecord);

			parentArray.add(parentJson);
		}

		finalJson.add("ksdFileJson", parentArray);
		return finalJson.toString();

	}

	public String convertDOToKsdMultiSheetlatest(String layoutDBResponse, String ksdDBResponse, String pjmId) {
		JsonArray jsonLayoutConfig = (JsonArray) parser.parse(layoutDBResponse);
		JsonArray jsonKsdFileDetails = (JsonArray) parser.parse(ksdDBResponse);

		JsonArray parentArray = new JsonArray();

		JsonObject finalJson = new JsonObject();
		for (int i = 0; i < jsonKsdFileDetails.size(); i++) {
			JsonElement ksdElement = jsonKsdFileDetails.get(i);
			JsonArray detailRecord = new JsonArray();
			JsonObject parentJson = new JsonObject();
			JsonObject obj1 = ksdElement.getAsJsonObject();

			convertDOToKsdMultiSheetDet(jsonLayoutConfig, detailRecord, obj1);

			if (obj1.has(HolmesAppConstants.CREATED_BY) || obj1.has(HolmesAppConstants.UPDATED_BY) || obj1.has(FILENAME)
					|| obj1.has(HolmesAppConstants.PJMID)) {

				parentJson.add(HolmesAppConstants.PROCESSJOBMAPPINGID, obj1.get("processJobMappingId"));
			}

			String whitelistSSNvalues = "";
			JsonArray whiteListssnJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.WHITE_LIST_SSN).isJsonNull()))
					&& (obj1.get(HolmesAppConstants.WHITE_LIST_SSN).toString().length() > 3)) {
				whitelistSSNvalues = obj1.get(HolmesAppConstants.WHITE_LIST_SSN).getAsString();
				String decryptedValues = decrypt(whitelistSSNvalues, pjmId);
				whitelistSSNvalues = decryptedValues;

			}
			if (!whitelistSSNvalues.isEmpty() && whitelistSSNvalues.length() > 1) {
				whiteListssnJsonArray = (JsonArray) parser.parse(whitelistSSNvalues);

			}

			String useLableingRpt = "";
			JsonArray useLableingRptJsonArray = new JsonArray();
			if ((!(obj1.get(HolmesAppConstants.USE_LABELLING_RPT).isJsonNull()))) {
				useLableingRpt = obj1.get(HolmesAppConstants.USE_LABELLING_RPT).getAsString();
				useLableingRptJsonArray = (JsonArray) parser.parse(useLableingRpt);
			}

			parentJson.add(HolmesAppConstants.FILE_NAME, obj1.get(HolmesAppConstants.FILE_NAME));
			parentJson.add(HolmesAppConstants.FILE_FORMAT, obj1.get(HolmesAppConstants.FILE_FORMAT_TYPE));
			parentJson.add(HolmesAppConstants.SUBJECT, obj1.get(HolmesAppConstants.SUBJ));
			parentJson.add(HolmesAppConstants.SENDER, obj1.get(HolmesAppConstants.SENDR));
			parentJson.add(HolmesAppConstants.SOURCE, obj1.get(HolmesAppConstants.SOURCE));
			parentJson.add("path", obj1.get("path"));
			parentJson.add(HolmesAppConstants.THRESHOLD_MIN, obj1.get(HolmesAppConstants.MIN_THRESHOLD));
			parentJson.add(HolmesAppConstants.THRESHOLD_MAX, obj1.get(HolmesAppConstants.MAX_THRESHOLD));
			parentJson.add(HolmesAppConstants.VARIANCE, obj1.get(HolmesAppConstants.VARIATION));
			parentJson.add(HolmesAppConstants.DELIMETER, obj1.get(HolmesAppConstants.DELIMETER));
			parentJson.add(HolmesAppConstants.PPT_IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIER));
			parentJson.add(HolmesAppConstants.IDENTIFIER, obj1.get(HolmesAppConstants.PPTIDENTIFIERTYPE));
			parentJson.add(HolmesAppConstants.FILETYPE, obj1.get(HolmesAppConstants.FILETYPE));
			parentJson.add(HolmesAppConstants.DATE_FORMAT, obj1.get(HolmesAppConstants.DATE_FORMAT));
			parentJson.add(HolmesAppConstants.CREATED_BY, obj1.get(HolmesAppConstants.CREATED_BY));
			parentJson.add(HolmesAppConstants.UPDATED_BY, obj1.get(HolmesAppConstants.UPDATED_BY));
			parentJson.add(HolmesAppConstants.ID, obj1.get(HolmesAppConstants.ID));
			parentJson.add(HolmesAppConstants.CREATED_DATE, obj1.get(HolmesAppConstants.CREATED_DATE));
			parentJson.add(HolmesAppConstants.UPDATED_DATE, obj1.get(HolmesAppConstants.UPDATED_DATE));
			parentJson.add(HolmesAppConstants.RECORD_IDENTIFIER_COL,
					obj1.get(HolmesAppConstants.RECORD_IDENTIFIER_COL));
			parentJson.add("tool", obj1.get("tool"));
			parentJson.add(HolmesAppConstants.DOMAIN, obj1.get(HolmesAppConstants.DOMAIN));
			parentJson.add(HolmesAppConstants.QUERY_JCL_NAME, obj1.get(HolmesAppConstants.QUERY_JCL_NAME));
			parentJson.add(HolmesAppConstants.DATE_GENERATED, obj1.get(HolmesAppConstants.DATE_GENERATED));
			parentJson.add(HolmesAppConstants.DATE_FREQUENCY, obj1.get(HolmesAppConstants.DATE_FREQUENCY));
			parentJson.add(HolmesAppConstants.DATE_PERIOD, obj1.get(HolmesAppConstants.DATE_PERIOD));
			parentJson.add(HolmesAppConstants.DATE_INTERVAL, obj1.get(HolmesAppConstants.DATE_INTERVAL));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME));
			parentJson.add(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS,
					obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME_WS));
			parentJson.add(HolmesAppConstants.SUB_FOLDER, obj1.get(HolmesAppConstants.SUB_FOLDER));
			parentJson.add(HolmesAppConstants.ACTION, obj1.get(HolmesAppConstants.ACTION));
			parentJson.add(HolmesAppConstants.USE_LABELLING_RPT, useLableingRptJsonArray);
			parentJson.add(HolmesAppConstants.CLIENT, obj1.get(HolmesAppConstants.CLIENT));
			parentJson.add(HolmesAppConstants.DATABASE, obj1.get(HolmesAppConstants.DATABASE));
			parentJson.add(HolmesAppConstants.SQL_QUERY, obj1.get(HolmesAppConstants.SQL_QUERY));
			parentJson.add(HolmesAppConstants.RECORD_CNT_CHECK, obj1.get(HolmesAppConstants.RECORD_CNT_CHECK));
			parentJson.add(HolmesAppConstants.WHITE_LIST_SSN, whiteListssnJsonArray);
			parentJson.add("detailRecords", detailRecord);
			parentArray.add(parentJson);
		}

		finalJson.add("ksdFileJson", parentArray);
		return finalJson.toString();

	}

	public KsdFileDetails getKsdFileDetailsNew(List<LayoutRecord> layoutRecordsList, LayoutRequest layoutrequest,
			String adID, String action) {
		if (layoutrequest != null) {
			LoggerUtil.log(getClass(), Level.INFO, "convertLayoutRequestToDONew() method",
					"This is FileNme: " + layoutrequest.getFileName() + " :This PjmId: "
							+ layoutrequest.getProcessJobMappingId() + " :This ADID: " + adID + " :For Action: "
							+ action);
		}

		KsdFileDetails ksdFileDetail = new KsdFileDetails();

		ksdFileDetail.setFileName(layoutrequest.getFileName());

		getKsdFileDetailsDet(layoutrequest, ksdFileDetail);
		if (layoutrequest.getAction() != null) {
			ksdFileDetail.setAction(layoutrequest.getAction());
		}
		if (layoutrequest.getClient() != null) {
			ksdFileDetail.setClient(layoutrequest.getClient());
		}
		if (layoutrequest.getDatabase() != null) {
			ksdFileDetail.setDatabase(layoutrequest.getDatabase());
		}
		if (layoutrequest.getSqlQuery() != null) {
			ksdFileDetail.setSqlQuery(layoutrequest.getSqlQuery());
		}
		if (layoutrequest.getRecordCntCheck() != null) {
			ksdFileDetail.setRecordCntCheck(layoutrequest.getRecordCntCheck());
		}
		// Add LabelingReport field
		if (layoutrequest.getUseLabellingRpt() != null) {
			ksdFileDetail.setUseLabellingRpt(gson.toJson(layoutrequest.getUseLabellingRpt()));
		}

		// newly added for sheet
		if (layoutrequest.getSheetName() != null) {
			ksdFileDetail.setSheetName(layoutrequest.getSheetName());
		}

		if (layoutrequest.getFileNameWoutSpace() != null) {
			ksdFileDetail.setFileNameWoutSpace(layoutrequest.getFileNameWoutSpace());
		} else {
			ksdFileDetail.setFileNameWoutSpace("");
		}
		if (layoutrequest.getSheetNameWoutSpace() != null) {
			ksdFileDetail.setSheetNameWoutSpace(layoutrequest.getSheetNameWoutSpace());
		} else {
			ksdFileDetail.setSheetNameWoutSpace("");
		}
		String pptItentifier = layoutrequest.getPptIdentifier();
		String cleanPPtIdentifier = cleanTextContent(pptItentifier);
		ksdFileDetail.setPptidentifier(cleanPPtIdentifier);
		ksdFileDetail.setPptidentifierType(layoutrequest.getIdentifier());
		ksdFileDetail.setDateFormat(layoutrequest.getDateFormat());
		ksdFileDetail.setFileType(layoutrequest.getFileType());
		ksdFileDetail.setWhitelistSSN("");
		ksdFileDetail.setSamplingCount(layoutrequest.getSamplingCount());
		ksdFileDetail.setBenePptIdentifier(layoutrequest.getBenePptIdentifier());
		ksdFileDetail.setBenePptIdentifierType(layoutrequest.getBenePptIdentifierType());
		ksdFileDetail.verifyFileDateDetailRecord(layoutrequest.isVerifyFileDateDetailRecord());

		Date date = new Date();
		if (action.equals("Save")) {
			ksdFileDetail.setActiveFlag("T");
			ksdFileDetail.setCreatedDate(date);
			ksdFileDetail.setUpdatedDate(date);
			ksdFileDetail.setCreatedBy(adID);
			ksdFileDetail.setUpdatedBy(adID);
		}

		if (action.equals("Modify")) {
			ksdFileDetail.setActiveFlag("T");
			ksdFileDetail.setUpdatedDate(date);
			ksdFileDetail.setUpdatedBy(adID);
			ksdFileDetail.setCreatedBy(layoutrequest.getCreatedBy());
			ksdFileDetail.setCreatedDate(layoutrequest.getCreatedDate());
		}

		if (layoutrequest.getFileType().equalsIgnoreCase("Report")) {
			ksdFileDetail.setSendr(layoutrequest.getSender());
			ksdFileDetail.setSubj(layoutrequest.getSubject());
		}

		ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
		processJobMappingConfig.setId(Long.parseLong(layoutrequest.getProcessJobMappingId()));
		ksdFileDetail.setProcessJobMapping(processJobMappingConfig);

		return ksdFileDetail;
	}

	public List<LayoutConfig> convertLayoutRequestToDONew(List<LayoutRecord> layoutRecordsList,
			LayoutRequest layoutRequest, String adID, String action) {
		if (layoutRequest != null) {
			LoggerUtil.log(getClass(), Level.INFO, "convertLayoutRequestToDONew() method",
					"This is FileNme: " + layoutRequest.getFileName() + " :This PjmId: "
							+ layoutRequest.getProcessJobMappingId() + " :This ADID: " + adID + " : For This Action: "
							+ action);
		}

		List<LayoutConfig> layoutConfigDOList = new ArrayList<>();

		for (int i = 0; i < layoutRecordsList.size(); i++) {

			LayoutConfig record = new LayoutConfig();

			record.setFileName(layoutRequest.getFileName());
			String element = layoutRecordsList.get(i).getDataElement();
			String cleanelement = cleanTextContent(element);
			record.setMfFieldName(cleanelement);

			convertLayoutRequestToDODet(layoutRecordsList, i, record);

			if (layoutRecordsList.get(i).getSheetNameWoutSpace() != null) {
				record.setSheetNameWoutSpace(layoutRecordsList.get(i).getSheetNameWoutSpace());
			} else {
				record.setSheetNameWoutSpace("");
			}
			record.setRecordFormat(layoutRecordsList.get(i).getFormat());
			record.setRecordIdentifier(layoutRecordsList.get(i).getRecordIdentifier());
			ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
			processJobMappingConfig.setId(Long.parseLong(layoutRequest.getProcessJobMappingId()));
			record.setProcessJobMappingConfig(processJobMappingConfig);
			if (action.equals("Save")) {
				record.setActiveFlag("T");
				record.setCreatedBy(adID);
				record.setUpdatedBy(adID);
				record.setCreatedDate(new Date());
				record.setUpdatedDate(new Date());
			}
			if (action.equals("Modify")) {
				record.setActiveFlag(layoutRecordsList.get(i).getActiveFlag());
				record.setUpdatedBy(adID);
				record.setUpdatedDate(new Date());
				record.setCreatedBy(layoutRequest.getCreatedBy());
				record.setCreatedDate(layoutRequest.getCreatedDate());

			}
			layoutConfigDOList.add(record);

		}

		return layoutConfigDOList;
	}

	public List<LayoutConfig> layoutDetailsForMultiSheetNew(List<List<LayoutRecord>> layoutRecordsList,
			ReportSheet request, String adID, String action, String getLayoutDetailsDB) {
		if (request.getKsdFileJson() != null && request.getKsdFileJson().size() > 0) {
			LoggerUtil.log(getClass(), Level.INFO, "layoutDetailsForMultiSheetNew() method",
					"This is FileNme: " + request.getKsdFileJson().get(0).getFileName() + " :This PjmId: "
							+ request.getKsdFileJson().get(0).getProcessJobMappingId() + " :This ADID: " + adID
							+ " : For This Action: " + action);
		}

		List<LayoutConfig> layoutConfigDOList = new ArrayList<>();

		int j = 0;
		for (List<LayoutRecord> list1 : layoutRecordsList) {
			for (int i = 0; i < list1.size(); i++) {

				LayoutConfig record = new LayoutConfig();
				if (request.getKsdFileJson().size() > 1) {
					record.setFileName(request.getKsdFileJson().get(j).getFileName());
				} else {
					record.setFileName(request.getKsdFileJson().get(0).getFileName());
				}
				String element = list1.get(i).getDataElement();
				String cleanelement = cleanTextContent(element);
				record.setMfFieldName(cleanelement);
				layoutDetailsForMultiSheetDetMulVapt(request, j, list1, i, record);

				layoutDetailsForMultiSheetDetMulS(list1, i, record);

				record.setRecordFormat(list1.get(i).getFormat());
				record.setRecordIdentifier(list1.get(i).getRecordIdentifier());
				ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
				processJobMappingConfig.setId(Long.parseLong(request.getKsdFileJson().get(0).getProcessJobMappingId()));
				record.setProcessJobMappingConfig(processJobMappingConfig);
				record.setPreFilter(list1.get(i).getPreFilter());
				record.setPreFilterOperator(list1.get(i).getPreFilterOperator());
//				record.setPreFilterDateType(list1.get(i).getPreFilterDateType());
//				record.setPreFilterDateNumber(list1.get(i).getPreFilterDateNumber());
//				record.setPreFilterDateFrequency(list1.get(i).getPreFilterDateFrequency());
				
                
				if (action.equals("Save")) {
					record.setCreatedBy(adID);
					record.setUpdatedBy(adID);
					record.setCreatedDate(new Date());
					record.setUpdatedDate(new Date());
				}

				if (action.equals("Modify")) {
					JSONObject jsonObj;
					SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
					try {
						JSONArray jsonArr = new JSONArray(getLayoutDetailsDB);
						jsonObj = jsonArr.getJSONObject(0);
						record.setCreatedBy(jsonObj.getString("createdBy"));
						record.setCreatedDate(formatter.parse(jsonObj.getString("createdDate")));
					} catch (JSONException | ParseException e) {
						LoggerUtil.log(getClass(), Level.ERROR, "layoutDetailsForMultiSheetNew",
								"record " + e.getMessage());

					}
					record.setUpdatedBy(adID);
					record.setUpdatedDate(new Date());
				}
				layoutDetailsForMultiSheetDetMulShVapt(request, j, record);
				layoutDetailsForMultiSheetDetMulShee(request, j, record);

				String fieldType = HolmesAppConstants.TEXT;
				String formatType = "";
				String lengthOfDataElementAsString = "0";
				int sizeOfDate = 0;
				int startOfLength = 0;
				int endOfLength = 0;
				if ((list1.get(i).getFormat() != null) && (list1.get(i).getFormat().length() > 0)) {
					formatType = list1.get(i).getFormat().substring(0, 1);
					startOfLength = list1.get(i).getFormat().indexOf("(");
					endOfLength = list1.get(i).getFormat().indexOf(")");
					if (startOfLength == endOfLength) {
						endOfLength++;

					}
					lengthOfDataElementAsString = list1.get(i).getFormat().substring(startOfLength + 1, endOfLength);
				}
				sizeOfDate = layoutDetailsForMultiSheetDetMulSheet(request, j, sizeOfDate);

				int lengthOfDataElement = 0;
				String dataElement = list1.get(i).getDataElement().toLowerCase();
				try {
					lengthOfDataElement = Integer.parseInt(lengthOfDataElementAsString);
				} catch (NumberFormatException nfe) {
					fieldType = HolmesAppConstants.TEXT;
				}

				// checking for leading sign
				fieldType = layoutDetailsForMultiSheetDetMulShe(list1, i, record, fieldType, formatType, sizeOfDate,
						lengthOfDataElement, dataElement);

				record.setFieldType(fieldType);

				layoutConfigDOList.add(record);

			}
			j++;

		}

		return layoutConfigDOList;
	}

	public List<KsdFileDetails> ksdFileDetailsforMultiSheetNew(ReportSheet reportSheet, String adID, String action,
			String getLayoutDetailsDB) {
		if (reportSheet.getKsdFileJson() != null && reportSheet.getKsdFileJson().size() > 0) {
			LoggerUtil.log(getClass(), Level.INFO, "ksdFileDetailsforMultiSheetNew() method",
					"This is FileNme: " + reportSheet.getKsdFileJson().get(0).getFileName() + " :This PjmId: "
							+ reportSheet.getKsdFileJson().get(0).getProcessJobMappingId() + " :This ADID: " + adID
							+ " : For This Action: " + action);
		}

		List<KsdFileDetails> ksdList = new ArrayList<>();
		for (int i = 0; i < reportSheet.getKsdFileJson().size(); i++) {

			KsdFileDetails ksdFileDetail = new KsdFileDetails();
			ReportMultiSheet reportMultiSheet = reportSheet.getKsdFileJson().get(i);
			LoggerUtil.log(getClass(), Level.INFO, "method",
					"Inside KSDfiledetails method, printing each object: " + reportMultiSheet.toString());

			ksdFileDetail.setFileName(reportMultiSheet.getFileName());
			ksdFileDetail.setEmailSearchBy(reportMultiSheet.getEmailSearchBy());
			ksdFileDetailsforMultiSheetDet(ksdFileDetail, reportMultiSheet);

			String pptItentifier = reportMultiSheet.getPptIdentifier();
			String cleanPPtIdentifier = cleanTextContent(pptItentifier);
			ksdFileDetail.setPptidentifier(cleanPPtIdentifier);
			ksdFileDetail.setPptidentifierType(reportMultiSheet.getIdentifier());
			ksdFileDetail.setDateFormat(reportMultiSheet.getDateFormat());
			ksdFileDetail.setFileType(reportMultiSheet.getFileType());
			ksdFileDetail.setActiveFlag(reportMultiSheet.getActiveFlag());
			ksdFileDetail.setVerifyEmailOnly(reportMultiSheet.isVerifyEmailOnly());
			ksdFileDetail.setVerifyFileDateDetailRecord(reportMultiSheet.isVerifyFileDateDetailRecord());

			if (StringUtils.hasText(reportMultiSheet.getSamplingCount()))
				ksdFileDetail.setSamplingCount(reportMultiSheet.getSamplingCount());
			
			if (StringUtils.hasText(reportMultiSheet.getBenePptIdentifier()))
				ksdFileDetail.setBenePptIdentifier(reportMultiSheet.getBenePptIdentifier());
			
			if (StringUtils.hasText(reportMultiSheet.getBenePptIdentifierType()))
				ksdFileDetail.setBenePptIdentifierType(reportMultiSheet.getBenePptIdentifierType());

			if (StringUtils.hasText(reportMultiSheet.getPrimaryFile()))
				ksdFileDetail.setPrimaryFile(reportMultiSheet.getPrimaryFile());
			else
				ksdFileDetail.setPrimaryFile("N");

			Date date = new Date();
			if (action.equals("Save")) {
				ksdFileDetail.setCreatedBy(adID);
				ksdFileDetail.setUpdatedBy(adID);
				ksdFileDetail.setCreatedDate(date);
				ksdFileDetail.setUpdatedDate(date);
			}

			if (action.equals("Modify")) {
				JSONObject jsonObj;
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
				try {
					JSONArray jsonArr = new JSONArray(getLayoutDetailsDB);
					jsonObj = jsonArr.getJSONObject(0);
					ksdFileDetail.setId(jsonObj.getLong("id"));
					ksdFileDetail.setCreatedBy(jsonObj.getString("createdBy"));
					ksdFileDetail.setCreatedDate(formatter.parse(jsonObj.getString("createdDate")));
				} catch (JSONException | ParseException e) {
					LoggerUtil.log(getClass(), Level.ERROR, "ksdFileDetailsforMultiSheetNew",
							"ksdFileDetail " + e.getMessage());

				}

				ksdFileDetail.setUpdatedBy(adID);
				ksdFileDetail.setUpdatedDate(date);
			}

			ksdFileDetailsforMultiSheetDetMultiVapt(ksdFileDetail, reportMultiSheet, date);
			ksdFileDetailsforMultiSheetDetMultiSheet(ksdFileDetail, reportMultiSheet);
			// sheetName also has to be added

			ProcessJobMapping processJobMappingConfig = new ProcessJobMapping();
			processJobMappingConfig.setId(Long.parseLong(reportMultiSheet.getProcessJobMappingId()));
			ksdFileDetail.setProcessJobMapping(processJobMappingConfig);

			if (reportMultiSheet.getSheetName() != null) {
				ksdFileDetail.setSheetName(reportMultiSheet.getSheetName());
			}

			if (reportMultiSheet.getFileNameWoutSpace() != null) {
				ksdFileDetail.setFileNameWoutSpace(reportMultiSheet.getFileNameWoutSpace());
			} else {
				ksdFileDetail.setFileNameWoutSpace("");
			}

			if (reportMultiSheet.getId() != null) {
				ksdFileDetail.setId(Long.parseLong(reportMultiSheet.getId()));
			}
			if (reportMultiSheet.getSheetNameWoutSpace() != null) {
				ksdFileDetail.setSheetNameWoutSpace(reportMultiSheet.getSheetNameWoutSpace());
			} else {
				ksdFileDetail.setSheetNameWoutSpace("");
			}

			ksdList.add(ksdFileDetail);

		}
		return ksdList;
	}

	private void layoutDetailsForMultiSheetDetMulVapt(ReportSheet request, int j, List<LayoutRecord> list1, int i,
			LayoutConfig record) {

		record.setActiveFlag(list1.get(i).getActiveFlag());

		if (list1.get(i).getId() != null) {
			record.setId(Long.parseLong(list1.get(i).getId()));
		}

		if (list1.get(i).getMfFieldWoutSpace() != null) {
			record.setMfFieldWoutSpace(list1.get(i).getMfFieldWoutSpace());
		} else {
			record.setMfFieldWoutSpace("NA");
		}

		if (list1.get(i).getStartPosition() != null) {
			record.setStartPos(list1.get(i).getStartPosition());
		}
		if (list1.get(i).getLength() != null) {
			record.setLength(Integer.parseInt(list1.get(i).getLength()));
		}
	}

	private void layoutDetailsForMultiSheetDetMulShVapt(ReportSheet request, int j, LayoutConfig record) {

		// added newly for sheet
		if (request.getKsdFileJson().get(j).getPptIdentifier() != null) {
			record.setPptIdentifier(request.getKsdFileJson().get(j).getPptIdentifier());
		}
		if (request.getKsdFileJson().get(j).getSheetName() != null) {
			record.setSheetName(request.getKsdFileJson().get(j).getSheetName());
		}
		if (request.getKsdFileJson().get(j).getFileNameWoutSpace() != null) {
			record.setFileNameWoutSpace(request.getKsdFileJson().get(j).getFileNameWoutSpace());
		} else {
			record.setFileNameWoutSpace("");
		}
	}

	private void ksdFileDetailsforMultiSheetDetMultiVapt(KsdFileDetails ksdFileDetail, ReportMultiSheet request1,
			Date date) {

		if (request1.getSubject() != null) {
			ksdFileDetail.setSubj(request1.getSubject());
		}
		if (request1.getSender() != null) {
			ksdFileDetail.setSendr(request1.getSender());
		}

		if (request1.getSource() != null) {
			ksdFileDetail.setSource(request1.getSource());
		}
		if (request1.getPath() != null) {
			ksdFileDetail.setPath(request1.getPath());
		}
		if (request1.getTool() != null) {
			ksdFileDetail.setTool(request1.getTool());
		}
		if (request1.getDomain() != null) {
			ksdFileDetail.setDomain(request1.getDomain());
		}
		if (request1.getQueryJCLName() != null) {
			ksdFileDetail.setQueryJCLName(request1.getQueryJCLName());
		}
		if (request1.getDateGenerated() != null) {
			ksdFileDetail.setDateGenerated(request1.getDateGenerated());
		}
		// DynRepName changes
		if (request1.getAppendNameFlag() != null) {
			ksdFileDetail.setAppendNameFlag(request1.getAppendNameFlag());
		}
		if (request1.getFileNameTemplate() != null) {
			ksdFileDetail.setFileNameTemplate(request1.getFileNameTemplate());
		}
	}
	
	public String convertTrustCodeMapping(boolean isUnAvailableTrustCodeMapping, String clientName,
			String trustCodeMappingValue) {
		LoggerUtil.log(getClass(), Level.INFO, "convertTrustCodeMapping", "convertTrustCodeMapping method start.");
		JsonObject trustCodeMappingJson = new JsonObject();
		JsonArray parentArray = new JsonArray();

		JsonArray trustCodeMappingArray = (JsonArray) JsonParser.parseString(trustCodeMappingValue);
		for (JsonElement trusCodeMappingElement : trustCodeMappingArray) {
			JsonObject childObj = new JsonObject();
			JsonObject obj = trusCodeMappingElement.getAsJsonObject();
			if (isUnAvailableTrustCodeMapping) {
				childObj.add(HolmesAppConstants.COMMENTS, obj.get(HolmesAppConstants.COMMENTS));
				childObj.add(HolmesAppConstants.ROW, obj.get(HolmesAppConstants.ROW));
				childObj.add(HolmesAppConstants.clientId, obj.get(HolmesAppConstants.clientId));
				childObj.add(HolmesAppConstants.clientName, obj.get(HolmesAppConstants.clientName));
				childObj.add(HolmesAppConstants.checkWritterName, obj.get(HolmesAppConstants.checkWritterName));
				childObj.add(HolmesAppConstants.longDescription, obj.get(HolmesAppConstants.longDescription));
				childObj.add(HolmesAppConstants.shortDescription, obj.get(HolmesAppConstants.shortDescription));
			} else {
				childObj.add(HolmesAppConstants.clientName, new JsonPrimitive(clientName));
				childObj.add(HolmesAppConstants.longDescription, obj.get(HolmesAppConstants.attributeCode));
				childObj.add(HolmesAppConstants.shortDescription, obj.get(HolmesAppConstants.attributeText));
			}
			childObj.add(HolmesAppConstants.trustCode, obj.get(HolmesAppConstants.trustCode));
			parentArray.add(childObj);

		}
		trustCodeMappingJson.add(HolmesAppConstants.TRUST_CODE_MAPPING_RESPONSE, parentArray);
		if (isUnAvailableTrustCodeMapping) 
			trustCodeMappingJson.add(HolmesAppConstants.ERROR, JsonParser.parseString("\"\""));
		
		LoggerUtil.log(getClass(), Level.INFO, "convertTrustCodeMapping", "convertTrustCodeMapping method end.");
		return trustCodeMappingJson.toString();
	}
}
