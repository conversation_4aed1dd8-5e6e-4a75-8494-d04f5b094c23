package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFDataValidationConstraint;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.BusinessOpsDao;
import com.wipro.fipc.dao.common.BusinessUnitClientDao;
import com.wipro.fipc.dao.BusinessUnitDao;
import com.wipro.fipc.dao.common.BusinessUnitOpsDao;
import com.wipro.fipc.dao.ClientDetailsDao;
import com.wipro.fipc.dao.ProcessDao;
import com.wipro.fipc.dao.common.ReporteeTypeDao;
import com.wipro.fipc.dao.common.RoleConfigDao;
import com.wipro.fipc.dao.common.UserDetailsDao;
import com.wipro.fipc.dao.tba.ProcessJobMappingDao;
import com.wipro.fipc.entity.BusinessOps;
import com.wipro.fipc.entity.BusinessUnit;
import com.wipro.fipc.entity.ClientDetails;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.Process;
import com.wipro.fipc.entity.common.RoleConfig;
import com.wipro.fipc.model.BusinessOpsBO;
import com.wipro.fipc.model.ClientDetailsBO;
import com.wipro.fipc.model.CustomClientBusinessOps;
import com.wipro.fipc.model.CustomRoleConfig;
import com.wipro.fipc.model.RoleConfigResponse;
import com.wipro.fipc.model.RoleConfigUI;
import com.wipro.fipc.model.generated.CustomBusinessUnitBO;
import com.wipro.fipc.pojo.BusinessOpsBo;
import com.wipro.fipc.pojo.BusinessOpsRoleConfig;
import com.wipro.fipc.pojo.BusinessUnitBo;
import com.wipro.fipc.pojo.BusinessUnitsBo;
import com.wipro.fipc.pojo.ClientDetail;
import com.wipro.fipc.pojo.ClientDetailsBo;
import com.wipro.fipc.pojo.ClientInformation;
import com.wipro.fipc.pojo.ClientInformationBo;
import com.wipro.fipc.pojo.CommonRoleConfig;
import com.wipro.fipc.pojo.CommonRoleConfigBo;
import com.wipro.fipc.pojo.CustomClientDetailsBO;
import com.wipro.fipc.pojo.CustomProcessBO;
import com.wipro.fipc.pojo.ProcessBo;
import com.wipro.fipc.pojo.ProcessRoleConfig;
import com.wipro.fipc.pojo.ProcessType;
import com.wipro.fipc.pojo.RoleConfigBo;
import com.wipro.fipc.pojo.RoleDetails;
import com.wipro.fipc.service.IRoleConfigService;
import com.wipro.fipc.utils.CustomBeanUtils;

@Service
public class RoleConfigServiceImpl implements IRoleConfigService {

	@Autowired
	Environment env;

	@Autowired
	CustomClientBusinessOps customClientBusinessOps;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	GenericDao<RoleConfig> genericDao;

	@Autowired
	UserDetailsDao userDetailsDao;

	@Autowired
	ReporteeTypeDao reporteeTypeDao;

	@Autowired
	private BaseDao<RoleConfig> businessDao;

	@Autowired
	BusinessUnitOpsDao businessUnitOpsDao;

	@Autowired
	BusinessUnitClientDao businessUnitClientDao;

	@Autowired
	ProcessJobMappingDao procesjobMappingDao;

	@Autowired
	private ClientDetailsDao clientdetailsDao;

	@Autowired
	private BusinessUnitDao basBusinessUnitDao;

	@Autowired
	private BusinessOpsDao buOpDao;

	@Autowired
	private ProcessDao proBaseDao;

	@Autowired
	private RoleConfigDao roleConfigDao;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	private static final String SAVE_ROLE_CONFIG_WITH_DUPLICATE = "saveRoleConfigByCheckingDuplicates";
	private static final String GET_REPORTEE_IDS = "getReporteeIds";
	private static final String GET_ROLECONFIG_BY_ADID = "getRoleConfigsByADID";

	protected static final String ROLE_CONFIG = "role_config";
	protected static final String COMMON_SCHEMA = "common";

	@Override
	public Workbook getTemplate(String loggedInUserId) {

		XSSFWorkbook workbook = new XSSFWorkbook();

		LoggerUtil.log(getClass(), Level.INFO, "method", "Updating Excel Data: ");
		XSSFSheet spreadsheet = workbook.createSheet("RoleConfig");

		XSSFCellStyle style = workbook.createCellStyle();
		style.setBorderBottom(BorderStyle.THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderRight(BorderStyle.THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(BorderStyle.THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(BorderStyle.THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillBackgroundColor(IndexedColors.YELLOW.getIndex());
		style.setAlignment(HorizontalAlignment.CENTER);
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		font.setColor(IndexedColors.BLUE.getIndex());
		style.setFont(font);

		XSSFCellStyle dataStyle = workbook.createCellStyle();
		dataStyle.setBorderBottom(BorderStyle.THIN);
		dataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		dataStyle.setBorderRight(BorderStyle.THIN);
		dataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		dataStyle.setBorderTop(BorderStyle.THIN);
		dataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		dataStyle.setBorderLeft(BorderStyle.THIN);
		dataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());

		XSSFRow row;
		XSSFCell cell;

		int rowCount = 0;
		row = spreadsheet.createRow(rowCount);

		cell = row.createCell(row.getLastCellNum() + 1);
		cell.setCellValue("Employee ADID");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.ROLE);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue("Business Unit");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.CLIENT_NAME);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue("Business Ops");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.PROCESS);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.PROCESS_TYPE);
		cell.setCellStyle(style);

		List<String> reporteeIdsDist = getReporteeIds(loggedInUserId);
		LoggerUtil.log(this.getClass(), Level.INFO, GET_REPORTEE_IDS, "reporteeIdsDist : " + reporteeIdsDist);

		XSSFDataValidationHelper dvHelperADID = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint dvConstraintADID = (XSSFDataValidationConstraint) dvHelperADID
				.createExplicitListConstraint(new String[] { "Employee ADID" });
		CellRangeAddressList formateListADID = new CellRangeAddressList(0, 0, 0, 0);
		XSSFDataValidation validationADID = (XSSFDataValidation) dvHelperADID.createValidation(dvConstraintADID,
				formateListADID);
		validationADID.setShowErrorBox(true);
		spreadsheet.addValidationData(validationADID);

		XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
				.createExplicitListConstraint(reporteeIdsDist.stream().toArray(String[]::new));
		CellRangeAddressList formateList = new CellRangeAddressList(1, 1048575, 0, 0);
		XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, formateList);
		validation.setShowErrorBox(true);
		spreadsheet.addValidationData(validation);

		XSSFDataValidationHelper roleHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint roleConstraint = (XSSFDataValidationConstraint) roleHelper
				.createExplicitListConstraint(new String[] { HolmesAppConstants.ROLE });
		CellRangeAddressList roleList = new CellRangeAddressList(0, 0, 1, 1);
		XSSFDataValidation roleValidation = (XSSFDataValidation) roleHelper.createValidation(roleConstraint, roleList);
		roleValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(roleValidation);

		XSSFDataValidationHelper identifierHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint identifierConstraint = (XSSFDataValidationConstraint) identifierHelper
				.createExplicitListConstraint(new String[] { "admin", "manager", "analyst" });
		CellRangeAddressList identifierList = new CellRangeAddressList(1, 1048575, 1, 1);
		XSSFDataValidation identifierValidation = (XSSFDataValidation) identifierHelper
				.createValidation(identifierConstraint, identifierList);
		identifierValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(identifierValidation);

		List<BusinessUnit> businessUnitList = getAllBusinessUnits();
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplate", "businessUnitList" + businessUnitList);
		List<String> businessUnitNames = new ArrayList<>();
		for (BusinessUnit bu : businessUnitList) {
			businessUnitNames.add(bu.getUnitName());
		}
		List<String> businessUnitNamesDist = businessUnitNames.stream().distinct().collect(Collectors.toList());

		XSSFDataValidationHelper unitHeaderHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint unitHeaderConstraint = (XSSFDataValidationConstraint) unitHeaderHelper
				.createExplicitListConstraint(new String[] { "Business Unit" });
		CellRangeAddressList unitHeaderList = new CellRangeAddressList(0, 0, 2, 2);
		XSSFDataValidation unitHeaderValidation = (XSSFDataValidation) unitHeaderHelper
				.createValidation(unitHeaderConstraint, unitHeaderList);
		unitHeaderValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(unitHeaderValidation);

		XSSFDataValidationHelper recordTypeHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint recordTypeConstraint = (XSSFDataValidationConstraint) recordTypeHelper
				.createExplicitListConstraint(businessUnitNamesDist.stream().toArray(String[]::new));
		CellRangeAddressList recordTypeList = new CellRangeAddressList(1, 1048575, 2, 2);
		XSSFDataValidation recordTypeValidation = (XSSFDataValidation) recordTypeHelper
				.createValidation(recordTypeConstraint, recordTypeList);
		recordTypeValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(recordTypeValidation);

		List<ClientDetails> clientDetailsList = getAllClientDetails();
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplate", "clientDetailsList" + clientDetailsList);

		XSSFSheet csheet = workbook.createSheet("Client");
		XSSFRow crow = null;
		XSSFCell ccell = null;
		Name cname = null;
		int crowCnt = 0;
		crow = csheet.createRow(crowCnt);
		ccell = crow.createCell(crow.getLastCellNum() + 1);
		ccell.setCellValue("Client Id");
		ccell.setCellStyle(style);

		ccell = crow.createCell(crow.getLastCellNum());
		ccell.setCellValue(HolmesAppConstants.CLIENT_NAME);
		ccell.setCellStyle(style);
		for (ClientDetails c : clientDetailsList) {
			crow = csheet.createRow(++crowCnt);

			ccell = crow.createCell(crow.getLastCellNum() + 1);
			ccell.setCellValue(c.getClientCode());
			ccell.setCellStyle(dataStyle);

			ccell = crow.createCell(crow.getLastCellNum());
			ccell.setCellValue(c.getClientName());
			ccell.setCellStyle(dataStyle);
		}
		cname = csheet.getWorkbook().createName();
		cname.setRefersToFormula("'Client'!$B$2:$B$300");
		cname.setNameName("CLIENTS");

		XSSFDataValidationHelper clientHeaderHelper1 = new XSSFDataValidationHelper(spreadsheet);
		CellRangeAddressList clientHeaderList1 = new CellRangeAddressList(0, 0, 3, 3);
		DataValidationConstraint clientHeaderConstraint1 = clientHeaderHelper1
				.createExplicitListConstraint(new String[] { HolmesAppConstants.CLIENT_NAME });
		XSSFDataValidation clientHeaderValidation1 = (XSSFDataValidation) clientHeaderHelper1
				.createValidation(clientHeaderConstraint1, clientHeaderList1);
		clientHeaderValidation1.setShowErrorBox(true);
		spreadsheet.addValidationData(clientHeaderValidation1);

		XSSFDataValidationHelper recordTypeHelper1 = new XSSFDataValidationHelper(spreadsheet);
		CellRangeAddressList recordTypeList1 = new CellRangeAddressList(1, 1048575, 3, 3);
		DataValidationConstraint recordTypeConstraint1 = recordTypeHelper1.createFormulaListConstraint("CLIENTS");
		XSSFDataValidation recordTypeValidation1 = (XSSFDataValidation) recordTypeHelper1
				.createValidation(recordTypeConstraint1, recordTypeList1);
		recordTypeValidation1.setShowErrorBox(true);
		spreadsheet.addValidationData(recordTypeValidation1);

		List<BusinessOps> businessOpsList = getAllBusinessOps();
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplate", "businessOpsList" + businessOpsList);
		List<String> businessOpsNames = new ArrayList<>();
		for (BusinessOps bo : businessOpsList) {
			businessOpsNames.add(bo.getOpsName());
		}
		List<String> businessOpsNamesDst = businessOpsNames.stream().distinct().collect(Collectors.toList());

		XSSFDataValidationHelper boHeaderHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint boHeaderConstraint = (XSSFDataValidationConstraint) boHeaderHelper
				.createExplicitListConstraint(new String[] { "Business Ops" });
		CellRangeAddressList boHeaderList = new CellRangeAddressList(0, 0, 4, 4);
		XSSFDataValidation boHeaderValidation = (XSSFDataValidation) boHeaderHelper.createValidation(boHeaderConstraint,
				boHeaderList);
		boHeaderValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(boHeaderValidation);

		XSSFDataValidationHelper boHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint boConstraint = (XSSFDataValidationConstraint) boHelper
				.createExplicitListConstraint(businessOpsNamesDst.stream().toArray(String[]::new));
		CellRangeAddressList boList = new CellRangeAddressList(1, 1048575, 4, 4);
		XSSFDataValidation boValidation = (XSSFDataValidation) boHelper.createValidation(boConstraint, boList);
		boValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(boValidation);

		List<Process> processTypeList = getAllProcess();
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplate", "processTypeList" + processTypeList);
		List<String> processNames = new ArrayList<>();
		for (Process process : processTypeList) {
			processNames.add(process.getProcessName());
		}

		XSSFSheet processsheet = workbook.createSheet(HolmesAppConstants.PROCESS);
		XSSFRow processrow = null;
		XSSFCell processcell = null;
		Name pname = null;
		int rowCnt = 0;
		processrow = processsheet.createRow(rowCnt);
		processcell = processrow.createCell(processrow.getLastCellNum() + 1);
		processcell.setCellValue(HolmesAppConstants.PROCESS);
		processcell.setCellStyle(style);

		processcell = processrow.createCell(processrow.getLastCellNum());
		processcell.setCellValue(HolmesAppConstants.PROCESS_TYPE);
		processcell.setCellStyle(style);
		for (Process p : processTypeList) {
			processrow = processsheet.createRow(++rowCnt);

			processcell = processrow.createCell(processrow.getLastCellNum() + 1);
			processcell.setCellValue(p.getProcessName());
			processcell.setCellStyle(dataStyle);

			processcell = processrow.createCell(processrow.getLastCellNum());
			processcell.setCellValue(p.getProcessType());
			processcell.setCellStyle(dataStyle);
		}

		pname = processsheet.getWorkbook().createName();
		pname.setRefersToFormula("'Process'!$A$2:$A$200");
		pname.setNameName(HolmesAppConstants.PROCESS);

		XSSFDataValidationHelper pHeaderHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint pHeaderConstraint = (XSSFDataValidationConstraint) pHeaderHelper
				.createExplicitListConstraint(new String[] { HolmesAppConstants.PROCESS });
		CellRangeAddressList pHeaderList = new CellRangeAddressList(0, 0, 5, 5);
		XSSFDataValidation pHeaderValidation = (XSSFDataValidation) pHeaderHelper.createValidation(pHeaderConstraint,
				pHeaderList);
		pHeaderValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(pHeaderValidation);

		XSSFDataValidationHelper pHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint pConstraint = (XSSFDataValidationConstraint) pHelper
				.createFormulaListConstraint(HolmesAppConstants.PROCESS);
		CellRangeAddressList pList = new CellRangeAddressList(1, 1048575, 5, 5);
		XSSFDataValidation pValidation = (XSSFDataValidation) pHelper.createValidation(pConstraint, pList);
		pValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(pValidation);

		XSSFDataValidationHelper ptHeaderHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint ptHeaderConstraint = (XSSFDataValidationConstraint) ptHeaderHelper
				.createExplicitListConstraint(new String[] { HolmesAppConstants.PROCESS_TYPE });
		CellRangeAddressList ptHeaderList = new CellRangeAddressList(0, 0, 6, 6);
		XSSFDataValidation ptHeaderValidation = (XSSFDataValidation) ptHeaderHelper.createValidation(ptHeaderConstraint,
				ptHeaderList);
		ptHeaderValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(ptHeaderValidation);

		XSSFDataValidationHelper ptHelper = new XSSFDataValidationHelper(spreadsheet);
		XSSFDataValidationConstraint ptConstraint = (XSSFDataValidationConstraint) ptHelper
				.createExplicitListConstraint(new String[] { "INBOUND", "OUTBOUND", "PROCESS CONTROL" });
		CellRangeAddressList ptList = new CellRangeAddressList(1, 1048575, 6, 6);
		XSSFDataValidation ptValidation = (XSSFDataValidation) ptHelper.createValidation(ptConstraint, ptList);
		ptValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(ptValidation);

		spreadsheet.autoSizeColumn(0);
		spreadsheet.autoSizeColumn(1);
		spreadsheet.autoSizeColumn(2);
		spreadsheet.autoSizeColumn(3);
		spreadsheet.autoSizeColumn(4);
		spreadsheet.autoSizeColumn(5);
		spreadsheet.autoSizeColumn(6);

		LoggerUtil.log(getClass(), Level.INFO, "method", "End of Update Excel Data: ");
		return workbook;
	}

	@Override
	public RoleConfigResponse uploadExcelFile(MultipartFile file, String loggedInUserId) {
		LoggerUtil.log(getClass(), Level.INFO, "uploadExcelFile", "uploadExcelFile()---> Started---->");
		RoleConfigResponse roleConfigData = null;
		String extension = FilenameUtils.getExtension(file.getOriginalFilename());
		if (extension.equalsIgnoreCase("xls") || extension.contentEquals("xlsx")) {
			roleConfigData = readDataFromExcel(file, loggedInUserId);
		}
		LoggerUtil.log(getClass(), Level.INFO, "uploadExcelFile", "uploadExcelFile()---> Ended---->");
		return roleConfigData;
	}

	public RoleConfigResponse readDataFromExcel(MultipartFile file, String loggedInUserId) {

		LoggerUtil.log(getClass(), Level.INFO, HolmesAppConstants.READ_DATA_FROM_EXCEL,
				"readDataFromExcel()---> Started---->");
		Workbook workbook = getWorkBook(file);
		if (workbook != null) {
			Sheet sheet = workbook.getSheetAt(0);
			Iterator<Row> rows = sheet.iterator();

			List<RoleConfig> roleConfigToBeSavedInExcel = new ArrayList<>();
			List<RoleConfigUI> inCorrectRoleConfigListInExcel = new ArrayList<>();

			rows.next();

			try {

				List<BusinessUnit> allBusinessUnits = getAllBusinessUnits();
				LoggerUtil.log(getClass(), Level.INFO, "readDataFromExcel", "allBusinessUnits: " + allBusinessUnits);

				List<ClientDetails> allClients = getAllClientDetails();
				LoggerUtil.log(getClass(), Level.INFO, "readDataFromExcel", "allClients: " + allClients);

				List<BusinessOps> allBusinessOps = getAllBusinessOps();
				LoggerUtil.log(getClass(), Level.INFO, "readDataFromExcel", "allBusinessOps: " + allBusinessOps);

				List<Process> allProcesses = getAllProcess();
				LoggerUtil.log(getClass(), Level.INFO, "readDataFromExcel", "allProcesses: " + allProcesses);

				while (rows.hasNext()) {

					Row row = rows.next();

					String adid = row.getCell(0).getStringCellValue();
					String role = row.getCell(1).getStringCellValue();
					String unitName = row.getCell(2).getStringCellValue();
					String clientName = row.getCell(3).getStringCellValue();

					String message = "";
					boolean flag = true;

					RoleConfig roleConfigInExcel = new RoleConfig();

					roleConfigInExcel.setAdid(adid);
					roleConfigInExcel.setRole(role);
					roleConfigInExcel.setType(role);

					List<BusinessUnit> businessUnitList = allBusinessUnits.stream()
							.filter(object -> object.getUnitName().equals(unitName)).collect(Collectors.toList());
					if (!CollectionUtils.isEmpty(businessUnitList)) {
						roleConfigInExcel.setBusinessUnit(businessUnitList.get(0));

					} else {
						BusinessUnit businessUnitR = new BusinessUnit();
						businessUnitR.setUnitName(unitName);
						roleConfigInExcel.setBusinessUnit(businessUnitR);
						message = "BusinessUnit";
						flag = false;
					}

					List<ClientDetails> clientDetails = allClients.stream()
							.filter(object -> object.getClientName().equals(clientName)).collect(Collectors.toList());

					clientDetailsCheck(clientDetails, roleConfigInExcel, clientName, flag, message);

					Cell opsCell = row.getCell(4);

					opsCellCheck(opsCell, row, allBusinessOps, roleConfigInExcel, flag, message);

					Cell processNameCell = row.getCell(5);
					Cell processTypeCell = row.getCell(6);

					processNameCellCheck(processNameCell, processTypeCell, row, allProcesses, roleConfigInExcel, flag,
							message);

					checkFlag(flag, roleConfigInExcel, loggedInUserId, roleConfigToBeSavedInExcel, message,
							inCorrectRoleConfigListInExcel);

				}

			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.READ_DATA_FROM_EXCEL,
						"Exception occured in readDataFromExcel()----->: " + e);

			}

			LoggerUtil.log(getClass(), Level.INFO, HolmesAppConstants.READ_DATA_FROM_EXCEL,
					"readDataFromExcel()---> No. of roleconfigs sent to save---->" + roleConfigToBeSavedInExcel.size());

			RoleConfigResponse roleConfigResponseInExcel = saveRoleConfigByCheckingDuplicates(
					roleConfigToBeSavedInExcel);
			List<RoleConfigUI> failedList = Stream
					.concat(inCorrectRoleConfigListInExcel.stream(), roleConfigResponseInExcel.getErrorList().stream())
					.collect(Collectors.toList());
			roleConfigResponseInExcel.setErrorList(failedList);

			return roleConfigResponseInExcel;
		}

		return null;
	}

	private void checkFlag(boolean flag, RoleConfig roleConfigInExcel, String loggedInUserId,
			List<RoleConfig> roleConfigToBeSavedInExcel, String message,
			List<RoleConfigUI> inCorrectRoleConfigListInExcel) {
		if (flag) {
			roleConfigInExcel.setOwnedBy(loggedInUserId);
			roleConfigInExcel.setActive(true);
			roleConfigInExcel.setCreatedBy(loggedInUserId);
			roleConfigInExcel.setCreatedDate(new Date());
			roleConfigInExcel.setUpdatedBy("");
			roleConfigToBeSavedInExcel.add(roleConfigInExcel);
		} else {
			RoleConfigUI roleConfigMessage = new RoleConfigUI(roleConfigInExcel);
			roleConfigMessage.setMessage(message);
			inCorrectRoleConfigListInExcel.add(roleConfigMessage);
		}
	}

	private void processNameCellCheck(Cell processNameCell, Cell processTypeCell, Row row, List<Process> allProcesses,
			RoleConfig roleConfigInExcel, boolean flag, String message) {
		if (processNameCell != null && processNameCell.getCellType() != CellType.BLANK && processTypeCell != null
				&& processTypeCell.getCellType() != CellType.BLANK) {
			String processName = row.getCell(5).getStringCellValue();
			String processType = row.getCell(6).getStringCellValue();
			Boolean processFlag = false;
			for (Process checkProcess : allProcesses) {
				if (checkProcess.getProcessName().equals(processName)
						&& checkProcess.getProcessType().equals(processType)) {
					processFlag = true;
					roleConfigInExcel.setProcess(checkProcess);
				}

			}
			if (!Boolean.TRUE.equals(processFlag)) {
				Process process = new Process();
				process.setProcessName(processName);
				process.setProcessType(processType);
				roleConfigInExcel.setProcess(process);

				if (!flag)
					message = message.concat(",Process");
				else
					message = HolmesAppConstants.PROCESS;
				flag = false;

			}
		}
	}

	private void opsCellCheck(Cell opsCell, Row row, List<BusinessOps> allBusinessOps, RoleConfig roleConfigInExcel,
			boolean flag, String message) {
		if (opsCell != null && opsCell.getCellType() != CellType.BLANK) {
			String opsName = row.getCell(4).getStringCellValue();
			List<BusinessOps> businessOpsList = allBusinessOps.stream()
					.filter(object -> object.getOpsName().equals(opsName)).collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(businessOpsList)) {
				roleConfigInExcel.setBusinessOps(businessOpsList.get(0));

			} else {
				BusinessOps businessOpsR = new BusinessOps();
				businessOpsR.setOpsName(opsName);
				roleConfigInExcel.setBusinessOps(businessOpsR);
				if (!flag)
					message = message.concat(",BusinessOps");
				else
					message = "BusinessOps";
				flag = false;
			}
		}
	}

	private void clientDetailsCheck(List<ClientDetails> clientDetails, RoleConfig roleConfigInExcel, String clientName,
			boolean flag, String message) {

		if (!CollectionUtils.isEmpty(clientDetails)) {
			roleConfigInExcel.setClientDetails(clientDetails.get(0));

		} else {
			ClientDetails clientDetailsR = new ClientDetails();
			clientDetailsR.setClientName(clientName);
			roleConfigInExcel.setClientDetails(clientDetailsR);
			if (!flag)
				message = message.concat(",ClientDetails");
			else
				message = "ClientDetails";
			flag = false;
		}
	}

	RoleConfigResponse saveRoleConfigByCheckingDuplicates(List<RoleConfig> roleConfigList) {
		LoggerUtil.log(getClass(), Level.INFO, SAVE_ROLE_CONFIG_WITH_DUPLICATE,
				"saveRoleConfigByCheckingDuplicates()---> Started---->");

		List<RoleConfigUI> successRoleConfigList = new ArrayList<>();

		List<RoleConfigUI> failedRoleConfigList = new ArrayList<>();
		RoleConfigResponse roleConfigResponse = new RoleConfigResponse();

		Long opsIdUrl = null;
		Long processIdUrl = null;

		for (RoleConfig roleConfig : roleConfigList) {

			List<String> columnNames = new ArrayList<>();
			List<String> columnConditions = new ArrayList<>();
			List<String> columnValues = new ArrayList<>();

			if (roleConfig.getBusinessOps() == null) {

				opsIdUrl = null;
				processIdUrl = null;

				columnNames.add(HolmesAppConstants.ADID);
				columnNames.add(HolmesAppConstants.ROLE);
				columnNames.add(HolmesAppConstants.ACTIVE);
				columnNames.add(HolmesAppConstants.BUSINESS_UNIT_ID);
				columnNames.add(HolmesAppConstants.PROCESS_ID);
				columnNames.add(HolmesAppConstants.CLIENT_ID);
				columnNames.add(HolmesAppConstants.BUSINESS_OPS_ID);

				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add("is");
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add("is");

				columnValues.add(roleConfig.getAdid());
				columnValues.add(roleConfig.getRole());
				columnValues.add("true");
				columnValues.add(String.valueOf(roleConfig.getBusinessUnit().getId()));
				columnValues.add(String.valueOf(processIdUrl));
				columnValues.add(String.valueOf(roleConfig.getClientDetails().getId()));
				columnValues.add(String.valueOf(opsIdUrl));

			}

			if (roleConfig.getBusinessOps() != null && roleConfig.getProcess() == null) {

				processIdUrl = null;

				columnNames.add(HolmesAppConstants.ADID);
				columnNames.add(HolmesAppConstants.ROLE);
				columnNames.add(HolmesAppConstants.ACTIVE);
				columnNames.add(HolmesAppConstants.BUSINESS_UNIT_ID);
				columnNames.add(HolmesAppConstants.PROCESS_ID);
				columnNames.add(HolmesAppConstants.CLIENT_ID);
				columnNames.add(HolmesAppConstants.BUSINESS_OPS_ID);

				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add("is");
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);

				columnValues.add(roleConfig.getAdid());
				columnValues.add(roleConfig.getRole());
				columnValues.add("true");
				columnValues.add(String.valueOf(roleConfig.getBusinessUnit().getId()));
				columnValues.add(String.valueOf(processIdUrl));
				columnValues.add(String.valueOf(roleConfig.getClientDetails().getId()));
				columnValues.add(roleConfig.getBusinessOps().getId().toString());

			}

			if (roleConfig.getBusinessOps() != null && roleConfig.getProcess() != null) {

				columnNames.add(HolmesAppConstants.ADID);
				columnNames.add(HolmesAppConstants.ROLE);
				columnNames.add(HolmesAppConstants.ACTIVE);
				columnNames.add(HolmesAppConstants.BUSINESS_UNIT_ID);
				columnNames.add(HolmesAppConstants.PROCESS_ID);
				columnNames.add(HolmesAppConstants.CLIENT_ID);
				columnNames.add(HolmesAppConstants.BUSINESS_OPS_ID);

				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);
				columnConditions.add(HolmesAppConstants.EQUAL);

				columnValues.add(roleConfig.getAdid());
				columnValues.add(roleConfig.getRole());
				columnValues.add("true");
				columnValues.add(String.valueOf(roleConfig.getBusinessUnit().getId()));
				columnValues.add(roleConfig.getProcess().getId().toString());
				columnValues.add(String.valueOf(roleConfig.getClientDetails().getId()));
				columnValues.add(roleConfig.getBusinessOps().getId().toString());

			}

			try {
				List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
						columnConditions, columnValues);
				LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
						"columnConditions: " + columnConditions);
				roleConfigList = genericDao.findByMultiColumnCondition(RoleConfig.class, COMMON_SCHEMA, ROLE_CONFIG,
						columnConditionParams);

				if (roleConfigList != null && roleConfigList.size() > 0) {
					RoleConfig respRoleConfig = roleConfigList.get(0);
					LoggerUtil.log(this.getClass(), Level.INFO, "",
							"Object is already present in RoleConfig table with id : " + respRoleConfig.getId());
					RoleConfigUI roleConfigUiResp = new RoleConfigUI(respRoleConfig);
					roleConfigUiResp.setMessage("Already Present");
					failedRoleConfigList.add(roleConfigUiResp);

				} else {

					LoggerUtil.log(this.getClass(), Level.INFO, "", "response body size is 0 ");

					roleConfig.setId(null);

					RoleConfig result = businessDao.save(roleConfig);

					roleConfigResponse = getRoleConfigResponse(result, roleConfig);
				}
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, SAVE_ROLE_CONFIG_WITH_DUPLICATE,
						"Exception occured in saveRoleConfigByCheckingDuplicates()----->: " + e);

			}
		}

		roleConfigResponse.setErrorList(failedRoleConfigList);
		roleConfigResponse.setSuccessList(successRoleConfigList);
		LoggerUtil.log(getClass(), Level.INFO, SAVE_ROLE_CONFIG_WITH_DUPLICATE,
				"saveRoleConfigByCheckingDuplicates()---> Ended---->");
		return roleConfigResponse;

	}

	private RoleConfigResponse getRoleConfigResponse(RoleConfig result, RoleConfig roleConfig) {
		List<RoleConfigUI> responseStatus = new ArrayList<>();
		RoleConfigResponse roleConfigResponse = new RoleConfigResponse();
		try {
			if (result != null) {
				RoleConfigUI roleConfigMessage = new RoleConfigUI(result);
				roleConfigMessage.setMessage("Successfully Inserted");
				responseStatus.add(roleConfigMessage);
				roleConfigResponse.setSuccessList(responseStatus);
			} else {
				RoleConfigUI roleConfigMessage = new RoleConfigUI(roleConfig);
				roleConfigMessage.setMessage("Failed at DB call.");
				responseStatus.add(roleConfigMessage);
				roleConfigResponse.setErrorList(responseStatus);
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getRoleConfigResponse", "Exception : ", e.getMessage());
		}
		return roleConfigResponse;
	}

	@Override
	public List<RoleConfigUI> getAllRoleConfig(String adId) throws IOException {
		String loggedInUserType = null;
		List<String> reporteeID = null;
		List<RoleConfig> roleConfigAll = null;

		try {
			loggedInUserType = userDetailsDao.getType(adId);
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", "userType not found!", ex);
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "getAllRoleConfig", "Role : " + loggedInUserType);

		List<RoleConfigUI> roleConfigUIList = new ArrayList<>();

		if (loggedInUserType.equalsIgnoreCase("Manager")) {

			try {
				reporteeID = reporteeTypeDao.getReporteesByManagerID(adId);
			} catch (Exception ex) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "", "reporteeId not found!", ex);
			}

			reporteeID.add(adId);

			for (String id : reporteeID) {

				List<RoleConfig> response = genericDao.findRecordByColumn(RoleConfig.class, COMMON_SCHEMA, ROLE_CONFIG,
						"adid", id);
				roleConfigUIList = getRoleConfigUI(response);
			}

		}

		else if (loggedInUserType.equalsIgnoreCase("Admin")) {

			try {
				roleConfigAll = (List<RoleConfig>) businessDao.findAll();
			} catch (Exception ex) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "", "roleConfig data not found!", ex);
			}

			if (roleConfigAll != null && roleConfigAll.size() > 0) {
				for (RoleConfig rc : roleConfigAll) {
					RoleConfigUI roleConfigUI = new RoleConfigUI(rc);
					roleConfigUIList.add(roleConfigUI);
				}
			}
		}

		return roleConfigUIList;
	}

	private List<RoleConfigUI> getRoleConfigUI(List<RoleConfig> response) throws IOException {

		List<RoleConfigUI> roleConfigUIList = new ArrayList<>();

		if (response != null && response.size() > 0) {
			for (RoleConfig rc : response) {
				RoleConfigUI roleConfigUI = new RoleConfigUI(rc);
				roleConfigUIList.add(roleConfigUI);
			}
		}
		return roleConfigUIList;
	}

	private Workbook getWorkBook(MultipartFile file) {
		Workbook workbook = null;
		String extension = FilenameUtils.getExtension(file.getOriginalFilename());
		try {
			if (extension.equalsIgnoreCase("xlsx") || extension.equalsIgnoreCase("xls")) {
				workbook = new XSSFWorkbook(file.getInputStream());
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getWorkBook", "Exception : ", e.getMessage());
		}
		return workbook;
	}

	@Override
	public String deleteRoleConfig(long id) {
		String response = "Status : Operation Not Supported";
		LoggerUtil.log(this.getClass(), Level.INFO, "", "dbResponse : " + response);

		if (response.contains("Success"))
			return "Success";

		return "Failed";
	}

	@Override
	public Object putRoleConfig(List<RoleConfigUI> roleConfigList, String loggedInUserId) {
		LoggerUtil.log(getClass(), Level.INFO, "putRoleConfig", "putRoleConfig()---> Started---->");

		List<RoleConfig> roleConfigToBeSaved = new ArrayList<>();

		List<RoleConfigUI> successRoleConfigList = new ArrayList<>();
		List<RoleConfigUI> failedRoleConfigList = new ArrayList<>();

		RoleConfigResponse finalResponse = new RoleConfigResponse();

		List<BusinessUnit> allBusinessUnits = getAllBusinessUnits();

		LoggerUtil.log(getClass(), Level.INFO, "putRoleConfig", "allBusinessUnits" + allBusinessUnits);
		List<ClientDetails> allClients = getAllClientDetails();
		LoggerUtil.log(getClass(), Level.INFO, "putRoleConfig", "allClients" + allClients);
		List<BusinessOps> allBusinessOps = getAllBusinessOps();
		LoggerUtil.log(getClass(), Level.INFO, "putRoleConfig", "allBusinessOps" + allBusinessOps);
		List<Process> allProcesses = getAllProcess();
		LoggerUtil.log(getClass(), Level.INFO, "putRoleConfig", "allProcesses" + allProcesses);

		List<RoleConfigUI> roleConfigSet = new ArrayList<>(roleConfigList);

		Iterator<RoleConfigUI> item = roleConfigSet.iterator();

		while (item.hasNext()) {

			RoleConfigUI roleConfigUi = item.next();

			RoleConfig roleConfig = new RoleConfig();

			String message = "";

			boolean flag = true;

			List<BusinessUnit> businessUnitList = allBusinessUnits.stream()
					.filter(object -> object.getUnitName().equals(roleConfigUi.getBusinessUnit()))
					.collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(businessUnitList)) {
				roleConfig.setBusinessUnit(businessUnitList.get(0));

			} else {
				BusinessUnit businessUnitR = new BusinessUnit();
				businessUnitR.setUnitName(roleConfigUi.getBusinessUnit());
				roleConfig.setBusinessUnit(businessUnitR);
				message = "BusinessUnit";
				flag = false;
			}

			List<ClientDetails> clientDetails = allClients.stream()
					.filter(object -> object.getClientName().equals(roleConfigUi.getClientName()))
					.collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(clientDetails)) {
				roleConfig.setClientDetails(clientDetails.get(0));

			} else {
				ClientDetails clientDetailsR = new ClientDetails();
				clientDetailsR.setClientName(roleConfigUi.getClientName());
				roleConfig.setClientDetails(clientDetailsR);
				if (!flag)
					message = message.concat(",ClientDetails");
				else
					message = "ClientDetails";
				flag = false;
			}

			flagChecks(roleConfigUi, roleConfig, message, allBusinessOps, flag);

			getDetails(roleConfigUi, roleConfig, message, flag, allProcesses);

			flagCheck(roleConfigUi, flag, roleConfig, loggedInUserId, roleConfigToBeSaved, successRoleConfigList,
					failedRoleConfigList, message);
		}

		finalResponse.setSuccessList(successRoleConfigList);
		finalResponse.setErrorList(failedRoleConfigList);

		if (!CollectionUtils.isEmpty(roleConfigToBeSaved)) {
			RoleConfigResponse roleConfigResponse = saveRoleConfigByCheckingDuplicates(roleConfigToBeSaved);
			finalResponse.setSuccessList(roleConfigResponse.getSuccessList());
			List<RoleConfigUI> fList = Stream
					.concat(failedRoleConfigList.stream(), roleConfigResponse.getErrorList().stream())
					.collect(Collectors.toList());
			finalResponse.setErrorList(fList);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "", "putRoleConfig()----> Ended----> ");
		return finalResponse;

	}

	private void flagCheck(RoleConfigUI roleConfigUi, boolean flag, RoleConfig roleConfig, String loggedInUserId,
			List<RoleConfig> roleConfigToBeSaved, List<RoleConfigUI> successRoleConfigList,
			List<RoleConfigUI> failedRoleConfigList, String message) {
		if (flag) {
			if (roleConfigUi.getId() == null) {
				roleConfig.setAdid(roleConfigUi.getEmployeeAdid());
				roleConfig.setRole(roleConfigUi.getRole());
				roleConfig.setType(roleConfigUi.getRole());
				roleConfig.setOwnedBy(loggedInUserId);
				roleConfig.setActive(true);
				roleConfig.setCreatedBy(loggedInUserId);
				roleConfig.setCreatedDate(new Date());
				roleConfig.setUpdatedBy("");
				roleConfigToBeSaved.add(roleConfig);
			} else {

				RoleConfig roleConfigToBeUpdated = new RoleConfig();

				try {
					List<RoleConfig> roleConfigResponse = genericDao.findRecordByColumn(RoleConfig.class, COMMON_SCHEMA,
							ROLE_CONFIG, "id", roleConfigUi.getId().toString());
					roleConfigToBeUpdated = roleConfigResponse.get(0);

					roleConfigToBeUpdated.setAdid(roleConfigUi.getEmployeeAdid());
					roleConfigToBeUpdated.setRole(roleConfigUi.getRole());
					roleConfigToBeUpdated.setType(roleConfigUi.getRole());
					roleConfigToBeUpdated.setBusinessUnit(roleConfig.getBusinessUnit());
					roleConfigToBeUpdated.setClientDetails(roleConfig.getClientDetails());
					roleConfigToBeUpdated.setBusinessOps(roleConfig.getBusinessOps());
					roleConfigToBeUpdated.setProcess(roleConfig.getProcess());
					roleConfigToBeUpdated.setUpdatedBy(loggedInUserId);
					roleConfigToBeUpdated.setUpdatedDate(new Date());
				} catch (Exception e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "",
							" Failed at DB GET call for retreiving roleconfig by id to update: " + e);
				}

				try {
					businessDao.save(roleConfigToBeUpdated);
					RoleConfigUI roleConfigMessage = new RoleConfigUI(roleConfigToBeUpdated);
					roleConfigMessage.setMessage("Successfully Updated");
					successRoleConfigList.add(roleConfigMessage);
					LoggerUtil.log(this.getClass(), Level.INFO, "", "Successfully updated ");
				} catch (Exception e) {

					RoleConfigUI roleConfigMessage = new RoleConfigUI(roleConfig);
					roleConfigMessage.setMessage("Failed at DB put call.");
					failedRoleConfigList.add(roleConfigMessage);
				}
			}

		} else {
			RoleConfigUI roleConfigMessage = new RoleConfigUI(roleConfig);
			roleConfigMessage.setMessage(message);
			failedRoleConfigList.add(roleConfigMessage);
		}

	}

	private void flagChecks(RoleConfigUI roleConfigUi, RoleConfig roleConfig, String message,
			List<BusinessOps> allBusinessOps, boolean flag) {

		if (roleConfigUi.getBusinessOps() != null && !roleConfigUi.getBusinessOps().isEmpty()) {
			List<BusinessOps> businessOpsList = allBusinessOps.stream()
					.filter(object -> object.getOpsName().equals(roleConfigUi.getBusinessOps()))
					.collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(businessOpsList)) {
				roleConfig.setBusinessOps(businessOpsList.get(0));

			} else {
				BusinessOps businessOpsR = new BusinessOps();
				businessOpsR.setOpsName(roleConfigUi.getBusinessOps());
				roleConfig.setBusinessOps(businessOpsR);
				if (!flag)
					message = message.concat(",BusinessOps");
				else
					message = "BusinessOps";
				flag = false;
			}
		}
	}

	private void getDetails(RoleConfigUI roleConfigUi, RoleConfig roleConfig, String message, boolean flag,
			List<Process> allProcesses) {
		if (roleConfigUi.getProcess() != null && !roleConfigUi.getProcess().isEmpty()
				&& roleConfigUi.getProcessType() != null && !roleConfigUi.getProcessType().isEmpty()) {
			Boolean processFlag = false;
			for (Process checkProcess : allProcesses) {
				if (checkProcess.getProcessName().equals(roleConfigUi.getProcess())
						&& checkProcess.getProcessType().equals(roleConfigUi.getProcessType())) {
					processFlag = true;
					roleConfig.setProcess(checkProcess);
					break;
				}

			}
			if (!Boolean.TRUE.equals(processFlag)) {
				Process process = new Process();
				process.setProcessName(roleConfigUi.getProcess());
				process.setProcessType(roleConfigUi.getProcessType());
				roleConfig.setProcess(process);

				if (!flag)
					message = message.concat(",Process");
				else
					message = HolmesAppConstants.PROCESS;
				flag = false;

			}
		}
	}

	@Override
	public List<CustomRoleConfig> getRoleConfigsByADID(String adId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "methodName", GET_ROLECONFIG_BY_ADID);

		LoggerUtil.log(this.getClass(), Level.INFO, GET_ROLECONFIG_BY_ADID, "Requested ADID= " + adId);

		List<RoleConfig> roleConfigList = getRoleConfigDetails(adId);
		List<CustomRoleConfig> customRoleConfigList = new ArrayList<>();
		CustomRoleConfig customRoleConfig;

		if (!CollectionUtils.isEmpty(roleConfigList)) {
			LoggerUtil.log(this.getClass(), Level.INFO, GET_ROLECONFIG_BY_ADID,
					"RoleConfig List:" + roleConfigList.toString());
			Long businessUnitId;
			Long businessOpsId;
			Long clientId;
			Long processId;
			for (RoleConfig roleConfig : roleConfigList) {
				customRoleConfig = new CustomRoleConfig();
				customRoleConfig.setId(roleConfig.getId());
				customRoleConfig.setAdid(roleConfig.getAdid());
				customRoleConfig.setType(roleConfig.getType());
				customRoleConfig.setRole(roleConfig.getRole());

				clientId = roleConfig.getClientDetails().getId();
				customRoleConfig.setClientId(clientId);
				customRoleConfig.setClientCode(roleConfig.getClientDetails().getClientCode());
				customRoleConfig.setClientName(roleConfig.getClientDetails().getClientName());

				processId = roleConfig.getProcess().getId();
				customRoleConfig.setProcessId(processId);
				customRoleConfig.setProcessType(roleConfig.getProcess().getProcessType());
				customRoleConfig.setProcessName(roleConfig.getProcess().getProcessName());

				businessUnitId = roleConfig.getBusinessUnit().getId();
				customRoleConfig.setUnitId(businessUnitId);
				customRoleConfig.setUnitCode(roleConfig.getBusinessUnit().getUnitCode());
				customRoleConfig.setUnitName(roleConfig.getBusinessUnit().getUnitName());

				businessOpsId = roleConfig.getBusinessOps().getId();
				customRoleConfig.setOpsId(businessOpsId);
				customRoleConfig.setOpsCode(roleConfig.getBusinessOps().getOpsCode());
				customRoleConfig.setOpsName(roleConfig.getBusinessOps().getOpsName());
				customRoleConfig.setActive(roleConfig.getActive());
				customRoleConfigList.add(customRoleConfig);

			}
		} else {
			LoggerUtil.log(this.getClass(), Level.ERROR, GET_ROLECONFIG_BY_ADID, "No details found with request adid");
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getRoleConfigsByAdId",
				"Method getRoleConfigDetails end -> customRoleConfigList: " + customRoleConfigList);

		return customRoleConfigList;

	}

	// Get Call
	public List<RoleConfig> getRoleConfigDetails(String adId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getRoleConfigDetails",
				"Method getRoleConfigDetails start -> adId" + adId);

		List<RoleConfig> roleConfigList = null;
		List<String> columnNames = new ArrayList<>();
		columnNames.add(HolmesAppConstants.ADID);
		columnNames.add(HolmesAppConstants.ACTIVE);

		List<String> columnConditions = new ArrayList<>();
		columnConditions.add("eq");
		columnConditions.add("eq");

		List<String> columnValues = new ArrayList<>();
		columnValues.add(adId);
		columnValues.add("true");
		try {
			List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
					columnConditions, columnValues);
			LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
					"columnConditions: " + columnConditions);
			roleConfigList = genericDao.findByMultiColumnCondition(RoleConfig.class, COMMON_SCHEMA, ROLE_CONFIG,
					columnConditionParams);

			LoggerUtil.log(this.getClass(), Level.INFO, "getRoleConfigDetails", "Role Config List:",
					roleConfigList.toString());
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getRoleConfigDetails",
					"Exception occured while gettingRole Config List:", ex);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getRoleConfigDetails",
				"Method getRoleConfigDetails end -> roleConfigList: " + roleConfigList);
		return roleConfigList;
	}

	@Override
	public List<String> getReporteeIds(String adId) {

		String loggedInUserType = null;
		try {
			loggedInUserType = userDetailsDao.getType(adId);
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getReporteeIds", "Type not found!", ex);
		}

		List<String> reporteeIds = new ArrayList<>();
		LoggerUtil.log(this.getClass(), Level.INFO, GET_REPORTEE_IDS, "User Role : " + loggedInUserType);

		if (loggedInUserType.equalsIgnoreCase("Admin")) {

			try {
				reporteeIds = userDetailsDao.getAllAdid();
			} catch (Exception ex) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "getReporteeIds", "reporteeIds not found!", ex);
			}
		}

		if (loggedInUserType.equalsIgnoreCase("Manager")) {

			try {
				reporteeIds = reporteeTypeDao.getReporteesByManagerID(adId);
				reporteeIds.add(adId);
			} catch (Exception ex) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "getReporteeIds", "reporteeIds not found!", ex);
			}
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "getReporteeIds", "Method end ->> getReporteeIds" + reporteeIds);
		return reporteeIds;
	}

	@Override
	public List<CustomBusinessUnitBO> getBusinessUnitAll() {
		List<CustomBusinessUnitBO> customBusinessUnitBoList = new ArrayList<>();
		List<BusinessUnit> response = null;
		try {
			response = basBusinessUnitDao.findAll();
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "BusinessUnits not found!", "", ex);
		}
		for (BusinessUnit businessUnit : response) {
			CustomBusinessUnitBO customBusinessUnitBO = new CustomBusinessUnitBO();
			customBusinessUnitBO.setId(businessUnit.getId());
			customBusinessUnitBO.setUnitCode(businessUnit.getUnitCode());
			customBusinessUnitBO.setUnitName(businessUnit.getUnitName());

			customBusinessUnitBoList.add(customBusinessUnitBO);
		}
		return customBusinessUnitBoList;
	}

	@Override
	public CustomClientBusinessOps getClientAndBusinessOps(String buId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "",
				"Getting client details and business ops details business unitid");
		List<CustomClientDetailsBO> customClientDetailsBOListOps = null;
		try {
			customClientDetailsBOListOps = businessUnitOpsDao.getListOfClientDetails(Long.valueOf(buId));
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", "customClientDetailsBOListOps not found!", ex);
		}
		Set<BusinessOpsBO> businessOpsBOSet = new HashSet<>();

		for (CustomClientDetailsBO customClientDetailsBO : customClientDetailsBOListOps) {
			BusinessOpsBO businessOpsBO = new BusinessOpsBO();
			businessOpsBO.setId(customClientDetailsBO.getBuOpsId());
			businessOpsBO.setOpsCode(customClientDetailsBO.getOpsCode());
			businessOpsBO.setOpsName(customClientDetailsBO.getOpsName());
			businessOpsBOSet.add(businessOpsBO);
		}
		List<CustomClientDetailsBO> customClientDetailsBOListclient = null;
		try {
			customClientDetailsBOListclient = businessUnitClientDao.getListclientDetails(Long.valueOf(buId));

		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", "customClientDetailsBOListclient not found!", ex);
		}
		Set<ClientDetailsBO> clientDetailsBOSet = new HashSet<>();
		for (CustomClientDetailsBO customClientDetailsBO : customClientDetailsBOListclient) {
			ClientDetailsBO clientDetailsBO = new ClientDetailsBO();
			clientDetailsBO.setId(customClientDetailsBO.getClientId());
			clientDetailsBO.setClientCode(customClientDetailsBO.getClientCode());
			clientDetailsBO.setClientName(customClientDetailsBO.getClientName());
			clientDetailsBOSet.add(clientDetailsBO);
		}

		customClientBusinessOps.setClientDetailsSet(clientDetailsBOSet);
		customClientBusinessOps.setBusinessOpsSet(businessOpsBOSet);
		return customClientBusinessOps;

	}

	@Override
	public Set<CustomProcessBO> getProcessDetails(String buId, String buOpsId, String clientId) {
		String clientID = Integer.toString(customBeanUtils.checkForClientCode(clientId));
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessDetails",
				"Method getProcessDetails start -> buId" + buId + "buOpsId" + buOpsId + "clientId" + clientID);

		List<CustomProcessBO> customProcessList = null;
		try {
			customProcessList = procesjobMappingDao.getRequiredDetails(Long.valueOf(buId), Long.valueOf(buOpsId),
					Long.valueOf(clientID));
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getProcessDetails", "customProcessList not found!", ex);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessDetails", "customProcessList " + customProcessList);
		return new HashSet<>(customProcessList);
	}

	public List<BusinessUnit> getAllBusinessUnits() {
		List<BusinessUnit> allBusinessUnits = null;
		try {
			allBusinessUnits = basBusinessUnitDao.findAll();

		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllBusinessUnits",
					"Exception occured while getting all businessUnits: " + e);
		}
		return allBusinessUnits;
	}

	public List<ClientDetails> getAllClientDetails() {
		List<ClientDetails> allClients = null;
		try {
			allClients = (List<ClientDetails>) clientdetailsDao.findAll();
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllClientDetails",
					"Exception occured while getting all clients: ", ex);
		}
		return allClients;
	}

	public List<BusinessOps> getAllBusinessOps() {
		List<BusinessOps> allBusinessOps = null;
		try {
			allBusinessOps = (List<BusinessOps>) buOpDao.findAll();
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllBusinessOps",
					"Exception occured while getting all businessOps: ", ex);
		}
		return allBusinessOps;
	}

	public List<Process> getAllProcess() {
		List<Process> allProcesses = null;
		try {
			allProcesses = (List<Process>) proBaseDao.findAll();
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllProcess", "Exception occured while getting all process: ",
					ex);
		}
		return allProcesses;
	}

	@Transactional
	@Override
	public List<ClientInformation> getLoginClientInformation(String adid, String role, Long buid, Long boid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformation",
				"getLoginClientInformation started on : " + System.currentTimeMillis());
		List<ClientInformation> clientInformations = new ArrayList<>();
		List<ProcessType> processTypes = new ArrayList<>();
		ClientInformation clientInformation = new ClientInformation();
		List<ClientDetail> clientDetails = new ArrayList<>();
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformation",
				"Calling DB to get ClientDetails : ");
		List<CommonRoleConfig> commonRoleConfigs = roleConfigDao.getClientInformationListData(adid, role, buid, boid);

		for (CommonRoleConfig commonRoleConfig : commonRoleConfigs) {
			if (commonRoleConfig.getBusinessOpsId() != null && commonRoleConfig.getBusinessUnitId() != null
					&& commonRoleConfig.getAdid() != null) {
				ProcessType processType = new ProcessType();
				processType.setProcessId(commonRoleConfig.getProcessId());
				processType.setProcessName(commonRoleConfig.getProcessName());
				processType.setProcessType(commonRoleConfig.getProcessType());
				processTypes.add(processType);

				ClientDetail clientDetail = new ClientDetail();
				clientDetail.setClientCode(commonRoleConfig.getClientDetailsCode());
				clientDetail.setClientId(commonRoleConfig.getClientDetailsId());
				clientDetail.setClientName(commonRoleConfig.getClientDetailsName());
				clientDetails.add(clientDetail);
			}

		}
		clientInformation.setAdid(adid);
		clientInformation.setRole(role);
		clientInformation.setBusinessOpsId(boid);
		clientInformation.setBusinessUnitId(buid);
		clientInformation.setClientDetails(clientDetails);
		clientInformation.setProcessTypes(processTypes);
		clientInformations.add(clientInformation);
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformationBo",
				"getLoginClientInformation ended with : " + System.currentTimeMillis());
		return clientInformations;
	}

	@Transactional
	@Override
	public List<ClientInformationBo> getLoginClientInformationBo(String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformationBo",
				"getLoginClientInformation started on : " + System.currentTimeMillis());

		List<ClientInformationBo> clientInformationBos = new ArrayList<>();
		ClientInformationBo clientInformationBo = new ClientInformationBo();
		CopyOnWriteArrayList<BusinessUnitsBo> businessUnitsBos = new CopyOnWriteArrayList<>();
		Set<Long> idSet = new HashSet<>();
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformationBo",
				"getting ClientInformationBoListData ");
		List<CommonRoleConfigBo> commonRoleConfigs = roleConfigDao.getClientInformationBoListData(adid);
		for (CommonRoleConfigBo bo : commonRoleConfigs) {
			if (CollectionUtils.isEmpty(businessUnitsBos)) {
				BusinessUnitsBo unitsBo = new BusinessUnitsBo();
				idSet.add(bo.getBusinessUnitId());
				unitsBo.setUnitId(bo.getBusinessUnitId());
				unitsBo.setUnitCode(bo.getBusinessUnitCode());
				unitsBo.setUnitName(bo.getBusinessUnitName());
				List<BusinessOpsBo> businessOpsBos = new ArrayList<>();
				BusinessOpsBo opsBo = new BusinessOpsBo();
				opsBo.setOpsId(bo.getBusinessOpsId());
				opsBo.setOpsCode(bo.getBusinessOpsCode());
				opsBo.setOpsName(bo.getBusinessOpsName());
				businessOpsBos.add(opsBo);
				unitsBo.setBusinessOpsBos(businessOpsBos);
				businessUnitsBos.add(unitsBo);
			} else {
				for (BusinessUnitsBo bo2 : businessUnitsBos) {
					if (bo.getBusinessUnitId().equals(bo2.getUnitId())) {
						BusinessUnitsBo unitsBo = new BusinessUnitsBo();
						idSet.add(bo.getBusinessUnitId());
						unitsBo.setUnitId(bo2.getUnitId());
						unitsBo.setUnitCode(bo2.getUnitCode());
						unitsBo.setUnitName(bo2.getUnitName());
						List<BusinessOpsBo> businessOpsBos = bo2.getBusinessOpsBos();
						BusinessOpsBo opsBo = new BusinessOpsBo();
						opsBo.setOpsId(bo.getBusinessOpsId());
						opsBo.setOpsCode(bo.getBusinessOpsCode());
						opsBo.setOpsName(bo.getBusinessOpsName());
						businessOpsBos.add(opsBo);
						unitsBo.setBusinessOpsBos(businessOpsBos);
						businessUnitsBos.remove(bo2);
						businessUnitsBos.add(unitsBo);
					} else {
						BusinessUnitsBo unitsBo = new BusinessUnitsBo();
						idSet.add(bo.getBusinessUnitId());
						unitsBo.setUnitId(bo.getBusinessUnitId());
						unitsBo.setUnitCode(bo.getBusinessUnitCode());
						unitsBo.setUnitName(bo.getBusinessUnitName());
						List<BusinessOpsBo> businessOpsBos = new ArrayList<>();
						BusinessOpsBo opsBo = new BusinessOpsBo();
						opsBo.setOpsId(bo.getBusinessOpsId());
						opsBo.setOpsCode(bo.getBusinessOpsCode());
						opsBo.setOpsName(bo.getBusinessOpsName());
						businessOpsBos.add(opsBo);
						unitsBo.setBusinessOpsBos(businessOpsBos);
						businessUnitsBos.add(unitsBo);
					}
				}
			}
		}
		clientInformationBo.setBusinessUnitsBos(businessUnitsBos);
		clientInformationBos.add(clientInformationBo);
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformationBo",
				"getLoginClientInformation ended with : " + System.currentTimeMillis());
		return clientInformationBos;

	}

	@Transactional
	@Override
	public List<RoleDetails> getLoginInformation(String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
				"getLoginInformation started on : " + System.currentTimeMillis());
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
				"calling DB  to get PrimList by adid : " + adid);
		List<BigInteger> primList = genericDao.getPrimList(adid);
		List<Number> primList1 = new ArrayList<>();
		List<Long> primList2 = new ArrayList<>();
		for (int i = 0; i < primList.size(); i++) {
			primList1.add(primList.get(i).longValue());
		}

		for (int i = 0; i < primList1.size(); i++) {

			primList2.add(primList1.get(i).longValue());

		}
		List<RoleConfigBo> analyst = new ArrayList<>();
		List<RoleConfigBo> manager = new ArrayList<>();
		List<RoleConfigBo> admin = new ArrayList<>();

		List<RoleDetails> roleDetails = new ArrayList<RoleDetails>();
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
				"calling DB  to get RoleConfigDetail by adid : " + adid);
		List<RoleConfig> rolconfig = roleConfigDao.getRoleDetailByAdid(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
				"calling DB  to get RoleListLogin by adid : " + adid);
		List<String> listRole = roleConfigDao.getRoleListLogin(adid);

		rolconfig.forEach(roleConf -> {
			String roleType = roleConf.getRole();

			Long bussinessUnitId = null;
			Long clientDetailsId = null;

			if (roleConf.getBusinessUnit() != null && roleConf.getBusinessUnit().getId() != null) {
				bussinessUnitId = roleConf.getBusinessUnit().getId();
			}

			if (roleConf.getClientDetails() != null && roleConf.getClientDetails().getId() != null) {
				clientDetailsId = roleConf.getClientDetails().getId();
			}

			if (roleType.equalsIgnoreCase("manager")) {
				RoleConfigBo rolconfigBo = new RoleConfigBo();
				BusinessUnit businessUnit = null;
				ClientDetails clientDetail = null;
				LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
						"calling DB  to get ProcessRoleConfig list by adid : " + adid);
				List<ProcessRoleConfig> processRoleConfigs = roleConfigDao.getprocessIdRoleConfigId(adid);
				for (ProcessRoleConfig processRoleConfig : processRoleConfigs) {
					Long processclientId = null;
					Long processId = null;
					processId = processRoleConfig.getProcessId();
					processclientId = processRoleConfig.getClinetId();
					if (clientDetailsId.equals(processclientId)) {
						ProcessBo proces = new ProcessBo();

						if (processId != null) {
							LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
									"calling DB  to get Process  by processId : " + processId);
							Process processObj = proBaseDao.findByUid(processId);
							if (processObj != null && processObj.getProcessName() != null
									&& processObj.getProcessType() != null && processObj.getId() != null) {
								proces.setProcessName(processObj.getProcessName());
								proces.setProcessType(processObj.getProcessType());
								proces.setProcessId(processObj.getId());
							}
						}

						rolconfigBo.setProcessType(proces);
					}

				}
				LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
						"calling DB  to get BusinessOpsRoleConfig list by adid : " + adid);
				List<BusinessOpsRoleConfig> businessOpsRoleConfigs = roleConfigDao.getbusinessOpsIdRoleConfigId(adid);
				for (BusinessOpsRoleConfig businessOpsRoleConfig : businessOpsRoleConfigs) {
					Long bussinessOpsId = null;
					Long bussinessunitId = null;

					bussinessOpsId = businessOpsRoleConfig.getBusinessOpsId();
					bussinessunitId = businessOpsRoleConfig.getBusinessUnitid();
					if (bussinessUnitId.equals(bussinessunitId)) {
						BusinessOpsBo business = new BusinessOpsBo();
						if (bussinessOpsId != null) {
							LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
									"calling DB  to get BusinessOps  by businessOpsId : " + bussinessOpsId);
							BusinessOps businessOpsObject = buOpDao.getBusinessOpsId(bussinessOpsId);
							if (businessOpsObject != null && businessOpsObject.getOpsCode() != null
									&& businessOpsObject.getOpsName() != null && businessOpsObject.getId() != null) {
								business.setOpsCode(businessOpsObject.getOpsCode());
								business.setOpsName(businessOpsObject.getOpsName());
								business.setOpsId(businessOpsObject.getId());
							}
						}
						rolconfigBo.setBusinessOps(business);
					}
				}

				if (bussinessUnitId != null) {
					LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
							"calling DB  to get BusinessUnit  by businessUnitId : " + bussinessUnitId);
					businessUnit = basBusinessUnitDao.getBusinessUnitId(bussinessUnitId);
				}
				BusinessUnitBo businessUn = new BusinessUnitBo();
				if (businessUnit != null && businessUnit.getUnitCode() != null && businessUnit.getUnitName() != null
						&& businessUnit.getId() != null) {
					businessUn.setUnitCode(businessUnit.getUnitCode());
					businessUn.setUnitName(businessUnit.getUnitName());
					businessUn.setUnitId(businessUnit.getId());
				}

				if (clientDetailsId != null) {
					LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
							"calling DB  to get ClientDetails  by clientDetailsId : " + clientDetailsId);
					clientDetail = clientdetailsDao.getClientDetailId(clientDetailsId);
				}
				ClientDetailsBo clientDetails = new ClientDetailsBo();
				if (clientDetail != null && clientDetail.getClientCode() != null && clientDetail.getClientName() != null
						&& clientDetail.getId() != null) {
					clientDetails.setClientCode(clientDetail.getClientCode());
					clientDetails.setClientName(clientDetail.getClientName());
					clientDetails.setClientId(clientDetail.getId());
				}

				rolconfigBo.setBusinessUnit(businessUn);
				rolconfigBo.setClientDetails(clientDetails);

				rolconfigBo.setPjmId(primList2);

				manager.add(rolconfigBo);

			} else if (roleType.equalsIgnoreCase("admin")) {
				RoleConfigBo rolconfigBo = new RoleConfigBo();
				BusinessUnit businessUnit = null;
				ClientDetails clientDetail = null;
				LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
						"calling DB  to get ProcessRoleConfig list by adid : " + adid);
				List<ProcessRoleConfig> processRoleConfigs = roleConfigDao.getprocessIdRoleConfigId(adid);
				for (ProcessRoleConfig processRoleConfig : processRoleConfigs) {
					Long processclientId = null;
					Long processId = null;
					processId = processRoleConfig.getProcessId();
					processclientId = processRoleConfig.getClinetId();
					if (clientDetailsId.equals(processclientId)) {
						ProcessBo proces = new ProcessBo();

						if (processId != null) {
							LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
									"calling DB  to get Process  by processId : " + processId);
							Process processObj = proBaseDao.findByUid(processId);
							if (processObj != null && processObj.getProcessName() != null
									&& processObj.getProcessType() != null && processObj.getId() != null) {
								proces.setProcessName(processObj.getProcessName());
								proces.setProcessType(processObj.getProcessType());
								proces.setProcessId(processObj.getId());
							}
						}

						rolconfigBo.setProcessType(proces);
					}

				}
				LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
						"calling DB  to get BusinessOpsRoleConfig list by adid : " + adid);
				List<BusinessOpsRoleConfig> businessOpsRoleConfigs = roleConfigDao.getbusinessOpsIdRoleConfigId(adid);
				for (BusinessOpsRoleConfig businessOpsRoleConfig : businessOpsRoleConfigs) {
					Long bussinessOpsId = null;
					Long bussinessunitId = null;

					bussinessOpsId = businessOpsRoleConfig.getBusinessOpsId();
					bussinessunitId = businessOpsRoleConfig.getBusinessUnitid();
					if (bussinessUnitId.equals(bussinessunitId)) {
						BusinessOpsBo business = new BusinessOpsBo();
						if (bussinessOpsId != null) {
							LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
									"calling DB  to get BusinessOps  by businessOpsId : " + bussinessOpsId);
							BusinessOps businessOpsObject = buOpDao.getBusinessOpsId(bussinessOpsId);
							if (businessOpsObject != null && businessOpsObject.getOpsCode() != null
									&& businessOpsObject.getOpsName() != null && businessOpsObject.getId() != null) {
								business.setOpsCode(businessOpsObject.getOpsCode());
								business.setOpsName(businessOpsObject.getOpsName());
								business.setOpsId(businessOpsObject.getId());
							}
						}
						rolconfigBo.setBusinessOps(business);
					}
				}

				if (bussinessUnitId != null) {
					LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
							"calling DB  to get BusinessUnit by businessUnitId : " + bussinessUnitId);
					businessUnit = basBusinessUnitDao.getBusinessUnitId(bussinessUnitId);
				}
				BusinessUnitBo businessUn = new BusinessUnitBo();
				if (businessUnit != null && businessUnit.getUnitCode() != null && businessUnit.getUnitName() != null
						&& businessUnit.getId() != null) {
					businessUn.setUnitCode(businessUnit.getUnitCode());
					businessUn.setUnitName(businessUnit.getUnitName());
					businessUn.setUnitId(businessUnit.getId());
				}

				if (clientDetailsId != null) {
					LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
							"calling DB  to get ClientDetails by clientDetailsId : " + clientDetailsId);
					clientDetail = clientdetailsDao.getClientDetailId(clientDetailsId);
				}
				ClientDetailsBo clientDetails = new ClientDetailsBo();
				if (clientDetail != null && clientDetail.getClientCode() != null && clientDetail.getClientName() != null
						&& clientDetail.getId() != null) {
					clientDetails.setClientCode(clientDetail.getClientCode());
					clientDetails.setClientName(clientDetail.getClientName());
					clientDetails.setClientId(clientDetail.getId());
				}

				rolconfigBo.setBusinessUnit(businessUn);
				rolconfigBo.setClientDetails(clientDetails);

				rolconfigBo.setPjmId(primList2);

				admin.add(rolconfigBo);

			} else if (roleType.equalsIgnoreCase("analyst")) {
				RoleConfigBo rolconfigBo = new RoleConfigBo();
				BusinessUnit businessUnit = null;
				ClientDetails clientDetail = null;
				LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
						"calling DB  to get ProcessRoleConfig list by adid : " + adid);
				List<ProcessRoleConfig> processRoleConfigs = roleConfigDao.getprocessIdRoleConfigId(adid);
				for (ProcessRoleConfig processRoleConfig : processRoleConfigs) {
					Long processclientId = null;
					Long processId = null;
					processId = processRoleConfig.getProcessId();
					processclientId = processRoleConfig.getClinetId();
					if (clientDetailsId.equals(processclientId)) {
						ProcessBo proces = new ProcessBo();

						if (processId != null) {
							LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
									"calling DB  to get Process  by process : " + processId);
							Process processObj = proBaseDao.findByUid(processId);
							if (processObj != null && processObj.getProcessName() != null
									&& processObj.getProcessType() != null && processObj.getId() != null) {
								proces.setProcessName(processObj.getProcessName());
								proces.setProcessType(processObj.getProcessType());
								proces.setProcessId(processObj.getId());
							}
						}

						rolconfigBo.setProcessType(proces);
					}

				}
				LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
						"calling DB  to get BusinessOpsRoleConfig list by adid : " + adid);
				List<BusinessOpsRoleConfig> businessOpsRoleConfigs = roleConfigDao.getbusinessOpsIdRoleConfigId(adid);
				for (BusinessOpsRoleConfig businessOpsRoleConfig : businessOpsRoleConfigs) {
					Long bussinessOpsId = null;
					Long bussinessunitId = null;

					bussinessOpsId = businessOpsRoleConfig.getBusinessOpsId();
					bussinessunitId = businessOpsRoleConfig.getBusinessUnitid();
					if (bussinessUnitId.equals(bussinessunitId)) {
						BusinessOpsBo business = new BusinessOpsBo();
						if (bussinessOpsId != null) {
							LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
									"calling DB  to get BusinessOps  by businessOpsId : " + bussinessOpsId);
							BusinessOps businessOpsObject = buOpDao.getBusinessOpsId(bussinessOpsId);
							if (businessOpsObject != null && businessOpsObject.getOpsCode() != null
									&& businessOpsObject.getOpsName() != null && businessOpsObject.getId() != null) {
								business.setOpsCode(businessOpsObject.getOpsCode());
								business.setOpsName(businessOpsObject.getOpsName());
								business.setOpsId(businessOpsObject.getId());
							}
						}
						rolconfigBo.setBusinessOps(business);
					}
				}

				if (bussinessUnitId != null) {
					LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
							"calling DB  to get BusinessUnit by businessUnitId : " + bussinessUnitId);
					businessUnit = basBusinessUnitDao.getBusinessUnitId(bussinessUnitId);
				}
				BusinessUnitBo businessUn = new BusinessUnitBo();
				if (businessUnit != null && businessUnit.getUnitCode() != null && businessUnit.getUnitName() != null
						&& businessUnit.getId() != null) {
					businessUn.setUnitCode(businessUnit.getUnitCode());
					businessUn.setUnitName(businessUnit.getUnitName());
					businessUn.setUnitId(businessUnit.getId());
				}

				if (clientDetailsId != null) {
					LoggerUtil.log(this.getClass(), Level.INFO, "getLoginInformation",
							"calling DB  to get ClientDetails  by clientDetailsId : " + clientDetailsId);
					clientDetail = clientdetailsDao.getClientDetailId(clientDetailsId);
				}
				ClientDetailsBo clientDetails = new ClientDetailsBo();
				if (clientDetail != null && clientDetail.getClientCode() != null && clientDetail.getClientName() != null
						&& clientDetail.getId() != null) {
					clientDetails.setClientCode(clientDetail.getClientCode());
					clientDetails.setClientName(clientDetail.getClientName());
					clientDetails.setClientId(clientDetail.getId());
				}

				rolconfigBo.setBusinessUnit(businessUn);
				rolconfigBo.setClientDetails(clientDetails);

				rolconfigBo.setPjmId(primList2);

				analyst.add(rolconfigBo);

			}

		});

		RoleDetails roleDetails2 = new RoleDetails();
		List<String> role = new ArrayList<>();
		for (String str : listRole) {
			role.add(str);
		}
		roleDetails2.setRole(role);
		roleDetails2.setAnalyst(analyst);
		roleDetails2.setManager(manager);
		roleDetails2.setAdmin(admin);
		roleDetails.add(roleDetails2);
		LoggerUtil.log(this.getClass(), Level.INFO,"getLoginInformation", "getLoginInformation ended with : "+System.currentTimeMillis());
		return roleDetails;
	}

}
