package com.wipro.fipc.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaWelfareInquiryConfig;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.TbaWelfareConfigDto;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.TbaWelfareConfigService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class TbaWelfareInquiryConfigServiceImpl implements TbaWelfareConfigService {

	@Autowired
	private CommonGetAdId commonGetUpdatedBy;
	
	@Autowired
	private TbaWelfareInquiryConfig tbaWelfareInquiryConfig;

	@Override
	public String createTbaWelfareInquiryConfig(List<TbaWelfareConfigDto> entity, String appName, String sessionToken) throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , createTbaWelfareInquiryConfig started on : " + System.currentTimeMillis());
		
		ObjectMapper objectMapper = new ObjectMapper();
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		entity.stream().forEach(s -> {
			s.setCreatedDate(HolmesAppConstants.getNewDate());
			s.setCreatedBy(adId);
			s.setActiveFlag(HolmesAppConstants.ACTIVE_FLAG_VALUE);
			s.setUpdatedDate(HolmesAppConstants.getNewDate());
			s.setUpdatedBy(adId);
		});

		List<CommonResRowBO> mydata = tbaWelfareInquiryConfig.saveIfNotDuplicate(entity);
		String res = objectMapper.writeValueAsString(mydata);
		ResponseDto response = new ResponseDto();
		response.setData(res);
		response.setStatus(HolmesAppConstants.SUCCESS_STATUS);
		response.setMessage(HolmesAppConstants.SAVE_SUCCESS_MESSAGE);
		
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , createTbaWelfareInquiryConfig end, response : " + response);
		return objectMapper.writeValueAsString(response);
	}

	@Override
	public String updateTbaWelfareInquiryConfig(List<TbaWelfareConfigDto> entity, String appName, String sessionToken) throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , updateTbaWelfareInquiryConfig started on : " + System.currentTimeMillis());
		
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		entity.stream().forEach(s -> {
			com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig welfareConfig = tbaWelfareInquiryConfig.findById(s.getId());

			if (welfareConfig != null) {
				s.setCreatedBy(welfareConfig.getCreatedBy());
				s.setCreatedDate(welfareConfig.getCreatedDate());
			}
			s.setActiveFlag(HolmesAppConstants.ACTIVE_FLAG_VALUE);

			s.setUpdatedDate(HolmesAppConstants.getNewDate());
			s.setUpdatedBy(adId);
		});

		ResponseDto response = new ResponseDto();
		List<CommonResRowBO> mydata = tbaWelfareInquiryConfig.saveIfNotDuplicate(entity);
		String res = objectMapper.writeValueAsString(mydata);
		response.setData(res);
		response.setStatus(HolmesAppConstants.SUCCESS_STATUS);
		response.setMessage(HolmesAppConstants.SAVE_SUCCESS_MESSAGE);
		
		LoggerUtil.log(this.getClass(), Level.INFO, "updateTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , updateTbaWelfareInquiryConfig end, response : " + response);
		return objectMapper.writeValueAsString(response);
	}

	@Override
	public String deleteTbaWelfareInquiryConfig(List<CommonDeleteDTO> entity, String appName, String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , deleteTbaWelfareInquiryConfig started on : " + System.currentTimeMillis());
		
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		entity.stream().forEach(s -> {
			s.setUpdatedBy(adId);
			s.setUpdatedDate(HolmesAppConstants.getNewDate());
			s.setActiveFlag("F");
		});
		List<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> newLayout = new ArrayList<>();

		for (CommonDeleteDTO req : entity) {

			com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig obj = new com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig();
			try {
				obj.setId(Long.parseLong(req.getId()));
				obj.setProcessJobMappingId(Long.valueOf(req.getProcessJobMappingId()));
				obj.setUpdatedBy(req.getUpdatedBy());
				obj.setUpdatedDate(req.getUpdatedDate());
				obj.setActiveFlag(req.getActiveFlag());
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "deleteTbaWelfareInquiryConfig",
						"Exception : ",e);
			}
			newLayout.add(obj);
		}

		List<CommonRowBO> mydata = tbaWelfareInquiryConfig.deleteMultipleRows(0, newLayout);
		String res = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "deleteTbaWelfareInquiryConfig", "Exception : " ,e.getMessage());
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , deleteTbaWelfareInquiryConfig end, response : " + res);
		return res;
	}

	@Override
	public String getTbaWelfareInquiryConfig(String columnName, String columnValue) throws IllegalAccessException, InvocationTargetException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , getTbaWelfareInquiryConfig started on : " + System.currentTimeMillis());
		
		List<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> mydata = tbaWelfareInquiryConfig.findByColumn(columnName,
				columnValue);
		List<TbaWelfareConfigDto> tbaWelfareInquiryConfigDtoList = new ArrayList<>();
		for (com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig tbaWelfareInquiryConfig : mydata) {
			TbaWelfareConfigDto configDto = new TbaWelfareConfigDto();
			BeanUtils.copyProperties(configDto, tbaWelfareInquiryConfig);

			configDto.setPracticeAreaCode(StringUtils.hasText(tbaWelfareInquiryConfig.getPracticeAreaCode()) ? tbaWelfareInquiryConfig.getPracticeAreaCode() : "");
			configDto.setTbaFieldName(StringUtils.hasText(tbaWelfareInquiryConfig.getTbaFieldName()) ? tbaWelfareInquiryConfig.getTbaFieldName() : "");
			configDto.setJsonKey(StringUtils.hasText(tbaWelfareInquiryConfig.getJsonKey()) ? tbaWelfareInquiryConfig.getJsonKey() : "");
			configDto.setSubJsonKey(StringUtils.hasText(tbaWelfareInquiryConfig.getSubJsonKey()) ? tbaWelfareInquiryConfig.getSubJsonKey() : "");
			configDto.setFieldType(StringUtils.hasText(tbaWelfareInquiryConfig.getFieldType()) ? tbaWelfareInquiryConfig.getFieldType() : "");
			configDto.setParName(StringUtils.hasText(tbaWelfareInquiryConfig.getParName()) ? tbaWelfareInquiryConfig.getParName() : "");
			configDto.setEventName(StringUtils.hasText(tbaWelfareInquiryConfig.getEventName()) ? tbaWelfareInquiryConfig.getEventName() : "");
			configDto.setEventLongDesc(StringUtils.hasText(tbaWelfareInquiryConfig.getEventLongDesc()) ? tbaWelfareInquiryConfig.getEventLongDesc() : "");

			tbaWelfareInquiryConfigDtoList.add(configDto);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaWelfareInquiryConfig",
				"TbaWelfareInquiryConfigServiceImpl , getTbaWelfareInquiryConfig end, response : " + tbaWelfareInquiryConfigDtoList);
		return new ObjectMapper().writeValueAsString(tbaWelfareInquiryConfigDtoList);
	}
}
