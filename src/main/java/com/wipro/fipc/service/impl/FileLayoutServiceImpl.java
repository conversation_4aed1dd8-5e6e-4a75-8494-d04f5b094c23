package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFDataValidationConstraint;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.KsdFileDetailsDao;
import com.wipro.fipc.dao.tba.TbaInquiryMetaDataDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.filelayout.CustomResponse;
import com.wipro.fipc.entity.filelayout.DatabaseConfig;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.filelayout.LayoutIdentifier;
import com.wipro.fipc.entity.filelayout.OutputReport;
import com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster;
import com.wipro.fipc.fileLayoutsupport.CommonContService;
import com.wipro.fipc.fileLayoutsupport.DatabaseConfigContService;
import com.wipro.fipc.fileLayoutsupport.KsdFileDetailsContService;
import com.wipro.fipc.fileLayoutsupport.KsdOutPutFileDetailsContService;
import com.wipro.fipc.fileLayoutsupport.LayoutConfigContService;
import com.wipro.fipc.fileLayoutsupport.LayoutIdentifierContService;
import com.wipro.fipc.fileLayoutsupport.OutputReportContrService1;
import com.wipro.fipc.fileLayoutsupport.TbaNoticeInqConfigContService;
import com.wipro.fipc.model.LayoutRecord;
import com.wipro.fipc.model.LayoutRequest;
import com.wipro.fipc.model.ReportSheet;
import com.wipro.fipc.model.TrustCodeMappingReport;
import com.wipro.fipc.model.UnAvailableTrustCodeRespnse;
import com.wipro.fipc.model.generated.KsdFileDetails;
import com.wipro.fipc.model.generated.LayoutConfig;
import com.wipro.fipc.service.IFileLayoutService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class FileLayoutServiceImpl implements IFileLayoutService {

	@Autowired
	LayoutDOConverter doConverter;

	@Autowired
	CommonContService objCommonController;

	@Autowired
	KsdOutPutFileDetailsContService objKsdOutPutFileDetailsController;

	@Autowired
	KsdFileDetailsDao ksdFileDetailsDao;

	@Autowired
	TbaNoticeInqConfigContService objTbaNoticeInqConfigController;

	@Autowired
	OutputReportContrService1 objOutputReportController;

	@Autowired
	Gson gson;

	@Autowired
	JsonParser parser;

	@Autowired
	KsdFileDetailsContService objKsdFileDetailsController;

	@Autowired
	LayoutConfigContService objLayoutConfigController;

	@Autowired
	LayoutIdentifierContService objLayoutIdentifierController;

	@Autowired
	DatabaseConfigContService objDatabaseConfigController;

	@Autowired
	private GenericDao<com.wipro.fipc.entity.layoutrule.LayoutConfig> genericDao;

	@Autowired
	private GenericDao<com.wipro.fipc.entity.batch.KsdFileDetails> genericDaoKsdFileDetails;

	@Value("${db.service.url}")
	String dbServiceHost;

	@Value("${db.service.layout.post}")
	String dbLayoutPost;

	@Value("${db.service.ksd.base.url}")
	String ksdConfigBaseUrl;

	@Value("${db.service.ksdfiledeatils}")
	String ksdfiledeatils;

	@Value("${db.service.recIdentifier}")
	String recordIdentifier;

	@Value("${db.layoutconfig.recType}")
	String detailRec;

	@Value("${db.layout.deletefileLayOutInput}")
	String deleteFileUrl;

	@Value("${tbaNotice.getNoticeConfigData}")
	String tbanotice;

	@Value("${db.service.ksdfiledeatilsSaveAllEntities}")
	String ksdfiledeatilsSaveAllEntities;

	@Value("${db.service.dataBaseConfigMasterData}")
	String dataBaseConfigMasterData;

	@Value("${db.service.ksdfiledeatilsLatestRecord}")
	String ksdfiledeatilsLatestRecord;

	@Value("${db.service.ksdfiledeatilsNewMulitQuery}")
	String ksdfiledeatilsNewMulitQuery;

	@Value("${db.service.ksdfiledeatilsNewFindByColumn}")
	String ksdfiledeatilsNewFindByColumn;
	
	@Value("#{'${trustCodeMapping.headers}'.split(',')}")
	private List<String> trustCodeMappingHeaders;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;
	@Autowired
	private GenericDao<com.wipro.fipc.entity.batch.KsdFileDetails> genericKsdFileDetailsDao;
	
	@Autowired
	private TbaInquiryMetaDataDao tbaInquiryMetaDataDao;

	public static final String FAILURE_MESSAGE = "Records are not saved. Contact system administrator.";
	public static final String PLEASE_CHECK = "Please check the ";
	public static final String FORMAT = " format.";
	protected static final String LAYOUT_CONFIG = "LAYOUT_CONFIG";
	protected static final String LAYOUT_SCHEMA = "layout_rule";
	protected static final String KSD_FILE_DETAILS = "KSD_FILE_DETAILS";
	protected static final String KSD_SCHEMA = "emails_scheduler";
	public static final String GETREQUIREDDETAILSBY = "get Required Details By";
	public static final String PLUS = "PLUS";
	public static final String PRCNT = "PRCNT";
	public static final String AMPNT = "AMPNT";

	@Override
	public Workbook getLayoutFile(String processJobMappingId, String fileName) throws IOException {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			LoggerUtil.log(getClass(), Level.INFO, "getLayoutFile",
					"Calling getLayoutDataBasedOnPjmId for getting data for pjmId : " + processJobMappingId);
			List<LayoutRecord> list = getLayoutDataBasedOnPjmId(processJobMappingId, fileName);
			updateExcelData(workbook, list);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLayoutFile()",
					"Json parsing Error due to {} \n" + e.getMessage());
		}
		return workbook;
	}

	private void updateExcelData(XSSFWorkbook workbook, List<LayoutRecord> list) {
		LoggerUtil.log(getClass(), Level.INFO, "updateExcelData", "Updating Excel Data: ");
		XSSFSheet spreadsheet = workbook.createSheet("Layout");
		XSSFCellStyle style = workbook.createCellStyle();
		style.setBorderBottom(BorderStyle.THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderRight(BorderStyle.THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(BorderStyle.THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(BorderStyle.THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);

		XSSFCellStyle dataStyle = workbook.createCellStyle();
		dataStyle.setBorderBottom(BorderStyle.THIN);
		dataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		dataStyle.setBorderRight(BorderStyle.THIN);
		dataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		dataStyle.setBorderTop(BorderStyle.THIN);
		dataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		dataStyle.setBorderLeft(BorderStyle.THIN);
		dataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());

		XSSFRow row;
		XSSFCell cell;

		int rowCount = 0;
		row = spreadsheet.createRow(rowCount);

		cell = row.createCell(row.getLastCellNum() + 1);
		cell.setCellValue("Data Element");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue("Start Position/Sequence");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue("Length");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue("Format");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue("Decimal Length");
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue("Record Type");
		cell.setCellStyle(style);

		XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(spreadsheet);

		XSSFDataValidationConstraint formatConstraint = (XSSFDataValidationConstraint) dvHelper
				.createExplicitListConstraint(new String[] { "Date", "Text", "Amount", "Number" });
		CellRangeAddressList formatList = new CellRangeAddressList(-1, -1, 3, 3);
		XSSFDataValidation formatValidation = (XSSFDataValidation) dvHelper.createValidation(formatConstraint,
				formatList);
		formatValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(formatValidation);

		XSSFDataValidationConstraint amountFormatConstraint = (XSSFDataValidationConstraint) dvHelper
				.createExplicitListConstraint(new String[] { "NA", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
		CellRangeAddressList amountFormatList = new CellRangeAddressList(-1, -1, 4, 4);
		XSSFDataValidation amountFormatValidation = (XSSFDataValidation) dvHelper
				.createValidation(amountFormatConstraint, amountFormatList);
		amountFormatValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(amountFormatValidation);

		XSSFDataValidationConstraint recordTypeConstraint = (XSSFDataValidationConstraint) dvHelper
				.createExplicitListConstraint(new String[] { "Header Record 1", "Header Record 2", "Header Record 3",
						"Header Record 4", "Header Record 5", "Detail Record", "Trailer Record" });

//		XSSFDataValidationConstraint recordTypeConstraint = (XSSFDataValidationConstraint) dvHelper
//				.createExplicitListConstraint(new String[] { "Header Record 1", "Header Record 2", "Header Record 3",
//						"Header Record 4", "Header Record 5", "Detail Record", "Trailer Record", "Trailer Record 1",
//						"Trailer Record 2", "Trailer Record 3", "Trailer Record 4", "Trailer Record 5" });
		CellRangeAddressList recordTypeList = new CellRangeAddressList(-1, -1, 5, 5);
		XSSFDataValidation recordTypeValidation = (XSSFDataValidation) dvHelper.createValidation(recordTypeConstraint,
				recordTypeList);
		recordTypeValidation.setShowErrorBox(true);
		spreadsheet.addValidationData(recordTypeValidation);
		for (LayoutRecord layoutRecord : list) {
			row = spreadsheet.createRow(++rowCount);
			cell = row.createCell(row.getLastCellNum() + 1);
			cell.setCellValue(layoutRecord.getDataElement());
			cell.setCellStyle(dataStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(layoutRecord.getStartPosition());
			cell.setCellStyle(dataStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(layoutRecord.getLength());
			cell.setCellStyle(dataStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(layoutRecord.getFormat());
			cell.setCellStyle(dataStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(layoutRecord.getAmountFormat());
			cell.setCellStyle(dataStyle);

			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue(layoutRecord.getRecordType());
			cell.setCellStyle(dataStyle);
		}
		LoggerUtil.log(getClass(), Level.INFO, "updateExcelData()", "End of Update Excel Data: ");
	}

	@Override
	public String createLayoutdata(LayoutRequest request) {
		String ksdDBResponse = null;
		StringBuilder formatErrmessage = new StringBuilder("");
		String formatErrIndicator = "No";

		if (request.getFileName().equals("")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "FileName is empty. Please Enter File Name");
			LoggerUtil.log(getClass(), Level.ERROR, "createLayoutdata", "FileName is empty \n");
			return jobj.toString();
		}

		List<LayoutRecord> layoutRecordsList = getLayoutRecordsList(request);
		formatErrmessage = formatErrmessage.append(PLEASE_CHECK);
		int count = 0;
		for (int i = 0; i < layoutRecordsList.size(); i++) {

			String dataFormat = layoutRecordsList.get(i).getFormat();
			if ((dataFormat.contains("(")) && (!dataFormat.contains(")"))
					|| (!dataFormat.contains("(")) && (dataFormat.contains(")"))) {

				if (count == 0) {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(" ").append(layoutRecordsList.get(i).getDataElement());
				} else {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(", ").append(layoutRecordsList.get(i).getDataElement());
				}
				count++;
			}

		}
		formatErrmessage = formatErrmessage.append(FORMAT);
		if (formatErrIndicator.equalsIgnoreCase("Yes")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, formatErrmessage.toString());
			LoggerUtil.log(getClass(), Level.ERROR, "createLayout data method", formatErrmessage.toString());
			return jobj.toString();
		}

		ksdDBResponse = getKsd(request);

		if (ksdDBResponse == null || ksdDBResponse.length() == 0) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createLayoutdata method",
					"Unable to receive response from ksdDB service \n");
			return jobj.toString();
		}

		JsonArray ksdJson = (JsonArray) parser.parse(ksdDBResponse);
		return addLayout(layoutRecordsList, request, ksdJson);
	}

	@Override
	public String createLayoutdataNew(LayoutRequest request, String appName, String sessionToken, String action)
			throws JsonProcessingException {
		String ksdDBResponse = null;
		StringBuilder formatErrmessage = new StringBuilder("");
		String formatErrIndicator = "No";

		if (request.getFileName().equals("")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "FileName is empty. Please Enter File Name");
			LoggerUtil.log(getClass(), Level.ERROR, "createLayoutdata", "FileName is empty \n");
			return jobj.toString();
		}

		List<LayoutRecord> layoutRecordsList = getLayoutRecordsList(request);
		formatErrmessage = formatErrmessage.append(PLEASE_CHECK);
		int count = 0;
		for (int i = 0; i < layoutRecordsList.size(); i++) {

			String dataFormat = layoutRecordsList.get(i).getFormat();
			if ((dataFormat.contains("(")) && (!dataFormat.contains(")"))
					|| (!dataFormat.contains("(")) && (dataFormat.contains(")"))) {

				if (count == 0) {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(" ").append(layoutRecordsList.get(i).getDataElement());
				} else {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(", ").append(layoutRecordsList.get(i).getDataElement());
				}
				count++;
			}

		}
		formatErrmessage = formatErrmessage.append(FORMAT);
		if (formatErrIndicator.equalsIgnoreCase("Yes")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, formatErrmessage.toString());
			LoggerUtil.log(getClass(), Level.ERROR, "createLayout data method", formatErrmessage.toString());
			return jobj.toString();
		}

		ksdDBResponse = getKsd(request);

		if (ksdDBResponse == null || ksdDBResponse.length() == 0) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createLayoutdata method",
					"Unable to receive response from ksdDB service \n");
			return jobj.toString();
		}

		JsonArray ksdJson = (JsonArray) parser.parse(ksdDBResponse);
		return addLayoutNew(layoutRecordsList, request, ksdJson, appName, sessionToken, action);
	}

	@Override
	public String updateLayoutData(LayoutRequest request) {
		String ksdDBResponse = null;
		StringBuilder formatErrmessage = new StringBuilder("");
		String formatErrIndicator = "No";

		if (request.getFileName().equals("")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "FileName is empty. Please Enter File Name");
			LoggerUtil.log(getClass(), Level.ERROR, "updateLayoutData", "FileName is empty \n");
			return jobj.toString();
		}

		List<LayoutRecord> layoutRecordsList = getLayoutRecordsList(request);
		formatErrmessage = formatErrmessage.append(PLEASE_CHECK);
		int count = 0;
		for (int i = 0; i < layoutRecordsList.size(); i++) {

			String dataFormat = layoutRecordsList.get(i).getFormat();
			if ((dataFormat.contains("(")) && (!dataFormat.contains(")"))
					|| (!dataFormat.contains("(")) && (dataFormat.contains(")"))) {

				if (count == 0) {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(" ").append(layoutRecordsList.get(i).getDataElement());
				} else {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(", ").append(layoutRecordsList.get(i).getDataElement());
				}
				count++;
			}

		}
		formatErrmessage = formatErrmessage.append(FORMAT);
		if (formatErrIndicator.equalsIgnoreCase("Yes")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, formatErrmessage.toString());
			LoggerUtil.log(getClass(), Level.ERROR, "updateLayout data method", formatErrmessage.toString());
			return jobj.toString();
		}

		ksdDBResponse = getKsd(request);

		if (ksdDBResponse == null || ksdDBResponse.length() == 0) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "updateLayoutData method",
					"Unable to receive response from ksdDB service \n");
			return jobj.toString();
		}

		JsonArray ksdJson = (JsonArray) parser.parse(ksdDBResponse);
		return addLayout(layoutRecordsList, request, ksdJson);
	}

	public List<LayoutRecord> getLayoutRecordsList(LayoutRequest request) {

		List<LayoutRecord> headerRecords = request.getHeaderRecords();
		List<LayoutRecord> detailRecords = request.getDetailRecords();
		List<LayoutRecord> trailerRecords = request.getTrailerRecords();
		List<LayoutRecord> layoutRecordsList;

		if (request.getSubject() == null) {
			layoutRecordsList = Stream.of(headerRecords, detailRecords, trailerRecords).flatMap(x -> x.stream())
					.collect(Collectors.toList());

		} else {

			layoutRecordsList = Stream.of(detailRecords).flatMap(x -> x.stream()).collect(Collectors.toList());
		}
		return layoutRecordsList;
	}

	public String getKsd(LayoutRequest request) {

		String ksdDBResponse = null;
		String ksdFileDetailsId = request.getId();

		if ((ksdFileDetailsId == null) || (ksdFileDetailsId.length() < 1)) {
			ksdFileDetailsId = "0";
		}
		try {
			List<String> columnName = new ArrayList<>();
			columnName.add(HolmesAppConstants.ACTIVE_FLAG);
			columnName.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName.add(HolmesAppConstants.FILE_NAMES);

			List<String> columnCondition = new ArrayList<>();
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);

			List<String> columnValue = new ArrayList<>();
			columnValue.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
			columnValue.add(request.getProcessJobMappingId());
			columnValue.add(request.getFileName());

			List<com.wipro.fipc.entity.batch.KsdFileDetails> ksdDBResponse1 = objKsdFileDetailsController
					.findByMultiColumnCondition(columnName, columnCondition, columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			ksdDBResponse = objectMapper.writeValueAsString(ksdDBResponse1);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "createLayoutdata method",
					"Unable to createLayoutdata by DB Service due to {} \n" + exception.getMessage());
		}

		return ksdDBResponse;
	}

	public String addLayoutNew(List<LayoutRecord> layoutRecordsList, LayoutRequest request, JsonArray ksdJson,
			String appName, String sessionToken, String action) throws JsonProcessingException {

		String layoutResponse = null;

		String ksdConfigresponse = null;
		String httpMethod = null;
		String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
		String pjmId = request.getProcessJobMappingId();
		String fileName = request.getFileName();
		if (action.equals("Modify")) {
			String getLayoutDetailsDB = getKsdFileDetailsVaptNew(pjmId, fileName);
			JSONObject jsonObj;
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
			try {
				JSONArray jsonArr = new JSONArray(getLayoutDetailsDB);
				jsonObj = jsonArr.getJSONObject(0);
				request.setCreatedBy(jsonObj.getString("createdBy"));
				request.setCreatedDate(formatter.parse(jsonObj.getString("createdDate")));
			} catch (JSONException | ParseException e) {
				LoggerUtil.log(getClass(), Level.ERROR, "addLayoutNew", "request " + e.getMessage());

			}
		}

		KsdFileDetails ksdFileDetailsRequest = doConverter.getKsdFileDetailsNew(layoutRecordsList, request, adID,
				action);
		if (ksdJson.size() > 0) {
			for (int i = 0; i < ksdJson.size(); i++) {

				JsonObject jobj = (JsonObject) ksdJson.get(i);

				if (jobj.get(HolmesAppConstants.ID).getAsString().equalsIgnoreCase(request.getId())) {

					String id = jobj.get(HolmesAppConstants.ID).getAsString();

					ksdFileDetailsRequest.setId(Long.parseLong(id));

					httpMethod = "PUT";

				}

			}
		} else {

			httpMethod = "POST";
		}

		List<LayoutConfig> layoutCongfigDOList = doConverter.convertLayoutRequestToDONew(layoutRecordsList, request,
				adID, action);
		try {
			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> newLayout = new ArrayList<>();
			for (LayoutConfig req : layoutCongfigDOList) {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
						false);
				String convertedString = objectMapper.writeValueAsString(req);
				com.wipro.fipc.entity.layoutrule.LayoutConfig obj = new ObjectMapper().readValue(convertedString,
						com.wipro.fipc.entity.layoutrule.LayoutConfig.class);
				if (("Mainframe").equalsIgnoreCase(request.getFileType()) && StringUtils.isEmpty(obj.getSheetName())) {
					obj.setSheetName("Sheet1");
					obj.setSheetNameWoutSpace("Sheet1");
				}
				newLayout.add(obj);
			}

			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String convertedString = objectMapper.writeValueAsString(ksdFileDetailsRequest);
			com.wipro.fipc.entity.batch.KsdFileDetails objksdFileDetailsRequest = new ObjectMapper()
					.readValue(convertedString, com.wipro.fipc.entity.batch.KsdFileDetails.class);
			List<String> mainframeFileName = genericDaoKsdFileDetails.getFileNameByFileType(KSD_SCHEMA,
					KSD_FILE_DETAILS, pjmId, request.getFileType());
			if (CollectionUtils.isNotEmpty(mainframeFileName)) {
				genericDaoKsdFileDetails.deletePreviousFile(com.wipro.fipc.entity.layoutrule.LayoutConfig.class,
						LAYOUT_SCHEMA, LAYOUT_CONFIG, pjmId, mainframeFileName);
			}
			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> saveAllData = objLayoutConfigController
					.saveAllData(newLayout);
			layoutResponse = objectMapper.writeValueAsString(saveAllData);

			genericDaoKsdFileDetails.deletePreviousFile(com.wipro.fipc.entity.batch.KsdFileDetails.class, KSD_SCHEMA,
					KSD_FILE_DETAILS, pjmId, request.getFileType());
			if (("Mainframe").equalsIgnoreCase(request.getFileType())
					&& StringUtils.isEmpty(objksdFileDetailsRequest.getSheetName())) {
				objksdFileDetailsRequest.setSheetName("Sheet1");
				objksdFileDetailsRequest.setSheetNameWoutSpace("Sheet1");
			}
			com.wipro.fipc.entity.batch.KsdFileDetails create = objKsdFileDetailsController
					.create(objksdFileDetailsRequest);

			ksdConfigresponse = objectMapper.writeValueAsString(create);

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "addLayout()",
					"Unable to connect to dbLayoutPost or ksdConfig  DB Service due to {} \n" + exception.getMessage());

		}
		JsonObject jobj = new JsonObject();

		if ((layoutResponse == null) || !(layoutResponse.contains(HolmesAppConstants.FILE_NAME))
				|| (ksdConfigresponse == null) || !(ksdConfigresponse.contains(HolmesAppConstants.FILE_NAME))) {
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createLayoutdata() method",
					"Unable to receive response from db service \n");

		} else {

			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.SUCCESS);
			jobj.addProperty(HolmesAppConstants.MESSAGE,
					"Records submitted Successfully. Please ensure no PII/PHI data is added.");
			LoggerUtil.log(getClass(), Level.INFO, "createLayout data method", "Data updated in db  \n");
		}
		return jobj.toString();

	}

	public String addLayout(List<LayoutRecord> layoutRecordsList, LayoutRequest request, JsonArray ksdJson) {

		String layoutResponse = null;

		String ksdConfigresponse = null;
		String httpMethod = null;
		KsdFileDetails ksdFileDetailsRequest = doConverter.getKsdFileDetails(layoutRecordsList, request);

		if (ksdJson.size() > 0) {
			JsonObject jobj = (JsonObject) ksdJson.get(0);

			String id = jobj.get(HolmesAppConstants.ID).getAsString();
			ksdFileDetailsRequest.setId(Long.parseLong(id));
			httpMethod = "PUT";
		} else {

			httpMethod = "POST";
		}

		List<LayoutConfig> layoutCongfigDOList = doConverter.convertLayoutRequestToDO(layoutRecordsList, request);

		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> newLayout = new ArrayList<>();
			for (LayoutConfig req : layoutCongfigDOList) {
				String convertedString = objectMapper.writeValueAsString(req);
				com.wipro.fipc.entity.layoutrule.LayoutConfig obj = objectMapper.readValue(convertedString,
						com.wipro.fipc.entity.layoutrule.LayoutConfig.class);
				newLayout.add(obj);
			}

			String convertedString = objectMapper.writeValueAsString(ksdFileDetailsRequest);
			com.wipro.fipc.entity.batch.KsdFileDetails objksdFileDetailsRequest = objectMapper
					.readValue(convertedString, com.wipro.fipc.entity.batch.KsdFileDetails.class);

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> saveAllData = objLayoutConfigController
					.saveAllData(newLayout);
			layoutResponse = objectMapper.writeValueAsString(saveAllData);
			com.wipro.fipc.entity.batch.KsdFileDetails create = objKsdFileDetailsController
					.create(objksdFileDetailsRequest);
			ksdConfigresponse = objectMapper.writeValueAsString(saveAllData);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "addLayout()",
					"Unable to connect to dbLayoutPost or ksdConfig  DB Service due to {} \n" + exception.getMessage());

		}
		JsonObject jobj = new JsonObject();

		if ((layoutResponse == null) || !(layoutResponse.contains(HolmesAppConstants.FILE_NAME))
				|| (ksdConfigresponse == null) || !(ksdConfigresponse.contains(HolmesAppConstants.FILE_NAME))) {
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createLayoutdata() method",
					"Unable to receive response from db service \n");

		} else {

			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.SUCCESS);
			jobj.addProperty(HolmesAppConstants.MESSAGE,
					"Records submitted Successfully. Please ensure no PII/PHI data is added.");
			LoggerUtil.log(getClass(), Level.ERROR, "createLayout data method", "Data updated in db  \n");
		}
		return jobj.toString();

	}

	public String getLayoutDetails(String pjmId, String fileName) {
		LoggerUtil.log(getClass(), Level.INFO, "getLayoutDetails", "FileName SeriveImpl:" + fileName);
		String layoutResponse = "";

		String fname = "";
		fileName = fileName.replace("+", HolmesAppConstants.PLUS);
		fileName = fileName.replace("%", HolmesAppConstants.PRCNT);
		fileName = fileName.replace("&", HolmesAppConstants.AMPNT);
		fileName = fileName.replace("'", "''");
		fname = fileName;

		try {
			List<String> columnName = new ArrayList();
			columnName.add(HolmesAppConstants.ACTIVE_FLAG);
			columnName.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName.add(HolmesAppConstants.FILE_NAMES);

			List<String> columnCondition = new ArrayList();
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);

			List<String> columnValue = new ArrayList();
			columnValue.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
			columnValue.add(pjmId);
			columnValue.add(fname);

			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			List<com.wipro.fipc.entity.batch.KsdFileDetails> ksdDBResponse1 = objKsdFileDetailsController
					.newFindByMultiColumnCondition(columnName, columnCondition, columnValue);
			String ksdDBResponse = objectMapper.writeValueAsString(ksdDBResponse1);

			List<String> columnName1 = new ArrayList();
			columnName1.add(HolmesAppConstants.ACTIVE_FLAG);
			columnName1.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName1.add(HolmesAppConstants.FILE_NAMES);

			List<String> columnCondition1 = new ArrayList();
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);

			List<String> columnValue1 = new ArrayList();
			columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
			columnValue1.add(pjmId);
			columnValue1.add(fname);
			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> layoutDBResponse1 = objLayoutConfigController
					.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
			String layoutDBResponse = objectMapper.writeValueAsString(layoutDBResponse1);
			if (layoutDBResponse.contains(HolmesAppConstants.ID)) {

				layoutResponse = doConverter.convertDOToLayout(layoutDBResponse, ksdDBResponse);
			} else {

				layoutResponse = doConverter.convertDOToKsd(ksdDBResponse);

			}

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLayoutDetails",
					"Unable to connect to ksdfiledetails or layoutConfig DB Service due to {} \n"
							+ exception.getMessage());
		}
		return layoutResponse;
	}

	@Override
	public String getMfFieldNamesByFieldType(String processJobMappingId, String fieldType) {
		List<String> mffieldNameList = new ArrayList<>();
		String layoutResponse = null;

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> findByColumn = objLayoutConfigController
					.findByColumn(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, processJobMappingId);
			layoutResponse = objectMapper.writeValueAsString(findByColumn);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getMfFieldNamesByFieldType()",
					"Unable to connect to layout config DB Service due to {} \n" + e.getMessage());
		}

		JsonArray layoutJson = (JsonArray) parser.parse(layoutResponse);

		for (int i = 0; i < layoutJson.size(); ++i) {
			JsonObject layoutObj = layoutJson.get(i).getAsJsonObject();

			JsonElement fieldTypedb = layoutObj.get("fieldType");
			String fieldTypereq = fieldType.replace("\"", "");
			String feildTypeJson = fieldTypedb.toString().replace("\"", "");

			JsonElement mfFieldName = layoutObj.get(HolmesAppConstants.MFFIELD_NAME);

			if (fieldTypereq.equalsIgnoreCase(feildTypeJson)) {
				mffieldNameList.add(mfFieldName.toString());
			}
		}
		String jsonString = mffieldNameList.toString();

		JSONObject jsonObject = new JSONObject();
		JSONArray jsonArray = new JSONArray();
		try {
			jsonObject.put(HolmesAppConstants.MFFIELD_NAME, jsonString);
			jsonObject.put("fieldType", fieldType);

			jsonArray.put(jsonObject);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getMfFieldNamesByFieldType method",
					"Unable put jsonObject\n" + e.getMessage());
		}

		return jsonArray.toString().replace("\\", "");

	}

	public String deleteAllRecords(String processJobMappingId) {

		Boolean deleteRows = genericDao.deleteRowsByUID(com.wipro.fipc.entity.layoutrule.LayoutConfig.class,
				LAYOUT_SCHEMA, LAYOUT_CONFIG, HolmesAppConstants.PROCESS_JOB_MAPPING_ID, processJobMappingId);

		return deleteRows.toString();

	}

	private List<LayoutRecord> getLayoutDataBasedOnPjmId(String pjmId, String fileName) {
		LoggerUtil.log(getClass(), Level.INFO, "getLayoutDataBasedOnPjmId", "Calling getLayoutDetails: " + pjmId);
		List<LayoutRecord> layoutRecordsList = new ArrayList<>();
		String response = getLayoutDetails(pjmId, fileName);
		if (!response.isEmpty()) {
			JsonObject jsonLayoutConfig = (JsonObject) parser.parse(response);
			JsonArray jsonArray = jsonLayoutConfig.get("json").getAsJsonArray();
			for (JsonElement elm : jsonArray) {
				LayoutRecord layoutRecord = new LayoutRecord();
				JsonObject jsonObject = elm.getAsJsonObject();
				if (jsonObject.get(HolmesAppConstants.DATA_ELEMENT).getAsString().length() > 0) {
					layoutRecord = getLayoutRecord(jsonObject, layoutRecord);
					layoutRecordsList.add(layoutRecord);
				}
			}
		}
		return layoutRecordsList;
	}

	private LayoutRecord getLayoutRecord(JsonObject jsonObject, LayoutRecord layoutRecord) {
		layoutRecord.setDataElement(jsonObject.get(HolmesAppConstants.DATA_ELEMENT).isJsonNull() ? ""
				: jsonObject.get(HolmesAppConstants.DATA_ELEMENT).getAsString());
		layoutRecord.setStartPosition(jsonObject.get(HolmesAppConstants.START_POSITION).isJsonNull() ? ""
				: jsonObject.get(HolmesAppConstants.START_POSITION).getAsString());
		layoutRecord.setLength(jsonObject.get(HolmesAppConstants.LENGTH).isJsonNull() ? ""
				: jsonObject.get(HolmesAppConstants.LENGTH).getAsString());
		layoutRecord.setFormat(jsonObject.get(HolmesAppConstants.FORMAT).isJsonNull() ? ""
				: jsonObject.get(HolmesAppConstants.FORMAT).getAsString());
		layoutRecord.setAmountFormat(jsonObject.get(HolmesAppConstants.AMOUNT_FORMAT).isJsonNull() ? "NA"
				: jsonObject.get(HolmesAppConstants.AMOUNT_FORMAT).getAsString());
		layoutRecord.setRecordIdentifierVal(jsonObject.get(HolmesAppConstants.RECORD_IDENTIFIER_VAL).isJsonNull() ? ""
				: jsonObject.get(HolmesAppConstants.RECORD_IDENTIFIER_VAL).getAsString());
		layoutRecord.setRecordIdentifier(jsonObject.get(HolmesAppConstants.RECORD_IDENTIFIER).isJsonNull() ? ""
				: jsonObject.get(HolmesAppConstants.RECORD_IDENTIFIER).getAsString());

		layoutRecord.setRecordType(jsonObject.get(HolmesAppConstants.RECORDTYPE).isJsonNull() ? ""
				: jsonObject.get(HolmesAppConstants.RECORDTYPE).getAsString());

		return layoutRecord;
	}

	public String getFileNames(String pjmId) {
		Set<String> fileNames = new HashSet<>();
		JsonArray ksdInPutOutputArray = new JsonArray();
		String resp = getAllFileNames(pjmId);

		JsonArray ksdFileDetailsJson = (JsonArray) parser.parse(resp);

		for (int i = 0; i < ksdFileDetailsJson.size(); i++) {

			JsonElement object = ksdFileDetailsJson.get(i);
			JsonObject jsonObj = object.getAsJsonObject();
			String fileName = jsonObj.get(HolmesAppConstants.FILE_NAME).getAsString();
			if (!fileNames.contains(fileName)) {
				fileNames.add(fileName);
				ksdInPutOutputArray.add(ksdFileDetailsJson.get(i));
			}

		}

		return ksdInPutOutputArray.toString();

	}

	public String getNames(String pjmId, String fileType) {

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String ksdDBResponse = "";
		List<String> fileNameList = new ArrayList<>();
		try {
			List<String> columnName = new ArrayList();
			columnName.add(HolmesAppConstants.ACTIVE_FLAG);
			columnName.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName.add("file_type");

			List<String> columnCondition = new ArrayList();
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);

			List<String> columnValue = new ArrayList();
			columnValue.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
			columnValue.add(pjmId);
			columnValue.add(fileType);

			List<com.wipro.fipc.entity.batch.KsdFileDetails> ksdDBResponse1 = objKsdFileDetailsController
					.findByMultiColumnCondition(columnName, columnCondition, columnValue);
			ksdDBResponse = objectMapper.writeValueAsString(ksdDBResponse1);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getNames()",
					"Unable to connect to ksdfiledeatils DB Service to get names due to {} \n" + e.getMessage());
		}

		JsonArray ksdJson = (JsonArray) parser.parse(ksdDBResponse);

		for (int i = 0; i < ksdJson.size(); ++i) {
			JsonObject ksdObj = ksdJson.get(i).getAsJsonObject();
			JsonElement fileNameElement = ksdObj.get(HolmesAppConstants.FILE_NAME);
			fileNameList.add(fileNameElement.toString());

		}
		return fileNameList.toString();
	}

	public String getRecIds(String pjmId, String fileName) {

		String fname = "";

		if (fileName.contains("+")) {
			fileName = fileName.replace("+", HolmesAppConstants.PLUS);
		}
		if (fileName.contains("%")) {
			fileName = fileName.replace("%", HolmesAppConstants.PRCNT);
		}
		if (fileName.contains("&")) {
			fileName = fileName.replace("&", HolmesAppConstants.AMPNT);
		}
		if (fileName.contains("'")) {
			fileName = fileName.replace("'", "''");
		}

		try {
			fname = URLEncoder.encode(fileName, HolmesAppConstants.UTF8);

		} catch (UnsupportedEncodingException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getRecIds()", "Unsupported Encoding Format \n" + e.getMessage());

		}
		JsonArray layoutArray = new JsonArray();

		layoutArray = getLayoutArrayForLayoutConfig(fileName, pjmId, layoutArray, fname);

		layoutArray = getLayoutArrayForOutputFileDetails(fileName, pjmId, layoutArray, fname);

		return layoutArray.toString();
	}

	public JsonArray getLayoutArrayForLayoutConfig(String fileName, String pjmId, JsonArray layoutArray, String fname) {

		String layoutDbResp = "";
		String layoutConfigUrl = "";
		String tbaNoticeURL = "";
		String tbaNoticeResp = "";
		String table = "";
		detailRec = URLDecoder.decode(detailRec);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		if (fileName.equalsIgnoreCase(HolmesAppConstants.TBA_IDENT)) {

			try {
				List<String> columnName1 = new ArrayList();

				columnName1.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
				columnName1.add(HolmesAppConstants.RECORD_TYPE);
				columnName1.add(HolmesAppConstants.ACTIVE_FLAG);

				List<String> columnCondition1 = new ArrayList();
				columnCondition1.add(HolmesAppConstants.EQUAL);
				columnCondition1.add(HolmesAppConstants.EQUAL);
				columnCondition1.add(HolmesAppConstants.EQUAL);

				List<String> columnValue1 = new ArrayList();

				columnValue1.add(pjmId);
				columnValue1.add(detailRec);
				columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
				List<com.wipro.fipc.entity.layoutrule.LayoutConfig> layoutDBResponse1 = objLayoutConfigController
						.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
				layoutDbResp = objectMapper.writeValueAsString(layoutDBResponse1);

			} catch (Exception e) {
				e.printStackTrace();
			}

		}

		else if (fileName.equalsIgnoreCase(HolmesAppConstants.TBA)) {

			table = "tba";

			layoutArray.add("");

			try {

				List<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> findByColumn = objTbaNoticeInqConfigController
						.findByColumn(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
				tbaNoticeResp = objectMapper.writeValueAsString(findByColumn);
			} catch (Exception e) {
				LoggerUtil.log(getClass(), Level.ERROR, "getLayoutArrayForLayoutConfig method",
						"Unable to connect to getAllTbaInquiryConfig DB Service due to {} \n" + e.getMessage());
			}

		} else {

			try {
				List<String> columnName1 = new ArrayList();

				columnName1.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
				columnName1.add(HolmesAppConstants.FILE_NAMES);
				columnName1.add(HolmesAppConstants.RECORD_TYPE);
				columnName1.add(HolmesAppConstants.ACTIVE_FLAG);

				List<String> columnCondition1 = new ArrayList();
				columnCondition1.add(HolmesAppConstants.EQUAL);
				columnCondition1.add(HolmesAppConstants.EQUAL);
				columnCondition1.add(HolmesAppConstants.EQUAL);
				columnCondition1.add(HolmesAppConstants.EQUAL);

				List<String> columnValue1 = new ArrayList();

				columnValue1.add(pjmId);
				columnValue1.add(fname);
				columnValue1.add(detailRec);
				columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
				List<com.wipro.fipc.entity.layoutrule.LayoutConfig> layoutDBResponse1 = objLayoutConfigController
						.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
				layoutDbResp = objectMapper.writeValueAsString(layoutDBResponse1);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		if (layoutDbResp.length() < 5) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLayoutArrayForLayoutConfig method", "db Respons is db {} \n");
		} else {
			layoutArray = getLayoutArrayForLayoutResp(layoutArray, layoutDbResp);
		}

		if (tbaNoticeResp.length() < 5) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLayoutArrayForLayoutConfig() method", "db Respons is db {} \n");
		} else if (table.equalsIgnoreCase("tba")) {
			layoutArray = getLayoutArrayForTbaNotice(layoutArray, tbaNoticeResp);
		}

		return layoutArray;
	}

	public JsonArray getLayoutArrayForLayoutResp(JsonArray layoutArray, String layoutDbResp) {

		JsonArray layoutJson = (JsonArray) parser.parse(layoutDbResp);
		JsonObject jobj = (JsonObject) layoutJson.get(0);
		if ((jobj.has(HolmesAppConstants.RECORD_IDENTIFIER)) && (jobj.has(HolmesAppConstants.MFFIELD_NAME))) {
			for (JsonElement layoutElement : layoutJson) {

				JsonObject obj = layoutElement.getAsJsonObject();
				JsonElement recId = obj.get(HolmesAppConstants.RECORD_IDENTIFIER);

				if (!layoutArray.contains(recId)) {
					layoutArray.add(recId);
				}
			}
		} else if (jobj.has(HolmesAppConstants.IDENTIFIER)) {
			for (JsonElement tbaElement : layoutJson) {
				JsonObject obj = tbaElement.getAsJsonObject();
				JsonElement recId = obj.get(HolmesAppConstants.IDENTIFIER);

				if (!layoutArray.contains(recId)) {

					layoutArray.add(recId);
				}
			}
		}
		return layoutArray;
	}

	public JsonArray getLayoutArrayForTbaNotice(JsonArray layoutArray, String tbaNoticeResp) {

		JsonArray noticeJson = (JsonArray) parser.parse(tbaNoticeResp);
		JsonObject jobj = (JsonObject) noticeJson.get(0);

		if ((jobj.has(HolmesAppConstants.IDENTIFIER)) && (jobj.has("noticeName"))) {
			for (JsonElement layoutElement : noticeJson) {

				JsonObject obj = layoutElement.getAsJsonObject();
				JsonElement recId = obj.get(HolmesAppConstants.IDENTIFIER);

				if (!layoutArray.contains(recId)) {
					layoutArray.add(recId);
				}
			}
		}
		return layoutArray;
	}

	public JsonArray getLayoutArrayForOutputFileDetails(String fileName, String pjmId, JsonArray layoutArray,
			String fname) {

		String outPutFileDetailsResponse = "";
		String outPutFileDetailsurl = "";
		String oPReport = "No";
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		if (fileName.equalsIgnoreCase(HolmesAppConstants.TBA_IDENT)) {

			oPReport = "Yes";
			try {
				List<String> columnName2 = new ArrayList();
				columnName2.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
				columnName2.add(HolmesAppConstants.ACTIVE_FLAG);

				List<String> columnCondition2 = new ArrayList();
				columnCondition2.add(HolmesAppConstants.EQUAL);
				columnCondition2.add(HolmesAppConstants.EQUAL);
				List<String> columnValue1 = new ArrayList();
				columnValue1.add(pjmId);
				columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
				List<OutputReport> findByMultiColumnCondition = objOutputReportController
						.findByMultiColumnCondition(columnName2, columnCondition2, columnValue1);
				outPutFileDetailsResponse = objectMapper.writeValueAsString(findByMultiColumnCondition);
			} catch (Exception e) {
				e.printStackTrace();
			}

		} else if (!(fileName.equalsIgnoreCase(HolmesAppConstants.TBA))
				&& !(fileName.equalsIgnoreCase(HolmesAppConstants.TBA_IDENT))) {

			oPReport = "Yes";

			try {
				List<String> columnName2 = new ArrayList();
				columnName2.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
				columnName2.add(HolmesAppConstants.FILE_NAMES);
				columnName2.add(HolmesAppConstants.ACTIVE_FLAG);
				List<String> columnCondition2 = new ArrayList();
				columnCondition2.add(HolmesAppConstants.EQUAL);
				columnCondition2.add(HolmesAppConstants.EQUAL);
				columnCondition2.add(HolmesAppConstants.EQUAL);

				List<String> columnValue1 = new ArrayList();
				columnValue1.add(pjmId);
				columnValue1.add(fname);
				columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
				List<OutputReport> findByMultiColumnCondition = objOutputReportController
						.findByMultiColumnCondition(columnName2, columnCondition2, columnValue1);
				outPutFileDetailsResponse = objectMapper.writeValueAsString(findByMultiColumnCondition);
			} catch (Exception e) {
				e.printStackTrace();
			}

		}
		if (oPReport.equals("Yes")) {
			try {

			} catch (Exception e) {
				LoggerUtil.log(getClass(), Level.ERROR, "getLayoutArrayForOutputFileDetails mthd",
						"Unable to connect to outPutFileDetails DB Service due to {} \n" + e.getMessage());
			}
			JsonArray outPutFileDetailsJsonArray = (JsonArray) parser.parse(outPutFileDetailsResponse);
			for (JsonElement outputReportDetails : outPutFileDetailsJsonArray) {

				JsonObject obj = outputReportDetails.getAsJsonObject();
				JsonElement recordIdentifierData = obj.get(HolmesAppConstants.RECORD_IDENTIFIER);
				if (!layoutArray.contains(recordIdentifierData)) {
					layoutArray.add(recordIdentifierData);
				}
			}
		}
		return layoutArray;

	}

	public String getAllRecordIdentifier() {

		String recordIdentifierList = "";

		try {
			List<LayoutIdentifier> list1 = objLayoutIdentifierController.list1();
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			recordIdentifierList = objectMapper.writeValueAsString(list1);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllRecordIdentifier()",
					"Unable to connect to recordIdentifier DB Service due to {} \n" + e.getMessage());
		}

		return recordIdentifierList;
	}

	@Override
	public String deleteFiles(String pjmId, String fileName, String adid) {

		String dBResponse = "";
		String message = "";
		String fname = fileName;

		fname = fname.replace(" ", "%20");
		fname = fname.replace("'", "%27");
		fname = fname.replace("#", "%23");
		fname = fname.replace("+", "%2B");
		fname = fname.replace("@", "%40");
		fname = fname.replace("$", "%24");
		fname = fname.replace("^", "%5E");
		fname = fname.replace("&", "%26");

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {

			Boolean commonsoftdeleteFileLayoutInputReport = objCommonController
					.commonsoftdeleteFileLayoutInputReport(Long.parseLong(pjmId), fname, adid);
			dBResponse = objectMapper.writeValueAsString(commonsoftdeleteFileLayoutInputReport);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteFiles()",
					"Unable to connect to deleteFile DB Service due to {} \n" + exception.getMessage());
		}
		JsonObject layoutJson = new JsonObject();

		if (dBResponse.contains("false")) {
			message = fileName + " is not deleted. Please contact system administrator.";
			layoutJson.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			layoutJson.addProperty(HolmesAppConstants.MESSAGE, message);
			LoggerUtil.log(getClass(), Level.ERROR, "deleteFiles",
					"No Response received from db File " + fileName + "not deleted\n");
		} else if (dBResponse.contains("true")) {
			message = fileName + " is deleted.";
			layoutJson.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.SUCCESS);
			layoutJson.addProperty(HolmesAppConstants.MESSAGE, message);
			LoggerUtil.log(getClass(), Level.ERROR, "deleteFiles()",
					"Success message received from db" + fileName + "deleted\n");
		} else {
			message = fileName + " is not deleted. Please contact system administrator";
			layoutJson.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			layoutJson.addProperty(HolmesAppConstants.MESSAGE, message);
			LoggerUtil.log(getClass(), Level.ERROR, "deleteFiles method",
					"Failure message received from db File " + fileName + "not deleted\n");
		}

		return layoutJson.toString();
	}

	@Override
	public String deleteFilesNew(String pjmId, String fileName, String appName, String sessionToken) {

		String dBResponse = "";
		String message = "";
		String fname = fileName;

		fname = fname.replace("'", "%27");
		fname = fname.replace("#", "%23");
		fname = fname.replace("+", "%2B");
		fname = fname.replace("@", "%40");
		fname = fname.replace("$", "%24");
		fname = fname.replace("^", "%5E");
		fname = fname.replace("&", "%26");

		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(getClass(), Level.INFO, "deleteFilesNew() method", "This is FileNme: " + fileName
				+ " :This PjmId: " + pjmId + " :This ADID: " + adid + " :For Delete Action: ");

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			Boolean commonsoftdeleteFileLayoutInputReport = objCommonController
					.commonsoftdeleteFileLayoutInputReport(Long.parseLong(pjmId), fname, adid);
			dBResponse = objectMapper.writeValueAsString(commonsoftdeleteFileLayoutInputReport);

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteFiles()",
					"Unable to connect to deleteFile DB Service due to {} \n" + exception.getMessage());
		}
		JsonObject layoutJson = new JsonObject();

		if (dBResponse.contains("false")) {
			message = fileName + " is not deleted. Please contact system administrator.";
			layoutJson.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			layoutJson.addProperty(HolmesAppConstants.MESSAGE, message);
			LoggerUtil.log(getClass(), Level.ERROR, "deleteFiles",
					"No Response received from db File " + fileName + "not deleted\n");
		} else if (dBResponse.contains("true")) {
			message = fileName + " is deleted.";
			layoutJson.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.SUCCESS);
			layoutJson.addProperty(HolmesAppConstants.MESSAGE, message);
			LoggerUtil.log(getClass(), Level.INFO, "deleteFiles()",
					"Success message received from db" + fileName + "deleted\n");
		} else {
			message = fileName + " is not deleted. Please contact system administrator";
			layoutJson.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			layoutJson.addProperty(HolmesAppConstants.MESSAGE, message);
			LoggerUtil.log(getClass(), Level.ERROR, "deleteFiles method",
					"Failure message received from db File " + fileName + "not deleted\n");
		}

		return layoutJson.toString();
	}

	@Override
	public String getLayoutDetails(String pjmId) {

		String recordIdentifierdata = getAllRecordIdentifier();
		String fileNames = getFileNames(pjmId);

		JsonArray recordIdentifierJsonArray = (JsonArray) parser.parse(recordIdentifierdata);
		JsonArray ksdFileJsonArray = (JsonArray) parser.parse(fileNames);

		JsonObject obj = new JsonObject();
		JsonArray recordIdentifierList = new JsonArray();
		JsonArray fileNameList = new JsonArray();

		for (JsonElement ksdElement : ksdFileJsonArray) {
			JsonObject ksdObj = ksdElement.getAsJsonObject();
			fileNameList.add(ksdObj.get(HolmesAppConstants.FILE_NAME));
		}
		for (JsonElement layoutElement : recordIdentifierJsonArray) {
			JsonObject layoutObj = layoutElement.getAsJsonObject();
			recordIdentifierList.add(layoutObj.get(HolmesAppConstants.RECORD_IDENTIFIER));
		}

		obj.add(HolmesAppConstants.FILE_NAME, fileNameList);
		obj.add(HolmesAppConstants.RECORD_IDENTIFIER, recordIdentifierList);

		return obj.toString();
	}

	// testing purpose for Input Report Multiple Sheets

	@Override
	public String createInputReport(ReportSheet request) {

		String formatErrmessage = "";
		String formatErrIndicator = "No";

		formatErrmessage = PLEASE_CHECK;
		int count = 0;

		for (int i = 0; i < request.getKsdFileJson().size(); i++) {
			List<LayoutRecord> layoutRecordsList = request.getKsdFileJson().get(i).getDetailRecords();
			JsonObject obj = getFormatErrMessage(layoutRecordsList, count, formatErrmessage, formatErrIndicator);
			formatErrIndicator = obj.get(HolmesAppConstants.FORMAT_ERR_INDICATOR).getAsString();
			formatErrmessage = obj.get(HolmesAppConstants.FORMAT_ERR_MESSAGE).getAsString();

			if (formatErrIndicator.equalsIgnoreCase("Yes")) {
				JsonObject jobj = new JsonObject();
				jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
				jobj.addProperty(HolmesAppConstants.MESSAGE, formatErrmessage);
				LoggerUtil.log(getClass(), Level.ERROR, "createInputReport() method", formatErrmessage);
				return jobj.toString();
			}
		}

		return addInputReport(request.getKsdFileJson().size(), request);

	}

	@Override
	public String createInputReportNew(ReportSheet request, String appName, String sessionToken, String action)
			throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "createInputReportNew",
				"FileLayoutServiceImpl  RequestData:" + request);
		String formatErrmessage = "";
		String formatErrIndicator = "No";

		formatErrmessage = PLEASE_CHECK;
		int count = 0;

		for (int i = 0; i < request.getKsdFileJson().size(); i++) {
			List<LayoutRecord> layoutRecordsList = request.getKsdFileJson().get(i).getDetailRecords();
			LoggerUtil.log(this.getClass(), Level.INFO, "createInputReportNew",
					"FileLayoutServiceImpl  layoutRecordsListDetailsRecords:" + layoutRecordsList);
			JsonObject obj = getFormatErrMessage(layoutRecordsList, count, formatErrmessage, formatErrIndicator);
			formatErrIndicator = obj.get(HolmesAppConstants.FORMAT_ERR_INDICATOR).getAsString();
			formatErrmessage = obj.get(HolmesAppConstants.FORMAT_ERR_MESSAGE).getAsString();

			if (formatErrIndicator.equalsIgnoreCase("Yes")) {
				JsonObject jobj = new JsonObject();
				jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
				jobj.addProperty(HolmesAppConstants.MESSAGE, formatErrmessage);
				LoggerUtil.log(getClass(), Level.ERROR, "createInputReport() method", formatErrmessage);
				return jobj.toString();
			}
		}

		return addInputReportNew(request.getKsdFileJson().size(), request, appName, sessionToken, action);

	}

	@Override
	public String updateInputReport(ReportSheet request) {

		String formatErrmessage = "";
		String formatErrIndicator = "No";

		formatErrmessage = PLEASE_CHECK;
		int count = 0;

		for (int i = 0; i < request.getKsdFileJson().size(); i++) {
			List<LayoutRecord> layoutRecordsList = request.getKsdFileJson().get(i).getDetailRecords();
			JsonObject obj = getFormatErrMessage(layoutRecordsList, count, formatErrmessage, formatErrIndicator);
			formatErrIndicator = obj.get(HolmesAppConstants.FORMAT_ERR_INDICATOR).getAsString();
			formatErrmessage = obj.get(HolmesAppConstants.FORMAT_ERR_MESSAGE).getAsString();

			if (formatErrIndicator.equalsIgnoreCase("Yes")) {
				JsonObject jobj = new JsonObject();
				jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
				jobj.addProperty(HolmesAppConstants.MESSAGE, formatErrmessage);
				LoggerUtil.log(getClass(), Level.ERROR, "updateInputReport() method", formatErrmessage);
				return jobj.toString();
			}
		}

		return addInputReport(request.getKsdFileJson().size(), request);

	}

	public JsonObject getFormatErrMessage(List<LayoutRecord> layoutRecordsList, int count, String formatErrmsg,
			String formatErrIndicator) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createInputReportNew",
				"FileLayoutServiceImpl  getFormatErrMessage method layoutRecordsList Size :"
						+ layoutRecordsList.size());
		StringBuilder formatErrmessage = new StringBuilder(formatErrmsg);
		JsonObject obj = new JsonObject();
		for (int j = 0; j < layoutRecordsList.size(); j++) {
			String dataFormat = layoutRecordsList.get(j).getFormat();
			if ((dataFormat.contains("(")) && (!dataFormat.contains(")"))
					|| (!dataFormat.contains("(")) && (dataFormat.contains(")"))) {
				if (count == 0) {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(" ").append(layoutRecordsList.get(j).getDataElement());
				} else {
					formatErrmessage = formatErrmessage.append(", ").append(layoutRecordsList.get(j).getDataElement());
				}
				count++;
			}
		}
		formatErrmessage = formatErrmessage.append(FORMAT);
		obj.addProperty("formatErrIndicator", formatErrIndicator);
		obj.addProperty("formatErrmessage", formatErrmessage.toString());
		return obj;
	}

	public String addInputReport(int ksdJsonSize, ReportSheet request) {

		List<List<LayoutRecord>> layoutList1 = new ArrayList<>();

		for (int i = 0; i < ksdJsonSize; i++) {
			List<LayoutRecord> detailRecord = request.getKsdFileJson().get(i).getDetailRecords();
			layoutList1.add(detailRecord);

		}
		List<LayoutConfig> layoutCongfigDOList = doConverter.layoutDetailsForMultiSheet(layoutList1, request);

		String layoutResponse = null;
		String ksdConfigresponse = "";
		String recResp = "";
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> newLayout = new ArrayList();
			for (LayoutConfig req : layoutCongfigDOList) {

				String convertedString = objectMapper.writeValueAsString(req);
				com.wipro.fipc.entity.layoutrule.LayoutConfig obj = new ObjectMapper().readValue(convertedString,
						com.wipro.fipc.entity.layoutrule.LayoutConfig.class);
				newLayout.add(obj);
			}

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> saveAllData = objLayoutConfigController
					.saveAllData(newLayout);
			layoutResponse = objectMapper.writeValueAsString(saveAllData);

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "addInputReport()", " " + exception.getMessage());
			recResp = "Failed";
		}

		List<KsdFileDetails> ksdFileDetailsRequest = doConverter.ksdFileDetailsforMultiSheet(request);

		try {
			List<com.wipro.fipc.entity.batch.KsdFileDetails> newLayout1 = new ArrayList();
			for (KsdFileDetails req : ksdFileDetailsRequest) {

				String convertedString = objectMapper.writeValueAsString(req);
				com.wipro.fipc.entity.batch.KsdFileDetails obj = new ObjectMapper().readValue(convertedString,
						com.wipro.fipc.entity.batch.KsdFileDetails.class);
				newLayout1.add(obj);
			}

			boolean saveAllEntities = objKsdFileDetailsController.saveAllEntities(newLayout1);

			ksdConfigresponse = objectMapper.writeValueAsString(saveAllEntities);
			HttpHeaders headers = new HttpHeaders();
			headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.CONTENT_TYPE_VALUE);
			URI ksdUri = new URI(ksdfiledeatilsSaveAllEntities);
			HttpEntity<List<KsdFileDetails>> request2 = new HttpEntity<>(ksdFileDetailsRequest, headers);

		}

		catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "addInputReport",
					"Unable to connect to ksdfiledeatilsSaveAllEntities DB Service due to {} \n"
							+ exception.getMessage());
			recResp = "Failed";
		}

		JsonObject jobj = new JsonObject();
		if (recResp.equalsIgnoreCase(HolmesAppConstants.FAILED)) {
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "All " + FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createInputReport method",
					"Unable to receive response from db service!! \n");

		} else if ((layoutResponse == null) || !(layoutResponse.contains(HolmesAppConstants.FILE_NAME))
				|| (ksdConfigresponse == null) || !(ksdConfigresponse.equalsIgnoreCase("true"))) {
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createInputReport",
					"Unable to receive response from db service \n");

		} else {

			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.SUCCESS);
			jobj.addProperty(HolmesAppConstants.MESSAGE,
					"Records submitted Successfully. Please ensure no PII/PHI data is added.");
			LoggerUtil.log(getClass(), Level.ERROR, "createInputReport()", "Data updated in db  \n");
		}
		return jobj.toString();
	}

	public String addInputReportNew(int ksdJsonSize, ReportSheet request, String appName, String sessionToken,
			String action) throws JsonProcessingException {

		String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
		List<List<LayoutRecord>> layoutList1 = new ArrayList<>();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		for (int i = 0; i < ksdJsonSize; i++) {
			List<LayoutRecord> detailRecord = request.getKsdFileJson().get(i).getDetailRecords();
			layoutList1.add(detailRecord);

		}
		String pjmId = request.getKsdFileJson().get(0).getProcessJobMappingId();
		String fileName = request.getKsdFileJson().get(0).getFileName();
		String fileType = request.getKsdFileJson().get(0).getFileType();
		boolean verifyEmailOnly = request.getKsdFileJson().get(0).isVerifyEmailOnly();

		String getLayoutDetailsDB = "";
		if (action.equals("Modify")) {
			getLayoutDetailsDB = getKsdFileDetailsVaptNew(pjmId, fileName);
		}
		List<LayoutConfig> layoutCongfigDOList = doConverter.layoutDetailsForMultiSheetNew(layoutList1, request, adID,
				action, getLayoutDetailsDB);

		String layoutResponse = null;
		String ksdConfigresponse = "";
		String recResp = "";
		try {
			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> newLayout = new ArrayList<>();
			int startPosition = 0;
			for (LayoutConfig req : layoutCongfigDOList) {

				String convertedString = objectMapper.writeValueAsString(req);
				com.wipro.fipc.entity.layoutrule.LayoutConfig obj = new ObjectMapper().readValue(convertedString,
						com.wipro.fipc.entity.layoutrule.LayoutConfig.class);
				String extension = FilenameUtils.getExtension(obj.getFileName());
				if ("csv".equalsIgnoreCase(extension) && StringUtils.isEmpty(obj.getSheetName())) {
					obj.setSheetName("Sheet1");
					obj.setSheetNameWoutSpace("Sheet1");
				}
				if ("Report".equalsIgnoreCase(fileType) && "csv".equalsIgnoreCase(extension)) {
					obj.setStartPos(String.valueOf(++startPosition));
				}
				newLayout.add(obj);
			}

			List<String> reportFileName = genericDaoKsdFileDetails.getFileNameByFileType(KSD_SCHEMA, KSD_FILE_DETAILS,
					pjmId, fileType);
			if (CollectionUtils.isNotEmpty(reportFileName)) {
				// Remove check of softDelete File
				// genericDaoKsdFileDetails.deletePreviousFile(com.wipro.fipc.entity.layoutrule.LayoutConfig.class,
				// LAYOUT_SCHEMA, LAYOUT_CONFIG, pjmId, reportFileName);
			}

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> saveAllData = objLayoutConfigController
					.saveAllData(newLayout);
			if (CollectionUtils.isNotEmpty(saveAllData))
				layoutResponse = objectMapper.writeValueAsString(saveAllData);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "addInputReport()",
					"Unable to connect to dbLayoutPost DB Service due to {} \n" + exception.getMessage());
			recResp = "Failed";
		}

		List<KsdFileDetails> ksdFileDetailsRequest = doConverter.ksdFileDetailsforMultiSheetNew(request, adID, action,
				getLayoutDetailsDB);

		try {

			List<com.wipro.fipc.entity.batch.KsdFileDetails> newLayout1 = new ArrayList();
			for (KsdFileDetails req : ksdFileDetailsRequest) {

				String convertedString = objectMapper.writeValueAsString(req);
				com.wipro.fipc.entity.batch.KsdFileDetails obj = new ObjectMapper().readValue(convertedString,
						com.wipro.fipc.entity.batch.KsdFileDetails.class);
				String extension = FilenameUtils.getExtension(obj.getFileName());
				if ("csv".equalsIgnoreCase(extension) && StringUtils.isEmpty(obj.getSheetName())) {
					obj.setSheetName("Sheet1");
					obj.setSheetNameWoutSpace("Sheet1");
					obj.setFileFormatType("Delimited");
				}
				newLayout1.add(obj);
			}
			// Remove check of softDelete File
			// genericDaoKsdFileDetails.deletePreviousFile(com.wipro.fipc.entity.batch.KsdFileDetails.class,
			// KSD_SCHEMA, KSD_FILE_DETAILS, pjmId, fileType);
			boolean isPrimaryKey = request.getKsdFileJson().stream()
					.anyMatch(ksdFile -> ksdFile.getPrimaryFile().equalsIgnoreCase("Y"));
			if (isPrimaryKey && ksdFileDetailsDao.getPrimaryFileCount(Long.parseLong(pjmId)) > 0)
				ksdFileDetailsDao.updatePrimaryFile("N", Long.parseLong(pjmId));
			boolean saveAllEntities = objKsdFileDetailsController.saveAllEntities(newLayout1);

			ksdConfigresponse = objectMapper.writeValueAsString(saveAllEntities);

		}

		catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "addInputReport",
					"Unable to connect to ksdfiledeatilsSaveAllEntities DB Service due to {} \n"
							+ exception.getMessage());
			recResp = "Failed";
		}

		JsonObject jobj = new JsonObject();
		if (recResp.equalsIgnoreCase(HolmesAppConstants.FAILED)) {
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "All " + FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createInputReport method",
					"Unable to receive response from db service!! \n");

		} else if ((!verifyEmailOnly
				&& ((layoutResponse == null) || !(layoutResponse.contains(HolmesAppConstants.FILE_NAME))))
				|| (ksdConfigresponse == null) || !(ksdConfigresponse.equalsIgnoreCase("true"))) {
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "createInputReport",
					"Unable to receive response from db service \n");
		} else {

			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.SUCCESS);
			jobj.addProperty(HolmesAppConstants.MESSAGE,
					"Records submitted Successfully. Please ensure no PII/PHI data is added.");
			LoggerUtil.log(getClass(), Level.ERROR, "createInputReport()", "Data updated in db  \n");
		}
		return jobj.toString();
	}

	// MultiInput Report
	@Override
	public String getAllMultiReport(String pjmId, String fileName) {

		String layoutResponse = "";
		String fname = "";
		fileName = fileName.replace("+", HolmesAppConstants.PLUS);
		fileName = fileName.replace("%", HolmesAppConstants.PRCNT);
		fileName = fileName.replace("&", HolmesAppConstants.AMPNT);
		fileName = fileName.replace("'", "''");

		fname = fileName;
		detailRec = URLDecoder.decode(detailRec);

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {

			List<String> columnName = new ArrayList<>();
			columnName.add(HolmesAppConstants.ACTIVE_FLAG);
			columnName.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName.add(HolmesAppConstants.FILE_NAMES);

			List<String> columnCondition = new ArrayList<>();
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);
			columnCondition.add(HolmesAppConstants.EQUAL);

			List<String> columnValue = new ArrayList<>();
			columnValue.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
			columnValue.add(pjmId);
			columnValue.add(fname);

			List<com.wipro.fipc.entity.batch.KsdFileDetails> ksdDBResponse1 = objKsdFileDetailsController
					.newFindByMultiColumnCondition(columnName, columnCondition, columnValue);
			String ksdDBResponse = objectMapper.writeValueAsString(ksdDBResponse1);

			List<String> columnName1 = new ArrayList<>();

			columnName1.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName1.add(HolmesAppConstants.FILE_NAMES);
			columnName1.add(HolmesAppConstants.RECORD_TYPE);
			columnName1.add(HolmesAppConstants.ACTIVE_FLAG);

			List<String> columnCondition1 = new ArrayList<>();
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);

			List<String> columnValue1 = new ArrayList<>();

			columnValue1.add(pjmId);
			columnValue1.add(fname);
			columnValue1.add(detailRec);
			columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> layoutDBResponse1 = objLayoutConfigController
					.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
			String layoutDBResponse = objectMapper.writeValueAsString(layoutDBResponse1);
			if (layoutDBResponse.contains("sheetName") && ksdDBResponse.contains("sheetName")) {

				layoutResponse = doConverter.convertDOToLayoutMultiSheet(layoutDBResponse, ksdDBResponse, pjmId);
			} else {

				layoutResponse = doConverter.convertDOToKsdMultiSheet(layoutDBResponse, ksdDBResponse, pjmId);

			}

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllMultiReport ()",
					"Unable to connect to layoutConfig or ksdfiledeatils DB Service due to {} \n"
							+ exception.getMessage());
		}
		return layoutResponse;
	}

	@Override
	public String getAllFileNames(String pjmId) {
		String ksdFileDetailsResponse = "";
		String ksdOutPutFileDetailsResponse = "";

		try {
			List<com.wipro.fipc.entity.batch.KsdFileDetails> newFindByColumn = objKsdFileDetailsController
					.newFindByColumn(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
			newFindByColumn.forEach(ksd -> {
				String newFileName = "INP:";
				ksd.setFileName(newFileName.concat(ksd.getFileName()));
			});
			Collections.sort(newFindByColumn,
					Comparator.comparingLong(com.wipro.fipc.entity.batch.KsdFileDetails::getId).reversed());
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			ksdFileDetailsResponse = objectMapper.writeValueAsString(newFindByColumn);

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllFileNames()",
					"Unable to connect to ksdFileDetails get DB Service due to {} \n" + exception.getMessage());
		}

		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);

			List<String> columnName2 = new ArrayList();
			columnName2.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName2.add(HolmesAppConstants.ACTIVE_FLAG);

			List<String> columnCondition2 = new ArrayList();
			columnCondition2.add(HolmesAppConstants.EQUAL);
			columnCondition2.add(HolmesAppConstants.EQUAL);

			List<String> columnValue1 = new ArrayList();
			columnValue1.add(pjmId);
			columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);

			List<KsdOutPutFileDetails> findByMultiColumnCondition = objKsdOutPutFileDetailsController
					.findByMultiColumnCondition(columnName2, columnCondition2, columnValue1);
			findByMultiColumnCondition.forEach(ksd -> {
				String newFileName = "OUT:";
				ksd.setFileName(newFileName.concat(ksd.getFileName()));
			});
			Collections.sort(findByMultiColumnCondition,
					Comparator.comparingLong(KsdOutPutFileDetails::getId).reversed());
			ksdOutPutFileDetailsResponse = objectMapper.writeValueAsString(findByMultiColumnCondition);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllFileNames method",
					"Unable to connect to getAllFileNames DB Service due to {} \n" + e.getMessage());
		}

		JsonArray ksdFileDetailsJson = (JsonArray) parser.parse(ksdFileDetailsResponse);
		JsonArray ksdOutPutFileDetailsJson = (JsonArray) parser.parse(ksdOutPutFileDetailsResponse);

		JsonArray ksdDupObjArray = new JsonArray();

		for (JsonElement ksdElement : ksdFileDetailsJson) {

			JsonObject obj = new JsonObject();
			JsonObject obj1 = ksdElement.getAsJsonObject();

			if (!(obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME).isJsonNull())
					&& obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME).getAsString().length() > 4) {
				obj.add(HolmesAppConstants.FILE_NAME, obj1.get(HolmesAppConstants.PREV_REPORT_FILE_NAME));
				obj.add("fileNameWoutSpace", obj1.get("prevReportFileNameWs"));
				obj.addProperty("fileType", "PrevReport");
				obj.add(HolmesAppConstants.ID, obj1.get(HolmesAppConstants.ID));

				ksdDupObjArray.add(obj);
			}
		}

		for (JsonElement layoutElement : ksdOutPutFileDetailsJson) {
			JsonObject obj = layoutElement.getAsJsonObject();
			JsonObject obj1 = new JsonObject();
			JsonElement generateReport = obj.get("generateReport");
			JsonElement referReport = obj.get("referReport");

			if ((generateReport.toString().contains("Generated")) || (generateReport.toString().contains("Modified"))
					|| (referReport.toString().contains("Yes"))) {
				ksdFileDetailsJson.add(obj);
				// Add PrvReport Object
				if (!(obj.get(HolmesAppConstants.PREV_REPORT_FILE_NAME).isJsonNull())
						&& obj.get(HolmesAppConstants.PREV_REPORT_FILE_NAME).getAsString().length() > 4) {
					obj1.add(HolmesAppConstants.ID, obj.get(HolmesAppConstants.ID));
					obj1.add(HolmesAppConstants.FILE_NAME, obj.get(HolmesAppConstants.PREV_REPORT_FILE_NAME));
					obj1.add("fileNameWoutSpace", obj.get("prevReportFileNameWs"));
					obj1.addProperty("fileType", "PrevReport");

					ksdDupObjArray.add(obj1);
				}
			}

		}
		ksdFileDetailsJson.addAll(ksdDupObjArray);
		return ksdFileDetailsJson.toString();
	}

	@Override
	public String getDataBaseConfigMasterData(String pjmId) {
		String dataBaseConfigMasterDataUrl = "";
		String dataBaseConfigList = "";

		try {

			List<DatabaseConfig> databaseConfig = objDatabaseConfigController.getDatabaseConfig(pjmId);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			dataBaseConfigList = objectMapper.writeValueAsString(databaseConfig);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getDataBaseConfigMasterData()",
					"Unable to connect to DB Service due to {} \n" + exception.getMessage());
		}
		return dataBaseConfigList;
	}

	@Override
	public String deleteFilesData(List<com.wipro.fipc.entity.batch.KsdFileDetails> ksdFileDetailsList) {
		HttpHeaders headers = new HttpHeaders();
		headers.set("Content-Type", "application/json");
		headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));

		String returnValue = "";
		try {
			List<CustomResponse> commonMultipledelete = objCommonController.commonMultipledelete(ksdFileDetailsList);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			returnValue = objectMapper.writeValueAsString(commonMultipledelete);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return returnValue;
	}

	@Override
	public String getLatestKsdFileDetailsByPjmId(String pjmId) {
		String fname = "";
		String fileName = "";
		String ksdFileDetailsResponse = "";
		String layoutResponse = "";
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			List<com.wipro.fipc.entity.batch.KsdFileDetails> singleKsdFileDetails = objKsdFileDetailsController
					.getSingleKsdFileDetails(Long.parseLong(pjmId));
			ksdFileDetailsResponse = objectMapper.writeValueAsString(singleKsdFileDetails);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLatestKsdFileDetailsReport ()",
					"Unable to connect to layoutConfig or ksdfiledeatils DB Service due to {} \n"
							+ exception.getMessage());
		}

		JsonArray ksdResponseJsonArray = (JsonArray) parser.parse(ksdFileDetailsResponse);
		for (JsonElement ksdResObject : ksdResponseJsonArray) {

			JsonObject object = ksdResObject.getAsJsonObject();
			fileName = object.get("fileName").getAsString();

		}

		fileName = fileName.replace("+", HolmesAppConstants.PLUS);
		fileName = fileName.replace("%", HolmesAppConstants.PRCNT);
		fileName = fileName.replace("&", HolmesAppConstants.AMPNT);
		fileName = fileName.replace("'", "''");

		try {
			fname = URLEncoder.encode(fileName, HolmesAppConstants.UTF8);

		} catch (UnsupportedEncodingException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLatestKsdFileDetails",
					"Unsupported Encoding Formats \n" + e.getMessage());

		}
		try {

			List<String> columnName1 = new ArrayList();

			columnName1.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
			columnName1.add(HolmesAppConstants.FILE_NAMES);
			columnName1.add(HolmesAppConstants.ACTIVE_FLAG);

			List<String> columnCondition1 = new ArrayList();
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);

			List<String> columnValue1 = new ArrayList();

			columnValue1.add(pjmId);
			columnValue1.add(fname);
			columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);

			List<com.wipro.fipc.entity.layoutrule.LayoutConfig> layoutDBResponse1 = objLayoutConfigController
					.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
			String layoutDBResponse = objectMapper.writeValueAsString(layoutDBResponse1);

			if (layoutDBResponse.contains(HolmesAppConstants.SHEET_NAME_CONSTANT)
					&& ksdFileDetailsResponse.contains(HolmesAppConstants.SHEET_NAME_CONSTANT)) {

				layoutResponse = doConverter.convertDOToLayoutMultiSheetLatest(layoutDBResponse, ksdFileDetailsResponse,
						pjmId);

			} else {

				layoutResponse = doConverter.convertDOToKsdMultiSheetlatest(layoutDBResponse, ksdFileDetailsResponse,
						pjmId);

			}

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLatestKsdFileDetails()",
					"Unable to connect to layoutConfig DB Service due to {} \n" + exception.getMessage());
		}

		return layoutResponse;
	}

	@Override
	public String updateLayoutDataNew(LayoutRequest request, String appName, String sessionToken) {
		String pjmId = request.getProcessJobMappingId();
		String id = request.getId();

		String ksdDBResponse = null;
		StringBuilder formatErrmessage = new StringBuilder("");
		String formatErrIndicator = "No";

		if (request.getFileName().equals("")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "FileName is empty. Please Enter File Name");
			LoggerUtil.log(getClass(), Level.ERROR, "updateLayoutData", "FileName is empty \n");
			return jobj.toString();
		}

		List<LayoutRecord> layoutRecordsList = getLayoutRecordsList(request);
		formatErrmessage = formatErrmessage.append(PLEASE_CHECK);
		int count = 0;
		for (int i = 0; i < layoutRecordsList.size(); i++) {

			String dataFormat = layoutRecordsList.get(i).getFormat();
			if ((dataFormat.contains("(")) && (!dataFormat.contains(")"))
					|| (!dataFormat.contains("(")) && (dataFormat.contains(")"))) {

				if (count == 0) {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(" ").append(layoutRecordsList.get(i).getDataElement());
				} else {
					formatErrIndicator = "Yes";
					formatErrmessage = formatErrmessage.append(", ").append(layoutRecordsList.get(i).getDataElement());
				}
				count++;
			}

		}
		formatErrmessage = formatErrmessage.append(FORMAT);
		if (formatErrIndicator.equalsIgnoreCase("Yes")) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, formatErrmessage.toString());
			LoggerUtil.log(getClass(), Level.ERROR, "updateLayout data method", formatErrmessage.toString());
			return jobj.toString();
		}

		ksdDBResponse = getKsd(request);

		if (ksdDBResponse == null || ksdDBResponse.length() == 0) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, FAILURE_MESSAGE);
			LoggerUtil.log(getClass(), Level.ERROR, "updateLayoutData method",
					"Unable to receive response from ksdDB service \n");
			return jobj.toString();
		}

		JsonArray ksdJson = (JsonArray) parser.parse(ksdDBResponse);
		return addLayout(layoutRecordsList, request, ksdJson);
	}

	public String getKsdFileDetailsVaptNew(String pjmId, String fileName) throws JsonProcessingException {
		LoggerUtil.log(getClass(), Level.INFO, "getLayoutDetails", "FileName SeriveImpl:" + fileName);
		String layoutResponse = "";
		String ksdDBResponse = "";

		fileName = fileName.replace("+", HolmesAppConstants.PLUS);
		fileName = fileName.replace("%", HolmesAppConstants.PRCNT);
		fileName = fileName.replace("&", HolmesAppConstants.AMPNT);
		fileName = fileName.replace("'", "''");

		List<String> columnNames = new ArrayList<>();
		columnNames.add(HolmesAppConstants.ACTIVE_FLAG);
		columnNames.add(HolmesAppConstants.PROCESS_JOB_MAPPING_ID);
		columnNames.add(HolmesAppConstants.FILE_NAMES);
		List<String> columnConditions = new ArrayList<>();
		columnConditions.add(HolmesAppConstants.EQUAL);
		columnConditions.add(HolmesAppConstants.EQUAL);
		columnConditions.add(HolmesAppConstants.EQUAL);
		List<String> columnValues = new ArrayList<>();
		columnValues.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
		columnValues.add(pjmId);
		columnValues.add(fileName);

		for (int j = 0; j < columnValues.size(); j++) {
			String columnValue = columnValues.get(j);
			String remove1 = PLUS;
			String remove2 = PRCNT;
			String remove3 = AMPNT;
			if (columnValue.contains(remove1))
				columnValue = columnValue.replaceAll(PLUS, "+");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of PLUS columnValue: " + columnValue);

			if (columnValue.contains(remove2))
				columnValue = columnValue.replaceAll(PRCNT, "%");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of PRCNT columnValue: " + columnValue);

			if (columnValue.contains(remove3))
				columnValue = columnValue.replaceAll(AMPNT, "&");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of AMPNT columnValue: " + columnValue);

			columnValues.set(j, columnValue);
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of columnValue: " + columnValue);

		}
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);

		List<com.wipro.fipc.entity.batch.KsdFileDetails> originList = genericKsdFileDetailsDao
				.findByMultiColumnCondition(com.wipro.fipc.entity.batch.KsdFileDetails.class, KSD_SCHEMA,
						KSD_FILE_DETAILS, columnConditionParams);

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(originList);
	}
	
	@Override
	public Workbook getTrustCodeMappingTemplate() {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			createTrustCodeMappingTemplate(workbook);
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTrustCodeMappingTemplate",
					"Unable to create trust mapping template due to {} \n", ex.getMessage());
		}
		return workbook;
	}

	private void createTrustCodeMappingTemplate(XSSFWorkbook workbook) {
		LoggerUtil.log(getClass(), Level.INFO, "createTrustCodeMappingTemplate", "Updating Excel Data: ");
		XSSFSheet spreadsheet = workbook.createSheet(HolmesAppConstants.SHEET_1);
		XSSFCellStyle style = workbook.createCellStyle();
		style.setBorderBottom(BorderStyle.THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderRight(BorderStyle.THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(BorderStyle.THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(BorderStyle.THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);

		XSSFRow row;
		XSSFCell cell;

		int rowCount = 0;
		row = spreadsheet.createRow(rowCount);

		cell = row.createCell(row.getLastCellNum() + 1);
		cell.setCellValue(HolmesAppConstants.CLIENTID);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.CLIENT_NAME);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.CHECK_WRITTER_NAME);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.LONG_DESCRIPTION);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.SHORT_DESCRIPTION);
		cell.setCellStyle(style);

		cell = row.createCell(row.getLastCellNum());
		cell.setCellValue(HolmesAppConstants.TRUST_CODE);
		cell.setCellStyle(style);

		LoggerUtil.log(getClass(), Level.INFO, "createTrustCodeMappingTemplate", "Excell updated.");
	}

	@Override
	public String parseTrustCodeMappingReport(MultipartFile file, String clientId) throws Exception {
		LoggerUtil.log(getClass(), Level.INFO, "parseTrustCodeMappingReport",
				"parseTrustCodeMappingReport method start.");
		List<UnAvailableTrustCodeRespnse> unAvailableTrustCodeList = new ArrayList<>();
		List<TrustCodeMappingReport> mappingReprtList = new ArrayList<>();
		String unAvailableTrustCodeValues = "";
		JsonObject obj = new JsonObject();
		obj.addProperty(HolmesAppConstants.TRUST_CODE_MAPPING_RESPONSE, "");
		obj.addProperty(HolmesAppConstants.ERROR, "");
		try (Workbook workBook = WorkbookFactory.create(file.getInputStream())) {

			Sheet sheet = workBook.getSheetAt(0);
			if (validateHeaders(trustCodeMappingHeaders, sheet.getRow(0))) {
				
				int totalRows = sheet.getLastRowNum();
				for (int row = 1; row <= totalRows; row++) {
					int cellIdx = 0;
					TrustCodeMappingReport report = new TrustCodeMappingReport();
					for (int i = 0; i < sheet.getRow(row).getLastCellNum(); i++) {
						Cell currentCell = sheet.getRow(row).getCell(i);
						report = getRecordFromCell(report, currentCell, cellIdx);
						cellIdx++;
					}
					mappingReprtList.add(report);
				}
				updateInquiryMetaData(mappingReprtList, unAvailableTrustCodeList, obj, clientId);
				if (CollectionUtils.isNotEmpty(unAvailableTrustCodeList)
						&& !StringUtils.hasText(obj.get(HolmesAppConstants.ERROR).getAsString())) {
					String trustCodeMappingValue = getObjectMapper().writeValueAsString(unAvailableTrustCodeList);
					unAvailableTrustCodeValues = doConverter.convertTrustCodeMapping(true, null, trustCodeMappingValue);
				} else
					unAvailableTrustCodeValues = obj.toString();

				workBook.close();
				LoggerUtil.log(getClass(), Level.INFO, "parseTrustCodeMappingReport",
						"parseTrustCodeMappingReport method end.");
				return unAvailableTrustCodeValues;
			} else {
				obj.addProperty(HolmesAppConstants.ERROR, HolmesAppConstants.HEADER_MISMATCH);
				return obj.toString();
			}
		}
	}

	private boolean validateHeaders(List<String> mappingHeaders, Row headerRow) {
		LoggerUtil.log(getClass(), Level.INFO, "validateHeaders", "validateHeaders method start.");
		List<String> headers = new ArrayList<>();
		Iterator<Cell> headerCells = headerRow.cellIterator();
		while (headerCells.hasNext()) {
			Cell cell = (Cell) headerCells.next();
			RichTextString headerColumn = cell.getRichStringCellValue();
			headers.add(headerColumn.getString());
		}
		if (mappingHeaders.equals(headers))
			return true;
		LoggerUtil.log(getClass(), Level.INFO, "validateHeaders", "validateHeaders method end.");
		return false;
	}

	private TrustCodeMappingReport getRecordFromCell(TrustCodeMappingReport report, Cell currentCell, int cellIdx)
			throws Exception {
		LoggerUtil.log(this.getClass(), Level.INFO, "getRecordFromCell", "Inside getRecordFromCell method");
		DataFormatter fmt = new DataFormatter();
		switch (cellIdx) {
		case 0:
			if (fmt.formatCellValue(currentCell).matches(HolmesAppConstants.DIGIT))
				report.setClientId(fmt.formatCellValue(currentCell));
			else
				throw new Exception("Client Id is invalid.");
			break;
		case 1:
			report.setClientName(fmt.formatCellValue(currentCell));
			break;
		case 2:
			report.setCheckWritterName(fmt.formatCellValue(currentCell));
			break;
		case 3:
			report.setLongDescription(fmt.formatCellValue(currentCell));
			break;
		case 4:
			report.setShortDescription(fmt.formatCellValue(currentCell));
			break;
		case 5:
			report.setTrustCode(fmt.formatCellValue(currentCell));
			break;
		default:
			break;
		}

		return report;
	}

	private void updateInquiryMetaData(List<TrustCodeMappingReport> mappingReportList,
			List<UnAvailableTrustCodeRespnse> unAvailableTrustCodeList, JsonObject obj, String clientId) throws Exception {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateInquiryMetaData", "Inside updateInquiryMetaData method");
		List<com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster> entityList = new ArrayList<>();
		
		Map<String, String> reportMap = mappingReportList.stream()
				.collect(Collectors.groupingBy(reports -> reports.getShortDescription().trim(),
						Collectors.mapping(TrustCodeMappingReport::getTrustCode, Collectors.collectingAndThen(
								Collectors.toCollection(LinkedHashSet::new), set -> String.join(",", set)))));
		mappingReportList = mappingReportList.stream().peek(reports -> {
			reports.setClientId(String.format("%05d", Integer.parseInt(reports.getClientId())));
		}).collect(Collectors.toList());
		
		try {
			for (int i = 0; i < mappingReportList.size(); i++) {

				boolean clientCodeMatch = mappingReportList.get(i).getClientId().equals(clientId) ? true : false;

				Optional<List<TbaInquiryMetaDataMaster>> result = tbaInquiryMetaDataDao
						.findByClientIdAndMetaDataTypeAndShortDescription(
								Integer.valueOf(mappingReportList.get(i).getClientId()), HolmesAppConstants.PAYMENT,
								mappingReportList.get(i).getShortDescription().trim());
				if (result.isPresent() && result.get().size() > 0 && clientCodeMatch) {
					LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaData",
							"inquiryMetaData List is Present");
					List<com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster> entities = result.get();

					entities.stream().filter(data -> reportMap.containsKey(data.getAttributeText()))
							.forEach(data -> data.setTrustCode(reportMap.get(data.getAttributeText())));

					entityList.addAll(entities);
				} else {
					UnAvailableTrustCodeRespnse response = UnAvailableTrustCodeRespnse.builder()
							.row(String.valueOf(i + 2)).clientId(mappingReportList.get(i).getClientId())
							.clientName(mappingReportList.get(i).getClientName())
							.checkWritterName(mappingReportList.get(i).getCheckWritterName())
							.longDescription(mappingReportList.get(i).getLongDescription())
							.shortDescription(mappingReportList.get(i).getShortDescription())
							.trustCode(mappingReportList.get(i).getTrustCode())
							.comments(clientCodeMatch ? HolmesAppConstants.SHORT_DESCRIPTION_MISMATCH
									: HolmesAppConstants.CLIENT_CODE_MISMATCH)
							.build();
					unAvailableTrustCodeList.add(response);
				}
			}

			if (CollectionUtils.isNotEmpty(entityList)) {
				tbaInquiryMetaDataDao.saveAll(entityList);
				LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaData",
						"inquiryMetaData updated successfully as per the mapping report.");
			}

		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateInquiryMetaData", "data could not be updated due to {} \\n",
					ex);
			obj.addProperty(HolmesAppConstants.ERROR, ex.getMessage());
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "updateInquiryMetaData", "updateInquiryMetaData method end.");
	}

	@Override
	public String getTrustCodeMappingValues(String clientId, String clientName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTrustCodeMappingValues",
				"getTrustCodeMappingValues method start.");
		String trustCodeMappingValues = "";
		try {
			Optional<List<TbaInquiryMetaDataMaster>> result = tbaInquiryMetaDataDao
					.findTrustCodeMappingRecord(Integer.valueOf(clientId), HolmesAppConstants.PAYMENT);
			if (result.isPresent() && result.get().size() > 0) {
				LoggerUtil.log(this.getClass(), Level.INFO, "getTrustCodeMappingValues",
						"inquiryMetaData List is Present");
				List<com.wipro.fipc.model.TbaInquiryMetaDataMaster> tbaInquiryMetadaMasterList = new ArrayList<>();
				List<com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster> entities = result.get();
				entities.stream().forEach(entity -> {
					com.wipro.fipc.model.TbaInquiryMetaDataMaster model = new com.wipro.fipc.model.TbaInquiryMetaDataMaster();
					BeanUtils.copyProperties(entity, model);
					if (!tbaInquiryMetadaMasterList.stream()
							.anyMatch(details -> com.wipro.fipc.model.TbaInquiryMetaDataMaster.comparator
									.compare(details, model) == 0))
						tbaInquiryMetadaMasterList.add(model);
				});

				String tbaInquiryMetadaMasterValues = getObjectMapper().writeValueAsString(tbaInquiryMetadaMasterList);
				trustCodeMappingValues = doConverter.convertTrustCodeMapping(false, clientName,
						tbaInquiryMetadaMasterValues);
			}

		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTrustCodeMappingValues",
					"Unable to fetch Trust code mapping records due to {} \n", ex);
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "getTrustCodeMappingValues",
				"getTrustCodeMappingValues method end.");
		return trustCodeMappingValues;
	}

	private ObjectMapper getObjectMapper() {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper;
	}

}