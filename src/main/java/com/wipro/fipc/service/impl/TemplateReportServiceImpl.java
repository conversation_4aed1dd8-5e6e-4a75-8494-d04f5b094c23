package com.wipro.fipc.service.impl;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.commons.io.IOUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.common.TemplateReportLayOutDao;
import com.wipro.fipc.dao.common.TemplateReportUploadDao;
import com.wipro.fipc.entity.TemplateReportLayOut;
import com.wipro.fipc.entity.TemplateReportUpload;
import com.wipro.fipc.model.Element;
import com.wipro.fipc.model.KsdOutPutFileDetails;
import com.wipro.fipc.model.LabellingRecord;
import com.wipro.fipc.model.LabellingReportRecord;
import com.wipro.fipc.model.OutputReport;
import com.wipro.fipc.model.TemplateReportLayout;
import com.wipro.fipc.model.TemplateReportLayoutResponse;
import com.wipro.fipc.model.TemplateReportModel;
import com.wipro.fipc.model.TemplateReportRequest;
import com.wipro.fipc.model.WriteOutputReportDto;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.pojo.TemplateReportUploadPojo;
import com.wipro.fipc.service.ITemplateReportService;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;

@Service
public class TemplateReportServiceImpl implements ITemplateReportService {

	public static final String SSN = "SSN";
	public static final String SEARCH_FOR_TEXT = "Search for text";
	public static final String WHITELIST = "whitelist";
	public static final String STATUS = "status";
	public static final String MESSAGE = "message";
	public static final String SUCCESS = "success";
	public static final String FAILED = "failed";
	public static final String REPORT = "report";
	public static final String FILE = "file";
	public static final String FILE_DELETED = "File Deleted";
	public static final String LABELLING_REPORT = "Labelling Report";

	@Autowired
	Gson gson;

	@Autowired
	Environment env;

	@Autowired
	JsonParser parser;

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	GenericDao<TemplateReportLayOut> genericDao;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	TemplateReportLayOutDao templateReportLayOutDao;

	@Autowired
	TemplateReportUploadDao templateReportUploadDao;

	@Autowired
	private BaseDao<TemplateReportLayOut> dao;

	@Autowired
	private BaseDao<TemplateReportUpload> templateReportUplBaDao;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	protected static final String TEMPLATE_REPORT_LAYOUT = "TEMPLATE_REPORT_LAYOUT";
	protected static final String SCHEMA = "common";

	@Override
	public String saveTemplateData(MultipartFile file, TemplateReportRequest uiRequest, String appName,
			String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "saveTemplateData() -> TEMPLATE", "ADID = " + adid);
		uiRequest.setCreatedBy(adid);
		uiRequest.setUpdatedBy(adid);
		uiRequest.setUploadedBy(adid);
		uiRequest.setActiveFlag('T');
		return saveTemplate(file, uiRequest);
	}

	@Override
	public String updateTemplateData(MultipartFile file, TemplateReportRequest uiUpdateReq, String appName,
			String sessionToken) {
		TemplateReportRequest dbData = getDbTemplate(uiUpdateReq.getId());
		CustomBeanUtils.copyProperties(uiUpdateReq, dbData);
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "saveTemplateData() -> TEMPLATE", "ADID = " + adid);
		dbData.setUpdatedBy(adid);
		dbData.setUploadedBy(adid);
		dbData.setActiveFlag('T');
		return updateTemplate(file, dbData);
	}

	public TemplateReportRequest getDbTemplate(Long id) {
		TemplateReportRequest dbData = new TemplateReportRequest();
		String url = env.getProperty("getTemplateUpload") + id;
		dbData = restTemplate.getForEntity(url, TemplateReportRequest[].class).getBody()[0];
		LoggerUtil.log(this.getClass(), Level.INFO, "getDbTemplate()", "successful response from db api for id " + id);
		return dbData;
	}

	@Override
	public String saveTemplate(MultipartFile file, TemplateReportRequest trequest) {
		Long id = null;
		List<TemplateReportLayOut> templateLayout = null;
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "calling addTemplateReport",
					" to add data to template_report_upload");
			TemplateReportUpload templateReportUploadObject = addTemplateReport(file, trequest);
			id = templateReportUploadObject.getId();
			templateLayout = getTemplateLayout(file, id);
			getTemplateLayoutRequest(id, trequest, templateLayout, templateReportUploadObject);
			addTemplateLayout(templateLayout);
			LoggerUtil.log(this.getClass(), Level.INFO, "addTemplateLayout call is successful", " " + id);
		} catch (Exception e) {
			if (id != null) {
				deleteTemplate(id);
			}
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Template/Report  is not saved. Please contact system administrator.");
			LoggerUtil.log(this.getClass(), Level.INFO, "saveTemplate method", "Report is not saved.");
			return jobj.toString();
		}

		JsonObject jobj = new JsonObject();
		jobj.addProperty(STATUS, SUCCESS);
		jobj.addProperty(MESSAGE, "Template/Report saved Successfully.");
		LoggerUtil.log(this.getClass(), Level.INFO, "saveTemplate()", "Template/Report saved Successfully.");
		return jobj.toString();
	}

	public TemplateReportUpload addTemplateReport(MultipartFile file, TemplateReportRequest templateReportUpload)
			throws IOException {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);

		Date date = new Date();
		TemplateReportUpload templateReportUploads = new TemplateReportUpload();
		byte[] content = file.getBytes();
		templateReportUploads.setReport(content);
		templateReportUploads.setBuId(templateReportUpload.getBuId());
		templateReportUploads.setActiveFlag(templateReportUpload.getActiveFlag());
		templateReportUploads.setClientId(templateReportUpload.getClientId());
		templateReportUploads.setClientName(templateReportUpload.getClientName());
		templateReportUploads.setCreatedBy(templateReportUpload.getCreatedBy());
		templateReportUploads.setTemplateReportName(templateReportUpload.getTemplateReportName());
		templateReportUploads.setType(templateReportUpload.getType());
		templateReportUploads.setUploadedBy(templateReportUpload.getUploadedBy());
		templateReportUploads.setUpdatedBy(templateReportUpload.getUploadedBy());
		templateReportUploads.setUpdatedDate(new Timestamp(date.getTime()));
		templateReportUploads.setUploadedDate(new Timestamp(date.getTime()));
		templateReportUploads.setCreatedDate(new Timestamp(date.getTime()));
		templateReportUploads.setTemplateReportNameWs(templateReportUpload.getTemplateReportNameWs());
		templateReportUploads.setReportFlag(templateReportUpload.getReportFlag());
		templateReportUplBaDao.save(templateReportUploads);
		return templateReportUploads;
	}

	public List<TemplateReportLayOut> getTemplateLayout(MultipartFile file, Long id) throws URISyntaxException {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		String getTemplateLayoutUrl = env.getProperty("getTemplateLayoutUrl");
		URI uri = new URI(getTemplateLayoutUrl);
		MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
		params.add(FILE, file.getResource());
		params.add("id", id);
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);
		TemplateReportLayoutResponse response = null;
		response = restTemplate.postForEntity(uri, request, TemplateReportLayoutResponse.class).getBody();
		return response.getJson();

	}

	public List<TemplateReportLayout> getTemplateLayoutForLabelling(MultipartFile file)
			throws URISyntaxException, JsonProcessingException {
		List<TemplateReportLayout> layoutList = new ArrayList<>();
		TemplateReportLayout layout = new TemplateReportLayout();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		layout.setLabellingReportRecord(objectMapper.writeValueAsString(getLabellingReportForFile(file)));
		layoutList.add(layout);
		return layoutList;
	}

	public LabellingReportRecord getLabellingReportForFile(MultipartFile file) throws URISyntaxException {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		String getTemplateLayoutUrl = env.getProperty("getLabellingReportForFileUrl");
		// Unknown url
		URI uri = new URI(getTemplateLayoutUrl);
		MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
		params.add("file", file.getResource());
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);
		LabellingReportRecord response = null;
		response = restTemplate.postForEntity(uri, request, LabellingReportRecord.class).getBody();
		return response;

	}

	public List<TemplateReportLayOut> addTemplateLayout(List<TemplateReportLayOut> templateLayout) {
		List<TemplateReportLayOut> response = null;
		try {
			response = genericDao.saveAllTempalteConfig(templateLayout);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.INFO, "addTemplateLayout",
					"error in post request :" + e.getMessage());

		}
		LoggerUtil.log(this.getClass(), Level.INFO, " response from addTemplateLayout()", " :" + response);
		return response;
	}

	public List<TemplateReportLayOut> getTemplateLayoutRequest(Long id, TemplateReportRequest trequest,
			List<TemplateReportLayOut> templateLayout, TemplateReportUpload templateReport) {
		Date date = new Date();
		for (TemplateReportLayOut temp : templateLayout) {
			temp.setTemplateReportUpload(templateReport);
			temp.setActiveFlag('T');
			temp.setUpdatedBy(trequest.getUpdatedBy());
			temp.setCreatedBy(trequest.getCreatedBy());
			temp.setTemplateReportName(trequest.getTemplateReportName());
			temp.setCreatedDate(date);
			temp.setUpdatedDate(date);
			temp.setTemplateReportNameWs(trequest.getTemplateReportNameWs());
		}
		return templateLayout;
	}

	@Override
	public String updateTemplate(MultipartFile file, TemplateReportRequest trequest) {
		Long id = null;
		List<TemplateReportLayOut> templateLayout = null;
		try {
			id = updateTemplateReport(file, trequest);
			templateLayout = getUpdatedTemplateLayout(file, id);
			templateLayout = getTemplateLayoutRequest(id, trequest, templateLayout, null);
			updateTemplateLayout(id, templateLayout);
		} catch (Exception e) {
			if (id != null) {
				deleteTemplate(id);
			}
		}
		JsonObject jobj = new JsonObject();
		jobj.addProperty(STATUS, SUCCESS);
		jobj.addProperty(MESSAGE, "Template/Report updated Successfully.");
		LoggerUtil.log(this.getClass(), Level.INFO, "updateTemplate()", "Template/Report updated Successfully.");
		return jobj.toString();
	}

	public Long updateTemplateReport(MultipartFile file, TemplateReportRequest trequest) throws URISyntaxException {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		String updateTemplateUrl = env.getProperty("updateTemplateUrl");
		URI uri = new URI(updateTemplateUrl);

		MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
		params.add("id", trequest.getId());
		params.add("type", trequest.getType());
		params.add("clientId", trequest.getClientId());
		params.add("templateReportName", trequest.getTemplateReportName());
		params.add("updatedBy", trequest.getUpdatedBy());
		params.add("uploadedBy", trequest.getUploadedBy());
		params.add("clientName", trequest.getClientName());
		params.add("activeFlag", trequest.getActiveFlag());
		params.add("buId", trequest.getBuId());
		params.add("templateReportNameWs", trequest.getTemplateReportNameWs());
		params.add("reportFlag", trequest.getReportFlag());
		params.add(REPORT, file.getResource());
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);
		TemplateReportRequest response = null;
		response = restTemplate.exchange(uri, HttpMethod.PUT, request, TemplateReportRequest.class).getBody();

		return response.getId();
	}

	public List<TemplateReportLayOut> getUpdatedTemplateLayout(MultipartFile file, Long id) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		String getTemplateLayoutUrl = env.getProperty("getTemplateLayoutUrl");// FileFOrmatter API ->
		MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
		params.add(REPORT, file.getResource());
		params.add("id", id);
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);

		TemplateReportLayoutResponse response = null;
		response = restTemplate.postForEntity(getTemplateLayoutUrl, request, TemplateReportLayoutResponse.class)
				.getBody();
		return response.getJson();
	}

	public boolean updateTemplateLayout(Long id, List<TemplateReportLayOut> templateLayout) throws URISyntaxException {

		templateReportLayOutDao.deleteTemplateReportLayOut(id);
		Optional<TemplateReportUpload> templateReportUpload = templateReportUplBaDao.findById(id);
		templateLayout.forEach(templateReportLayOut -> {
			templateReportLayOut.setTemplateReportUpload(templateReportUpload.get());
			dao.save(templateReportLayOut);
		});

		return true;
	}

	@Override
	public byte[] downloadTemplate(long id) throws IOException {

		Optional<TemplateReportUpload> temp = templateReportUplBaDao.findById(id);
		TemplateReportUpload tempppp = temp.get();
		ByteArrayInputStream inputStream = new ByteArrayInputStream(tempppp.getReport());

		LoggerUtil.log(this.getClass(), Level.INFO, "downloadTemplate() -> TEMPLATE ", "ID = " + id);
		LoggerUtil.log(this.getClass(), Level.INFO, "downloadTemplate()",
				"got successful response from db download api");
		return IOUtils.toByteArray(inputStream);
	}

	@Override
	public String deleteTemplate(long id) {

		String response = null;
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTemplate -> TEMPLATE", "ID = " + id);
		templateReportUploadDao.deleteTemplateReportUpload(id);
		response = "File Deleted";
		JsonObject jobj = new JsonObject();
		if (response.equals(FILE_DELETED)) {
			jobj.addProperty(STATUS, SUCCESS);
			jobj.addProperty(MESSAGE, "Template/Report deleted successfully.");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteTemplate()", "Template/Report deleted successfully.");
			return jobj.toString();
		}
		jobj.addProperty(STATUS, FAILED);
		jobj.addProperty(MESSAGE, "Template/Report is not deleted.");
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTemplate()", "Template/Report is  not deleted.");
		return jobj.toString();
	}

	@Override
	public List<TemplateReportUploadPojo> getAllTemplates() {

		List<TemplateReportUploadPojo> templateReportList = templateReportUploadDao.getListOfData();
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllTemplates()", "success response from db api");

		return templateReportList;
	}

	@Override
	public List<TemplateReportUploadPojo> getTemplateForClient(String clientId) {
		List<TemplateReportUploadPojo> getallDataWithAllClients = null;
		String clientID = Integer.toString(customBeanUtils.checkForClientCode(clientId));
		getallDataWithAllClients = templateReportUploadDao.getallDataWithAllClients(clientID);
		
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateForClient()",
				"successful response from db  " + clientId);
		return getallDataWithAllClients;
	}

	@Override
	public String getTemplateLayout(long id, long pjmid) throws JsonProcessingException {
		WriteOutputReportDto output = new WriteOutputReportDto();
		List<KsdOutPutFileDetails> ksdlist = new ArrayList<>();
		List<TemplateReportLayOut> response = null;
		response = templateReportLayOutDao.getListOfTemplateReportLayout(id);
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateLayout method",
				"successful response from db   " + response);
		ProcessJobMapping pjm = new ProcessJobMapping();
		pjm.setId(pjmid);

		boolean flag = false;
		for (TemplateReportLayOut tempLayout : response) {
			OutputReport outrep = new OutputReport();
			outrep.setActiveFlag(String.valueOf(tempLayout.getActiveFlag()));
			outrep.setCreatedBy(tempLayout.getCreatedBy());
			outrep.setCreatedDate(tempLayout.getCreatedDate());
			outrep.setUpdatedBy(tempLayout.getUpdatedBy());
			outrep.setUpdatedDate(tempLayout.getUpdatedDate());
			outrep.setCellName(tempLayout.getSequence());
			outrep.setDataElement(tempLayout.getDataElement());
			outrep.setFileName(tempLayout.getTemplateReportName());
			outrep.setRecordIdentifier(tempLayout.getIdentifier());
			outrep.setDataFormat(tempLayout.getFiledType());
			outrep.setAddOn("");
			outrep.setCellValue("");
			outrep.setChildElement("");
			outrep.setDataElementWoutSpace(tempLayout.getDataElementWs());
			outrep.setProcessJobMapping(pjm);
			outrep.setTotal("");

			for (KsdOutPutFileDetails ksd : ksdlist) {
				if (ksd.getSheetName().equals(tempLayout.getSheetName())) {
					flag = true;
					ksd.getOutputReports().add(outrep);
				}
			}
			if (!flag) {
				KsdOutPutFileDetails ksdoutput = new KsdOutPutFileDetails();
				ksdoutput.getOutputReports().add(outrep);
				ksdoutput.setFileName(tempLayout.getTemplateReportName());
				ksdoutput.setProcessJobMapping(pjm);
				ksdoutput.setSheetName(tempLayout.getSheetName());
				ksdlist.add(ksdoutput);
				ksdoutput.setSheetNameWoutSpace(tempLayout.getSheetNameWs());
				ksdoutput.setFileNameWoutSpace(tempLayout.getTemplateReportNameWs());

			}
			flag = false;

		}
		output.setUiJson(ksdlist);
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateLayout()",
				"successful response from db api  " + output);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(output);
	}

	@Override
	public String addLabellingReportData(TemplateReportModel uiData, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "addLabellingReportData() -> TEMPLATE", "ADID = " + adid);
		uiData.setCreatedBy(adid);
		uiData.setUpdatedBy(adid);
		uiData.setUploadedBy(adid);
		uiData.setActiveFlag('T');
		return saveLabellingReportData(uiData);
	}

	@Override
	public String updateLabellingReportData(Long id, TemplateReportModel uiUpdatedData, String appName,
			String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateLabellingReportData() -> TEMPLATE", "ADID = " + adid);
		uiUpdatedData.setUpdatedBy(adid);
		uiUpdatedData.setUploadedBy(adid);
		uiUpdatedData.setCreatedBy(adid);
		uiUpdatedData.setActiveFlag('T');
		return updateLabellingReportData(id, uiUpdatedData);
	}

	@Override
	public String saveLabellingReportData(TemplateReportModel data) {
		byte[] s = new byte[0];
		MultipartFile multipartFile = new MockMultipartFile(data.getTemplateReportName(), data.getTemplateReportName(),
				"", s);
		TemplateReportRequest upload = getLabellingReportUpload(data);
		LoggerUtil.log(this.getClass(), Level.INFO, "saveLabellingReportData()", " method is called ");
		Long id = null;
		try {
			TemplateReportUpload tru = addTemplateReport(multipartFile, upload);
			List<TemplateReportLayOut> layoutList = getLabellingReportLayout(id, data);
			addTemplateLayout(layoutList);
		} catch (Exception e) {
			if (id != null) {
				deleteTemplate(id);
			}
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Labelling Report is not saved.");
			LoggerUtil.log(this.getClass(), Level.INFO, "saveLabellingReportData method",
					"LabellingReport is not saved.");
			return jobj.toString();
		}
		JsonObject jobj = new JsonObject();
		jobj.addProperty(STATUS, SUCCESS);
		jobj.addProperty(MESSAGE, "Labelling Report saved Successfully.");
		LoggerUtil.log(this.getClass(), Level.INFO, "saveLabellingReportData()", "LabellingReport saved Successfully.");
		return jobj.toString();
	}

	public TemplateReportRequest getLabellingReportUpload(TemplateReportModel data) {
		TemplateReportRequest upload = new TemplateReportRequest();
		upload.setActiveFlag(data.getActiveFlag());
		upload.setBuId(data.getBuId());
		upload.setClientId(data.getClientId());
		upload.setClientName(data.getClientName());
		upload.setCreatedBy(data.getCreatedBy());
		upload.setTemplateReportName(data.getTemplateReportName());
		upload.setTemplateReportNameWs(data.getTemplateReportNameWs());
		upload.setType(data.getType());
		upload.setUpdatedBy(data.getUpdatedBy());
		upload.setUploadedBy(data.getUploadedBy());
		upload.setReportFlag(data.getReportFlag());
		return upload;
	}

	public List<TemplateReportLayOut> getLabellingReportLayout(Long id, TemplateReportModel data)
			throws JsonProcessingException {
		Date date = new Date();
		TemplateReportLayOut layout = new TemplateReportLayOut();
		layout.setCreatedDate(date);
		layout.setUpdatedDate(date);
		layout.setCreatedBy(data.getCreatedBy());
		layout.setUpdatedBy(data.getUpdatedBy());
		layout.setActiveFlag('T');
		layout.setTemplateReportName(data.getTemplateReportName());
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		layout.setLabellingReportRecord(objectMapper.writeValueAsString(data.getLabellingReportRecord()));
		List<TemplateReportLayOut> layoutList = new ArrayList<>();
		layoutList.add(layout);
		return layoutList;
	}

	@Override
	public List<TemplateReportUploadPojo> getTemplateReportNames(String type, String clientId) {
		String clientID = Integer.toString(customBeanUtils.checkForClientCode(clientId));
		List<TemplateReportUploadPojo> getallDataWithAllClients = templateReportUploadDao
				.getallDataWithAllClients(clientID);
		ArrayList<TemplateReportUploadPojo> templist = new ArrayList<>();
		LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateReportNames()",
				"calling db api to getTemplateReportNames");
		for (TemplateReportUploadPojo temp : getallDataWithAllClients) {
			if (temp.getType().equalsIgnoreCase(type)) {
				templist.add(temp);
			}
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllTemplates()",
				"successful response from getTemplateForClients db api");
		return templist;
	}

	@Override
	public String getLabellingReportData(long id) throws IOException {
		ObjectMapper obj = new ObjectMapper();
		obj.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		TemplateReportModel response = getLabellingReport(id);
		return obj.setSerializationInclusion(Include.NON_NULL).writeValueAsString(response);
	}

	public TemplateReportModel getLabellingReport(long id) throws IOException {
		ObjectMapper obj = new ObjectMapper();
		TemplateReportModel response = new TemplateReportModel();
		LoggerUtil.log(this.getClass(), Level.INFO, "getLabellingReport()",
				"calling db api to get LabellingReportData");
		List<TemplateReportLayOut> listOfData = templateReportLayOutDao.getListOfTemplateReportLayout(id);
		LoggerUtil.log(this.getClass(), Level.INFO, "getLabellingReport()", "successful response from db api");
		for (TemplateReportLayOut temp : listOfData) {
			LabellingReportRecord data = null;
			data = obj.readValue(temp.getLabellingReportRecord(), LabellingReportRecord.class);
			response.setTemplateReportName(temp.getTemplateReportName());
			response.setLabellingReportRecord(data);
			response.setId(temp.getId().toString());
		}
		return response;
	}

	public String getWhitelistName(long id) throws IOException {
		String whitelistName = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getWhitelistId method", "for labelling id " + id);
		LabellingReportRecord data = getLabellingReport(id).getLabellingReportRecord();
		for (LabellingRecord rec : data.getLabellingRecord()) {
			if (rec.getDataElement().equalsIgnoreCase(SSN)) {
				for (Element e : rec.getElement()) {
					if (e.getPatternIdentification().equalsIgnoreCase(SEARCH_FOR_TEXT) && e.isCheckBoxSelection()) {
						whitelistName = e.getPatternValue().get(0);
					}
				}
			}
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getWhitelistId()", "whitelistName is " + whitelistName);
		return whitelistName;
	}

	public long getWhitelistId(String whitelistName, String clientId) {
		long whitelistId = 0;
		List<TemplateReportUploadPojo> uploadList = getTemplateReportNames(WHITELIST, clientId);
		for (TemplateReportUploadPojo upload : uploadList) {
			if (upload.getTemplateReportName().equals(whitelistName)) {
				whitelistId = upload.getId();
			}
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getWhitelistId()", "successful response from db api");
		return whitelistId;
	}

	public String getSSNFromWhitelist(long whiteListId) throws IOException {
		String response = "";
		byte[] fileResponse = downloadTemplate(whiteListId);
		LoggerUtil.log(this.getClass(), Level.INFO, "getSSNFromWhitelist() ",
				"downloaded whitelist file for id " + whiteListId);
		File f = new File("whitelist.xlsx");
		try (FileOutputStream fos = new FileOutputStream(f)) {
			fos.write(fileResponse);
			MultipartFile multipartFile = new MockMultipartFile("whitelist.xlsx", f.getName(), "text/plain",
					IOUtils.toByteArray(new FileInputStream(f)));
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.MULTIPART_FORM_DATA);
			String getListOfSSNUrl = env.getProperty("getListOfSSN");
			URI uri = new URI(getListOfSSNUrl);
			MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
			params.add("file", multipartFile.getResource());
			params.add("columnName", SSN);
			HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);
			LoggerUtil.log(this.getClass(), Level.INFO, "getSSNFromWhitelist",
					"calling file formatter api with request :" + request);
			response = restTemplate.postForEntity(uri, request, String.class).getBody();// FileFormatter
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.INFO, "getSSNFromWhitelist methos ", "response is " + response);
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "getSSNList() ", "response is " + response);
		return response;
	}

	@Override
	public String getSSNList(long id, String clientId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getSSNList() ", "method is called for id " + id);
		JSONObject jobj = new JSONObject();
		try {
			String clientID = Integer.toString(customBeanUtils.checkForClientCode(clientId));
			jobj.put(WHITELIST, Collections.emptyList());
			String whitelistName = getWhitelistName(id);
			long whiteListId = getWhitelistId(whitelistName, clientID);
			if (whiteListId != 0) {
				return getSSNFromWhitelist(whiteListId);
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.INFO, "getSSNList method",
					"Exception while getting SSN " + e.getMessage());
		}
		return jobj.toString();
	}

	@Override
	public String updateLabellingReportData(Long id, TemplateReportModel data) {
		JsonObject jobj = new JsonObject();
		try {
			List<TemplateReportLayOut> layoutList = getLabellingReportLayout(id, data);
			boolean updated = updateTemplateLayout(id, layoutList);
			if (updated) {
				jobj.addProperty(STATUS, SUCCESS);
				jobj.addProperty(MESSAGE, "Labelling Report is updated Successfully.");
				return jobj.toString();
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.INFO, "updateLabellingReportData()",
					"LabellingReport is not updated with exception " + e.getMessage());
		}
		jobj.addProperty(STATUS, FAILED);
		jobj.addProperty(MESSAGE, "Labelling Report is not updated.");
		LoggerUtil.log(this.getClass(), Level.INFO, "updateLabellingReportData()", "LabellingReport is not updated.");
		return jobj.toString();
	}

}