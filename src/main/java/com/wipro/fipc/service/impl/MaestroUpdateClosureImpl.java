package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.GenericRestService;
import com.wipro.fipc.entity.filelayout.TaskUpdateConfig;
import com.wipro.fipc.service.MaestroUpdateClosureService;

@Service
public class MaestroUpdateClosureImpl implements MaestroUpdateClosureService {

	@Autowired
	MaestroUpdateClosureService maestroupdateclosureservice;
	@Autowired
	GenericDao<TaskUpdateConfig> taskdaoGeneric;


	GenericRestService<TaskUpdateConfig> genericService = new GenericRestService();;

	@Autowired
	Environment env;

	protected static final String TASK_UPDATE_CONFIG = "TASK_UPDATE_CONFIG";
	protected static final String MAESTRO_SCHEMA = "maestro";

	@Override
	public String getMaestroTicket(String columnName, String columnValue) {
		String response = "";
		try {

			List<TaskUpdateConfig> responseData = taskdaoGeneric.findByColumn(TaskUpdateConfig.class, MAESTRO_SCHEMA,
					TASK_UPDATE_CONFIG, columnName, columnValue);
			ObjectMapper objectMapper=new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			String listStr = objectMapper.writeValueAsString(responseData);
			return listStr;

		}

		catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getMaestroTicket1", "Exception : ", e.getMessage());
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getMaestroTicket1", "Response " + response);
		return response;
	}

	@Override
	public String createMaestroTicket(String maestroRequest) {

		TaskUpdateConfig obj = null;
		try {
			obj = new ObjectMapper().readValue(maestroRequest, TaskUpdateConfig.class);
		} catch (JsonParseException e) {

			e.printStackTrace();
		} catch (JsonMappingException e) {

			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();

		}
		genericService.create(obj);
		return maestroRequest;
	}

}
