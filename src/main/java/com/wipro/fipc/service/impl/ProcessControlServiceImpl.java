package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.Processcontrolsupport.RulesConfigContService;
import com.wipro.fipc.Processcontrolsupport.TbaUpdateConfigContService;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.TemplateReportLayOut;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.tba.TbaPendingEvent;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.fileLayoutsupport.KsdFileDetailsContService;
import com.wipro.fipc.fileLayoutsupport.LayoutConfigContService;
import com.wipro.fipc.fileLayoutsupport.OutputReportContrService1;
import com.wipro.fipc.fileLayoutsupport.ProcessControlConfigContService;
import com.wipro.fipc.fileLayoutsupport.TbaCommentInqConfigContService;
import com.wipro.fipc.fileLayoutsupport.TbaEventInquiryConfigContService;
import com.wipro.fipc.fileLayoutsupport.TbaInquiryConfigContService1;
import com.wipro.fipc.fileLayoutsupport.TbaNoticeInqConfigContService;
import com.wipro.fipc.fileLayoutsupport.TemplateReportLayOutContService;
import com.wipro.fipc.model.ProcessControl;
import com.wipro.fipc.model.ProcessControlResponse;
import com.wipro.fipc.model.TbaInquiryNoticeConfig;
import com.wipro.fipc.model.generated.KsdFileDetails;
import com.wipro.fipc.model.generated.LayoutConfig;
import com.wipro.fipc.model.generated.OutputReport;
import com.wipro.fipc.model.generated.ProcessControlConfig;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.RulesDefinition;
import com.wipro.fipc.model.generated.TbaCommentInqConfig;
import com.wipro.fipc.model.generated.TbaNoticeInqConfig;
import com.wipro.fipc.model.generated.TbaUpdateConfig;
import com.wipro.fipc.model.generated.ValidationType;
import com.wipro.fipc.service.IProcessControlService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class ProcessControlServiceImpl implements IProcessControlService {

	@Autowired
	private Gson gson;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	KsdFileDetailsContService objKsdFileDetailsContService;

	@Autowired
	ProcessControlConfigContService objProcessControlConfigContService;

	@Autowired
	TbaInquiryConfigContService1 objTbaInquiryConfigContService1;
	
	@Autowired
	TbaEventInquiryConfigContService objTbaEventInquiryConfigContService;

	@Autowired
	TbaNoticeInqConfigContService objTbaNoticeInqConfigContService;

	@Autowired
	TbaCommentInqConfigContService objTbaCommentInqConfigContService;

	@Autowired
	TemplateReportLayOutContService objTemplateReportLayOutContService;

	@Autowired
	LayoutConfigContService objLayoutConfigController;

	@Autowired
	KsdFileDetailsContService objKsdFileDetailsController;

	@Autowired
	RulesConfigContService objRulesConfigContService;

	@Autowired
	TbaUpdateConfigContService objTbaUpdateConfigContService;

	@Autowired
	OutputReportContrService1 objOutputReportContrService1;
	
	@Autowired
	private GenericDao<com.wipro.fipc.entity.filelayout.ValidationType> validationDao;
	
	@Autowired
	private GenericDao<TbaPendingEvent> tbaPendingEventDao;
	

	public static final String PJM_ID = "pjmID:";
	private static final String ADID2 = " ADID =";
	public static final String BUSINESS_RULE_CODE = "BR";
	protected static final String RULES_VALIDATION_TYPE = "RULES_VALIDATION_TYPE";
	protected static final String LAYOUT_SCHEMA = "layout_rule";

	@Override
	public List<KsdFileDetails> getApplication(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_TBA_FIELD,
				"ProcessControlServiceImpl-->getApplication()-->starts:");
		try {

			List<com.wipro.fipc.entity.batch.KsdFileDetails> findByColumn = objKsdFileDetailsContService
					.findByColumn(columnName, columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String res = objectMapper.writeValueAsString(findByColumn);
			List<KsdFileDetails> ksdList = Arrays.asList(new ObjectMapper().readValue(res, KsdFileDetails[].class));
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_TBA_FIELD,
					"ProcessControlServiceImpl-->getApplication()--ends.");
			return ksdList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_TBA_FIELD,
					"ProcessControlServiceImpl-->getApplication()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	@Override
	public List<TbaInquiryNoticeConfig> getTBAField(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_TBA_FIELD,
				"ProcessControlServiceImpl-->getTBAField()-->starts:");
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);

			List<com.wipro.fipc.entity.tba.TbaInquiryConfig> findByColumn = objTbaInquiryConfigContService1
					.findByColumn(columnName, columnValue);
			List<com.wipro.fipc.entity.tba.TbaInquiryConfig> tbaConfigList = findByColumn;
			List<TbaInquiryNoticeConfig> tbaInquiryNoticeList = new ArrayList<>();
			Iterator<com.wipro.fipc.entity.tba.TbaInquiryConfig> iteConfig = tbaConfigList.iterator();
			while (iteConfig.hasNext()) {
				tbaInquiryNoticeList.add(this.convertTbaInquiry(iteConfig.next()));
			}

			// Get Data from TBA inquiry notice table

			List<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> findByColumn2 = objTbaNoticeInqConfigContService
					.findByColumn(columnName, columnValue);
			String res = objectMapper.writeValueAsString(findByColumn2);

			List<TbaNoticeInqConfig> tbaNoticeList = Arrays
					.asList(new ObjectMapper().readValue(res, TbaNoticeInqConfig[].class));
			Iterator<TbaNoticeInqConfig> iteNotice = tbaNoticeList.iterator();
			while (iteNotice.hasNext()) {
				tbaInquiryNoticeList.add(this.convertTbaNoticeInquiry(iteNotice.next()));
			}

			// Get Data from TBA comment inquiry table

			List<com.wipro.fipc.entity.tba.TbaCommentInqConfig> findByColumn3 = objTbaCommentInqConfigContService
					.findByColumn(columnName, columnValue);
			res = objectMapper.writeValueAsString(findByColumn3);
			List<TbaCommentInqConfig> commentList = Arrays
					.asList(new ObjectMapper().readValue(res, TbaCommentInqConfig[].class));
			Iterator<TbaCommentInqConfig> iteComment = commentList.iterator();
			while (iteComment.hasNext()) {
				tbaInquiryNoticeList.add(this.convertTbaCommentInquiry(iteComment.next()));
			}

			List<com.wipro.fipc.entity.tba.EventInquiryConfig> findByColumn4 = objTbaEventInquiryConfigContService.findByColumn(columnName, columnValue);
			List<com.wipro.fipc.entity.tba.EventInquiryConfig> eventInquiryConfigList = findByColumn4;
			Iterator<com.wipro.fipc.entity.tba.EventInquiryConfig> iteratorEventInquiryConfig = eventInquiryConfigList.iterator();
			while (iteratorEventInquiryConfig.hasNext()) {
				tbaInquiryNoticeList.add(this.convertTbaEventInquiry(iteratorEventInquiryConfig.next()));
			}
			
			List<TbaPendingEvent> tbaPendingEventInquiryList = tbaPendingEventDao.findByColumn(TbaPendingEvent.class,
					HolmesAppConstants.TBA, HolmesAppConstants.PENDING_EVENT_INQ_CONFIG, columnName, columnValue);
			convertTbaPendingEventInquiry(tbaPendingEventInquiryList,tbaInquiryNoticeList);
			
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_TBA_FIELD,
					"ProcessControlServiceImpl-->getTBAField()--ends.");
			return tbaInquiryNoticeList;
		} catch (Exception e) {
			e.printStackTrace();
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_TBA_FIELD,
					"ProcessControlServiceImpl-->getTBAField()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	private void convertTbaPendingEventInquiry(List<TbaPendingEvent> tbaPendingEventInquiryList,
			List<TbaInquiryNoticeConfig> tbaInquiryNoticeList) {
		tbaPendingEventInquiryList.forEach(tbaPendingEvent -> {
			TbaInquiryNoticeConfig inqNoticeConfig = new TbaInquiryNoticeConfig();
			
			inqNoticeConfig.setActiveFlag(String.valueOf(tbaPendingEvent.getActiveFlag()));
			inqNoticeConfig.setCreatedBy(tbaPendingEvent.getCreatedBy());
			inqNoticeConfig.setCreatedDate(tbaPendingEvent.getCreatedDate());
			inqNoticeConfig.setId(tbaPendingEvent.getId());
			inqNoticeConfig.setInquiryName(tbaPendingEvent.getEventName());
			inqNoticeConfig.setInquiryDefName(tbaPendingEvent.getPendgEvntDefName());
			inqNoticeConfig.setJsonKey(tbaPendingEvent.getJsonKey());
			inqNoticeConfig.setMetadata(tbaPendingEvent.getMetaData());
			inqNoticeConfig.setParNm(tbaPendingEvent.getParNm());
			inqNoticeConfig.setSubJsonKey(tbaPendingEvent.getSubKey());
			if (StringUtils.hasText(tbaPendingEvent.getPanelId()))
				inqNoticeConfig.setPanelId(Integer.valueOf(tbaPendingEvent.getPanelId()));
			
			inqNoticeConfig.setTbaFieldName(tbaPendingEvent.getTbaFieldName());
			inqNoticeConfig.setUpdatedBy(tbaPendingEvent.getUpdatedBy());
			inqNoticeConfig.setUpdatedDate(tbaPendingEvent.getUpdatedDate());
			inqNoticeConfig.setSheetName("");
			inqNoticeConfig.setSheetNameWoutSpace("");
			inqNoticeConfig.setIdentifier("");
			tbaInquiryNoticeList.add(inqNoticeConfig);
		});

	}

	@Override
	public List<RulesConfig> getRuleName(String columnName, String columnValue, String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE,
				"ProcessControlServiceImpl-->getRuleName()-->starts:");
			List<RulesConfig> rulesConfigJson = new ArrayList<>();

			try {
				List<com.wipro.fipc.entity.filelayout.RulesConfig> findByColumn = objRulesConfigContService
						.findByColumn(columnName, columnValue);
				List<com.wipro.fipc.entity.filelayout.ValidationType> validationTypes = validationDao.findAll(
						com.wipro.fipc.entity.filelayout.ValidationType.class,LAYOUT_SCHEMA, RULES_VALIDATION_TYPE );
				List<com.wipro.fipc.entity.common.RulesDefinition> rulesDefinitions;
				for (com.wipro.fipc.entity.filelayout.RulesConfig entity : findByColumn) {
		        	RulesConfig config =new RulesConfig();
		        	ProcessJobMapping processJobMapping=new ProcessJobMapping();
		        	processJobMapping.setId(entity.getProcessJobMapping().getId());
		        	com.wipro.fipc.model.generated.RulesDefinition rulesDefinitionModel = new com.wipro.fipc.model.generated.RulesDefinition();
		        	 rulesDefinitions =new ArrayList<>();
		        	 rulesDefinitions=entity.getRulesDefinitions();
		        	 List<com.wipro.fipc.model.generated.RulesDefinition> rulesDefinitionModelList = new ArrayList<>();
		        	 Long validationid;
		        	  for(com.wipro.fipc.entity.common.RulesDefinition entityRuleDefinition : rulesDefinitions) {
		        		 validationid=entityRuleDefinition.getValidationType().getId();
		        			BeanUtils.copyProperties(entityRuleDefinition, rulesDefinitionModel);
		        			ValidationType validationType=new ValidationType();
				        	validationType.setId(validationid);
				        	rulesDefinitionModel.setValidationType(validationType);
				        	rulesDefinitionModelList.add(rulesDefinitionModel);
		        	  }
		        	
		        	BeanUtils.copyProperties(entity, config);
		        	
		      
		        	config.setProcessJobMapping(processJobMapping);
		        	config.setRulesDefinitions(rulesDefinitionModelList);
		        	rulesConfigJson.add(config);
		        }
			List<RulesConfig> newRulesConfigList = new ArrayList<>();
			for (RulesConfig ruleConfig : rulesConfigJson) {
				boolean validationMatches = false;
				List<RulesDefinition> rulesDefinitionList = ruleConfig.getRulesDefinitions();
				for (RulesDefinition rulesDefinition : rulesDefinitionList) {
					
					String request;
					if(!ArrayUtils.isEmpty(rulesDefinition.getConditionJson())) {
						 request=deserializeObject(rulesDefinition.getConditionJson());
						 rulesDefinition.setJson(request);
						 rulesDefinition.setJsonWoutName(request);
					}
					ValidationType validationType = rulesDefinition.getValidationType();
					
				com.wipro.fipc.entity.filelayout.ValidationType vt = validationTypes.stream().filter(e -> e.getId() == validationType.getId()).findFirst().orElse(null);
				String valTypeCode = "";
				if(vt != null)
					valTypeCode = vt.getValTypeCode();
				if (BUSINESS_RULE_CODE.equals(valTypeCode)) {
						validationMatches = true;
						break;
					}
				}
				if (validationMatches) {
					newRulesConfigList.add(ruleConfig);
				}
			}
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE,
					"ProcessControlServiceImpl-->getRuleName()--ends.");
			return newRulesConfigList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_RULE,
					"ProcessControlServiceImpl-->getRuleName()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	@Override
	public List<ProcessControl> getProcessControlData(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
				"ProcessControlServiceImpl-->getProcessControlData()-->starts:");
		try {
			List<ProcessControl> pcList = new ArrayList<>();

			List<com.wipro.fipc.entity.filelayout.ProcessControlConfig> findByColumn = objProcessControlConfigContService
					.findByColumn(columnName, columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String res = objectMapper.writeValueAsString(findByColumn);
			ProcessControlConfig[] pcConfigList = new ObjectMapper().readValue(res, ProcessControlConfig[].class);
			for (ProcessControlConfig pcConfig : pcConfigList) {
				ProcessControl pc = pcConfigToPcConversion.apply(pcConfig);
				pcList.add(pc);
			}
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
					"ProcessControlServiceImpl-->getProcessControlData()--ends.");
			return pcList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
					"ProcessControlServiceImpl-->getProcessControlData()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	@Override
	public Object getLayoutConfig(String columnName, String columnValue, String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_LAYOUT_CONFIG,
				"ProcessControlServiceImpl-->getLayoutConfig()-->starts:");
		if (searchField.length() > 5 && searchField.substring(0, 5).equals("Prev-")) {
			searchField = searchField.substring(5);
		}
		String fileName = searchField;
		searchField = searchField.replace("+", "PLUS");
		searchField = searchField.replace("%", "PRCNT");
		searchField = searchField.replace("&", "AMPNT");
		searchField = searchField.replace("'", "''");
		try {
			if (columnName.equals("client_id")) {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_LAYOUT_CONFIG,
						"--Fetching the external Report data---");
				return getLayoutConfigIfPart(columnName, columnValue, searchField);
			} else {
				return getLayoutConfigElsePart(columnName, columnValue, searchField, fileName);
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_LAYOUT_CONFIG,
					"ProcessControlServiceImpl-->getLayoutConfig()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	public List<LayoutConfig> getLayoutConfigElsePart(String columnName, String columnValue, String searchField,
			String fileName) throws IOException {

		fileName = fileName.replace("'", "''");

		String res = "";

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

		List<String> columnName1 = new ArrayList<>();
		columnName1.add(columnName);
		columnName1.add(HolmesAppConstants.FILE_NAMES);
		columnName1.add(HolmesAppConstants.ACTIVE_FLAG);
		columnName1.add(HolmesAppConstants.MF_FIELD_NAME);
		columnName1.add(HolmesAppConstants.RECORD_TYPE);

		List<String> columnCondition1 = new ArrayList<>();
		columnCondition1.add(HolmesAppConstants.EQUAL);
		columnCondition1.add(HolmesAppConstants.EQUAL);
		columnCondition1.add(HolmesAppConstants.EQUAL);
		columnCondition1.add(HolmesAppConstants.NOT_LIKE);
		columnCondition1.add(HolmesAppConstants.EQUAL);

		List<String> columnValue1 = new ArrayList<>();
		columnValue1.add(columnValue);
		columnValue1.add(searchField);
		columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);
		columnValue1.add(HolmesAppConstants.MF_FIELD_NAME_VALUE);
		columnValue1.add("Detail Record");
		List<com.wipro.fipc.entity.layoutrule.LayoutConfig> layoutDBResponse1 = objLayoutConfigController
				.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);

		res = objectMapper.writeValueAsString(layoutDBResponse1);
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE, "response formed: " + res);

		if (res.equals("[]")) {

			List<String> columnName2 = new ArrayList<>();
			columnName2.add(columnName);
			columnName2.add(HolmesAppConstants.FILE_NAMES);
			columnName2.add(HolmesAppConstants.ACTIVE_FLAG);

			List<String> columnCondition2 = new ArrayList<>();
			columnCondition2.add(HolmesAppConstants.EQUAL);
			columnCondition2.add(HolmesAppConstants.EQUAL);
			columnCondition2.add(HolmesAppConstants.EQUAL);

			List<String> columnValue2 = new ArrayList<>();
			columnValue2.add(columnValue);
			columnValue2.add(fileName);
			columnValue2.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);

			List<KsdOutPutFileDetails> ksdDBResponseNew = objKsdFileDetailsController
					.findByMultiColumnConditionNew(columnName2, columnCondition2, columnValue2);
			res = objectMapper.writeValueAsString(ksdDBResponseNew);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE, "response2 formed: " + res);
			List<LayoutConfig> layoutConfigList = new ArrayList<>();
			JsonParser parser = new JsonParser();
			JsonArray ksdRes = parser.parse(res).getAsJsonArray();
			String sheetName = null;
			for (int i = 0; i < ksdRes.size(); i++) {
				JsonObject obj = ksdRes.get(i).getAsJsonObject();

				sheetName = getLayoutConfigElsePart1(obj);
				layoutConfigList = getLayoutConfigElsePart2(sheetName, obj, layoutConfigList);
			}
			return layoutConfigList;
		} else {
			List<LayoutConfig> layoutConfigList = Arrays
					.asList(new ObjectMapper().readValue(res, LayoutConfig[].class));
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_LAYOUT_CONFIG,
					"ProcessControlServiceImpl-->getLayoutConfig()--ends.");
			return layoutConfigList;
		}

	}

	private String getLayoutConfigElsePart1(JsonObject obj) {
		String sheetName = null;
		if (obj.has(HolmesAppConstants.SHEET_NAME_CONSTANT)) {
			if (!obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT).isJsonNull()) {
				sheetName = obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT).getAsString() == null ? ""
						: obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT).getAsString();
			} else {
				sheetName = "";
			}
		} else {
			sheetName = "";
		}
		return sheetName;
	}

	public List<LayoutConfig> getLayoutConfigElsePart2(String sheetName, JsonObject obj,
			List<LayoutConfig> layoutConfigList) {
		String sheetNameWoutSpace = null;
		LayoutConfig layoutConfig = null;

		if (obj.has(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT)) {
			if (!obj.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT).isJsonNull()) {
				sheetNameWoutSpace = obj.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT).getAsString() == null
						? ""
						: obj.get(HolmesAppConstants.SHEET_NAME_WOUT_SPACE_CONSTANT).getAsString();
			} else {
				sheetNameWoutSpace = "";
			}
		} else {
			sheetNameWoutSpace = "";
		}
		if (obj.has("outputReports")) {
			JsonArray reportList = obj.get("outputReports").getAsJsonArray();
			for (int j = 0; j < reportList.size(); j++) {
				JsonObject output = reportList.get(j).getAsJsonObject();
				layoutConfig = new LayoutConfig();
				layoutConfig.setMfFieldName(output.get(HolmesAppConstants.DATA_ELEMENT).getAsString());
				layoutConfig.setMfFieldWoutSpace(output.get("dataElementWoutSpace").getAsString());
				layoutConfig.setRecordIdentifier(output.get("recordIdentifier").getAsString());
				layoutConfig.setSheetName(sheetName);
				layoutConfig.setSheetNameWoutSpace(sheetNameWoutSpace);
				layoutConfigList.add(layoutConfig);
			}
		} else {
			if ((sheetName != null && !sheetName.equalsIgnoreCase(""))
					|| (sheetNameWoutSpace != null && !sheetNameWoutSpace.equalsIgnoreCase(""))) {
				layoutConfig = new LayoutConfig();
				layoutConfig.setMfFieldName("");
				layoutConfig.setMfFieldWoutSpace("");
				layoutConfig.setRecordIdentifier("");
				layoutConfig.setSheetName(sheetName);
				layoutConfig.setSheetNameWoutSpace(sheetNameWoutSpace);
				layoutConfigList.add(layoutConfig);
			}
		}
		return layoutConfigList;

	}

	public List<LayoutConfig> getLayoutConfigIfPart(String columnName, String columnValue, String searchField) {

		String res = "";
		try {
			List<TemplateReportLayOut> datas = objTemplateReportLayOutContService.getDatas(columnValue, searchField);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(datas);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_LAYOUT_CONFIG, "data is:" + res);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getLayoutConfigIfPart", "Exception Occurred - ", e);
		}
		List<LayoutConfig> layoutConfigList = new ArrayList<>();
		LayoutConfig layoutConfig = null;
		JsonParser parser = new JsonParser();
		JsonArray templateLayoutList = parser.parse(res).getAsJsonArray();
		for (int i = 0; i < templateLayoutList.size(); i++) {

			layoutConfig = new LayoutConfig();
			JsonObject obj = templateLayoutList.get(i).getAsJsonObject();
			if (obj.has(HolmesAppConstants.DATA_ELEMENT)) {
				layoutConfig.setMfFieldName(obj.get(HolmesAppConstants.DATA_ELEMENT).getAsString());
			} else {
				layoutConfig.setMfFieldName("");
			}
			layoutConfig = getLayoutConfigIfPartCondition(obj, layoutConfig);
			layoutConfigList.add(layoutConfig);
		}
		return layoutConfigList;
	}

	public LayoutConfig getLayoutConfigIfPartCondition(JsonObject obj, LayoutConfig layoutConfig) {
		if (obj.has(HolmesAppConstants.DATA_ELEMENT_WS) && !obj.get(HolmesAppConstants.DATA_ELEMENT_WS).isJsonNull()) {
			layoutConfig.setMfFieldWoutSpace(obj.get(HolmesAppConstants.DATA_ELEMENT_WS).getAsString());
		} else {
			layoutConfig.setMfFieldWoutSpace("");
		}
		if (obj.has(HolmesAppConstants.TEMPLATE_REPORT_NAME_WS_CONSTANT)
				&& !obj.get(HolmesAppConstants.TEMPLATE_REPORT_NAME_WS_CONSTANT).isJsonNull()) {
			layoutConfig
					.setFileNameWoutSpace(obj.get(HolmesAppConstants.TEMPLATE_REPORT_NAME_WS_CONSTANT).getAsString());
		} else {
			layoutConfig.setFileNameWoutSpace("");
		}
		if (obj.has("templateReportName")) {
			layoutConfig.setFileName(obj.get("templateReportName").getAsString());
		} else {
			layoutConfig.setFileName("");
		}
		if (obj.has(HolmesAppConstants.SHEET_NAME_CONSTANT)) {
			layoutConfig.setSheetName(obj.get(HolmesAppConstants.SHEET_NAME_CONSTANT).getAsString());
		} else {
			layoutConfig.setSheetName("");
		}
		if (obj.has(HolmesAppConstants.SHEET_NAME_WS_CONSTANT)
				&& !obj.get(HolmesAppConstants.SHEET_NAME_WS_CONSTANT).isJsonNull()) {
			layoutConfig.setSheetNameWoutSpace(obj.get(HolmesAppConstants.SHEET_NAME_WS_CONSTANT).getAsString());
		} else {
			layoutConfig.setSheetNameWoutSpace("");
		}
		if (obj.has("identifier")) {
			layoutConfig.setRecordIdentifier(obj.get("identifier").getAsString());
		} else {
			layoutConfig.setRecordIdentifier("");
		}
		return layoutConfig;
	}

	@Override
	public Object submit(String action, List<ProcessControl> pcList, String appName, String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
				"ProcessControlServiceImpl-->submit()-->starts:");
		try {
			List<ProcessControlConfig> newPcConfigList = new ArrayList<>();
			List<ProcessControlConfig> errorPcConfigList = new ArrayList<>();
			ProcessControl pcDbdata = null;
			String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
			Set<ProcessControl> pcConfigSet = new HashSet<>(pcList);
			Iterator<ProcessControl> ite = pcConfigSet.iterator();
			while (ite.hasNext()) {
				ProcessControl pc = ite.next();
				ProcessControlConfig pcConfig = pcToConfigConversion.apply(pc);

				if (action.equals("Modify") || action.equals("Delete")) {

					pcDbdata = getProcessControlData("id", pcConfig.getId().toString()).get(0);

				}

				if ((action.equals("Save")) || (action.equals("Modify"))) {
					pcConfig.setActiveFlag("T");
				} else if (action.equals("Delete")) {
					pcConfig.setActiveFlag("F");
				}

				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
						"active flag updated in pcConfig");

				if (pcConfig.getActiveFlag().equals("T")) {

					List<String> columnName2 = new ArrayList<>();
					columnName2.add("application");
					columnName2.add("field_name");
					columnName2.add("rule_name");
					columnName2.add("corrective_action");
					columnName2.add("process_job_mapping_id");
					columnName2.add(HolmesAppConstants.ACTIVE_FLAG);

					List<String> columnCondition2 = new ArrayList<>();
					columnCondition2.add(HolmesAppConstants.EQUAL);
					columnCondition2.add(HolmesAppConstants.EQUAL);
					columnCondition2.add(HolmesAppConstants.EQUAL);
					columnCondition2.add(HolmesAppConstants.EQUAL);
					columnCondition2.add(HolmesAppConstants.EQUAL);
					columnCondition2.add(HolmesAppConstants.EQUAL);

					List<String> columnValue2 = new ArrayList<>();
					columnValue2.add(pcConfig.getApplication());
					columnValue2.add(pcConfig.getFieldName());
					columnValue2.add(pcConfig.getRuleName().replaceAll("'", "''"));
					columnValue2.add(pcConfig.getCorrectiveAction());
					columnValue2.add(String.valueOf(pcConfig.getProcessJobMapping().getId()));
					columnValue2.add(pcConfig.getActiveFlag());

					List<com.wipro.fipc.entity.filelayout.ProcessControlConfig> findByMultiColumnCondition = objProcessControlConfigContService
							.findByMultiColumnCondition(columnName2, columnCondition2, columnValue2);
					ObjectMapper objectMapper = new ObjectMapper();
					objectMapper.configure(
							com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
					String response = objectMapper.writeValueAsString(findByMultiColumnCondition);
					ProcessControlConfig[] pcConfigArr = new ObjectMapper().readValue(response,
							ProcessControlConfig[].class); 
					if (pcConfigArr.length > 0 && ArrayUtils.isEmpty(pcConfigArr[0].getByteaActions()) &&  pcConfigArr[0].getActions().equals(pcConfig.getActions())) {	
						errorPcConfigList.add(pcConfig);
					} else if(pcConfigArr.length > 0 && !ArrayUtils.isEmpty(pcConfigArr[0].getByteaActions()) && deserializeObject(pcConfigArr[0].getByteaActions()).equals(deserializeObject(pcConfig.getByteaActions()))) {
						errorPcConfigList.add(pcConfig);
					}else {
						if (pcConfig.getId() != null) {
							pcConfig.setCreatedDate(pcDbdata.getCreatedDate());
							pcConfig.setCreatedBy(pcDbdata.getCreatedBy());
							pcConfig.setUpdatedDate(new Date());
							pcConfig.setUpdatedBy(adID);
							LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
									"pcConfig inside modify loop");
							LoggerUtil.log(this.getClass(), Level.INFO, "submit() -> Process_Control ",
									PJM_ID + pc.getProcessJobMapping().getId() + ADID2 + adID + " action = MODIFY");
						} else {
							pcConfig.setCreatedDate(new Date());
							pcConfig.setCreatedBy(adID);
							pcConfig.setUpdatedDate(new Date());
							pcConfig.setUpdatedBy(adID);
							LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
									"pcConfig inside save loop");
							LoggerUtil.log(this.getClass(), Level.INFO, "submit() -> Process_Control ",
									PJM_ID + pc.getProcessJobMapping().getId() + ADID2 + adID + " action = CREATE");
						}
						newPcConfigList.add(pcConfig);
					}
				} else {
					pcConfig.setCreatedDate(pcDbdata.getCreatedDate());
					pcConfig.setCreatedBy(pcDbdata.getCreatedBy());
					pcConfig.setUpdatedDate(new Date());
					pcConfig.setUpdatedBy(adID);
					LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
							"pcConfig inside delete loop");
					LoggerUtil.log(this.getClass(), Level.INFO, "submit() -> Process_Control ",
							PJM_ID + pc.getProcessJobMapping().getId() + ADID2 + adID + " action = DELETE");
					newPcConfigList.add(pcConfig);
				}
			}
			return submitSubMethod(newPcConfigList, errorPcConfigList ,action);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.SUBMIT,
					"ProcessControlServiceImpl-->submit()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	public ProcessControlResponse submitSubMethod(List<ProcessControlConfig> newPcConfigList,
			List<ProcessControlConfig> errorPcConfigList , String action) {
		ProcessControlResponse pcResponse = new ProcessControlResponse();
		if (!newPcConfigList.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT, "----Updating DB-----");
			String getBody = "";
			List<com.wipro.fipc.entity.filelayout.ProcessControlConfig> objnew = new ArrayList<>();
			try {
				ObjectMapper objectMapper = new ObjectMapper();
				objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
						false);
				for (ProcessControlConfig obj : newPcConfigList) {
					String obj1 = objectMapper.writeValueAsString(obj);
					com.wipro.fipc.entity.filelayout.ProcessControlConfig obj2 = new ObjectMapper().readValue(obj1,
							com.wipro.fipc.entity.filelayout.ProcessControlConfig.class);
					objnew.add(obj2);
				}
				boolean saveAllEntities = objProcessControlConfigContService.saveAllEntities(objnew);
				getBody = objectMapper.writeValueAsString(saveAllEntities);
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "submitSubMethod", "Exception Occurred - ", e);
			}
			if (getBody != null && getBody.equals("false")) {
				throw new BusinessException(HolmesAppConstants.FAILED);
			}
			pcResponse.setMessage(HolmesAppConstants.SUCCESS);
			if (!errorPcConfigList.isEmpty()) {
				pcResponse.setErrorList(errorPcConfigList);
			}
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
					"ProcessControlServiceImpl-->submit()--ends.");
			return pcResponse;
		} else {
			 if(action.equals("Modify")) {
				 pcResponse.setMessage(HolmesAppConstants.SUCCESS); 
			 }else {
				 pcResponse.setMessage("All Records are Duplicate");
			 }
			
			if (!errorPcConfigList.isEmpty()) {
				pcResponse.setErrorList(errorPcConfigList);
			}
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
					"ProcessControlServiceImpl-->submit()--ends.");
			return pcResponse;
		}
	}

	@Override
	public List<TbaUpdateConfig> getCorrectiveActionData(String columnName, String columnValue, String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
				"ProcessControlServiceImpl-->getCorrectiveActionData()-->starts:");
		try {

			List<com.wipro.fipc.entity.tba.TbaUpdateConfig> findByColumn = objTbaUpdateConfigContService
					.findByColumn(columnName, columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String res = objectMapper.writeValueAsString(findByColumn);

			List<TbaUpdateConfig> tbaUpdateConfigList = Arrays
					.asList(new ObjectMapper().readValue(res, TbaUpdateConfig[].class));
			if (searchField.equals("TBA Update")) {
				tbaUpdateConfigList = tbaUpdateConfigList.stream().filter(obj -> obj.getRerunFlag().equals("N"))
						.collect(Collectors.toList());
			} else {
				tbaUpdateConfigList = tbaUpdateConfigList.stream().filter(obj -> obj.getRerunFlag().equals("Y"))
						.collect(Collectors.toList());
			}
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
					"ProcessControlServiceImpl-->getCorrectiveActionData()--ends.");
			return tbaUpdateConfigList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
					"ProcessControlServiceImpl-->getCorrectiveActionData()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	private Function<ProcessControl, ProcessControlConfig> pcToConfigConversion = (ProcessControl pc) -> {
		ProcessControlConfig pcConfig = new ProcessControlConfig();
		if (pc.getActions() != null) {
			pcConfig.setActions(null);
		}
		if (pc.getActions() != null) {
			pcConfig.setByteaActions(gson.toJson(pc.getActions()).toString().getBytes(StandardCharsets.UTF_8));
		}
		if (pc.getActiveFlag() != null) {
			pcConfig.setActiveFlag(pc.getActiveFlag());
		}
		if (pc.getApplication() != null) {
			pcConfig.setApplication(pc.getApplication());
		}
		if (pc.getApplicationWoutSpace() != null) {
			pcConfig.setApplicationWoutSpace(pc.getApplicationWoutSpace());
		}
		if (pc.getCorrectiveAction() != null) {
			pcConfig.setCorrectiveAction(pc.getCorrectiveAction());
		}
		if (pc.getCreatedBy() != null) {
			pcConfig.setCreatedBy(pc.getCreatedBy());
		}
		if (pc.getCreatedDate() != null) {
			pcConfig.setCreatedDate(pc.getCreatedDate());
		}
		if (pc.getFieldName() != null) {
			pcConfig.setFieldName(pc.getFieldName());
		}
		if (pc.getFieldNameWoutSpace() != null) {
			pcConfig.setFieldNameWoutSpace(pc.getFieldNameWoutSpace());
		}
		if (pc.getId() != null) {
			pcConfig.setId(pc.getId());
		}
		if (pc.getIdentifier() != null) {
			pcConfig.setIdentifier(pc.getIdentifier());
		}
		if (pc.getProcessJobMapping() != null) {
			pcConfig.setProcessJobMapping(pc.getProcessJobMapping());
		}
		if (pc.getRuleName() != null) {
			pcConfig.setRuleName(pc.getRuleName());
		}
		if (pc.getUpdatedBy() != null) {
			pcConfig.setUpdatedBy(pc.getUpdatedBy());
		}
		return pcConfig;
	};

	private Function<ProcessControlConfig, ProcessControl> pcConfigToPcConversion = (ProcessControlConfig pcConfig) -> {
		ProcessControl pc = new ProcessControl();
		if(!ArrayUtils.isEmpty(pcConfig.getByteaActions())) {
            pc.setActions(gson.fromJson(deserializeObject(pcConfig.getByteaActions()), Object.class));
            
		}else if(pcConfig.getActions() != null) {
			pc.setActions(gson.fromJson(pcConfig.getActions(), Object.class));
		}
		if (pcConfig.getActiveFlag() != null) {
			pc.setActiveFlag(pcConfig.getActiveFlag());
		}
		if (pcConfig.getApplication() != null) {
			pc.setApplication(pcConfig.getApplication());
		}
		if (pcConfig.getApplicationWoutSpace() != null) {
			pc.setApplicationWoutSpace(pcConfig.getApplicationWoutSpace());
		}
		if (pcConfig.getCorrectiveAction() != null) {
			pc.setCorrectiveAction(pcConfig.getCorrectiveAction());
		}
		if (pcConfig.getCreatedBy() != null) {
			pc.setCreatedBy(pcConfig.getCreatedBy());
		}
		if (pcConfig.getCreatedDate() != null) {
			pc.setCreatedDate(pcConfig.getCreatedDate());
		}
		if (pcConfig.getFieldName() != null) {
			pc.setFieldName(pcConfig.getFieldName());
		}
		if (pcConfig.getFieldNameWoutSpace() != null) {
			pc.setFieldNameWoutSpace(pcConfig.getFieldNameWoutSpace());
		}
		if (pcConfig.getId() != null) {
			pc.setId(pcConfig.getId());
		}
		if (pcConfig.getIdentifier() != null) {
			pc.setIdentifier(pcConfig.getIdentifier());
		}
		if (pcConfig.getProcessJobMapping() != null) {
			pc.setProcessJobMapping(pcConfig.getProcessJobMapping());
		}
		if (pcConfig.getRuleName() != null) {
			pc.setRuleName(pcConfig.getRuleName());
		}
		if (pcConfig.getUpdatedBy() != null) {
			pc.setUpdatedBy(pcConfig.getUpdatedBy());
		}
		if (pcConfig.getUpdatedDate() != null) {
			pc.setUpdatedDate(pcConfig.getUpdatedDate());
		}
		return pc;
	};

	private TbaInquiryNoticeConfig convertTbaInquiry(com.wipro.fipc.entity.tba.TbaInquiryConfig inqConfig) {
		TbaInquiryNoticeConfig inqNoticeConfig = new TbaInquiryNoticeConfig();
		if (inqConfig.getActiveFlag() !=null) {
			inqNoticeConfig.setActiveFlag(inqConfig.getActiveFlag() + "");
		}
		if (inqConfig.getCreatedBy() != null) {
			inqNoticeConfig.setCreatedBy(inqConfig.getCreatedBy());
		}
		if (inqConfig.getCreatedDate() != null) {
			inqNoticeConfig.setCreatedDate(inqConfig.getCreatedDate());
		}
		if (inqConfig.getFieldType() != null) {
			inqNoticeConfig.setFieldType(inqConfig.getFieldType());
		}
		if (inqConfig.getId() != null) {
			inqNoticeConfig.setId(inqConfig.getId());
		}
		if (inqConfig.getIdentifier() != null) {
			inqNoticeConfig.setIdentifier(inqConfig.getIdentifier());
		}
		if (inqConfig.getInquiryDefName() != null) {
			inqNoticeConfig.setInquiryDefName(inqConfig.getInquiryDefName());
		}
		if (inqConfig.getInquiryName() != null) {
			inqNoticeConfig.setInquiryName(inqConfig.getInquiryName());
		}
		if (inqConfig.getJsonKey() != null) {
			inqNoticeConfig.setJsonKey(inqConfig.getJsonKey());
		}
		if (inqConfig.getMetaData() != null) {
			inqNoticeConfig.setMetadata(inqConfig.getMetaData());
		}
		inqNoticeConfig = convertTbaInquirySubPart(inqConfig, inqNoticeConfig);
		inqNoticeConfig.setSheetName("");
		inqNoticeConfig.setSheetNameWoutSpace("");
		return inqNoticeConfig;
	}

	private TbaInquiryNoticeConfig convertTbaEventInquiry(com.wipro.fipc.entity.tba.EventInquiryConfig eventInquiryConfig) {
		TbaInquiryNoticeConfig inqNoticeConfig = new TbaInquiryNoticeConfig();
		if (eventInquiryConfig.getActiveFlag() !=null) {
			inqNoticeConfig.setActiveFlag(eventInquiryConfig.getActiveFlag() + "");
		}
		if (eventInquiryConfig.getCreatedBy() != null) {
			inqNoticeConfig.setCreatedBy(eventInquiryConfig.getCreatedBy());
		}
		if (eventInquiryConfig.getCreatedDate() != null) {
			inqNoticeConfig.setCreatedDate(eventInquiryConfig.getCreatedDate());
		}
		if (eventInquiryConfig.getId() != null) {
			inqNoticeConfig.setId(eventInquiryConfig.getId());
		}
		if (eventInquiryConfig.getEventInquiryDefName() != null) {
			inqNoticeConfig.setInquiryDefName(eventInquiryConfig.getEventInquiryDefName());
		}
		if (eventInquiryConfig.getEventName() != null) {
			inqNoticeConfig.setInquiryName(eventInquiryConfig.getEventName());
		}
		if (eventInquiryConfig.getJsonKey() != null) {
			inqNoticeConfig.setJsonKey(eventInquiryConfig.getJsonKey());
		}
		if (eventInquiryConfig.getMetadata() != null) {
			inqNoticeConfig.setMetadata(eventInquiryConfig.getMetadata());
		}
		if (eventInquiryConfig.getFieldType() != null) {
			inqNoticeConfig.setFieldType(eventInquiryConfig.getFieldType());
		}
		inqNoticeConfig = convertTbaEventInquirySubPart(eventInquiryConfig, inqNoticeConfig);
		inqNoticeConfig.setSheetName("");
		inqNoticeConfig.setSheetNameWoutSpace("");
		return inqNoticeConfig;
	}
	
	private TbaInquiryNoticeConfig convertTbaInquirySubPart(com.wipro.fipc.entity.tba.TbaInquiryConfig inqConfig,
			TbaInquiryNoticeConfig inqNoticeConfig) {
		if (inqConfig.getPanelId() != 0) {
			inqNoticeConfig.setPanelId(inqConfig.getPanelId());
		}
		if (inqConfig.getParNM() != null) {
			inqNoticeConfig.setParNm(inqConfig.getParNM());
		}
		if (inqConfig.getProcessJobMapping() != null) {
			inqNoticeConfig.setProcessJobMapping(inqConfig.getProcessJobMapping());
		}
		if (inqConfig.getRecordIdentifier() != null) {
			inqNoticeConfig.setRecordIdentifier(inqConfig.getRecordIdentifier());
		}
		if (inqConfig.getSubJsonKey() != null) {
			inqNoticeConfig.setSubJsonKey(inqConfig.getSubJsonKey());
		}
		if (inqConfig.getTbaFieldName() != null) {
			inqNoticeConfig.setTbaFieldName(inqConfig.getTbaFieldName());
		}
		if (inqConfig.getUpdatedBy() != null) {
			inqNoticeConfig.setUpdatedBy(inqConfig.getUpdatedBy());
		}
		if (inqConfig.getUpdatedDate() != null) {
			inqNoticeConfig.setUpdatedDate(inqConfig.getUpdatedDate());
		}
		return inqNoticeConfig;
	}

	private TbaInquiryNoticeConfig convertTbaEventInquirySubPart(com.wipro.fipc.entity.tba.EventInquiryConfig eventInquiryConfig,
			TbaInquiryNoticeConfig inqNoticeConfig) {
		if (eventInquiryConfig.getPanelId() != 0) {
			inqNoticeConfig.setPanelId(eventInquiryConfig.getPanelId());
		}
		if (eventInquiryConfig.getParNm() != null) {
			inqNoticeConfig.setParNm(eventInquiryConfig.getParNm());
		}
		if (eventInquiryConfig.getProcessJobMapping() != null) {
			inqNoticeConfig.setProcessJobMapping(eventInquiryConfig.getProcessJobMapping());
		}
		if (eventInquiryConfig.getRecordIdentifier() != null) {
			inqNoticeConfig.setRecordIdentifier(eventInquiryConfig.getRecordIdentifier());
		}
		if (eventInquiryConfig.getTbaFieldName() != null) {
			inqNoticeConfig.setTbaFieldName(eventInquiryConfig.getTbaFieldName());
		}
		if (eventInquiryConfig.getUpdatedBy() != null) {
			inqNoticeConfig.setUpdatedBy(eventInquiryConfig.getUpdatedBy());
		}
		if (eventInquiryConfig.getUpdatedDate() != null) {
			inqNoticeConfig.setUpdatedDate(eventInquiryConfig.getUpdatedDate());
		}
		return inqNoticeConfig;
	}
	
	private TbaInquiryNoticeConfig convertTbaNoticeInquiry(TbaNoticeInqConfig noticeConfig) {
		TbaInquiryNoticeConfig inqNoticeConfig = new TbaInquiryNoticeConfig();
		if (noticeConfig.getActiveFlag() != null) {
			inqNoticeConfig.setActiveFlag(noticeConfig.getActiveFlag());
		}
		if (noticeConfig.getClientId() != null) {
			inqNoticeConfig.setClientId(noticeConfig.getClientId());
		}
		if (noticeConfig.getCreatedBy() != null) {
			inqNoticeConfig.setCreatedBy(noticeConfig.getCreatedBy());
		}
		if (noticeConfig.getCreatedDate() != null) {
			inqNoticeConfig.setCreatedDate(noticeConfig.getCreatedDate());
		}
		if (noticeConfig.getFieldType() != null) {
			inqNoticeConfig.setFieldType(noticeConfig.getFieldType());
		}
		if (noticeConfig.getId() != null) {
			inqNoticeConfig.setId(noticeConfig.getId());
		}
		if (noticeConfig.getIdentifier() != null) {
			inqNoticeConfig.setIdentifier(noticeConfig.getIdentifier());
		} else {
			inqNoticeConfig.setIdentifier("");
		}
		if (noticeConfig.getInquiryDefName() != null) {
			inqNoticeConfig.setInquiryDefName(noticeConfig.getInquiryDefName());
		}
		if (noticeConfig.getJsonKey() != null) {
			inqNoticeConfig.setJsonKey(noticeConfig.getJsonKey());
		}
		if (noticeConfig.getMetadata() != null) {
			inqNoticeConfig.setMetadata(noticeConfig.getMetadata());
		}
		inqNoticeConfig = convertTbaNoticeInquirySubPart(noticeConfig, inqNoticeConfig);
		inqNoticeConfig.setSheetName("");
		inqNoticeConfig.setSheetNameWoutSpace("");
		return inqNoticeConfig;
	}

	private TbaInquiryNoticeConfig convertTbaNoticeInquirySubPart(TbaNoticeInqConfig noticeConfig,
			TbaInquiryNoticeConfig inqNoticeConfig) {
		if (noticeConfig.getNoticeId() != null) {
			inqNoticeConfig.setNoticeId(noticeConfig.getNoticeId());
		}
		if (noticeConfig.getParNm() != null) {
			inqNoticeConfig.setParNm(noticeConfig.getParNm());
		}
		if (noticeConfig.getProcessJobMappingId() != null) {
			inqNoticeConfig.setProcessJobMappingId(noticeConfig.getProcessJobMappingId());
		}
		if (noticeConfig.getRecordIdentifier() != null) {
			inqNoticeConfig.setRecordIdentifier(noticeConfig.getRecordIdentifier());
		}
		if (noticeConfig.getSubJsonKey() != null) {
			inqNoticeConfig.setSubJsonKey(noticeConfig.getSubJsonKey());
		}
		if (noticeConfig.getTbaFieldName() != null) {
			inqNoticeConfig.setTbaFieldName(noticeConfig.getTbaFieldName());
		}
		if (noticeConfig.getUpdatedBy() != null) {
			inqNoticeConfig.setUpdatedBy(noticeConfig.getUpdatedBy());
		}
		if (noticeConfig.getUpdatedDate() != null) {
			inqNoticeConfig.setUpdatedDate(noticeConfig.getUpdatedDate());
		}
		return inqNoticeConfig;
	}

	private TbaInquiryNoticeConfig convertTbaCommentInquiry(TbaCommentInqConfig commentConfig) {
		TbaInquiryNoticeConfig inqNoticeConfig = new TbaInquiryNoticeConfig();
		if (commentConfig.getActiveFlag() != null) {
			inqNoticeConfig.setActiveFlag(commentConfig.getActiveFlag());
		}
		if (commentConfig.getCreatedBy() != null) {
			inqNoticeConfig.setCreatedBy(commentConfig.getCreatedBy());
		}
		if (commentConfig.getCreatedDate() != null) {
			inqNoticeConfig.setCreatedDate(commentConfig.getCreatedDate());
		}
		if (commentConfig.getFieldType() != null) {
			inqNoticeConfig.setFieldType(commentConfig.getFieldType());
		}
		if (commentConfig.getId() != null) {
			inqNoticeConfig.setId(commentConfig.getId());
		}
		if (commentConfig.getInquiryDefName() != null) {
			inqNoticeConfig.setInquiryDefName(commentConfig.getInquiryDefName());
		}
		if (commentConfig.getJsonKey() != null) {
			inqNoticeConfig.setJsonKey(commentConfig.getJsonKey());
		}
		if (commentConfig.getSubJsonKey() != null) {
			inqNoticeConfig.setSubJsonKey(commentConfig.getSubJsonKey());
		}
		if (commentConfig.getTbaFieldName() != null) {
			inqNoticeConfig.setTbaFieldName(commentConfig.getTbaFieldName());
		}
		if (commentConfig.getUpdatedBy() != null) {
			inqNoticeConfig.setUpdatedBy(commentConfig.getUpdatedBy());
		}
		if (commentConfig.getUpdatedDate() != null) {
			inqNoticeConfig.setUpdatedDate(commentConfig.getUpdatedDate());
		}
		inqNoticeConfig.setSheetName("");
		inqNoticeConfig.setSheetNameWoutSpace("");
		inqNoticeConfig.setIdentifier("");
		return inqNoticeConfig;
	}

	@Override
	public List<String> getOutputReport(String columnName, String columnValue, String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_OUTPUT_REPORT,
				"ProcessControlServiceImpl-->getOutputReport()-->starts:");
		try {
			List<String> fieldList;
			searchField = searchField.replace("+", "PLUS");
			searchField = searchField.replace("%", "PRCNT");
			searchField = searchField.replace("&", "AMPNT");
			searchField = searchField.replace("#", "HASH");

			List<String> columnName1 = new ArrayList<>();
			columnName1.add(columnName);
			columnName1.add(HolmesAppConstants.FILE_NAMES);
			columnName1.add(HolmesAppConstants.ACTIVE_FLAG);

			List<String> columnCondition1 = new ArrayList<>();
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);
			columnCondition1.add(HolmesAppConstants.EQUAL);

			List<String> columnValue1 = new ArrayList<>();
			columnValue1.add(columnValue);
			columnValue1.add(searchField);
			columnValue1.add(HolmesAppConstants.ACTIVE_FLAG_VALUE);

			List<com.wipro.fipc.entity.filelayout.OutputReport> findByMultiColumnCondition = objOutputReportContrService1
					.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);

			String res = objectMapper.writeValueAsString(findByMultiColumnCondition);

			if (res != null && res.equals("[]")) {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_OUTPUT_REPORT,
						"Fetching Data from LayoutConfig");

				List<com.wipro.fipc.entity.layoutrule.LayoutConfig> findByMultiColumnCondition2 = objLayoutConfigController
						.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
				res = objectMapper.writeValueAsString(findByMultiColumnCondition2);

				List<LayoutConfig> layoutConfigList = Arrays
						.asList(new ObjectMapper().readValue(res, LayoutConfig[].class));
				fieldList = layoutConfigList.stream().map(obj -> obj.getMfFieldWoutSpace())
						.collect(Collectors.toList());
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_OUTPUT_REPORT,
						"ProcessControlServiceImpl-->getOutputReport()-->ends");
				return fieldList;
			} else {
				List<OutputReport> reportList = Arrays.asList(new ObjectMapper().readValue(res, OutputReport[].class));
				fieldList = reportList.stream().map(obj -> obj.getDataElementWoutSpace()).collect(Collectors.toList());
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_OUTPUT_REPORT,
						"ProcessControlServiceImpl-->getOutputReport()-->ends");
				return fieldList;
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_OUTPUT_REPORT,
					"ProcessControlServiceImpl-->getOutputReport()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	@Override
	public Object getConditionName(String columnName, String columnValue, String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CONDITION_NAME,
				"ProcessControlServiceImpl-->getConditionName()-->starts:");
		try {
			JsonParser parser = new JsonParser();
			List<String> conditionNameList = new ArrayList<>();

			List<com.wipro.fipc.entity.filelayout.RulesConfig> findByColumn = objRulesConfigContService
					.findByColumn(columnName, columnValue);
			List<RulesConfig> rulesConfigJson = new ArrayList<>();		
			
			List<com.wipro.fipc.entity.common.RulesDefinition> rulesDefinitions;
			  for(com.wipro.fipc.entity.filelayout.RulesConfig entity : findByColumn) {
		        	RulesConfig config =new RulesConfig();
		        	ProcessJobMapping processJobMapping=new ProcessJobMapping();
		        	processJobMapping.setId(entity.getProcessJobMapping().getId());
		        	com.wipro.fipc.model.generated.RulesDefinition rulesDefinitionModel = new com.wipro.fipc.model.generated.RulesDefinition();
		        	 rulesDefinitions =new ArrayList<>();
		        	 rulesDefinitions=entity.getRulesDefinitions();
		        	 List<com.wipro.fipc.model.generated.RulesDefinition> rulesDefinitionModelList = new ArrayList<>();
		        	 Long validationid;
		        	  for(com.wipro.fipc.entity.common.RulesDefinition entityRuleDefinition : rulesDefinitions) {
		        		 validationid=entityRuleDefinition.getValidationType().getId();
		        			BeanUtils.copyProperties(entityRuleDefinition, rulesDefinitionModel);
		        			ValidationType validationType=new ValidationType();
				        	validationType.setId(validationid);
				        	rulesDefinitionModel.setValidationType(validationType);
				        	rulesDefinitionModelList.add(rulesDefinitionModel);
		        	  }
		        	
		        	BeanUtils.copyProperties(entity, config);
		        	
		      
		        	config.setProcessJobMapping(processJobMapping);
		        	config.setRulesDefinitions(rulesDefinitionModelList);
		        	rulesConfigJson.add(config);
		        }
			for (RulesConfig ruleConfig : rulesConfigJson) {
				List<RulesDefinition> rulesDefinitionList = ruleConfig.getRulesDefinitions();
				for (RulesDefinition rulesDefinition : rulesDefinitionList) {				
					if (rulesDefinition.getRuleName().equals(searchField)) {
						String request="";
						if(!ArrayUtils.isEmpty(rulesDefinition.getConditionJson())) 
							request=deserializeObject(rulesDefinition.getConditionJson());						
						else
						 request = rulesDefinition.getJsonWoutName();
						JsonArray jsonArr = parser.parse(request).getAsJsonArray();
						for (int i = 0; i < jsonArr.size(); i++) {
							JsonObject obj = jsonArr.get(i).getAsJsonObject();
							conditionNameList.add(obj.get("conditionName").getAsString());
						}
					}
				}
			}
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CONDITION_NAME,
					"ProcessControlServiceImpl-->getConditionName()--ends.");
			return conditionNameList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_CONDITION_NAME,
					"ProcessControlServiceImpl-->getConditionName()--Exception: " + e.getMessage());
			if (e.getMessage().contains("Not a JSON")) {
				return "[]";
			}
			throw new BusinessException(e.getMessage());
		}
	}
	
	public String deserializeObject(byte[] dataByte) {
		String json=new String(dataByte);
		return json;
		
	}

}
