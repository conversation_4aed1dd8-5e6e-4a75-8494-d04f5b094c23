package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.beanutils.BeanUtils;
import org.json.JSONObject;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javaparser.utils.StringEscapeUtils;
import com.google.gson.Gson;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.BusinessOpsDao;
import com.wipro.fipc.dao.BusinessUnitDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.KsdConfigDao;
import com.wipro.fipc.dao.ProcessDao;
import com.wipro.fipc.dao.batch.JobScheduleDao;
import com.wipro.fipc.dao.batch.KsdMasterDao;
import com.wipro.fipc.dao.common.RoleConfigDao;
import com.wipro.fipc.dao.filelayout.ProcessFeatureConfigDao;
import com.wipro.fipc.dao.tba.ProcessJobMappingDao;
import com.wipro.fipc.entity.BusinessOps;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.batch.ClientConfigJobs;
import com.wipro.fipc.entity.batch.JobSchedule;
import com.wipro.fipc.entity.batch.KsdConfig;
import com.wipro.fipc.entity.batch.KsdFileDetails;
import com.wipro.fipc.entity.batch.KsdMaster;
import com.wipro.fipc.entity.filelayout.ProcessFeatureConfig;
import com.wipro.fipc.exception.processconfiguration.DBServiceException;
import com.wipro.fipc.exception.processconfiguration.InvalidInputException;
import com.wipro.fipc.exception.processconfiguration.ResourceNotFoundException;
import com.wipro.fipc.model.CustomProcessConfigurationUI;
import com.wipro.fipc.model.FileData;
import com.wipro.fipc.model.JobScheduleBean;
import com.wipro.fipc.model.PhaseNames;
import com.wipro.fipc.model.ProcessConfiguration;
import com.wipro.fipc.model.Question;
import com.wipro.fipc.model.QuestionBool;
import com.wipro.fipc.model.UpdatePhaseNames;
import com.wipro.fipc.pojo.ClientDetailsBo;
import com.wipro.fipc.pojo.CustomBusinessOpsBO;
import com.wipro.fipc.pojo.CustomBusinessUnitBO;
import com.wipro.fipc.pojo.CustomClientBO;
import com.wipro.fipc.pojo.CustomEftJobNameBO;
import com.wipro.fipc.pojo.CustomKsdNameBO;
import com.wipro.fipc.pojo.CustomPFCClientCodeBO;
import com.wipro.fipc.pojo.CustomPJMBO;
import com.wipro.fipc.pojo.CustomPJMDto;
import com.wipro.fipc.pojo.CustomProcessDetailsBO;
import com.wipro.fipc.pojo.CustomProcessFeaturesBO;
import com.wipro.fipc.pojo.ProcessCofigReqBody;
import com.wipro.fipc.pojo.ProcessCofigResBody;
import com.wipro.fipc.service.ProcessConfigurationService;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;

@Service
public class ProcessConfigurationServiceImpl implements ProcessConfigurationService {

	public static final String PROCESS_JOB_MAPPING_ID = "process_job_mapping_id";
	public static final String PROCESS_JOB_NAME = "primary_job_name";
	public static final String CREATED_BY = "created_by";
	public static final String ACTIVE_FLAG = "active_flag";
	public static final String MANAGER_ID = "manager_id";
	public static final String CREATEDBY = "createdBy";
	public static final String EFT_COLUMN = "eft_name";
	public static final String BUSINESS_OPS_NAME = "Files & Interfaces";
	public static final String BUSINESS_UNIT_NAME = "HWS";
	public static final String ID_LIST = "idList";
	public static final String ID = "id";
	public static final String BOTS_MAESTRO_TSK = "Maestro Tsk";
	public static final String BOTS_NOTIFICATION = "Notification";
	public static final String BOTS_MAESTRO_TKT = "Maestro Tkt";
	public static final String BOTS_MIMICTRON = "Mimictron";
	public static final String BOTS_FILE_FORMATTER = "File Formatter";
	public static final String BOTS_EMAIL_OPERATION = "Email Operation";
	public static final String BOTS_FILE_VALIDATOR = "File Validator";
	public static final String BOTS_SOURCE_MATCH = "Source Match";
	public static final String BOTS_PROCESS_CONTROL = "Process Control";
	public static final String METHOD_NAME = "";
	public static final String DUPLICATE = "Duplicate";
	public static final String FAILED = "Failed";
	public static final String KSDNAME_URL = "KsdNameURL";
	public static final String EXCEPTION_NAME = "Exception occured while converting string to long: ";
	public static final String CONTAIN_TYPE = "Content-Type";
	public static final String APPLICATION_JSON = "application/json";
	public static final String EXCEPTION_WHILE_FETECHING = "Exception occured while fetching the records:";
	public static final String DATA_NOT_PRESENT = "Data is not present in ProcessFeatureConfig table for the adid: ";
	public static final String SIZE_OF_KSDMASTER = "Size of retreived KsdMaterList: ";
	public static final String EXCEPTION_PROCESSFEATURE = "Exception occured while getting processFeatureConfigs: ";
	public static final String SIZE_OF_PROCESSFEATURECONFIG = "ProcessFeatureConfiglist size for given adid: ";
	public static final String PROCESS_FEATURE_URL = "Process_Feature_Config get all URL";
	public static final String INVALID_INPUT = "Invalid input either buID or clientID or buOpsID or processID:";
	public static final String GET_PROCESSCONFIGDETAILS = "getAllProcessConfigDetails";
	protected static final String PROCESS_FEATURE_CONFIG = "PROCESS_FEATURE_CONFIG";
	protected static final String COMMON_SCHEMA = "common";
	protected static final String KSD_CONFIG = "ksd_config";
	protected static final String KSD_SCHEMA = "emails_scheduler";
	protected static final String BUSINESS_OPS = "BUSINESS_OPS";
	private static final String CUSTOM = "custom";

	@Autowired
	private GenericDao<ProcessFeatureConfig> processFeaturConfigGenericDao;

	@Autowired
	private GenericDao<KsdConfig> ksdGenericDao;

	@Autowired
	private GenericDao<BusinessOps> businessGenericDao;

	@Autowired
	private ProcessFeatureConfigDao processFeatureConfigDao;

	@Autowired
	private BaseDao<ProcessFeatureConfig> processFeatureConfigBaseDao;

	@Autowired
	private BusinessUnitDao businessUnitDao;

	@Autowired
	private BusinessOpsDao businessOpsDao;

	@Autowired
	private ProcessDao processDao;

	@Autowired
	private KsdMasterDao ksdMasterDao;

	@Autowired
	private BaseDao<KsdConfig> baseKsdConfigDao;

	@Autowired
	private BaseDao<KsdFileDetails> ksdFileDetailsDao;
	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	private ProcessJobMappingDao processJobMappingDao;

	@Autowired
	private RoleConfigDao roleConfigDao;

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private KsdConfigDao ksdConfigDao;
	
	@Autowired
	private CommonGetAdId commonGetUpdatedBy;

	@Autowired
	private JobScheduleDao jobScheduleDao;

	Gson gson = new Gson();

	@Autowired
	private ObjectMapper objm;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	@Override
	public List<CustomBusinessUnitBO> getBusinessUnitByAlightID(String alightID) {
		String methodName = "getBusinessUnitByAlightID";
		if (alightID == null || alightID.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName, "AlightID is either null or empty:" + alightID);
			throw new InvalidInputException("Invalid input alightID :" + alightID);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, methodName, "Getting Business Units by Alight ID: " + alightID);

		List<CustomBusinessUnitBO> customBusinessUnitList = new ArrayList<>();
		customBusinessUnitList = businessUnitDao.getByAlightId(alightID);

		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"getBusinessUnitByAlightID before return customBusinessUnitList : " + customBusinessUnitList.size());
		return customBusinessUnitList;

	}

	@Override
	public List<CustomClientBO> getClientDetails(String alightID, String buID) {
		String methodName = "getClientDetails";
		if (alightID == null || buID == null || alightID.isEmpty() || buID.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"Either alightID or buID is null or empty :" + alightID + " " + buID);
			throw new InvalidInputException("Invalid Input either alightID or buID :" + alightID + " " + buID);
		}

		return roleConfigDao.getCustomClient(alightID, Long.parseLong(buID));

	}

	@Override
	public List<CustomBusinessOpsBO> getBusinessOpsDetails(String alightID, String buID) {
		String methodName = "getBusinessOpsDetails";
		if (alightID == null || buID == null || alightID.isEmpty() || buID.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"Either alightID or buID is null or empty :" + alightID + " " + buID);
			throw new InvalidInputException("Invalid Input either alightID or buID :" + alightID + " " + buID);
		}

		Long.parseLong(buID);
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"Getting business ops details by alight id and business unitid: " + alightID + ", " + buID);

		List<BigInteger> opsIdList = processFeaturConfigGenericDao.getOpsIdList(alightID, buID);

		List<CustomBusinessOpsBO> customBusinessOpsList = new ArrayList<>();

		if (opsIdList.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"Business Ops ID s not found for the selected ADID and business unit id");
			return customBusinessOpsList;
		}
		List<Long> opsIdList2 = new ArrayList<>();
		for (BigInteger object : opsIdList) {
			opsIdList2.add(object.longValue());
		}
		LoggerUtil.log(this.getClass(), Level.INFO, methodName, "Business Ops ID List: " + opsIdList);
		customBusinessOpsList = businessOpsDao.getBusinessOpsByIds(opsIdList2);

		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"Size of BusinessOpsList returned to UI: " + customBusinessOpsList.size());

		return customBusinessOpsList;

	}

	@Override
	public List<CustomProcessDetailsBO> getProcessDetails(String alightID, String buID, String clientID,
			String buOpsID) {

		String methodName = "getProcessDetails";
		String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientID));
		if (alightID == null || buID == null || alightID.isEmpty() || buID.isEmpty() || clientId == null
				|| clientId.isEmpty() || buOpsID == null || buOpsID.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"Either alightiD or buID or clientID or buOpsID is null or empty:");
			throw new InvalidInputException("Invalid input either alightID or buID or clientID or buOpsID :" + alightID
					+ " " + buID + " " + clientId + " " + buOpsID);
		}

		Long.parseLong(buID);
		Long.parseLong(clientId);
		Long.parseLong(buOpsID);
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"Getting process details by alightID, buid, clientID and busOpsID : " + alightID + " " + buID + " "
						+ clientId + " " + buOpsID);

		List<BigInteger> processIdList = processFeaturConfigGenericDao.getProcessIdList(alightID, buID, clientId,
				buOpsID);
		List<CustomProcessDetailsBO> customProcessList = new ArrayList<>();
		if (processIdList.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"Process ID s not found for the selected ADID, bu id, client id, ops id");
			return customProcessList;
		}
		LoggerUtil.log(this.getClass(), Level.INFO, methodName, "Process ID List: " + processIdList.size());

		List<Long> opsIdList2 = new ArrayList<>();
		for (BigInteger object : processIdList) {
			opsIdList2.add(object.longValue());
		}

		customProcessList = processDao.getProcessByIds(opsIdList2);

		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"Size of customProcessList sending to UI: " + customProcessList.size());
		return customProcessList;

	}

	@Override
	public List<String> getJobDetails(String buID, String clientID, String buOpsID, String processID) {
		String methodName = "getJobDetails";
		String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientID));
		if (buID == null || clientId == null || buOpsID == null || processID == null) {
			LoggerUtil.log(this.getClass(), Level.ERROR, methodName, "Any id is either null or empty:");
			throw new InvalidInputException("Invalid input either buID or clientID or buOpsID orprocessID:" + buID + " "
					+ clientId + " " + buOpsID + " " + processID);
		}

		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"Getting job details by buid, clientid, buopsid and processID: " + buID + "  " + clientId + " "
						+ buOpsID + " " + processID);
		List<String> jobList = new ArrayList<>();

		jobList = processJobMappingDao.getJobNames(Long.parseLong(buID), Long.parseLong(buOpsID),
				Long.parseLong(clientId), Long.parseLong(processID));

		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"getJobDetails before return jobList : " + jobList.size());
		return jobList;
	}

	@Override
	public List<String> getEFTDetails(String buID, String clientID, String buOpsID, String processID,
			CustomEftJobNameBO customEftJobNameBO) {
		String methodName = "getEFTDetails";
		String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientID));
		String jobName = customEftJobNameBO.getJobName();
		if (buID == null || clientId == null || buOpsID == null || processID == null || jobName == null
				|| jobName.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.ERROR, methodName, "One of the input is either null or empty:");
			throw new InvalidInputException("Invalid input either buID or clientID or buOpsID or processID or jobName:"
					+ buID + " " + clientId + " " + buOpsID + " " + processID + " " + jobName);
		}

		LoggerUtil.log(this.getClass(), Level.INFO, methodName, "getEFTDetails before return eftList : ");
		return processJobMappingDao.getEftNames(Long.parseLong(buID), Long.parseLong(buOpsID), Long.parseLong(clientId),
				Long.parseLong(processID), jobName);
	}

	@Override
	public List<CustomKsdNameBO> getKsdNamesAndIds(String buID, String clientID, String buOpsID, String processID,
			CustomEftJobNameBO customEftJobNameBO) {
		String methodName = "getKsdNamesAndIds";
		String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientID));
		String jobName = customEftJobNameBO.getJobName();
		String eftSubject = customEftJobNameBO.getEftSubject();
		if (buID == null || clientId == null || buOpsID == null || processID == null || jobName == null
				|| jobName.isEmpty() || eftSubject == null || eftSubject.isEmpty()) {
			LoggerUtil.log(this.getClass(), Level.ERROR, methodName, "One of the input is either null or empty:");
			throw new InvalidInputException(
					"Invalid input either buID or clientID or buOpsID or processID or jobName or eftSubject:" + buID
							+ " " + clientId + " " + buOpsID + " " + processID + " " + jobName + " " + eftSubject);
		}

		LoggerUtil.log(this.getClass(), Level.INFO, methodName, "getKsdNamesAndIds before return ksdNameBOList : ");
		return processJobMappingDao.getKsdNamesAndIds(Long.parseLong(buID), Long.parseLong(buOpsID),
				Long.parseLong(clientId), Long.parseLong(processID), jobName, eftSubject);

	}

	// add new record in PC page
	@Override
	public ProcessConfiguration saveProcessConfigDetails(CustomProcessConfigurationUI processFeatureConfigUI)
			throws IllegalAccessException, InvocationTargetException {

		LoggerUtil.log(this.getClass(), Level.INFO, "saveProcessConfigDetails",
				"saveProcessConfigDetails for processConfigurationUI: "
						+ processFeatureConfigUI.getProcessJobMappingId());
		ProcessConfiguration allSavedRecords = null;

		allSavedRecords = addNewRecordInProcessConfigPage(processFeatureConfigUI);

		if (allSavedRecords != null
				&& (DUPLICATE.equals(allSavedRecords.getMessage()) || FAILED.equals(allSavedRecords.getMessage()))) {
			return allSavedRecords;
		}

		saveProcessConfigurationsInKsd(processFeatureConfigUI);

		LoggerUtil.log(this.getClass(), Level.INFO, "saveProcessConfigDetails",
				"saveProcessConfigDetails before return allSavedRecords : " + allSavedRecords.getMessage());
		return allSavedRecords;
	}

	public String insertProcessConfigDetailsinPFC(ProcessFeatureConfig processFeatureConfig)
			throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME,
				"insertProcessConfigDetailsinPFC for processFeatureConfig: " + processFeatureConfig);
		ProcessFeatureConfig featureConfig = processFeatureConfigBaseDao.save(processFeatureConfig);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(featureConfig);
	}

	public List<KsdConfig> getKsdAndChildDetails(String processJobMappingId, String primaryJobName) {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME,
				"getKsdAndChildDetails for processJobMappingId and primaryJobName: " + processJobMappingId + " "
						+ primaryJobName);

		List<KsdConfig> populatedKsdRecordsForUI = ksdGenericDao.findActiveRecordByColumn(KsdConfig.class, KSD_SCHEMA,
				KSD_CONFIG, processJobMappingId, primaryJobName);
		return populatedKsdRecordsForUI;
	}

	public List<KsdMaster> getKsdMastersDetails(String jobColumnValue, String eftColumnValue) {

		List<KsdMaster> ksdMasters = ksdMasterDao.findKsdMasterByJobNameAndEftSubject(jobColumnValue, eftColumnValue);
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdMastersDetails", SIZE_OF_KSDMASTER + ksdMasters.size());
		return ksdMasters;
	}

	public void copyKsdMasterToKsdConfig(CustomProcessConfigurationUI processFeatureConfigUI) {
		String methodName = "copyKsdMasterToKsdConfig";
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"copyKsdMasterToKsdConfig for input json started----> ");
		com.wipro.fipc.entity.ProcessJobMapping pfcId = new com.wipro.fipc.entity.ProcessJobMapping();
		pfcId.setId(processFeatureConfigUI.getProcessJobMappingId());

		KsdConfig ksdConfigEdited = new KsdConfig();
		ksdConfigEdited.setActiveFlag('T');
		ksdConfigEdited.setProcessJobMapping(pfcId);
		ksdConfigEdited.setPrimaryJobName(processFeatureConfigUI.getJobName());
		ksdConfigEdited.setProcessJobMappingId(processFeatureConfigUI.getProcessJobMappingId());
		ksdConfigEdited.setFileType("Mainframe"); // As of now it is hard coded. Needs to be changed.
		ksdConfigEdited.setCreatedDate(new Date());
		ksdConfigEdited.setCreatedBy(processFeatureConfigUI.getCreatedBy());
		ksdConfigEdited.setEftName(processFeatureConfigUI.getEftSubject());
		// set jobSchedules
		List<JobSchedule> jobSchedulesList = new ArrayList<>();
		JobSchedule jobSchedule = new JobSchedule();
		jobSchedule.setActiveFlag('T');
		jobSchedule.setCreatedDate(new Date());
		jobSchedule.setCreatedBy(processFeatureConfigUI.getCreatedBy());

		jobSchedulesList.add(jobSchedule);
		String primaryJobValue = processFeatureConfigUI.getJobName();
		String eftValue = processFeatureConfigUI.getEftSubject();
		List<KsdMaster> ksdMasters = getKsdMastersDetails(primaryJobValue, eftValue);
		KsdMaster ksdMaster = null;
		if (ksdMasters != null && !ksdMasters.isEmpty()) {
			KsdConfig newksdConfig = null;
			ksdMaster = ksdMasters.get(0);
			newksdConfig = setKsdConfigFromKsdMaster(ksdConfigEdited, ksdMaster);
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"KsdConfig to be saved primary job name" + newksdConfig.getPrimaryJobName());

			saveKsdAndChildDetails(newksdConfig);
			jobSchedule.setKsdConfigJobSchedule(newksdConfig);
			jobScheduleDao.save(jobSchedule);
			setAndSaveKsdFileDetails(processFeatureConfigUI, ksdMaster);
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"Ksd Master details of given job name is not found " + primaryJobValue);
			LoggerUtil.log(this.getClass(), Level.INFO, methodName,
					"KsdConfig to be saved" + ksdConfigEdited.toString());

			saveKsdAndChildDetails(ksdConfigEdited);
			jobSchedule.setKsdConfigJobSchedule(ksdConfigEdited);
			jobScheduleDao.save(jobSchedule);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"copyKsdMasterToKsdConfig before return insertKsdConfigResultJson : ");
	}

	public void setAndSaveKsdFileDetails(CustomProcessConfigurationUI processFeatureConfigUI, KsdMaster ksdMaster) {

		if (processFeatureConfigUI.getBusinessOpsName().equalsIgnoreCase(BOTS_PROCESS_CONTROL)) {
			LoggerUtil.log(this.getClass(), Level.INFO, "setAndSaveKsdFileDetails",
					"BusinessOps is Process Control. So, KsdFileDetails cannot be saved");
		} else {
			com.wipro.fipc.entity.ProcessJobMapping pfcId = new com.wipro.fipc.entity.ProcessJobMapping();
			pfcId.setId(processFeatureConfigUI.getProcessJobMappingId());

			KsdFileDetails ksdFiledtl = new KsdFileDetails();
			ksdFiledtl.setProcessJobMapping(pfcId); // set
			ksdFiledtl.setProcessJobMappingId(processFeatureConfigUI.getProcessJobMappingId());
			ksdFiledtl.setFileFormatType(ksdMaster.getFileFormatType());
			ksdFiledtl.setFileName(ksdMaster.getFileName());
			ksdFiledtl.setActiveFlag('T');
			ksdFiledtl.setFileType(HolmesAppConstants.FILE_TYPE);
			ksdFiledtl.setCreatedBy(processFeatureConfigUI.getCreatedBy());
			ksdFiledtl.setCreatedDate(new Date());
			saveKSDFileDetail(ksdFiledtl);

			LoggerUtil.log(this.getClass(), Level.INFO, "<<<<< KSDFile detail saved successfulely for File ==",
					ksdMaster.getFileName());
		}
	}

	public KsdFileDetails saveKSDFileDetail(KsdFileDetails ksdFileDetail) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveKSDFileDetail", "KSDFileDetails Insertion started --->: ");

		return ksdFileDetailsDao.save(ksdFileDetail);
	}

	public KsdConfig saveKsdAndChildDetails(KsdConfig ksdConfig) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveKsdAndChildDetails", "KSDConfig Insertion Started--->");

		return baseKsdConfigDao.save(ksdConfig);

	}

	public JSONObject toJsonKsdConfig(KsdConfig ksdConfig) {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME, "toJsonKsdConfig for ksdConfig: ");
		JSONObject json = new JSONObject();
		if (ksdConfig != null) {

			json.put("id", ksdConfig.getId());
			json.put("clientConfigJobs", ksdConfig.getClientConfigJobs());
			json.put(CREATED_BY, ksdConfig.getCreatedBy());
			json.put("createdDate", ksdConfig.getCreatedDate());
			json.put("dailyTaskReportSubjectNameOutLook", ksdConfig.getDailyTaskReportSubjectNameOutLook());
			json.put("eftName", ksdConfig.getEftName());
			json.put("fileType", ksdConfig.getFileType());
			json.put("frequency", ksdConfig.getFrequency());
			json.put("jobCutOffTime", ksdConfig.getJobCutOffTime());
			json.put("jobScheduleTime", ksdConfig.getJobScheduleTime());
			json.put("jobSchedules", ksdConfig.getJobSchedules());
			json.put("jobStream", ksdConfig.getJobStream());
			json.put("maestroTaskName", ksdConfig.getMaestroTaskName());
			json.put("primaryJobName", ksdConfig.getPrimaryJobName());
			json.put("processJobMapping", ksdConfig.getProcessJobMapping());
			json.put("processJobMappingId", ksdConfig.getProcessJobMappingId());
			json.put("turnaroundTime", ksdConfig.getTurnaroundTime());
			json.put("updatedBy", ksdConfig.getUpdatedBy());
			json.put("updatedDate", ksdConfig.getUpdatedDate());

		}
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME, "toJsonKsdConfig before return json : " + json);
		return json;

	}

	public KsdConfig setKsdConfigFromKsdMaster(KsdConfig ksdConfig, KsdMaster ksdMaster) {
		LoggerUtil.log(this.getClass(), Level.INFO, "setKsdConfigFromKsdMaster",
				"setKsdConfigFromKsdMaster for ksdConfig and ksdMaster: ");
		ksdConfig.setJobStream(ksdMaster.getJobStream());
		ksdConfig.setFrequency(ksdMaster.getFrequency());
		ksdConfig.setJobCutOffTime(ksdMaster.getJobCutOffTime());
		ksdConfig.setTurnaroundTime(ksdMaster.getTurnaroundTime());
		ksdConfig.setJobScheduleTime(ksdMaster.getJobScheduleTime());
		ksdConfig.setMaestroTaskName(ksdMaster.getMaestroTaskName());

		String jobStream = ksdMaster.getJobStream();

		if (ksdMaster.getJobStream() != null && !StringUtil.isBlank(ksdMaster.getJobStream())) {
			List<ClientConfigJobs> clientJobList = new ArrayList<>();
			String[] allChildJobs = jobStream.split(",");
			for (String childJob : allChildJobs) {
				ClientConfigJobs clientJobs = new ClientConfigJobs();
				clientJobs.setActiveFlag('T');
				clientJobs.setChildJobName(childJob);
				clientJobs.setCreatedDate(new Date());
				clientJobs.setCreatedBy(ksdConfig.getCreatedBy());
				clientJobs.setProcessJobMappingId(ksdConfig.getProcessJobMappingId());
				clientJobList.add(clientJobs);
			}
			ksdConfig.setClientConfigJobs(clientJobList);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "setKsdConfigFromKsdMaster",
				"setKsdConfigFromKsdMaster before return ksdConfig : " + ksdConfig);
		return ksdConfig;
	}

	public void saveProcessConfigurationsInKsd(CustomProcessConfigurationUI processFeatureConfigUI) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveProcessConfigurationsInKsd",
				"saveProcessConfigurationsInKsd for processConfigurationUI: "
						+ processFeatureConfigUI.getProcessJobMappingId());

		String processJobMappingId = String.valueOf(processFeatureConfigUI.getProcessJobMappingId());
		List<KsdConfig> ksdConfigs = getKsdAndChildDetails(PROCESS_JOB_MAPPING_ID, processJobMappingId);
		if (ksdConfigs.isEmpty()) {
			copyKsdMasterToKsdConfig(processFeatureConfigUI);
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, "saveProcessConfigurationsInKsd",
					"Data is already present in KSDCONFIG table for processJobMappingID: " + processJobMappingId);
		}
	}

	public ProcessConfiguration addNewRecordInProcessConfigPage(CustomProcessConfigurationUI processFeatureConfigUI)
			throws IllegalAccessException, InvocationTargetException {
		LoggerUtil.log(this.getClass(), Level.INFO, "addNewRecordInProcessConfigPage",
				"addNewRecordInProcessConfigPage for processConfigurationUI: "
						+ processFeatureConfigUI.getProcessJobMappingId());
		ProcessFeatureConfig processFeatureConfig = new ProcessFeatureConfig();
		ProcessConfiguration processConfiguration = new ProcessConfiguration();
		BeanUtils.copyProperties(processFeatureConfig, processFeatureConfigUI);
		BeanUtils.copyProperties(processConfiguration, processFeatureConfigUI);
		com.wipro.fipc.entity.ProcessJobMapping pfcId = new com.wipro.fipc.entity.ProcessJobMapping();
		pfcId.setId(processFeatureConfigUI.getProcessJobMappingId());
		processFeatureConfig.setProcessJobMapping(pfcId);
		processFeatureConfig.setCreatedDate(new Date());
		processFeatureConfig.setUpdatedDate(processFeatureConfig.getCreatedDate());
		processFeatureConfig.setUpdatedBy(processFeatureConfig.getCreatedBy());
		processFeatureConfig.setActiveFlag('T');
		processFeatureConfig.setConfigStatus("In-progress");
		processFeatureConfig.setApprovedBy("");
		String processJobMapping = String.valueOf(processFeatureConfig.getProcessJobMapping().getId());
		int duplicateResult = checkDuplicateRecordsInPFC(processJobMapping);

		processConfiguration.setConfigStatus(processFeatureConfig.getConfigStatus());
		processConfiguration.setApprovedBy(processFeatureConfig.getApprovedBy());
		if (duplicateResult > 0) {
			processConfiguration.setMessage(DUPLICATE);
			processConfiguration.setUpdatedBy(processFeatureConfig.getCreatedBy());
			processConfiguration.setUpdatedDate(processFeatureConfig.getCreatedDate());
			return processConfiguration;

		} else {

			ProcessFeatureConfig featureConfig = processFeatureConfigBaseDao.save(processFeatureConfig);
			if (featureConfig != null) {
				processConfiguration.setMessage("Success");
				processConfiguration.setId(featureConfig.getId());
				processConfiguration.setUpdatedBy(featureConfig.getUpdatedBy());
				processConfiguration.setUpdatedDate(featureConfig.getUpdatedDate());
				return processConfiguration;
			} else {
				processConfiguration.setMessage(FAILED);
				return processConfiguration;
			}
		}
	}

	int checkDuplicateRecordsInPFC(String pjmID) {

		return processFeatureConfigDao.checkForDuplicates(Long.parseLong(pjmID));
	}

	// display rows in pC page
	@Override
	public List<CustomPFCClientCodeBO> getAllProcessConfigDetails(String alightID) {

		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME,
				"getAllProcessConfigDetails for alightID: " + alightID);
		List<CustomPFCClientCodeBO> customPFCClientCodeBO = new ArrayList<>();
		customPFCClientCodeBO = roleConfigDao.getPFCrows1(alightID);
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllProcessConfigDetails",
				"getAllProcessConfigDetails before return customPFCClientCodeBO size : "
						+ customPFCClientCodeBO.size());
		return customPFCClientCodeBO;
	}

	public List<CustomPFCClientCodeBO> getAllProcessConfigDetailsExceptReport(String alightID) {

		LoggerUtil.log(this.getClass(), Level.INFO, "getAllProcessConfigDetailsExceptReport",
				"getAllProcessConfigDetailsExceptReport for alightID: " + alightID);
		List<CustomPFCClientCodeBO> customPFCClientCodeBO = new ArrayList<>();
		customPFCClientCodeBO = roleConfigDao.getPFCrowsList(alightID);
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllProcessConfigDetailsExceptReport",
				"getAllProcessConfigDetailsExceptReport before return customPFCClientCodeBO : "
						+ customPFCClientCodeBO.size());
		return customPFCClientCodeBO;
	}

	public List<ProcessFeatureConfig> getAllDetailsPJMID(String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllDetailsPJMID",
				"getAllDetailsPJMID for columnValue: " + columnValue);
		List<ProcessFeatureConfig> processFeatureConfigs = null;
		List<String> columnNames = new ArrayList<>();
		columnNames.add(PROCESS_JOB_MAPPING_ID);
		columnNames.add(ACTIVE_FLAG);
		List<String> columnConditions = new ArrayList<>();
		columnConditions.add("eq");
		columnConditions.add("eq");
		List<String> columnValues = new ArrayList<>();
		columnValues.add(columnValue);
		columnValues.add("T");

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting ProcessFeatureConfig columnConditionParams: " + columnConditionParams);

		processFeatureConfigs = processFeaturConfigGenericDao.findByMultiColumnCondition(ProcessFeatureConfig.class,
				COMMON_SCHEMA, PROCESS_FEATURE_CONFIG, columnConditionParams);
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME,
				"getAllDetailsPJMID before return processFeatureConfigs : " + processFeatureConfigs);
		return processFeatureConfigs;
	}

	@Override
	public String updatePhaseNamesDetails(String id, UpdatePhaseNames updatePhaseNames) {
		String methodName = "updatePhaseNamesDetails";
		LoggerUtil.log(this.getClass(), Level.INFO, "updatePhaseNamesDetails",
				"updatePhaseNamesDetails for id and updatePhaseNames: " + id + " " + updatePhaseNames);
		List<ProcessFeatureConfig> processFeatureConfig = new ArrayList<>();

		processFeatureConfig = processFeaturConfigGenericDao.findByColumn(ProcessFeatureConfig.class, COMMON_SCHEMA,
				PROCESS_FEATURE_CONFIG, "id", id);

		LoggerUtil.log(this.getClass(), Level.INFO, "updatePhaseNamesDetails",
				"getting ProcessFeatureConfig size of : " + processFeatureConfig.size());

		String message = null;

		if (processFeatureConfig.size() > 0) {
			for (ProcessFeatureConfig processFatureConifgUpdate : processFeatureConfig) {
				processFatureConifgUpdate.setPhaseNames(updatePhaseNames.getPhaseNames());
				processFeatureConfigBaseDao.save(processFatureConifgUpdate);
				message = "Success";

			}
		}

		if (message == null)
			return FAILED;
		else
			return "Success";

	}

	@Override
	public Set<String> getConfiguredJobList(String opsId, String adid)
			throws IllegalAccessException, InvocationTargetException {
		String methodName = "getConfiguredJobList";
		List<BusinessOps> businessOpsList = null;
		String columnName = "id";
		businessOpsList = businessGenericDao.findRecordByColumn(BusinessOps.class, COMMON_SCHEMA, BUSINESS_OPS,
				columnName, opsId);
		String businessOpsName = null;
		if (businessOpsList.size() > 0) {
			businessOpsName = businessOpsList.get(0).getOpsName();
		}
		List<CustomPFCClientCodeBO> processConfigurationList = getAllProcessConfigDetailsExceptReport(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				SIZE_OF_PROCESSFEATURECONFIG + processConfigurationList.size());
		Set<String> configuredJobList = new HashSet<>();
		if (!processConfigurationList.isEmpty()) {
			for (CustomPFCClientCodeBO processConfiguration : processConfigurationList) {
				if (processConfiguration.getBusinessOpsName().equals(businessOpsName))
					configuredJobList.add(processConfiguration.getKsdName());
			}
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName, DATA_NOT_PRESENT + adid);
		}
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				"Configured Job List before returning: " + configuredJobList.toString());
		return configuredJobList;
	}

	@Override
	public Set<String> getConfiguredEftDetails(CustomEftJobNameBO customEftJobNameBO, String adid) {
		String methodName = "getConfiguredEftDetails";
		List<CustomPFCClientCodeBO> processConfigurationList = getAllProcessConfigDetails(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				SIZE_OF_PROCESSFEATURECONFIG + processConfigurationList.size());
		Set<String> configuredEFTList = new HashSet<>();
		if (!processConfigurationList.isEmpty()) {
			for (CustomPFCClientCodeBO processConfiguration : processConfigurationList) {
				if (processConfiguration.getJobName().equals(customEftJobNameBO.getJobName())) {
					configuredEFTList.add(processConfiguration.getEftSubject());
				}
			}
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName, DATA_NOT_PRESENT + adid);
		}
		return configuredEFTList;
	}

	@Override
	public Set<CustomKsdNameBO> getConfiguredKsdNamesAndIds(CustomEftJobNameBO customEftJobNameBO, String adid) {
		String methodName = "getConfiguredKsdNamesAndIds";
		List<CustomPFCClientCodeBO> processConfigurationList = getAllProcessConfigDetails(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, methodName,
				SIZE_OF_PROCESSFEATURECONFIG + processConfigurationList.size());
		Set<CustomKsdNameBO> configuredKSDList = new HashSet<>();
		if (!processConfigurationList.isEmpty()) {
			for (CustomPFCClientCodeBO processConfiguration : processConfigurationList) {
				if (processConfiguration.getJobName().equals(customEftJobNameBO.getJobName())
						&& processConfiguration.getEftSubject().equals(customEftJobNameBO.getEftSubject())) {
					CustomKsdNameBO customKsdNameBO = new CustomKsdNameBO();
					customKsdNameBO.setId(processConfiguration.getProcessJobMappingId());
					customKsdNameBO.setKsdName(processConfiguration.getKsdName());
					configuredKSDList.add(customKsdNameBO);
				}
			}
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, methodName, DATA_NOT_PRESENT + adid);
		}
		return configuredKSDList;
	}

	@Override
	public CustomPFCClientCodeBO getPhaseNamesDetails(String id)
			throws IllegalAccessException, InvocationTargetException {

		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME, "getPhaseNamesDetails for id: " + id);
		List<ProcessFeatureConfig> processFeatureConfig = new ArrayList<>();

		processFeatureConfig = processFeaturConfigGenericDao.findByColumn(ProcessFeatureConfig.class, COMMON_SCHEMA,
				PROCESS_FEATURE_CONFIG, ID, id);
		LoggerUtil.log(this.getClass(), Level.INFO, "getPhaseNamesDetails",
				"getting ProcessFeatureConfig size of : " + processFeatureConfig.size());
		CustomPFCClientCodeBO customPFCClientCodeBO = new CustomPFCClientCodeBO();

		if (processFeatureConfig == null || processFeatureConfig.isEmpty()) {
			return null;
		}
		for (ProcessFeatureConfig pfConfig : processFeatureConfig) {
			BeanUtils.copyProperties(customPFCClientCodeBO, pfConfig);
			customPFCClientCodeBO.setProcessJobMappingId(pfConfig.getProcessJobMapping().getId());
		}
		return customPFCClientCodeBO;
	}

	public void getErrorCode(Exception exception) {
		String msg = exception.getMessage();
		LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaMetadata", "Exception in getErrorCode: ",
				exception.getMessage());
		if (msg.contains("NotFound: 404")) {
			msg = "Corresponding resouce/url not found in DBService";
			throw new ResourceNotFoundException(msg);
		}

		if (msg.contains("Connection refused: connect")) {
			msg = "DBService is down. Please contact administrator";
		}

		if (msg.contains("InternalServerError: 500")) {
			msg = "Output from DBservice is broken";
		}

		throw new DBServiceException(msg);
	}

	@Override
	public List<String> getKsdNameList(String buid, String clientId) {
		String clientID = Integer.toString(customBeanUtils.checkForClientCode(clientId));
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdNameList",
				"getKsdNameList for buid and clientId: " + buid + " " + clientID);

		List<String> ksdNameList = processJobMappingDao.getListOfKsdName(Long.parseLong(buid),
				Long.parseLong(clientID));
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdNameList",
				"Size of retreived ksdNameList: " + ksdNameList.size());
		return ksdNameList;
	}

	@Override
	public CustomPJMBO getProcessJobMapping(CustomKsdNameBO customKsdNameBO) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessJobMapping",
				"getProcessJobMapping for ksdName:  " + customKsdNameBO.getKsdName());

		return processJobMappingDao.getBusinessOpsProcessDetailsByKsd(customKsdNameBO.getKsdName());
	}

	@Override
	public CustomProcessFeaturesBO getProcessfeatureConfig(CustomKsdNameBO customKsdNameBO) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessfeatureConfig",
				"getProcessFeature for ksdName:  " + customKsdNameBO.getKsdName());
		return processFeatureConfigDao.getprocessfeatureConfig(customKsdNameBO.getKsdName()).get(0);

	}

	@Override
	public List<CustomPFCClientCodeBO> getPFCDetailsForRole(String alightID, String roleOfUser)
			throws IllegalAccessException, InvocationTargetException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME,
				"getPFCDetailsForRole for alightID and roleOfUser: " + alightID + " " + roleOfUser);
		return roleConfigDao.getPFCrowsAnalyst(alightID);
	}

	@Override
	public List<ClientDetailsBo> getClientOfManager(String adid, String role, Long buid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getClientOfManager",
				"getClientOfManager for buid and clientId: " + adid + " " + role + " " + buid);
		return roleConfigDao.getclientdetailsAnalyst(adid, buid, role);
	}

	@Override
	public String updatePhaseNameById(String id, List<FileData> files)
			throws IllegalAccessException, IOException, JsonProcessingException, InvocationTargetException {
		String jsonFormattedString = StringEscapeUtils.unescapeJava(getPhaseNamesDetails(id).getPhaseNames());
		PhaseNames pn = objm.setSerializationInclusion(Include.NON_NULL).readValue(jsonFormattedString,
				PhaseNames.class);
		HashSet<String> bots = new HashSet<>();
		HashSet<String> sourceMatch = new HashSet<>();
		HashSet<String> processControl = new HashSet<>();

		deleteFilesInPhaseName(pn, files);

		ksdQuestionOne(pn.getQuesYesNO().getKsdQueOne(), bots);

		ksdQuestionTwo(pn.getQuesYesNO().getKsdQueTwo(), bots);

		layoutOrReportQuestionOne(
				mergeList(pn.getQuesYesNO().getLayoutQueOne(), pn.getQuesYesNO().getReportlayoutQueOne()), bots);

		layoutOrReportQuestionTwo(
				mergeList(pn.getQuesYesNO().getLayoutQueTwo(), pn.getQuesYesNO().getReportlayoutQueTwo()), bots,
				sourceMatch);

		layoutOrReportQuestionThree(
				mergeList(pn.getQuesYesNO().getLayoutQueThree(), pn.getQuesYesNO().getReportlayoutQueThree()), bots,
				processControl);

		pn.setBots(setToStringConcat(bots));
		pn.setProcessControl(setToStringConcat(processControl));
		pn.setSourceMatch(setToStringConcat(sourceMatch));
		UpdatePhaseNames upn = new UpdatePhaseNames();
		objm.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		upn.setPhaseNames(objm.writeValueAsString(pn));
		return updatePhaseNamesDetails(id, upn);
	}

	private void deleteFilesInPhaseName(PhaseNames pn, List<FileData> files) {
		if (!files.isEmpty()) {
			files.parallelStream().forEach(file -> {
				if (file.isSourceMatch()) {
					deleteFilesBool(mergeList(pn.getUi().getMatchTBA(), pn.getQues().getLayoutQueTwo(),
							pn.getQues().getReportlayoutQueTwo()), file.getFileName());
					deleteFilesString(
							mergeList(pn.getQuesYesNO().getLayoutQueTwo(), pn.getQuesYesNO().getReportlayoutQueTwo()),
							file.getFileName());
				}
				if (file.isProcessControl()) {
					deleteFilesBool(mergeList(pn.getUi().getProcessControl(), pn.getQues().getLayoutQueThree(),
							pn.getQues().getReportlayoutQueThree()), file.getFileName());
					deleteFilesString(mergeList(pn.getQuesYesNO().getLayoutQueThree(),
							pn.getQuesYesNO().getReportlayoutQueThree()), file.getFileName());
				}
			});
		}

	}

	private void deleteFilesBool(List<QuestionBool> questions, String fileName) {
		questions.parallelStream().forEach(question -> {
			if (question.getFileName().equals(fileName))
				question.setFlag(false);
		});

	}

	private void deleteFilesString(List<Question> questions, String fileName) {
		questions.parallelStream().forEach(question -> {
			if (question.getFileName().equals(fileName))
				question.setFlag("");
		});

	}

	private void ksdQuestionOne(String questionOne, Set<String> bots) {
		if (!questionOne.isEmpty())
			bots.addAll(Arrays.asList(BOTS_MAESTRO_TSK, BOTS_NOTIFICATION, BOTS_MAESTRO_TKT));
	}

	private void ksdQuestionTwo(String questionTwo, Set<String> bots) {
		switch (questionTwo) {
		case "mainframe":
			bots.addAll(Arrays.asList(BOTS_MAESTRO_TSK, BOTS_MIMICTRON, BOTS_FILE_FORMATTER, BOTS_NOTIFICATION,
					BOTS_MAESTRO_TKT));
			break;

		case "reports":
			bots.addAll(Arrays.asList(BOTS_MAESTRO_TSK, BOTS_EMAIL_OPERATION, BOTS_FILE_FORMATTER, BOTS_NOTIFICATION,
					BOTS_MAESTRO_TKT));
			break;

		case "both":
			bots.addAll(Arrays.asList(BOTS_MAESTRO_TSK, BOTS_MIMICTRON, BOTS_EMAIL_OPERATION, BOTS_FILE_FORMATTER,
					BOTS_NOTIFICATION, BOTS_MAESTRO_TKT));
			break;

		default:
			break;
		}
	}

	private void layoutOrReportQuestionOne(List<Question> questions, Set<String> bots) {
		if (!questions.isEmpty()) {
			questions.forEach(question -> {
				if (question.getFlag().equals("verify_layout_yes"))
					bots.add(BOTS_FILE_VALIDATOR);
			});
		}
	}

	private void layoutOrReportQuestionTwo(List<Question> questions, Set<String> bots, Set<String> sourceMatchList) {
		if (!questions.isEmpty()) {
			questions.forEach(question -> {
				if (question.getFlag().equals("compare_tba_yes")) {
					bots.add(BOTS_SOURCE_MATCH);
					sourceMatchList.add(question.getFileName());
				}
			});
		}
	}

	private void layoutOrReportQuestionThree(List<Question> questions, Set<String> bots,
			Set<String> processControlList) {
		if (!questions.isEmpty()) {
			questions.forEach(question -> {
				if (question.getFlag().equals("participant_tba_yes")) {
					bots.add(BOTS_PROCESS_CONTROL);
					processControlList.add(question.getFileName());
				}

			});
		}
	}

	private String setToStringConcat(Set<String> set) {
		StringBuilder strb = new StringBuilder();
		set.forEach(item -> {
			if (strb.length() == 0) {
				strb.append(item);
			} else {
				strb.append("," + item);
			}
		});
		return strb.toString();
	}

	@SafeVarargs
	private static <T> List<T> mergeList(List<T>... lists) {
		List<T> list = new ArrayList<>();
		Stream.of(lists).forEach(list::addAll);

		return list;
	}

	@Override
	public ProcessCofigResBody getAllFilterdProcessConfigDetails(ProcessCofigReqBody reqBody)
			throws IllegalAccessException, InvocationTargetException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllFilterdProcessConfigDetails",
				"getAllProcessConfigDetails for alightID: " + reqBody.getAdid());

		return processFeaturConfigGenericDao.getAllFilterdProcessConfigDetails(reqBody);

	}

	@Override
	public CustomPJMDto getProcessJobMappingById(Long pjmid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessJobMappingById",
				"getProcessJobMappingById for pjmid:  " + pjmid);
		return processJobMappingDao.getBusinessOpsProcessDetailByPjmid(pjmid);
	}

	@Override
	public void updateScheduleJobs(JobScheduleBean jobScheduleBean, String appName, String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateScheduleJobs", "Inside UI service IMPL Activity tracker");

		List<KsdConfig> ksdConfigs = ksdConfigDao.findByProcessJobMappingId(jobScheduleBean.getProcessJobMappingIds());
		if (!CollectionUtils.isEmpty(ksdConfigs)) {
			String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
			List<KsdConfig> Updatedresponse = new ArrayList<>();

			for (KsdConfig ksdConfig : ksdConfigs) {
				ksdConfig.setActiveFlag('T');
				ksdConfig.setUpdatedBy(adID);
				ksdConfig.setUpdatedDate(new Date());
				ksdConfig.setFrequency(CUSTOM);
				ksdConfig.setJobCutOffTime(jobScheduleBean.getCutoffTime());
				ksdConfig.setJobScheduleTime(jobScheduleBean.getScheduleTime());

				List<JobSchedule> jobSchedules = ksdConfig.getJobSchedules();
				if(!CollectionUtils.isEmpty(jobSchedules)) {
					JobSchedule jobSchedule = ksdConfig.getJobSchedules().get(0);

					jobSchedule.setFrequency(CUSTOM);
					jobSchedule.setCustomDates(jobScheduleBean.getCustomDates());
					jobSchedule.setUpdatedDate(new Date());
					jobSchedule.setUpdatedBy(adID);
					jobSchedule.setActiveFlag('T');

					List<JobSchedule> jobScheduleResponses = new ArrayList<>();
					jobScheduleResponses.add(jobSchedule);
					ksdConfig.setJobSchedules(jobScheduleResponses);
				}

				Updatedresponse.add(ksdConfig);
			}

			ksdConfigDao.saveAll(Updatedresponse);
		}
	}
}