package com.wipro.fipc.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.restservice.GenericService;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.RulesRequest;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.service.IRuleEngineTransformationService;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;

@Service
public class RuleEngineTransformationServiceImpl implements IRuleEngineTransformationService {

	@Autowired
	RuleEngineDOConverter doConverter;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	GenericService<com.wipro.fipc.entity.filelayout.RulesConfig> genericService;

	@Autowired
	GenericDao<com.wipro.fipc.entity.filelayout.RulesConfig> genericDao;

	public static final String LOGGER = "Unable to connect to DB Service due to :";

	public static final String PJM_ID = "pjmID:";

	private static final String ADID2 = " ADID =";

	private static final String RULE_NAME = "ruleName = ";

	protected static final String RULES_CONFIG = "RULES_CONFIG";
	protected static final String LAYOUT_SCHEMA = "layout_rule";

	@Override
	public Object createTransformationRules(RulesRequest request) {
		RulesRequest response = null;
		ObjectMapper mapper = new ObjectMapper();
		try {
			LoggerUtil.log(getClass(), Level.INFO, "createTransformationRules",
					"Inside createTransformationRules method : ");

			if (request.getConversionType().equalsIgnoreCase("Date")) {
				RulesConfig rulesConfigDO = doConverter.convertTransRuleRequestForDateToDO(request);
				if (rulesConfigDO == null) {
					return "Date format is not found in LayoutConfig";
				} else {
					com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
							com.wipro.fipc.entity.filelayout.RulesConfig.class);
					com.wipro.fipc.entity.filelayout.RulesConfig responseRulesConfig = genericService
							.create(rulesConfig);
					mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
							false);
					response = doConverter.convertCreateResponse(responseRulesConfig);
				}
			} else {
				RulesConfig rulesConfigDO = doConverter.convertTransformationRuleRequestToDO(request);

				com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
						com.wipro.fipc.entity.filelayout.RulesConfig.class);
				com.wipro.fipc.entity.filelayout.RulesConfig responseRulesConfig = genericService.create(rulesConfig);
				mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
				response = doConverter.convertCreateResponse(responseRulesConfig);
			}

		} catch (Exception exception) {

			LoggerUtil.log(getClass(), Level.ERROR, "createTransformationRules", LOGGER + exception.getMessage());
		}
		return response;
	}

	@Override
	public RulesRequest updateBusinessRulesByRuleId(RulesRequest rulesRequest, String pjmId, String ruleId) {
		JsonParser parser = new JsonParser();
		String getResponse = null;
		RulesRequest response = null;
		RulesConfig config = null;
		Gson gson = new Gson();

		try {
			LoggerUtil.log(getClass(), Level.INFO, "updateBusinessRulesByRuleId",
					"Calling DB service for update by ruele id : ");

			List<com.wipro.fipc.entity.filelayout.RulesConfig> ruleConfigData = genericDao.findByColumn(
					com.wipro.fipc.entity.filelayout.RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, "id", ruleId);
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			getResponse = ow.writeValueAsString(ruleConfigData);

			JsonArray arr = parser.parse(getResponse).getAsJsonArray();
			for (int i = 0; i < arr.size(); i++) {
				config = gson.fromJson(arr.get(i).getAsJsonObject(), RulesConfig.class);
			}

			RulesConfig rulesConfigDO = doConverter.updateTransByRuleIdConvertor(rulesRequest, config, ruleId);

			ObjectMapper mapper = new ObjectMapper();
			com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
					com.wipro.fipc.entity.filelayout.RulesConfig.class);

			com.wipro.fipc.entity.filelayout.RulesConfig responseUpdateData = genericService
					.update(Long.valueOf(ruleId), rulesConfig);
			mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			String updateData = mapper.writeValueAsString(responseUpdateData);

			response = doConverter.convertCreateResponse(updateData);

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateBusinessRulesByRuleId", LOGGER + exception.getMessage());
			throw new BusinessException(exception.getMessage());
		}

		return response;

	}

	public String deleteTransformationRules(String processId) {
		String response = null;
		try {
			LoggerUtil.log(getClass(), Level.INFO, "deleteTransformationRules",
					"Calling DB service for delete all rules : ");

			boolean deleteResponse = genericDao.deleteByColumn(com.wipro.fipc.entity.filelayout.RulesConfig.class,
					LAYOUT_SCHEMA, RULES_CONFIG, "id", processId);

			response = String.valueOf(deleteResponse);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteTransformationRules", LOGGER + exception.getMessage());
		}
		return response;
	}

	@Override
	public String deleteAllTransformationRule(String processJobMappingId) {
		String response = null;
		try {
			LoggerUtil.log(getClass(), Level.INFO, "deleteAllTransformationRule",
					"Calling DB service for delete by rule id : ");
			boolean deleteResponse = genericDao.deleteByColumn(com.wipro.fipc.entity.filelayout.RulesConfig.class,
					LAYOUT_SCHEMA, RULES_CONFIG, "process_job_mapping_id", processJobMappingId);
			response = String.valueOf(deleteResponse);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteAllTransformationRule", LOGGER + exception.getMessage());
		}
		return response;
	}

	@Override
	public Object createTransformationRulesData(RulesRequest transformationRules, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		transformationRules.setCreatedBy(adid);
		transformationRules.setUpdatedBy(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "createTransformationRulesData() -> RULES ",
				PJM_ID + Long.toString(transformationRules.getProcessJobMappingId()) + RULE_NAME
						+ transformationRules.getRuleName() + ADID2 + adid + " action = CREATE");
		return createTransformationRules(transformationRules);
	}

	@Override
	public RulesRequest updateBusinessRulesByRuleIdData(RulesRequest rulesRequest, String pjmId, String ruleId,
			String appName, String sessionToken) {
		JsonParser parser = new JsonParser();
		String getResponse = null;
		RulesRequest response = null;
		RulesConfig config = null;
		Gson gson = new Gson();

		try {
			LoggerUtil.log(getClass(), Level.INFO, "updateBusinessRulesByRuleId",
					"Calling DB service for update by ruele id : ");

			List<com.wipro.fipc.entity.filelayout.RulesConfig> ruleConfigData = genericDao.findByColumn(
					com.wipro.fipc.entity.filelayout.RulesConfig.class, LAYOUT_SCHEMA, RULES_CONFIG, "id", ruleId);
			ObjectMapper ow = new ObjectMapper();
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			getResponse = ow.writeValueAsString(ruleConfigData);

			JsonArray arr = parser.parse(getResponse).getAsJsonArray();
			for (int i = 0; i < arr.size(); i++) {
				config = gson.fromJson(arr.get(i).getAsJsonObject(), RulesConfig.class);
			}
			if (null != config) {
				CustomBeanUtils.copyProperties(rulesRequest, config);
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, "updateBusinessRulesByRuleIdData ",
						"No rule configured for the given ruleId" + ruleId);
			}

			String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, "updateByRuleId() -> RULES ",
					PJM_ID + Long.toString(rulesRequest.getProcessJobMappingId()) + RULE_NAME
							+ rulesRequest.getRuleName() + ADID2 + adid + " action = UPDATE" + " Rule_Id ="
							+ rulesRequest.getRuleId());

			RulesConfig rulesConfigDO = doConverter.updateTransByRuleIdConvertorNew(rulesRequest, config, ruleId, adid);

			ObjectMapper mapper = new ObjectMapper();
			com.wipro.fipc.entity.filelayout.RulesConfig rulesConfig = mapper.convertValue(rulesConfigDO,
					com.wipro.fipc.entity.filelayout.RulesConfig.class);

			com.wipro.fipc.entity.filelayout.RulesConfig updateDataResponse = genericService
					.update(Long.valueOf(ruleId), rulesConfig);
			ow.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			String updateData = ow.writeValueAsString(updateDataResponse);
			response = doConverter.convertCreateResponse(updateData);

		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateBusinessRulesByRuleId", LOGGER + exception.getMessage());
			throw new BusinessException(exception.getMessage());
		}

		return response;

	}

}
