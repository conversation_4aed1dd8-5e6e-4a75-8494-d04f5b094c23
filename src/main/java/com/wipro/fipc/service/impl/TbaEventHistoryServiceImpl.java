package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.exception.processconfiguration.InvalidInputException;
import com.wipro.fipc.model.EventHistoryEffectiveDate;
import com.wipro.fipc.model.EventHistoryUI;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.generated.EventHistInqConfig;
import com.wipro.fipc.model.generated.EventHistoryMaster;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.ITbaEventHistoryService;
import com.wipro.fipc.tba.service.EventConfigTba;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;

@Service
public class TbaEventHistoryServiceImpl implements ITbaEventHistoryService {

	@Autowired
	Environment env;

	@Autowired
	EventConfigTba eventConfigTba;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.EventHistInqConfig> genericDao;
	
	@Autowired
	private CustomBeanUtils customBeanUtils;

	public static final String CLIENTID_COLUMN = "client_id";
	public static final String PJMID_COLUMN = "process_job_mapping_id";
	public static final String FROMDATE_COLUMN = "eff_from_date";
	public static final String TODATE_COLUMN = "eff_to_date";
	public static final String EVENTNAME_COLUMN = "event_name";
	public static final String TBAFIELDNAME_COLUMN = "tba_field_name";
	public static final String SPACE_SEPERATOR = " ";
	public static final String GET_ALL_URL = "eventHistInqConfig.getAll";
	public static final String DUPLICATE = "duplicate";
	protected static final String EVENT_HIST_INQ_CONFIG = "EVENT_HIST_INQ_CONFIG";
	protected static final String SCHEMA = "tba";

	@Autowired
	private Gson gson;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	/* Save EventHistoryUI details */

	@Override
	public String saveEventHistInqConfigWithOutDuplicates(List<EventHistoryUI> tbaList, String appName,
			String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		tbaList.stream().forEach(s -> {
			s.setCreatedBy(adid);
			s.setCreatedDate(new Date());
			s.setActiveFlag("T");
			s.setUpdatedBy(adid);
			s.setUpdatedDate(new Date());
			LoggerUtil.log(this.getClass(), Level.INFO, "saveEventHistInqConfigWithOutDuplicates>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Save" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adid);
		});
		return saveEventHistInqConfigWithOutDuplicates(tbaList);
	}

	/* modifyEventHistInqConfigWithOutDuplicates */

	@Override
	public String modifyEventHistInqConfigWithOutDuplicates(EventHistoryUI tbaList, String appName,
			String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		EventHistoryUI dbData = getEventHistInqConfigByID(tbaList.getId().toString()).get(0);
		tbaList.setCreatedBy(dbData.getCreatedBy());
		tbaList.setCreatedDate(dbData.getCreatedDate());
		tbaList.setUpdatedBy(adid);
		tbaList.setUpdatedDate(new Date());
		tbaList.setActiveFlag("T");
		tbaList.setActivityId(dbData.getActivityId());
		LoggerUtil.log(this.getClass(), Level.INFO, "modifyEventHistInqConfigWithOutDuplicates>>>>>>>>",
				"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
						+ tbaList.getProcessJobMappingId() + ",ADID: " + adid);
		return modifyEventHistInqConfigWithOutDuplicates(tbaList);
	}

	/* Delete EventHistory Config */

	@Override
	public String deleteEventHistoryConfig(List<EventHistoryUI> entity, String appName, String sessionToken) {
		String adid = commonGetUpdatedBy.getADID(appName, sessionToken);
		gson = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").create();
		entity.stream().forEach(s -> {
			s.setUpdatedBy(adid);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteEventHistoryConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adid);
		});
		return deleteEventHistoryConfig(gson.toJson(entity));
	}

	/* Get EventHistInqConfig By ID */

	public List<EventHistoryUI> getEventHistInqConfigByID(String id) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByid",
				"TbaEventHistoryServiceImpl--->Getting EventHistInqConfig by id--->starts: ");

		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByID>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,PjmId=" + id);

		List<EventHistInqConfig> response = new ArrayList<>();

		try {
			List<com.wipro.fipc.entity.tba.EventHistInqConfig> findByColumn = genericDao.findByColumn(
					com.wipro.fipc.entity.tba.EventHistInqConfig.class, SCHEMA, EVENT_HIST_INQ_CONFIG, "id", id);
			LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByPJMID2",
					"EventHistInqConfig GET call response size : {}", response.size());

			ObjectMapper objectMapper = new ObjectMapper();
			response = Arrays.asList(objectMapper.convertValue(findByColumn, EventHistInqConfig[].class));

		} catch (Exception exception) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "getEventHistInqConfigByID3",
					"Exception occured while getting EventHistInqConfig List:", exception);
		}

		List<EventHistoryUI> eventHistoryUIList = seteventHistoryUIFromConfig(response);
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByID4",
				"TbaEventHistoryServiceImpl--->Getting EventHistInqConfig by id--->ends: ");

		return eventHistoryUIList;
	}

	/* Save EventHistInqConfig WithOutDuplicates */

	@Override
	public String saveEventHistInqConfigWithOutDuplicates(List<EventHistoryUI> tbaEventList) {

		LoggerUtil.log(this.getClass(), Level.INFO, "saveEventHistInqConfigWithOutDuplicates()",
				"TbaEventHistoryServiceImpl--->Save()---> No. of objects: " + tbaEventList.size());
		String convertedString = "";
		String dbResponse = "";
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		List<EventHistInqConfig> entityList = new ArrayList<>();
		for (EventHistoryUI eventHistoryUI : tbaEventList) {
			EventHistInqConfig eventConfig = seteventHistInqConfigFromUI(eventHistoryUI);
			eventConfig.setCreatedDate(new Date());
			entityList.add(eventConfig);
		}
		List<com.wipro.fipc.entity.tba.EventHistInqConfig> newLayout = new ArrayList();
		for (EventHistInqConfig req : entityList) {

			try {
				convertedString = objectMapper.writeValueAsString(req);
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			com.wipro.fipc.entity.tba.EventHistInqConfig obj = null;
			try {
				obj = new ObjectMapper().readValue(convertedString, com.wipro.fipc.entity.tba.EventHistInqConfig.class);
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			newLayout.add(obj);
		}
		List<CommonResRowBO> mydata = eventConfigTba.saveIfNotDuplicate(newLayout);

		try {
			dbResponse = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "saveEventHistInqConfigWithOutDuplicates()3",
				"TbaEventHistoryServiceImpl--->Save()--->ends: ");

		ResponseDto response = new ResponseDto();
		response.setData(dbResponse);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		try {
			return objectMapper.writeValueAsString(response);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "saveEventHistInqConfigWithOutDuplicates()2",
					"TbaEventHistoryServiceImpl-->save()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}

	}

	/* ModifyEventHistInqConfig WithOutDuplicates */

	public String modifyEventHistInqConfigWithOutDuplicates(EventHistoryUI eventHistoryUI) {
		String convertedString = "";
		String dbResponse = "";

		LoggerUtil.log(this.getClass(), Level.INFO, "modifyEventHistInqConfig()",
				"TbaEventHistoryServiceImpl--->modifyEventHistInqConfig()---> Starts");

		EventHistInqConfig eventConfigToModify = seteventHistInqConfigFromUI(eventHistoryUI);
		eventConfigToModify.setUpdatedDate(new Date());

		List<EventHistInqConfig> configs = new ArrayList<>();
		configs.add(eventConfigToModify);
		List<com.wipro.fipc.entity.tba.EventHistInqConfig> newLayout = new ArrayList();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		for (EventHistInqConfig req : configs) {

			try {
				convertedString = objectMapper.writeValueAsString(req);
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			com.wipro.fipc.entity.tba.EventHistInqConfig obj = null;
			try {
				obj = new ObjectMapper().readValue(convertedString, com.wipro.fipc.entity.tba.EventHistInqConfig.class);
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			newLayout.add(obj);
		}
		List<CommonResRowBO> mydata = eventConfigTba.saveIfNotDuplicate(newLayout);

		try {
			dbResponse = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}

		ResponseDto response = new ResponseDto();
		response.setData(dbResponse);
		response.setStatus("success");
		response.setMessage("Records Updated Successfully.");

		LoggerUtil.log(this.getClass(), Level.INFO, "modifyEventHistInqConfig()3",
				"Successfully updated the EventHistInqConfig of id: " + eventConfigToModify.getId());

		try {
			return objectMapper.writeValueAsString(response);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "modifyEventHistInqConfigWithOutDuplicates()2",
					"TbaEventHistoryServiceImpl-->modifyEventHistInqConfigWithOutDuplicates()--Exception: "
							+ e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	/* Fetch the Details EventHistoryUI */

	@Override
	public List<EventHistoryUI> getEventHistInqConfigByPJMID(String pjmID) {
		List<EventHistInqConfig> tbaConfigList = null;
		String mydata = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByPJMID",
				"TbaEventHistoryServiceImpl--->Getting EventHistInqConfig by pjmID--->starts: ");
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByPJMID>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,PjmId=" + pjmID);
		if (pjmID == null || pjmID.isEmpty())
			throw new InvalidInputException("Invalid Input PJMID: " + pjmID);

		ParameterizedTypeReference<List<EventHistInqConfig>> retunType = new ParameterizedTypeReference<List<EventHistInqConfig>>() {
		};

		List<com.wipro.fipc.entity.tba.EventHistInqConfig> response = eventConfigTba.findByColumn(PJMID_COLUMN, pjmID);

		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			mydata = objectMapper.writeValueAsString(response);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}

		try {
			tbaConfigList = Arrays.asList(new ObjectMapper().readValue(mydata, EventHistInqConfig[].class));
		} catch (JsonParseException e) {
			e.printStackTrace();
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		List<EventHistoryUI> eventHistoryUIList = seteventHistoryUIFromConfig(tbaConfigList);
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByPJMID4",
				"TbaEventHistoryServiceImpl--->Getting EventHistInqConfig by pjmID--->ends: ");

		return eventHistoryUIList;
	}

	/* Get EventHistoryMaster ByClientID */

	@Override
	public List<EventHistoryMaster> getEventHistoryMasterByClientID(String clientID) {

		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistoryMasterByClientID",
				"TbaEventHistoryServiceImpl--->Getting EventHistoryMaster for clientID--->starts: ");
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistoryMasterByClientID>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET,clientID=" + clientID);
		String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientID));
		if (clientId == null || clientId.isEmpty())
			throw new InvalidInputException("Invalid Input clientId: " + clientId);

		List<EventHistoryMaster> response = new ArrayList<>();

		try {
			List<com.wipro.fipc.entity.tba.EventHistoryMaster> mydata = eventConfigTba
					.findRecordByColumn(CLIENTID_COLUMN, clientId);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			String respons = objectMapper.writeValueAsString(mydata);
			try {
				response = Arrays.asList(new ObjectMapper().readValue(respons, EventHistoryMaster[].class));
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}

			LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistoryMasterByClientID2",
					"EventHistoryMaster GET call response size : {}", response.size());

		} catch (

		Exception exception) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "getEventHistoryMasterByClientID3",
					"Exception occured while getting EventHistoryMaster List:", exception);
		}
		;
		return response;

	}

	/*
	 * Set EventHistInqConfig from EventHistoryUI class
	 */
	public EventHistInqConfig seteventHistInqConfigFromUI(EventHistoryUI tbaEventHistoryUI) {
		LoggerUtil.log(this.getClass(), Level.INFO, "seteventHistInqConfigFromUI()", "setting config from UI starts");

		EventHistInqConfig eventConfig = new EventHistInqConfig();

		try {
			BeanUtils.copyProperties(eventConfig, tbaEventHistoryUI);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "seteventHistInqConfigFromUI()1",
					"IllegalAccessException: " + e);
		} catch (InvocationTargetException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "seteventHistInqConfigFromUI()2",
					"InvocationTargetException: " + e);
		}

		eventConfig.setFiledType(tbaEventHistoryUI.getFieldType());
		eventConfig.setTbaFiledName(tbaEventHistoryUI.getTbaFieldName());

		String fromDate = tbaEventHistoryUI.getFromDate().getDatePeriod() + SPACE_SEPERATOR
				+ tbaEventHistoryUI.getFromDate().getDateFrequency() + SPACE_SEPERATOR
				+ tbaEventHistoryUI.getFromDate().getDateInterval();
		eventConfig.setEffFromDate(fromDate);

		String toDate = tbaEventHistoryUI.getToDate().getDatePeriod() + SPACE_SEPERATOR
				+ tbaEventHistoryUI.getToDate().getDateFrequency() + SPACE_SEPERATOR
				+ tbaEventHistoryUI.getToDate().getDateInterval();
		eventConfig.setEffToDate(toDate);

		LoggerUtil.log(this.getClass(), Level.INFO, "seteventHistInqConfigFromUI()", "setting config from UI ends");

		return eventConfig;

	}

	/*
	 * Set EventHistoryUI from config
	 */
	public List<EventHistoryUI> seteventHistoryUIFromConfig(List<EventHistInqConfig> eventHistInqConfigList) {

		LoggerUtil.log(this.getClass(), Level.INFO, "seteventHistoryUIFromConfig()", "setting UI from config starts");
		List<EventHistoryUI> uiList = new ArrayList<>();

		for (EventHistInqConfig eventConfig : eventHistInqConfigList) {
			EventHistoryUI eventUI = new EventHistoryUI();
			try {
				BeanUtils.copyProperties(eventUI, eventConfig);
			} catch (IllegalAccessException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "seteventHistoryUIFromConfig()1",
						"IllegalAccessException: " + e);
			} catch (InvocationTargetException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "seteventHistoryUIFromConfig()2",
						"InvocationTargetException: " + e);
			}

			eventUI.setFromDate(splitEffectiveDateBySpace(eventConfig.getEffFromDate()));
			eventUI.setToDate(splitEffectiveDateBySpace(eventConfig.getEffToDate()));
			eventUI.setFieldType(eventConfig.getFiledType());
			eventUI.setTbaFieldName(eventConfig.getTbaFiledName());

			uiList.add(eventUI);
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "seteventHistoryUIFromConfig()", "setting UI from config ends");
		return uiList;
	}

	public EventHistoryEffectiveDate splitEffectiveDateBySpace(String effectiveDateInString) {
		String[] dbList = effectiveDateInString.split(" ");
		EventHistoryEffectiveDate dateInObject = new EventHistoryEffectiveDate();

		dateInObject.setDatePeriod(dbList[0]);
		dateInObject.setDateFrequency(dbList[1]);
		dateInObject.setDateInterval(dbList[2]);

		return dateInObject;

	}

	/* Delete EventHistoryConfig Details */

	@Override
	public String deleteEventHistoryConfig(String entityList) {

		String res = "";
		List<com.wipro.fipc.entity.tba.EventHistInqConfig> uiList = new ArrayList<>();
		ObjectMapper mapper = new ObjectMapper();
		try {
			com.wipro.fipc.entity.tba.EventHistInqConfig[] obj = mapper.readValue(entityList,
					com.wipro.fipc.entity.tba.EventHistInqConfig[].class);
			uiList = new ArrayList<com.wipro.fipc.entity.tba.EventHistInqConfig>(Arrays.asList(obj));

		} catch (IOException e1) {
			e1.printStackTrace();
		}
		List<CommonRowBO> mydata = eventConfigTba.deletemultiplerows(1, uiList);
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		if (res.isEmpty()) {
			throw new BusinessException("URL is NULL");

		} else {
			return res;
		}
	}
}
