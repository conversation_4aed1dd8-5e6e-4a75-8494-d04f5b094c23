package com.wipro.fipc.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.batch.KsdMasterDao;
import com.wipro.fipc.model.generated.ExcelGeneratorBo;
import com.wipro.fipc.pojo.HWSBossKSDConfigReport;
import com.wipro.fipc.pojo.HWSBusinessKSDConfigReport;
import com.wipro.fipc.service.IKSDConfigReportGenerator;

@Service
public class KSDConfigReportGeneratorImpl implements IKSDConfigReportGenerator {

	private static final String GET_HWS_KSD_CONFIG_REPORT = "getHwsKsdConfigReport";
	private static final String WRITE_AND_SAVE_KSD_RECORDS = "writeAndSaveExcelKSDRecords";
	private static final String YYYY_MM_DD_HH = "yyyy-MM-dd HH:mm:ss.SSS";
	private static final String PJM_ID = "pjmId";
	private static final String CREATED_ON = "createdOn";
	private static final String UPDATED_ON = "updatedOn";
	private static final String APPROVED_ON = "approvedOn";
	private static final String WRITE_AND_SAVE_NON_BUSINESS_EXCEL_KSD_RECORDS = "writeAndSaveNonBusinessExcelKSDRecords";
	private static final String TBA_INQUIRY_ID = "tbaInquiryId";
	private static final String TBA_UPDATE_ID = "tbaUpdateId";
	private static final String RULES_ID = "rulesId";
	private static final String MAESTRO_TASK_ID = "maestroTaskId";
	private static final String MAESTRO_TICKET_ID = "maestroTicketId";
	private static final String LOTUS_NOTES_ID = "lotusNotesId";

	@Autowired
	Environment env;

	@Autowired
	KsdMasterDao ksdMasterDao;

	@Override
	public List<HWSBusinessKSDConfigReport> getHwsBusinessKsdConfigReport(String date, String time)
			throws ParseException {
		DateFormat oldFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		DateFormat newFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
		String dateTime = date + " " + time;
		Date date1 = oldFormat.parse(dateTime);
		dateTime = newFormat.format(date1);
		Date createdDate = newFormat.parse(dateTime);
		LoggerUtil.log(this.getClass(), Level.INFO, GET_HWS_KSD_CONFIG_REPORT,
				"formatted date: " + newFormat.format(createdDate));
		ExcelGeneratorBo excelGeneratorBoRequest = new ExcelGeneratorBo();
		excelGeneratorBoRequest.setCreatedDate(createdDate);
		LoggerUtil.log(this.getClass(), Level.INFO, "GET_HWS_KSD_CONFIG_REPORT",
				"excelGeneratorBoRequest: " + excelGeneratorBoRequest.toString());
		LoggerUtil.log(this.getClass(), Level.INFO, "getHwsExcelList",
				"excel generation by date in hws for business" + excelGeneratorBoRequest.getCreatedDate());
		List<HWSBusinessKSDConfigReport> excelList1 = ksdMasterDao
				.getHWSExcelList1(excelGeneratorBoRequest.getCreatedDate());
		List<HWSBusinessKSDConfigReport> excelList2 = ksdMasterDao
				.getHWSExcelList2(excelGeneratorBoRequest.getCreatedDate());
		List<HWSBusinessKSDConfigReport> finalList = new ArrayList<>();
		finalList.addAll(excelList1);
		finalList.addAll(excelList2);
		return finalList;
	}

	@Override
	public List<HWSBossKSDConfigReport> getHwsBossKsdConfigReport(String date, String time) throws ParseException {
		DateFormat oldFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		DateFormat newFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
		String dateTime = date + " " + time;
		Date date1 = oldFormat.parse(dateTime);
		dateTime = newFormat.format(date1);
		Date createdDate = newFormat.parse(dateTime);
		LoggerUtil.log(this.getClass(), Level.INFO, GET_HWS_KSD_CONFIG_REPORT,
				"formatted date: " + newFormat.format(createdDate));
		ExcelGeneratorBo excelGeneratorBoRequest = new ExcelGeneratorBo();
		excelGeneratorBoRequest.setCreatedDate(createdDate);
		LoggerUtil.log(this.getClass(), Level.INFO, "GET_HWS_KSD_CONFIG_REPORT",
				"excelGeneratorBoRequest: " + excelGeneratorBoRequest.toString());
		LoggerUtil.log(this.getClass(), Level.INFO, "getBossHWSExcel",
				"excel generation by date in hws for anind" + excelGeneratorBoRequest.getCreatedDate());

		List<HWSBossKSDConfigReport> excelList1 = ksdMasterDao
				.getBossHWSExcelList1(excelGeneratorBoRequest.getCreatedDate());
		List<HWSBossKSDConfigReport> excelList2 = ksdMasterDao
				.getBossHWSExcelList2(excelGeneratorBoRequest.getCreatedDate());
		List<HWSBossKSDConfigReport> finalList = new ArrayList<>();
		finalList.addAll(excelList1);
		finalList.addAll(excelList2);
		return finalList;
	}

	@Override
	public ByteArrayOutputStream writeAndSaveExcelKSDRecords(List<HWSBusinessKSDConfigReport> ksdRecordsListToSave,
			String fileLoadPath) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("dd'_'MMMM");
		String date = dateFormat.format(new Date());
		ByteArrayOutputStream stream = null;
		LoggerUtil.log(this.getClass(), Level.INFO, WRITE_AND_SAVE_KSD_RECORDS,
				"writeAndSaveExcelKSDRecords file path: " + fileLoadPath);
		File excelFile = new File(fileLoadPath);
		FileOutputStream fout = null;

		SimpleDateFormat oldFormat = new SimpleDateFormat(YYYY_MM_DD_HH);
		if (!(excelFile.exists())) {
			try (XSSFWorkbook hwsWorkbook = new XSSFWorkbook();) {
				stream = new ByteArrayOutputStream();
				String sheetName = "Status_" + date;
				XSSFSheet activeSheet = hwsWorkbook.createSheet(sheetName);
				CellStyle cellStyle = hwsWorkbook.createCellStyle();
				CreationHelper helper = hwsWorkbook.getCreationHelper();
				XSSFFont font = hwsWorkbook.createFont();
				font.setFontHeight(9);
				font.setFontName("Calibri");
				font.setColor(IndexedColors.BLACK.getIndex());

				String[] fieldsNames = setHeader();
				int rowCount = 0;
				int colCount = 0;
				Row row = activeSheet.createRow(rowCount++);
				setFieldName(fieldsNames, row, cellStyle, font, colCount);

				Class<? extends Object> classz = ksdRecordsListToSave.get(0).getClass();
				setHWSBusinessKSDConfigReport(ksdRecordsListToSave, activeSheet, font, cellStyle, oldFormat, helper,
						classz);

				fout = new FileOutputStream(excelFile);
				hwsWorkbook.write(fout);
				hwsWorkbook.write(stream);

				LoggerUtil.log(this.getClass(), Level.INFO, WRITE_AND_SAVE_KSD_RECORDS,
						"written hws business records successfully!!!");
			} catch (IOException | IllegalArgumentException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveExcelKSDRecords1",
						"Exception occured in writeAndSaveExcelKSDRecords1 :", e.getMessage());
			} finally {
				try {
					if (stream != null) {
						stream.close();
					}

					if (fout != null) {
						fout.close();
					}
				} catch (IOException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveExcelKSDRecords2",
							"Exception occured in writeAndSaveExcelKSDRecords2 :", e.getMessage());
				}
			}
		} else {
			stream = new ByteArrayOutputStream();
			LoggerUtil.log(this.getClass(), Level.INFO, WRITE_AND_SAVE_KSD_RECORDS, "File already exists!!!");
			try (FileInputStream fin = new FileInputStream(excelFile);
					XSSFWorkbook hwsWorkbook1 = new XSSFWorkbook(fin);) {
				hwsWorkbook1.write(stream);
			} catch (IOException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveExcelKSDRecords3",
						"Exception occured in writeAndSaveExcelKSDRecords3 :", e.getMessage());
			} finally {

				try {
					stream.close();
				} catch (IOException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveExcelKSDRecords4",
							"Exception occured in writeAndSaveExcelKSDRecords4 :", e.getMessage());
				}
			}
		}
		return stream;
	}

	String[] setHeader() {
		return new String[] { "businessUnit", "ksdName", "tower", "configStatus", "clientName", "clientCode",
				"processName", "processType", "ksdJobName", "ksdEftSubjectName", CREATED_ON, UPDATED_ON, APPROVED_ON,
				"createdBy", "updatedBy", "approvedBy", "userGroup", "userGroupStatus", "business" };

	}

	void setFieldName(String[] fieldsNames, Row row, CellStyle cellStyle, XSSFFont font, int colCount) {

		for (String field : fieldsNames) {
			font.setBold(true);
			cellStyle.setFillBackgroundColor(IndexedColors.AQUA.getIndex());
			cellStyle.setFont(font);
			cellStyle.setAlignment(HorizontalAlignment.CENTER);
			Cell cell = row.createCell(colCount++);
			field = field.trim().replaceAll("([^_A-Z])([A-Z])", "$1 $2");
			field = field.substring(0, 1).toUpperCase() + field.substring(1);
			cell.setCellValue(field);
			cell.setCellStyle(cellStyle);
		}
	}

	void setHWSBusinessKSDConfigReport(List<HWSBusinessKSDConfigReport> ksdRecordsListToSave, XSSFSheet activeSheet,
			XSSFFont font, CellStyle cellStyle, SimpleDateFormat oldFormat, CreationHelper helper,
			Class<? extends Object> classz) {

		int rowCount = 1;
		String[] fieldsNames = setHeader();
		for (HWSBusinessKSDConfigReport ksdConfig : ksdRecordsListToSave) {
			Row row = activeSheet.createRow(rowCount++);
			font.setBold(false);
			cellStyle.setFont(font);
			try {
				setKsdValue(ksdConfig, row, cellStyle, fieldsNames, oldFormat, helper, classz);
			} catch (IllegalAccessException | InvocationTargetException e) {

				LoggerUtil.log(this.getClass(), Level.ERROR, "setHWSBusinessKSDConfigReport",
						"Exception occured in setHWSBusinessKSDConfigReport :", e.getMessage());
			}
		}
	}

	void setKsdValue(HWSBusinessKSDConfigReport ksdConfig, Row row, CellStyle cellStyle, String[] fieldsNames,
			SimpleDateFormat oldFormat, CreationHelper helper, Class<? extends Object> classz)
			throws IllegalAccessException, InvocationTargetException {

		int colCount = 0;
		for (String field : fieldsNames) {
			cellStyle.setAlignment(HorizontalAlignment.LEFT);
			Cell cell = row.createCell(colCount);
			Method method = null;
			try {
				method = classz.getMethod("get" + capitalize(field));
			} catch (NoSuchMethodException n) {
				try {
					method = classz.getMethod("get" + field);
				} catch (NoSuchMethodException | SecurityException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "setKsdValue", "Exception occured in setKsdValue :",
							e.getMessage());
				}
				LoggerUtil.log(this.getClass(), Level.INFO, "setKsdValue", "NoSuchMethodException" + n.getCause());
			}
			Object objValue = null;
			if (method != null) {
				objValue = method.invoke(ksdConfig, (Object[]) null);
			}

			setObjValue(objValue, field, cellStyle, cell, oldFormat, helper);
			colCount++;
		}

	}

	void setObjValue(Object objValue, String field, CellStyle cellStyle, Cell cell, SimpleDateFormat oldFormat,
			CreationHelper helper) {

		if (objValue != null && !(field.equalsIgnoreCase(PJM_ID))) {
			if (objValue instanceof String) {
				cell.setCellValue((String) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Integer) {
				cell.setCellValue((Integer) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Long) {
				cell.setCellValue((Long) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Double) {
				cell.setCellValue((Double) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Date && (field.equalsIgnoreCase(APPROVED_ON)
					|| field.equalsIgnoreCase(UPDATED_ON) || field.equalsIgnoreCase(CREATED_ON))) {
				String objVal = oldFormat.format(objValue);
				Date objDate = null;
				try {
					objDate = oldFormat.parse(objVal);
				} catch (ParseException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "setObjValue", "Exception occured in setObjValue :",
							e.getMessage());
				}
				cellStyle.setDataFormat(helper.createDataFormat().getFormat(YYYY_MM_DD_HH));
				cell.setCellStyle(cellStyle);
				cell.setCellValue(DateUtil.getExcelDate(objDate));
			}
		} else if (objValue != null && field.equalsIgnoreCase(PJM_ID)) {
			cell.setCellValue((Long) objValue);
		}
	}

	/*
	 * anindito's HWS report generation
	 */
	@Override
	public ByteArrayOutputStream writeAndSaveNonBusinessExcelKSDRecords(
			List<HWSBossKSDConfigReport> ksdRecordsListToSave, String fileLoadPath) {
		ByteArrayOutputStream stream = null;
		LoggerUtil.log(this.getClass(), Level.INFO, WRITE_AND_SAVE_NON_BUSINESS_EXCEL_KSD_RECORDS,
				"writeAndSaveNonBusinessExcelKSDRecords file path: " + fileLoadPath);
		File excelFile = new File(fileLoadPath);
		FileOutputStream fout = null;

		SimpleDateFormat oldFormat = new SimpleDateFormat(YYYY_MM_DD_HH);
		if (!(excelFile.exists())) {
			try (XSSFWorkbook hwsWorkbook = new XSSFWorkbook();) {
				stream = new ByteArrayOutputStream();
				XSSFSheet activeSheet = hwsWorkbook.createSheet("Sheet1");
				CellStyle cellStyle = hwsWorkbook.createCellStyle();
				CreationHelper helper = hwsWorkbook.getCreationHelper();
				XSSFFont font = hwsWorkbook.createFont();
				font.setFontHeight(9);
				font.setFontName("Calibri");
				font.setColor(IndexedColors.BLACK.getIndex());
				String[] fieldsNames = setNonBusinessHeader();

				int rowCount = 0;
				int colCount = 0;
				Row row = activeSheet.createRow(rowCount++);
				setNonBusinessFieldName(fieldsNames, row, cellStyle, font, colCount);

				Class<? extends Object> classz = ksdRecordsListToSave.get(0).getClass();
				setHWSBossKSDConfigReport(ksdRecordsListToSave, activeSheet, font, cellStyle, oldFormat, helper,
						classz);

				fout = new FileOutputStream(excelFile);
				hwsWorkbook.write(fout);
				hwsWorkbook.write(stream);

				LoggerUtil.log(this.getClass(), Level.INFO, WRITE_AND_SAVE_NON_BUSINESS_EXCEL_KSD_RECORDS,
						"written hws non business records successfully!!!");
			} catch (IOException | IllegalArgumentException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveNonBusinessExcelKSDRecords1",
						"Exception occured in writeAndSaveNonBusinessExcelKSDRecords1 :", e.getMessage());
			} finally {
				try {
					if (stream != null) {
						stream.close();
					}
					if (fout != null) {
						fout.close();
					}
				} catch (IOException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveNonBusinessExcelKSDRecords2",
							"Exception occured in writeAndSaveNonBusinessExcelKSDRecords2 :", e.getMessage());
				}
			}
		} else {
			stream = new ByteArrayOutputStream();
			LoggerUtil.log(this.getClass(), Level.INFO, WRITE_AND_SAVE_NON_BUSINESS_EXCEL_KSD_RECORDS,
					"File already exists!!!");
			try (FileInputStream fin = new FileInputStream(excelFile);
					XSSFWorkbook hwsWorkbook1 = new XSSFWorkbook(fin);) {

				hwsWorkbook1.write(stream);
			} catch (IOException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveNonBusinessExcelKSDRecords3",
						"Exception occured in writeAndSaveNonBusinessExcelKSDRecords3 :", e.getMessage());
			} finally {
				try {
					stream.close();
				} catch (IOException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "writeAndSaveNonBusinessExcelKSDRecords4",
							"Exception occured in writeAndSaveNonBusinessExcelKSDRecords4 :", e.getMessage());
				}
			}
		}
		return stream;
	}

	String[] setNonBusinessHeader() {
		return new String[] { "businessUnit", "ksdName", "tower", PJM_ID, "configStatus", "clientName", "clientCode",
				"processName", "processType", "ksdJobName", "ksdEftSubjectName", CREATED_ON, UPDATED_ON, APPROVED_ON,
				"createdBy", "updatedBy", "approvedBy", TBA_INQUIRY_ID, TBA_UPDATE_ID, RULES_ID, MAESTRO_TASK_ID,
				MAESTRO_TICKET_ID, LOTUS_NOTES_ID, "owner" };
	}

	void setNonBusinessFieldName(String[] fieldsNames, Row row, CellStyle cellStyle, XSSFFont font, int colCount) {

		for (String field : fieldsNames) {
			font.setBold(true);
			cellStyle.setFillBackgroundColor(IndexedColors.AQUA.getIndex());

			cellStyle.setFont(font);
			cellStyle.setAlignment(HorizontalAlignment.CENTER);
			Cell cell = row.createCell(colCount++);
			field = field.trim().replaceAll("([^_A-Z])([A-Z])", "$1_$2");
			field = field.toLowerCase();
			cell.setCellValue(field);
			cell.setCellStyle(cellStyle);
		}
	}

	void setHWSBossKSDConfigReport(List<HWSBossKSDConfigReport> ksdRecordsListToSave, XSSFSheet activeSheet,
			XSSFFont font, CellStyle cellStyle, SimpleDateFormat oldFormat, CreationHelper helper,
			Class<? extends Object> classz) {
		int rowCount = 1;
		String[] fieldsNames = setNonBusinessHeader();
		for (HWSBossKSDConfigReport ksdConfig : ksdRecordsListToSave) {
			Row row = activeSheet.createRow(rowCount++);
			font.setBold(false);
			cellStyle.setFont(font);
			try {
				setNonBusinessKsdValue(ksdConfig, row, cellStyle, fieldsNames, oldFormat, helper, classz);
			} catch (IllegalAccessException | InvocationTargetException e) {

				LoggerUtil.log(this.getClass(), Level.ERROR, "setHWSBossKSDConfigReport",
						"Exception occured in setHWSBossKSDConfigReport :", e.getMessage());
			}

		}
	}

	void setNonBusinessKsdValue(HWSBossKSDConfigReport ksdConfig, Row row, CellStyle cellStyle, String[] fieldsNames,
			SimpleDateFormat oldFormat, CreationHelper helper, Class<? extends Object> classz)
			throws IllegalAccessException, InvocationTargetException {

		int colCount = 0;
		for (String field : fieldsNames) {
			cellStyle.setAlignment(HorizontalAlignment.LEFT);
			Cell cell = row.createCell(colCount);
			Method method = null;
			try {
				method = classz.getMethod("get" + capitalize(field));
			} catch (NoSuchMethodException n) {
				try {
					method = classz.getMethod("get" + field);
				} catch (NoSuchMethodException | SecurityException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "setNonBusinessKsdValue1",
							"Exception occured in setNonBusinessKsdValue :", e.getMessage());
				}
				LoggerUtil.log(this.getClass(), Level.INFO, "setNonBusinessKsdValue",
						"NoSuchMethodException" + n.getCause());
			}
			Object objValue = null;
			if (method != null) {
				objValue = method.invoke(ksdConfig, (Object[]) null);
			}
			setNonBusinessObjValue(objValue, field, cellStyle, cell, oldFormat, helper);
			colCount++;
		}
	}

	void setNonBusinessObjValue(Object objValue, String field, CellStyle cellStyle, Cell cell,
			SimpleDateFormat oldFormat, CreationHelper helper) {

		setInetgerValue(objValue, field, cell);
		if (!(field.equalsIgnoreCase(PJM_ID) || field.equalsIgnoreCase(TBA_INQUIRY_ID)
				|| field.equalsIgnoreCase(TBA_UPDATE_ID) || field.equalsIgnoreCase(RULES_ID)
				|| field.equalsIgnoreCase(MAESTRO_TASK_ID) || field.equalsIgnoreCase(MAESTRO_TICKET_ID)
				|| field.equalsIgnoreCase(LOTUS_NOTES_ID))) {
			if (objValue instanceof String) {
				cell.setCellValue((String) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Integer) {
				cell.setCellValue((Integer) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Long) {
				cell.setCellValue((Long) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Double) {
				cell.setCellValue((Double) objValue);
				cell.setCellStyle(cellStyle);
			} else if (objValue instanceof Date && (field.equalsIgnoreCase(APPROVED_ON)
					|| field.equalsIgnoreCase(UPDATED_ON) || field.equalsIgnoreCase(CREATED_ON))) {
				String objVal = oldFormat.format(objValue);
				Date objDate = null;
				try {
					objDate = oldFormat.parse(objVal);
				} catch (ParseException e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "setNonBusinessObjValue",
							"Exception occured in setNonBusinessObjValue :", e.getMessage());
				}
				cellStyle.setDataFormat(helper.createDataFormat().getFormat(YYYY_MM_DD_HH));
				cell.setCellStyle(cellStyle);
				cell.setCellValue(DateUtil.getExcelDate(objDate));
			} else {
				cell.setCellValue("");
			}
		}
	}

	void setInetgerValue(Object objValue, String field, Cell cell) {

		if (objValue != null && (field.equalsIgnoreCase(PJM_ID) || field.equalsIgnoreCase(TBA_INQUIRY_ID)
				|| field.equalsIgnoreCase(TBA_UPDATE_ID) || field.equalsIgnoreCase(RULES_ID)
				|| field.equalsIgnoreCase(MAESTRO_TASK_ID) || field.equalsIgnoreCase(MAESTRO_TICKET_ID)
				|| field.equalsIgnoreCase(LOTUS_NOTES_ID))) {
			cell.setCellValue((Long) objValue);
		}
	}

	public List<String> getFieldNamesFromClass(Class<?> clazz) {
		List<String> fieldNames = new ArrayList<>();
		Field[] fields = clazz.getDeclaredFields();
		for (int i = 0; i < fields.length; i++) {
			fieldNames.add(fields[i].getName());
		}
		return fieldNames;
	}

	public String capitalize(String s) {
		if (s.length() == 0)
			return s;
		return s.substring(0, 1).toUpperCase() + s.substring(1);
	}

}
