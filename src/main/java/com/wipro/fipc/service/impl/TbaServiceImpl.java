package com.wipro.fipc.service.impl;

import org.springframework.stereotype.Service;

import com.wipro.fipc.model.JsonKey;
import com.wipro.fipc.model.UITemplate;
import com.wipro.fipc.service.TbaService;

@Service
public class TbaServiceImpl implements TbaService {

	@Override
	public String dbSubmission(UITemplate uiTemplate) {

		JsonKey jsonKey = new JsonKey();
		
		jsonKey.setJsonKey(uiTemplate.getJsonKey());
		jsonKey.setPanelId(uiTemplate.getPanelId());
		jsonKey.setSubJsonKey(uiTemplate.getSubJsonKey());
		
		return null;
	}

}
