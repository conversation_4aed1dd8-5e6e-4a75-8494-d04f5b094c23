package com.wipro.fipc.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import com.wipro.fipc.dao.batch.HolidayCalendarDao;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.KsdConfigDao;
import com.wipro.fipc.dao.KsdFileDetailsDao;
import com.wipro.fipc.dao.filelayout.ProcessControlConfigDao;
import com.wipro.fipc.dao.filelayout.RulesConfigDao;
import com.wipro.fipc.dao.filelayout.RulesDefinitionDao;
import com.wipro.fipc.dao.filelayout.TbaMatchConfigDao;
import com.wipro.fipc.dao.layoutrule.LayoutConfigDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.batch.ClientConfigJobs;
import com.wipro.fipc.entity.batch.HolidayCalendar;
import com.wipro.fipc.entity.batch.JobSchedule;
import com.wipro.fipc.entity.batch.KsdConfig;
import com.wipro.fipc.entity.batch.KsdMaster;
import com.wipro.fipc.entity.filelayout.ProcessFeatureConfig;
import com.wipro.fipc.entity.batch.KsdFileDetails;
import com.wipro.fipc.exception.KsdBatchException;
import com.wipro.fipc.model.JobScheduleTimeDto;
import com.wipro.fipc.model.generated.ModelApiResponse;
import com.wipro.fipc.service.IKsdBatchService;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;

/**
 * <AUTHOR> TbaUpdateConfig
 */
@Service
public class KsdBatchServiceImpl implements IKsdBatchService {

	public static final String PRIMARY_JOB_NAME = "primary_job_name";
	public static final String PROCESS_JOB_MAPPING_ID = "process_job_mapping_id";
	public static final char ACTIVE_FLAG_TRUE = 'T';
	public static final char ACTIVE_FLAG_FALSE = 'F';
	public static final String METHOD_NAME = "methodName";
	public static final String CONTENT_TYPE = "Content-Type";
	public static final String APPLICATION_JSON = "application/json";
	public static final String UPDATE_KSD_CHILD_DETAILS = "updateKsdAndChildDetails";
	public static final String UPDATE_KSD_CONFIG_PATH = "update.ksdconfig.path";
	public static final String KSD_DELETE_ALLCONFIG = "ksd.delete.allconfig";
	public static final String DELETE_ALL_CONFIGS_FROM_SCREEN = "deleteAllConfigsFromScreen";
	public static final String UPDATE_KSD_AND_CLIENT_CONFIG_DETAILS = "updateKsdAndClientConfigDetails";

	@Autowired
	Environment env;

	@Autowired
	private BaseDao<ProcessFeatureConfig> pfcdao;

	@Autowired
	private KsdFileDetailsDao ksdFileDetailsDao;
	@Autowired
	private KsdConfigDao ksdDao;
	@Autowired
	private ProcessControlConfigDao processControlConfigDao;
	@Autowired
	private LayoutConfigDao layoutConfigDao;
	@Autowired
	private TbaMatchConfigDao tbaMatchConfigDao;

	@Autowired
	private GenericDao<KsdMaster> genericKsdMasterDao;
	@Autowired
	private RulesDefinitionDao rulesDefinitionDao;

	@Autowired
	private RulesConfigDao rulesConfigDao;
	@Autowired
	private BaseDao<KsdConfig> ksdConfigDao;

	@Autowired
	private BaseDao<HolidayCalendar> holiDayDao;

	@Autowired
	private GenericDao<KsdConfig> genericKsdConfigDao;
	
	@Autowired
	private GenericDao<HolidayCalendar> genericHolidayDao;

	@Autowired
	private ProcessConfigurationServiceImpl processConfigServiceImpl;
	
	@Autowired
	private CommonGetAdId commonGetUpdatedBy;

	@Autowired
	HolidayCalendarDao holidayRepo;

	protected static final String KSD_CONFIG = "ksd_config";
	protected static final String HOLIDAY_CALENDAR = "HOLIDAY_CALENDAR";
	protected static final String EMAILS_SCHEDULER_SCHEMA = "emails_scheduler";
	protected static final String KSD_MASTER = "ksd_master";

	/**
	 * 
	 * saving Ksd and Child Details
	 * 
	 * @param ksdConfig
	 * @return
	 * @throws KsdBatchException, JsonProcessingException
	 * 
	 */
	@Override
	public String saveKsdAndChildDetails(KsdConfig ksdConfig) throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveKsdAndChildDetails", "inside saveKsdAndChildDetails method");
		KsdConfig save = null;

		try {
			if (ksdConfig.getDirectTBAUpdate() == null)
				ksdConfig.setDirectTBAUpdate(true);
			save = ksdConfigDao.save(ksdConfig);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "saveKsdAndChildDetails", "Exception1 : ", e.getMessage());
		}
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(save);

	}

	/**
	 * updating Ksd and Child Details
	 * 
	 * @param processJobmappingId
	 * @param ksdConfigDetails
	 * @return
	 * @throws KsdBatchException,JsonProcessingException
	 */
	@Override
	public String updateKsdAndChildDetails(String processJobmappingId, KsdConfig ksdConfigDetails)
			throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateKsdAndChildDetails",
				"inside updateKsdAndChildDetails method");
		KsdConfig config = null;
		List<KsdConfig> ksdMasterConfigList = getKsdAndChildDetails(PROCESS_JOB_MAPPING_ID,
				String.valueOf(ksdConfigDetails.getProcessJobMappingId()));
		Set<String> jobStreamAsSet = new HashSet<>();
		if (ksdMasterConfigList != null && !ksdMasterConfigList.isEmpty()) {
			KsdConfig ksdConfig = ksdMasterConfigList.get(0);
			List<ClientConfigJobs> clientConfigJobsDB = ksdConfig.getClientConfigJobs();
			List<ClientConfigJobs> clientConfigJobsUI = ksdConfigDetails.getClientConfigJobs();
			String jobStreamRequestJson = ksdConfigDetails.getJobStream();

			if ((jobStreamRequestJson == null || jobStreamRequestJson.isEmpty() || jobStreamRequestJson.equals(""))
					&& !clientConfigJobsUI.isEmpty()) {
				LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_CHILD_DETAILS,
						"JobStream is null,Child need to be delete");
				deleteChildJobs(ksdConfigDetails, clientConfigJobsUI);

			} else if (jobStreamRequestJson != null && ksdConfigDetails.getJobStream() != null
					&& !(ksdConfigDetails.getJobStream().isEmpty())) {
				String[] childJobsArrayRequestJson = jobStreamRequestJson.split(",");
				List<String> jobStramAsList = Arrays.asList(childJobsArrayRequestJson);

				jobStreamAsSet = addJobStreamAsSet(jobStramAsList, jobStreamAsSet);

				List<String> listChildJobs = new ArrayList<>();
				listChildJobs = addChildJobInList(clientConfigJobsUI, listChildJobs);

				ksdConfigDetails = addClientConfigJob(jobStreamRequestJson, clientConfigJobsDB, clientConfigJobsUI,
						jobStreamAsSet, listChildJobs, ksdConfigDetails);

			}
			ksdConfigDetails = updateScheduleTimeInKsdConfigDetails(ksdConfig, ksdConfigDetails);

		}
		String jobStream = String.join(",", jobStreamAsSet);
		ksdConfigDetails.setJobStream(jobStream);
		ksdConfigDetails.setJobSchedules(ksdConfigDetails.getJobSchedules());
		ksdConfigDetails.setUpdatedDate(new Date());
		ksdConfigDetails.setUpdatedDate(new Date());
		LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_CHILD_DETAILS, "request to update ksdconfig record:  ");

		try {
			config = ksdConfigDao.save(ksdConfigDetails);
		} catch (Exception e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, UPDATE_KSD_CHILD_DETAILS,
					"Exception in updateKsdAndChildDetails : " + e.getMessage());

		}
		LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_CHILD_DETAILS,
				"response from ksdconfig update: " + new ObjectMapper().writeValueAsString(config));
		List<ProcessFeatureConfig> processFeatureConfigRecords = processConfigServiceImpl
				.getAllDetailsPJMID(String.valueOf(ksdConfigDetails.getProcessJobMapping().getId()));
		if (processFeatureConfigRecords != null && !processFeatureConfigRecords.isEmpty()) {
			ProcessFeatureConfig processFeatureConfig = processFeatureConfigRecords.get(0);
			processFeatureConfig.setUpdatedBy(ksdConfigDetails.getUpdatedBy());
			processFeatureConfig.setUpdatedDate(new Date());
			LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_CHILD_DETAILS, "Request to update pfcRecord: ");
			String pfcDetailsUpdateResponse = updateProcessFeatureConfigDetails(processFeatureConfig,
					processFeatureConfig.getId());
			if (pfcDetailsUpdateResponse != null && (!pfcDetailsUpdateResponse.isEmpty())) {
				LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_CHILD_DETAILS,
						"Response from processFeatureConfig,after update: " + pfcDetailsUpdateResponse.length());
			}
		}
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(config);
	}

	/**
	 * Deleted child job
	 * 
	 * @param ksdConfigDetails
	 * @param clientConfigJobsUI
	 */
	public void deleteChildJobs(KsdConfig ksdConfigDetails, List<ClientConfigJobs> clientConfigJobsUI) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteChildJobs", "inside deleteChildJobs method");
		for (ClientConfigJobs clientConfigJobs : clientConfigJobsUI) {
			clientConfigJobs.setActiveFlag(ACTIVE_FLAG_FALSE);
		}
		ksdConfigDetails.setClientConfigJobs(clientConfigJobsUI);
	}

	/**
	 * Adding Job Stream as Set
	 * 
	 * @param jobStramAsList
	 * @param jobStreamAsSet
	 * @return
	 */
	public Set<String> addJobStreamAsSet(List<String> jobStramAsList, Set<String> jobStreamAsSet) {

		LoggerUtil.log(this.getClass(), Level.INFO, "addJobStreamAsSet", "Adding job stream as a set: ");
		for (String jobStream : jobStramAsList) {
			jobStreamAsSet.add(jobStream);
		}
		return jobStreamAsSet;
	}

	/**
	 * Adding Child job in List
	 * 
	 * @param clientConfigJobsUI
	 * @param listChildJobs
	 * @return
	 */
	public List<String> addChildJobInList(List<ClientConfigJobs> clientConfigJobsUI, List<String> listChildJobs) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addChildJobInList", "Adding child job in the list: ");
		if (clientConfigJobsUI != null && !clientConfigJobsUI.isEmpty()) {
			for (ClientConfigJobs eachclientConfigJobs : clientConfigJobsUI) {
				String childJobName = eachclientConfigJobs.getChildJobName();
				listChildJobs.add(childJobName);
			}
		}
		return listChildJobs;
	}

	/**
	 * Adding Client Config job
	 * 
	 * @param jobStreamRequestJson
	 * @param clientConfigJobsDB
	 * @param clientConfigJobsUI
	 * @param jobStreamAsSet
	 * @param listChildJobs
	 * @param ksdConfigDetails
	 * @return
	 */
	public KsdConfig addClientConfigJob(String jobStreamRequestJson, List<ClientConfigJobs> clientConfigJobsDB,
			List<ClientConfigJobs> clientConfigJobsUI, Set<String> jobStreamAsSet, List<String> listChildJobs,
			KsdConfig ksdConfigDetails) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addClientConfigJob",
				"Adding clientConfigJob in ksdConfigDetails: ");
		if (!(jobStreamRequestJson == null || jobStreamRequestJson.isEmpty())
				&& (clientConfigJobsDB == null || clientConfigJobsDB.isEmpty())) {
			ksdConfigDetails = addNewChildJob(jobStreamAsSet, ksdConfigDetails);
		} else if ((jobStreamRequestJson == null || jobStreamRequestJson.isEmpty() || jobStreamRequestJson.equals(""))
				&& (clientConfigJobsUI != null && !clientConfigJobsUI.isEmpty())) {
			for (ClientConfigJobs clientConfigJobs : clientConfigJobsUI) {
				clientConfigJobs.setActiveFlag(ACTIVE_FLAG_FALSE);
			}
			ksdConfigDetails.setClientConfigJobs(clientConfigJobsUI);
		} else if (jobStreamAsSet.size() != listChildJobs.size()) {
			ksdConfigDetails = addUnequalChildJob(clientConfigJobsUI, jobStreamAsSet, ksdConfigDetails);
		} else if (jobStreamAsSet.size() == listChildJobs.size()) {
			ksdConfigDetails = addEqualChildJob(clientConfigJobsUI, listChildJobs, jobStreamAsSet, ksdConfigDetails);
		}
		return ksdConfigDetails;
	}

	/**
	 * Adding new Child job
	 * 
	 * @param jobStreamAsSet
	 * @param ksdConfigDetails
	 * @return
	 */
	public KsdConfig addNewChildJob(Set<String> jobStreamAsSet, KsdConfig ksdConfigDetails) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addNewChildJob",
				"Adding new child job in ksdConfigDetails when clientConfigJobsDB is empty: ");
		List<ClientConfigJobs> newClientConfigRecords = new ArrayList<>();
		for (String childJobInClientConfig : jobStreamAsSet) {
			ClientConfigJobs clientConfigRecord = new ClientConfigJobs();
			clientConfigRecord.setProcessJobMappingId(ksdConfigDetails.getProcessJobMappingId());
			clientConfigRecord.setChildJobName(childJobInClientConfig);
			clientConfigRecord.setActiveFlag(ACTIVE_FLAG_TRUE);
			clientConfigRecord.setCreatedDate(new Date());
			clientConfigRecord.setCreatedBy(ksdConfigDetails.getCreatedBy());
			newClientConfigRecords.add(clientConfigRecord);
		}
		ksdConfigDetails.setClientConfigJobs(newClientConfigRecords);
		return ksdConfigDetails;
	}

	/**
	 * Adding Unequal Child job
	 * 
	 * @param clientConfigJobsUI
	 * @param jobStreamAsSet
	 * @param ksdConfigDetails
	 * @return
	 */
	public KsdConfig addUnequalChildJob(List<ClientConfigJobs> clientConfigJobsUI, Set<String> jobStreamAsSet,
			KsdConfig ksdConfigDetails) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addUnequalChildJob",
				"Adding new child job and deleting old child job in ksdConfigDetails: ");

		for (ClientConfigJobs clientConfigJobs : clientConfigJobsUI) {
			clientConfigJobs.setActiveFlag(ACTIVE_FLAG_FALSE);
		}
		for (String childJobInClientConfig : jobStreamAsSet) {
			ClientConfigJobs clientConfigRecord = new ClientConfigJobs();
			clientConfigRecord.setProcessJobMappingId(ksdConfigDetails.getProcessJobMappingId());
			clientConfigRecord.setChildJobName(childJobInClientConfig);
			clientConfigRecord.setActiveFlag(ACTIVE_FLAG_TRUE);
			clientConfigRecord.setCreatedDate(new Date());
			clientConfigRecord.setCreatedBy(ksdConfigDetails.getCreatedBy());

			clientConfigJobsUI.add(clientConfigRecord);

		}

		ksdConfigDetails.setClientConfigJobs(clientConfigJobsUI);
		return ksdConfigDetails;
	}

	/**
	 * Adding equal child job
	 * 
	 * @param clientConfigJobsUI
	 * @param listChildJobs
	 * @param jobStreamAsSet
	 * @param ksdConfigDetails
	 * @return
	 */
	public KsdConfig addEqualChildJob(List<ClientConfigJobs> clientConfigJobsUI, List<String> listChildJobs,
			Set<String> jobStreamAsSet, KsdConfig ksdConfigDetails) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addEqualChildJob",
				"Adding new child job and deleting old child job in ksdConfigDetails when size is equal: ");
		int jobCount = 0;
		Iterator<String> itr = jobStreamAsSet.iterator();
		while (itr.hasNext()) {
			String job = itr.next();
			for (String childJob : listChildJobs) {
				if (job.equals(childJob))
					jobCount++;
			}
		}
		if (jobCount != listChildJobs.size()) {
			for (ClientConfigJobs clientConfigJobs : clientConfigJobsUI) {
				clientConfigJobs.setActiveFlag(ACTIVE_FLAG_FALSE);
			}
			for (String childJobInClientConfig : jobStreamAsSet) {
				ClientConfigJobs clientConfigRecord = new ClientConfigJobs();
				clientConfigRecord.setProcessJobMappingId(ksdConfigDetails.getProcessJobMappingId());
				clientConfigRecord.setChildJobName(childJobInClientConfig);
				clientConfigRecord.setActiveFlag(ACTIVE_FLAG_TRUE);
				clientConfigRecord.setCreatedDate(new Date());
				clientConfigRecord.setCreatedBy(ksdConfigDetails.getCreatedBy());

				clientConfigJobsUI.add(clientConfigRecord);
			}
			ksdConfigDetails.setClientConfigJobs(clientConfigJobsUI);

		}
		return ksdConfigDetails;
	}

	/**
	 * Update Schedule Time in Ksd Config details
	 * 
	 * @param ksdConfig
	 * @param ksdConfigDetails
	 * @return
	 */
	public KsdConfig updateScheduleTimeInKsdConfigDetails(KsdConfig ksdConfig, KsdConfig ksdConfigDetails) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateScheduleTimeInKsdConfigDetails",
				"Updating jobSchedule in ksdConfigDetails: ");
		if (!(ksdConfig.getJobSchedules() == null || ksdConfig.getJobSchedules().isEmpty())) {
			ksdConfigDetails.getJobSchedules().get(0).setUpdatedDate(new Date());
		} else if (ksdConfigDetails.getJobSchedules().get(0).getFrequency() != null
				&& (ksdConfig.getJobSchedules() == null || ksdConfig.getJobSchedules().isEmpty())) {
			ksdConfigDetails.getJobSchedules().get(0).setCreatedDate(new Date());
			ksdConfigDetails.getJobSchedules().get(0).setUpdatedBy(null);
			ksdConfigDetails.getJobSchedules().get(0).setUpdatedDate(null);
		}
		return ksdConfigDetails;
	}

	/**
	 * updating JobSchedule Time ksd config
	 * 
	 * @param id
	 * @param jobScheduleTimeDto
	 */
	@Override
	public String updateJobScheduleTime(long id, JobScheduleTimeDto jobScheduleTimeDto) throws KsdBatchException {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateJobScheduleTime", "updating JobSchedule Time ksd config");
		boolean flag = false;
		int noOfRetries = 4;
		try {

			ksdDao.updateJobScheduleTime(id, jobScheduleTimeDto.getJobScheduleTime());
			flag = true;
			LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME, "Retry Put call Response : {} ", "");

		} catch (Exception e) {
			flag = false;
			LoggerUtil.log(this.getClass(), Level.ERROR, METHOD_NAME, " Error in Put call: ", e.getCause());
			if (--noOfRetries == 0) {
				throw new KsdBatchException(" Error in Put call: " + e);

			}
		}
		JsonObject jsonObject = new JsonObject();
		if (flag) {
			jsonObject.addProperty("Status", "Success");
		} else {
			jsonObject.addProperty("Status", "Failed");
		}
		return jsonObject.toString();

	}

	/**
	 * Sending response to updateJobScheduleTime
	 * 
	 * @param response
	 * @return
	 */
	public String returnReponse(ResponseEntity<String> response) {
		LoggerUtil.log(this.getClass(), Level.INFO, "returnReponse", "sending response to updateJobScheduleTime: ");
		String updateResponse = "";
		if (response != null && (response.getBody() == null || response.getBody().isEmpty())
				&& response.getStatusCode().is2xxSuccessful()) {
			updateResponse = "success";
		} else {
			updateResponse = "failed";
		}
		return updateResponse;
	}

	/**
	 * Update Process Feature Config details
	 * 
	 * @param pfcRecord
	 * @param pfcId
	 * @return
	 * @throws JsonProcessingException
	 */
	public String updateProcessFeatureConfigDetails(ProcessFeatureConfig pfcRecord, Long pfcId)
			throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateProcessFeatureConfigDetails",
				"updating ProcessFeatureConfigDetails");

		ProcessFeatureConfig featureConfig = null;
		try {
			featureConfig = pfcdao.save(pfcRecord);

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateProcessFeatureConfigDetails1",
					"Exception in updateProcessFeatureConfigDetails1 : " + e.getMessage());

		}
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(featureConfig);
	}

	/**
	 * getting KsdAndChildDetails
	 * 
	 * @param columnName
	 * @param columnValue
	 * @return
	 * @throws KsdBatchException
	 */
	@Override
	public List<KsdConfig> getKsdAndChildDetails(String columnName, String columnValue) throws KsdBatchException {

		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdAndChildDetails", "getting KsdAndChildDetails");
		List<KsdConfig> populatedKsdRecordsForUI = null;

		try {

			populatedKsdRecordsForUI = genericKsdConfigDao.findByColumn(KsdConfig.class, EMAILS_SCHEDULER_SCHEMA,
					KSD_CONFIG, columnName, columnValue);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getKsdAndChildDetails",
					"Exception in getKsdAndChildDetails :" + e.getMessage());
		}
		return populatedKsdRecordsForUI;
	}

	/**
	 * save HolidayCalendarDetails
	 * 
	 * @param holidayCalendar
	 * @return
	 * @throws KsdBatchException,JsonProcessingException
	 * 
	 */
	@Override
	public String saveHolidayCalendarDetails(HolidayCalendar holidayCalendar)
			throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveHolidayCalendarDetails", "save HolidayCalendarDetails");
		HolidayCalendar save = null;
		try {
			save = holiDayDao.save(holidayCalendar);

		} catch (Exception e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "saveHolidayCalendarDetails",
					"Exception in saveHolidayCalendarDetails1 :" + e.getMessage());

		}
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(save);

	}

	/**
	 * updating HolidayCalendarDetails
	 * 
	 * @param holidayCalendar
	 * @return
	 * @throws KsdBatchException, JsonProcessingException
	 * 
	 */
	@Override
	public String updateHolidayCalendarDetails(HolidayCalendar holidayCalendar)
			throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateHolidayCalendarDetails", "updating HolidayCalendarDetails");
		HolidayCalendar save = null;
		try {
			save = holiDayDao.save(holidayCalendar);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateHolidayCalendarDetails",
					"Exception : {0}" + e.getMessage());

		}
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(save);
	}

	/**
	 * getting HolidayCalendar Details
	 * 
	 * @param columnName
	 * @param columnValue
	 * @return
	 * @throws KsdBatchException
	 */
	@Override
	public String[] getHolidayCalendarDetails(String columnName, String columnValue) throws KsdBatchException {

		LoggerUtil.log(this.getClass(), Level.INFO, "getHolidayCalendarDetails", "getting HolidayCalendar Details");

		List<String> calendarHolidays = new ArrayList<>();
		String[] dates;
		try {
			calendarHolidays=holidayRepo.holidayDates(Long.valueOf(columnValue));
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getHolidayCalendarDetails",
					"Exception : {0}" + e.getMessage());
		}

		dates = calendarHolidays.toArray(new String[0]);
		return dates;
	}

	/**
	 * copy KsdMaster to KsdConfig
	 * 
	 * @param processFeatureConfig
	 * @return
	 * @throws JsonProcessingException
	 */
	public String copyKsdMasterToKsdConfig(ProcessFeatureConfig processFeatureConfig) throws JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD_NAME, "copyKsdMasterToKsdConfig");

		String columnValue = processFeatureConfig.getJobName();
		List<KsdMaster> ksdMasters = getKsdMastersDetails(PRIMARY_JOB_NAME, columnValue);
		KsdConfig newksdConfig = null;
		if (ksdMasters.size() == 1) {
			KsdMaster ksdMaster = ksdMasters.get(0);

			KsdConfig ksdConfig = new KsdConfig();
			newksdConfig = setKsdConfigFromKsdMaster(ksdConfig, ksdMaster, processFeatureConfig);

		}
		try {
			return saveKsdAndChildDetails(newksdConfig);
		} catch (KsdBatchException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "copyKsdMasterToKsdConfig",
					"Exception in copyKsdMasterToKsdConfig : " + e.getMessage());
		}
		return null;
	}

	/**
	 * Getting KsdMasterDetails
	 * 
	 * @param columnName
	 * @param columnValue
	 * @return
	 */
	public List<KsdMaster> getKsdMastersDetails(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdMastersDetails", "getting ksd master details");
		List<KsdMaster> ksdMasters = null;
		try {
			ksdMasters = genericKsdMasterDao.findByColumn(KsdMaster.class, EMAILS_SCHEDULER_SCHEMA, KSD_MASTER,
					columnName, columnValue);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getKsdMastersDetails", "Exception : ", e.getMessage());
		}
		return ksdMasters;
	}

	/**
	 * set KsdConfig From KsdMaster
	 * 
	 * @param ksdConfig
	 * @param ksdMaster
	 * @param processFeatureConfig
	 * @return
	 */
	public KsdConfig setKsdConfigFromKsdMaster(KsdConfig ksdConfig, KsdMaster ksdMaster,
			ProcessFeatureConfig processFeatureConfig) {
		LoggerUtil.log(this.getClass(), Level.INFO, "setKsdConfigFromKsdMaster", "setting up KsdConfig From KsdMaster");
		ksdConfig.setPrimaryJobName(ksdMaster.getPrimaryJobName());
		ksdConfig.setFrequency(ksdMaster.getFrequency());
		ksdConfig.setJobCutOffTime(ksdMaster.getJobCutOffTime());
		ksdConfig.setTurnaroundTime(ksdMaster.getTurnaroundTime());
		ksdConfig.setJobScheduleTime(ksdMaster.getJobScheduleTime());
		ksdConfig.setEftName(ksdMaster.getEftName());
		ksdConfig.setMaestroTaskName(ksdMaster.getMaestroTaskName());
		ksdConfig.setDailyTaskReportSubjectNameOutLook(ksdMaster.getMaestroTaskName());
		ksdConfig.setProcessJobMapping(processFeatureConfig.getProcessJobMapping());

		if (ksdMaster.getJobStream() != null) {
			List<ClientConfigJobs> clientJobList = new ArrayList<>();
			String jobStream = ksdMaster.getJobStream();
			String[] allChildJobs = jobStream.split(",");
			for (String childJob : allChildJobs) {
				ClientConfigJobs clientJobs = new ClientConfigJobs();
				clientJobs.setChildJobName(childJob);

				clientJobList.add(clientJobs);
			}
			ksdConfig.setClientConfigJobs(clientJobList);
		}

		List<JobSchedule> jobSchedulesList = new ArrayList<>();
		JobSchedule jobSchedule = new JobSchedule();

		jobSchedulesList.add(jobSchedule);
		ksdConfig.setJobSchedules(jobSchedulesList);

		List<KsdFileDetails> ksdFileDetailsList = new ArrayList<>();
		KsdFileDetails ksdFileDetails = new KsdFileDetails();
		ksdFileDetails.setFileName(ksdMaster.getFileName());
		ksdFileDetails.setFileFormatType(ksdMaster.getFileFormatType());
		ksdFileDetailsList.add(ksdFileDetails);

		return ksdConfig;
	}

	/**
	 * delete All Configs From Screen by adid , pjmId and fileType
	 * 
	 * @param adid
	 * @param pjmId
	 * @param fileType
	 * @return
	 */
	@Override
	public ModelApiResponse deleteAllConfigsFromScreen(String adid, long pjmId, String fileType) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteAllConfigsFromScreen",
				"delete All Configs From Screen by adid , pjmId and fileType");
		ModelApiResponse responseFromDB = new ModelApiResponse();
		try {

			LoggerUtil.log(this.getClass(), Level.INFO, "deleteByFile", "  adid = : " + adid);
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteByFile", "  process_job_mapping_id = : " + pjmId);
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteByFile", "  filetype = : " + fileType);
			responseFromDB = deleteByFile(pjmId, fileType, adid);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, DELETE_ALL_CONFIGS_FROM_SCREEN,
					"Exception in deleting all config" + e.getMessage());
		}
		LoggerUtil.log(this.getClass(), Level.INFO, DELETE_ALL_CONFIGS_FROM_SCREEN,
				"Response after deleting all config" + responseFromDB);

		return responseFromDB;

	}

	/**
	 * updating KSD and ClientConfig Details
	 * 
	 * @param ksdConfigFromUI
	 * @param appName
	 * @param sessionToken
	 * @return
	 * @throws KsdBatchException
	 */
	@Override
	public String updateKsdAndClientConfigDetails(com.wipro.fipc.model.generated.KsdConfig ksdConfigFromUI,
			String appName, String sessionToken) throws KsdBatchException {

		LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_AND_CLIENT_CONFIG_DETAILS,
				"pjmid is received from UI is****:  " + ksdConfigFromUI.getProcessJobMappingId());
		List<KsdConfig> ksdMasterConfigList = getKsdAndChildDetails(PROCESS_JOB_MAPPING_ID,
				String.valueOf(ksdConfigFromUI.getProcessJobMappingId()));

		Set<String> jobStreamAsSet = new HashSet<>();
		KsdConfig ksdConfigDetails = new KsdConfig();
		if (ksdMasterConfigList != null && !ksdMasterConfigList.isEmpty()) {
			ksdConfigDetails = ksdMasterConfigList.get(0);
			String primaryJobName = ksdConfigDetails.getPrimaryJobName();
			String maestroTaskName = ksdConfigDetails.getMaestroTaskName();
			String dailyTaskReportSubjectNameOutLook = ksdConfigDetails.getDailyTaskReportSubjectNameOutLook();
			String eftName = ksdConfigDetails.getEftName();
			String timeZone = ksdConfigDetails.getJobSchedules().get(0).getTimeZone();
			String createdByOfKsdConfig = ksdConfigDetails.getCreatedBy();
			Date createdDateofKsdConfig = ksdConfigDetails.getCreatedDate();
			String createdByOfJobSchedule = ksdConfigDetails.getJobSchedules().get(0).getCreatedBy();
			Date createdDateOfJobSchedule = ksdConfigDetails.getJobSchedules().get(0).getCreatedDate();
			List<JobSchedule> jobschedulefromDB = ksdConfigDetails.getJobSchedules();
			List<ClientConfigJobs> clientConfigJobsFromDB = ksdConfigDetails.getClientConfigJobs();
			ProcessJobMapping processJobMapping = ksdConfigDetails.getProcessJobMapping();
			if (ksdConfigFromUI.getDirectTBAUpdate() == null)
				ksdConfigFromUI.setDirectTBAUpdate(true);
			BeanUtils.copyProperties(ksdConfigFromUI, ksdConfigDetails);
			ksdConfigDetails.setActiveFlag('T');
			ksdConfigDetails.setPrimaryJobName(primaryJobName);
			ksdConfigDetails.setMaestroTaskName(maestroTaskName);
			ksdConfigDetails.setDailyTaskReportSubjectNameOutLook(dailyTaskReportSubjectNameOutLook);
			ksdConfigDetails.setEftName(eftName);
			ksdConfigDetails.setCreatedBy(createdByOfKsdConfig);
			ksdConfigDetails.setSkipControlMReport(ksdConfigFromUI.getSkipControlMReport());
			ksdConfigDetails.setTaskCreationOnly(ksdConfigFromUI.getTaskCreationOnly());
			ksdConfigDetails.setIsJiraUsed(ksdConfigFromUI.getIsJiraUsed());
			ksdConfigDetails.setCreatedDate(createdDateofKsdConfig);
			ksdConfigDetails.setJobSchedules(jobschedulefromDB);
			ksdConfigDetails.setClientConfigJobs(clientConfigJobsFromDB);
			ksdConfigDetails.setProcessJobMapping(processJobMapping);
			List<com.wipro.fipc.model.generated.JobSchedule> jobScheduleListFromUI = ksdConfigFromUI.getJobSchedules();
			List<JobSchedule> jobScheduleListFromDB = ksdConfigDetails.getJobSchedules();
			for (int i = 0; i < jobScheduleListFromUI.size(); i++) {
				CustomBeanUtils.copyProperties(jobScheduleListFromUI.get(i), jobScheduleListFromDB.get(i));
			}
			ksdConfigDetails.setJobSchedules(jobScheduleListFromDB);
			ksdConfigDetails.getJobSchedules().get(0).setTimeZone(timeZone);
			ksdConfigDetails.getJobSchedules().get(0).setCreatedBy(createdByOfJobSchedule);
			ksdConfigDetails.getJobSchedules().get(0).setCreatedDate(createdDateOfJobSchedule);

			KsdConfig ksdConfig = ksdMasterConfigList.get(0);
			List<ClientConfigJobs> clientConfigJobsUI = ksdConfigDetails.getClientConfigJobs();
			String jobStreamRequestJson = ksdConfigDetails.getJobStream();

			//FIPC-3437 Job Stream not allowing to save data
			/*
			 * if ((jobStreamRequestJson == null || jobStreamRequestJson.isEmpty() ||
			 * jobStreamRequestJson.equals("")) && !clientConfigJobsUI.isEmpty()) {
			 * LoggerUtil.log(this.getClass(), Level.INFO,
			 * UPDATE_KSD_AND_CLIENT_CONFIG_DETAILS,
			 * "JobStream is null,Child job needs to be delete");
			 * deleteChildJobs(ksdConfigDetails, clientConfigJobsUI);
			 * 
			 * } else if (jobStreamRequestJson != null && ksdConfigDetails.getJobStream() !=
			 * null && !(ksdConfigDetails.getJobStream().isEmpty())) { String[]
			 * childJobsArrayRequestJson = jobStreamRequestJson.split(","); List<String>
			 * jobStramAsList = Arrays.asList(childJobsArrayRequestJson);
			 * 
			 * jobStreamAsSet = addJobStreamAsSet(jobStramAsList, jobStreamAsSet);
			 * 
			 * List<String> listChildJobs = new ArrayList<>(); listChildJobs =
			 * addChildJobInList(clientConfigJobsUI, listChildJobs);
			 * 
			 * ksdConfigDetails = addClientConfigJobInKsdConfig(jobStreamRequestJson,
			 * clientConfigJobsUI, jobStreamAsSet, listChildJobs, ksdConfigDetails);
			 * 
			 * }
			 */
			ksdConfigDetails = updateScheduleTimeInKsdConfigDetails(ksdConfig, ksdConfigDetails);

		}
		String jobStream = String.join(",", jobStreamAsSet);
		ksdConfigDetails.setJobStream(jobStream);
		ksdConfigDetails.setJobSchedules(ksdConfigDetails.getJobSchedules());
		ksdConfigDetails.setUpdatedDate(new Date());
		String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
		ksdConfigDetails.setUpdatedBy(adID);
		ksdConfigDetails.getJobSchedules().get(0).setUpdatedBy(adID);
		LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_AND_CLIENT_CONFIG_DETAILS,
				"UPDATING the KSD config fields >>>>>  primaryJobName= " + ksdConfigDetails.getPrimaryJobName()
						+ " :eftName= " + ksdConfigDetails.getEftName() + " :maestroTaskName= "
						+ ksdConfigDetails.getMaestroTaskName() + " :dailyTaskReportSubjectNameOutLook= "
						+ ksdConfigDetails.getDailyTaskReportSubjectNameOutLook() + " :TimeZone= "
						+ ksdConfigDetails.getJobSchedules().get(0).getTimeZone() + " :UpdatedBy= "
						+ ksdConfigDetails.getUpdatedBy() + " :processJobMappingId= "
						+ ksdConfigDetails.getProcessJobMappingId());
		List<KsdConfig> ksdConfigDetailsList = new ArrayList<>();
		ksdConfigDetailsList.add(ksdConfigDetails);
		String updateKsdDataResultJson = null;
		try {
			ksdConfigDao.saveAll(ksdConfigDetailsList);

			List<ProcessFeatureConfig> processFeatureConfigRecords = processConfigServiceImpl
					.getAllDetailsPJMID(String.valueOf(ksdConfigDetails.getProcessJobMapping().getId()));
			if (!CollectionUtils.isEmpty(processFeatureConfigRecords)) {
				ProcessFeatureConfig processFeatureConfig = processFeatureConfigRecords.get(0);
				processFeatureConfig.setUpdatedBy(ksdConfigDetails.getUpdatedBy());
				processFeatureConfig.setUpdatedDate(ksdConfigDetails.getUpdatedDate());
				LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_AND_CLIENT_CONFIG_DETAILS, "Request to update process feature config Record: ");
				String pfcDetailsUpdateResponse = updateProcessFeatureConfigDetails(processFeatureConfig,
						processFeatureConfig.getId());
				if (StringUtils.hasText(pfcDetailsUpdateResponse)) 
					LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_CHILD_DETAILS,
							"Response from processFeatureConfig,after update: " + pfcDetailsUpdateResponse.length());
				
				updateKsdDataResultJson = "true";
			} else {
				LoggerUtil.log(this.getClass(), Level.ERROR, UPDATE_KSD_CHILD_DETAILS,
						"Data not found in ProcessFeature table for the pjmId: "+ ksdConfigDetails.getProcessJobMapping().getId());
				updateKsdDataResultJson = "false";
			}
			
		} catch (Exception e) {
			updateKsdDataResultJson = "false";
			LoggerUtil.log(this.getClass(), Level.ERROR, UPDATE_KSD_AND_CLIENT_CONFIG_DETAILS,
					"Exception: " + e.getMessage());

		}
		LoggerUtil.log(this.getClass(), Level.INFO, UPDATE_KSD_AND_CLIENT_CONFIG_DETAILS,
				"response from ksdconfig update: " + updateKsdDataResultJson);

		return updateKsdDataResultJson;
	}

	/**
	 * Adding Client Config Job in KsdConfig
	 * 
	 * @param jobStreamRequestJson
	 * @param clientConfigJobsUI
	 * @param jobStreamAsSet
	 * @param listChildJobs
	 * @param ksdConfigDetails
	 * @return
	 */
	public KsdConfig addClientConfigJobInKsdConfig(String jobStreamRequestJson,
			List<ClientConfigJobs> clientConfigJobsUI, Set<String> jobStreamAsSet, List<String> listChildJobs,
			KsdConfig ksdConfigDetails) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addClientConfigJobInKsdConfig",
				"Adding clientConfigJob in ksdConfigDetails: ");
		if (!(jobStreamRequestJson == null || jobStreamRequestJson.isEmpty())
				&& (clientConfigJobsUI == null || clientConfigJobsUI.isEmpty())) {
			ksdConfigDetails = addNewChildJob(jobStreamAsSet, ksdConfigDetails);
		} else if ((jobStreamRequestJson == null || jobStreamRequestJson.isEmpty() || jobStreamRequestJson.equals(""))
				&& (clientConfigJobsUI != null && !clientConfigJobsUI.isEmpty())) {
			for (ClientConfigJobs clientConfigJobs : clientConfigJobsUI) {
				clientConfigJobs.setActiveFlag(ACTIVE_FLAG_FALSE);
			}
			ksdConfigDetails.setClientConfigJobs(clientConfigJobsUI);
		} else if (jobStreamAsSet.size() != listChildJobs.size()) {
			ksdConfigDetails = addUnequalChildJob(clientConfigJobsUI, jobStreamAsSet, ksdConfigDetails);
		} else if (jobStreamAsSet.size() == listChildJobs.size()) {
			ksdConfigDetails = addEqualChildJob(clientConfigJobsUI, listChildJobs, jobStreamAsSet, ksdConfigDetails);
		}
		return ksdConfigDetails;
	}

	/**
	 * Deleting All Configs From Screen by adid , pjmId and fileType
	 * 
	 * @param processJobMappingId
	 * @param fileType
	 * @param adid
	 * @return
	 */
	public ModelApiResponse deleteByFile(long processJobMappingId, String fileType, String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteAllConfigsFromScreen",
				"delete All Configs From Screen by adid , pjmId and fileType");
		ModelApiResponse modelApiResponse = new ModelApiResponse();
		List<String> fileNameList = ksdFileDetailsDao.findByFile(processJobMappingId, fileType);
		try {
			fileNameList.forEach(name -> {
				tbaMatchConfigDao.commondelete2(adid, name, processJobMappingId);
				processControlConfigDao.commondelete(adid, processJobMappingId);
				ksdFileDetailsDao.commondelete(adid, name, processJobMappingId);
				layoutConfigDao.commondelete(adid, name, processJobMappingId);
				rulesConfigDao.commondelete(adid, name, processJobMappingId);
				rulesDefinitionDao.commondelete(adid, name, processJobMappingId);
			});
		} catch (Exception e) {
			modelApiResponse.setSTATUS("Failed");
			modelApiResponse.setMESSAGE("Records not deleted");
			return modelApiResponse;
		}

		modelApiResponse.setSTATUS("Success");
		modelApiResponse.setMESSAGE("Records deleted successfully");
		return modelApiResponse;
	}

}
