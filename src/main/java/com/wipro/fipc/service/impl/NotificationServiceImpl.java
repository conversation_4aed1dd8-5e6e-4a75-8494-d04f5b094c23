package com.wipro.fipc.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

import javax.persistence.EntityManager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.common.beans.Credentials;
import com.wipro.fipc.common.beans.Payload;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.EmailPlaceHolderRepo;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.NotificationMailConfigRepository;
import com.wipro.fipc.dao.NotificationReportConfigRepository;
import com.wipro.fipc.entity.batch.EmailPlaceholderMaster;
import com.wipro.fipc.entity.batch.KsdFileDetails;
import com.wipro.fipc.entity.common.NotificationReportConfig;
import com.wipro.fipc.entity.filelayout.CustomResponse;
import com.wipro.fipc.entity.notification.NotificationMailConfigEntity;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.EmailPlaceHolder;
import com.wipro.fipc.service.INotificationService;
import com.wipro.fipc.utils.CommonGetAdId;

@Service
public class NotificationServiceImpl implements INotificationService {
	private static final String CREATE_REPORT_RESPONSE = "createReportResponse:  ";
	private static final String CREATE_MAIL_RESPONSE = "createMailResponse:  ";
	@Autowired
	Environment env;
	@Autowired
	JsonParser parser;
	@Autowired
	Gson gson;
	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	NotificationMailConfigRepository objNotificationMailConfigRepository;

	@Autowired
	NotificationReportConfigRepository objNotificationReportConfigRepository;
	@Autowired
	private EntityManager entityManager;

	@Autowired
	private GenericDao<NotificationMailConfigEntity> genericDao;

	@Autowired
	private GenericDao<NotificationReportConfig> genericDaoReport;

	@Autowired
	NotificationReportConfigRepository notificationReportConfigRepository;

	@Autowired
	private EmailPlaceHolderRepo emailPlaceHolderRepo;

	private static final String UPDATED_DATE = "updatedDate";
	private static final String NOT_IF_REPORT = "notifReport";
	private static final String NOT_IF_MAIL = "notifMail";
	private static final String ERROR = "following error occured: {} \n";
	private static final String DB_URL_REPORT = "dbUrlReport";
	private static final String DB_URL_MAIL = "dbUrlMail";
	private static final String CREATED_DATE = "createdDate";
	private static final String ITEM_COL_NAME = "itemColName";
	private static final String REPORT_NAME = "reportName";
	private static final String PASSWORD = "password";
	private static final String ATTACHMENT_NAME = "attachmentName";
	private static final String ACTION = "action";
	private static final String CREATED_BY = "createdBy";
	private static final String UPDATED_BY = "updatedBy";
	private static final String ACTIVE_FLAGE = "activeFlag";
	protected static final String NOTIFICATION_MAIL_CONFIG = "NOTIFICATION_MAIL_CONFIG";
	protected static final String NOTIFICATION_REPORT_CONFIG = "NOTIFICATION_REPORT_CONFIG";
	protected static final String COMMON_SCHEMA = "common";
	private static final String PLACE_HOLDER = "placeHolder";

	public String getDetails(String columnName, String columnValue) {
		String notificationMailDetails = "";
		String notificationReportDetails = "";

		JsonParser jsonParser = new JsonParser();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			List<NotificationMailConfigEntity> obj = entityManager
					.createNativeQuery("SELECT * FROM common.notification_mail_config WHERE " + columnName + " = "
							+ columnValue + " and active_flag='T'", NotificationMailConfigEntity.class)
					.getResultList();
			notificationMailDetails = objectMapper.writeValueAsString(obj);
			JsonArray notifMail = jsonParser.parse(notificationMailDetails).getAsJsonArray();
			notificationMailDetails(notifMail);
			notificationMailDetails = notifMail.size() > 0 ? notifMail.toString() : notificationMailDetails;
			if (!StringUtils.hasText(notificationMailDetails)) {
				notificationMailDetails = HolmesAppConstants.FAILED;
			}

		} catch (Exception e) {
			e.printStackTrace();
			LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.GETREQUESTMETHOD,
					"Unable to connect to DB Service due to {} \n" + e.getMessage());
			notificationMailDetails = HolmesAppConstants.ERROR;
		}
		try {

			List<NotificationReportConfig> obj = entityManager
					.createNativeQuery("SELECT * FROM common.notification_report_config WHERE " + columnName + " = "
							+ columnValue + " and active_flag='T'", NotificationReportConfig.class)
					.getResultList();
			notificationReportDetails = objectMapper.writeValueAsString(obj);
			JsonArray notifReport = jsonParser.parse(notificationReportDetails).getAsJsonArray();
			notificationReportDetails(notifReport);
			notificationReportDetails = notifReport.size() > 0 ? notifReport.toString() : notificationReportDetails;
			if (!StringUtils.hasText(notificationReportDetails)) {
				notificationReportDetails = HolmesAppConstants.FAILED;
			}
		} catch (Exception e) {
			LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.GETREQUESTMETHOD,
					"Unable to connect to DB Service due to {} \n" + e.getMessage());
			notificationReportDetails = HolmesAppConstants.ERROR;
		}
		JsonObject jsDetailsStatus = new JsonObject();
		jsDetailsStatus.addProperty(NOT_IF_MAIL, notificationMailDetails);
		jsDetailsStatus.addProperty(NOT_IF_REPORT, notificationReportDetails);
		return jsDetailsStatus.toString();
	}

	@Override
	public String createUpdate(Object request, String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createUpdate", "inside service class");
		String jsonString = "";
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			jsonString = mapper.writeValueAsString(request);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
			LoggerUtil.log(this.getClass(), Level.ERROR, "createUpdate", "Exception : ", e1.getMessage());
		}
		JsonObject requestt = parser.parse(jsonString).getAsJsonObject();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		LocalDateTime now = LocalDateTime.now();
		String createMailResponse = "";
		String createReportResponse = "";
		if (requestt.has(NOT_IF_MAIL)) {
			try {
				JsonArray notifMailArray = requestt.getAsJsonArray(NOT_IF_MAIL);
				notificationMailArray(notifMailArray, dtf, now, sessionToken);
				List<NotificationMailConfigEntity> obj2 = new ArrayList<NotificationMailConfigEntity>();
				for (int i = 0; i < notifMailArray.size(); i++) {
					String string = notifMailArray.get(i).toString();
					NotificationMailConfigEntity obj1 = new ObjectMapper().readValue(string,
							NotificationMailConfigEntity.class);
					obj2.add(obj1);
				}
				objNotificationMailConfigRepository.saveAll(obj2);
				createMailResponse = "true";

				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.POSTREQUESTMETHOD,
						CREATE_MAIL_RESPONSE + createMailResponse);
				if (!StringUtils.hasText(createMailResponse)) {
					createMailResponse = HolmesAppConstants.FAILED;
				}
			} catch (Exception e) {
				e.printStackTrace();
				createMailResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		if (requestt.has(NOT_IF_REPORT)) {
			try {
				JsonArray notifReportArray = requestt.getAsJsonArray(NOT_IF_REPORT);
				notificationReportArray(notifReportArray, dtf, now);

				List<NotificationReportConfig> obj2 = new ArrayList<NotificationReportConfig>();
				for (int i = 0; i < notifReportArray.size(); i++) {
					String string = notifReportArray.get(i).toString();
					NotificationReportConfig obj1 = new ObjectMapper().readValue(string,
							NotificationReportConfig.class);
					obj2.add(obj1);
				}
				objNotificationReportConfigRepository.saveAll(obj2);
				createReportResponse = "true";
				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.POSTREQUESTMETHOD,
						CREATE_REPORT_RESPONSE + createReportResponse);
				if (!StringUtils.hasText(createReportResponse)) {
					createReportResponse = HolmesAppConstants.FAILED;
				}
			} catch (Exception e) {
				createReportResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		JsonObject resp = new JsonObject();
		resp.addProperty(NOT_IF_MAIL, createMailResponse);
		resp.addProperty(NOT_IF_REPORT, createReportResponse);
		return gson.toJson(resp);
	}

	public String deleteDetails(String notifDelete) {
		String deleteResponse = "";
		try {
			JsonObject js = parser.parse(notifDelete).getAsJsonObject();
			if (js.has(NOT_IF_MAIL)) {
				int id = js.get(NOT_IF_MAIL).getAsInt();
				String deleteMailUrl = env.getProperty(DB_URL_MAIL) + "/" + id;
				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.DELETEREQUESTMETHOD,
						"notifMail delete id:  " + id + " url " + deleteMailUrl);
				deleteResponse = "Status : Operation Not Supported";
				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.DELETEREQUESTMETHOD,
						"response from server is deleteResponse  " + deleteResponse);
				if (deleteResponse.length() == 0 || deleteResponse.isEmpty()) {
					deleteResponse = HolmesAppConstants.FAILED;
				}
			} else if (js.has(NOT_IF_REPORT)) {
				int id = js.get(NOT_IF_REPORT).getAsInt();
				String deleteReportUrl = env.getProperty(DB_URL_REPORT) + "/" + id;
				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.DELETEREQUESTMETHOD,
						"notifReport delete id:  " + id + " url " + deleteReportUrl);
				deleteResponse = "Status : Operation Not Supported";
				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.DELETEREQUESTMETHOD,
						"response from server is deleteResponse  " + deleteResponse);
				if (deleteResponse.length() == 0 || deleteResponse.isEmpty()) {
					deleteResponse = HolmesAppConstants.FAILED;
				}
			}
		} catch (Exception e) {
			deleteResponse = HolmesAppConstants.ERROR;
			LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.DELETEREQUESTMETHOD,
					ERROR + e.getMessage());
		}
		return deleteResponse;
	}

	private void notificationMailDetails(JsonArray notifMail) throws Exception {
		for (int i = 0; i < notifMail.size(); i++) {
			if (notifMail.get(i).getAsJsonObject().has(ATTACHMENT_NAME)) {
				String attachmentName = notifMail.get(i).getAsJsonObject().get(ATTACHMENT_NAME).getAsString();

				Object obj = gson.fromJson(attachmentName, Object.class);
				notifMail.get(i).getAsJsonObject().remove(ATTACHMENT_NAME);
				notifMail.get(i).getAsJsonObject().addProperty(ATTACHMENT_NAME, obj == null ? "" : obj.toString());

			}
			if (notifMail.get(i).getAsJsonObject().has(PLACE_HOLDER)) {
				String placeHolder = notifMail.get(i).getAsJsonObject().get(PLACE_HOLDER).isJsonNull()? ""
						: notifMail.get(i).getAsJsonObject().get(PLACE_HOLDER).getAsString();
				Object obj = gson.fromJson(placeHolder, Object.class);
				notifMail.get(i).getAsJsonObject().remove(PLACE_HOLDER);
				notifMail.get(i).getAsJsonObject().addProperty(PLACE_HOLDER, obj == null ? "" : obj.toString());
			}

			if (notifMail.get(i).getAsJsonObject().has(PASSWORD)
					&& !notifMail.get(i).getAsJsonObject().get(PASSWORD).isJsonNull()) {
				String password = notifMail.get(i).getAsJsonObject().get(PASSWORD).getAsString();
				if (!password.equalsIgnoreCase("")) {
					JsonArray obj = gson.fromJson(password, JsonArray.class);
					getPasswordInAws(obj);
					notifMail.get(i).getAsJsonObject().remove(PASSWORD);
					notifMail.get(i).getAsJsonObject().addProperty(PASSWORD, obj == null ? "" : obj.toString());
				}
			}
		}

	}

	private void notificationReportDetails(JsonArray notifReport) {
		for (int i = 0; i < notifReport.size(); i++) {
			if (notifReport.get(i).getAsJsonObject().has(REPORT_NAME)) {
				String reportName = notifReport.get(i).getAsJsonObject().get(REPORT_NAME).getAsString();
				Object obj = gson.fromJson(reportName, Object.class);
				notifReport.get(i).getAsJsonObject().remove(REPORT_NAME);
				notifReport.get(i).getAsJsonObject().addProperty(REPORT_NAME, obj == null ? "" : obj.toString());
			}
			itemColName(notifReport, i);

			if (notifReport.get(i).getAsJsonObject().has(ACTION)
					&& !notifReport.get(i).getAsJsonObject().get(ACTION).isJsonNull()) {
				String action = notifReport.get(i).getAsJsonObject().get(ACTION).getAsString();
				Object obj = gson.fromJson(action, Object.class);
				notifReport.get(i).getAsJsonObject().remove(ACTION);
				notifReport.get(i).getAsJsonObject().addProperty(ACTION, obj == null ? "" : obj.toString());
			}
		}
	}

	private void itemColName(JsonArray notifReport, int i) {
		if (notifReport.get(i).getAsJsonObject().has(ITEM_COL_NAME)
				&& !notifReport.get(i).getAsJsonObject().get(ITEM_COL_NAME).isJsonNull()) {
			String itemColName = notifReport.get(i).getAsJsonObject().get(ITEM_COL_NAME).getAsString();
			Object obj = gson.fromJson(itemColName, Object.class);
			notifReport.get(i).getAsJsonObject().remove(ITEM_COL_NAME);
			notifReport.get(i).getAsJsonObject().addProperty(ITEM_COL_NAME, obj == null ? "" : obj.toString());
		}
	}

	private void notificationMailArray(JsonArray notifMailArray, DateTimeFormatter dtf, LocalDateTime now,
			String sessionToken) throws Exception {

		for (int i = 0; i < notifMailArray.size(); i++) {
			JsonArray attachmentName = null;
			JsonArray psw = null;
			if (notifMailArray.get(i).getAsJsonObject().has("id")
					&& !notifMailArray.get(i).getAsJsonObject().get("id").getAsString().isEmpty()) {
				notifMailArray.get(i).getAsJsonObject().remove(UPDATED_DATE);
				notifMailArray.get(i).getAsJsonObject().addProperty(UPDATED_DATE, dtf.format(now));
			} else {
				notifMailArray.get(i).getAsJsonObject().remove(CREATED_DATE);
				notifMailArray.get(i).getAsJsonObject().addProperty(CREATED_DATE, dtf.format(now));
			}
			if (notifMailArray.get(i).getAsJsonObject().has(ATTACHMENT_NAME)) {
				attachmentName = notifMailArray.get(i).getAsJsonObject().get(ATTACHMENT_NAME).getAsJsonArray();
				JsonArray attachmentNames = encodedValues(attachmentName);
				String res = gson.toJson(attachmentNames);
				String resdecode = decodeValues(res);
				notifMailArray.get(i).getAsJsonObject().remove(ATTACHMENT_NAME);
				notifMailArray.get(i).getAsJsonObject().addProperty(ATTACHMENT_NAME, resdecode);
			}
			if (notifMailArray.get(i).getAsJsonObject().has(PASSWORD)) {
				psw = notifMailArray.get(i).getAsJsonObject().get(PASSWORD).getAsJsonArray();
				notifMailArray.get(i).getAsJsonObject().remove(PASSWORD);
				storePasswordInAws(psw, notifMailArray.get(i).getAsJsonObject().get("condition").getAsString(),
						sessionToken);
				String res = gson.toJson(psw);
				notifMailArray.get(i).getAsJsonObject().addProperty(PASSWORD, res);
			}
		}

	}

	private void notificationReportArray(JsonArray notifReportArray, DateTimeFormatter dtf, LocalDateTime now) {

		for (int i = 0; i < notifReportArray.size(); i++) {
			JsonArray reportName = null;
			JsonArray itemColName = null;
			JsonArray action = null;
			if (notifReportArray.get(i).getAsJsonObject().has("id")
					&& !notifReportArray.get(i).getAsJsonObject().get("id").getAsString().isEmpty()) {
				notifReportArray.get(i).getAsJsonObject().remove(UPDATED_DATE);
				notifReportArray.get(i).getAsJsonObject().addProperty(UPDATED_DATE, dtf.format(now));
			} else {
				notifReportArray.get(i).getAsJsonObject().remove(CREATED_DATE);
				notifReportArray.get(i).getAsJsonObject().addProperty(CREATED_DATE, dtf.format(now));
			}

			if (notifReportArray.get(i).getAsJsonObject().has(REPORT_NAME)) {
				reportName = notifReportArray.get(i).getAsJsonObject().get(REPORT_NAME).getAsJsonArray();
				JsonArray reportNames = encodedValues(reportName);
				String res = gson.toJson(reportNames);
				String resdecode = decodeValues(res);
				notifReportArray.get(i).getAsJsonObject().remove(REPORT_NAME);
				notifReportArray.get(i).getAsJsonObject().addProperty(REPORT_NAME, resdecode);
			}

			if (notifReportArray.get(i).getAsJsonObject().has(ITEM_COL_NAME)) {
				itemColName = notifReportArray.get(i).getAsJsonObject().get(ITEM_COL_NAME).getAsJsonArray();
				notifReportArray.get(i).getAsJsonObject().remove(ITEM_COL_NAME);
				String res = gson.toJson(itemColName);
				notifReportArray.get(i).getAsJsonObject().addProperty(ITEM_COL_NAME, res);
			}

			if (notifReportArray.get(i).getAsJsonObject().has(ACTION)) {
				action = notifReportArray.get(i).getAsJsonObject().get(ACTION).getAsJsonArray();
				notifReportArray.get(i).getAsJsonObject().remove(ACTION);
				String res = gson.toJson(action);
				notifReportArray.get(i).getAsJsonObject().addProperty(ACTION, res);
			}

		}

	}

	@Override
	public String deleteNotificationData(String notifType, String entity) {
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.CONTENT_TYPE_VALUE);
			headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
			String deleteallnotification = null;
			JsonArray asJsonArray = parser.parse(entity).getAsJsonArray();
			List<KsdFileDetails> ksdFileDetailsList = new ArrayList<>();
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			for (int i = 0; i < asJsonArray.size(); i++) {
				KsdFileDetails obj = new KsdFileDetails();
				obj.setId(asJsonArray.get(i).getAsJsonObject().get("id").getAsLong());
				obj.setProcessJobMappingId(asJsonArray.get(i).getAsJsonObject().get("processJobMappingId").getAsLong());
				obj.setUpdatedBy(asJsonArray.get(i).getAsJsonObject().get("updatedBy").getAsString());
				ksdFileDetailsList.add(obj);
			}

			if (notifType.equals(NOT_IF_REPORT)) {
				String returnValue = "";
				List<CustomResponse> responses = new ArrayList<>();
				try {

					LoggerUtil.log(this.getClass(), Level.INFO, "notificationdelete",
							"  Notification Report Values = : " + ksdFileDetailsList);
					for (KsdFileDetails ksdFileDetails : ksdFileDetailsList) {
						CustomResponse response = new CustomResponse();
						try {
							objNotificationReportConfigRepository.notificationDelete(ksdFileDetails.getUpdatedBy(),
									ksdFileDetails.getProcessJobMappingId(), ksdFileDetails.getId());
							response.setId(ksdFileDetails.getId());
							response.setMessage("Records deleted successfully");
							response.setStatus("Success");
						} catch (Exception e) {
							e.printStackTrace();
							response.setMessage("Records not deleted");
							response.setStatus("Failed");
						}
						responses.add(response);
					}
					returnValue = objectMapper.writeValueAsString(responses);
				} catch (Exception e) {
					e.printStackTrace();
				}

				return returnValue;
			} else if (notifType.equals(NOT_IF_MAIL)) {
				List<CustomResponse> responses = new ArrayList<>();
				String returnValue = "";

				try {
					for (KsdFileDetails ksdFileDetails : ksdFileDetailsList) {
						CustomResponse response = new CustomResponse();
						try {
							objNotificationMailConfigRepository.notificationMaildelete(ksdFileDetails.getUpdatedBy(),
									ksdFileDetails.getProcessJobMappingId(), ksdFileDetails.getId());
							response.setId(ksdFileDetails.getId());
							response.setMessage("Records deleted successfully");
							response.setStatus("Success");
						} catch (Exception e) {
							e.printStackTrace();
							response.setMessage("Records not deleted");
							response.setStatus("Failed");
						}
						responses.add(response);
					}
					returnValue = objectMapper.writeValueAsString(responses);
				} catch (Exception e) {
					e.printStackTrace();
				}

				return returnValue;

			}
			LoggerUtil.log(getClass(), Level.INFO, "deleteNotificationData",
					"Calling deleteAllFiles DB service: " + deleteallnotification);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "Error";
	}

	@Override
	public String createUpdateNotification(Object request, String sessionToken) {

		String jsonString = "";
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			jsonString = mapper.writeValueAsString(request);
		} catch (JsonProcessingException e1) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "createUpdate", "Exception : ", e1.getMessage());
		}
		JsonObject requestt = parser.parse(jsonString).getAsJsonObject();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		LocalDateTime now = LocalDateTime.now();
		String createMailResponse = "";
		String createReportResponse = "";
		JsonParser jsonParser = new JsonParser();
		if (requestt.has(NOT_IF_MAIL)) {

			try {
				JsonArray notifMailArray = requestt.getAsJsonArray(NOT_IF_MAIL);
				notificationMailArray(notifMailArray, dtf, now, sessionToken);

				List<NotificationMailConfigEntity> obj2 = new ArrayList<NotificationMailConfigEntity>();
				for (int i = 0; i < notifMailArray.size(); i++) {
					String string = notifMailArray.get(i).toString();
					NotificationMailConfigEntity obj1 = new ObjectMapper().readValue(string,
							NotificationMailConfigEntity.class);
					obj2.add(obj1);
				}

				List<NotificationMailConfigEntity> notificationMailConfig = objNotificationMailConfigRepository
						.saveAll(obj2);
				createMailResponse = mapper.writeValueAsString(notificationMailConfig);

				JsonArray notifMail = jsonParser.parse(createMailResponse).getAsJsonArray();
				notificationMailDetails(notifMail);
				createMailResponse = notifMail.size() > 0 ? notifMail.toString() : createMailResponse;

				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.POSTREQUESTMETHOD,
						CREATE_MAIL_RESPONSE + createMailResponse);
				if (!StringUtils.hasText(createMailResponse)) {
					createMailResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				createMailResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		if (requestt.has(NOT_IF_REPORT)) {
			try {
				JsonArray notifReportArray = requestt.getAsJsonArray(NOT_IF_REPORT);
				notificationReportArray(notifReportArray, dtf, now);
				List<NotificationReportConfig> obj2 = new ArrayList<NotificationReportConfig>();
				for (int i = 0; i < notifReportArray.size(); i++) {
					String string = notifReportArray.get(i).toString();
					NotificationReportConfig obj1 = new ObjectMapper().readValue(string,
							NotificationReportConfig.class);
					obj2.add(obj1);
				}
				List<NotificationReportConfig> notificationMailConfig = objNotificationReportConfigRepository
						.saveAll(obj2);
				createReportResponse = mapper.writeValueAsString(notificationMailConfig);
				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.POSTREQUESTMETHOD,
						CREATE_REPORT_RESPONSE + createReportResponse);
				JsonArray notifReport = jsonParser.parse(createReportResponse).getAsJsonArray();
				notificationReportDetails(notifReport);
				createReportResponse = notifReport.size() > 0 ? notifReport.toString() : createReportResponse;

				if (!StringUtils.hasText(createReportResponse)) {
					createReportResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				createReportResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		JsonObject jobj = new JsonObject();
		jobj.addProperty(NOT_IF_MAIL, createMailResponse);
		jobj.addProperty(NOT_IF_REPORT, createReportResponse);
		return jobj.toString();
	}

	private void storePasswordInAws(JsonArray psw, String condition, String sessionToken) throws Exception {
		LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, " In storePasswordInAws ",
				"to store password into AWS.");
		String url = env.getProperty("storePassword");
		LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, " In storePasswordInAws ",
				"store password url :- " + url);
		HttpHeaders headers = new HttpHeaders();
		headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.APPLICATION_JSON);
		headers.set(HolmesAppConstants.APP_NAME, HolmesAppConstants.FNI);
		headers.set(HolmesAppConstants.API_KEY, env.getProperty(HolmesAppConstants.API_KEY));
		String response = null;
		headers.set("sessionToken", sessionToken);
		for (JsonElement ele : psw) {
			Payload payload = new Payload();
			payload.setClientType(HolmesAppConstants.HUMAN);
			payload.setAppNameConfig(HolmesAppConstants.FNI);
			payload.setRacfID("Notif_" + condition.substring(0, 1) + "_"
					+ ele.getAsJsonObject().get("filnameWithPid").getAsString());
			payload.setRacfPassword(ele.getAsJsonObject().get("password").getAsString());
			HttpEntity<Payload> request = new HttpEntity<>(payload, headers);
			response = restTemplate.postForEntity(url, request, String.class).getBody();
			if (response.equalsIgnoreCase("true"))
				ele.getAsJsonObject().addProperty("password", payload.getRacfID());
			else
				throw new Exception(response);
		}
		LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, " In storePasswordInAws ", "password stored!");
	}

	private void getPasswordInAws(JsonArray psw) throws Exception {
		LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, " In getPasswordInAws ", "to get password from AWS.");
		String response = null;
		String url = env.getProperty("getPassword");
		LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, " In getPasswordInAws ",
				"get password url :- " + url);
		HttpHeaders headers = new HttpHeaders();
		headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.APPLICATION_JSON);
		headers.set(HolmesAppConstants.APP_NAME, HolmesAppConstants.FNI);
		headers.set(HolmesAppConstants.API_KEY, env.getProperty(HolmesAppConstants.API_KEY));
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		for (JsonElement ele : psw) {
			Payload payload = new Payload();
			payload.setClientType(HolmesAppConstants.HUMAN);
			payload.setAppNameConfig(HolmesAppConstants.FNI);
			payload.setRacfID(ele.getAsJsonObject().get("password").getAsString());
			HttpEntity<Payload> request = new HttpEntity<>(payload, headers);
			response = restTemplate.postForEntity(url, request, String.class).getBody();
			Credentials cred = mapper.readValue(response, Credentials.class);
			ele.getAsJsonObject().addProperty("password", cred.getPassword());

		}
		LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, " In getPasswordInAws ",
				"Password retrived successfully!");
	}

	@Override
	public String updateNotification(Object request, String sessionToken) {

		String jsonString = "";
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			jsonString = mapper.writeValueAsString(request);
		} catch (JsonProcessingException e1) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "updateNotification", "Exception : ", e1.getMessage());
		}
		JsonObject requestt = parser.parse(jsonString).getAsJsonObject();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		LocalDateTime now = LocalDateTime.now();
		String updateMailResponse = "";
		String updateReportResponse = "";
		JsonParser jsonParser = new JsonParser();
		if (requestt.has(NOT_IF_MAIL)) {

			try {
				JsonArray notifMailArray = requestt.getAsJsonArray(NOT_IF_MAIL);
				notificationMailArray(notifMailArray, dtf, now, sessionToken);

				List<NotificationMailConfigEntity> obj2 = new ArrayList<NotificationMailConfigEntity>();
				for (int i = 0; i < notifMailArray.size(); i++) {
					String string = notifMailArray.get(i).toString();
					NotificationMailConfigEntity obj1 = new ObjectMapper().readValue(string,
							NotificationMailConfigEntity.class);
					obj2.add(obj1);
				}
				List<NotificationMailConfigEntity> notificationMailConfig = objNotificationMailConfigRepository
						.saveAll(obj2);
				updateMailResponse = mapper.writeValueAsString(notificationMailConfig);
				JsonArray notifMail = jsonParser.parse(updateMailResponse).getAsJsonArray();
				notificationMailDetails(notifMail);
				updateMailResponse = notifMail.size() > 0 ? notifMail.toString() : updateMailResponse;

				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.POSTREQUESTMETHOD,
						CREATE_MAIL_RESPONSE + updateMailResponse);

				if (!StringUtils.hasText(updateMailResponse)) {
					updateMailResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				updateMailResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		if (requestt.has(NOT_IF_REPORT)) {
			try {
				JsonArray notifReportArray = requestt.getAsJsonArray(NOT_IF_REPORT);
				notificationReportArray(notifReportArray, dtf, now);
				List<NotificationReportConfig> obj2 = new ArrayList<NotificationReportConfig>();
				for (int i = 0; i < notifReportArray.size(); i++) {
					String string = notifReportArray.get(i).toString();
					NotificationReportConfig obj1 = new ObjectMapper().readValue(string,
							NotificationReportConfig.class);
					obj2.add(obj1);
				}
				List<NotificationReportConfig> notificationMailConfig = objNotificationReportConfigRepository
						.saveAll(obj2);
				updateReportResponse = mapper.writeValueAsString(notificationMailConfig);
				LoggerUtil.log(NotificationServiceImpl.class, Level.INFO, HolmesAppConstants.POSTREQUESTMETHOD,
						CREATE_REPORT_RESPONSE + updateReportResponse);
				JsonArray notifReport = jsonParser.parse(updateReportResponse).getAsJsonArray();
				notificationReportDetails(notifReport);
				updateReportResponse = notifReport.size() > 0 ? notifReport.toString() : updateReportResponse;

				if (!StringUtils.hasText(updateReportResponse)) {
					updateReportResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				updateReportResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		JsonObject jobj = new JsonObject();
		jobj.addProperty(NOT_IF_MAIL, updateMailResponse);
		jobj.addProperty(NOT_IF_REPORT, updateReportResponse);
		return jobj.toString();
	}

	private JsonArray encodedValues(JsonArray attachmentName) {
		String fname = "";
		JsonArray attachmentNames = new JsonArray();

		for (int i = 0; i < attachmentName.size(); i++) {
			String fName = attachmentName.get(i).getAsString();
			try {
				fname = URLEncoder.encode(fName, HolmesAppConstants.UTF8);

			} catch (UnsupportedEncodingException e) {
				LoggerUtil.log(getClass(), Level.ERROR, "encodedValues",
						"Unsupported Encoding Format \n" + e.getMessage());

			}
			attachmentNames.add(fname);
		}
		return attachmentNames;
	}

	private String decodeValues(String attachmentName) {
		String fname = "";
		JsonArray attachmentNames = new JsonArray();

		JsonArray enArray = (JsonArray) parser.parse(attachmentName);
		for (int i = 0; i < enArray.size(); i++) {
			String fName = enArray.get(i).getAsString();
			try {
				fname = URLDecoder.decode(fName, HolmesAppConstants.UTF8);

			} catch (UnsupportedEncodingException e) {
				LoggerUtil.log(getClass(), Level.ERROR, "decodeValues",
						"Unsupported Decoding Format \n" + e.getMessage());

			}
			attachmentNames.add(fname);
		}

		return attachmentNames.toString();
	}

	@Override
	public String deleteNotificationDataVaptNew(String notifType, List<CommonDeleteDTO> entity, String appName,
			String sessionToken) {
		HttpHeaders headers = new HttpHeaders();
		headers.set(HolmesAppConstants.CONTENT_TYPE, HolmesAppConstants.CONTENT_TYPE_VALUE);
		headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
		String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
		List<KsdFileDetails> ksdFileDetailsList = new ArrayList<>();
		Iterator<CommonDeleteDTO> cmdto = entity.iterator();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			while (cmdto.hasNext()) {
				CommonDeleteDTO commondto = cmdto.next();
				commondto.setUpdatedBy(adID);
				commondto.setActiveFlag("F");
				commondto.setUpdatedDate(new Date());
				LoggerUtil.log(getClass(), Level.INFO, "deleteNotificationDataVaptNew() method",
						" : For This ADID: " + adID + " :This PjmId: " + commondto.getProcessJobMappingId());

				KsdFileDetails obj1 = new KsdFileDetails();
				obj1.setId(Long.parseLong(commondto.getId()));
				obj1.setProcessJobMappingId(Long.parseLong(commondto.getProcessJobMappingId()));
				obj1.setUpdatedBy(commondto.getUpdatedBy());
				obj1.setUpdatedDate(commondto.getUpdatedDate());
				obj1.setActiveFlag(commondto.getActiveFlag().toCharArray()[0]);
				obj1.setFileName(commondto.getFileName());
				ksdFileDetailsList.add(obj1);

			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		if (notifType.equals(NOT_IF_REPORT)) {
			String returnValue = "";
			List<CustomResponse> responses = new ArrayList<>();
			try {

				LoggerUtil.log(this.getClass(), Level.INFO, "notificationdelete",
						"  Notification Report Values = : " + ksdFileDetailsList);
				for (KsdFileDetails ksdFileDetails : ksdFileDetailsList) {
					CustomResponse response = new CustomResponse();
					try {
						objNotificationReportConfigRepository.notificationDelete(ksdFileDetails.getUpdatedBy(),
								ksdFileDetails.getProcessJobMappingId(), ksdFileDetails.getId());
						response.setId(ksdFileDetails.getId());
						response.setMessage("Records deleted successfully");
						response.setStatus("Success");
					} catch (Exception e) {
						e.printStackTrace();
						response.setMessage("Records not deleted");
						response.setStatus("Failed");
					}
					responses.add(response);
				}
				returnValue = objectMapper.writeValueAsString(responses);
			} catch (Exception e) {
				e.printStackTrace();
			}

			return returnValue;

		} else if (notifType.equals(NOT_IF_MAIL)) {
			List<CustomResponse> responses = new ArrayList<>();
			String returnValue = "";

			try {
				for (KsdFileDetails ksdFileDetails : ksdFileDetailsList) {
					CustomResponse response = new CustomResponse();
					try {
						objNotificationMailConfigRepository.notificationMaildelete(ksdFileDetails.getUpdatedBy(),
								ksdFileDetails.getProcessJobMappingId(), ksdFileDetails.getId());
						response.setId(ksdFileDetails.getId());
						response.setMessage("Records deleted successfully");
						response.setStatus("Success");
					} catch (Exception e) {
						e.printStackTrace();
						response.setMessage("Records not deleted");
						response.setStatus("Failed");
					}
					responses.add(response);
				}
				returnValue = objectMapper.writeValueAsString(responses);
			} catch (Exception e) {
				e.printStackTrace();
			}

			return returnValue;

		}
		return "Error";
	}

	public String getMailDataById(JsonArray notifMailArray) throws JsonProcessingException {
		String id = "";
		for (int i = 0; i < notifMailArray.size(); i++) {
			id = notifMailArray.get(i).getAsJsonObject().get("id").getAsString();
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getMailDataById",
				"Getting NotificationMailConfig columnValues: " + id);

		Optional<NotificationMailConfigEntity> findById = objNotificationMailConfigRepository
				.findById(Long.parseLong(id));
		NotificationMailConfigEntity notificationMailConfigEntity = findById.get();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(notificationMailConfigEntity);
	}

	public String getReportDataById(JsonArray notifReportArray) throws JsonProcessingException {
		String id = "";
		for (int i = 0; i < notifReportArray.size(); i++) {
			id = notifReportArray.get(i).getAsJsonObject().get("id").getAsString();
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn",
				"Getting NotificationMailConfig columnValues: " + id);
		Optional<NotificationReportConfig> findByColumn = notificationReportConfigRepository
				.findById(Long.parseLong(id));
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		return objectMapper.writeValueAsString(findByColumn.get());

	}

	@Override
	public String updateNotificationVaptNew(Object request, String appName, String sessionToken) {

		String jsonString = "";
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			jsonString = mapper.writeValueAsString(request);
		} catch (JsonProcessingException e1) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "updateNotification", "Exception : ", e1.getMessage());
		}
		JsonObject requestt = parser.parse(jsonString).getAsJsonObject();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		LocalDateTime now = LocalDateTime.now();
		String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
		String updateMailResponse = "";
		String updateReportResponse = "";
		JsonParser jsonParser = new JsonParser();
		if (requestt.has(NOT_IF_MAIL)) {

			try {
				JsonArray notifMailArray = requestt.getAsJsonArray(NOT_IF_MAIL);
				notificationMailArrayVaptNew(notifMailArray, dtf, now, adID, sessionToken);

				List<NotificationMailConfigEntity> obj2 = new ArrayList<NotificationMailConfigEntity>();
				for (int i = 0; i < notifMailArray.size(); i++) {
					String string = notifMailArray.get(i).toString();
					NotificationMailConfigEntity obj1 = new ObjectMapper().readValue(string,
							NotificationMailConfigEntity.class);
					obj2.add(obj1);
				}

				List<NotificationMailConfigEntity> notificationMailConfig = objNotificationMailConfigRepository
						.saveAll(obj2);
				updateMailResponse = mapper.writeValueAsString(notificationMailConfig);
				JsonArray notifMail = jsonParser.parse(updateMailResponse).getAsJsonArray();
				notificationMailDetails(notifMail);
				updateMailResponse = notifMail.size() > 0 ? notifMail.toString() : updateMailResponse;

				if (!StringUtils.hasText(updateMailResponse)) {
					updateMailResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				updateMailResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		if (requestt.has(NOT_IF_REPORT)) {
			try {
				JsonArray notifReportArray = requestt.getAsJsonArray(NOT_IF_REPORT);
				notificationReportArrayVaptNew(notifReportArray, dtf, now, adID);
				List<NotificationReportConfig> obj2 = new ArrayList<NotificationReportConfig>();
				for (int i = 0; i < notifReportArray.size(); i++) {
					String string = notifReportArray.get(i).toString();
					NotificationReportConfig obj1 = new ObjectMapper().readValue(string,
							NotificationReportConfig.class);
					obj2.add(obj1);
				}
				List<NotificationReportConfig> notificationMailConfig = objNotificationReportConfigRepository
						.saveAll(obj2);
				updateReportResponse = mapper.writeValueAsString(notificationMailConfig);
				JsonArray notifReport = jsonParser.parse(updateReportResponse).getAsJsonArray();
				notificationReportDetails(notifReport);
				updateReportResponse = notifReport.size() > 0 ? notifReport.toString() : updateReportResponse;

				if (!StringUtils.hasText(updateReportResponse)) {
					updateReportResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				updateReportResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, HolmesAppConstants.POSTREQUESTMETHOD,
						ERROR + e.getMessage());
			}
		}
		JsonObject jobj = new JsonObject();
		jobj.addProperty(NOT_IF_MAIL, updateMailResponse);
		jobj.addProperty(NOT_IF_REPORT, updateReportResponse);
		return jobj.toString();
	}

	public String createUpdateNotificationViptNew(Object request, String appName, String sessionToken) {

		String jsonString = "";
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		try {
			jsonString = mapper.writeValueAsString(request);
		} catch (JsonProcessingException e1) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "createUpdateNotificationViptNew", "Exception : ", e1);
		}
		JsonObject requestt = parser.parse(jsonString).getAsJsonObject();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		LocalDateTime now = LocalDateTime.now();
		String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
		String createMailResponse = "";
		String createReportResponse = "";
		JsonParser jsonParser = new JsonParser();
		if (requestt.has(NOT_IF_MAIL)) {

			try {
				JsonArray notifMailArray = requestt.getAsJsonArray(NOT_IF_MAIL);
				notificationMailArrayVaptNew(notifMailArray, dtf, now, adID, sessionToken);

				List<NotificationMailConfigEntity> obj2 = new ArrayList<NotificationMailConfigEntity>();
				for (int i = 0; i < notifMailArray.size(); i++) {
					String string = notifMailArray.get(i).toString();
					NotificationMailConfigEntity obj1 = new ObjectMapper().readValue(string,
							NotificationMailConfigEntity.class);
					obj2.add(obj1);
				}

				List<NotificationMailConfigEntity> notificationMailConfig = objNotificationMailConfigRepository
						.saveAll(obj2);
				createMailResponse = mapper.writeValueAsString(notificationMailConfig);
				JsonArray notifMail = jsonParser.parse(createMailResponse).getAsJsonArray();
				notificationMailDetails(notifMail);
				createMailResponse = notifMail.size() > 0 ? notifMail.toString() : createMailResponse;

				if (!StringUtils.hasText(createMailResponse)) {
					createMailResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				createMailResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, "createUpdateNotificationViptNew",
						ERROR + e);
			}
		}
		if (requestt.has(NOT_IF_REPORT)) {
			try {
				JsonArray notifReportArray = requestt.getAsJsonArray(NOT_IF_REPORT);
				notificationReportArrayVaptNew(notifReportArray, dtf, now, adID);
				List<NotificationReportConfig> obj2 = new ArrayList<NotificationReportConfig>();
				for (int i = 0; i < notifReportArray.size(); i++) {
					String string = notifReportArray.get(i).toString();
					NotificationReportConfig obj1 = new ObjectMapper().readValue(string,
							NotificationReportConfig.class);
					obj2.add(obj1);
				}
				List<NotificationReportConfig> notificationMailConfig = objNotificationReportConfigRepository
						.saveAll(obj2);
				createReportResponse = mapper.writeValueAsString(notificationMailConfig);
				JsonArray notifReport = jsonParser.parse(createReportResponse).getAsJsonArray();
				notificationReportDetails(notifReport);
				createReportResponse = notifReport.size() > 0 ? notifReport.toString() : createReportResponse;
				if (!StringUtils.hasText(createReportResponse)) {
					createReportResponse = HolmesAppConstants.FAILED;
				}

			} catch (Exception e) {
				createReportResponse = HolmesAppConstants.ERROR;
				LoggerUtil.log(NotificationServiceImpl.class, Level.ERROR, "createUpdateNotificationViptNew",
						ERROR + e.getMessage());
			}
		}
		JsonObject jobj = new JsonObject();
		jobj.addProperty(NOT_IF_MAIL, createMailResponse);
		jobj.addProperty(NOT_IF_REPORT, createReportResponse);
		return jobj.toString();
	}

	private void notificationMailArrayVaptNew(JsonArray notifMailArray, DateTimeFormatter dtf, LocalDateTime now,
			String adID, String sessionToken) throws Exception {

		LoggerUtil.log(getClass(), Level.INFO, "notificationMailArrayVaptNew() method", " : For This ADID: " + adID);

		for (int i = 0; i < notifMailArray.size(); i++) {

			JsonArray attachmentName = null;
			JsonArray placeHolder = null;

			JsonArray psw = null;

			if (notifMailArray.get(i).getAsJsonObject().has("id")
					&& !notifMailArray.get(i).getAsJsonObject().get("id").getAsString().isEmpty()) {

				String getMailDataDB = getMailDataById(notifMailArray);

				JsonObject jsonObject = new JsonParser().parse(getMailDataDB).getAsJsonObject();

				notifMailArray.get(i).getAsJsonObject().addProperty(UPDATED_BY, adID);

				notifMailArray.get(i).getAsJsonObject().addProperty(UPDATED_DATE, dtf.format(now));

				if (!jsonObject.get("createdDate").isJsonNull()) {
					notifMailArray.get(i).getAsJsonObject().addProperty(CREATED_DATE,
							jsonObject.get("createdDate").getAsString());
				}

				notifMailArray.get(i).getAsJsonObject().addProperty(CREATED_BY,
						jsonObject.get("createdBy").getAsString());

				notifMailArray.get(i).getAsJsonObject().addProperty(ACTIVE_FLAGE, "T");

			} else {

				notifMailArray.get(i).getAsJsonObject().addProperty(CREATED_BY, adID);

				notifMailArray.get(i).getAsJsonObject().addProperty(UPDATED_BY, adID);

				notifMailArray.get(i).getAsJsonObject().addProperty(CREATED_DATE, dtf.format(now));

				notifMailArray.get(i).getAsJsonObject().addProperty(UPDATED_DATE, dtf.format(now));

				notifMailArray.get(i).getAsJsonObject().addProperty(ACTIVE_FLAGE, "T");

			}

			if (notifMailArray.get(i).getAsJsonObject().has(ATTACHMENT_NAME)) {

				attachmentName = notifMailArray.get(i).getAsJsonObject().get(ATTACHMENT_NAME).getAsJsonArray();

				String res = new GsonBuilder().disableHtmlEscaping().create().toJson(attachmentName);

				notifMailArray.get(i).getAsJsonObject().remove(ATTACHMENT_NAME);

				notifMailArray.get(i).getAsJsonObject().addProperty(ATTACHMENT_NAME, res);

			}
			if (notifMailArray.get(i).getAsJsonObject().has(PLACE_HOLDER)) {

				placeHolder = notifMailArray.get(i).getAsJsonObject().get(PLACE_HOLDER).getAsJsonArray();

				String res = gson.toJson(placeHolder);

				notifMailArray.get(i).getAsJsonObject().remove(PLACE_HOLDER);

				notifMailArray.get(i).getAsJsonObject().addProperty(PLACE_HOLDER, res);

			}

			if (notifMailArray.get(i).getAsJsonObject().has(PASSWORD)) {

				psw = notifMailArray.get(i).getAsJsonObject().get(PASSWORD).getAsJsonArray();

				notifMailArray.get(i).getAsJsonObject().remove(PASSWORD);

				storePasswordInAws(psw, notifMailArray.get(i).getAsJsonObject().get("condition").getAsString(),
						sessionToken);

				String res = gson.toJson(psw);

				notifMailArray.get(i).getAsJsonObject().addProperty(PASSWORD, res);

			}

		}

	}

	private void notificationReportArrayVaptNew(JsonArray notifReportArray, DateTimeFormatter dtf, LocalDateTime now,
			String adID) throws JsonProcessingException {

		LoggerUtil.log(getClass(), Level.INFO, "notificationReportArrayVaptNew() method", " : For This ADID: " + adID);

		for (int i = 0; i < notifReportArray.size(); i++) {

			JsonArray reportName = null;

			JsonArray itemColName = null;

			JsonArray action = null;

			if (notifReportArray.get(i).getAsJsonObject().has("id")
					&& !notifReportArray.get(i).getAsJsonObject().get("id").getAsString().isEmpty()) {

				String getReportDataDB = getReportDataById(notifReportArray);

				JsonObject jsonObject = new JsonParser().parse(getReportDataDB).getAsJsonObject();

				notifReportArray.get(i).getAsJsonObject().addProperty(UPDATED_BY, adID);

				notifReportArray.get(i).getAsJsonObject().addProperty(UPDATED_DATE, dtf.format(now));

				notifReportArray.get(i).getAsJsonObject().addProperty(CREATED_DATE,
						jsonObject.get("createdDate").getAsString());

				notifReportArray.get(i).getAsJsonObject().addProperty(CREATED_BY,
						jsonObject.get("createdBy").getAsString());

				notifReportArray.get(i).getAsJsonObject().addProperty(ACTIVE_FLAGE, "T");

			} else {

				notifReportArray.get(i).getAsJsonObject().addProperty(CREATED_BY, adID);

				notifReportArray.get(i).getAsJsonObject().addProperty(UPDATED_BY, adID);

				notifReportArray.get(i).getAsJsonObject().addProperty(CREATED_DATE, dtf.format(now));

				notifReportArray.get(i).getAsJsonObject().addProperty(UPDATED_DATE, dtf.format(now));

				notifReportArray.get(i).getAsJsonObject().addProperty(ACTIVE_FLAGE, "T");

			}

			if (notifReportArray.get(i).getAsJsonObject().has(REPORT_NAME)) {

				reportName = notifReportArray.get(i).getAsJsonObject().get(REPORT_NAME).getAsJsonArray();

				String res = new GsonBuilder().disableHtmlEscaping().create().toJson(reportName);

				notifReportArray.get(i).getAsJsonObject().remove(REPORT_NAME);

				notifReportArray.get(i).getAsJsonObject().addProperty(REPORT_NAME, res);

			}

			if (notifReportArray.get(i).getAsJsonObject().has(ITEM_COL_NAME)) {

				itemColName = notifReportArray.get(i).getAsJsonObject().get(ITEM_COL_NAME).getAsJsonArray();

				notifReportArray.get(i).getAsJsonObject().remove(ITEM_COL_NAME);

				String res = gson.toJson(itemColName);

				notifReportArray.get(i).getAsJsonObject().addProperty(ITEM_COL_NAME, res);

			}

			if (notifReportArray.get(i).getAsJsonObject().has(ACTION)) {

				action = notifReportArray.get(i).getAsJsonObject().get(ACTION).getAsJsonArray();

				notifReportArray.get(i).getAsJsonObject().remove(ACTION);

				String res = gson.toJson(action);

				notifReportArray.get(i).getAsJsonObject().addProperty(ACTION, res);

			}

		}

	}

	@Override
	public List<EmailPlaceHolder> getEmailPlaceHolder() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getPlaceHolder", "started on : " + System.currentTimeMillis());

		List<EmailPlaceHolder> placeHolderList = new ArrayList<>();
		try {
			List<EmailPlaceholderMaster> emailPlaceholderMaster = emailPlaceHolderRepo.getPlaceHolders();
			for (EmailPlaceholderMaster placeHolder : emailPlaceholderMaster) {

				placeHolderList.add(new EmailPlaceHolder(placeHolder.getPlaceHolder(), placeHolder.getValue()));
			}

		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getPlaceHolder",
					"placeHolderList unavailable : " + e.getMessage());
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "getPlaceHolder", "end -> placeHolderList: " + placeHolderList);
		return placeHolderList;
	}
}
