package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaConfigDao;
import com.wipro.fipc.dao.tba.TbaUpdateProcessDao;
import com.wipro.fipc.entity.TbaUpdateJsonKey;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaCDDFieldMapping;
import com.wipro.fipc.entity.tba.TbaPaymentDeliveryType;
import com.wipro.fipc.entity.tba.TbaPaymentDescriptor;
import com.wipro.fipc.entity.tba.TbaPlanDescriptor;
import com.wipro.fipc.entity.tba.TbaUpdateConfig;
import com.wipro.fipc.entity.tba.TbaUpdateMetaData;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.TbaUpdateConfigDto;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.TbaUpdateConfigService;
import com.wipro.fipc.tba.service.UpdateConfigService;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;
import com.wipro.fipc.utils.TableType;

@Service
public class TbaUpdateConfigServiceImpl implements TbaUpdateConfigService {



	@Autowired
	Environment env;

	@Autowired
	TbaConfigDao tbaConfigDao;

	@Autowired
	UpdateConfigService updateConfigService;

	@Autowired
	private Gson gson;

	@Autowired
	CommonGetAdId commonGetUpdatedBy;

	@Autowired
	CustomBeanUtils customBeanUtils;

	/* Save TbaUpdateConfig Details */

	@Override
	public String createTbaUpdateConfig(List<TbaUpdateConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException {

		String res = "";

		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaUpdateConfig", "request size : {0}", entity.size());
		List<TbaUpdateConfigDto> newUpdateConfigList = new ArrayList<>();
		Iterator<TbaUpdateConfigDto> ite = entity.iterator();
		while (ite.hasNext()) {
			TbaUpdateConfigDto entityUpdate = ite.next();
			entityUpdate.setActiveFlag("T");
			entityUpdate.setCreatedDate(new Date());
			String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
			entityUpdate.setCreatedBy(adID);
			entityUpdate.setUpdatedDate(new Date());
			entityUpdate.setUpdatedBy(adID);
			updateEventNameAndLongDesc(entityUpdate);
			
			LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "entityUpdate: " + entityUpdate.toString());
			newUpdateConfigList.add(entityUpdate);

			LoggerUtil.log(this.getClass(), Level.INFO, "createTbaUpdateConfig>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Create" + ",PJMID = "
							+ entityUpdate.getProcessJobMappingId() + ",ADID: " + adID);
		}

		List<com.wipro.fipc.pojo.tba.TbaUpdateConfigDto> newLayout = new ArrayList<>();
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		for (TbaUpdateConfigDto req : newUpdateConfigList) {

			String convertedString = mapper.writeValueAsString(req);
			mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			com.wipro.fipc.pojo.tba.TbaUpdateConfigDto obj = mapper.readValue(convertedString,
					com.wipro.fipc.pojo.tba.TbaUpdateConfigDto.class);
			newLayout.add(obj);
		}
		List<CommonResRowBO> mydata = tbaConfigDao.saveIfNotDuplicate(newLayout);

		res = mapper.writeValueAsString(mydata);

		ResponseDto response = new ResponseDto();
		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		return mapper.writeValueAsString(response);

	}

	/* Get the TbaUpdateMetadata */
	@Override
	public String getTbaUpdateMetadata(int panelId, int clientId) {
		String res = "";
		List<TbaUpdateMetaData> mydata = updateConfigService.getTbaUpdateMetaData(panelId, clientId);
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateMetadata()", "JsonProcessingException: " + e);
		}

		return res;
	}

	/* Get the TbaUpdateMetadata */
	@Override
	public String getTbaUpdateMetadata(int panelId, int clientId, Integer activityId) {
		String res = "";
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = activityProcessor(activityId, panelId, clientId, objectMapper);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateMetadata()", "JsonProcessingException: " + e);
		}

		return res;
	}

	/**
	 * This method process activity as an input parameter from request controller
	 * 
	 * @param activityId
	 * @param panelId
	 * @param clientId
	 * @param objectMapper
	 * @return objectJSON
	 * @throws JsonProcessingException
	 */
	@Autowired
	TbaUpdateProcessDao tbaUpdateProcessDao;
	public String activityProcessor(Integer activityId, int panelId, int clientId, ObjectMapper objectMapper)
			throws JsonProcessingException {
		String tableType=tbaUpdateProcessDao.findTableTypeByEventidAndPanelId(activityId,panelId,clientId);
		switch (TableType.getTableType(tableType))
		{
		case CDD:
			return objectMapper.writeValueAsString(mapCddList(
					updateConfigService.getTbaCDDFieldMappingData(panelId, clientId), TbaUpdateMetaData.class));
		case PYMNT:
			return objectMapper.writeValueAsString(mapListPayment(
					updateConfigService.getTbaPaymentDescriptorData(panelId, clientId), com.wipro.fipc.pojo.TbaUpdateMetaData.class));
		case PYMNT_DED:
			return objectMapper.writeValueAsString(mapList(
					updateConfigService.getTbaPaymentDescriptorData(panelId, clientId), TbaUpdateMetaData.class));
		case PYMNT_PLAN_ATTR:
		case PYMNT_PLAN_BENE:
			return objectMapper.writeValueAsString(mapPlanList(
					updateConfigService.getTbaPlanDescriptorData(panelId, clientId), com.wipro.fipc.pojo.TbaUpdateMetaData.class, tableType));
		default:
			return objectMapper.writeValueAsString(updateConfigService.getTbaUpdateMetaData(panelId, clientId));
		}

	}
	
	private List<com.wipro.fipc.pojo.TbaUpdateMetaData> mapListPayment(List<TbaPaymentDescriptor> tbaPaymentDescriptor,
			Class< com.wipro.fipc.pojo.TbaUpdateMetaData> tbaUpdateMetaData){
		
		List<com.wipro.fipc.pojo.TbaUpdateMetaData> tbaUpdateMetaDatas=new ArrayList<>();
	      for(TbaPaymentDescriptor tbaPtDescriptor:tbaPaymentDescriptor) {
	    	  com.wipro.fipc.pojo.TbaUpdateMetaData data=null;
	    	  List<TbaPaymentDeliveryType> tbaPaymentDeliveryType = tbaPtDescriptor.getTbaPaymentDeliveryType();
	    	  if(!CollectionUtils.isEmpty(tbaPaymentDeliveryType)) {
	    	  for(TbaPaymentDeliveryType tbaPaymentype:tbaPaymentDeliveryType) {
	    		  data=new com.wipro.fipc.pojo.TbaUpdateMetaData();
	    		  BeanUtils.copyProperties(tbaPtDescriptor, data);
	    		  data.setSubMetaData(tbaPaymentype.getDeliveryTypeText());
	    		  data.setSubMetaDataId(String.valueOf(tbaPaymentype.getDeliveryTypeId()));
	    		  data.setTransId(tbaPtDescriptor.getPaymentId());
	    		  data.setMetaData(tbaPtDescriptor.getPaymentShortDescription());
	    		  tbaUpdateMetaDatas.add(data);
	    	  }}
	    	  else {
	    		  data=new com.wipro.fipc.pojo.TbaUpdateMetaData();
	    		  BeanUtils.copyProperties(tbaPtDescriptor, data);
	    		  data.setTransId(tbaPtDescriptor.getPaymentId());
	    		  data.setMetaData(tbaPtDescriptor.getPaymentShortDescription());
	    		  tbaUpdateMetaDatas.add(data);
	    	  }
	      }
	      return tbaUpdateMetaDatas;
	}
	
	private List<TbaUpdateMetaData> mapList(List<TbaPaymentDescriptor> tbaPaymentDescriptor,
			Class<TbaUpdateMetaData> tbaUpdateMetaData) {
		ModelMapper modelMapper = new ModelMapper();

		TypeMap<TbaPaymentDescriptor, TbaUpdateMetaData> propertyMapper = modelMapper
				.createTypeMap(TbaPaymentDescriptor.class, TbaUpdateMetaData.class);
		propertyMapper.addMapping(TbaPaymentDescriptor::getPaymentId, TbaUpdateMetaData::setTransId);
		propertyMapper.addMapping(TbaPaymentDescriptor::getPaymentShortDescription, TbaUpdateMetaData::setMetaData);


		return tbaPaymentDescriptor.stream().map(element -> modelMapper.map(element, tbaUpdateMetaData))
				.collect(Collectors.toList());
	}
	
	private List<com.wipro.fipc.pojo.TbaUpdateMetaData> mapPlanList(List<TbaPlanDescriptor> tbaPlanDescriptor,
			Class<com.wipro.fipc.pojo.TbaUpdateMetaData> tbaUpdateMetaData, String tableType) {
		LoggerUtil.log(this.getClass(), Level.INFO, " method - mapPlanList ","To Convert DBElectionId to tTransId, DBElectionShortDescription to MetaData");
		ModelMapper modelMapper = new ModelMapper();
		TypeMap<TbaPlanDescriptor, com.wipro.fipc.pojo.TbaUpdateMetaData> propertyMapper = modelMapper
				.createTypeMap(TbaPlanDescriptor.class, com.wipro.fipc.pojo.TbaUpdateMetaData.class);
		propertyMapper.addMapping(TbaPlanDescriptor::getPlanId, com.wipro.fipc.pojo.TbaUpdateMetaData::setTransId);
		propertyMapper.addMapping(TbaPlanDescriptor::getPlanShortDescription, com.wipro.fipc.pojo.TbaUpdateMetaData::setMetaData);
		propertyMapper.addMapping(TbaPlanDescriptor::getDbElectionShortDescription, com.wipro.fipc.pojo.TbaUpdateMetaData::setSubMetaData);
		if(TableType.PYMNT_PLAN_BENE.getType().equals(tableType)) {	
		propertyMapper.addMapping(TbaPlanDescriptor::getDbElectionIdToString, com.wipro.fipc.pojo.TbaUpdateMetaData::setSubMetaDataId);
		}else if(TableType.PYMNT_PLAN_ATTR.getType().equals(tableType)){
		propertyMapper.addMapping(TbaPlanDescriptor::getBenefitShortDescription, com.wipro.fipc.pojo.TbaUpdateMetaData::setSubMetaDataId);
	    }
		return tbaPlanDescriptor.stream().map(element -> modelMapper.map(element, tbaUpdateMetaData))
				.collect(Collectors.toList());
	}
	
	/**
	 * This method is used for mapping between metadata and cddfield data
	 * @param tbaCDDFieldMappingData
	 * @param tbaUpdateMetaData
	 * @return
	 */
	private Object mapCddList(List<TbaCDDFieldMapping> tbaCDDFieldMappingData, Class<TbaUpdateMetaData> tbaUpdateMetaData) {
		ModelMapper modelMapper = new ModelMapper();
		TypeMap<TbaCDDFieldMapping, TbaUpdateMetaData> propertyMapper = modelMapper
				.createTypeMap(TbaCDDFieldMapping.class, TbaUpdateMetaData.class);
		propertyMapper.addMapping(TbaCDDFieldMapping::getCddFieldDesc, TbaUpdateMetaData::setMetaData);
		propertyMapper.addMapping(TbaCDDFieldMapping::getCddFieldCode, TbaUpdateMetaData::setTransId);
		
		return tbaCDDFieldMappingData.stream().map(element -> modelMapper.map(element, tbaUpdateMetaData))
				.collect(Collectors.toList());
		
	}

	/* Get TbaUpdateProcess Details */

	@Override
	public String getTbaUpdateProcess(int clientId, boolean check) throws URISyntaxException, IOException {

		Set<com.wipro.fipc.pojo.tba.TbaUpdateProcessDto> myData = updateConfigService.getUpdateInquiryNames(clientId,check);
		myData.stream().filter(data -> org.springframework.util.StringUtils.hasText(data.getEventLongDesc()))
				.forEach(data -> data.setEventName(
						data.getEventName().concat(HolmesAppConstants.SEPARATOR).concat(data.getEventLongDesc())));
		
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String res = objectMapper.writeValueAsString(myData);

		return res;

	}

	/* Get TbaUpdateJsonKey */
	@Override
	public String getTbaUpdateJsonKey(String columnName, String columnValue) {
		String res = "";

		List<TbaUpdateJsonKey> mydata = updateConfigService.findRecordByColumn(columnName, columnValue);
		List<TbaUpdateJsonKey> tbaUpdateJsonKeyList = new ArrayList<>();
		
		List<String> tbaFieldNameList = new ArrayList<>();
		for (TbaUpdateJsonKey tbaUpdateJsonKey : mydata) {
			tbaFieldNameList.add(tbaUpdateJsonKey.getTbaFieldName());
		}
		
		for (TbaUpdateJsonKey tbaUpdateJsonKey : mydata) {
			int frequency = Collections.frequency(tbaFieldNameList, tbaUpdateJsonKey.getTbaFieldName());
			if(frequency > 1)
				tbaUpdateJsonKey.setTbaFieldName(tbaUpdateJsonKey.getTbaFieldName().concat("-").concat(tbaUpdateJsonKey.getBaseKey()));
			tbaUpdateJsonKeyList.add(tbaUpdateJsonKey);
		}
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateJsonKey()", "JsonProcessingException: " + e);
		}
		return res;
	}

	/* Update the TbaUpdateConfig */

	public String updateTbaUpdateConfig(List<TbaUpdateConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException {

		LoggerUtil.log(this.getClass(), Level.INFO, "updateTbaUpdateConfig", "request size : {0}", entity.size());		
		List<TbaUpdateConfigDto> newUpdateConfigList = new ArrayList<>();
		Iterator<TbaUpdateConfigDto> ite = entity.iterator();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		while (ite.hasNext()) {
			TbaUpdateConfigDto entityUpdate = ite.next();
			com.wipro.fipc.entity.tba.TbaUpdateConfig mydata = updateConfigService
					.findById(entityUpdate.getId());
			String resp = objectMapper.writeValueAsString(mydata);

			TbaUpdateConfig config = null;
			if (StringUtils.isNotBlank(resp)) {
				config = new ObjectMapper().readValue(resp, TbaUpdateConfig.class);
				char c = config.getAddManualFlag();
				String s = String.valueOf(c);
				entityUpdate.setAddManualFlag(s);
				entityUpdate.setRerunFlag(String.valueOf(config.getRerunFlag()));
				entityUpdate.setCreatedBy(config.getCreatedBy());
				entityUpdate.setCreatedDate(config.getCreatedDate());
				entityUpdate.setBaseKey(config.getBaseKey());
			}
			updateEventNameAndLongDesc(entityUpdate);
			entityUpdate.setActiveFlag("T");
			String adID = commonGetUpdatedBy.getADID(appName, sessionToken);
			entityUpdate.setUpdatedDate(new Date());
			entityUpdate.setUpdatedBy(adID);
			LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "entityUpdate: " + entityUpdate.toString());
			newUpdateConfigList.add(entityUpdate);
			LoggerUtil.log(this.getClass(), Level.INFO, "updateTbaUpdateConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
							+ entityUpdate.getProcessJobMappingId() + ",ADID: " + adID);
		}
		List<com.wipro.fipc.pojo.tba.TbaUpdateConfigDto> newLayout = new ArrayList<>();
		for (TbaUpdateConfigDto req : entity) {
			String convertedString = null;
			try {
				convertedString = objectMapper.writeValueAsString(req);
			} catch (JsonProcessingException e1) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "updateTbaUpdateConfig()", "JsonProcessingException: " + e1);
			}
			com.wipro.fipc.pojo.tba.TbaUpdateConfigDto obj = null;
			try {
				obj = objectMapper.readValue(convertedString, com.wipro.fipc.pojo.tba.TbaUpdateConfigDto.class);
			} catch (IOException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "updateTbaUpdateConfig()", "IOException: " + e);
			}
			newLayout.add(obj);
		}
		List<CommonResRowBO> mydata1 = tbaConfigDao.saveIfNotDuplicate(newLayout);

		String res = objectMapper.writeValueAsString(mydata1);
		ResponseDto response = new ResponseDto();
		response.setData(res);
		response.setStatus("success");
		response.setMessage("Records Saved Successfully.");
		return objectMapper.writeValueAsString(response);
	}

	private void updateEventNameAndLongDesc(TbaUpdateConfigDto entityUpdate) {
		if (org.springframework.util.StringUtils.hasLength(entityUpdate.getEventName())) {
			String eventNameAndLongDes = entityUpdate.getEventName();
			int firstIndexOfSeparator = eventNameAndLongDes.indexOf(HolmesAppConstants.SEPARATOR);
			if (firstIndexOfSeparator != -1) {
				entityUpdate.setEventName(eventNameAndLongDes.substring(0, firstIndexOfSeparator));
				entityUpdate.setEventLongDesc(
						eventNameAndLongDes.substring(firstIndexOfSeparator + HolmesAppConstants.SEPARATOR.length()));
			}
		}
	}

	/* GetTbaUpdateConfig Details */
	@Override
	public String getTbaUpdateConfig(String columnName, String columnValue) {
		List<TbaUpdateConfig> mydata = updateConfigService.findByColumn(columnName,
				columnValue);
		String tbaUpdateConfig = "";
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			tbaUpdateConfig = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e1) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateConfig()", "JsonProcessingException: " + e1);
		}
			List<com.wipro.fipc.model.generated.TbaUpdateConfig> tbaConfigList;
			try {
			tbaConfigList = Arrays.asList(new ObjectMapper().readValue(tbaUpdateConfig,
					com.wipro.fipc.model.generated.TbaUpdateConfig[].class));

			tbaConfigList = tbaConfigList.stream().filter(obj -> String.valueOf(obj.getRerunFlag()).equals("N"))
					.collect(Collectors.toList());
			
			tbaConfigList.stream().filter(data -> org.springframework.util.StringUtils.hasText(data.getEventLongDesc()))
					.forEach(data -> data.setEventName(
							data.getEventName().concat(HolmesAppConstants.SEPARATOR).concat(data.getEventLongDesc())));
			
			gson = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").serializeNulls().create();
			return gson.toJson(tbaConfigList);
			} catch (JsonMappingException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateConfig()", "JsonMappingException: " + e);
			} catch (JsonProcessingException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaUpdateConfig()", "JsonProcessingException: " + e);
			}
			return null;
	}

	/* Get All the TbaUpdateConfig */

	@Override
	public String getAllTbaUpdateConfig() {
		List<com.wipro.fipc.entity.tba.TbaUpdateConfig> mydata = updateConfigService.list();

		String res = "";
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getAllTbaUpdateConfig()", "JsonProcessingException: " + e);
		}
		return res;
	}

	/* Deleted Operation Performed */
	@Override
	public String deleteUpdateEventConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken)
			throws URISyntaxException {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		entities.stream().forEach(s -> {
			s.setUpdatedBy(adId);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteUpdateEventConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});
		List<com.wipro.fipc.entity.tba.TbaUpdateConfig> newLayout = new ArrayList<>();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		for (CommonDeleteDTO req : entities) {

			String convertedString = null;
			try {

				convertedString = objectMapper.writeValueAsString(req);
			} catch (JsonProcessingException e1) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "deleteUpdateEventConfig", "JsonProcessingException: " + e1);
			}
			com.wipro.fipc.entity.tba.TbaUpdateConfig obj = null;
			try {
				obj = objectMapper.readValue(convertedString,
						com.wipro.fipc.entity.tba.TbaUpdateConfig.class);
			} catch (IOException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "deleteUpdateEventConfig", "IOException: " + e);
			}
			newLayout.add(obj);
		}
		List<CommonRowBO> mydata = updateConfigService.deletemultiplerows(1, newLayout);
		String res = null;
		try {
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "deleteUpdateEventConfig", "JsonProcessingException: " + e);
		}

		return res;
	}

	/* Get the TbaEditsMasterDetails */

	@Override
	public String getTbaEditsMasterDetails(String clientId, String eventName) throws URISyntaxException, IOException {

		if (org.springframework.util.StringUtils.hasText(eventName))
			eventName = eventName.split(HolmesAppConstants.SEPARATOR)[0];
		String clientID = Integer.toString(customBeanUtils.checkForClientCode(clientId));
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEditsMasterDetails>>>>>>>>>>>",
				"Method Type= " + HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + ",clientId=" + clientId
						+ ",eventName=" + eventName);
		List<String> columnName1 = new ArrayList<>();
		columnName1.add("client_id");
		columnName1.add("event_name");

		List<String> columnCondition1 = new ArrayList<>();
		columnCondition1.add(HolmesAppConstants.EQUAL);
		columnCondition1.add(HolmesAppConstants.EQUAL);

		List<String> columnValue1 = new ArrayList<>();
		columnValue1.add(clientID);
		columnValue1.add(eventName);

		List<com.wipro.fipc.entity.tba.TbaEditsMaster> mydata = updateConfigService
				.findByMultiColumnCondition(columnName1, columnCondition1, columnValue1);
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		String res = objectMapper.writeValueAsString(mydata);

		return res;
	}

}