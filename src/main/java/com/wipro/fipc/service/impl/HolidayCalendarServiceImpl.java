package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.batch.HolidayCalendarDao;
import com.wipro.fipc.entity.ClientDetails;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.batch.HolidayCalendar;
import com.wipro.fipc.model.HolidayCalendarRequest;
import com.wipro.fipc.model.HolidayCalendarResponse;
import com.wipro.fipc.service.IHolidayCalendarService;
import com.wipro.fipc.utils.CustomBeanUtils;
import com.wipro.fipc.utils.DateUtils;

@Service
public class HolidayCalendarServiceImpl implements IHolidayCalendarService {

	@Autowired
	GenericDao<ClientDetails> clientGenericDao;

	@Autowired
	GenericDao<HolidayCalendar> holidayGenericDao;

	@Autowired
	private BaseDao<HolidayCalendar> holidayDao;

	@Autowired
	HolidayCalendarDao holidayRepo;
	
	@Autowired
	private CustomBeanUtils customBeanUtils;

	protected static final String HOLIDAY_CALENDAR = "HOLIDAY_CALENDAR";
	protected static final String EMAILS_SCHEDULER_SCHEMA = "emails_scheduler";

	protected static final String CLIENT_DETAILS = "client_details";
	protected static final String COMMON_SCHEMA = "common";

	private String getHolidayCalendar(String clientCode) {
		List<ClientDetails> clientDetailsList = null;
		try {
			int clientId = customBeanUtils.checkForClientCode(clientCode);
			clientDetailsList = clientGenericDao.findRecordByColumn(ClientDetails.class, COMMON_SCHEMA, CLIENT_DETAILS,
					"client_code", Integer.toString(clientId));
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", " Client details not found ", e);
		}

		if (!CollectionUtils.isEmpty(clientDetailsList)) {
			ClientDetails clientDetailsObj = clientDetailsList.get(0);
			LoggerUtil.log(this.getClass(), Level.INFO, " ", "ClientDetailsObj : " + clientDetailsObj);

			String id = String.valueOf(clientDetailsObj.getId());
			String jsonStr = null;
			try {
				List<HolidayCalendar> holidayCalendar = holidayGenericDao.findRecordByColumn(HolidayCalendar.class,
						EMAILS_SCHEDULER_SCHEMA, HOLIDAY_CALENDAR, "client_id", id);
				jsonStr = new ObjectMapper().writeValueAsString(holidayCalendar);
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "", " Client details not found ", e);
			}

			return jsonStr;
		} else
			return "ClientCode is not present";
	}

	private String getHolidayCalendarByYear(String clientCode, String selectedYear) {
		LoggerUtil.log(this.getClass(), Level.ERROR, "getHolidayCalendarByYear",
				"Method start -> clientCode: " + clientCode + ", : " + selectedYear);

		if (!StringUtils.hasText(selectedYear))
			return "Selected year is invalid";

		List<ClientDetails> clientDetailsList = null;
		try {
			clientDetailsList = clientGenericDao.findRecordByColumn(ClientDetails.class, COMMON_SCHEMA, CLIENT_DETAILS,
					"client_code", clientCode);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getHolidayCalendarByYear", " Client details not found ", e);
		}

		if (!CollectionUtils.isEmpty(clientDetailsList)) {
			ClientDetails clientDetailsObj = clientDetailsList.get(0);
			LoggerUtil.log(this.getClass(), Level.INFO, "getHolidayCalendarByYear",
					"ClientDetailsObj : " + clientDetailsObj);

			String id = String.valueOf(clientDetailsObj.getId());
			String jsonStr = null;
			try {
				List<HolidayCalendar> holidayCalendar = null;
				if (selectedYear.equalsIgnoreCase("ALL")) {
					holidayCalendar = holidayGenericDao.findRecordByColumn(HolidayCalendar.class,
							EMAILS_SCHEDULER_SCHEMA, HOLIDAY_CALENDAR, "client_id", id);
				} else {
					ColumnConditionParam cond1 = new ColumnConditionParam();
					cond1.setColName("client_id");
					cond1.setColCondion("eq");
					cond1.setColValue(id);

					ColumnConditionParam cond2 = new ColumnConditionParam();
					cond2.setColName("holiday_date");
					cond2.setColCondion("lk");
					cond2.setColValue(selectedYear);

					List<ColumnConditionParam> columnConditionParams = Arrays.asList(cond1, cond2);
					holidayCalendar = holidayGenericDao.findByMultiColumnCondition(HolidayCalendar.class,
							EMAILS_SCHEDULER_SCHEMA, HOLIDAY_CALENDAR, columnConditionParams);
				}

				LoggerUtil.log(this.getClass(), Level.INFO, "getHolidayCalendarByYear",
						"Holiday calendar size: " + holidayCalendar.size());
				jsonStr = new ObjectMapper().writeValueAsString(holidayCalendar);
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "getHolidayCalendarByYear", " Client details not found ",
						e);
			}
			return jsonStr;
		} else
			return "ClientCode is not present";
	}

	private HolidayCalendarResponse postHolidayCalendar(HolidayCalendarRequest[] holidayCalendarRequestList, ClientDetails clientDetail) {
		LoggerUtil.log(this.getClass(), Level.INFO, "postHolidayCalendar", "Method start ->  ");
		HolidayCalendarResponse holidayCalendarResponse = new HolidayCalendarResponse();
		List<HolidayCalendar> holidayList = new ArrayList<>();
		List<HolidayCalendar> delHolidayList = new ArrayList<>();
		List<String>holidayDates=holidayRepo.holidayDates(clientDetail.getId());
		List<HolidayCalendarRequest> validHolidays = Arrays.stream(holidayCalendarRequestList).filter(x -> !holidayDates.contains(x.getHolidayDate()))
				.collect(Collectors.toList());
		for (HolidayCalendarRequest hcr : validHolidays) {
			if (hcr.getAction().equalsIgnoreCase("ADD")) {
				LoggerUtil.log(this.getClass(), Level.INFO, "postHolidayCalendar", "Adding Holiday Calendars ->  requestList: ");
				HolidayCalendar holidayCalendar = new HolidayCalendar();
				holidayCalendar.setHolidayDate(hcr.getHolidayDate());
				holidayCalendar.setHolidayDesc(hcr.getHolidayDesc());
				holidayCalendar.setClientDetails(clientDetail);
				holidayCalendar.setCreatedBy(hcr.getUpdatedBy());
				holidayCalendar.setCreatedDate(DateUtils.getDateInRequiredFormat(HolmesAppConstants.SQL_DATE_FORMAT));
				holidayList.add(holidayCalendar);
			} else if (hcr.getAction().equalsIgnoreCase("UPD")) {
				Optional<HolidayCalendar> hcalendar = holidayRepo.findById(Long.parseLong(hcr.getId()));
				if (hcalendar.isPresent()) {
					HolidayCalendar holidayCalendar = hcalendar.get();
					holidayCalendar.setHolidayDate(hcr.getHolidayDate());
					holidayCalendar.setHolidayDesc(hcr.getHolidayDesc());
					holidayCalendar.setClientDetails(clientDetail);
					holidayCalendar.setUpdatedBy(hcr.getUpdatedBy());
					holidayCalendar.setUpdatedDate(DateUtils.getDateInRequiredFormat(HolmesAppConstants.SQL_DATE_FORMAT));
					holidayList.add(holidayCalendar);
				}
			}
		}
		for (HolidayCalendarRequest hcr : holidayCalendarRequestList) {
			if (hcr.getAction().equalsIgnoreCase("DEL")) {
				LoggerUtil.log(this.getClass(), Level.INFO, "postHolidayCalendar", "Deleting Holiday Calendars");
				HolidayCalendar holidayCalendar = new HolidayCalendar();
				holidayCalendar.setId(Long.valueOf(hcr.getId()));
				delHolidayList.add(holidayCalendar);
			}
		}

		try {
			if (!CollectionUtils.isEmpty(holidayList)) holidayDao.saveAll(holidayList);
			if (!CollectionUtils.isEmpty(delHolidayList)) holidayDao.deleteAll(delHolidayList);
			holidayCalendarResponse.setMessage(HolmesAppConstants.SUCCESS);
			return holidayCalendarResponse;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "postHolidayCalendar", "Exception occurs while saving/Updating the HolidayCalendar : ", e);
		}
		holidayCalendarResponse.setMessage(HolmesAppConstants.FAILED);
		return holidayCalendarResponse;
	}

	@Override
	public HolidayCalendarResponse newputHolidayRequests(String requestList, String clientCode) throws JsonParseException, JsonMappingException, IOException {
		LoggerUtil.log(this.getClass(), Level.INFO, "newputHolidayRequests", "Method start ->  requestList: " + requestList + ", clientCode: " + clientCode);
		boolean dateValidationFlag = false;
		Gson gson = new Gson();
		HolidayCalendarRequest[] requests = gson.fromJson(requestList, HolidayCalendarRequest[].class);
		HolidayCalendarResponse hcr = new HolidayCalendarResponse();
		List<ClientDetails> clientDetails = new ArrayList<>();
		ClientDetails clientDetail = null;
		try {
			clientDetails = clientGenericDao.findRecordByColumn(ClientDetails.class, COMMON_SCHEMA, CLIENT_DETAILS, "client_code", clientCode);
			if (!CollectionUtils.isEmpty(clientDetails)) clientDetail = clientDetails.get(0);
			else {
				hcr.setMessage(HolmesAppConstants.FAILED + ": Client Not Found");
				return hcr;
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "newputHolidayRequests", "Exception: ", e);
		}

		for (HolidayCalendarRequest hcreq : requests) {
			dateValidationFlag = checkDateFormat(hcreq.getHolidayDate());
			if (!dateValidationFlag)
				break;
		}

		if (dateValidationFlag) {
			//Remove records with holiday dates before the current date
			List<HolidayCalendarRequest> validHolidayRequest = Arrays.stream(requests).filter(this::isValidHolidayDate).collect(Collectors.toList());
			// Remove Duplicate Holiday dates, Keeping The First occurence
			List<HolidayCalendarRequest> uniqueHolidayRequest = new ArrayList<>();
			List<String> uniqueHolidays = new ArrayList<>();
			validHolidayRequest.forEach(holidayCalendarRequest -> {
				if (!uniqueHolidays.contains(holidayCalendarRequest.getHolidayDate())) {
					uniqueHolidays.add(holidayCalendarRequest.getHolidayDate());
					uniqueHolidayRequest.add(holidayCalendarRequest);
				}
			});
			return postHolidayCalendar(uniqueHolidayRequest.toArray(new HolidayCalendarRequest[0]), clientDetail);
		} else {
			hcr.setMessage(HolmesAppConstants.FAILED + ": INVALID DATA OR DATE FORMAT");
			return hcr;
		}
	}

	private boolean isValidHolidayDate(HolidayCalendarRequest request) {
		LocalDate currentDate = LocalDate.now();
		try {
			LocalDate holidayDate = LocalDate.parse(request.getHolidayDate(), HolmesAppConstants.dateFormat);
			return !holidayDate.isBefore(currentDate);
		} catch (DateTimeParseException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "isValidHolidayDate", "Invalid DateFormat for Holiday Date" + request.getHolidayDate());
			return false;

		}
	}
	@Override
	public List<HolidayCalendarResponse> autoPopulateHolidayCalendarByYear(String clientCode, String selectedYear)
			throws ParseException {
		LoggerUtil.log(this.getClass(), Level.INFO, "autoPopulateHolidayCalendarByYear",
				"Method start -> clientCode: " + clientCode + ", selectedYear: " + selectedYear);

		List<HolidayCalendarResponse> response = new ArrayList<>();
		String holidayCalendarRecords = getHolidayCalendarByYear(clientCode, selectedYear);
		try {
			HolidayCalendar[] holidayList = new ObjectMapper().readValue(holidayCalendarRecords,
					HolidayCalendar[].class);
			if (holidayList != null && holidayList.length > 0) {
				for (HolidayCalendar hc : holidayList) {
					HolidayCalendarResponse hcr = new HolidayCalendarResponse();
					hcr.setHolidayDate(hc.getHolidayDate());
					hcr.setHolidayDesc(hc.getHolidayDesc());
					hcr.setClientCode(hc.getClientDetails().getClientCode());
					hcr.setClientName(hc.getClientDetails().getClientName());
					hcr.setId(hc.getId());
					response.add(hcr);
				}
			} else
				LoggerUtil.log(this.getClass(), Level.ERROR, "autoPopulateHolidayCalendarByYear",
						"Holiday Calendar table is empty");
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "autoPopulateHolidayCalendarByYear",
					"Holiday Calendar is unavailable to process further!");
		}
		return response;
	}

	private boolean checkDateFormat(String strDate) {
		/*
		 * Create Date object parse the string into date
		 */
		try {
			HolmesAppConstants.dateFormat.parse(strDate);
			LoggerUtil.log(this.getClass(), Level.INFO, "checkDateFormat", strDate + " is valid date format");
		}
		/* Date format is invalid */ catch (DateTimeParseException e) {
			LoggerUtil.log(this.getClass(), Level.INFO, "checkDateFormat", strDate + " is Invalid Date format");
			return false;
		}
		/* Return true if date format is valid */
		return true;
	}

	@Override
	public void getTemplate(String clientCode, HttpServletResponse holidayCalendar) throws IOException, ParseException {
		try (XSSFWorkbook workbook = new XSSFWorkbook()) {
			LoggerUtil.log(getClass(), Level.INFO, "getTemplate", "Updating Excel Data: ");
			XSSFSheet spreadsheet = workbook.createSheet("HolidayCalendar");
			XSSFCellStyle style = workbook.createCellStyle();
			style.setBorderBottom(BorderStyle.THIN);
			style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
			style.setBorderRight(BorderStyle.THIN);
			style.setRightBorderColor(IndexedColors.BLACK.getIndex());
			style.setBorderTop(BorderStyle.THIN);
			style.setTopBorderColor(IndexedColors.BLACK.getIndex());
			style.setBorderLeft(BorderStyle.THIN);
			style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
			style.setFillBackgroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
			style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			style.setAlignment(HorizontalAlignment.CENTER);
			XSSFFont font = workbook.createFont();
			font.setBold(true);
			font.setColor(IndexedColors.WHITE.getIndex());
			style.setFont(font);
			XSSFCellStyle dataStyle = workbook.createCellStyle();
			dataStyle.setBorderBottom(BorderStyle.THIN);
			dataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
			dataStyle.setBorderRight(BorderStyle.THIN);
			dataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
			dataStyle.setBorderTop(BorderStyle.THIN);
			dataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
			dataStyle.setBorderLeft(BorderStyle.THIN);
			dataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
			XSSFRow row;
			XSSFCell cell;
			int rowCount = 0;
			List<HolidayCalendarResponse> response = autoPopulateHolidayCalendar(clientCode);
			rowCount += 2;
			row = spreadsheet.createRow(rowCount);
			cell = row.createCell(row.getLastCellNum() + 1);
			cell.setCellValue("Holiday");
			cell.setCellStyle(style);
			cell = row.createCell(row.getLastCellNum());
			cell.setCellValue("Date (dd/MM/yyyy)");
			cell.setCellStyle(style);
			rowCount += 1;
			if (response != null) {
				for (HolidayCalendarResponse hcr : response) {
					row = spreadsheet.createRow(rowCount);
					cell = row.createCell(row.getLastCellNum() + 1);
					cell.setCellValue(hcr.getHolidayDesc());
					cell = row.createCell(row.getLastCellNum());
					cell.setCellValue(hcr.getHolidayDate());
					rowCount += 1;
				}
			}

			try {
				workbook.write(holidayCalendar.getOutputStream());
			} catch (Exception e) {
				LoggerUtil.log(getClass(), Level.ERROR, "getTemplate", "Unable to download", e);
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTemplate", "XFSS error", e);
		}
	}

	private List<HolidayCalendarResponse> autoPopulateHolidayCalendar(String clientCode) throws ParseException {
		LoggerUtil.log(this.getClass(), Level.INFO, "autoPopulateHolidayCalendar",
				"Method start -> clientCode: " + clientCode);

		List<HolidayCalendarResponse> response = new ArrayList<>();
		String holidayCalendarRecords = getHolidayCalendar(clientCode);
		try {
			HolidayCalendar[] holidayList = new ObjectMapper().readValue(holidayCalendarRecords,
					HolidayCalendar[].class);
			if (holidayList != null && holidayList.length > 0) {
				for (HolidayCalendar hc : holidayList) {
					HolidayCalendarResponse hcr = new HolidayCalendarResponse();
					hcr.setHolidayDate(hc.getHolidayDate());
					hcr.setHolidayDesc(hc.getHolidayDesc());
					hcr.setClientCode(hc.getClientDetails().getClientCode());
					hcr.setClientName(hc.getClientDetails().getClientName());
					hcr.setId(hc.getId());
					response.add(hcr);

					LoggerUtil.log(this.getClass(), Level.INFO, "autoPopulateHolidayCalendar",
							"Data successfully fetched from HolidayCalendar");
				}
			} else
				LoggerUtil.log(this.getClass(), Level.ERROR, "autoPopulateHolidayCalendar",
						"HolidayCalendar table is empty");
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "autoPopulateHolidayCalendar",
					"HolidayCalendar is unavailable to process further!", e);
		}
		return response;
	}
}