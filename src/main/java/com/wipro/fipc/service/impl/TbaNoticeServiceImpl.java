package com.wipro.fipc.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.filelayout.ProcessFeatureConfigDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaNoticeMaster;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.ResponseDto;
import com.wipro.fipc.model.generated.TbaNoticeInqConfig;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.service.ITbaNoticeService;
import com.wipro.fipc.tba.service.NoticeTbaService;
import com.wipro.fipc.utils.CommonGetAdId;
import com.wipro.fipc.utils.CustomBeanUtils;
import com.wipro.fipc.model.TbaNoticeInquiryConfig;

@Service
public class TbaNoticeServiceImpl implements ITbaNoticeService {



	@Autowired
	CommonGetAdId commonGetUpdatedBy;
	@Autowired
	NoticeTbaService noticeTbaService;

	@Autowired
	private ProcessFeatureConfigDao configDao;

	@Autowired
	private CustomBeanUtils customBeanUtils;
	/* Get NoticeMaster Details */
	@Override
	public String getNoticeMaster(String columnName, String columnValue) {
		String resp1 = "";
		LoggerUtil.log(this.getClass(), Level.INFO, "getNoticeMaster()",
				"TbaNoticeServiceImpl-->getNoticeMaster()-->starts:");
		LoggerUtil.log(this.getClass(), Level.INFO, "getNoticeMaster>>>>>>>>>>>", "Method Type= "
				+ HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + columnName + "=" + columnValue);

		List<TbaNoticeMaster> mydata = noticeTbaService.findRecordByColumn(columnName, columnValue);
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			resp1 = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getNoticeMaster()1", e.getMessage());
		}

		LoggerUtil.log(this.getClass(), Level.INFO, "getNoticeMaster()1",
				"TbaNoticeServiceImpl-->getNoticeMaster()--ends.");

		return resp1;
	}

	/* Get TbaNoticeData Details */

	@Override
	public List<TbaNoticeInqConfig> getTbaNoticeData(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaNoticeData()",
				"TbaNoticeServiceImpl-->getTbaNoticeData()-->starts:");
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaNoticeData>>>>>>>>>>>", "Method Type= "
				+ HolmesAppConstants.GETREQUESTMETHOD + ",Action= GET" + columnName + "=" + columnValue);
		String res = "";
		try {

			List<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> mydata = noticeTbaService.findByColumn(columnName,
					columnValue);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
					false);
			res = objectMapper.writeValueAsString(mydata);

			List<TbaNoticeInqConfig> tbaNoticeList = Arrays
					.asList(new ObjectMapper().readValue(res, TbaNoticeInqConfig[].class));
			LoggerUtil.log(this.getClass(), Level.INFO, "getTbaNoticeData()",
					"TbaNoticeServiceImpl-->getTbaNoticeData()--ends.");
			return tbaNoticeList;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaNoticeData()2",
					"TbaNoticeServiceImpl-->getTbaNoticeData()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	/* Save TbaNoticeInqConfig */
	@Override
	public String saveNotice(List<TbaNoticeInquiryConfig> tbaNoticeList, String appName, String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "TbaNoticeServiceImpl-->submit()-->starts:");
		HttpHeaders headers = new HttpHeaders();
		headers.set("Content-Type", "application/json");
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		ResponseEntity<String> res = null;
		try {
			String adId = commonGetUpdatedBy.getADID(appName, sessionToken);

			String clientCode = configDao.getClientCode(tbaNoticeList.get(0).getProcessJobMappingId()).stream()
					.findFirst().orElse(null);
			clientCode = String.valueOf(customBeanUtils.checkForClientCode(clientCode));
			Iterator<TbaNoticeInquiryConfig> ite = tbaNoticeList.iterator();
			while (ite.hasNext()) {
				TbaNoticeInquiryConfig tbaNoticeConfig = ite.next();
				if (tbaNoticeConfig.getId() != null) {
					com.wipro.fipc.entity.tba.TbaNoticeInqConfig tbanotice = noticeTbaService
							.findById(tbaNoticeConfig.getId());
					String resp = objectMapper.writeValueAsString(tbanotice);

					TbaNoticeInquiryConfig config = null;
					if (StringUtils.isNotBlank(resp)) {
						config = new ObjectMapper().readValue(resp, TbaNoticeInquiryConfig.class);
						tbaNoticeConfig.setAddManualFlag(config.getAddManualFlag());
						tbaNoticeConfig.setCreatedBy(config.getCreatedBy());
						tbaNoticeConfig.setCreatedDate(config.getCreatedDate());
					}

					tbaNoticeConfig.setUpdatedDate(new Date());
					tbaNoticeConfig.setUpdatedBy(adId);
					tbaNoticeConfig.setActiveFlag('T');
					tbaNoticeConfig.setClientId(Integer.valueOf(clientCode));

					LoggerUtil.log(this.getClass(), Level.INFO, "saveNotice>>>>>>>>",
							"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Update" + ",PJMID = "
									+ tbaNoticeConfig.getProcessJobMappingId() + ",ADID: " + adId);

				} else {
					tbaNoticeConfig.setCreatedDate(new Date());
					tbaNoticeConfig.setCreatedBy(adId);
					tbaNoticeConfig.setActiveFlag('T');
					tbaNoticeConfig.setUpdatedDate(new Date());
					tbaNoticeConfig.setUpdatedBy(adId);
					LoggerUtil.log(this.getClass(), Level.INFO, "saveNotice>>>>>>>>",
							"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Create" + ",PJMID = "
									+ tbaNoticeConfig.getProcessJobMappingId() + ",ADID: " + adId);
				}
			}
			List<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> newLayout = new ArrayList();
			for (TbaNoticeInquiryConfig req : tbaNoticeList) {

				String convertedString = null;
				try {
					convertedString = objectMapper.writeValueAsString(req);
				} catch (JsonProcessingException e1) {
					e1.printStackTrace();
				}
				com.wipro.fipc.entity.tba.TbaNoticeInqConfig obj = null;
				try {
					obj = new ObjectMapper().readValue(convertedString,
							com.wipro.fipc.entity.tba.TbaNoticeInqConfig.class);
				} catch (IOException e) {
					e.printStackTrace();
				}
				newLayout.add(obj);
			}

			List<CommonResRowBO> mydata = noticeTbaService.saveIfNotDuplicate(newLayout);
			String resp = objectMapper.writeValueAsString(mydata);
			LoggerUtil.log(this.getClass(), Level.INFO, "submit()2", "TbaNoticeServiceImpl-->submit()-->ends:");
			ResponseDto response = new ResponseDto();
			response.setData(resp);
			response.setStatus("success");
			response.setMessage("Records Saved/Updated Successfully.");
			return objectMapper.writeValueAsString(response);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "submit()3",
					"TbaNoticeServiceImpl-->submit()--Exception: " + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	/* Delete NoticeConfig */
	@Override
	public String deleteNoticeConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken) {
		String adId = commonGetUpdatedBy.getADID(appName, sessionToken);
		entities.stream().forEach(s -> {
			s.setUpdatedBy(adId);
			s.setUpdatedDate(new Date());
			s.setActiveFlag("F");
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteNoticeConfig>>>>>>>>>>>>>>>>>",
					"Method Type= " + HolmesAppConstants.POSTREQUESTMETHOD + ",Action= Delete" + ",PJMID = "
							+ s.getProcessJobMappingId() + ",ADID: " + adId);
		});

		List<com.wipro.fipc.entity.tba.TbaNoticeInqConfig> newLayout = new ArrayList();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
		for (CommonDeleteDTO req : entities) {

			com.wipro.fipc.entity.tba.TbaNoticeInqConfig obj = null;
			try {
				objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				obj = objectMapper.convertValue(req, com.wipro.fipc.entity.tba.TbaNoticeInqConfig.class);
			} catch (Exception e) {
				e.printStackTrace();
			}
			newLayout.add(obj);
		}
		List<CommonRowBO> mydata = noticeTbaService.deletemultiplerows(1, newLayout);
		String res = null;
		try {
			res = objectMapper.writeValueAsString(mydata);
		} catch (JsonProcessingException e) {

			e.printStackTrace();
		}
		return res;
	}
}
