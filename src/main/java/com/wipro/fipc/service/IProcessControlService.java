package com.wipro.fipc.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.wipro.fipc.model.ProcessControl;
import com.wipro.fipc.model.TbaInquiryNoticeConfig;
import com.wipro.fipc.model.generated.KsdFileDetails;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.TbaUpdateConfig;

@Service
public interface IProcessControlService {
	
	 List<KsdFileDetails> getApplication(String columnName, String columnValue);
	
	 List<TbaInquiryNoticeConfig> getTBAField(String columnName, String columnValue);
	
	 List<RulesConfig> getRuleName(String columnName, String columnValue, String searchField);
	 
	 List<ProcessControl> getProcessControlData(String columnName, String columnValue);
	 	 
	 Object submit(String action, List<ProcessControl> pcList, String appName, String sessionToken);
	 
	 Object getLayoutConfig(String columnName, String columnValue, String searchField);
	 
	 List<TbaUpdateConfig> getCorrectiveActionData(String columnName, String columnValue, String searchField);
	 
	 List<String> getOutputReport(String columnName, String columnValue, String searchField);
	 
	 Object getConditionName(String columnName,String columnValue, String searchField);
	 
	
}
