package com.wipro.fipc.service;

import java.io.IOException;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.model.TemplateReportModel;
import com.wipro.fipc.model.TemplateReportRequest;
import com.wipro.fipc.pojo.TemplateReportUploadPojo;

public interface ITemplateReportService {

	String saveTemplate(MultipartFile file, TemplateReportRequest trequest);// -> File Formatter

	String updateTemplate(MultipartFile file, TemplateReportRequest trequest);

	public String deleteTemplate(long id);

	byte[] downloadTemplate(long id) throws IOException;

	List<TemplateReportUploadPojo> getTemplateForClient(String client);

	public List<TemplateReportUploadPojo> getAllTemplates();

	String getTemplateLayout(long id, long pjmid) throws JsonProcessingException;

	List<TemplateReportUploadPojo> getTemplateReportNames(String type, String client);

	String getLabellingReportData(long id) throws IOException;

	String saveLabellingReportData(TemplateReportModel data);

	String getSSNList(long id, String clientId);

	String updateLabellingReportData(Long id, TemplateReportModel data);

	String saveTemplateData(MultipartFile file, TemplateReportRequest trequest, String appName, String sessionToken);

	String updateTemplateData(MultipartFile file, TemplateReportRequest trequest, String appName, String sessionToken);

	String addLabellingReportData(TemplateReportModel data, String appName, String sessionToken);

	String updateLabellingReportData(Long id, TemplateReportModel data, String appName, String sessionToken);

}