package com.wipro.fipc.service;

import java.lang.reflect.InvocationTargetException;

import com.wipro.fipc.model.CustomProcessConfigurationUI;
import com.wipro.fipc.model.ModelApiResponse;
import com.wipro.fipc.model.ProcessConfiguration;

public interface AllBotsConfigService {

	public ModelApiResponse deleteAllConfigs(String adid, String processJobMappingId) throws NumberFormatException;

	ProcessConfiguration copyJobToAllConfigsDetails(CustomProcessConfigurationUI processConfigurationUI)
			throws IllegalAccessException, InvocationTargetException;
}
