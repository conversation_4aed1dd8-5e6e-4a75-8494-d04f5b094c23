package com.wipro.fipc.service;

import java.net.URISyntaxException;
import java.util.List;

import com.wipro.fipc.model.RulesRequest;

public interface IRuleEngineBusinessService {

	RulesRequest createBusinessRules(RulesRequest rulesRequest);

	String deleteAllBusinessRule(String processId);

	String deleteBusinessRule(String id);

	List<RulesRequest> getByPjmId(String pjmId);

	public List<RulesRequest> getByRuleConfigId(String id);

	List<Object> getAllConditions();

	RulesRequest updateBusinessRulesByRuleId(RulesRequest bizRules, String pjmId, String ruleId);

	String updateJsonWoutSpace(String ruleId);

	String updateFileName(String id);

	List<Object> getAllData(String pjmId);

	String deleteRuleConfig(String entity) throws URISyntaxException;

	List<Object> getByFileName(String fileName, String pjmId);

	RulesRequest createBusinessRulesData(RulesRequest bizRules, String appName, String sessionToken);

	RulesRequest updateBusinessRulesByRuleIdData(RulesRequest bizRules, String pjmId, String ruleId, String appName,
			String sessionToken);

	String deleteRuleLatest(String ruleName, String pjmId, String appName, String sessionToken);
}