package com.wipro.fipc.service;

import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.entity.batch.HolidayCalendar;
import com.wipro.fipc.entity.batch.KsdConfig;
import com.wipro.fipc.exception.KsdBatchException;
import com.wipro.fipc.model.JobScheduleTimeDto;
import com.wipro.fipc.model.generated.ModelApiResponse;

public interface IKsdBatchService {

	public String saveKsdAndChildDetails(KsdConfig ksdConfig) throws KsdBatchException, JsonProcessingException;

	public String updateKsdAndChildDetails(String processJobMappingId, KsdConfig ksdConfig)
			throws KsdBatchException, JsonProcessingException;

	public String updateJobScheduleTime(long id, JobScheduleTimeDto jobScheduleTimeDto) throws KsdBatchException;

	public List<KsdConfig> getKsdAndChildDetails(String processJobMappingId, String primaryJobName)
			throws KsdBatchException;

	public String saveHolidayCalendarDetails(HolidayCalendar holidayCalendar)
			throws KsdBatchException, JsonProcessingException;

	public String updateHolidayCalendarDetails(HolidayCalendar holidayCalendar)
			throws KsdBatchException, JsonProcessingException;

	public String[] getHolidayCalendarDetails(String columnName, String columnValue) throws KsdBatchException;

	public ModelApiResponse deleteAllConfigsFromScreen(String adid, long pjmId, String fileType);

	public String updateKsdAndClientConfigDetails(com.wipro.fipc.model.generated.KsdConfig ksdConfigFromUI, String appName, String sessionToken)
			throws KsdBatchException;

}