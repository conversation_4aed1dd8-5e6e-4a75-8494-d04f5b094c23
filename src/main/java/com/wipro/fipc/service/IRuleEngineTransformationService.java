package com.wipro.fipc.service;

import com.wipro.fipc.model.RulesRequest;

public interface IRuleEngineTransformationService {

	Object createTransformationRules(RulesRequest rules);

	String deleteTransformationRules(String id);

	String deleteAllTransformationRule(String processJobMappingId);

	RulesRequest updateBusinessRulesByRuleId(RulesRequest bizRules, String pjmId, String ruleId);

	Object createTransformationRulesData(RulesRequest transformationRules, String appName, String sessionToken);

	RulesRequest updateBusinessRulesByRuleIdData(RulesRequest bizRules, String pjmId, String ruleId, String appName,
			String sessionToken);

}
