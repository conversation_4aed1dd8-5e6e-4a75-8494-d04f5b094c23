package com.wipro.fipc.service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaUpdateConfigDto;

public interface TbaUpdateConfigService {

	String createTbaUpdateConfig(List<TbaUpdateConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException;

	String getTbaUpdateMetadata(int panelId, int clientId);
	
	String getTbaUpdateMetadata(int panelId, int clientId, Integer activityId);

	String getTbaUpdateJsonKey(String columnName, String columnValue);

	String updateTbaUpdateConfig(List<TbaUpdateConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException;

	String getTbaUpdateConfig(String columnName, String columnValue);

	String getAllTbaUpdateConfig();

	String getTbaUpdateProcess(int clientId,boolean check) throws URISyntaxException, IOException;

	String deleteUpdateEventConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken)
			throws URISyntaxException;

	String getTbaEditsMasterDetails(String clientId, String eventName) throws URISyntaxException, IOException;



}
