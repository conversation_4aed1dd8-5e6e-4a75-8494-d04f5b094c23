package com.wipro.fipc.service;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.pojo.tba.TbaEventInquiryConfigDto;

public interface TbaEventInquiryConfigService {

	String createTbaEventInquiryConfig(List<TbaEventInquiryConfigDto> entity, String appName, String sessionToken)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException;
	
	String getTbaEventInquiryJsonKey( String columnValue);

	Object getTbaEventInquiryConfig(String columnName, String columnValue) throws JsonProcessingException, IOException, IllegalAccessException, InvocationTargetException;

	String updateTbaEventInquiryConfig(List<TbaEventInquiryConfigDto> entity, String appName, String sessionTokeny)
			throws URISyntaxException, IOException, IllegalAccessException, InvocationTargetException;

	String getAllTbaEventInquiryConfig();

	String deleteInquiryEventConfig(List<CommonDeleteDTO> entity, String appName, String sessionToken)
			throws URISyntaxException;
	String getTbaEventUpdateProcess(int clientId , boolean check) throws URISyntaxException, IOException;
}
