package com.wipro.fipc.service;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Set;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.model.CustomProcessConfigurationUI;
import com.wipro.fipc.model.FileData;
import com.wipro.fipc.model.JobScheduleBean;
import com.wipro.fipc.model.ProcessConfiguration;
import com.wipro.fipc.model.UpdatePhaseNames;
import com.wipro.fipc.pojo.ClientDetailsBo;
import com.wipro.fipc.pojo.CustomBusinessOpsBO;
import com.wipro.fipc.pojo.CustomBusinessUnitBO;
import com.wipro.fipc.pojo.CustomClientBO;
import com.wipro.fipc.pojo.CustomEftJobNameBO;
import com.wipro.fipc.pojo.CustomKsdNameBO;
import com.wipro.fipc.pojo.CustomPFCClientCodeBO;
import com.wipro.fipc.pojo.CustomPJMBO;
import com.wipro.fipc.pojo.CustomPJMDto;
import com.wipro.fipc.pojo.CustomProcessDetailsBO;
import com.wipro.fipc.pojo.CustomProcessFeaturesBO;
import com.wipro.fipc.pojo.ProcessCofigReqBody;
import com.wipro.fipc.pojo.ProcessCofigResBody;

public interface ProcessConfigurationService {
	List<CustomBusinessUnitBO> getBusinessUnitByAlightID(String alightID);

	List<CustomProcessDetailsBO> getProcessDetails(String alightID, String buID, String clientID, String buOpsID);

	List<String> getJobDetails(String buID, String clientID, String buOpsID, String processID);

	public ProcessConfiguration saveProcessConfigDetails(CustomProcessConfigurationUI processFeatureConfig)
			throws IllegalAccessException, InvocationTargetException;

	public List<CustomPFCClientCodeBO> getAllProcessConfigDetails(String columnValue);

	public String updatePhaseNamesDetails(String id, UpdatePhaseNames updatePhaseNames);

	public CustomPFCClientCodeBO getPhaseNamesDetails(String id)
			throws IllegalAccessException, InvocationTargetException;

	public List<String> getEFTDetails(String buID, String clientID, String buOpsID, String processID,
			CustomEftJobNameBO customEftJobNameBO);

	public List<CustomKsdNameBO> getKsdNamesAndIds(String buID, String clientID, String buOpsID, String processID,
			CustomEftJobNameBO customEftJobNameBO);

	public Set<String> getConfiguredJobList(String businessOpsName, String adid)
			throws IllegalAccessException, InvocationTargetException;

	public Set<String> getConfiguredEftDetails(CustomEftJobNameBO customEftJobNameBO, String adid);

	List<CustomClientBO> getClientDetails(String alightID, String buID);

	List<CustomBusinessOpsBO> getBusinessOpsDetails(String alightID, String buID);

	public Set<CustomKsdNameBO> getConfiguredKsdNamesAndIds(CustomEftJobNameBO customEftJobNameBO, String adid)
			throws IllegalAccessException, InvocationTargetException;

	public List<String> getKsdNameList(String buid, String clientId);

	CustomPJMBO getProcessJobMapping(CustomKsdNameBO customKsdNameBO);

	public CustomProcessFeaturesBO getProcessfeatureConfig(CustomKsdNameBO customKsdNameBO);

	public List<CustomPFCClientCodeBO> getPFCDetailsForRole(String adId, String roleOfUser)
			throws IllegalAccessException, InvocationTargetException;

	List<ClientDetailsBo> getClientOfManager(String adid, String role, Long buid);

	String updatePhaseNameById(String id, List<FileData> files)
			throws JsonProcessingException, IOException, IllegalAccessException, InvocationTargetException;

	public ProcessCofigResBody getAllFilterdProcessConfigDetails(ProcessCofigReqBody processCofigReqBody)
			throws IllegalAccessException, InvocationTargetException;

	public CustomPJMDto getProcessJobMappingById(Long pjmid);

	public void updateScheduleJobs (JobScheduleBean jobScheduleBean, String appName, String sessionToken);
}
