package com.wipro.fipc.service;

import java.io.IOException;
import java.util.List;
import java.util.Set;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import com.wipro.fipc.model.CustomClientBusinessOps;
import com.wipro.fipc.model.CustomRoleConfig;
import com.wipro.fipc.model.RoleConfigResponse;
import com.wipro.fipc.model.RoleConfigUI;
import com.wipro.fipc.model.generated.CustomBusinessUnitBO;
import com.wipro.fipc.pojo.ClientInformation;
import com.wipro.fipc.pojo.ClientInformationBo;
import com.wipro.fipc.pojo.CustomProcessBO;
import com.wipro.fipc.pojo.RoleDetails;

public interface IRoleConfigService {

	List<CustomRoleConfig> getRoleConfigsByADID(String adId);

	RoleConfigResponse uploadExcelFile(MultipartFile file, String loggedInUserId);

	List<RoleConfigUI> getAllRoleConfig(String loggedInUserId) throws IOException;

	String deleteRoleConfig(long id);

	Object putRoleConfig(List<RoleConfigUI> roleConfigList, String loggedInUserId);

	Workbook getTemplate(String loggedInUserId);

	List<String> getReporteeIds(String loggedInUserId);

	List<CustomBusinessUnitBO> getBusinessUnitAll();

	CustomClientBusinessOps getClientAndBusinessOps(String buId);

	Set<CustomProcessBO> getProcessDetails(String buID, String buOpsID, String clientID);

	List<RoleDetails> getLoginInformation(String adid);

	List<ClientInformationBo> getLoginClientInformationBo(String ldapId);

	List<ClientInformation> getLoginClientInformation(String ldapId, String role, Long bUnit, Long businessOpsId);

}
