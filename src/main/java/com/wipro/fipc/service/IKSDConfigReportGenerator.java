package com.wipro.fipc.service;

import java.text.ParseException;
import java.util.List;

import org.apache.commons.io.output.ByteArrayOutputStream;

import com.wipro.fipc.pojo.HWSBossKSDConfigReport;
import com.wipro.fipc.pojo.HWSBusinessKSDConfigReport;

public interface IKSDConfigReportGenerator {

	public List<HWSBusinessKSDConfigReport> getHwsBusinessKsdConfigReport(String date, String time)
			throws ParseException;

	public List<HWSBossKSDConfigReport> getHwsBossKsdConfigReport(String date, String time) throws ParseException;

	public ByteArrayOutputStream writeAndSaveExcelKSDRecords(List<HWSBusinessKSDConfigReport> ksdRecordsListToSave,
			String fileLoadPath);

	public ByteArrayOutputStream writeAndSaveNonBusinessExcelKSDRecords(
			List<HWSBossKSDConfigReport> ksdRecordsListToSave, String fileLoadPath);

}
