package com.wipro.fipc.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaUpdateConfigDto;

@Service
public interface ITbaReRunService {

	 String saveReRun(List<TbaUpdateConfigDto> tbaList, String appName, String sessionToken);
	 
	 List<TbaUpdateConfigDto> getTbaReRunData( String columnName, String columnValue);
	 
	 String getEventHistoryMaster( String columnName, String columnValue) throws JsonProcessingException;
	 
	 String deleteReRunConfig(List<CommonDeleteDTO> entities, String appName, String sessionToken);
	
}
