package com.wipro.fipc.service;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.filelayout.OutputReport;
import com.wipro.fipc.entity.filelayout.ParticipantRecordIdentifier;
import com.wipro.fipc.model.WriteOutputReportDto;

public interface IOutputReportService {

	String saveInputReportAndOutputReportData(String pjmId, String fileName,  WriteOutputReportDto  entity)
			throws URISyntaxException;

	String updateKsdOutPutFileDetails(WriteOutputReportDto entity);

	String getKsdOutPutFileDetails(String pjmId, String fileName) throws JsonProcessingException, UnsupportedEncodingException;
	
	String getKsdOutPutFileDetailsBySheetName(String pjmId, String sheetName) throws JsonProcessingException;
	
	String getKsdOutPutFileDetails(String pjmId) throws JsonProcessingException ;

	String getOutputReport(String pjmId, String fileName) throws UnsupportedEncodingException, JsonProcessingException;
	
	String getInputFileDetailsByFileName(String pjmId, String fileName) throws JsonProcessingException;

	String updateOutPutReportDetails(List<OutputReport> entity) ;//->done

	String updateParticipantRecordIdentifierDetails(List<ParticipantRecordIdentifier> entity);//->done
	
	String deleteOutPutFileDetails(long pjmId, String fileName, String updatedBy) throws UnsupportedEncodingException;

	String  getDistinctFileNames(String pjmId) throws JsonProcessingException;

	String deleteOutputReport(KsdOutPutFileDetails entity) throws URISyntaxException;

	String saveKsdOutPutFileDetails(String pjmId, String fileName,  WriteOutputReportDto  entity, String appName,
			String sessionToken) throws URISyntaxException;

	String updateKsdOutPutFileDetails(WriteOutputReportDto  entity, String appName, String sessionToken) throws URISyntaxException;

	String deleteOutputReport(KsdOutPutFileDetails entity, String appName, String sessionToken) throws URISyntaxException;


}
