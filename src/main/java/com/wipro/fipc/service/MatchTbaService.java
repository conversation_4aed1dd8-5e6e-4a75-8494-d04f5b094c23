package com.wipro.fipc.service;

import java.util.List;

import com.wipro.fipc.model.MatchTba;

import java.net.URISyntaxException;

public interface MatchTbaService {
	
	public String getFileNameandField(String columnName, String columnValue);
	
	public String getTbaField(String columnName, String columnValue);
	
	public List<String> getRule(String columnName, String columnValue);
	
	public String getRule(Long processJobMappingId,String fileName);
	
	public List<String> getMismatch(String columnName, String columnValue);
	
	public String deleteMatchTba(String id);
	
	public String createMatchTbaRecord(String request);
	
	public String getDetailsTbaMatchConfig(String columnName, String columnValue);
	 Object submit(String action, List<MatchTba> mtList, String appName, String sessionToken);
	 List<MatchTba> getDetailsTbaMatchConfigDetails(String columnName, String columnValue);

	public Object getResultVariable(String columnName, String columnValue, String ruleName);

	String deleteMatchTbaRecords(String entityList)  throws URISyntaxException;

	public Object updateMatchTba(List<MatchTba> mtList);
	
}
