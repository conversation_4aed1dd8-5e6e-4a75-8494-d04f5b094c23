package com.wipro.fipc.service;

import java.util.List;

import org.springframework.web.bind.annotation.PathVariable;

import com.wipro.fipc.entity.filelayout.TaskUpdateConfig;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;
import com.wipro.fipc.model.ConfigStatusBO;
import com.wipro.fipc.model.MaestroTicketTaskConfig;
import com.wipro.fipc.model.generated.ConfigStatusApproveBO;

public interface MaestroTicketConfigService {

	public MaestroTicketTaskConfig getAllMaestroTicketTask();

	public MaestroTicketTaskConfig getByColumnMaestroTikcetTask(@PathVariable String columnName,
			@PathVariable String columnValue);
	
	public List<String> createUpdateMaestroTicketConfig(MaestroTicketTaskConfig ticketCreationUpdateConfig);
	
	public List<String> newcreateUpdateMaestroTicketConfig(MaestroTicketTaskConfig ticketCreationUpdateConfig,
			String appName, String sessionToken, String pjmId);

	public boolean createupdateMaestroTicketConfigList(List<TicketCreationConfig> ticketCreationConfig);

	public boolean createMaestroTaskUpdateClosureConfig(List<TaskUpdateConfig> listTaskUpdateConfig);

	public List<TicketCreationConfig> getAllMaestroTicketConfig();

	public List<TaskUpdateConfig> getAllMaestroTaskUpdateClosureConfig();

	public String deleteByIdMaestroTicketConfig(Long id);

	public List<TicketCreationConfig> getByColumnMaestroTicketConfig(String columnName, String columnValue);

	public List<TaskUpdateConfig> getByColumnMaestroTaskConfig(String columnName, String columnValue);

	public String updateConfigStatus(ConfigStatusBO configStatusBO);

	public String updatemaestroConfigStatus(ConfigStatusBO configStatusBO, String appName, String sessionToken);

	public String updateApproveConfigStatus(ConfigStatusApproveBO configStatusApproveBO);

	public String updateApprovemaestroConfigStatus(ConfigStatusApproveBO configStatusApproveBO, String appName,
			String sessionToken);

	public List<String> deleteMaestroTicketConfig(MaestroTicketTaskConfig ticketCreationUpdateConfig, String appName,
			String sessionToken, String pjmId);

}
