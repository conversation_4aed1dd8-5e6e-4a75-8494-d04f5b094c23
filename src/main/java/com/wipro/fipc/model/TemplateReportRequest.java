package com.wipro.fipc.model;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
@Data
public class TemplateReportRequest {
	
	  @JsonProperty("activeFlag")
	  private char activeFlag;

	  @JsonProperty("buId")
	  private Integer buId = null;

	  @JsonProperty("clientId")
	  private String clientId = null;

	  @JsonProperty("clientName")
	  private String clientName = null;

	  @JsonProperty("createdBy")
	  private String createdBy = null;

	  @JsonProperty("createdDate")
	  private Date createdDate = null;

	  @JsonProperty("id")
	  private Long id = null;

	  @JsonProperty("reportFlag")
	  private Character reportFlag;

	  @JsonProperty("templateReportName")
	  private String templateReportName = null;

	  @JsonProperty("templateReportNameWs")
	  private String templateReportNameWs = null;

	  @JsonProperty("type")
	  private String type = null;

	  @JsonProperty("updatedBy")
	  private String updatedBy = null;

	  @JsonProperty("updatedDate")
	  private Date updatedDate = null;

	  @JsonProperty("uploadedBy")
	  private String uploadedBy = null;

	  @JsonProperty("uploadedDate")
	  private Date uploadedDate = null;

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Integer getBuId() {
		return buId;
	}

	public void setBuId(Integer buId) {
		this.buId = buId;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public String getClientName() {
		return clientName;
	}

	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Character getReportFlag() {
		return reportFlag;
	}

	public void setReportFlag(Character reportFlag) {
		this.reportFlag = reportFlag;
	}

	public String getTemplateReportName() {
		return templateReportName;
	}

	public void setTemplateReportName(String templateReportName) {
		this.templateReportName = templateReportName;
	}

	public String getTemplateReportNameWs() {
		return templateReportNameWs;
	}

	public void setTemplateReportNameWs(String templateReportNameWs) {
		this.templateReportNameWs = templateReportNameWs;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getUploadedBy() {
		return uploadedBy;
	}

	public void setUploadedBy(String uploadedBy) {
		this.uploadedBy = uploadedBy;
	}

	public Date getUploadedDate() {
		return uploadedDate;
	}

	public void setUploadedDate(Date uploadedDate) {
		this.uploadedDate = uploadedDate;
	}

}
