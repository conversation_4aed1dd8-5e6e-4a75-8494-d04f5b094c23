package com.wipro.fipc.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class QuesYesNO {

	@JsonProperty("ksdQueOne")
	private String ksdQueOne = null;

	@JsonProperty("ksdQueTwo")
	private String ksdQueTwo = null;

	@JsonProperty("layoutQueOne")
	private List<Question> layoutQueOne = null;

	@JsonProperty("layoutQueTwo")
	private List<Question> layoutQueTwo = null;

	@JsonProperty("layoutQueThree")
	private List<Question> layoutQueThree = null;

	@JsonProperty("layoutQueFour")
	private List<Question> layoutQueFour = null;
	
	@JsonProperty("reportlayoutQueOne")
	private List<Question> reportlayoutQueOne = null;

	@JsonProperty("reportlayoutQueTwo")
	private List<Question> reportlayoutQueTwo = null;

	@JsonProperty("reportlayoutQueThree")
	private List<Question> reportlayoutQueThree = null;

	@JsonProperty("reportlayoutQueFour")
	private List<Question> reportlayoutQueFour = null;

	@JsonProperty("tbaQueOne")
	private Object tbaQueOne = null;
	
	@JsonProperty("tbaQueTwo")
	private Object tbaQueTwo = null;

	
	
}
