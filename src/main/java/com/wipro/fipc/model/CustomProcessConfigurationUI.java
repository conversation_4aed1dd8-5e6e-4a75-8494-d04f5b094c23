package com.wipro.fipc.model;

import lombok.Data;

@Data
public class CustomProcessConfigurationUI {

	private String phaseNames;

	private String processName;

	private String clientName;

	private String businessUnitName;

	private String businessOpsName;

	private String jobName;

	private String createdBy;

	private String clientCode;
	private String jobCopied;
	private Long pjmIdCopied;

	private Long processJobMappingId;
	private String eftSubject;
	private String eftSubjectCopied;
	private String processType;
	private String ksdName;
	public String getPhaseNames() {
		return phaseNames;
	}
	public void setPhaseNames(String phaseNames) {
		this.phaseNames = phaseNames;
	}
	public String getProcessName() {
		return processName;
	}
	public void setProcessName(String processName) {
		this.processName = processName;
	}
	public String getClientName() {
		return clientName;
	}
	public void setClientName(String clientName) {
		this.clientName = clientName;
	}
	public String getBusinessUnitName() {
		return businessUnitName;
	}
	public void setBusinessUnitName(String businessUnitName) {
		this.businessUnitName = businessUnitName;
	}
	public String getBusinessOpsName() {
		return businessOpsName;
	}
	public void setBusinessOpsName(String businessOpsName) {
		this.businessOpsName = businessOpsName;
	}
	public String getJobName() {
		return jobName;
	}
	public void setJobName(String jobName) {
		this.jobName = jobName;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public String getClientCode() {
		return clientCode;
	}
	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}
	public String getJobCopied() {
		return jobCopied;
	}
	public void setJobCopied(String jobCopied) {
		this.jobCopied = jobCopied;
	}
	public Long getPjmIdCopied() {
		return pjmIdCopied;
	}
	public void setPjmIdCopied(Long pjmIdCopied) {
		this.pjmIdCopied = pjmIdCopied;
	}
	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}
	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}
	public String getEftSubject() {
		return eftSubject;
	}
	public void setEftSubject(String eftSubject) {
		this.eftSubject = eftSubject;
	}
	public String getEftSubjectCopied() {
		return eftSubjectCopied;
	}
	public void setEftSubjectCopied(String eftSubjectCopied) {
		this.eftSubjectCopied = eftSubjectCopied;
	}
	public String getProcessType() {
		return processType;
	}
	public void setProcessType(String processType) {
		this.processType = processType;
	}
	public String getKsdName() {
		return ksdName;
	}
	public void setKsdName(String ksdName) {
		this.ksdName = ksdName;
	}

}