package com.wipro.fipc.model;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class LabellingRecord {

	@JsonProperty("dataElement")
	private String dataElement;
	
	@JsonProperty("element")
	private List<Element> element = new ArrayList<Element>();

	/**
	 * @return the dataElement
	 */
	public String getDataElement() {
		return dataElement;
	}

	/**
	 * @param dataElement the dataElement to set
	 */
	public void setDataElement(String dataElement) {
		this.dataElement = dataElement;
	}

	/**
	 * @return the element
	 */
	public List<Element> getElement() {
		return element;
	}

	/**
	 * @param element the element to set
	 */
	public void setElement(List<Element> element) {
		this.element = element;
	}
	
}
