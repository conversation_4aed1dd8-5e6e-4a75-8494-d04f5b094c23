package com.wipro.fipc.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class Ques {

	@JsonProperty("ksdQueOne")
	private Object ksdQueOne ;

	@JsonProperty("ksdQueTwo")
	private Object ksdQueTwo ;
	
	@JsonProperty("reportlayoutQueOne")
	private List<Object> reportlayoutQueOne = null;

	@JsonProperty("reportlayoutQueTwo")
	private List<QuestionBool> reportlayoutQueTwo = null;

	@JsonProperty("reportlayoutQueThree")
	private List<QuestionBool> reportlayoutQueThree = null;

	@JsonProperty("reportlayoutQueFour")
	private List<Object> reportlayoutQueFour = null;
	
	@JsonProperty("layoutQueOne")
	private List<Object> layoutQueOne = null;

	@JsonProperty("layoutQueTwo")
	private List<QuestionBool> layoutQueTwo = null;

	@JsonProperty("layoutQueThree")
	private List<QuestionBool> layoutQueThree = null;

	@JsonProperty("layoutQueFour")
	private List<Object> layoutQueFour = null;
	
	@JsonProperty("tbaQueOne")
	private Object tbaQueOne;
	
	@JsonProperty("tbaQueTwo")
	private Object tbaQueTwo;
}
