package com.wipro.fipc.model;

import java.util.List;

import lombok.Data;

@Data
public class GenerateDroolsJson {

	private List<Conditions> conditions;
	private List<Conditions> beneficiaryConditions;
	private String conditionName;
	private String operationWithBeneficiary;
	private List<OperationJson> variableRowOp;
	private String variableName;
	private String varArithEqualOp;
	private List<GenerateDroolsResult> varResultJson;
}
