package com.wipro.fipc.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.google.gson.annotations.SerializedName;

import lombok.Data;

@Data
public class LayoutRecord extends BaseRequest {

	private String id;
	private String dataElement;
	private String startPosition;
	private String length;
	private String format;
	private String recordType;
	private String recordIdentifier;
	private String mfFieldWoutSpace;
	@SerializedName("active_flag")
	private String activeFlag;
	@SerializedName("created_date")
	private Date createdDate;
	private String recordIdentifierVal;
	@SerializedName("sheet_Name")
	private String sheetName;
	@SerializedName("sheetName_WoutSpace")
	private String sheetNameWoutSpace;
	@SerializedName("fileName_WoutSpace")
	private String fileNameWoutSpace;
	private String mfFieldName; // added for rules screen
	private List<String> fieldTemplate;
	private ArrayList valueDetails;
	@SerializedName("pre_filter")
	private String preFilter;
	@SerializedName("pre_filter_operator")
	private String preFilterOperator;
	private String amountFormat;
//	private String preFilterDateType;
//	private String preFilterDateNumber;
//	private String preFilterDateFrequency;

}
