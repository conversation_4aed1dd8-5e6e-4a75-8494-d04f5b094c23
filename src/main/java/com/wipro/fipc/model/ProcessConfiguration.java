
package com.wipro.fipc.model;

import java.util.Date;

import lombok.Data;

@Data
public class ProcessConfiguration {
	private Long id;
	private Long processJobMappingId;
	private String processName;
	private String clientName;
	private String businessUnitName;
	private String businessOpsName;
	private String jobName;
	private String clientCode;
	private String updatedBy;
	private Date updatedDate;
	private String phaseNames;
	private String message;
	private String eftSubject;
	private String processType;
	private String configStatus;
	private String ksdName;
	private String approvedBy;
	private Date approvedDate;
	private String createdBy;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}
	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}
	public String getProcessName() {
		return processName;
	}
	public void setProcessName(String processName) {
		this.processName = processName;
	}
	public String getClientName() {
		return clientName;
	}
	public void setClientName(String clientName) {
		this.clientName = clientName;
	}
	public String getBusinessUnitName() {
		return businessUnitName;
	}
	public void setBusinessUnitName(String businessUnitName) {
		this.businessUnitName = businessUnitName;
	}
	public String getBusinessOpsName() {
		return businessOpsName;
	}
	public void setBusinessOpsName(String businessOpsName) {
		this.businessOpsName = businessOpsName;
	}
	public String getJobName() {
		return jobName;
	}
	public void setJobName(String jobName) {
		this.jobName = jobName;
	}
	public String getClientCode() {
		return clientCode;
	}
	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public String getPhaseNames() {
		return phaseNames;
	}
	public void setPhaseNames(String phaseNames) {
		this.phaseNames = phaseNames;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getEftSubject() {
		return eftSubject;
	}
	public void setEftSubject(String eftSubject) {
		this.eftSubject = eftSubject;
	}
	public String getProcessType() {
		return processType;
	}
	public void setProcessType(String processType) {
		this.processType = processType;
	}
	public String getConfigStatus() {
		return configStatus;
	}
	public void setConfigStatus(String configStatus) {
		this.configStatus = configStatus;
	}
	public String getKsdName() {
		return ksdName;
	}
	public void setKsdName(String ksdName) {
		this.ksdName = ksdName;
	}
	public String getApprovedBy() {
		return approvedBy;
	}
	public void setApprovedBy(String approvedBy) {
		this.approvedBy = approvedBy;
	}
	public Date getApprovedDate() {
		return approvedDate;
	}
	public void setApprovedDate(Date approvedDate) {
		this.approvedDate = approvedDate;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
}