package com.wipro.fipc.model;

import lombok.Data;

@Data
public class NotificationPassword {

	private String clientId = "";
	private String clientType;
	private String racfID;
	private String racfPassword;
	private String appNameConfig;
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getClientType() {
		return clientType;
	}
	public void setClientType(String clientType) {
		this.clientType = clientType;
	}
	public String getRacfID() {
		return racfID;
	}
	public void setRacfID(String racfID) {
		this.racfID = racfID;
	}
	public String getRacfPassword() {
		return racfPassword;
	}
	public void setRacfPassword(String racfPassword) {
		this.racfPassword = racfPassword;
	}
	public String getAppNameConfig() {
		return appNameConfig;
	}
	public void setAppNameConfig(String appNameConfig) {
		this.appNameConfig = appNameConfig;
	}
	
}
