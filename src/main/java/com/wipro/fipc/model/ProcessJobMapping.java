package com.wipro.fipc.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.model.generated.BusinessUnitOps;
import com.wipro.fipc.model.generated.ClientDetails;
import com.wipro.fipc.model.generated.Process;

import lombok.Data;

@Data
public class ProcessJobMapping {

	@JsonProperty("activeFlag")
	private String activeFlag = null;

	@JsonProperty("businessUnitOps")
	private BusinessUnitOps businessUnitOps = null;

	@JsonProperty("clientDetails")
	private ClientDetails clientDetails = null;

	@JsonProperty("createdBy")
	private String createdBy = null;

	@JsonProperty("createdDate")
	private Date createdDate = null;

	@JsonProperty("eftSubject")
	private String eftSubject = null;

	@JsonProperty("id")
	private Long id = null;

	@JsonProperty("jobName")
	private String jobName = null;

	@JsonProperty("ksdName")
	private String ksdName = null;

	@JsonProperty("process")
	private Process process = null;

	@JsonProperty("tower")
	private String tower = null;

	@JsonProperty("updatedBy")
	private String updatedBy = null;

	@JsonProperty("updatedDate")
	private Date updatedDate = null;

}
