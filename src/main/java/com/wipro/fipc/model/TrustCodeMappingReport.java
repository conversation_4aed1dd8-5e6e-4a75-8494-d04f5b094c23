package com.wipro.fipc.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
public class TrustCodeMappingReport {

	private String clientId;
	private String clientName;
	private String checkWritterName;
	private String longDescription;
	private String shortDescription;
	private String trustCode;
}

