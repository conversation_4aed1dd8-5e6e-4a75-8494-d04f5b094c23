package com.wipro.fipc.model;


import java.util.Date;

//import com.wipro.holmes.model.generated.ProcessJobMapping;


public class MatchTba {
	  private Object actions;
	  private String activeFlag;
	  private String correctiveAction;
	  private String createdBy;
	  private Date createdDate;
	  private String fileName;
	  private Long id;
	  private String identifier;
	  private String inquiryDefName;
	  private String mfFieldName;
	  private String mfFieldWoutSpace;
	  private com.wipro.fipc.entity.ProcessJobMapping processJobMapping;
	  private String reportIdentifier;
	  private String resultField;
	  private String ruleName;
	  private String tbaFieldName;
	  private String updatedBy;
	  private Date updatedDate;
	  private String fileNameDest;
	  private String matchType;
	  private String mfFieldNameDest;
	  private String mfFieldWoutSpaceDest;
	  private String reportIdentifierDest;
	  //
	  private String fileNameWoutSpace;
	  private String fileNameWoutSpaceDest;
	  private String sheetName;
	  private String sheetNameWoutSpace;
	  private String sheetNameDest;
	  private String sheetNameDestWoutSpace;
	  private String pptVerifyTba;
	public Object getActions() {
		return actions;
	}
	public void setActions(Object actions) {
		this.actions = actions;
	}
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public String getCorrectiveAction() {
		return correctiveAction;
	}
	public void setCorrectiveAction(String correctiveAction) {
		this.correctiveAction = correctiveAction;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getIdentifier() {
		return identifier;
	}
	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}
	public String getInquiryDefName() {
		return inquiryDefName;
	}
	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}
	public String getMfFieldName() {
		return mfFieldName;
	}
	public void setMfFieldName(String mfFieldName) {
		this.mfFieldName = mfFieldName;
	}
	public String getMfFieldWoutSpace() {
		return mfFieldWoutSpace;
	}
	public void setMfFieldWoutSpace(String mfFieldWoutSpace) {
		this.mfFieldWoutSpace = mfFieldWoutSpace;
	}
	public com.wipro.fipc.entity.ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}
	public void setProcessJobMapping(com.wipro.fipc.entity.ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}
	public String getReportIdentifier() {
		return reportIdentifier;
	}
	public void setReportIdentifier(String reportIdentifier) {
		this.reportIdentifier = reportIdentifier;
	}
	public String getResultField() {
		return resultField;
	}
	public void setResultField(String resultField) {
		this.resultField = resultField;
	}
	public String getRuleName() {
		return ruleName;
	}
	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}
	public String getTbaFieldName() {
		return tbaFieldName;
	}
	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public String getFileNameDest() {
		return fileNameDest;
	}
	public void setFileNameDest(String fileNameDest) {
		this.fileNameDest = fileNameDest;
	}
	public String getMatchType() {
		return matchType;
	}
	public void setMatchType(String matchType) {
		this.matchType = matchType;
	}
	public String getMfFieldNameDest() {
		return mfFieldNameDest;
	}
	public void setMfFieldNameDest(String mfFieldNameDest) {
		this.mfFieldNameDest = mfFieldNameDest;
	}
	public String getMfFieldWoutSpaceDest() {
		return mfFieldWoutSpaceDest;
	}
	public void setMfFieldWoutSpaceDest(String mfFieldWoutSpaceDest) {
		this.mfFieldWoutSpaceDest = mfFieldWoutSpaceDest;
	}
	public String getReportIdentifierDest() {
		return reportIdentifierDest;
	}
	public void setReportIdentifierDest(String reportIdentifierDest) {
		this.reportIdentifierDest = reportIdentifierDest;
	}
	public String getFileNameWoutSpace() {
		return fileNameWoutSpace;
	}
	public void setFileNameWoutSpace(String fileNameWoutSpace) {
		this.fileNameWoutSpace = fileNameWoutSpace;
	}
	public String getFileNameWoutSpaceDest() {
		return fileNameWoutSpaceDest;
	}
	public void setFileNameWoutSpaceDest(String fileNameWoutSpaceDest) {
		this.fileNameWoutSpaceDest = fileNameWoutSpaceDest;
	}
	public String getSheetName() {
		return sheetName;
	}
	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}
	public String getSheetNameWoutSpace() {
		return sheetNameWoutSpace;
	}
	public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
		this.sheetNameWoutSpace = sheetNameWoutSpace;
	}
	public String getSheetNameDest() {
		return sheetNameDest;
	}
	public void setSheetNameDest(String sheetNameDest) {
		this.sheetNameDest = sheetNameDest;
	}
	public String getSheetNameDestWoutSpace() {
		return sheetNameDestWoutSpace;
	}
	public void setSheetNameDestWoutSpace(String sheetNameDestWoutSpace) {
		this.sheetNameDestWoutSpace = sheetNameDestWoutSpace;
	}
	public String getPptVerifyTba() {
		return pptVerifyTba;
	}
	public void setPptVerifyTba(String pptVerifyTba) {
		this.pptVerifyTba = pptVerifyTba;
	}
	  
}
