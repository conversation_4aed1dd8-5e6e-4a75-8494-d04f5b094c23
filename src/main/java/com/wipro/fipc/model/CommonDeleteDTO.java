package com.wipro.fipc.model;

import java.util.Date;

import com.wipro.fipc.model.generated.ProcessJobMapping;

import lombok.Data;

@Data
public class CommonDeleteDTO {
	
	private String id;
	private String processJobMappingId;
	private ProcessJobMapping processJobMapping;
	private String updatedBy;
	private Date updatedDate;
	private String activeFlag;
	private String fileName;
	private Boolean updateFlag;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getProcessJobMappingId() {
		return processJobMappingId;
	}
	public void setProcessJobMappingId(String processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}
	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}
	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public Boolean getUpdateFlag() {
		return updateFlag;
	}
	public void setUpdateFlag(Boolean updateFlag) {
		this.updateFlag = updateFlag;
	}
	@Override
	public String toString() {
		return "CommonDeleteDTO [id=" + id + ", processJobMappingId=" + processJobMappingId + ", processJobMapping="
				+ processJobMapping + ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate + ", activeFlag="
				+ activeFlag + ", fileName=" + fileName + ", updateFlag=" + updateFlag + "]";
	}
	
}