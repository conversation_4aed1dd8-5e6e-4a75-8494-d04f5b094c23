package com.wipro.fipc.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.entity.TemplateReportLayOut;

import lombok.Data;

@Data
public class TemplateReportLayoutResponse {

	@JsonProperty("json")
	private List<TemplateReportLayOut> json;

	@JsonProperty("status")
	private String status;

	@JsonProperty("statusMessage")
	private String statusMessage;

	/**
	 * @return the json
	 */
	public List<TemplateReportLayOut> getJson() {
		return json;
	}

	/**
	 * @param json the json to set
	 */
	public void setJson(List<TemplateReportLayOut> json) {
		this.json = json;
	}

	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the statusMessage
	 */
	public String getStatusMessage() {
		return statusMessage;
	}

	/**
	 * @param statusMessage the statusMessage to set
	 */
	public void setStatusMessage(String statusMessage) {
		this.statusMessage = statusMessage;
	}
}
