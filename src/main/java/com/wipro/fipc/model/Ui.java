package com.wipro.fipc.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class Ui {

	@JsonProperty("KSD")
	private boolean ksd;

	@JsonProperty("Layout")
	private List<QuestionBool> layout;

	@JsonProperty("TBA")
	private List<QuestionBool> tba;

	@JsonProperty("Rules")
	private List<QuestionBool> rules;

	@JsonProperty("Match TBA")
	private List<QuestionBool> matchTBA;

	@JsonProperty("Process Control")
	private List<QuestionBool> processControl;

	@JsonProperty("Notification")
	private boolean notification;

	@JsonProperty("Maestro")
	private boolean maestro;

	@JsonProperty("Output Report")
	private boolean outputReport;

}
