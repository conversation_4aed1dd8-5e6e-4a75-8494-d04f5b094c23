package com.wipro.fipc.model;
/*package com.wipro.holmes.model;

import java.util.HashMap;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@RefreshScope

public class HolmesAppConfigServerModel {
	HashMap<String, Object> config;


	@Value("${server.port}")
	private String server_port;
	
	@Value("${eureka.instance.appname}")
	private String eureka_instance_appname;
	
	@Value("${eureka.client.fetchRegistry}")
	private String eureka_client_fetchRegistry;
	
	@Value("${eureka.client.serviceUrl.defaultZone}")
	private String eureka_client_serviceUrl_defaultZone;
	

	public HashMap<String, Object> getHomlesAppConfigInfo() {

		config = new HashMap<String, Object>();

		config.put("server.port","server_port");	
		config.put("eureka.instance.appname","eureka_instance_appname");
		config.put("eureka.client.fetchRegistry","eureka_client_fetchRegistry");
		config.put("eureka.client.serviceUrl.defaultZone","eureka_client_serviceUrl_defaultZone");

		return config;
	}

}
*/