package com.wipro.fipc.model;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PanelDataBean {
	private String displayCode;
	private String displayName;
	private List<TbaInquiryMetaDataMaster> TbaInquiryMetaDatas;
	private List<Map<String, String>> TbaUpdateMetaDatas;
}