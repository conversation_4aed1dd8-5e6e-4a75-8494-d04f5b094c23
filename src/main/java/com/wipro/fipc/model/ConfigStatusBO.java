










package com.wipro.fipc.model;

import lombok.Data;

@Data
public class ConfigStatusBO {
       private Long processJobMappingId;
       private String configStatus;
       private String updatedBy;
       

       /**
	 * @return the processJobMappingId
	 */
	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}


	/**
	 * @param processJobMappingId the processJobMappingId to set
	 */
	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}


	/**
	 * @return the configStatus
	 */
	public String getConfigStatus() {
		return configStatus;
	}


	/**
	 * @param configStatus the configStatus to set
	 */
	public void setConfigStatus(String configStatus) {
		this.configStatus = configStatus;
	}


	/**
	 * @return the updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}


	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}


	public ConfigStatusBO(Long processJobMappingId, String configStatus,String updatedBy) {
              super();
              this.processJobMappingId = processJobMappingId;
              this.configStatus = configStatus;
              this.updatedBy = updatedBy;
       }
}