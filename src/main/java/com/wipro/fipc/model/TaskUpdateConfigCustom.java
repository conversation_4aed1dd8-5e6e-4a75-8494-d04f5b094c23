/*
 * Generic REST API
 * Simple REST API Generation
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.model.generated.TaskUpdateConfig;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TaskUpdateConfigCustom
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2020-03-02T16:44:52.246+05:30")
public class TaskUpdateConfigCustom   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("attachment")
  private String[] attachment = null;
  
  @JsonProperty("unsecuredAttachment")
  private String[] unsecuredAttachment = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("interestedFields")
  private String[] interestedFields = null;

  @JsonProperty("maestroTaskName")
  private String maestroTaskName = null;

  @JsonProperty("newDiscussion")
  private String newDiscussion = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;
  
  @JsonProperty("questionnaire")
  private String questionnaire = null;
  
  @JsonProperty("type")
  private String type = null; 

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TaskUpdateConfigCustom activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TaskUpdateConfigCustom attachment(String[] attachment) {
    this.attachment = attachment;
    return this;
  }

   /**
   * Get attachment
   * @return attachment
  **/
  @JsonProperty("attachment")
  @ApiModelProperty(value = "")
  public String[] getAttachment() {
    return attachment;
  }

  public void setAttachment(String[] attachment) {
    this.attachment = attachment;
  }
  public TaskUpdateConfigCustom unsecuredAttachment(String[] unsecuredAttachment) {
	  this.unsecuredAttachment = unsecuredAttachment;
	  return this;
  }
  
  /**
   * Get attachment
   * @return attachment
   **/
  @JsonProperty("unsecuredAttachment")
  @ApiModelProperty(value = "")
  public String[] getUnsecuredAttachment() {
	  return unsecuredAttachment;
  }
  
  public void setUnsecuredAttachment(String[] unsecuredAttachment) {
	  this.unsecuredAttachment = unsecuredAttachment;
  }

  public TaskUpdateConfigCustom createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TaskUpdateConfigCustom createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TaskUpdateConfigCustom id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TaskUpdateConfigCustom interestedFields(String[] interestedFields) {
    this.interestedFields = interestedFields;
    return this;
  }

   /**
   * Get interestedFields
   * @return interestedFields
  **/
  @JsonProperty("interestedFields")
  @ApiModelProperty(value = "")
  public String[] getInterestedFields() {
    return interestedFields;
  }

  public void setInterestedFields(String[] interestedFields) {
    this.interestedFields = interestedFields;
  }

  public TaskUpdateConfigCustom maestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
    return this;
  }

   /**
   * Get maestroTaskName
   * @return maestroTaskName
  **/
  @JsonProperty("maestroTaskName")
  @ApiModelProperty(value = "")
  public String getMaestroTaskName() {
    return maestroTaskName;
  }

  public void setMaestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
  }

  public TaskUpdateConfigCustom newDiscussion(String newDiscussion) {
    this.newDiscussion = newDiscussion;
    return this;
  }

   /**
   * Get newDiscussion
   * @return newDiscussion
  **/
  @JsonProperty("newDiscussion")
  @ApiModelProperty(value = "")
  public String getNewDiscussion() {
    return newDiscussion;
  }

  public void setNewDiscussion(String newDiscussion) {
    this.newDiscussion = newDiscussion;
  }

  public TaskUpdateConfigCustom processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }
  
  public TaskUpdateConfigCustom questionnaire(String questionnaire) {
	    this.questionnaire = questionnaire;
	    return this;
	  }
  /**
   * Get questionnaire
   * @return questionnaire
  **/
  @JsonProperty("questionnaire")
  @ApiModelProperty(value = "")
  public String getQuestionnaire() {
    return questionnaire;
  }

  public void setQuestionnaire(String questionnaire) {
    this.questionnaire = questionnaire;
  }

  public TaskUpdateConfigCustom type(String type) {
	    this.type = type;
	    return this;
	  }

	   /**
	   * Get type
	   * @return type
	  **/
	  @JsonProperty("type")
	  @ApiModelProperty(value = "")
	  public String getType() {
	    return type;
	  }

	  public void setType(String type) {
	    this.type = type;
	  }

  
  public TaskUpdateConfigCustom updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TaskUpdateConfigCustom updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskUpdateConfigCustom TaskUpdateConfigCustom = (TaskUpdateConfigCustom) o;
    return Objects.equals(this.activeFlag, TaskUpdateConfigCustom.activeFlag) &&
        Objects.equals(this.attachment, TaskUpdateConfigCustom.attachment) &&
        Objects.equals(this.unsecuredAttachment, TaskUpdateConfigCustom.unsecuredAttachment) &&
        Objects.equals(this.createdBy, TaskUpdateConfigCustom.createdBy) &&
        Objects.equals(this.createdDate, TaskUpdateConfigCustom.createdDate) &&
        Objects.equals(this.id, TaskUpdateConfigCustom.id) &&
        Objects.equals(this.interestedFields, TaskUpdateConfigCustom.interestedFields) &&
        Objects.equals(this.maestroTaskName, TaskUpdateConfigCustom.maestroTaskName) &&
        Objects.equals(this.newDiscussion, TaskUpdateConfigCustom.newDiscussion) &&
        Objects.equals(this.processJobMapping, TaskUpdateConfigCustom.processJobMapping) &&
        Objects.equals(this.questionnaire, TaskUpdateConfigCustom.questionnaire) &&
        Objects.equals(this.type, TaskUpdateConfigCustom.type) &&
        Objects.equals(this.updatedBy, TaskUpdateConfigCustom.updatedBy) &&
        Objects.equals(this.updatedDate, TaskUpdateConfigCustom.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, attachment,unsecuredAttachment, createdBy, createdDate, id, interestedFields, maestroTaskName, newDiscussion, processJobMapping, questionnaire, type, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskUpdateConfigCustom {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    attachment: ").append(toIndentedString(attachment)).append("\n");
    sb.append("    unsecuredAttachment: ").append(toIndentedString(unsecuredAttachment)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    interestedFields: ").append(toIndentedString(interestedFields)).append("\n");
    sb.append("    maestroTaskName: ").append(toIndentedString(maestroTaskName)).append("\n");
    sb.append("    newDiscussion: ").append(toIndentedString(newDiscussion)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    questionnaire: ").append(toIndentedString(questionnaire)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

