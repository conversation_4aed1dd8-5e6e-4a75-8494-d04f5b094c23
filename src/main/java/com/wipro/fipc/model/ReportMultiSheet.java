package com.wipro.fipc.model;

import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ReportMultiSheet {
	private String id;
	private String processJobMappingId;
	private String fileName;
	private String fileFormat;
	private String thresholdMin;
	private String thresholdMax;
	private String dateFormat;
	private String subject;
	private String sender;
	private String variance;
	private String fileType;
	private String delimiter;
	private String pptIdentifier;
	private String identifier;
	private String sheetName;
	private String source;
	private String path;
	private String recordIdentifierCol;
	private String createdBy;
	private String updatedBy;
	private Date createdDate;
	private String activeFlag;
	private String sheetNameWoutSpace;
	private String fileNameWoutSpace;
	private String tool;
	private String domain;
	private String queryJCLName;
	private String dateGenerated;
	private String dateFrequency;
	private String datePeriod;
	private String dateInterval;
	private String prevReportFileName;
	private String prevReportFileNameWs;
	private String subfolder;
	private String action;
	private List useLabellingRpt;
	private List<String> whitelistSSN;
	private String client;
	private String database;
	private String sqlQuery;
	private String recordCntCheck;
	private List<LayoutRecord> detailRecords;
	private String primaryFile;
	private String samplingCount;
	private String fileNameTemplate;
	private String appendNameFlag;
	private boolean verifyEmailOnly;
	private String emailSearchBy;
	private String benePptIdentifier;
	private String benePptIdentifierType;
	private boolean verifyFileDateDetailRecord;

}
