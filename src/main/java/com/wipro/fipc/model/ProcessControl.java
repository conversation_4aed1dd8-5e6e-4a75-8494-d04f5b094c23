package com.wipro.fipc.model;

import java.util.Date;

import com.wipro.fipc.model.generated.ProcessJobMapping;

import lombok.Data;

@Data
public class ProcessControl {
	 
	  private Object actions;
	  private String activeFlag;
	  private String application;
	  private String correctiveAction;
	  private String createdBy;
	  private Date createdDate;
	  private String fieldName;
	  private String fieldNameWoutSpace;
	  private Long id;
	  private String identifier;
	  private ProcessJobMapping processJobMapping;
	  private String ruleName;
	  private String updatedBy;
	  private Date updatedDate;
	  private String applicationWoutSpace;
	public Object getActions() {
		return actions;
	}
	public void setActions(Object actions) {
		this.actions = actions;
	}
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public String getApplication() {
		return application;
	}
	public void setApplication(String application) {
		this.application = application;
	}
	public String getCorrectiveAction() {
		return correctiveAction;
	}
	public void setCorrectiveAction(String correctiveAction) {
		this.correctiveAction = correctiveAction;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public String getFieldName() {
		return fieldName;
	}
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	public String getFieldNameWoutSpace() {
		return fieldNameWoutSpace;
	}
	public void setFieldNameWoutSpace(String fieldNameWoutSpace) {
		this.fieldNameWoutSpace = fieldNameWoutSpace;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getIdentifier() {
		return identifier;
	}
	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}
	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}
	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}
	public String getRuleName() {
		return ruleName;
	}
	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public String getApplicationWoutSpace() {
		return applicationWoutSpace;
	}
	public void setApplicationWoutSpace(String applicationWoutSpace) {
		this.applicationWoutSpace = applicationWoutSpace;
	}
	  
}
