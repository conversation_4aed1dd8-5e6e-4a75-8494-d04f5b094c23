/*
 * Generic REST API
 * Simple REST API Generation
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * DroolFileDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2020-02-27T10:21:55.385+05:30")
public class DroolFileDetailsManualModel  {
  @JsonProperty("pjmId")
  private Integer pjmId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

/*  @JsonProperty("droolContent")
  private List<byte[]> droolContent = new ArrayList<byte[]>();*/
  
  @JsonProperty("droolContent")
  private byte[] droolContent = null;
  
  @JsonProperty("activeFlag")
  private String activeFlag=null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("keyName")
  private String keyName = null;

  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")  
  public String getActiveFlag() {
	return activeFlag;
}

public void setActiveFlag(String activeFlag) {
	this.activeFlag = activeFlag;
}

@JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public DroolFileDetailsManualModel pjmId(Integer pjmId) {
    this.pjmId = pjmId;
    return this;
  }

  @JsonProperty("pjmId")
  @ApiModelProperty(value = "")
  public Integer getPjmId() {
    return pjmId;
  }

  public void setPjmId(Integer pjmId) {
    this.pjmId = pjmId;
  }

  public DroolFileDetailsManualModel createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public DroolFileDetailsManualModel createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

/*  public DroolFileDetails droolContent(List<byte[]> droolContent) {
    this.droolContent = droolContent;
    return this;
  }

  public DroolFileDetails addDroolContentItem(byte[] droolContentItem) {
    this.droolContent.add(droolContentItem);
    return this;
  }*/
  
  public DroolFileDetailsManualModel droolContent(byte[] droolContent){
	  this.droolContent=droolContent;
	  return this;
  }

   /**
   * Get droolContent
   * @return droolContent
  **/
/*  @JsonProperty("droolContent")
  @ApiModelProperty(value = "")
  public List<byte[]> getDroolContent() {
    return droolContent;
  }

  public void setDroolContent(List<byte[]> droolContent) {
    this.droolContent = droolContent;
  }
*/
  
  
  public DroolFileDetailsManualModel id(Long id) {
    this.id = id;
    return this;
  }

  
  @JsonProperty("droolContent")
  @ApiModelProperty(value = "")
  public byte[] getDroolContent() {
	return droolContent;
}

public void setDroolContent(byte[] droolContent) {
	this.droolContent = droolContent;
}

/**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public DroolFileDetailsManualModel keytName(String keyName) {
    this.keyName = keyName;
    return this;
  }

   /**
   * Get keytName
   * @return keytName
  **/
  @JsonProperty("keytName")
  @ApiModelProperty(value = "")
  public String getKeyName() {
    return keyName;
  }

  public void setKeyName(String keyName) {
    this.keyName = keyName;
  }

  public DroolFileDetailsManualModel updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public DroolFileDetailsManualModel updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DroolFileDetailsManualModel droolFileDetails = (DroolFileDetailsManualModel) o;
    return Objects.equals(this.pjmId, droolFileDetails.pjmId) &&
        Objects.equals(this.createdBy, droolFileDetails.createdBy) &&
        Objects.equals(this.createdDate, droolFileDetails.createdDate) &&
        Objects.equals(this.droolContent, droolFileDetails.droolContent) &&
        Objects.equals(this.id, droolFileDetails.id) &&
        Objects.equals(this.keyName, droolFileDetails.keyName) &&
        Objects.equals(this.updatedBy, droolFileDetails.updatedBy) &&
        Objects.equals(this.activeFlag, droolFileDetails.activeFlag) &&
        Objects.equals(this.updatedDate, droolFileDetails.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(pjmId, createdBy, activeFlag, createdDate, droolContent, id, keyName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DroolFileDetails {\n");
    
    sb.append("    pjmId: ").append(toIndentedString(pjmId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    droolContent: ").append(toIndentedString(droolContent)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    keyName: ").append(toIndentedString(keyName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

