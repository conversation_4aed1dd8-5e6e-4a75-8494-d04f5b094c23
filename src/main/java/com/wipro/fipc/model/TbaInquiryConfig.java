package com.wipro.fipc.model;



import java.sql.Date;



import com.wipro.fipc.model.generated.ProcessJobMapping;



import lombok.Data;



@Data
public class TbaInquiryConfig {



   private String activeFlag = null;



   private String columnMatrix = null;



   private String createdBy = null;



   private Date createdDate = null;



   private String effDateType = null;



   private String effFromDate = null;



   private String effToDate = null;



   private String fieldType = null;



   private String flag = null;



   private Long id = null;



   private String identifier = null;



   private String inquiryDefName = null;



   private String inquiryName = null;



   private String jsonKey = null;



   private String metaData = null;



   private Integer panelId = null;



   private String parNM = null;



   private ProcessJobMapping processJobMapping = null;



   private String recordIdentifier = null;



   private String rowMatrix = null;



   private String sequence = null;



   private String subJsonKey = null;



   private String tbaFieldName = null;



   private String updatedBy = null;



   private Date updatedDate = null;
   
   private Object conditionJson=null;
}