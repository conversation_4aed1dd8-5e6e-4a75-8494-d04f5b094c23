package com.wipro.fipc.model;

import lombok.Data;

@Data
public class OperationJson {

	
	private String varRadio;
	private String varTextStaticValue;
	private String varApplication;
	private String varSheetName;
	private String varRecordIdentifier;
	private String varField;
	private String varArithOp;
	private String varFieldType;
	private String varDatePeriod;
	private String varDateFrequency;
	private String varDateInterval;
	private String varOpenRoundBracket;
	private String varCloseRoundBracket;
	private String varReuseVariable;
	private String[] varGroupByArray;
	private String varSheetNameForFilter;
	
}
