package com.wipro.fipc.model;



import java.util.Date;

import lombok.Data;

@Data
public class TbaInquiryConfigBO {

	private String activeFlag;
	private String createdBy;
	private Date createdDate;
	private String fieldType;
	private Long id;
	private String identifier;
	private String inquiryDefName;
	private String inquiryName;
	private String jsonKey;
	private String metaData;
	private Integer panelId;
	private String parNM;
	private Long processJobMapping;
	private String recordIdentifier;
	private String subJsonKey;
	private String tbaFieldName;
	private String updatedBy;
	private Date updatedDate;
	private String sheetName;
	private String sheetNameWoutSpace;

}
