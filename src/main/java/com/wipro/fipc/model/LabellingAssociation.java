package com.wipro.fipc.model;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class LabellingAssociation {

	@JsonProperty("association")
	private List<String> association = new ArrayList<String>();

	/**
	 * @return the association
	 */
	public List<String> getAssociation() {
		return association;
	}

	/**
	 * @param association the association to set
	 */
	public void setAssociation(List<String> association) {
		this.association = association;
	}

}
