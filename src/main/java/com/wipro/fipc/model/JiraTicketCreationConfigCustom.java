package com.wipro.fipc.model;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class JiraTicketCreationConfigCustom implements Serializable{

	@JsonProperty("id")
	private Long id;

	@JsonProperty("reporter")
	private String reporter;

	@JsonProperty("summary")
	private String summary;

	@JsonProperty("issueType")
	private String issueType;
	
	@JsonProperty("domain")
	private String domain;

	@JsonProperty("priority")
	private String priority;
	
	@JsonProperty("dueDate")
	private String dueDate;
	
	@JsonProperty("project")
	private String project;
	
	@JsonProperty("applicableClients")
	private String applicableClients;

	@JsonProperty("reportingGroup")
	private String reportingGroup;

	@JsonProperty("description")
	private String description;

	@JsonProperty("status")
	private String status;

	@JsonProperty("attachment")
	private String[] attachment;

	@JsonProperty("activeFlag")
	private String activeFlag;

	@JsonProperty("processJobMapping")
	private ProcessJobMapping processJobMapping;

	@JsonProperty("assignee")
	private String assignee;

	@JsonProperty("assignee_email")
	private String assignee_email;
	
	@JsonProperty("epicName")
	private String epicName;
	
	@JsonProperty("epicType")
	private String epicType;
	
	@JsonProperty("beginDate")
	private String beginDate;
	
	@JsonProperty("jiraWatchers")
	private List<JiraWatcher> jiraWatchers; 
	
	@JsonProperty("processName")
	private String processName;
	
	@JsonProperty("impactedSharedGroup")
	private String impactedSharedGroup;
}
