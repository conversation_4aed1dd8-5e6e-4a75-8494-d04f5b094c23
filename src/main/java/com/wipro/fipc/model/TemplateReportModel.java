package com.wipro.fipc.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class TemplateReportModel {

	@JsonProperty("id")
	private String id = null;

	@JsonProperty("activeFlag")
	private char activeFlag;

	@JsonProperty("buId")
	private Integer buId = null;

	@JsonProperty("clientId")
	private String clientId = null;

	@JsonProperty("clientName")
	private String clientName = null;

	@JsonProperty("createdBy")
	private String createdBy = null;

	@JsonProperty("templateReportName")
	private String templateReportName = null;

	@JsonProperty("templateReportNameWs")
	private String templateReportNameWs = null;

	@JsonProperty("type")
	private String type = null;

	@JsonProperty("updatedBy")
	private String updatedBy = null;

	@JsonProperty("uploadedBy")
	private String uploadedBy = null;

	@JsonProperty("reportFlag")
	private Character reportFlag;

	@JsonProperty("labellingReportRecord")
	private LabellingReportRecord labellingReportRecord;

	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @return the activeFlag
	 */
	public char getActiveFlag() {
		return activeFlag;
	}

	/**
	 * @param activeFlag the activeFlag to set
	 */
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	/**
	 * @return the buId
	 */
	public Integer getBuId() {
		return buId;
	}

	/**
	 * @param buId the buId to set
	 */
	public void setBuId(Integer buId) {
		this.buId = buId;
	}

	/**
	 * @return the clientId
	 */
	public String getClientId() {
		return clientId;
	}

	/**
	 * @param clientId the clientId to set
	 */
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	/**
	 * @return the clientName
	 */
	public String getClientName() {
		return clientName;
	}

	/**
	 * @param clientName the clientName to set
	 */
	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	/**
	 * @return the createdBy
	 */
	public String getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the templateReportName
	 */
	public String getTemplateReportName() {
		return templateReportName;
	}

	/**
	 * @param templateReportName the templateReportName to set
	 */
	public void setTemplateReportName(String templateReportName) {
		this.templateReportName = templateReportName;
	}

	/**
	 * @return the templateReportNameWs
	 */
	public String getTemplateReportNameWs() {
		return templateReportNameWs;
	}

	/**
	 * @param templateReportNameWs the templateReportNameWs to set
	 */
	public void setTemplateReportNameWs(String templateReportNameWs) {
		this.templateReportNameWs = templateReportNameWs;
	}

	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param type the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * @return the updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the uploadedBy
	 */
	public String getUploadedBy() {
		return uploadedBy;
	}

	/**
	 * @param uploadedBy the uploadedBy to set
	 */
	public void setUploadedBy(String uploadedBy) {
		this.uploadedBy = uploadedBy;
	}

	/**
	 * @return the reportFlag
	 */
	public Character getReportFlag() {
		return reportFlag;
	}

	/**
	 * @param reportFlag the reportFlag to set
	 */
	public void setReportFlag(Character reportFlag) {
		this.reportFlag = reportFlag;
	}

	/**
	 * @return the labellingReportRecord
	 */
	public LabellingReportRecord getLabellingReportRecord() {
		return labellingReportRecord;
	}

	/**
	 * @param labellingReportRecord the labellingReportRecord to set
	 */
	public void setLabellingReportRecord(LabellingReportRecord labellingReportRecord) {
		this.labellingReportRecord = labellingReportRecord;
	}
}
