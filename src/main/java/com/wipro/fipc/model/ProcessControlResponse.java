package com.wipro.fipc.model;

import java.util.List;

import com.wipro.fipc.model.generated.ProcessControlConfig;

import lombok.Data;

@Data
public class ProcessControlResponse {

	private String message;
	private List<ProcessControlConfig> errorList;
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public List<ProcessControlConfig> getErrorList() {
		return errorList;
	}
	public void setErrorList(List<ProcessControlConfig> errorList) {
		this.errorList = errorList;
	}
	
}
