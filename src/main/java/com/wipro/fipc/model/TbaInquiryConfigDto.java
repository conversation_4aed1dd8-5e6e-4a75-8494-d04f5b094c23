package com.wipro.fipc.model;



import java.util.Date;



import com.wipro.fipc.model.generated.ProcessJobMapping;



import lombok.Data;



@Data
public class TbaInquiryConfigDto {



   private String activeFlag = null;



   private String columnMatrix = null;



   private String createdBy = null;



   private Date createdDate = null;



   private String effDateType = null;



   private Object effFromDate = null;



   private Object effToDate = null;



   private String fieldType = null;



   private String flag = null;



   private Long id = null;



   private String identifier = null;



   private String inquiryDefName = null;



   private String inquiryName = null;



   private String jsonKey = null;



   private String metaData = null;



   private Integer panelId = null;



   private String parNM = null;



   private ProcessJobMapping processJobMapping = null;



   private Long processJobMappingId = null;



   private String recordIdentifier = null;



   private String rowMatrix = null;



   private String sequence = null;



   private String subJsonKey = null;



   private String tbaFieldName = null;



   private String updatedBy = null;



   private Date updatedDate = null;



}