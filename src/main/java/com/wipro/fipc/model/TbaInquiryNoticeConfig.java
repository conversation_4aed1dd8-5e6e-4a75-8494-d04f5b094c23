package com.wipro.fipc.model;

import java.util.Date;

import com.wipro.fipc.entity.ProcessJobMapping;


public class TbaInquiryNoticeConfig {
	  private String activeFlag;
	  private Integer clientId;
	  private String createdBy;
	  private Date createdDate;
	  private String fieldType;
	  private Long id;
	  private String identifier;
	  private String inquiryDefName;
	  private String inquiryName;
	  private String jsonKey;
	  private String metadata;
	  private Integer noticeId;
	  private String noticeName;
	  private Integer panelId;
	  private String parNm;
	  private ProcessJobMapping processJobMapping;
	  private Long processJobMappingId;
	  private String recordIdentifier;
	  private String subJsonKey;
	  private String tbaFieldName; 
	  private String updatedBy;
	  private Date updatedDate;
	  private String sheetName;
	  private String sheetNameWoutSpace;
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public Integer getClientId() {
		return clientId;
	}
	public void setClientId(Integer clientId) {
		this.clientId = clientId;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getIdentifier() {
		return identifier;
	}
	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}
	public String getInquiryDefName() {
		return inquiryDefName;
	}
	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}
	public String getInquiryName() {
		return inquiryName;
	}
	public void setInquiryName(String inquiryName) {
		this.inquiryName = inquiryName;
	}
	public String getJsonKey() {
		return jsonKey;
	}
	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}
	public String getMetadata() {
		return metadata;
	}
	public void setMetadata(String metadata) {
		this.metadata = metadata;
	}
	public Integer getNoticeId() {
		return noticeId;
	}
	public void setNoticeId(Integer noticeId) {
		this.noticeId = noticeId;
	}
	public String getNoticeName() {
		return noticeName;
	}
	public void setNoticeName(String noticeName) {
		this.noticeName = noticeName;
	}
	public Integer getPanelId() {
		return panelId;
	}
	public void setPanelId(Integer panelId) {
		this.panelId = panelId;
	}
	public String getParNm() {
		return parNm;
	}
	public void setParNm(String parNm) {
		this.parNm = parNm;
	}
	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}
	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}
	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}
	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}
	public String getRecordIdentifier() {
		return recordIdentifier;
	}
	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}
	public String getSubJsonKey() {
		return subJsonKey;
	}
	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}
	public String getTbaFieldName() {
		return tbaFieldName;
	}
	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public String getSheetName() {
		return sheetName;
	}
	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}
	public String getSheetNameWoutSpace() {
		return sheetNameWoutSpace;
	}
	public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
		this.sheetNameWoutSpace = sheetNameWoutSpace;
	}
	  
}
