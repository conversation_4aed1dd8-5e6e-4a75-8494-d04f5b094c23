package com.wipro.fipc.model;

import lombok.Data;

@Data
public class EventHistoryEffectiveDate {
	
	private String datePeriod;
	private String dateFrequency;
	private String dateInterval;
	public String getDatePeriod() {
		return datePeriod;
	}
	public void setDatePeriod(String datePeriod) {
		this.datePeriod = datePeriod;
	}
	public String getDateFrequency() {
		return dateFrequency;
	}
	public void setDateFrequency(String dateFrequency) {
		this.dateFrequency = dateFrequency;
	}
	public String getDateInterval() {
		return dateInterval;
	}
	public void setDateInterval(String dateInterval) {
		this.dateInterval = dateInterval;
	}


}
