package com.wipro.fipc.model;

import java.util.Date;

import lombok.Data;

@Data
public class NoticeInquiryBO {

	  private String activeFlag;
	  private Integer clientId;
	  private String createdBy;
	  private Date createdDate;
	  private String fieldType;
	  private Long id;
	  private String identifier;
	  private String inquiryDefName;
	  private String jsonKey;
	  private String metadata;
	  private Integer noticeId;
	  private String noticeName;
	  private String parNm;
	  private Long processJobMappingId;
	  private String recordIdentifier;
	  private String subJsonKey;
	  private String tbaFieldName;
	  private String updatedBy;
	  private Date updatedDate;
	  private String sheetName;
	  private String sheetNameWoutSpace;

}
