package com.wipro.fipc.model;

import java.util.Comparator;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TbaInquiryMetaDataMaster {
	@JsonIgnore
	private int clientId;

	@JsonIgnore
	private int panelId;

	@JsonIgnore
	private String metadataType;

	@JsonIgnore
	private String metadataTag;

	private String attributeId;
	
	private String attributeCode;

	private String attributeText;
	
	private String trustCode;
	
	public static final Comparator<TbaInquiryMetaDataMaster> comparator = Comparator
			.comparing(TbaInquiryMetaDataMaster::getClientId)
			.thenComparing(TbaInquiryMetaDataMaster::getMetadataType)
			.thenComparing(TbaInquiryMetaDataMaster::getAttributeText)
			.thenComparing(TbaInquiryMetaDataMaster::getTrustCode);
}
