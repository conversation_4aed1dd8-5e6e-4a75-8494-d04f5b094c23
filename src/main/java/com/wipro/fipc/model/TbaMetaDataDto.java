package com.wipro.fipc.model;

import java.util.Date;

import lombok.Data;

@Data
public class TbaMetaDataDto {
	
	  private Integer clientId;
	  private String[] columnValue;
	  private String createdBy;
	  private Date createdDate;
	  private Long id;
	  private String metaData;
	  private Integer panelId;
	  private String[] rowValue;
	  private String updatedBy;
	  private Date updatedDate;
	public Integer getClientId() {
		return clientId;
	}
	public void setClientId(Integer clientId) {
		this.clientId = clientId;
	}
	public String[] getColumnValue() {
		return columnValue;
	}
	public void setColumnValue(String[] columnValue) {
		this.columnValue = columnValue;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getMetaData() {
		return metaData;
	}
	public void setMetaData(String metaData) {
		this.metaData = metaData;
	}
	public Integer getPanelId() {
		return panelId;
	}
	public void setPanelId(Integer panelId) {
		this.panelId = panelId;
	}
	public String[] getRowValue() {
		return rowValue;
	}
	public void setRowValue(String[] rowValue) {
		this.rowValue = rowValue;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	  
	  
}
