package com.wipro.fipc.model;

import lombok.Data;

@Data
public class KsdParentMetaData {

	private String primaryJobName;
	private Long processFeatureMappingId;
	private String jobStream;
	private String frequency;
	private String jobSchedule;
	private String dayOfTheWeek;
	private String date;
	private String custom;
	private String timeZone;
	private String jobScheduleTime;
	private String jobCutOffTime;
	private String turnaroundTime;
	private String mainframeFileName;
	private String mainframeFileType;
	private String processStartTime;
	private String eftName;
	private String maestroTaskName;
	private String variation;
	private String minThreshold;
	private String maxThreshold;
	private String dailyTaskReportSubjectNameOutLook;
	private String delimeter;
	private String createdBy;
	private String updatedBy;
	private String createdDate;
	private String updatedDate;

}
