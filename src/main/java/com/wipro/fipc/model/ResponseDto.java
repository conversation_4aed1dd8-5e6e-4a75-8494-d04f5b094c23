package com.wipro.fipc.model;

import com.fasterxml.jackson.annotation.JsonRawValue;

import lombok.Data;

@Data
public class ResponseDto {

	@JsonRawValue
	private String data;
	private String status;
	private String message;
	public String getData() {
		return data;
	}
	public void setData(String data) {
		this.data = data;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	
}
