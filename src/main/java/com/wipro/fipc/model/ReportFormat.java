/*
 * Generic REST API
 * Simple REST API Generation
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model;

import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.utils.CustomJsonObjectDeserializer;

import io.swagger.annotations.ApiModelProperty;

/**
 * ReportFormat
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2020-06-25T22:16:38.911+05:30")
public class ReportFormat   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonDeserialize(using= CustomJsonObjectDeserializer.class)
  @JsonRawValue
  @JsonProperty("alignment")
  private String alignment = null;

  @JsonDeserialize(using= CustomJsonObjectDeserializer.class)
  @JsonRawValue
  @JsonProperty("border")
  private String border = null;

  @JsonProperty("columnName")
  private String columnName = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonDeserialize(using= CustomJsonObjectDeserializer.class)
  @JsonRawValue
  @JsonProperty("font")
  private String font = null;

  @JsonProperty("freezes")
  private String freezes = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonDeserialize(using= CustomJsonObjectDeserializer.class)
  @JsonRawValue
  @JsonProperty("patternFill")
  private String patternFill = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public ReportFormat activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ReportFormat alignment(String alignment) {
    this.alignment = alignment;
    return this;
  }

   /**
   * Get alignment
   * @return alignment
  **/
  @JsonProperty("alignment")
  @ApiModelProperty(value = "")
  public String getAlignment() {
    return alignment;
  }

  public void setAlignment(String alignment) {
    this.alignment = alignment;
  }

  public ReportFormat border(String border) {
    this.border = border;
    return this;
  }

   /**
   * Get border
   * @return border
  **/
  @JsonProperty("border")
  @ApiModelProperty(value = "")
  public String getBorder() {
    return border;
  }

  public void setBorder(String border) {
    this.border = border;
  }

  public ReportFormat columnName(String columnName) {
    this.columnName = columnName;
    return this;
  }

   /**
   * Get columnName
   * @return columnName
  **/
  @JsonProperty("columnName")
  @ApiModelProperty(value = "")
  public String getColumnName() {
    return columnName;
  }

  public void setColumnName(String columnName) {
    this.columnName = columnName;
  }

  public ReportFormat createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ReportFormat createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ReportFormat fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public ReportFormat font(String font) {
    this.font = font;
    return this;
  }

   /**
   * Get font
   * @return font
  **/
  @JsonProperty("font")
  @ApiModelProperty(value = "")
  public String getFont() {
    return font;
  }

  public void setFont(String font) {
    this.font = font;
  }

  public ReportFormat freezes(String freezes) {
    this.freezes = freezes;
    return this;
  }

   /**
   * Get freezes
   * @return freezes
  **/
  @JsonProperty("freezes")
  @ApiModelProperty(value = "")
  public String getFreezes() {
    return freezes;
  }

  public void setFreezes(String freezes) {
    this.freezes = freezes;
  }

  public ReportFormat id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ReportFormat patternFill(String patternFill) {
    this.patternFill = patternFill;
    return this;
  }

   /**
   * Get patternFill
   * @return patternFill
  **/
  @JsonProperty("patternFill")
  @ApiModelProperty(value = "")
  public String getPatternFill() {
    return patternFill;
  }

  public void setPatternFill(String patternFill) {
    this.patternFill = patternFill;
  }

  public ReportFormat processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public ReportFormat recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public ReportFormat updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ReportFormat updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReportFormat reportFormat = (ReportFormat) o;
    return Objects.equals(this.activeFlag, reportFormat.activeFlag) &&
        Objects.equals(this.alignment, reportFormat.alignment) &&
        Objects.equals(this.border, reportFormat.border) &&
        Objects.equals(this.columnName, reportFormat.columnName) &&
        Objects.equals(this.createdBy, reportFormat.createdBy) &&
        Objects.equals(this.createdDate, reportFormat.createdDate) &&
        Objects.equals(this.fileName, reportFormat.fileName) &&
        Objects.equals(this.font, reportFormat.font) &&
        Objects.equals(this.freezes, reportFormat.freezes) &&
        Objects.equals(this.id, reportFormat.id) &&
        Objects.equals(this.patternFill, reportFormat.patternFill) &&
        Objects.equals(this.processJobMapping, reportFormat.processJobMapping) &&
        Objects.equals(this.recordIdentifier, reportFormat.recordIdentifier) &&
        Objects.equals(this.updatedBy, reportFormat.updatedBy) &&
        Objects.equals(this.updatedDate, reportFormat.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, alignment, border, columnName, createdBy, createdDate, fileName, font, freezes, id, patternFill, processJobMapping, recordIdentifier, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReportFormat {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    alignment: ").append(toIndentedString(alignment)).append("\n");
    sb.append("    border: ").append(toIndentedString(border)).append("\n");
    sb.append("    columnName: ").append(toIndentedString(columnName)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    font: ").append(toIndentedString(font)).append("\n");
    sb.append("    freezes: ").append(toIndentedString(freezes)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    patternFill: ").append(toIndentedString(patternFill)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

