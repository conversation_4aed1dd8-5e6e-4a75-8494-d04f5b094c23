package com.wipro.fipc.model;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class LabellingReportRecord {

	@JsonProperty("labellingRecord")
	private List<LabellingRecord> labellingRecord = new ArrayList<>();
	
	@JsonProperty("labellingAssociation")
	private List<LabellingAssociation> labellingAssociation = new ArrayList<>();

	/**
	 * @return the labellingRecord
	 */
	public List<LabellingRecord> getLabellingRecord() {
		return labellingRecord;
	}

	/**
	 * @param labellingRecord the labellingRecord to set
	 */
	public void setLabellingRecord(List<LabellingRecord> labellingRecord) {
		this.labellingRecord = labellingRecord;
	}

	/**
	 * @return the labellingAssociation
	 */
	public List<LabellingAssociation> getLabellingAssociation() {
		return labellingAssociation;
	}

	/**
	 * @param labellingAssociation the labellingAssociation to set
	 */
	public void setLabellingAssociation(List<LabellingAssociation> labellingAssociation) {
		this.labellingAssociation = labellingAssociation;
	}
	
}
