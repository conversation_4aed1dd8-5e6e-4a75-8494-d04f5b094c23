package com.wipro.fipc.model;

import java.util.Date;

import lombok.Data;

@Data
public class TbaUpdateConfigBo {

	private Long id;
	private String activeFlag;
	private String tbaFieldName;
	private String basicInfo;
	private String eventName;
	private Integer activityId;
	private Integer panelId;
	private Integer classId;
	private String jsonKey;
	private String baseKey;
	private String subKey;
	private String metadata;
	private String transId;
	private String value;
	private Date createdDate;
	private String createdBy;
	private String updatedBy;
	private Date updatedDate;
	private String parNm;
	private String sequence;
	private String recordIdentifier;
	private String identifier;
	private String rerunFlag;
	private String addManualFlag;
	private String actLngDesc;
	
	private String sheetName;
	private String sheetNameWoutSpace;
	private String fieldType;
	
	private String inquiryDefName;
	

}
