package com.wipro.fipc.model;

public class MaestroTaskNameBO {

	private Long processJobMappingId;
	private String maestroTaskName;
	private String updatedBy;
	private String configStatus;
	private String maestroTaskType;

	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public String getMaestroTaskName() {
		return maestroTaskName;
	}

	public void setMaestroTaskName(String maestroTaskName) {
		this.maestroTaskName = maestroTaskName;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getConfigStatus() {
		return configStatus;
	}

	public void setConfigStatus(String configStatus) {
		this.configStatus = configStatus;
	}

	public String getMaestroTaskType() {
		return maestroTaskType;
	}

	public void setMaestroTaskType(String maestroTaskType) {
		this.maestroTaskType = maestroTaskType;
	}

}
