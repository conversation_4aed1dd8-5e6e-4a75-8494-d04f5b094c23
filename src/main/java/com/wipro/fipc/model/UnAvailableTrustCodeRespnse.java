package com.wipro.fipc.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Builder
public class UnAvailableTrustCodeRespnse {

	private String row;
	private String clientId;
	private String clientName;
	private String checkWritterName;
	private String longDescription;
	private String shortDescription;
	private String trustCode;
	private String comments;

}
