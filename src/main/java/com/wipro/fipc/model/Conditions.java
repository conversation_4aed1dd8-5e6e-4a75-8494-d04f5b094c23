package com.wipro.fipc.model;

import java.util.List;
import lombok.Data;

@Data
public class Conditions {

    private String field;
    private String fieldType;
    private String condition;
    private String value;
    private String operator;
    private String radio;
    private String fieldValue;
    private String tbaValue;
    private List<String> tbaFieldName;
    private String valueFieldType;
    private String datePeriod;
    private String dateFrequency;
    private String dateInterval;
    private String recordIdentifier;
    private String valueRecordIdentifier;
	private String uniqueValueField;
    private String uniqueField;
    private String appName;
    private String sheetName;
    private String valueAppName;
    private String valueSheetName;
    private String resultVariable;
    private String resultVariableRadio;
    private String valueResultVariable;
    private String openRoundBracket;
    private String closeRoundBracket;
    private String  sheetNameForFilter;
    private String valueSheetNameForFilter;
    private String sourceAggregateOperator;
    private String destAggregateOperator;
    private String sourceFieldType;
    private String destFieldType;

	public String getField() {
		return field;
	}
	public void setField(String field) {
		this.field = field;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public String getCondition() {
		return condition;
	}
	public void setCondition(String condition) {
		this.condition = condition;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	public String getRadio() {
		return radio;
	}
	public void setRadio(String radio) {
		this.radio = radio;
	}
	public String getFieldValue() {
		return fieldValue;
	}
	public void setFieldValue(String fieldValue) {
		this.fieldValue = fieldValue;
	}
	public String getTbaValue() {
		return tbaValue;
	}
	public void setTbaValue(String tbaValue) {
		this.tbaValue = tbaValue;
	}
	public List<String> getTbaFieldName() {
		return tbaFieldName;
	}
	public void setTbaFieldName(List<String> tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}
	public String getValueFieldType() {
		return valueFieldType;
	}
	public void setValueFieldType(String valueFieldType) {
		this.valueFieldType = valueFieldType;
	}
	public String getDatePeriod() {
		return datePeriod;
	}
	public void setDatePeriod(String datePeriod) {
		this.datePeriod = datePeriod;
	}
	public String getDateFrequency() {
		return dateFrequency;
	}
	public void setDateFrequency(String dateFrequency) {
		this.dateFrequency = dateFrequency;
	}
	public String getDateInterval() {
		return dateInterval;
	}
	public void setDateInterval(String dateInterval) {
		this.dateInterval = dateInterval;
	}
	public String getRecordIdentifier() {
		return recordIdentifier;
	}
	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}
	public String getValueRecordIdentifier() {
		return valueRecordIdentifier;
	}
	public void setValueRecordIdentifier(String valueRecordIdentifier) {
		this.valueRecordIdentifier = valueRecordIdentifier;
	}
	public String getUniqueValueField() {
		return uniqueValueField;
	}
	public void setUniqueValueField(String uniqueValueField) {
		this.uniqueValueField = uniqueValueField;
	}
	public String getUniqueField() {
		return uniqueField;
	}
	public void setUniqueField(String uniqueField) {
		this.uniqueField = uniqueField;
	}
	public String getAppName() {
		return appName;
	}
	public void setAppName(String appName) {
		this.appName = appName;
	}
	public String getSheetName() {
		return sheetName;
	}
	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}
	public String getValueAppName() {
		return valueAppName;
	}
	public void setValueAppName(String valueAppName) {
		this.valueAppName = valueAppName;
	}
	public String getValueSheetName() {
		return valueSheetName;
	}
	public void setValueSheetName(String valueSheetName) {
		this.valueSheetName = valueSheetName;
	}
	public String getResultVariable() {
		return resultVariable;
	}
	public void setResultVariable(String resultVariable) {
		this.resultVariable = resultVariable;
	}
	public String getResultVariableRadio() {
		return resultVariableRadio;
	}
	public void setResultVariableRadio(String resultVariableRadio) {
		this.resultVariableRadio = resultVariableRadio;
	}
	public String getValueResultVariable() {
		return valueResultVariable;
	}
	public void setValueResultVariable(String valueResultVariable) {
		this.valueResultVariable = valueResultVariable;
	}
	public String getOpenRoundBracket() {
		return openRoundBracket;
	}
	public void setOpenRoundBracket(String openRoundBracket) {
		this.openRoundBracket = openRoundBracket;
	}
	public String getCloseRoundBracket() {
		return closeRoundBracket;
	}
	public void setCloseRoundBracket(String closeRoundBracket) {
		this.closeRoundBracket = closeRoundBracket;
	}
	public String getSheetNameForFilter() {
		return sheetNameForFilter;
	}
	public void setSheetNameForFilter(String sheetNameForFilter) {
		this.sheetNameForFilter = sheetNameForFilter;
	}
	public String getValueSheetNameForFilter() {
		return valueSheetNameForFilter;
	}
	public void setValueSheetNameForFilter(String valueSheetNameForFilter) {
		this.valueSheetNameForFilter = valueSheetNameForFilter;
	}
	public String getSourceAggregateOperator() {
		return sourceAggregateOperator;
	}
	public void setSourceAggregateOperator(String sourceAggregateOperator) {
		this.sourceAggregateOperator = sourceAggregateOperator;
	}
	public String getDestAggregateOperator() {
		return destAggregateOperator;
	}
	public void setDestAggregateOperator(String destAggregateOperator) {
		this.destAggregateOperator = destAggregateOperator;
	}
	public String getSourceFieldType() {
		return sourceFieldType;
	}
	public void setSourceFieldType(String sourceFieldType) {
		this.sourceFieldType = sourceFieldType;
	}
	public String getDestFieldType() {
		return destFieldType;
	}
	public void setDestFieldType(String destFieldType) {
		this.destFieldType = destFieldType;
	} 
}