package com.wipro.fipc.model;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class JiraTaskUpdateConfigCustom implements Serializable {

	@JsonProperty("id")
	private Long id;

	@JsonProperty("issueType")
	private String issueType;

	@JsonProperty("newComments")
	private String newComments;

	@JsonProperty("interestedParties")
	private String[] interestedParties;

	@JsonProperty("attachment")
	private String[] attachment;

	@JsonProperty("activeFlag")
	private String activeFlag;

	@JsonProperty("processJobMapping")
	private ProcessJobMapping processJobMapping;
	
	@JsonProperty("jiraWatchers")
	private List<JiraWatcher> jiraWatchers;

}
