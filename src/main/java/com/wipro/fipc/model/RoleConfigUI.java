package com.wipro.fipc.model;

import com.wipro.fipc.entity.common.RoleConfig;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class RoleConfigUI {

	private Long id;

	private String employeeAdid;

	private String role;

	private String businessUnit;

	private String clientName;

	private String businessOps;

	private String process;

	private String processType;

	private String message;

	public RoleConfigUI(RoleConfig roleConfig) {
		if (roleConfig.getId() != null)
			this.id = roleConfig.getId();
		this.employeeAdid = roleConfig.getAdid();
		this.role = roleConfig.getRole();
		this.businessUnit = roleConfig.getBusinessUnit().getUnitName();
		this.clientName = roleConfig.getClientDetails().getClientName();
		if (roleConfig.getBusinessOps() != null)
			this.businessOps = roleConfig.getBusinessOps().getOpsName();
		if (roleConfig.getBusinessOps() == null)
			this.businessOps = "";
		if (roleConfig.getProcess() != null) {
			this.process = roleConfig.getProcess().getProcessName();
			this.processType = roleConfig.getProcess().getProcessType();
		}

		if (roleConfig.getProcess() == null) {
			this.process = "";
			this.processType = "";
		}
	}

}
