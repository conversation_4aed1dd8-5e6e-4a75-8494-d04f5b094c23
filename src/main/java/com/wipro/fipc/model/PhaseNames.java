package com.wipro.fipc.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class PhaseNames {

	@JsonProperty("UI")
	private Ui ui = null;

	@JsonProperty("Ques")
	private Ques ques = null;

	@JsonProperty("QuesYesNO")
	private QuesYesNO quesYesNO = null;

	@JsonProperty("externalReports")
	private Object externalReports = null;

	@JsonProperty("SourceMatch")
	private String sourceMatch = null;

	@JsonProperty("ProcessControl")
	private String processControl = null;

	@JsonProperty("Bots")
	private String bots = null;
	
	@JsonProperty("template")
	private Object template;
	
	@JsonProperty("labellingReport")
	private Object labellingReport;

}
