package com.wipro.fipc.model;

import java.util.Date;

import com.wipro.fipc.model.generated.ProcessJobMapping;

import lombok.Data;

@Data
public class TbaCommentInquiry {
	  private String activeFlag ;
	  private String createdBy ;
	  private Date createdDate ;
	  private EventHistoryEffectiveDate eftFromDate;
	  private EventHistoryEffectiveDate eftToDate;
	  private String fieldType ;
	  private Long id ;
	  private String inquiryDefName ;
	  private String jsonKey ;
	  private String parNM ;
	  private ProcessJobMapping processJobMapping ;
	  private String subJsonKey ;
	  private String tbaFieldName ;
	  private String updatedBy ;
	  private Date updatedDate ;
	  private Boolean updateFlag;
	  
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public EventHistoryEffectiveDate getEftFromDate() {
		return eftFromDate;
	}
	public void setEftFromDate(EventHistoryEffectiveDate eftFromDate) {
		this.eftFromDate = eftFromDate;
	}
	public EventHistoryEffectiveDate getEftToDate() {
		return eftToDate;
	}
	public void setEftToDate(EventHistoryEffectiveDate eftToDate) {
		this.eftToDate = eftToDate;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getInquiryDefName() {
		return inquiryDefName;
	}
	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}
	public String getJsonKey() {
		return jsonKey;
	}
	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}
	public String getParNM() {
		return parNM;
	}
	public void setParNM(String parNM) {
		this.parNM = parNM;
	}
	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}
	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}
	public String getSubJsonKey() {
		return subJsonKey;
	}
	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}
	public String getTbaFieldName() {
		return tbaFieldName;
	}
	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public Boolean getUpdateFlag() {
		return updateFlag;
	}
	public void setUpdateFlag(Boolean updateFlag) {
		this.updateFlag = updateFlag;
	}
	@Override
	public String toString() {
		return "TbaCommentInquiry [activeFlag=" + activeFlag + ", createdBy=" + createdBy + ", createdDate="
				+ createdDate + ", eftFromDate=" + eftFromDate + ", eftToDate=" + eftToDate + ", fieldType=" + fieldType
				+ ", id=" + id + ", inquiryDefName=" + inquiryDefName + ", jsonKey=" + jsonKey + ", parNM=" + parNM
				+ ", processJobMapping=" + processJobMapping + ", subJsonKey=" + subJsonKey + ", tbaFieldName="
				+ tbaFieldName + ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate + ", updateFlag="
				+ updateFlag + "]";
	}
	  
}
