/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaUpdateJsonKey
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaUpdateJsonKey   {
  @JsonProperty("baseKey")
  private String baseKey = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("parNm")
  private String parNm = null;

  @JsonProperty("subKey")
  private String subKey = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaUpdateJsonKey baseKey(String baseKey) {
    this.baseKey = baseKey;
    return this;
  }

   /**
   * Get baseKey
   * @return baseKey
  **/
  @JsonProperty("baseKey")
  @ApiModelProperty(value = "")
  public String getBaseKey() {
    return baseKey;
  }

  public void setBaseKey(String baseKey) {
    this.baseKey = baseKey;
  }

  public TbaUpdateJsonKey createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaUpdateJsonKey createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaUpdateJsonKey id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaUpdateJsonKey jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public TbaUpdateJsonKey parNm(String parNm) {
    this.parNm = parNm;
    return this;
  }

   /**
   * Get parNm
   * @return parNm
  **/
  @JsonProperty("parNm")
  @ApiModelProperty(value = "")
  public String getParNm() {
    return parNm;
  }

  public void setParNm(String parNm) {
    this.parNm = parNm;
  }

  public TbaUpdateJsonKey subKey(String subKey) {
    this.subKey = subKey;
    return this;
  }

   /**
   * Get subKey
   * @return subKey
  **/
  @JsonProperty("subKey")
  @ApiModelProperty(value = "")
  public String getSubKey() {
    return subKey;
  }

  public void setSubKey(String subKey) {
    this.subKey = subKey;
  }

  public TbaUpdateJsonKey tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaUpdateJsonKey updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaUpdateJsonKey updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaUpdateJsonKey tbaUpdateJsonKey = (TbaUpdateJsonKey) o;
    return Objects.equals(this.baseKey, tbaUpdateJsonKey.baseKey) &&
        Objects.equals(this.createdBy, tbaUpdateJsonKey.createdBy) &&
        Objects.equals(this.createdDate, tbaUpdateJsonKey.createdDate) &&
        Objects.equals(this.id, tbaUpdateJsonKey.id) &&
        Objects.equals(this.jsonKey, tbaUpdateJsonKey.jsonKey) &&
        Objects.equals(this.parNm, tbaUpdateJsonKey.parNm) &&
        Objects.equals(this.subKey, tbaUpdateJsonKey.subKey) &&
        Objects.equals(this.tbaFieldName, tbaUpdateJsonKey.tbaFieldName) &&
        Objects.equals(this.updatedBy, tbaUpdateJsonKey.updatedBy) &&
        Objects.equals(this.updatedDate, tbaUpdateJsonKey.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(baseKey, createdBy, createdDate, id, jsonKey, parNm, subKey, tbaFieldName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaUpdateJsonKey {\n");
    
    sb.append("    baseKey: ").append(toIndentedString(baseKey)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    parNm: ").append(toIndentedString(parNm)).append("\n");
    sb.append("    subKey: ").append(toIndentedString(subKey)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

