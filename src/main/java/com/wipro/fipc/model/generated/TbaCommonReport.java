/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.TbaSubTransMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * TbaCommonReport
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaCommonReport   {
  @JsonProperty("fieldType")
  private String fieldType = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("subMetaData")
  private String subMetaData = null;

  @JsonProperty("subMetaDataId")
  private String subMetaDataId = null;

  @JsonProperty("fileNameWoutSpace")
  private String fileNameWoutSpace = null;

  @JsonProperty("fileType")
  private String fileType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("mfFieldName")
  private String mfFieldName = null;

  @JsonProperty("mfFieldWoutSpace")
  private String mfFieldWoutSpace = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("sheetName")
  private String sheetName = null;

  @JsonProperty("sheetNameWoutSpace")
  private String sheetNameWoutSpace = null;

  @JsonProperty("fileFormatType")
  private String fileFormatType = null;

  @JsonProperty("panelId")
  private Integer panelId = null;

  @JsonProperty("transId")
  private String transId = null;

  @JsonProperty("tbaSubTransMapping")
  private List<TbaSubTransMapping> tbaSubTransMapping = new ArrayList<TbaSubTransMapping>();

  public TbaCommonReport fieldType(String fieldType) {
    this.fieldType = fieldType;
    return this;
  }

   /**
   * Get fieldType
   * @return fieldType
  **/
  @JsonProperty("fieldType")
  @ApiModelProperty(value = "")
  public String getFieldType() {
    return fieldType;
  }

  public void setFieldType(String fieldType) {
    this.fieldType = fieldType;
  }

  public TbaCommonReport fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public TbaCommonReport subMetaData(String subMetaData) {
    this.subMetaData = subMetaData;
    return this;
  }

   /**
   * Get subMetaData
   * @return subMetaData
  **/
  @JsonProperty("subMetaData")
  @ApiModelProperty(value = "")
  public String getSubMetaData() {
    return subMetaData;
  }

  public void setSubMetaData(String subMetaData) {
    this.subMetaData = subMetaData;
  }

  public TbaCommonReport subMetaDataId(String subMetaDataId) {
    this.subMetaDataId = subMetaDataId;
    return this;
  }

   /**
   * Get subMetaDataId
   * @return subMetaDataId
  **/
  @JsonProperty("subMetaDataId")
  @ApiModelProperty(value = "")
  public String getSubMetaDataId() {
    return subMetaDataId;
  }

  public void setSubMetaDataId(String subMetaDataId) {
    this.subMetaDataId = subMetaDataId;
  }

  public TbaCommonReport fileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
    return this;
  }

   /**
   * Get fileNameWoutSpace
   * @return fileNameWoutSpace
  **/
  @JsonProperty("fileNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getFileNameWoutSpace() {
    return fileNameWoutSpace;
  }

  public void setFileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
  }

  public TbaCommonReport fileType(String fileType) {
    this.fileType = fileType;
    return this;
  }

   /**
   * Get fileType
   * @return fileType
  **/
  @JsonProperty("fileType")
  @ApiModelProperty(value = "")
  public String getFileType() {
    return fileType;
  }

  public void setFileType(String fileType) {
    this.fileType = fileType;
  }

  public TbaCommonReport id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaCommonReport mfFieldName(String mfFieldName) {
    this.mfFieldName = mfFieldName;
    return this;
  }

   /**
   * Get mfFieldName
   * @return mfFieldName
  **/
  @JsonProperty("mfFieldName")
  @ApiModelProperty(value = "")
  public String getMfFieldName() {
    return mfFieldName;
  }

  public void setMfFieldName(String mfFieldName) {
    this.mfFieldName = mfFieldName;
  }

  public TbaCommonReport mfFieldWoutSpace(String mfFieldWoutSpace) {
    this.mfFieldWoutSpace = mfFieldWoutSpace;
    return this;
  }

   /**
   * Get mfFieldWoutSpace
   * @return mfFieldWoutSpace
  **/
  @JsonProperty("mfFieldWoutSpace")
  @ApiModelProperty(value = "")
  public String getMfFieldWoutSpace() {
    return mfFieldWoutSpace;
  }

  public void setMfFieldWoutSpace(String mfFieldWoutSpace) {
    this.mfFieldWoutSpace = mfFieldWoutSpace;
  }

  public TbaCommonReport processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public TbaCommonReport recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public TbaCommonReport sheetName(String sheetName) {
    this.sheetName = sheetName;
    return this;
  }

   /**
   * Get sheetName
   * @return sheetName
  **/
  @JsonProperty("sheetName")
  @ApiModelProperty(value = "")
  public String getSheetName() {
    return sheetName;
  }

  public void setSheetName(String sheetName) {
    this.sheetName = sheetName;
  }

  public TbaCommonReport sheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
    return this;
  }

   /**
   * Get sheetNameWoutSpace
   * @return sheetNameWoutSpace
  **/
  @JsonProperty("sheetNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getSheetNameWoutSpace() {
    return sheetNameWoutSpace;
  }

  public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
  }

  public TbaCommonReport fileFormatType(String fileFormatType) {
    this.fileFormatType = fileFormatType;
    return this;
  }

   /**
   * Get fileFormatType
   * @return fileFormatType
  **/
  @JsonProperty("fileFormatType")
  @ApiModelProperty(value = "")
  public String getFileFormatType() {
    return fileFormatType;
  }

  public void setFileFormatType(String fileFormatType) {
    this.fileFormatType = fileFormatType;
  }

  public TbaCommonReport panelId(Integer panelId) {
    this.panelId = panelId;
    return this;
  }

   /**
   * Get panelId
   * @return panelId
  **/
  @JsonProperty("panelId")
  @ApiModelProperty(value = "")
  public Integer getPanelId() {
    return panelId;
  }

  public void setPanelId(Integer panelId) {
    this.panelId = panelId;
  }

  public TbaCommonReport transId(String transId) {
    this.transId = transId;
    return this;
  }

   /**
   * Get transId
   * @return transId
  **/
  @JsonProperty("transId")
  @ApiModelProperty(value = "")
  public String getTransId() {
    return transId;
  }

  public void setTransId(String transId) {
    this.transId = transId;
  }

  public TbaCommonReport tbaSubTransMapping(List<TbaSubTransMapping> tbaSubTransMapping) {
    this.tbaSubTransMapping = tbaSubTransMapping;
    return this;
  }

  public TbaCommonReport addTbaSubTransMappingItem(TbaSubTransMapping tbaSubTransMappingItem) {
    this.tbaSubTransMapping.add(tbaSubTransMappingItem);
    return this;
  }

   /**
   * Get tbaSubTransMapping
   * @return tbaSubTransMapping
  **/
  @JsonProperty("tbaSubTransMapping")
  @ApiModelProperty(value = "")
  public List<TbaSubTransMapping> getTbaSubTransMapping() {
    return tbaSubTransMapping;
  }

  public void setTbaSubTransMapping(List<TbaSubTransMapping> tbaSubTransMapping) {
    this.tbaSubTransMapping = tbaSubTransMapping;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaCommonReport tbaCommonReport = (TbaCommonReport) o;
    return Objects.equals(this.fieldType, tbaCommonReport.fieldType) &&
        Objects.equals(this.fileName, tbaCommonReport.fileName) &&
        Objects.equals(this.subMetaData, tbaCommonReport.subMetaData) &&
        Objects.equals(this.subMetaDataId, tbaCommonReport.subMetaDataId) &&
        Objects.equals(this.fileNameWoutSpace, tbaCommonReport.fileNameWoutSpace) &&
        Objects.equals(this.fileType, tbaCommonReport.fileType) &&
        Objects.equals(this.id, tbaCommonReport.id) &&
        Objects.equals(this.mfFieldName, tbaCommonReport.mfFieldName) &&
        Objects.equals(this.mfFieldWoutSpace, tbaCommonReport.mfFieldWoutSpace) &&
        Objects.equals(this.processJobMappingId, tbaCommonReport.processJobMappingId) &&
        Objects.equals(this.recordIdentifier, tbaCommonReport.recordIdentifier) &&
        Objects.equals(this.sheetName, tbaCommonReport.sheetName) &&
        Objects.equals(this.sheetNameWoutSpace, tbaCommonReport.sheetNameWoutSpace) &&
        Objects.equals(this.fileFormatType, tbaCommonReport.fileFormatType) &&
        Objects.equals(this.panelId, tbaCommonReport.panelId) &&
        Objects.equals(this.transId, tbaCommonReport.transId) &&
        Objects.equals(this.tbaSubTransMapping, tbaCommonReport.tbaSubTransMapping);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fieldType, fileName, subMetaData, subMetaDataId, fileNameWoutSpace, fileType, id, mfFieldName, mfFieldWoutSpace, processJobMappingId, recordIdentifier, sheetName, sheetNameWoutSpace, fileFormatType, panelId, transId, tbaSubTransMapping);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaCommonReport {\n");
    
    sb.append("    fieldType: ").append(toIndentedString(fieldType)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    subMetaData: ").append(toIndentedString(subMetaData)).append("\n");
    sb.append("    subMetaDataId: ").append(toIndentedString(subMetaDataId)).append("\n");
    sb.append("    fileNameWoutSpace: ").append(toIndentedString(fileNameWoutSpace)).append("\n");
    sb.append("    fileType: ").append(toIndentedString(fileType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    mfFieldName: ").append(toIndentedString(mfFieldName)).append("\n");
    sb.append("    mfFieldWoutSpace: ").append(toIndentedString(mfFieldWoutSpace)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    sheetName: ").append(toIndentedString(sheetName)).append("\n");
    sb.append("    sheetNameWoutSpace: ").append(toIndentedString(sheetNameWoutSpace)).append("\n");
    sb.append("    fileFormatType: ").append(toIndentedString(fileFormatType)).append("\n");
    sb.append("    panelId: ").append(toIndentedString(panelId)).append("\n");
    sb.append("    transId: ").append(toIndentedString(transId)).append("\n");
    sb.append("    tbaSubTransMapping: ").append(toIndentedString(tbaSubTransMapping)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

