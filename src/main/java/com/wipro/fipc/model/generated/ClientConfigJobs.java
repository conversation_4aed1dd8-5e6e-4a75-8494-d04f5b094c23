/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.KsdConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ClientConfigJobs
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ClientConfigJobs   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("childJobName")
  private String childJobName = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("ksdConfig")
  private KsdConfig ksdConfig = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public ClientConfigJobs activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ClientConfigJobs childJobName(String childJobName) {
    this.childJobName = childJobName;
    return this;
  }

   /**
   * Get childJobName
   * @return childJobName
  **/
  @JsonProperty("childJobName")
  @ApiModelProperty(value = "")
  public String getChildJobName() {
    return childJobName;
  }

  public void setChildJobName(String childJobName) {
    this.childJobName = childJobName;
  }

  public ClientConfigJobs createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ClientConfigJobs createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ClientConfigJobs id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ClientConfigJobs ksdConfig(KsdConfig ksdConfig) {
    this.ksdConfig = ksdConfig;
    return this;
  }

   /**
   * Get ksdConfig
   * @return ksdConfig
  **/
  @JsonProperty("ksdConfig")
  @ApiModelProperty(value = "")
  public KsdConfig getKsdConfig() {
    return ksdConfig;
  }

  public void setKsdConfig(KsdConfig ksdConfig) {
    this.ksdConfig = ksdConfig;
  }

  public ClientConfigJobs processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public ClientConfigJobs updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ClientConfigJobs updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ClientConfigJobs clientConfigJobs = (ClientConfigJobs) o;
    return Objects.equals(this.activeFlag, clientConfigJobs.activeFlag) &&
        Objects.equals(this.childJobName, clientConfigJobs.childJobName) &&
        Objects.equals(this.createdBy, clientConfigJobs.createdBy) &&
        Objects.equals(this.createdDate, clientConfigJobs.createdDate) &&
        Objects.equals(this.id, clientConfigJobs.id) &&
        Objects.equals(this.ksdConfig, clientConfigJobs.ksdConfig) &&
        Objects.equals(this.processJobMappingId, clientConfigJobs.processJobMappingId) &&
        Objects.equals(this.updatedBy, clientConfigJobs.updatedBy) &&
        Objects.equals(this.updatedDate, clientConfigJobs.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, childJobName, createdBy, createdDate, id, ksdConfig, processJobMappingId, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ClientConfigJobs {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    childJobName: ").append(toIndentedString(childJobName)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    ksdConfig: ").append(toIndentedString(ksdConfig)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

