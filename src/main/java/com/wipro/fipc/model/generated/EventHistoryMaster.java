/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * EventHistoryMaster
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class EventHistoryMaster   {
  @JsonProperty("actLongDesc")
  private String actLongDesc = null;

  @JsonProperty("activityId")
  private Integer activityId = null;

  @JsonProperty("clientId")
  private Integer clientId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eventName")
  private String eventName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("practiceAreaCode")
  private String practiceAreaCode = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public EventHistoryMaster actLongDesc(String actLongDesc) {
    this.actLongDesc = actLongDesc;
    return this;
  }

   /**
   * Get actLongDesc
   * @return actLongDesc
  **/
  @JsonProperty("actLongDesc")
  @ApiModelProperty(value = "")
  public String getActLongDesc() {
    return actLongDesc;
  }

  public void setActLongDesc(String actLongDesc) {
    this.actLongDesc = actLongDesc;
  }

  public EventHistoryMaster activityId(Integer activityId) {
    this.activityId = activityId;
    return this;
  }

   /**
   * Get activityId
   * @return activityId
  **/
  @JsonProperty("activityId")
  @ApiModelProperty(value = "")
  public Integer getActivityId() {
    return activityId;
  }

  public void setActivityId(Integer activityId) {
    this.activityId = activityId;
  }

  public EventHistoryMaster clientId(Integer clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Integer getClientId() {
    return clientId;
  }

  public void setClientId(Integer clientId) {
    this.clientId = clientId;
  }

  public EventHistoryMaster createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public EventHistoryMaster createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public EventHistoryMaster eventName(String eventName) {
    this.eventName = eventName;
    return this;
  }

   /**
   * Get eventName
   * @return eventName
  **/
  @JsonProperty("eventName")
  @ApiModelProperty(value = "")
  public String getEventName() {
    return eventName;
  }

  public void setEventName(String eventName) {
    this.eventName = eventName;
  }

  public EventHistoryMaster id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public EventHistoryMaster practiceAreaCode(String practiceAreaCode) {
    this.practiceAreaCode = practiceAreaCode;
    return this;
  }

   /**
   * Get practiceAreaCode
   * @return practiceAreaCode
  **/
  @JsonProperty("practiceAreaCode")
  @ApiModelProperty(value = "")
  public String getPracticeAreaCode() {
    return practiceAreaCode;
  }

  public void setPracticeAreaCode(String practiceAreaCode) {
    this.practiceAreaCode = practiceAreaCode;
  }

  public EventHistoryMaster updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public EventHistoryMaster updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EventHistoryMaster eventHistoryMaster = (EventHistoryMaster) o;
    return Objects.equals(this.actLongDesc, eventHistoryMaster.actLongDesc) &&
        Objects.equals(this.activityId, eventHistoryMaster.activityId) &&
        Objects.equals(this.clientId, eventHistoryMaster.clientId) &&
        Objects.equals(this.createdBy, eventHistoryMaster.createdBy) &&
        Objects.equals(this.createdDate, eventHistoryMaster.createdDate) &&
        Objects.equals(this.eventName, eventHistoryMaster.eventName) &&
        Objects.equals(this.id, eventHistoryMaster.id) &&
        Objects.equals(this.practiceAreaCode, eventHistoryMaster.practiceAreaCode) &&
        Objects.equals(this.updatedBy, eventHistoryMaster.updatedBy) &&
        Objects.equals(this.updatedDate, eventHistoryMaster.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actLongDesc, activityId, clientId, createdBy, createdDate, eventName, id, practiceAreaCode, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EventHistoryMaster {\n");
    
    sb.append("    actLongDesc: ").append(toIndentedString(actLongDesc)).append("\n");
    sb.append("    activityId: ").append(toIndentedString(activityId)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eventName: ").append(toIndentedString(eventName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    practiceAreaCode: ").append(toIndentedString(practiceAreaCode)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

