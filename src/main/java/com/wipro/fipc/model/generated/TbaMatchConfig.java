/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaMatchConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaMatchConfig   {
  @JsonProperty("actions")
  private String actions = null;

  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("correctiveAction")
  private String correctiveAction = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("fileNameDest")
  private String fileNameDest = null;

  @JsonProperty("fileNameDestWoutSpace")
  private String fileNameDestWoutSpace = null;

  @JsonProperty("fileNameWoutSpace")
  private String fileNameWoutSpace = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("inquiryDefName")
  private String inquiryDefName = null;

  @JsonProperty("matchType")
  private String matchType = null;

  @JsonProperty("mfFieldName")
  private String mfFieldName = null;

  @JsonProperty("mfFieldNameDest")
  private String mfFieldNameDest = null;

  @JsonProperty("mfFieldWoutSpace")
  private String mfFieldWoutSpace = null;

  @JsonProperty("mfFieldWoutSpaceDest")
  private String mfFieldWoutSpaceDest = null;

  @JsonProperty("pptVerifyTba")
  private String pptVerifyTba = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("reportIdentifier")
  private String reportIdentifier = null;

  @JsonProperty("reportIdentifierDest")
  private String reportIdentifierDest = null;

  @JsonProperty("resultField")
  private String resultField = null;

  @JsonProperty("ruleName")
  private String ruleName = null;

  @JsonProperty("sheetName")
  private String sheetName = null;

  @JsonProperty("sheetNameDest")
  private String sheetNameDest = null;

  @JsonProperty("sheetNameDestWoutSpace")
  private String sheetNameDestWoutSpace = null;

  @JsonProperty("sheetNameWoutSpace")
  private String sheetNameWoutSpace = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaMatchConfig actions(String actions) {
    this.actions = actions;
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  @JsonProperty("actions")
  @ApiModelProperty(value = "")
  public String getActions() {
    return actions;
  }

  public void setActions(String actions) {
    this.actions = actions;
  }

  public TbaMatchConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TbaMatchConfig correctiveAction(String correctiveAction) {
    this.correctiveAction = correctiveAction;
    return this;
  }

   /**
   * Get correctiveAction
   * @return correctiveAction
  **/
  @JsonProperty("correctiveAction")
  @ApiModelProperty(value = "")
  public String getCorrectiveAction() {
    return correctiveAction;
  }

  public void setCorrectiveAction(String correctiveAction) {
    this.correctiveAction = correctiveAction;
  }

  public TbaMatchConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaMatchConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaMatchConfig fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public TbaMatchConfig fileNameDest(String fileNameDest) {
    this.fileNameDest = fileNameDest;
    return this;
  }

   /**
   * Get fileNameDest
   * @return fileNameDest
  **/
  @JsonProperty("fileNameDest")
  @ApiModelProperty(value = "")
  public String getFileNameDest() {
    return fileNameDest;
  }

  public void setFileNameDest(String fileNameDest) {
    this.fileNameDest = fileNameDest;
  }

  public TbaMatchConfig fileNameDestWoutSpace(String fileNameDestWoutSpace) {
    this.fileNameDestWoutSpace = fileNameDestWoutSpace;
    return this;
  }

   /**
   * Get fileNameDestWoutSpace
   * @return fileNameDestWoutSpace
  **/
  @JsonProperty("fileNameDestWoutSpace")
  @ApiModelProperty(value = "")
  public String getFileNameDestWoutSpace() {
    return fileNameDestWoutSpace;
  }

  public void setFileNameDestWoutSpace(String fileNameDestWoutSpace) {
    this.fileNameDestWoutSpace = fileNameDestWoutSpace;
  }

  public TbaMatchConfig fileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
    return this;
  }

   /**
   * Get fileNameWoutSpace
   * @return fileNameWoutSpace
  **/
  @JsonProperty("fileNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getFileNameWoutSpace() {
    return fileNameWoutSpace;
  }

  public void setFileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
  }

  public TbaMatchConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaMatchConfig identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public TbaMatchConfig inquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
    return this;
  }

   /**
   * Get inquiryDefName
   * @return inquiryDefName
  **/
  @JsonProperty("inquiryDefName")
  @ApiModelProperty(value = "")
  public String getInquiryDefName() {
    return inquiryDefName;
  }

  public void setInquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
  }

  public TbaMatchConfig matchType(String matchType) {
    this.matchType = matchType;
    return this;
  }

   /**
   * Get matchType
   * @return matchType
  **/
  @JsonProperty("matchType")
  @ApiModelProperty(value = "")
  public String getMatchType() {
    return matchType;
  }

  public void setMatchType(String matchType) {
    this.matchType = matchType;
  }

  public TbaMatchConfig mfFieldName(String mfFieldName) {
    this.mfFieldName = mfFieldName;
    return this;
  }

   /**
   * Get mfFieldName
   * @return mfFieldName
  **/
  @JsonProperty("mfFieldName")
  @ApiModelProperty(value = "")
  public String getMfFieldName() {
    return mfFieldName;
  }

  public void setMfFieldName(String mfFieldName) {
    this.mfFieldName = mfFieldName;
  }

  public TbaMatchConfig mfFieldNameDest(String mfFieldNameDest) {
    this.mfFieldNameDest = mfFieldNameDest;
    return this;
  }

   /**
   * Get mfFieldNameDest
   * @return mfFieldNameDest
  **/
  @JsonProperty("mfFieldNameDest")
  @ApiModelProperty(value = "")
  public String getMfFieldNameDest() {
    return mfFieldNameDest;
  }

  public void setMfFieldNameDest(String mfFieldNameDest) {
    this.mfFieldNameDest = mfFieldNameDest;
  }

  public TbaMatchConfig mfFieldWoutSpace(String mfFieldWoutSpace) {
    this.mfFieldWoutSpace = mfFieldWoutSpace;
    return this;
  }

   /**
   * Get mfFieldWoutSpace
   * @return mfFieldWoutSpace
  **/
  @JsonProperty("mfFieldWoutSpace")
  @ApiModelProperty(value = "")
  public String getMfFieldWoutSpace() {
    return mfFieldWoutSpace;
  }

  public void setMfFieldWoutSpace(String mfFieldWoutSpace) {
    this.mfFieldWoutSpace = mfFieldWoutSpace;
  }

  public TbaMatchConfig mfFieldWoutSpaceDest(String mfFieldWoutSpaceDest) {
    this.mfFieldWoutSpaceDest = mfFieldWoutSpaceDest;
    return this;
  }

   /**
   * Get mfFieldWoutSpaceDest
   * @return mfFieldWoutSpaceDest
  **/
  @JsonProperty("mfFieldWoutSpaceDest")
  @ApiModelProperty(value = "")
  public String getMfFieldWoutSpaceDest() {
    return mfFieldWoutSpaceDest;
  }

  public void setMfFieldWoutSpaceDest(String mfFieldWoutSpaceDest) {
    this.mfFieldWoutSpaceDest = mfFieldWoutSpaceDest;
  }

  public TbaMatchConfig pptVerifyTba(String pptVerifyTba) {
    this.pptVerifyTba = pptVerifyTba;
    return this;
  }

   /**
   * Get pptVerifyTba
   * @return pptVerifyTba
  **/
  @JsonProperty("pptVerifyTba")
  @ApiModelProperty(value = "")
  public String getPptVerifyTba() {
    return pptVerifyTba;
  }

  public void setPptVerifyTba(String pptVerifyTba) {
    this.pptVerifyTba = pptVerifyTba;
  }

  public TbaMatchConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TbaMatchConfig reportIdentifier(String reportIdentifier) {
    this.reportIdentifier = reportIdentifier;
    return this;
  }

   /**
   * Get reportIdentifier
   * @return reportIdentifier
  **/
  @JsonProperty("reportIdentifier")
  @ApiModelProperty(value = "")
  public String getReportIdentifier() {
    return reportIdentifier;
  }

  public void setReportIdentifier(String reportIdentifier) {
    this.reportIdentifier = reportIdentifier;
  }

  public TbaMatchConfig reportIdentifierDest(String reportIdentifierDest) {
    this.reportIdentifierDest = reportIdentifierDest;
    return this;
  }

   /**
   * Get reportIdentifierDest
   * @return reportIdentifierDest
  **/
  @JsonProperty("reportIdentifierDest")
  @ApiModelProperty(value = "")
  public String getReportIdentifierDest() {
    return reportIdentifierDest;
  }

  public void setReportIdentifierDest(String reportIdentifierDest) {
    this.reportIdentifierDest = reportIdentifierDest;
  }

  public TbaMatchConfig resultField(String resultField) {
    this.resultField = resultField;
    return this;
  }

   /**
   * Get resultField
   * @return resultField
  **/
  @JsonProperty("resultField")
  @ApiModelProperty(value = "")
  public String getResultField() {
    return resultField;
  }

  public void setResultField(String resultField) {
    this.resultField = resultField;
  }

  public TbaMatchConfig ruleName(String ruleName) {
    this.ruleName = ruleName;
    return this;
  }

   /**
   * Get ruleName
   * @return ruleName
  **/
  @JsonProperty("ruleName")
  @ApiModelProperty(value = "")
  public String getRuleName() {
    return ruleName;
  }

  public void setRuleName(String ruleName) {
    this.ruleName = ruleName;
  }

  public TbaMatchConfig sheetName(String sheetName) {
    this.sheetName = sheetName;
    return this;
  }

   /**
   * Get sheetName
   * @return sheetName
  **/
  @JsonProperty("sheetName")
  @ApiModelProperty(value = "")
  public String getSheetName() {
    return sheetName;
  }

  public void setSheetName(String sheetName) {
    this.sheetName = sheetName;
  }

  public TbaMatchConfig sheetNameDest(String sheetNameDest) {
    this.sheetNameDest = sheetNameDest;
    return this;
  }

   /**
   * Get sheetNameDest
   * @return sheetNameDest
  **/
  @JsonProperty("sheetNameDest")
  @ApiModelProperty(value = "")
  public String getSheetNameDest() {
    return sheetNameDest;
  }

  public void setSheetNameDest(String sheetNameDest) {
    this.sheetNameDest = sheetNameDest;
  }

  public TbaMatchConfig sheetNameDestWoutSpace(String sheetNameDestWoutSpace) {
    this.sheetNameDestWoutSpace = sheetNameDestWoutSpace;
    return this;
  }

   /**
   * Get sheetNameDestWoutSpace
   * @return sheetNameDestWoutSpace
  **/
  @JsonProperty("sheetNameDestWoutSpace")
  @ApiModelProperty(value = "")
  public String getSheetNameDestWoutSpace() {
    return sheetNameDestWoutSpace;
  }

  public void setSheetNameDestWoutSpace(String sheetNameDestWoutSpace) {
    this.sheetNameDestWoutSpace = sheetNameDestWoutSpace;
  }

  public TbaMatchConfig sheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
    return this;
  }

   /**
   * Get sheetNameWoutSpace
   * @return sheetNameWoutSpace
  **/
  @JsonProperty("sheetNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getSheetNameWoutSpace() {
    return sheetNameWoutSpace;
  }

  public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
  }

  public TbaMatchConfig tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaMatchConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaMatchConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaMatchConfig tbaMatchConfig = (TbaMatchConfig) o;
    return Objects.equals(this.actions, tbaMatchConfig.actions) &&
        Objects.equals(this.activeFlag, tbaMatchConfig.activeFlag) &&
        Objects.equals(this.correctiveAction, tbaMatchConfig.correctiveAction) &&
        Objects.equals(this.createdBy, tbaMatchConfig.createdBy) &&
        Objects.equals(this.createdDate, tbaMatchConfig.createdDate) &&
        Objects.equals(this.fileName, tbaMatchConfig.fileName) &&
        Objects.equals(this.fileNameDest, tbaMatchConfig.fileNameDest) &&
        Objects.equals(this.fileNameDestWoutSpace, tbaMatchConfig.fileNameDestWoutSpace) &&
        Objects.equals(this.fileNameWoutSpace, tbaMatchConfig.fileNameWoutSpace) &&
        Objects.equals(this.id, tbaMatchConfig.id) &&
        Objects.equals(this.identifier, tbaMatchConfig.identifier) &&
        Objects.equals(this.inquiryDefName, tbaMatchConfig.inquiryDefName) &&
        Objects.equals(this.matchType, tbaMatchConfig.matchType) &&
        Objects.equals(this.mfFieldName, tbaMatchConfig.mfFieldName) &&
        Objects.equals(this.mfFieldNameDest, tbaMatchConfig.mfFieldNameDest) &&
        Objects.equals(this.mfFieldWoutSpace, tbaMatchConfig.mfFieldWoutSpace) &&
        Objects.equals(this.mfFieldWoutSpaceDest, tbaMatchConfig.mfFieldWoutSpaceDest) &&
        Objects.equals(this.pptVerifyTba, tbaMatchConfig.pptVerifyTba) &&
        Objects.equals(this.processJobMapping, tbaMatchConfig.processJobMapping) &&
        Objects.equals(this.reportIdentifier, tbaMatchConfig.reportIdentifier) &&
        Objects.equals(this.reportIdentifierDest, tbaMatchConfig.reportIdentifierDest) &&
        Objects.equals(this.resultField, tbaMatchConfig.resultField) &&
        Objects.equals(this.ruleName, tbaMatchConfig.ruleName) &&
        Objects.equals(this.sheetName, tbaMatchConfig.sheetName) &&
        Objects.equals(this.sheetNameDest, tbaMatchConfig.sheetNameDest) &&
        Objects.equals(this.sheetNameDestWoutSpace, tbaMatchConfig.sheetNameDestWoutSpace) &&
        Objects.equals(this.sheetNameWoutSpace, tbaMatchConfig.sheetNameWoutSpace) &&
        Objects.equals(this.tbaFieldName, tbaMatchConfig.tbaFieldName) &&
        Objects.equals(this.updatedBy, tbaMatchConfig.updatedBy) &&
        Objects.equals(this.updatedDate, tbaMatchConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, activeFlag, correctiveAction, createdBy, createdDate, fileName, fileNameDest, fileNameDestWoutSpace, fileNameWoutSpace, id, identifier, inquiryDefName, matchType, mfFieldName, mfFieldNameDest, mfFieldWoutSpace, mfFieldWoutSpaceDest, pptVerifyTba, processJobMapping, reportIdentifier, reportIdentifierDest, resultField, ruleName, sheetName, sheetNameDest, sheetNameDestWoutSpace, sheetNameWoutSpace, tbaFieldName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaMatchConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    correctiveAction: ").append(toIndentedString(correctiveAction)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    fileNameDest: ").append(toIndentedString(fileNameDest)).append("\n");
    sb.append("    fileNameDestWoutSpace: ").append(toIndentedString(fileNameDestWoutSpace)).append("\n");
    sb.append("    fileNameWoutSpace: ").append(toIndentedString(fileNameWoutSpace)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    inquiryDefName: ").append(toIndentedString(inquiryDefName)).append("\n");
    sb.append("    matchType: ").append(toIndentedString(matchType)).append("\n");
    sb.append("    mfFieldName: ").append(toIndentedString(mfFieldName)).append("\n");
    sb.append("    mfFieldNameDest: ").append(toIndentedString(mfFieldNameDest)).append("\n");
    sb.append("    mfFieldWoutSpace: ").append(toIndentedString(mfFieldWoutSpace)).append("\n");
    sb.append("    mfFieldWoutSpaceDest: ").append(toIndentedString(mfFieldWoutSpaceDest)).append("\n");
    sb.append("    pptVerifyTba: ").append(toIndentedString(pptVerifyTba)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    reportIdentifier: ").append(toIndentedString(reportIdentifier)).append("\n");
    sb.append("    reportIdentifierDest: ").append(toIndentedString(reportIdentifierDest)).append("\n");
    sb.append("    resultField: ").append(toIndentedString(resultField)).append("\n");
    sb.append("    ruleName: ").append(toIndentedString(ruleName)).append("\n");
    sb.append("    sheetName: ").append(toIndentedString(sheetName)).append("\n");
    sb.append("    sheetNameDest: ").append(toIndentedString(sheetNameDest)).append("\n");
    sb.append("    sheetNameDestWoutSpace: ").append(toIndentedString(sheetNameDestWoutSpace)).append("\n");
    sb.append("    sheetNameWoutSpace: ").append(toIndentedString(sheetNameWoutSpace)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

