/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.*;

/**
 * DroolFileDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class DroolFileDetails   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("droolContent")
  private List<byte[]> droolContent = new ArrayList<byte[]>();

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("keyName")
  private String keyName = null;

  @JsonProperty("pjmId")
  private Integer pjmId = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public DroolFileDetails activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public DroolFileDetails createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public DroolFileDetails createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public DroolFileDetails droolContent(List<byte[]> droolContent) {
    this.droolContent = droolContent;
    return this;
  }

  public DroolFileDetails addDroolContentItem(byte[] droolContentItem) {
    this.droolContent.add(droolContentItem);
    return this;
  }

   /**
   * Get droolContent
   * @return droolContent
  **/
  @JsonProperty("droolContent")
  @ApiModelProperty(value = "")
  public List<byte[]> getDroolContent() {
    return droolContent;
  }

  public void setDroolContent(List<byte[]> droolContent) {
    this.droolContent = droolContent;
  }

  public DroolFileDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public DroolFileDetails keyName(String keyName) {
    this.keyName = keyName;
    return this;
  }

   /**
   * Get keyName
   * @return keyName
  **/
  @JsonProperty("keyName")
  @ApiModelProperty(value = "")
  public String getKeyName() {
    return keyName;
  }

  public void setKeyName(String keyName) {
    this.keyName = keyName;
  }

  public DroolFileDetails pjmId(Integer pjmId) {
    this.pjmId = pjmId;
    return this;
  }

   /**
   * Get pjmId
   * @return pjmId
  **/
  @JsonProperty("pjmId")
  @ApiModelProperty(value = "")
  public Integer getPjmId() {
    return pjmId;
  }

  public void setPjmId(Integer pjmId) {
    this.pjmId = pjmId;
  }

  public DroolFileDetails updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public DroolFileDetails updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DroolFileDetails droolFileDetails = (DroolFileDetails) o;
    return Objects.equals(this.activeFlag, droolFileDetails.activeFlag) &&
        Objects.equals(this.createdBy, droolFileDetails.createdBy) &&
        Objects.equals(this.createdDate, droolFileDetails.createdDate) &&
        Objects.equals(this.droolContent, droolFileDetails.droolContent) &&
        Objects.equals(this.id, droolFileDetails.id) &&
        Objects.equals(this.keyName, droolFileDetails.keyName) &&
        Objects.equals(this.pjmId, droolFileDetails.pjmId) &&
        Objects.equals(this.updatedBy, droolFileDetails.updatedBy) &&
        Objects.equals(this.updatedDate, droolFileDetails.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, createdBy, createdDate, droolContent, id, keyName, pjmId, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DroolFileDetails {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    droolContent: ").append(toIndentedString(droolContent)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    keyName: ").append(toIndentedString(keyName)).append("\n");
    sb.append("    pjmId: ").append(toIndentedString(pjmId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

