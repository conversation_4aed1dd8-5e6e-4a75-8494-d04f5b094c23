/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.*;

/**
 * TbaUpdateConfigDto
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaUpdateConfigDto   {
  @JsonProperty("actLngDesc")
  private String actLngDesc = null;

  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("addManualFlag")
  private String addManualFlag = null;

  @JsonProperty("basicInfo")
  private String basicInfo = null;

  @JsonProperty("clientId")
  private Integer clientId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eventName")
  private String eventName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("metaData")
  private String metaData = null;

  @JsonProperty("overrideEdits")
  private List<String> overrideEdits = new ArrayList<String>();

  @JsonProperty("panelId")
  private Integer panelId = null;

  @JsonProperty("parNm")
  private String parNm = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("rerunFlag")
  private String rerunFlag = null;

  @JsonProperty("sequence")
  private String sequence = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("tbaUpdateAction")
  private String tbaUpdateAction = null;

  @JsonProperty("updateName")
  private String updateName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  public TbaUpdateConfigDto actLngDesc(String actLngDesc) {
    this.actLngDesc = actLngDesc;
    return this;
  }

   /**
   * Get actLngDesc
   * @return actLngDesc
  **/
  @JsonProperty("actLngDesc")
  @ApiModelProperty(value = "")
  public String getActLngDesc() {
    return actLngDesc;
  }

  public void setActLngDesc(String actLngDesc) {
    this.actLngDesc = actLngDesc;
  }

  public TbaUpdateConfigDto activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TbaUpdateConfigDto addManualFlag(String addManualFlag) {
    this.addManualFlag = addManualFlag;
    return this;
  }

   /**
   * Get addManualFlag
   * @return addManualFlag
  **/
  @JsonProperty("addManualFlag")
  @ApiModelProperty(value = "")
  public String getAddManualFlag() {
    return addManualFlag;
  }

  public void setAddManualFlag(String addManualFlag) {
    this.addManualFlag = addManualFlag;
  }

  public TbaUpdateConfigDto basicInfo(String basicInfo) {
    this.basicInfo = basicInfo;
    return this;
  }

   /**
   * Get basicInfo
   * @return basicInfo
  **/
  @JsonProperty("basicInfo")
  @ApiModelProperty(value = "")
  public String getBasicInfo() {
    return basicInfo;
  }

  public void setBasicInfo(String basicInfo) {
    this.basicInfo = basicInfo;
  }

  public TbaUpdateConfigDto clientId(Integer clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Integer getClientId() {
    return clientId;
  }

  public void setClientId(Integer clientId) {
    this.clientId = clientId;
  }

  public TbaUpdateConfigDto createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaUpdateConfigDto createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaUpdateConfigDto eventName(String eventName) {
    this.eventName = eventName;
    return this;
  }

   /**
   * Get eventName
   * @return eventName
  **/
  @JsonProperty("eventName")
  @ApiModelProperty(value = "")
  public String getEventName() {
    return eventName;
  }

  public void setEventName(String eventName) {
    this.eventName = eventName;
  }

  public TbaUpdateConfigDto id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaUpdateConfigDto identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public TbaUpdateConfigDto metaData(String metaData) {
    this.metaData = metaData;
    return this;
  }

   /**
   * Get metaData
   * @return metaData
  **/
  @JsonProperty("metaData")
  @ApiModelProperty(value = "")
  public String getMetaData() {
    return metaData;
  }

  public void setMetaData(String metaData) {
    this.metaData = metaData;
  }

  public TbaUpdateConfigDto overrideEdits(List<String> overrideEdits) {
    this.overrideEdits = overrideEdits;
    return this;
  }

  public TbaUpdateConfigDto addOverrideEditsItem(String overrideEditsItem) {
    this.overrideEdits.add(overrideEditsItem);
    return this;
  }

   /**
   * Get overrideEdits
   * @return overrideEdits
  **/
  @JsonProperty("overrideEdits")
  @ApiModelProperty(value = "")
  public List<String> getOverrideEdits() {
    return overrideEdits;
  }

  public void setOverrideEdits(List<String> overrideEdits) {
    this.overrideEdits = overrideEdits;
  }

  public TbaUpdateConfigDto panelId(Integer panelId) {
    this.panelId = panelId;
    return this;
  }

   /**
   * Get panelId
   * @return panelId
  **/
  @JsonProperty("panelId")
  @ApiModelProperty(value = "")
  public Integer getPanelId() {
    return panelId;
  }

  public void setPanelId(Integer panelId) {
    this.panelId = panelId;
  }

  public TbaUpdateConfigDto parNm(String parNm) {
    this.parNm = parNm;
    return this;
  }

   /**
   * Get parNm
   * @return parNm
  **/
  @JsonProperty("parNm")
  @ApiModelProperty(value = "")
  public String getParNm() {
    return parNm;
  }

  public void setParNm(String parNm) {
    this.parNm = parNm;
  }

  public TbaUpdateConfigDto processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TbaUpdateConfigDto processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public TbaUpdateConfigDto recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public TbaUpdateConfigDto rerunFlag(String rerunFlag) {
    this.rerunFlag = rerunFlag;
    return this;
  }

   /**
   * Get rerunFlag
   * @return rerunFlag
  **/
  @JsonProperty("rerunFlag")
  @ApiModelProperty(value = "")
  public String getRerunFlag() {
    return rerunFlag;
  }

  public void setRerunFlag(String rerunFlag) {
    this.rerunFlag = rerunFlag;
  }

  public TbaUpdateConfigDto sequence(String sequence) {
    this.sequence = sequence;
    return this;
  }

   /**
   * Get sequence
   * @return sequence
  **/
  @JsonProperty("sequence")
  @ApiModelProperty(value = "")
  public String getSequence() {
    return sequence;
  }

  public void setSequence(String sequence) {
    this.sequence = sequence;
  }

  public TbaUpdateConfigDto tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaUpdateConfigDto tbaUpdateAction(String tbaUpdateAction) {
    this.tbaUpdateAction = tbaUpdateAction;
    return this;
  }

   /**
   * Get tbaUpdateAction
   * @return tbaUpdateAction
  **/
  @JsonProperty("tbaUpdateAction")
  @ApiModelProperty(value = "")
  public String getTbaUpdateAction() {
    return tbaUpdateAction;
  }

  public void setTbaUpdateAction(String tbaUpdateAction) {
    this.tbaUpdateAction = tbaUpdateAction;
  }

  public TbaUpdateConfigDto updateName(String updateName) {
    this.updateName = updateName;
    return this;
  }

   /**
   * Get updateName
   * @return updateName
  **/
  @JsonProperty("updateName")
  @ApiModelProperty(value = "")
  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public TbaUpdateConfigDto updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaUpdateConfigDto tbaUpdateConfigDto = (TbaUpdateConfigDto) o;
    return Objects.equals(this.actLngDesc, tbaUpdateConfigDto.actLngDesc) &&
        Objects.equals(this.activeFlag, tbaUpdateConfigDto.activeFlag) &&
        Objects.equals(this.addManualFlag, tbaUpdateConfigDto.addManualFlag) &&
        Objects.equals(this.basicInfo, tbaUpdateConfigDto.basicInfo) &&
        Objects.equals(this.clientId, tbaUpdateConfigDto.clientId) &&
        Objects.equals(this.createdBy, tbaUpdateConfigDto.createdBy) &&
        Objects.equals(this.createdDate, tbaUpdateConfigDto.createdDate) &&
        Objects.equals(this.eventName, tbaUpdateConfigDto.eventName) &&
        Objects.equals(this.id, tbaUpdateConfigDto.id) &&
        Objects.equals(this.identifier, tbaUpdateConfigDto.identifier) &&
        Objects.equals(this.metaData, tbaUpdateConfigDto.metaData) &&
        Objects.equals(this.overrideEdits, tbaUpdateConfigDto.overrideEdits) &&
        Objects.equals(this.panelId, tbaUpdateConfigDto.panelId) &&
        Objects.equals(this.parNm, tbaUpdateConfigDto.parNm) &&
        Objects.equals(this.processJobMapping, tbaUpdateConfigDto.processJobMapping) &&
        Objects.equals(this.processJobMappingId, tbaUpdateConfigDto.processJobMappingId) &&
        Objects.equals(this.recordIdentifier, tbaUpdateConfigDto.recordIdentifier) &&
        Objects.equals(this.rerunFlag, tbaUpdateConfigDto.rerunFlag) &&
        Objects.equals(this.sequence, tbaUpdateConfigDto.sequence) &&
        Objects.equals(this.tbaFieldName, tbaUpdateConfigDto.tbaFieldName) &&
        Objects.equals(this.tbaUpdateAction, tbaUpdateConfigDto.tbaUpdateAction) &&
        Objects.equals(this.updateName, tbaUpdateConfigDto.updateName) &&
        Objects.equals(this.updatedBy, tbaUpdateConfigDto.updatedBy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actLngDesc, activeFlag, addManualFlag, basicInfo, clientId, createdBy, createdDate, eventName, id, identifier, metaData, overrideEdits, panelId, parNm, processJobMapping, processJobMappingId, recordIdentifier, rerunFlag, sequence, tbaFieldName, tbaUpdateAction, updateName, updatedBy);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaUpdateConfigDto {\n");
    
    sb.append("    actLngDesc: ").append(toIndentedString(actLngDesc)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    addManualFlag: ").append(toIndentedString(addManualFlag)).append("\n");
    sb.append("    basicInfo: ").append(toIndentedString(basicInfo)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eventName: ").append(toIndentedString(eventName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    metaData: ").append(toIndentedString(metaData)).append("\n");
    sb.append("    overrideEdits: ").append(toIndentedString(overrideEdits)).append("\n");
    sb.append("    panelId: ").append(toIndentedString(panelId)).append("\n");
    sb.append("    parNm: ").append(toIndentedString(parNm)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    rerunFlag: ").append(toIndentedString(rerunFlag)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    tbaUpdateAction: ").append(toIndentedString(tbaUpdateAction)).append("\n");
    sb.append("    updateName: ").append(toIndentedString(updateName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

