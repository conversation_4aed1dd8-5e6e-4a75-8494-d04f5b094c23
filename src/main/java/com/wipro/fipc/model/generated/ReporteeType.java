/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * ReporteeType
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ReporteeType   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("managerId")
  private String managerId = null;

  @JsonProperty("reporteeId")
  private String reporteeId = null;

  @JsonProperty("reporteeType")
  private String reporteeType = null;

  public ReporteeType activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ReporteeType id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ReporteeType managerId(String managerId) {
    this.managerId = managerId;
    return this;
  }

   /**
   * Get managerId
   * @return managerId
  **/
  @JsonProperty("managerId")
  @ApiModelProperty(value = "")
  public String getManagerId() {
    return managerId;
  }

  public void setManagerId(String managerId) {
    this.managerId = managerId;
  }

  public ReporteeType reporteeId(String reporteeId) {
    this.reporteeId = reporteeId;
    return this;
  }

   /**
   * Get reporteeId
   * @return reporteeId
  **/
  @JsonProperty("reporteeId")
  @ApiModelProperty(value = "")
  public String getReporteeId() {
    return reporteeId;
  }

  public void setReporteeId(String reporteeId) {
    this.reporteeId = reporteeId;
  }

  public ReporteeType reporteeType(String reporteeType) {
    this.reporteeType = reporteeType;
    return this;
  }

   /**
   * Get reporteeType
   * @return reporteeType
  **/
  @JsonProperty("reporteeType")
  @ApiModelProperty(value = "")
  public String getReporteeType() {
    return reporteeType;
  }

  public void setReporteeType(String reporteeType) {
    this.reporteeType = reporteeType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReporteeType reporteeType = (ReporteeType) o;
    return Objects.equals(this.activeFlag, reporteeType.activeFlag) &&
        Objects.equals(this.id, reporteeType.id) &&
        Objects.equals(this.managerId, reporteeType.managerId) &&
        Objects.equals(this.reporteeId, reporteeType.reporteeId) &&
        Objects.equals(this.reporteeType, reporteeType.reporteeType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, id, managerId, reporteeId, reporteeType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReporteeType {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    managerId: ").append(toIndentedString(managerId)).append("\n");
    sb.append("    reporteeId: ").append(toIndentedString(reporteeId)).append("\n");
    sb.append("    reporteeType: ").append(toIndentedString(reporteeType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

