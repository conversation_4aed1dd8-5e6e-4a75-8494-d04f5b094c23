/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.JobSchedule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * SkippedJobsSchedule
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class SkippedJobsSchedule   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobSchedule")
  private JobSchedule jobSchedule = null;

  @JsonProperty("primaryJobName")
  private String primaryJobName = null;

  @JsonProperty("skippedDate")
  private Date skippedDate = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public SkippedJobsSchedule activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public SkippedJobsSchedule createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public SkippedJobsSchedule createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public SkippedJobsSchedule id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public SkippedJobsSchedule jobSchedule(JobSchedule jobSchedule) {
    this.jobSchedule = jobSchedule;
    return this;
  }

   /**
   * Get jobSchedule
   * @return jobSchedule
  **/
  @JsonProperty("jobSchedule")
  @ApiModelProperty(value = "")
  public JobSchedule getJobSchedule() {
    return jobSchedule;
  }

  public void setJobSchedule(JobSchedule jobSchedule) {
    this.jobSchedule = jobSchedule;
  }

  public SkippedJobsSchedule primaryJobName(String primaryJobName) {
    this.primaryJobName = primaryJobName;
    return this;
  }

   /**
   * Get primaryJobName
   * @return primaryJobName
  **/
  @JsonProperty("primaryJobName")
  @ApiModelProperty(value = "")
  public String getPrimaryJobName() {
    return primaryJobName;
  }

  public void setPrimaryJobName(String primaryJobName) {
    this.primaryJobName = primaryJobName;
  }

  public SkippedJobsSchedule skippedDate(Date skippedDate) {
    this.skippedDate = skippedDate;
    return this;
  }

   /**
   * Get skippedDate
   * @return skippedDate
  **/
  @JsonProperty("skippedDate")
  @ApiModelProperty(value = "")
  public Date getSkippedDate() {
    return skippedDate;
  }

  public void setSkippedDate(Date skippedDate) {
    this.skippedDate = skippedDate;
  }

  public SkippedJobsSchedule status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public SkippedJobsSchedule updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public SkippedJobsSchedule updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SkippedJobsSchedule skippedJobsSchedule = (SkippedJobsSchedule) o;
    return Objects.equals(this.activeFlag, skippedJobsSchedule.activeFlag) &&
        Objects.equals(this.createdBy, skippedJobsSchedule.createdBy) &&
        Objects.equals(this.createdDate, skippedJobsSchedule.createdDate) &&
        Objects.equals(this.id, skippedJobsSchedule.id) &&
        Objects.equals(this.jobSchedule, skippedJobsSchedule.jobSchedule) &&
        Objects.equals(this.primaryJobName, skippedJobsSchedule.primaryJobName) &&
        Objects.equals(this.skippedDate, skippedJobsSchedule.skippedDate) &&
        Objects.equals(this.status, skippedJobsSchedule.status) &&
        Objects.equals(this.updatedBy, skippedJobsSchedule.updatedBy) &&
        Objects.equals(this.updatedDate, skippedJobsSchedule.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, createdBy, createdDate, id, jobSchedule, primaryJobName, skippedDate, status, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SkippedJobsSchedule {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobSchedule: ").append(toIndentedString(jobSchedule)).append("\n");
    sb.append("    primaryJobName: ").append(toIndentedString(primaryJobName)).append("\n");
    sb.append("    skippedDate: ").append(toIndentedString(skippedDate)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

