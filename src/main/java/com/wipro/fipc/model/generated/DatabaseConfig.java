/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * DatabaseConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class DatabaseConfig   {
  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("databaseId")
  private String databaseId = null;

  @JsonProperty("id")
  private Long id = null;

  public DatabaseConfig clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public DatabaseConfig clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public DatabaseConfig databaseId(String databaseId) {
    this.databaseId = databaseId;
    return this;
  }

   /**
   * Get databaseId
   * @return databaseId
  **/
  @JsonProperty("databaseId")
  @ApiModelProperty(value = "")
  public String getDatabaseId() {
    return databaseId;
  }

  public void setDatabaseId(String databaseId) {
    this.databaseId = databaseId;
  }

  public DatabaseConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DatabaseConfig databaseConfig = (DatabaseConfig) o;
    return Objects.equals(this.clientCode, databaseConfig.clientCode) &&
        Objects.equals(this.clientName, databaseConfig.clientName) &&
        Objects.equals(this.databaseId, databaseConfig.databaseId) &&
        Objects.equals(this.id, databaseConfig.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(clientCode, clientName, databaseId, id);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DatabaseConfig {\n");
    
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    databaseId: ").append(toIndentedString(databaseId)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

