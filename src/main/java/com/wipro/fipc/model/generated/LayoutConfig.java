/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * LayoutConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2024-05-30T11:26:35.043+05:30")
public class LayoutConfig   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("dateFormat")
  private String dateFormat = null;

  @JsonProperty("fieldNo")
  private Integer fieldNo = null;

  @JsonProperty("fieldTemplate")
  private String fieldTemplate = null;

  @JsonProperty("fieldType")
  private String fieldType = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("fileNameWoutSpace")
  private String fileNameWoutSpace = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("labellingJson")
  private String labellingJson = null;

  @JsonProperty("length")
  private Integer length = null;

  @JsonProperty("mandatory")
  private String mandatory = null;

  @JsonProperty("mfFieldName")
  private String mfFieldName = null;

  @JsonProperty("mfFieldWoutSpace")
  private String mfFieldWoutSpace = null;

  @JsonProperty("pptIdentifier")
  private String pptIdentifier = null;

  @JsonProperty("processJobMappingConfig")
  private ProcessJobMapping processJobMappingConfig = null;

  @JsonProperty("recordFormat")
  private String recordFormat = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("recordIdentifierVal")
  private String recordIdentifierVal = null;

  @JsonProperty("recordType")
  private String recordType = null;

  @JsonProperty("sheetName")
  private String sheetName = null;

  @JsonProperty("sheetNameWoutSpace")
  private String sheetNameWoutSpace = null;

  @JsonProperty("startPos")
  private String startPos = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("valueDetails")
  private String valueDetails = null;

  @JsonProperty("preFilter")
  private String preFilter = null;

  @JsonProperty("preFilterOperator")
  private String preFilterOperator = null;

  @JsonProperty("amountFormat")
  private String amountFormat = null;

  public LayoutConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public LayoutConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public LayoutConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public LayoutConfig dateFormat(String dateFormat) {
    this.dateFormat = dateFormat;
    return this;
  }

   /**
   * Get dateFormat
   * @return dateFormat
  **/
  @JsonProperty("dateFormat")
  @ApiModelProperty(value = "")
  public String getDateFormat() {
    return dateFormat;
  }

  public void setDateFormat(String dateFormat) {
    this.dateFormat = dateFormat;
  }

  public LayoutConfig fieldNo(Integer fieldNo) {
    this.fieldNo = fieldNo;
    return this;
  }

   /**
   * Get fieldNo
   * @return fieldNo
  **/
  @JsonProperty("fieldNo")
  @ApiModelProperty(value = "")
  public Integer getFieldNo() {
    return fieldNo;
  }

  public void setFieldNo(Integer fieldNo) {
    this.fieldNo = fieldNo;
  }

  public LayoutConfig fieldTemplate(String fieldTemplate) {
    this.fieldTemplate = fieldTemplate;
    return this;
  }

   /**
   * Get fieldTemplate
   * @return fieldTemplate
  **/
  @JsonProperty("fieldTemplate")
  @ApiModelProperty(value = "")
  public String getFieldTemplate() {
    return fieldTemplate;
  }

  public void setFieldTemplate(String fieldTemplate) {
    this.fieldTemplate = fieldTemplate;
  }

  public LayoutConfig fieldType(String fieldType) {
    this.fieldType = fieldType;
    return this;
  }

   /**
   * Get fieldType
   * @return fieldType
  **/
  @JsonProperty("fieldType")
  @ApiModelProperty(value = "")
  public String getFieldType() {
    return fieldType;
  }

  public void setFieldType(String fieldType) {
    this.fieldType = fieldType;
  }

  public LayoutConfig fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public LayoutConfig fileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
    return this;
  }

   /**
   * Get fileNameWoutSpace
   * @return fileNameWoutSpace
  **/
  @JsonProperty("fileNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getFileNameWoutSpace() {
    return fileNameWoutSpace;
  }

  public void setFileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
  }

  public LayoutConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public LayoutConfig labellingJson(String labellingJson) {
    this.labellingJson = labellingJson;
    return this;
  }

   /**
   * Get labellingJson
   * @return labellingJson
  **/
  @JsonProperty("labellingJson")
  @ApiModelProperty(value = "")
  public String getLabellingJson() {
    return labellingJson;
  }

  public void setLabellingJson(String labellingJson) {
    this.labellingJson = labellingJson;
  }

  public LayoutConfig length(Integer length) {
    this.length = length;
    return this;
  }

   /**
   * Get length
   * @return length
  **/
  @JsonProperty("length")
  @ApiModelProperty(value = "")
  public Integer getLength() {
    return length;
  }

  public void setLength(Integer length) {
    this.length = length;
  }

  public LayoutConfig mandatory(String mandatory) {
    this.mandatory = mandatory;
    return this;
  }

   /**
   * Get mandatory
   * @return mandatory
  **/
  @JsonProperty("mandatory")
  @ApiModelProperty(value = "")
  public String getMandatory() {
    return mandatory;
  }

  public void setMandatory(String mandatory) {
    this.mandatory = mandatory;
  }

  public LayoutConfig mfFieldName(String mfFieldName) {
    this.mfFieldName = mfFieldName;
    return this;
  }

   /**
   * Get mfFieldName
   * @return mfFieldName
  **/
  @JsonProperty("mfFieldName")
  @ApiModelProperty(value = "")
  public String getMfFieldName() {
    return mfFieldName;
  }

  public void setMfFieldName(String mfFieldName) {
    this.mfFieldName = mfFieldName;
  }

  public LayoutConfig mfFieldWoutSpace(String mfFieldWoutSpace) {
    this.mfFieldWoutSpace = mfFieldWoutSpace;
    return this;
  }

   /**
   * Get mfFieldWoutSpace
   * @return mfFieldWoutSpace
  **/
  @JsonProperty("mfFieldWoutSpace")
  @ApiModelProperty(value = "")
  public String getMfFieldWoutSpace() {
    return mfFieldWoutSpace;
  }

  public void setMfFieldWoutSpace(String mfFieldWoutSpace) {
    this.mfFieldWoutSpace = mfFieldWoutSpace;
  }

  public LayoutConfig pptIdentifier(String pptIdentifier) {
    this.pptIdentifier = pptIdentifier;
    return this;
  }

   /**
   * Get pptIdentifier
   * @return pptIdentifier
  **/
  @JsonProperty("pptIdentifier")
  @ApiModelProperty(value = "")
  public String getPptIdentifier() {
    return pptIdentifier;
  }

  public void setPptIdentifier(String pptIdentifier) {
    this.pptIdentifier = pptIdentifier;
  }

  public LayoutConfig processJobMappingConfig(ProcessJobMapping processJobMappingConfig) {
    this.processJobMappingConfig = processJobMappingConfig;
    return this;
  }

   /**
   * Get processJobMappingConfig
   * @return processJobMappingConfig
  **/
  @JsonProperty("processJobMappingConfig")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMappingConfig() {
    return processJobMappingConfig;
  }

  public void setProcessJobMappingConfig(ProcessJobMapping processJobMappingConfig) {
    this.processJobMappingConfig = processJobMappingConfig;
  }

  public LayoutConfig recordFormat(String recordFormat) {
    this.recordFormat = recordFormat;
    return this;
  }

   /**
   * Get recordFormat
   * @return recordFormat
  **/
  @JsonProperty("recordFormat")
  @ApiModelProperty(value = "")
  public String getRecordFormat() {
    return recordFormat;
  }

  public void setRecordFormat(String recordFormat) {
    this.recordFormat = recordFormat;
  }

  public LayoutConfig recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public LayoutConfig recordIdentifierVal(String recordIdentifierVal) {
    this.recordIdentifierVal = recordIdentifierVal;
    return this;
  }

   /**
   * Get recordIdentifierVal
   * @return recordIdentifierVal
  **/
  @JsonProperty("recordIdentifierVal")
  @ApiModelProperty(value = "")
  public String getRecordIdentifierVal() {
    return recordIdentifierVal;
  }

  public void setRecordIdentifierVal(String recordIdentifierVal) {
    this.recordIdentifierVal = recordIdentifierVal;
  }

  public LayoutConfig recordType(String recordType) {
    this.recordType = recordType;
    return this;
  }

   /**
   * Get recordType
   * @return recordType
  **/
  @JsonProperty("recordType")
  @ApiModelProperty(value = "")
  public String getRecordType() {
    return recordType;
  }

  public void setRecordType(String recordType) {
    this.recordType = recordType;
  }

  public LayoutConfig sheetName(String sheetName) {
    this.sheetName = sheetName;
    return this;
  }

   /**
   * Get sheetName
   * @return sheetName
  **/
  @JsonProperty("sheetName")
  @ApiModelProperty(value = "")
  public String getSheetName() {
    return sheetName;
  }

  public void setSheetName(String sheetName) {
    this.sheetName = sheetName;
  }

  public LayoutConfig sheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
    return this;
  }

   /**
   * Get sheetNameWoutSpace
   * @return sheetNameWoutSpace
  **/
  @JsonProperty("sheetNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getSheetNameWoutSpace() {
    return sheetNameWoutSpace;
  }

  public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
  }

  public LayoutConfig startPos(String startPos) {
    this.startPos = startPos;
    return this;
  }

   /**
   * Get startPos
   * @return startPos
  **/
  @JsonProperty("startPos")
  @ApiModelProperty(value = "")
  public String getStartPos() {
    return startPos;
  }

  public void setStartPos(String startPos) {
    this.startPos = startPos;
  }

  public LayoutConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public LayoutConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public LayoutConfig valueDetails(String valueDetails) {
    this.valueDetails = valueDetails;
    return this;
  }

   /**
   * Get valueDetails
   * @return valueDetails
  **/
  @JsonProperty("valueDetails")
  @ApiModelProperty(value = "")
  public String getValueDetails() {
    return valueDetails;
  }

  public void setValueDetails(String valueDetails) {
    this.valueDetails = valueDetails;
  }

  public LayoutConfig preFilter(String preFilter) {
    this.preFilter = preFilter;
    return this;
  }

   /**
   * Get preFilter
   * @return preFilter
  **/
  @JsonProperty("preFilter")
  @ApiModelProperty(value = "")
  public String getPreFilter() {
    return preFilter;
  }

  public void setPreFilter(String preFilter) {
    this.preFilter = preFilter;
  }

  public LayoutConfig preFilterOperator(String preFilterOperator) {
    this.preFilterOperator = preFilterOperator;
    return this;
  }

   /**
   * Get preFilterOperator
   * @return preFilterOperator
  **/
  @JsonProperty("preFilterOperator")
  @ApiModelProperty(value = "")
  public String getPreFilterOperator() {
    return preFilterOperator;
  }

  public void setPreFilterOperator(String preFilterOperator) {
    this.preFilterOperator = preFilterOperator;
  }

  public LayoutConfig amountFormat(String amountFormat) {
    this.amountFormat = amountFormat;
    return this;
  }

   /**
   * Get amountFormat
   * @return amountFormat
  **/
  @JsonProperty("amountFormat")
  @ApiModelProperty(value = "")
  public String getAmountFormat() {
    return amountFormat;
  }

  public void setAmountFormat(String amountFormat) {
    this.amountFormat = amountFormat;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LayoutConfig layoutConfig = (LayoutConfig) o;
    return Objects.equals(this.activeFlag, layoutConfig.activeFlag) &&
        Objects.equals(this.createdBy, layoutConfig.createdBy) &&
        Objects.equals(this.createdDate, layoutConfig.createdDate) &&
        Objects.equals(this.dateFormat, layoutConfig.dateFormat) &&
        Objects.equals(this.fieldNo, layoutConfig.fieldNo) &&
        Objects.equals(this.fieldTemplate, layoutConfig.fieldTemplate) &&
        Objects.equals(this.fieldType, layoutConfig.fieldType) &&
        Objects.equals(this.fileName, layoutConfig.fileName) &&
        Objects.equals(this.fileNameWoutSpace, layoutConfig.fileNameWoutSpace) &&
        Objects.equals(this.id, layoutConfig.id) &&
        Objects.equals(this.labellingJson, layoutConfig.labellingJson) &&
        Objects.equals(this.length, layoutConfig.length) &&
        Objects.equals(this.mandatory, layoutConfig.mandatory) &&
        Objects.equals(this.mfFieldName, layoutConfig.mfFieldName) &&
        Objects.equals(this.mfFieldWoutSpace, layoutConfig.mfFieldWoutSpace) &&
        Objects.equals(this.pptIdentifier, layoutConfig.pptIdentifier) &&
        Objects.equals(this.processJobMappingConfig, layoutConfig.processJobMappingConfig) &&
        Objects.equals(this.recordFormat, layoutConfig.recordFormat) &&
        Objects.equals(this.recordIdentifier, layoutConfig.recordIdentifier) &&
        Objects.equals(this.recordIdentifierVal, layoutConfig.recordIdentifierVal) &&
        Objects.equals(this.recordType, layoutConfig.recordType) &&
        Objects.equals(this.sheetName, layoutConfig.sheetName) &&
        Objects.equals(this.sheetNameWoutSpace, layoutConfig.sheetNameWoutSpace) &&
        Objects.equals(this.startPos, layoutConfig.startPos) &&
        Objects.equals(this.updatedBy, layoutConfig.updatedBy) &&
        Objects.equals(this.updatedDate, layoutConfig.updatedDate) &&
        Objects.equals(this.valueDetails, layoutConfig.valueDetails) &&
        Objects.equals(this.preFilter, layoutConfig.preFilter) &&
        Objects.equals(this.preFilterOperator, layoutConfig.preFilterOperator) &&
        Objects.equals(this.amountFormat, layoutConfig.amountFormat);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, createdBy, createdDate, dateFormat, fieldNo, fieldTemplate, fieldType, fileName, fileNameWoutSpace, id, labellingJson, length, mandatory, mfFieldName, mfFieldWoutSpace, pptIdentifier, processJobMappingConfig, recordFormat, recordIdentifier, recordIdentifierVal, recordType, sheetName, sheetNameWoutSpace, startPos, updatedBy, updatedDate, valueDetails, preFilter, preFilterOperator, amountFormat);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LayoutConfig {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    dateFormat: ").append(toIndentedString(dateFormat)).append("\n");
    sb.append("    fieldNo: ").append(toIndentedString(fieldNo)).append("\n");
    sb.append("    fieldTemplate: ").append(toIndentedString(fieldTemplate)).append("\n");
    sb.append("    fieldType: ").append(toIndentedString(fieldType)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    fileNameWoutSpace: ").append(toIndentedString(fileNameWoutSpace)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    labellingJson: ").append(toIndentedString(labellingJson)).append("\n");
    sb.append("    length: ").append(toIndentedString(length)).append("\n");
    sb.append("    mandatory: ").append(toIndentedString(mandatory)).append("\n");
    sb.append("    mfFieldName: ").append(toIndentedString(mfFieldName)).append("\n");
    sb.append("    mfFieldWoutSpace: ").append(toIndentedString(mfFieldWoutSpace)).append("\n");
    sb.append("    pptIdentifier: ").append(toIndentedString(pptIdentifier)).append("\n");
    sb.append("    processJobMappingConfig: ").append(toIndentedString(processJobMappingConfig)).append("\n");
    sb.append("    recordFormat: ").append(toIndentedString(recordFormat)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    recordIdentifierVal: ").append(toIndentedString(recordIdentifierVal)).append("\n");
    sb.append("    recordType: ").append(toIndentedString(recordType)).append("\n");
    sb.append("    sheetName: ").append(toIndentedString(sheetName)).append("\n");
    sb.append("    sheetNameWoutSpace: ").append(toIndentedString(sheetNameWoutSpace)).append("\n");
    sb.append("    startPos: ").append(toIndentedString(startPos)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    valueDetails: ").append(toIndentedString(valueDetails)).append("\n");
    sb.append("    preFilter: ").append(toIndentedString(preFilter)).append("\n");
    sb.append("    preFilterOperator: ").append(toIndentedString(preFilterOperator)).append("\n");
    sb.append("    amountFormat: ").append(toIndentedString(amountFormat)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

