/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaPendingEvent
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-19T14:06:26.735+05:30")
public class TbaPendingEvent   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("activityId")
  private Integer activityId = null;

  @JsonProperty("baseKey")
  private String baseKey = null;

  @JsonProperty("classId")
  private String classId = null;

  @JsonProperty("clientId")
  private String clientId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eventLongDesc")
  private String eventLongDesc = null;

  @JsonProperty("eventName")
  private String eventName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("identifyFlag")
  private String identifyFlag = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("manualFlag")
  private String manualFlag = null;

  @JsonProperty("metaData")
  private String metaData = null;

  @JsonProperty("panelDisc")
  private String panelDisc = null;

  @JsonProperty("panelId")
  private String panelId = null;

  @JsonProperty("parNm")
  private String parNm = null;

  @JsonProperty("pendgEvntDefName")
  private String pendgEvntDefName = null;

  @JsonProperty("processMultipleOccurrences")
  private Boolean processMultipleOccurrences = null;

  @JsonProperty("criticalEdits")
  private Boolean criticalEdits = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("sequence")
  private String sequence = null;

  @JsonProperty("jsonKeyId")
  private String jsonKeyId = null;

  @JsonProperty("subKey")
  private String subKey = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("transId")
  private String transId = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaPendingEvent activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TbaPendingEvent activityId(Integer activityId) {
    this.activityId = activityId;
    return this;
  }

   /**
   * Get activityId
   * @return activityId
  **/
  @JsonProperty("activityId")
  @ApiModelProperty(value = "")
  public Integer getActivityId() {
    return activityId;
  }

  public void setActivityId(Integer activityId) {
    this.activityId = activityId;
  }

  public TbaPendingEvent baseKey(String baseKey) {
    this.baseKey = baseKey;
    return this;
  }

   /**
   * Get baseKey
   * @return baseKey
  **/
  @JsonProperty("baseKey")
  @ApiModelProperty(value = "")
  public String getBaseKey() {
    return baseKey;
  }

  public void setBaseKey(String baseKey) {
    this.baseKey = baseKey;
  }

  public TbaPendingEvent classId(String classId) {
    this.classId = classId;
    return this;
  }

   /**
   * Get classId
   * @return classId
  **/
  @JsonProperty("classId")
  @ApiModelProperty(value = "")
  public String getClassId() {
    return classId;
  }

  public void setClassId(String classId) {
    this.classId = classId;
  }

  public TbaPendingEvent clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public TbaPendingEvent createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaPendingEvent createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaPendingEvent eventLongDesc(String eventLongDesc) {
    this.eventLongDesc = eventLongDesc;
    return this;
  }

   /**
   * Get eventLongDesc
   * @return eventLongDesc
  **/
  @JsonProperty("eventLongDesc")
  @ApiModelProperty(value = "")
  public String getEventLongDesc() {
    return eventLongDesc;
  }

  public void setEventLongDesc(String eventLongDesc) {
    this.eventLongDesc = eventLongDesc;
  }

  public TbaPendingEvent eventName(String eventName) {
    this.eventName = eventName;
    return this;
  }

   /**
   * Get eventName
   * @return eventName
  **/
  @JsonProperty("eventName")
  @ApiModelProperty(value = "")
  public String getEventName() {
    return eventName;
  }

  public void setEventName(String eventName) {
    this.eventName = eventName;
  }

  public TbaPendingEvent id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaPendingEvent identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public TbaPendingEvent identifyFlag(String identifyFlag) {
    this.identifyFlag = identifyFlag;
    return this;
  }

   /**
   * Get identifyFlag
   * @return identifyFlag
  **/
  @JsonProperty("identifyFlag")
  @ApiModelProperty(value = "")
  public String getIdentifyFlag() {
    return identifyFlag;
  }

  public void setIdentifyFlag(String identifyFlag) {
    this.identifyFlag = identifyFlag;
  }

  public TbaPendingEvent jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public TbaPendingEvent manualFlag(String manualFlag) {
    this.manualFlag = manualFlag;
    return this;
  }

   /**
   * Get manualFlag
   * @return manualFlag
  **/
  @JsonProperty("manualFlag")
  @ApiModelProperty(value = "")
  public String getManualFlag() {
    return manualFlag;
  }

  public void setManualFlag(String manualFlag) {
    this.manualFlag = manualFlag;
  }

  public TbaPendingEvent metaData(String metaData) {
    this.metaData = metaData;
    return this;
  }

   /**
   * Get metaData
   * @return metaData
  **/
  @JsonProperty("metaData")
  @ApiModelProperty(value = "")
  public String getMetaData() {
    return metaData;
  }

  public void setMetaData(String metaData) {
    this.metaData = metaData;
  }

  public TbaPendingEvent panelDisc(String panelDisc) {
    this.panelDisc = panelDisc;
    return this;
  }

   /**
   * Get panelDisc
   * @return panelDisc
  **/
  @JsonProperty("panelDisc")
  @ApiModelProperty(value = "")
  public String getPanelDisc() {
    return panelDisc;
  }

  public void setPanelDisc(String panelDisc) {
    this.panelDisc = panelDisc;
  }

  public TbaPendingEvent panelId(String panelId) {
    this.panelId = panelId;
    return this;
  }

   /**
   * Get panelId
   * @return panelId
  **/
  @JsonProperty("panelId")
  @ApiModelProperty(value = "")
  public String getPanelId() {
    return panelId;
  }

  public void setPanelId(String panelId) {
    this.panelId = panelId;
  }

  public TbaPendingEvent parNm(String parNm) {
    this.parNm = parNm;
    return this;
  }

   /**
   * Get parNm
   * @return parNm
  **/
  @JsonProperty("parNm")
  @ApiModelProperty(value = "")
  public String getParNm() {
    return parNm;
  }

  public void setParNm(String parNm) {
    this.parNm = parNm;
  }

  public TbaPendingEvent pendgEvntDefName(String pendgEvntDefName) {
    this.pendgEvntDefName = pendgEvntDefName;
    return this;
  }

   /**
   * Get pendgEvntDefName
   * @return pendgEvntDefName
  **/
  @JsonProperty("pendgEvntDefName")
  @ApiModelProperty(value = "")
  public String getPendgEvntDefName() {
    return pendgEvntDefName;
  }

  public void setPendgEvntDefName(String pendgEvntDefName) {
    this.pendgEvntDefName = pendgEvntDefName;
  }

  public TbaPendingEvent processMultipleOccurrences(Boolean processMultipleOccurrences) {
    this.processMultipleOccurrences = processMultipleOccurrences;
    return this;
  }

   /**
   * Get processMultipleOccurrences
   * @return processMultipleOccurrences
  **/
  @JsonProperty("processMultipleOccurrences")
  @ApiModelProperty(value = "")
  public Boolean getProcessMultipleOccurrences() {
    return processMultipleOccurrences;
  }

  public void setProcessMultipleOccurrences(Boolean processMultipleOccurrences) {
    this.processMultipleOccurrences = processMultipleOccurrences;
  }

  public TbaPendingEvent criticalEdits(Boolean criticalEdits) {
    this.criticalEdits = criticalEdits;
    return this;
  }

   /**
   * Get criticalEdits
   * @return criticalEdits
  **/
  @JsonProperty("criticalEdits")
  @ApiModelProperty(value = "")
  public Boolean getCriticalEdits() {
    return criticalEdits;
  }

  public void setCriticalEdits(Boolean criticalEdits) {
    this.criticalEdits = criticalEdits;
  }

  public TbaPendingEvent processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TbaPendingEvent sequence(String sequence) {
    this.sequence = sequence;
    return this;
  }

   /**
   * Get sequence
   * @return sequence
  **/
  @JsonProperty("sequence")
  @ApiModelProperty(value = "")
  public String getSequence() {
    return sequence;
  }

  public void setSequence(String sequence) {
    this.sequence = sequence;
  }

  public TbaPendingEvent jsonKeyId(String jsonKeyId) {
    this.jsonKeyId = jsonKeyId;
    return this;
  }

   /**
   * Get jsonKeyId
   * @return jsonKeyId
  **/
  @JsonProperty("jsonKeyId")
  @ApiModelProperty(value = "")
  public String getJsonKeyId() {
    return jsonKeyId;
  }

  public void setJsonKeyId(String jsonKeyId) {
    this.jsonKeyId = jsonKeyId;
  }

  public TbaPendingEvent subKey(String subKey) {
    this.subKey = subKey;
    return this;
  }

   /**
   * Get subKey
   * @return subKey
  **/
  @JsonProperty("subKey")
  @ApiModelProperty(value = "")
  public String getSubKey() {
    return subKey;
  }

  public void setSubKey(String subKey) {
    this.subKey = subKey;
  }

  public TbaPendingEvent tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaPendingEvent transId(String transId) {
    this.transId = transId;
    return this;
  }

   /**
   * Get transId
   * @return transId
  **/
  @JsonProperty("transId")
  @ApiModelProperty(value = "")
  public String getTransId() {
    return transId;
  }

  public void setTransId(String transId) {
    this.transId = transId;
  }

  public TbaPendingEvent updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaPendingEvent updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaPendingEvent tbaPendingEvent = (TbaPendingEvent) o;
    return Objects.equals(this.activeFlag, tbaPendingEvent.activeFlag) &&
        Objects.equals(this.activityId, tbaPendingEvent.activityId) &&
        Objects.equals(this.baseKey, tbaPendingEvent.baseKey) &&
        Objects.equals(this.classId, tbaPendingEvent.classId) &&
        Objects.equals(this.clientId, tbaPendingEvent.clientId) &&
        Objects.equals(this.createdBy, tbaPendingEvent.createdBy) &&
        Objects.equals(this.createdDate, tbaPendingEvent.createdDate) &&
        Objects.equals(this.eventLongDesc, tbaPendingEvent.eventLongDesc) &&
        Objects.equals(this.eventName, tbaPendingEvent.eventName) &&
        Objects.equals(this.id, tbaPendingEvent.id) &&
        Objects.equals(this.identifier, tbaPendingEvent.identifier) &&
        Objects.equals(this.identifyFlag, tbaPendingEvent.identifyFlag) &&
        Objects.equals(this.jsonKey, tbaPendingEvent.jsonKey) &&
        Objects.equals(this.manualFlag, tbaPendingEvent.manualFlag) &&
        Objects.equals(this.metaData, tbaPendingEvent.metaData) &&
        Objects.equals(this.panelDisc, tbaPendingEvent.panelDisc) &&
        Objects.equals(this.panelId, tbaPendingEvent.panelId) &&
        Objects.equals(this.parNm, tbaPendingEvent.parNm) &&
        Objects.equals(this.pendgEvntDefName, tbaPendingEvent.pendgEvntDefName) &&
        Objects.equals(this.processMultipleOccurrences, tbaPendingEvent.processMultipleOccurrences) &&
        Objects.equals(this.criticalEdits, tbaPendingEvent.criticalEdits) &&
        Objects.equals(this.processJobMapping, tbaPendingEvent.processJobMapping) &&
        Objects.equals(this.sequence, tbaPendingEvent.sequence) &&
        Objects.equals(this.jsonKeyId, tbaPendingEvent.jsonKeyId) &&
        Objects.equals(this.subKey, tbaPendingEvent.subKey) &&
        Objects.equals(this.tbaFieldName, tbaPendingEvent.tbaFieldName) &&
        Objects.equals(this.transId, tbaPendingEvent.transId) &&
        Objects.equals(this.updatedBy, tbaPendingEvent.updatedBy) &&
        Objects.equals(this.updatedDate, tbaPendingEvent.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, activityId, baseKey, classId, clientId, createdBy, createdDate, eventLongDesc, eventName, id, identifier, identifyFlag, jsonKey, manualFlag, metaData, panelDisc, panelId, parNm, pendgEvntDefName, processMultipleOccurrences, criticalEdits, processJobMapping, sequence, jsonKeyId, subKey, tbaFieldName, transId, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaPendingEvent {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    activityId: ").append(toIndentedString(activityId)).append("\n");
    sb.append("    baseKey: ").append(toIndentedString(baseKey)).append("\n");
    sb.append("    classId: ").append(toIndentedString(classId)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eventLongDesc: ").append(toIndentedString(eventLongDesc)).append("\n");
    sb.append("    eventName: ").append(toIndentedString(eventName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    identifyFlag: ").append(toIndentedString(identifyFlag)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    manualFlag: ").append(toIndentedString(manualFlag)).append("\n");
    sb.append("    metaData: ").append(toIndentedString(metaData)).append("\n");
    sb.append("    panelDisc: ").append(toIndentedString(panelDisc)).append("\n");
    sb.append("    panelId: ").append(toIndentedString(panelId)).append("\n");
    sb.append("    parNm: ").append(toIndentedString(parNm)).append("\n");
    sb.append("    pendgEvntDefName: ").append(toIndentedString(pendgEvntDefName)).append("\n");
    sb.append("    processMultipleOccurrences: ").append(toIndentedString(processMultipleOccurrences)).append("\n");
    sb.append("    criticalEdits: ").append(toIndentedString(criticalEdits)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    jsonKeyId: ").append(toIndentedString(jsonKeyId)).append("\n");
    sb.append("    subKey: ").append(toIndentedString(subKey)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    transId: ").append(toIndentedString(transId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

