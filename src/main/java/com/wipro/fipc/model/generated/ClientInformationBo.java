/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.BusinessUnitsBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * ClientInformationBo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ClientInformationBo   {
  @JsonProperty("businessUnitsBos")
  private List<BusinessUnitsBo> businessUnitsBos = new ArrayList<BusinessUnitsBo>();

  public ClientInformationBo businessUnitsBos(List<BusinessUnitsBo> businessUnitsBos) {
    this.businessUnitsBos = businessUnitsBos;
    return this;
  }

  public ClientInformationBo addBusinessUnitsBosItem(BusinessUnitsBo businessUnitsBosItem) {
    this.businessUnitsBos.add(businessUnitsBosItem);
    return this;
  }

   /**
   * Get businessUnitsBos
   * @return businessUnitsBos
  **/
  @JsonProperty("businessUnitsBos")
  @ApiModelProperty(value = "")
  public List<BusinessUnitsBo> getBusinessUnitsBos() {
    return businessUnitsBos;
  }

  public void setBusinessUnitsBos(List<BusinessUnitsBo> businessUnitsBos) {
    this.businessUnitsBos = businessUnitsBos;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ClientInformationBo clientInformationBo = (ClientInformationBo) o;
    return Objects.equals(this.businessUnitsBos, clientInformationBo.businessUnitsBos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessUnitsBos);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ClientInformationBo {\n");
    
    sb.append("    businessUnitsBos: ").append(toIndentedString(businessUnitsBos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

