/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ChildJobs
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ChildJobs   {
  @JsonProperty("allClientJobId")
  private Long allClientJobId = null;

  @JsonProperty("childEndTimeInReport")
  private String childEndTimeInReport = null;

  @JsonProperty("childJobCutOffTime")
  private String childJobCutOffTime = null;

  @JsonProperty("childJobFailureId")
  private String childJobFailureId = null;

  @JsonProperty("childJobName")
  private String childJobName = null;

  @JsonProperty("childStartTimeInReport")
  private String childStartTimeInReport = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("titleOfDailyTaskReport")
  private String titleOfDailyTaskReport = null;

  @JsonProperty("uid")
  private String uid = null;

  public ChildJobs allClientJobId(Long allClientJobId) {
    this.allClientJobId = allClientJobId;
    return this;
  }

   /**
   * Get allClientJobId
   * @return allClientJobId
  **/
  @JsonProperty("allClientJobId")
  @ApiModelProperty(value = "")
  public Long getAllClientJobId() {
    return allClientJobId;
  }

  public void setAllClientJobId(Long allClientJobId) {
    this.allClientJobId = allClientJobId;
  }

  public ChildJobs childEndTimeInReport(String childEndTimeInReport) {
    this.childEndTimeInReport = childEndTimeInReport;
    return this;
  }

   /**
   * Get childEndTimeInReport
   * @return childEndTimeInReport
  **/
  @JsonProperty("childEndTimeInReport")
  @ApiModelProperty(value = "")
  public String getChildEndTimeInReport() {
    return childEndTimeInReport;
  }

  public void setChildEndTimeInReport(String childEndTimeInReport) {
    this.childEndTimeInReport = childEndTimeInReport;
  }

  public ChildJobs childJobCutOffTime(String childJobCutOffTime) {
    this.childJobCutOffTime = childJobCutOffTime;
    return this;
  }

   /**
   * Get childJobCutOffTime
   * @return childJobCutOffTime
  **/
  @JsonProperty("childJobCutOffTime")
  @ApiModelProperty(value = "")
  public String getChildJobCutOffTime() {
    return childJobCutOffTime;
  }

  public void setChildJobCutOffTime(String childJobCutOffTime) {
    this.childJobCutOffTime = childJobCutOffTime;
  }

  public ChildJobs childJobFailureId(String childJobFailureId) {
    this.childJobFailureId = childJobFailureId;
    return this;
  }

   /**
   * Get childJobFailureId
   * @return childJobFailureId
  **/
  @JsonProperty("childJobFailureId")
  @ApiModelProperty(value = "")
  public String getChildJobFailureId() {
    return childJobFailureId;
  }

  public void setChildJobFailureId(String childJobFailureId) {
    this.childJobFailureId = childJobFailureId;
  }

  public ChildJobs childJobName(String childJobName) {
    this.childJobName = childJobName;
    return this;
  }

   /**
   * Get childJobName
   * @return childJobName
  **/
  @JsonProperty("childJobName")
  @ApiModelProperty(value = "")
  public String getChildJobName() {
    return childJobName;
  }

  public void setChildJobName(String childJobName) {
    this.childJobName = childJobName;
  }

  public ChildJobs childStartTimeInReport(String childStartTimeInReport) {
    this.childStartTimeInReport = childStartTimeInReport;
    return this;
  }

   /**
   * Get childStartTimeInReport
   * @return childStartTimeInReport
  **/
  @JsonProperty("childStartTimeInReport")
  @ApiModelProperty(value = "")
  public String getChildStartTimeInReport() {
    return childStartTimeInReport;
  }

  public void setChildStartTimeInReport(String childStartTimeInReport) {
    this.childStartTimeInReport = childStartTimeInReport;
  }

  public ChildJobs createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ChildJobs id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ChildJobs status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public ChildJobs titleOfDailyTaskReport(String titleOfDailyTaskReport) {
    this.titleOfDailyTaskReport = titleOfDailyTaskReport;
    return this;
  }

   /**
   * Get titleOfDailyTaskReport
   * @return titleOfDailyTaskReport
  **/
  @JsonProperty("titleOfDailyTaskReport")
  @ApiModelProperty(value = "")
  public String getTitleOfDailyTaskReport() {
    return titleOfDailyTaskReport;
  }

  public void setTitleOfDailyTaskReport(String titleOfDailyTaskReport) {
    this.titleOfDailyTaskReport = titleOfDailyTaskReport;
  }

  public ChildJobs uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChildJobs childJobs = (ChildJobs) o;
    return Objects.equals(this.allClientJobId, childJobs.allClientJobId) &&
        Objects.equals(this.childEndTimeInReport, childJobs.childEndTimeInReport) &&
        Objects.equals(this.childJobCutOffTime, childJobs.childJobCutOffTime) &&
        Objects.equals(this.childJobFailureId, childJobs.childJobFailureId) &&
        Objects.equals(this.childJobName, childJobs.childJobName) &&
        Objects.equals(this.childStartTimeInReport, childJobs.childStartTimeInReport) &&
        Objects.equals(this.createdDate, childJobs.createdDate) &&
        Objects.equals(this.id, childJobs.id) &&
        Objects.equals(this.status, childJobs.status) &&
        Objects.equals(this.titleOfDailyTaskReport, childJobs.titleOfDailyTaskReport) &&
        Objects.equals(this.uid, childJobs.uid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(allClientJobId, childEndTimeInReport, childJobCutOffTime, childJobFailureId, childJobName, childStartTimeInReport, createdDate, id, status, titleOfDailyTaskReport, uid);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChildJobs {\n");
    
    sb.append("    allClientJobId: ").append(toIndentedString(allClientJobId)).append("\n");
    sb.append("    childEndTimeInReport: ").append(toIndentedString(childEndTimeInReport)).append("\n");
    sb.append("    childJobCutOffTime: ").append(toIndentedString(childJobCutOffTime)).append("\n");
    sb.append("    childJobFailureId: ").append(toIndentedString(childJobFailureId)).append("\n");
    sb.append("    childJobName: ").append(toIndentedString(childJobName)).append("\n");
    sb.append("    childStartTimeInReport: ").append(toIndentedString(childStartTimeInReport)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    titleOfDailyTaskReport: ").append(toIndentedString(titleOfDailyTaskReport)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

