/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * KsdFileDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2024-07-25T11:39:22.176+05:30")
public class KsdFileDetails   {
  @JsonProperty("action")
  private String action = null;

  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("append")
  private String append = null;

  @JsonProperty("appendFormat")
  private String appendFormat = null;

  @JsonProperty("appendNameFlag")
  private String appendNameFlag = null;

  @JsonProperty("verifyEmailOnly")
  private Boolean verifyEmailOnly = null;

  @JsonProperty("verifyFileDateDetailRecord")
  private Boolean verifyFileDateDetailRecord = null;

  @JsonProperty("client")
  private String client = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("database")
  private String database = null;

  @JsonProperty("dateFormat")
  private String dateFormat = null;

  @JsonProperty("dateFrequency")
  private String dateFrequency = null;

  @JsonProperty("dateGenerated")
  private String dateGenerated = null;

  @JsonProperty("dateInterval")
  private String dateInterval = null;

  @JsonProperty("datePeriod")
  private String datePeriod = null;

  @JsonProperty("delimiter")
  private String delimiter = null;

  @JsonProperty("domain")
  private String domain = null;

  @JsonProperty("emailSearchBy")
  private String emailSearchBy = null;

  @JsonProperty("fileFormatType")
  private String fileFormatType = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("fileNameTemplate")
  private String fileNameTemplate = null;

  @JsonProperty("fileNameWoutSpace")
  private String fileNameWoutSpace = null;

  @JsonProperty("fileType")
  private String fileType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("maxThreshold")
  private String maxThreshold = null;

  @JsonProperty("minThreshold")
  private String minThreshold = null;

  @JsonProperty("path")
  private String path = null;

  @JsonProperty("pptidentifier")
  private String pptidentifier = null;

  @JsonProperty("pptidentifierType")
  private String pptidentifierType = null;

  @JsonProperty("prevReportFileName")
  private String prevReportFileName = null;

  @JsonProperty("prevReportFileNameWs")
  private String prevReportFileNameWs = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("queryJCLName")
  private String queryJCLName = null;

  @JsonProperty("recordCntCheck")
  private String recordCntCheck = null;

  @JsonProperty("recordIdentifierCol")
  private String recordIdentifierCol = null;

  @JsonProperty("reportFlag")
  private String reportFlag = null;

  @JsonProperty("sendr")
  private String sendr = null;

  @JsonProperty("sheetName")
  private String sheetName = null;

  @JsonProperty("sheetNameWoutSpace")
  private String sheetNameWoutSpace = null;

  @JsonProperty("source")
  private String source = null;

  @JsonProperty("sqlQuery")
  private String sqlQuery = null;

  @JsonProperty("subfolder")
  private String subfolder = null;

  @JsonProperty("subj")
  private String subj = null;

  @JsonProperty("tool")
  private String tool = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("useLabellingRpt")
  private String useLabellingRpt = null;

  @JsonProperty("variation")
  private String variation = null;

  @JsonProperty("verticalIDentifier")
  private String verticalIDentifier = null;

  @JsonProperty("whitelist")
  private String whitelist = null;

  @JsonProperty("primaryFile")
  private String primaryFile = null;

  @JsonProperty("samplingCount")
  private String samplingCount = null;

  @JsonProperty("benePptIdentifier")
  private String benePptIdentifier = null;

  @JsonProperty("benePptIdentifierType")
  private String benePptIdentifierType = null;

  @JsonProperty("whitelistSSN")
  private String whitelistSSN = null;

  public KsdFileDetails action(String action) {
    this.action = action;
    return this;
  }

   /**
   * Get action
   * @return action
  **/
  @JsonProperty("action")
  @ApiModelProperty(value = "")
  public String getAction() {
    return action;
  }

  public void setAction(String action) {
    this.action = action;
  }

  public KsdFileDetails activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public KsdFileDetails append(String append) {
    this.append = append;
    return this;
  }

   /**
   * Get append
   * @return append
  **/
  @JsonProperty("append")
  @ApiModelProperty(value = "")
  public String getAppend() {
    return append;
  }

  public void setAppend(String append) {
    this.append = append;
  }

  public KsdFileDetails appendFormat(String appendFormat) {
    this.appendFormat = appendFormat;
    return this;
  }

   /**
   * Get appendFormat
   * @return appendFormat
  **/
  @JsonProperty("appendFormat")
  @ApiModelProperty(value = "")
  public String getAppendFormat() {
    return appendFormat;
  }

  public void setAppendFormat(String appendFormat) {
    this.appendFormat = appendFormat;
  }

  public KsdFileDetails appendNameFlag(String appendNameFlag) {
    this.appendNameFlag = appendNameFlag;
    return this;
  }

   /**
   * Get appendNameFlag
   * @return appendNameFlag
  **/
  @JsonProperty("appendNameFlag")
  @ApiModelProperty(value = "")
  public String getAppendNameFlag() {
    return appendNameFlag;
  }

  public void setAppendNameFlag(String appendNameFlag) {
    this.appendNameFlag = appendNameFlag;
  }

  public KsdFileDetails verifyEmailOnly(Boolean verifyEmailOnly) {
    this.verifyEmailOnly = verifyEmailOnly;
    return this;
  }

   /**
   * Get verifyEmailOnly
   * @return verifyEmailOnly
  **/
  @JsonProperty("verifyEmailOnly")
  @ApiModelProperty(value = "")
  public Boolean getVerifyEmailOnly() {
    return verifyEmailOnly;
  }

  public void setVerifyEmailOnly(Boolean verifyEmailOnly) {
    this.verifyEmailOnly = verifyEmailOnly;
  }

  public KsdFileDetails verifyFileDateDetailRecord(Boolean verifyFileDateDetailRecord) {
    this.verifyFileDateDetailRecord = verifyFileDateDetailRecord;
    return this;
  }

   /**
   * Get verifyFileDateDetailRecord
   * @return verifyFileDateDetailRecord
  **/
  @JsonProperty("verifyFileDateDetailRecord")
  @ApiModelProperty(value = "")
  public Boolean getVerifyFileDateDetailRecord() {
    return verifyFileDateDetailRecord;
  }

  public void setVerifyFileDateDetailRecord(Boolean verifyFileDateDetailRecord) {
    this.verifyFileDateDetailRecord = verifyFileDateDetailRecord;
  }

  public KsdFileDetails client(String client) {
    this.client = client;
    return this;
  }

   /**
   * Get client
   * @return client
  **/
  @JsonProperty("client")
  @ApiModelProperty(value = "")
  public String getClient() {
    return client;
  }

  public void setClient(String client) {
    this.client = client;
  }

  public KsdFileDetails createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public KsdFileDetails createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public KsdFileDetails database(String database) {
    this.database = database;
    return this;
  }

   /**
   * Get database
   * @return database
  **/
  @JsonProperty("database")
  @ApiModelProperty(value = "")
  public String getDatabase() {
    return database;
  }

  public void setDatabase(String database) {
    this.database = database;
  }

  public KsdFileDetails dateFormat(String dateFormat) {
    this.dateFormat = dateFormat;
    return this;
  }

   /**
   * Get dateFormat
   * @return dateFormat
  **/
  @JsonProperty("dateFormat")
  @ApiModelProperty(value = "")
  public String getDateFormat() {
    return dateFormat;
  }

  public void setDateFormat(String dateFormat) {
    this.dateFormat = dateFormat;
  }

  public KsdFileDetails dateFrequency(String dateFrequency) {
    this.dateFrequency = dateFrequency;
    return this;
  }

   /**
   * Get dateFrequency
   * @return dateFrequency
  **/
  @JsonProperty("dateFrequency")
  @ApiModelProperty(value = "")
  public String getDateFrequency() {
    return dateFrequency;
  }

  public void setDateFrequency(String dateFrequency) {
    this.dateFrequency = dateFrequency;
  }

  public KsdFileDetails dateGenerated(String dateGenerated) {
    this.dateGenerated = dateGenerated;
    return this;
  }

   /**
   * Get dateGenerated
   * @return dateGenerated
  **/
  @JsonProperty("dateGenerated")
  @ApiModelProperty(value = "")
  public String getDateGenerated() {
    return dateGenerated;
  }

  public void setDateGenerated(String dateGenerated) {
    this.dateGenerated = dateGenerated;
  }

  public KsdFileDetails dateInterval(String dateInterval) {
    this.dateInterval = dateInterval;
    return this;
  }

   /**
   * Get dateInterval
   * @return dateInterval
  **/
  @JsonProperty("dateInterval")
  @ApiModelProperty(value = "")
  public String getDateInterval() {
    return dateInterval;
  }

  public void setDateInterval(String dateInterval) {
    this.dateInterval = dateInterval;
  }

  public KsdFileDetails datePeriod(String datePeriod) {
    this.datePeriod = datePeriod;
    return this;
  }

   /**
   * Get datePeriod
   * @return datePeriod
  **/
  @JsonProperty("datePeriod")
  @ApiModelProperty(value = "")
  public String getDatePeriod() {
    return datePeriod;
  }

  public void setDatePeriod(String datePeriod) {
    this.datePeriod = datePeriod;
  }

  public KsdFileDetails delimiter(String delimiter) {
    this.delimiter = delimiter;
    return this;
  }

   /**
   * Get delimiter
   * @return delimiter
  **/
  @JsonProperty("delimiter")
  @ApiModelProperty(value = "")
  public String getDelimiter() {
    return delimiter;
  }

  public void setDelimiter(String delimiter) {
    this.delimiter = delimiter;
  }

  public KsdFileDetails domain(String domain) {
    this.domain = domain;
    return this;
  }

   /**
   * Get domain
   * @return domain
  **/
  @JsonProperty("domain")
  @ApiModelProperty(value = "")
  public String getDomain() {
    return domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  public KsdFileDetails emailSearchBy(String emailSearchBy) {
    this.emailSearchBy = emailSearchBy;
    return this;
  }

   /**
   * Get emailSearchBy
   * @return emailSearchBy
  **/
  @JsonProperty("emailSearchBy")
  @ApiModelProperty(value = "")
  public String getEmailSearchBy() {
    return emailSearchBy;
  }

  public void setEmailSearchBy(String emailSearchBy) {
    this.emailSearchBy = emailSearchBy;
  }

  public KsdFileDetails fileFormatType(String fileFormatType) {
    this.fileFormatType = fileFormatType;
    return this;
  }

   /**
   * Get fileFormatType
   * @return fileFormatType
  **/
  @JsonProperty("fileFormatType")
  @ApiModelProperty(value = "")
  public String getFileFormatType() {
    return fileFormatType;
  }

  public void setFileFormatType(String fileFormatType) {
    this.fileFormatType = fileFormatType;
  }

  public KsdFileDetails fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public KsdFileDetails fileNameTemplate(String fileNameTemplate) {
    this.fileNameTemplate = fileNameTemplate;
    return this;
  }

   /**
   * Get fileNameTemplate
   * @return fileNameTemplate
  **/
  @JsonProperty("fileNameTemplate")
  @ApiModelProperty(value = "")
  public String getFileNameTemplate() {
    return fileNameTemplate;
  }

  public void setFileNameTemplate(String fileNameTemplate) {
    this.fileNameTemplate = fileNameTemplate;
  }

  public KsdFileDetails fileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
    return this;
  }

   /**
   * Get fileNameWoutSpace
   * @return fileNameWoutSpace
  **/
  @JsonProperty("fileNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getFileNameWoutSpace() {
    return fileNameWoutSpace;
  }

  public void setFileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
  }

  public KsdFileDetails fileType(String fileType) {
    this.fileType = fileType;
    return this;
  }

   /**
   * Get fileType
   * @return fileType
  **/
  @JsonProperty("fileType")
  @ApiModelProperty(value = "")
  public String getFileType() {
    return fileType;
  }

  public void setFileType(String fileType) {
    this.fileType = fileType;
  }

  public KsdFileDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public KsdFileDetails maxThreshold(String maxThreshold) {
    this.maxThreshold = maxThreshold;
    return this;
  }

   /**
   * Get maxThreshold
   * @return maxThreshold
  **/
  @JsonProperty("maxThreshold")
  @ApiModelProperty(value = "")
  public String getMaxThreshold() {
    return maxThreshold;
  }

  public void setMaxThreshold(String maxThreshold) {
    this.maxThreshold = maxThreshold;
  }

  public KsdFileDetails minThreshold(String minThreshold) {
    this.minThreshold = minThreshold;
    return this;
  }

   /**
   * Get minThreshold
   * @return minThreshold
  **/
  @JsonProperty("minThreshold")
  @ApiModelProperty(value = "")
  public String getMinThreshold() {
    return minThreshold;
  }

  public void setMinThreshold(String minThreshold) {
    this.minThreshold = minThreshold;
  }

  public KsdFileDetails path(String path) {
    this.path = path;
    return this;
  }

   /**
   * Get path
   * @return path
  **/
  @JsonProperty("path")
  @ApiModelProperty(value = "")
  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public KsdFileDetails pptidentifier(String pptidentifier) {
    this.pptidentifier = pptidentifier;
    return this;
  }

   /**
   * Get pptidentifier
   * @return pptidentifier
  **/
  @JsonProperty("pptidentifier")
  @ApiModelProperty(value = "")
  public String getPptidentifier() {
    return pptidentifier;
  }

  public void setPptidentifier(String pptidentifier) {
    this.pptidentifier = pptidentifier;
  }

  public KsdFileDetails pptidentifierType(String pptidentifierType) {
    this.pptidentifierType = pptidentifierType;
    return this;
  }

   /**
   * Get pptidentifierType
   * @return pptidentifierType
  **/
  @JsonProperty("pptidentifierType")
  @ApiModelProperty(value = "")
  public String getPptidentifierType() {
    return pptidentifierType;
  }

  public void setPptidentifierType(String pptidentifierType) {
    this.pptidentifierType = pptidentifierType;
  }

  public KsdFileDetails prevReportFileName(String prevReportFileName) {
    this.prevReportFileName = prevReportFileName;
    return this;
  }

   /**
   * Get prevReportFileName
   * @return prevReportFileName
  **/
  @JsonProperty("prevReportFileName")
  @ApiModelProperty(value = "")
  public String getPrevReportFileName() {
    return prevReportFileName;
  }

  public void setPrevReportFileName(String prevReportFileName) {
    this.prevReportFileName = prevReportFileName;
  }

  public KsdFileDetails prevReportFileNameWs(String prevReportFileNameWs) {
    this.prevReportFileNameWs = prevReportFileNameWs;
    return this;
  }

   /**
   * Get prevReportFileNameWs
   * @return prevReportFileNameWs
  **/
  @JsonProperty("prevReportFileNameWs")
  @ApiModelProperty(value = "")
  public String getPrevReportFileNameWs() {
    return prevReportFileNameWs;
  }

  public void setPrevReportFileNameWs(String prevReportFileNameWs) {
    this.prevReportFileNameWs = prevReportFileNameWs;
  }

  public KsdFileDetails processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public KsdFileDetails processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public KsdFileDetails queryJCLName(String queryJCLName) {
    this.queryJCLName = queryJCLName;
    return this;
  }

   /**
   * Get queryJCLName
   * @return queryJCLName
  **/
  @JsonProperty("queryJCLName")
  @ApiModelProperty(value = "")
  public String getQueryJCLName() {
    return queryJCLName;
  }

  public void setQueryJCLName(String queryJCLName) {
    this.queryJCLName = queryJCLName;
  }

  public KsdFileDetails recordCntCheck(String recordCntCheck) {
    this.recordCntCheck = recordCntCheck;
    return this;
  }

   /**
   * Get recordCntCheck
   * @return recordCntCheck
  **/
  @JsonProperty("recordCntCheck")
  @ApiModelProperty(value = "")
  public String getRecordCntCheck() {
    return recordCntCheck;
  }

  public void setRecordCntCheck(String recordCntCheck) {
    this.recordCntCheck = recordCntCheck;
  }

  public KsdFileDetails recordIdentifierCol(String recordIdentifierCol) {
    this.recordIdentifierCol = recordIdentifierCol;
    return this;
  }

   /**
   * Get recordIdentifierCol
   * @return recordIdentifierCol
  **/
  @JsonProperty("recordIdentifierCol")
  @ApiModelProperty(value = "")
  public String getRecordIdentifierCol() {
    return recordIdentifierCol;
  }

  public void setRecordIdentifierCol(String recordIdentifierCol) {
    this.recordIdentifierCol = recordIdentifierCol;
  }

  public KsdFileDetails reportFlag(String reportFlag) {
    this.reportFlag = reportFlag;
    return this;
  }

   /**
   * Get reportFlag
   * @return reportFlag
  **/
  @JsonProperty("reportFlag")
  @ApiModelProperty(value = "")
  public String getReportFlag() {
    return reportFlag;
  }

  public void setReportFlag(String reportFlag) {
    this.reportFlag = reportFlag;
  }

  public KsdFileDetails sendr(String sendr) {
    this.sendr = sendr;
    return this;
  }

   /**
   * Get sendr
   * @return sendr
  **/
  @JsonProperty("sendr")
  @ApiModelProperty(value = "")
  public String getSendr() {
    return sendr;
  }

  public void setSendr(String sendr) {
    this.sendr = sendr;
  }

  public KsdFileDetails sheetName(String sheetName) {
    this.sheetName = sheetName;
    return this;
  }

   /**
   * Get sheetName
   * @return sheetName
  **/
  @JsonProperty("sheetName")
  @ApiModelProperty(value = "")
  public String getSheetName() {
    return sheetName;
  }

  public void setSheetName(String sheetName) {
    this.sheetName = sheetName;
  }

  public KsdFileDetails sheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
    return this;
  }

   /**
   * Get sheetNameWoutSpace
   * @return sheetNameWoutSpace
  **/
  @JsonProperty("sheetNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getSheetNameWoutSpace() {
    return sheetNameWoutSpace;
  }

  public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
  }

  public KsdFileDetails source(String source) {
    this.source = source;
    return this;
  }

   /**
   * Get source
   * @return source
  **/
  @JsonProperty("source")
  @ApiModelProperty(value = "")
  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public KsdFileDetails sqlQuery(String sqlQuery) {
    this.sqlQuery = sqlQuery;
    return this;
  }

   /**
   * Get sqlQuery
   * @return sqlQuery
  **/
  @JsonProperty("sqlQuery")
  @ApiModelProperty(value = "")
  public String getSqlQuery() {
    return sqlQuery;
  }

  public void setSqlQuery(String sqlQuery) {
    this.sqlQuery = sqlQuery;
  }

  public KsdFileDetails subfolder(String subfolder) {
    this.subfolder = subfolder;
    return this;
  }

   /**
   * Get subfolder
   * @return subfolder
  **/
  @JsonProperty("subfolder")
  @ApiModelProperty(value = "")
  public String getSubfolder() {
    return subfolder;
  }

  public void setSubfolder(String subfolder) {
    this.subfolder = subfolder;
  }

  public KsdFileDetails subj(String subj) {
    this.subj = subj;
    return this;
  }

   /**
   * Get subj
   * @return subj
  **/
  @JsonProperty("subj")
  @ApiModelProperty(value = "")
  public String getSubj() {
    return subj;
  }

  public void setSubj(String subj) {
    this.subj = subj;
  }

  public KsdFileDetails tool(String tool) {
    this.tool = tool;
    return this;
  }

   /**
   * Get tool
   * @return tool
  **/
  @JsonProperty("tool")
  @ApiModelProperty(value = "")
  public String getTool() {
    return tool;
  }

  public void setTool(String tool) {
    this.tool = tool;
  }

  public KsdFileDetails updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public KsdFileDetails updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public KsdFileDetails useLabellingRpt(String useLabellingRpt) {
    this.useLabellingRpt = useLabellingRpt;
    return this;
  }

   /**
   * Get useLabellingRpt
   * @return useLabellingRpt
  **/
  @JsonProperty("useLabellingRpt")
  @ApiModelProperty(value = "")
  public String getUseLabellingRpt() {
    return useLabellingRpt;
  }

  public void setUseLabellingRpt(String useLabellingRpt) {
    this.useLabellingRpt = useLabellingRpt;
  }

  public KsdFileDetails variation(String variation) {
    this.variation = variation;
    return this;
  }

   /**
   * Get variation
   * @return variation
  **/
  @JsonProperty("variation")
  @ApiModelProperty(value = "")
  public String getVariation() {
    return variation;
  }

  public void setVariation(String variation) {
    this.variation = variation;
  }

  public KsdFileDetails verticalIDentifier(String verticalIDentifier) {
    this.verticalIDentifier = verticalIDentifier;
    return this;
  }

   /**
   * Get verticalIDentifier
   * @return verticalIDentifier
  **/
  @JsonProperty("verticalIDentifier")
  @ApiModelProperty(value = "")
  public String getVerticalIDentifier() {
    return verticalIDentifier;
  }

  public void setVerticalIDentifier(String verticalIDentifier) {
    this.verticalIDentifier = verticalIDentifier;
  }

  public KsdFileDetails whitelist(String whitelist) {
    this.whitelist = whitelist;
    return this;
  }

   /**
   * Get whitelist
   * @return whitelist
  **/
  @JsonProperty("whitelist")
  @ApiModelProperty(value = "")
  public String getWhitelist() {
    return whitelist;
  }

  public void setWhitelist(String whitelist) {
    this.whitelist = whitelist;
  }

  public KsdFileDetails primaryFile(String primaryFile) {
    this.primaryFile = primaryFile;
    return this;
  }

   /**
   * Get primaryFile
   * @return primaryFile
  **/
  @JsonProperty("primaryFile")
  @ApiModelProperty(value = "")
  public String getPrimaryFile() {
    return primaryFile;
  }

  public void setPrimaryFile(String primaryFile) {
    this.primaryFile = primaryFile;
  }

  public KsdFileDetails samplingCount(String samplingCount) {
    this.samplingCount = samplingCount;
    return this;
  }

   /**
   * Get samplingCount
   * @return samplingCount
  **/
  @JsonProperty("samplingCount")
  @ApiModelProperty(value = "")
  public String getSamplingCount() {
    return samplingCount;
  }

  public void setSamplingCount(String samplingCount) {
    this.samplingCount = samplingCount;
  }

  public KsdFileDetails benePptIdentifier(String benePptIdentifier) {
    this.benePptIdentifier = benePptIdentifier;
    return this;
  }

   /**
   * Get benePptIdentifier
   * @return benePptIdentifier
  **/
  @JsonProperty("benePptIdentifier")
  @ApiModelProperty(value = "")
  public String getBenePptIdentifier() {
    return benePptIdentifier;
  }

  public void setBenePptIdentifier(String benePptIdentifier) {
    this.benePptIdentifier = benePptIdentifier;
  }

  public KsdFileDetails benePptIdentifierType(String benePptIdentifierType) {
    this.benePptIdentifierType = benePptIdentifierType;
    return this;
  }

   /**
   * Get benePptIdentifierType
   * @return benePptIdentifierType
  **/
  @JsonProperty("benePptIdentifierType")
  @ApiModelProperty(value = "")
  public String getBenePptIdentifierType() {
    return benePptIdentifierType;
  }

  public void setBenePptIdentifierType(String benePptIdentifierType) {
    this.benePptIdentifierType = benePptIdentifierType;
  }

  public KsdFileDetails whitelistSSN(String whitelistSSN) {
    this.whitelistSSN = whitelistSSN;
    return this;
  }

   /**
   * Get whitelistSSN
   * @return whitelistSSN
  **/
  @JsonProperty("whitelistSSN")
  @ApiModelProperty(value = "")
  public String getWhitelistSSN() {
    return whitelistSSN;
  }

  public void setWhitelistSSN(String whitelistSSN) {
    this.whitelistSSN = whitelistSSN;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KsdFileDetails ksdFileDetails = (KsdFileDetails) o;
    return Objects.equals(this.action, ksdFileDetails.action) &&
        Objects.equals(this.activeFlag, ksdFileDetails.activeFlag) &&
        Objects.equals(this.append, ksdFileDetails.append) &&
        Objects.equals(this.appendFormat, ksdFileDetails.appendFormat) &&
        Objects.equals(this.appendNameFlag, ksdFileDetails.appendNameFlag) &&
        Objects.equals(this.verifyEmailOnly, ksdFileDetails.verifyEmailOnly) &&
        Objects.equals(this.verifyFileDateDetailRecord, ksdFileDetails.verifyFileDateDetailRecord) &&
        Objects.equals(this.client, ksdFileDetails.client) &&
        Objects.equals(this.createdBy, ksdFileDetails.createdBy) &&
        Objects.equals(this.createdDate, ksdFileDetails.createdDate) &&
        Objects.equals(this.database, ksdFileDetails.database) &&
        Objects.equals(this.dateFormat, ksdFileDetails.dateFormat) &&
        Objects.equals(this.dateFrequency, ksdFileDetails.dateFrequency) &&
        Objects.equals(this.dateGenerated, ksdFileDetails.dateGenerated) &&
        Objects.equals(this.dateInterval, ksdFileDetails.dateInterval) &&
        Objects.equals(this.datePeriod, ksdFileDetails.datePeriod) &&
        Objects.equals(this.delimiter, ksdFileDetails.delimiter) &&
        Objects.equals(this.domain, ksdFileDetails.domain) &&
        Objects.equals(this.emailSearchBy, ksdFileDetails.emailSearchBy) &&
        Objects.equals(this.fileFormatType, ksdFileDetails.fileFormatType) &&
        Objects.equals(this.fileName, ksdFileDetails.fileName) &&
        Objects.equals(this.fileNameTemplate, ksdFileDetails.fileNameTemplate) &&
        Objects.equals(this.fileNameWoutSpace, ksdFileDetails.fileNameWoutSpace) &&
        Objects.equals(this.fileType, ksdFileDetails.fileType) &&
        Objects.equals(this.id, ksdFileDetails.id) &&
        Objects.equals(this.maxThreshold, ksdFileDetails.maxThreshold) &&
        Objects.equals(this.minThreshold, ksdFileDetails.minThreshold) &&
        Objects.equals(this.path, ksdFileDetails.path) &&
        Objects.equals(this.pptidentifier, ksdFileDetails.pptidentifier) &&
        Objects.equals(this.pptidentifierType, ksdFileDetails.pptidentifierType) &&
        Objects.equals(this.prevReportFileName, ksdFileDetails.prevReportFileName) &&
        Objects.equals(this.prevReportFileNameWs, ksdFileDetails.prevReportFileNameWs) &&
        Objects.equals(this.processJobMapping, ksdFileDetails.processJobMapping) &&
        Objects.equals(this.processJobMappingId, ksdFileDetails.processJobMappingId) &&
        Objects.equals(this.queryJCLName, ksdFileDetails.queryJCLName) &&
        Objects.equals(this.recordCntCheck, ksdFileDetails.recordCntCheck) &&
        Objects.equals(this.recordIdentifierCol, ksdFileDetails.recordIdentifierCol) &&
        Objects.equals(this.reportFlag, ksdFileDetails.reportFlag) &&
        Objects.equals(this.sendr, ksdFileDetails.sendr) &&
        Objects.equals(this.sheetName, ksdFileDetails.sheetName) &&
        Objects.equals(this.sheetNameWoutSpace, ksdFileDetails.sheetNameWoutSpace) &&
        Objects.equals(this.source, ksdFileDetails.source) &&
        Objects.equals(this.sqlQuery, ksdFileDetails.sqlQuery) &&
        Objects.equals(this.subfolder, ksdFileDetails.subfolder) &&
        Objects.equals(this.subj, ksdFileDetails.subj) &&
        Objects.equals(this.tool, ksdFileDetails.tool) &&
        Objects.equals(this.updatedBy, ksdFileDetails.updatedBy) &&
        Objects.equals(this.updatedDate, ksdFileDetails.updatedDate) &&
        Objects.equals(this.useLabellingRpt, ksdFileDetails.useLabellingRpt) &&
        Objects.equals(this.variation, ksdFileDetails.variation) &&
        Objects.equals(this.verticalIDentifier, ksdFileDetails.verticalIDentifier) &&
        Objects.equals(this.whitelist, ksdFileDetails.whitelist) &&
        Objects.equals(this.primaryFile, ksdFileDetails.primaryFile) &&
        Objects.equals(this.samplingCount, ksdFileDetails.samplingCount) &&
        Objects.equals(this.benePptIdentifier, ksdFileDetails.benePptIdentifier) &&
        Objects.equals(this.benePptIdentifierType, ksdFileDetails.benePptIdentifierType) &&
        Objects.equals(this.whitelistSSN, ksdFileDetails.whitelistSSN);
  }

  @Override
  public int hashCode() {
    return Objects.hash(action, activeFlag, append, appendFormat, appendNameFlag, verifyEmailOnly, verifyFileDateDetailRecord, client, createdBy, createdDate, database, dateFormat, dateFrequency, dateGenerated, dateInterval, datePeriod, delimiter, domain, emailSearchBy, fileFormatType, fileName, fileNameTemplate, fileNameWoutSpace, fileType, id, maxThreshold, minThreshold, path, pptidentifier, pptidentifierType, prevReportFileName, prevReportFileNameWs, processJobMapping, processJobMappingId, queryJCLName, recordCntCheck, recordIdentifierCol, reportFlag, sendr, sheetName, sheetNameWoutSpace, source, sqlQuery, subfolder, subj, tool, updatedBy, updatedDate, useLabellingRpt, variation, verticalIDentifier, whitelist, primaryFile, samplingCount, benePptIdentifier, benePptIdentifierType, whitelistSSN);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KsdFileDetails {\n");
    
    sb.append("    action: ").append(toIndentedString(action)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    append: ").append(toIndentedString(append)).append("\n");
    sb.append("    appendFormat: ").append(toIndentedString(appendFormat)).append("\n");
    sb.append("    appendNameFlag: ").append(toIndentedString(appendNameFlag)).append("\n");
    sb.append("    verifyEmailOnly: ").append(toIndentedString(verifyEmailOnly)).append("\n");
    sb.append("    verifyFileDateDetailRecord: ").append(toIndentedString(verifyFileDateDetailRecord)).append("\n");
    sb.append("    client: ").append(toIndentedString(client)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    database: ").append(toIndentedString(database)).append("\n");
    sb.append("    dateFormat: ").append(toIndentedString(dateFormat)).append("\n");
    sb.append("    dateFrequency: ").append(toIndentedString(dateFrequency)).append("\n");
    sb.append("    dateGenerated: ").append(toIndentedString(dateGenerated)).append("\n");
    sb.append("    dateInterval: ").append(toIndentedString(dateInterval)).append("\n");
    sb.append("    datePeriod: ").append(toIndentedString(datePeriod)).append("\n");
    sb.append("    delimiter: ").append(toIndentedString(delimiter)).append("\n");
    sb.append("    domain: ").append(toIndentedString(domain)).append("\n");
    sb.append("    emailSearchBy: ").append(toIndentedString(emailSearchBy)).append("\n");
    sb.append("    fileFormatType: ").append(toIndentedString(fileFormatType)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    fileNameTemplate: ").append(toIndentedString(fileNameTemplate)).append("\n");
    sb.append("    fileNameWoutSpace: ").append(toIndentedString(fileNameWoutSpace)).append("\n");
    sb.append("    fileType: ").append(toIndentedString(fileType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    maxThreshold: ").append(toIndentedString(maxThreshold)).append("\n");
    sb.append("    minThreshold: ").append(toIndentedString(minThreshold)).append("\n");
    sb.append("    path: ").append(toIndentedString(path)).append("\n");
    sb.append("    pptidentifier: ").append(toIndentedString(pptidentifier)).append("\n");
    sb.append("    pptidentifierType: ").append(toIndentedString(pptidentifierType)).append("\n");
    sb.append("    prevReportFileName: ").append(toIndentedString(prevReportFileName)).append("\n");
    sb.append("    prevReportFileNameWs: ").append(toIndentedString(prevReportFileNameWs)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    queryJCLName: ").append(toIndentedString(queryJCLName)).append("\n");
    sb.append("    recordCntCheck: ").append(toIndentedString(recordCntCheck)).append("\n");
    sb.append("    recordIdentifierCol: ").append(toIndentedString(recordIdentifierCol)).append("\n");
    sb.append("    reportFlag: ").append(toIndentedString(reportFlag)).append("\n");
    sb.append("    sendr: ").append(toIndentedString(sendr)).append("\n");
    sb.append("    sheetName: ").append(toIndentedString(sheetName)).append("\n");
    sb.append("    sheetNameWoutSpace: ").append(toIndentedString(sheetNameWoutSpace)).append("\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    sqlQuery: ").append(toIndentedString(sqlQuery)).append("\n");
    sb.append("    subfolder: ").append(toIndentedString(subfolder)).append("\n");
    sb.append("    subj: ").append(toIndentedString(subj)).append("\n");
    sb.append("    tool: ").append(toIndentedString(tool)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    useLabellingRpt: ").append(toIndentedString(useLabellingRpt)).append("\n");
    sb.append("    variation: ").append(toIndentedString(variation)).append("\n");
    sb.append("    verticalIDentifier: ").append(toIndentedString(verticalIDentifier)).append("\n");
    sb.append("    whitelist: ").append(toIndentedString(whitelist)).append("\n");
    sb.append("    primaryFile: ").append(toIndentedString(primaryFile)).append("\n");
    sb.append("    samplingCount: ").append(toIndentedString(samplingCount)).append("\n");
    sb.append("    benePptIdentifier: ").append(toIndentedString(benePptIdentifier)).append("\n");
    sb.append("    benePptIdentifierType: ").append(toIndentedString(benePptIdentifierType)).append("\n");
    sb.append("    whitelistSSN: ").append(toIndentedString(whitelistSSN)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

