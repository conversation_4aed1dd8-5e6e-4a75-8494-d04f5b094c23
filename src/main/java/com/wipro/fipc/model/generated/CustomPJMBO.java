/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * CustomPJMBO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class CustomPJMBO   {
  @JsonProperty("businessOpsId")
  private Long businessOpsId = null;

  @JsonProperty("businessOpsName")
  private String businessOpsName = null;

  @JsonProperty("eftSubject")
  private String eftSubject = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  public CustomPJMBO businessOpsId(Long businessOpsId) {
    this.businessOpsId = businessOpsId;
    return this;
  }

   /**
   * Get businessOpsId
   * @return businessOpsId
  **/
  @JsonProperty("businessOpsId")
  @ApiModelProperty(value = "")
  public Long getBusinessOpsId() {
    return businessOpsId;
  }

  public void setBusinessOpsId(Long businessOpsId) {
    this.businessOpsId = businessOpsId;
  }

  public CustomPJMBO businessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
    return this;
  }

   /**
   * Get businessOpsName
   * @return businessOpsName
  **/
  @JsonProperty("businessOpsName")
  @ApiModelProperty(value = "")
  public String getBusinessOpsName() {
    return businessOpsName;
  }

  public void setBusinessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
  }

  public CustomPJMBO eftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
    return this;
  }

   /**
   * Get eftSubject
   * @return eftSubject
  **/
  @JsonProperty("eftSubject")
  @ApiModelProperty(value = "")
  public String getEftSubject() {
    return eftSubject;
  }

  public void setEftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
  }

  public CustomPJMBO jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public CustomPJMBO processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public CustomPJMBO processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public CustomPJMBO processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomPJMBO customPJMBO = (CustomPJMBO) o;
    return Objects.equals(this.businessOpsId, customPJMBO.businessOpsId) &&
        Objects.equals(this.businessOpsName, customPJMBO.businessOpsName) &&
        Objects.equals(this.eftSubject, customPJMBO.eftSubject) &&
        Objects.equals(this.jobName, customPJMBO.jobName) &&
        Objects.equals(this.processJobMappingId, customPJMBO.processJobMappingId) &&
        Objects.equals(this.processName, customPJMBO.processName) &&
        Objects.equals(this.processType, customPJMBO.processType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessOpsId, businessOpsName, eftSubject, jobName, processJobMappingId, processName, processType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomPJMBO {\n");
    
    sb.append("    businessOpsId: ").append(toIndentedString(businessOpsId)).append("\n");
    sb.append("    businessOpsName: ").append(toIndentedString(businessOpsName)).append("\n");
    sb.append("    eftSubject: ").append(toIndentedString(eftSubject)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

