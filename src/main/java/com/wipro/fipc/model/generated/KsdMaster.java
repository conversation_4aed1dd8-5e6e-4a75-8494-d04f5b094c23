/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * KsdMaster
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class KsdMaster   {
  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eftName")
  private String eftName = null;

  @JsonProperty("fileFormatType")
  private String fileFormatType = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("frequency")
  private String frequency = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobCutOffTime")
  private String jobCutOffTime = null;

  @JsonProperty("jobScheduleTime")
  private String jobScheduleTime = null;

  @JsonProperty("jobStream")
  private String jobStream = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("maestroTaskName")
  private String maestroTaskName = null;

  @JsonProperty("primaryJobName")
  private String primaryJobName = null;

  @JsonProperty("turnaroundTime")
  private String turnaroundTime = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public KsdMaster createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public KsdMaster createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public KsdMaster eftName(String eftName) {
    this.eftName = eftName;
    return this;
  }

   /**
   * Get eftName
   * @return eftName
  **/
  @JsonProperty("eftName")
  @ApiModelProperty(value = "")
  public String getEftName() {
    return eftName;
  }

  public void setEftName(String eftName) {
    this.eftName = eftName;
  }

  public KsdMaster fileFormatType(String fileFormatType) {
    this.fileFormatType = fileFormatType;
    return this;
  }

   /**
   * Get fileFormatType
   * @return fileFormatType
  **/
  @JsonProperty("fileFormatType")
  @ApiModelProperty(value = "")
  public String getFileFormatType() {
    return fileFormatType;
  }

  public void setFileFormatType(String fileFormatType) {
    this.fileFormatType = fileFormatType;
  }

  public KsdMaster fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public KsdMaster frequency(String frequency) {
    this.frequency = frequency;
    return this;
  }

   /**
   * Get frequency
   * @return frequency
  **/
  @JsonProperty("frequency")
  @ApiModelProperty(value = "")
  public String getFrequency() {
    return frequency;
  }

  public void setFrequency(String frequency) {
    this.frequency = frequency;
  }

  public KsdMaster id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public KsdMaster jobCutOffTime(String jobCutOffTime) {
    this.jobCutOffTime = jobCutOffTime;
    return this;
  }

   /**
   * Get jobCutOffTime
   * @return jobCutOffTime
  **/
  @JsonProperty("jobCutOffTime")
  @ApiModelProperty(value = "")
  public String getJobCutOffTime() {
    return jobCutOffTime;
  }

  public void setJobCutOffTime(String jobCutOffTime) {
    this.jobCutOffTime = jobCutOffTime;
  }

  public KsdMaster jobScheduleTime(String jobScheduleTime) {
    this.jobScheduleTime = jobScheduleTime;
    return this;
  }

   /**
   * Get jobScheduleTime
   * @return jobScheduleTime
  **/
  @JsonProperty("jobScheduleTime")
  @ApiModelProperty(value = "")
  public String getJobScheduleTime() {
    return jobScheduleTime;
  }

  public void setJobScheduleTime(String jobScheduleTime) {
    this.jobScheduleTime = jobScheduleTime;
  }

  public KsdMaster jobStream(String jobStream) {
    this.jobStream = jobStream;
    return this;
  }

   /**
   * Get jobStream
   * @return jobStream
  **/
  @JsonProperty("jobStream")
  @ApiModelProperty(value = "")
  public String getJobStream() {
    return jobStream;
  }

  public void setJobStream(String jobStream) {
    this.jobStream = jobStream;
  }

  public KsdMaster ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public KsdMaster maestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
    return this;
  }

   /**
   * Get maestroTaskName
   * @return maestroTaskName
  **/
  @JsonProperty("maestroTaskName")
  @ApiModelProperty(value = "")
  public String getMaestroTaskName() {
    return maestroTaskName;
  }

  public void setMaestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
  }

  public KsdMaster primaryJobName(String primaryJobName) {
    this.primaryJobName = primaryJobName;
    return this;
  }

   /**
   * Get primaryJobName
   * @return primaryJobName
  **/
  @JsonProperty("primaryJobName")
  @ApiModelProperty(value = "")
  public String getPrimaryJobName() {
    return primaryJobName;
  }

  public void setPrimaryJobName(String primaryJobName) {
    this.primaryJobName = primaryJobName;
  }

  public KsdMaster turnaroundTime(String turnaroundTime) {
    this.turnaroundTime = turnaroundTime;
    return this;
  }

   /**
   * Get turnaroundTime
   * @return turnaroundTime
  **/
  @JsonProperty("turnaroundTime")
  @ApiModelProperty(value = "")
  public String getTurnaroundTime() {
    return turnaroundTime;
  }

  public void setTurnaroundTime(String turnaroundTime) {
    this.turnaroundTime = turnaroundTime;
  }

  public KsdMaster updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public KsdMaster updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KsdMaster ksdMaster = (KsdMaster) o;
    return Objects.equals(this.createdBy, ksdMaster.createdBy) &&
        Objects.equals(this.createdDate, ksdMaster.createdDate) &&
        Objects.equals(this.eftName, ksdMaster.eftName) &&
        Objects.equals(this.fileFormatType, ksdMaster.fileFormatType) &&
        Objects.equals(this.fileName, ksdMaster.fileName) &&
        Objects.equals(this.frequency, ksdMaster.frequency) &&
        Objects.equals(this.id, ksdMaster.id) &&
        Objects.equals(this.jobCutOffTime, ksdMaster.jobCutOffTime) &&
        Objects.equals(this.jobScheduleTime, ksdMaster.jobScheduleTime) &&
        Objects.equals(this.jobStream, ksdMaster.jobStream) &&
        Objects.equals(this.ksdName, ksdMaster.ksdName) &&
        Objects.equals(this.maestroTaskName, ksdMaster.maestroTaskName) &&
        Objects.equals(this.primaryJobName, ksdMaster.primaryJobName) &&
        Objects.equals(this.turnaroundTime, ksdMaster.turnaroundTime) &&
        Objects.equals(this.updatedBy, ksdMaster.updatedBy) &&
        Objects.equals(this.updatedDate, ksdMaster.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(createdBy, createdDate, eftName, fileFormatType, fileName, frequency, id, jobCutOffTime, jobScheduleTime, jobStream, ksdName, maestroTaskName, primaryJobName, turnaroundTime, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KsdMaster {\n");
    
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eftName: ").append(toIndentedString(eftName)).append("\n");
    sb.append("    fileFormatType: ").append(toIndentedString(fileFormatType)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    frequency: ").append(toIndentedString(frequency)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobCutOffTime: ").append(toIndentedString(jobCutOffTime)).append("\n");
    sb.append("    jobScheduleTime: ").append(toIndentedString(jobScheduleTime)).append("\n");
    sb.append("    jobStream: ").append(toIndentedString(jobStream)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    maestroTaskName: ").append(toIndentedString(maestroTaskName)).append("\n");
    sb.append("    primaryJobName: ").append(toIndentedString(primaryJobName)).append("\n");
    sb.append("    turnaroundTime: ").append(toIndentedString(turnaroundTime)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

