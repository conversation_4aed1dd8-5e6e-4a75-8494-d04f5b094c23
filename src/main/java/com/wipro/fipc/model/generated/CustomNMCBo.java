/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * CustomNMCBo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class CustomNMCBo   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("appendSubject")
  private String appendSubject = null;

  @JsonProperty("attachmentName")
  private String attachmentName = null;

  @JsonProperty("ccList")
  private String ccList = null;

  @JsonProperty("condition")
  private String condition = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("interestedFields")
  private String interestedFields = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("remarks")
  private String remarks = null;

  @JsonProperty("subject")
  private String subject = null;

  @JsonProperty("toList")
  private String toList = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public CustomNMCBo activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public CustomNMCBo appendSubject(String appendSubject) {
    this.appendSubject = appendSubject;
    return this;
  }

   /**
   * Get appendSubject
   * @return appendSubject
  **/
  @JsonProperty("appendSubject")
  @ApiModelProperty(value = "")
  public String getAppendSubject() {
    return appendSubject;
  }

  public void setAppendSubject(String appendSubject) {
    this.appendSubject = appendSubject;
  }

  public CustomNMCBo attachmentName(String attachmentName) {
    this.attachmentName = attachmentName;
    return this;
  }

   /**
   * Get attachmentName
   * @return attachmentName
  **/
  @JsonProperty("attachmentName")
  @ApiModelProperty(value = "")
  public String getAttachmentName() {
    return attachmentName;
  }

  public void setAttachmentName(String attachmentName) {
    this.attachmentName = attachmentName;
  }

  public CustomNMCBo ccList(String ccList) {
    this.ccList = ccList;
    return this;
  }

   /**
   * Get ccList
   * @return ccList
  **/
  @JsonProperty("ccList")
  @ApiModelProperty(value = "")
  public String getCcList() {
    return ccList;
  }

  public void setCcList(String ccList) {
    this.ccList = ccList;
  }

  public CustomNMCBo condition(String condition) {
    this.condition = condition;
    return this;
  }

   /**
   * Get condition
   * @return condition
  **/
  @JsonProperty("condition")
  @ApiModelProperty(value = "")
  public String getCondition() {
    return condition;
  }

  public void setCondition(String condition) {
    this.condition = condition;
  }

  public CustomNMCBo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public CustomNMCBo createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public CustomNMCBo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public CustomNMCBo interestedFields(String interestedFields) {
    this.interestedFields = interestedFields;
    return this;
  }

   /**
   * Get interestedFields
   * @return interestedFields
  **/
  @JsonProperty("interestedFields")
  @ApiModelProperty(value = "")
  public String getInterestedFields() {
    return interestedFields;
  }

  public void setInterestedFields(String interestedFields) {
    this.interestedFields = interestedFields;
  }

  public CustomNMCBo processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public CustomNMCBo remarks(String remarks) {
    this.remarks = remarks;
    return this;
  }

   /**
   * Get remarks
   * @return remarks
  **/
  @JsonProperty("remarks")
  @ApiModelProperty(value = "")
  public String getRemarks() {
    return remarks;
  }

  public void setRemarks(String remarks) {
    this.remarks = remarks;
  }

  public CustomNMCBo subject(String subject) {
    this.subject = subject;
    return this;
  }

   /**
   * Get subject
   * @return subject
  **/
  @JsonProperty("subject")
  @ApiModelProperty(value = "")
  public String getSubject() {
    return subject;
  }

  public void setSubject(String subject) {
    this.subject = subject;
  }

  public CustomNMCBo toList(String toList) {
    this.toList = toList;
    return this;
  }

   /**
   * Get toList
   * @return toList
  **/
  @JsonProperty("toList")
  @ApiModelProperty(value = "")
  public String getToList() {
    return toList;
  }

  public void setToList(String toList) {
    this.toList = toList;
  }

  public CustomNMCBo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public CustomNMCBo updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomNMCBo customNMCBo = (CustomNMCBo) o;
    return Objects.equals(this.activeFlag, customNMCBo.activeFlag) &&
        Objects.equals(this.appendSubject, customNMCBo.appendSubject) &&
        Objects.equals(this.attachmentName, customNMCBo.attachmentName) &&
        Objects.equals(this.ccList, customNMCBo.ccList) &&
        Objects.equals(this.condition, customNMCBo.condition) &&
        Objects.equals(this.createdBy, customNMCBo.createdBy) &&
        Objects.equals(this.createdDate, customNMCBo.createdDate) &&
        Objects.equals(this.id, customNMCBo.id) &&
        Objects.equals(this.interestedFields, customNMCBo.interestedFields) &&
        Objects.equals(this.processJobMappingId, customNMCBo.processJobMappingId) &&
        Objects.equals(this.remarks, customNMCBo.remarks) &&
        Objects.equals(this.subject, customNMCBo.subject) &&
        Objects.equals(this.toList, customNMCBo.toList) &&
        Objects.equals(this.updatedBy, customNMCBo.updatedBy) &&
        Objects.equals(this.updatedDate, customNMCBo.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, appendSubject, attachmentName, ccList, condition, createdBy, createdDate, id, interestedFields, processJobMappingId, remarks, subject, toList, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomNMCBo {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    appendSubject: ").append(toIndentedString(appendSubject)).append("\n");
    sb.append("    attachmentName: ").append(toIndentedString(attachmentName)).append("\n");
    sb.append("    ccList: ").append(toIndentedString(ccList)).append("\n");
    sb.append("    condition: ").append(toIndentedString(condition)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    interestedFields: ").append(toIndentedString(interestedFields)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    remarks: ").append(toIndentedString(remarks)).append("\n");
    sb.append("    subject: ").append(toIndentedString(subject)).append("\n");
    sb.append("    toList: ").append(toIndentedString(toList)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

