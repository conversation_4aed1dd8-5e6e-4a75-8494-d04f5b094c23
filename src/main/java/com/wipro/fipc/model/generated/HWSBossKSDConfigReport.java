/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * HWSBossKSDConfigReport
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class HWSBossKSDConfigReport   {
  @JsonProperty("approved_by")
  private String approvedBy = null;

  @JsonProperty("approved_on")
  private Date approvedOn = null;

  @JsonProperty("business_unit")
  private String businessUnit = null;

  @JsonProperty("client_code")
  private String clientCode = null;

  @JsonProperty("client_name")
  private String clientName = null;

  @JsonProperty("config_status")
  private String configStatus = null;

  @JsonProperty("created_by")
  private String createdBy = null;

  @JsonProperty("created_on")
  private Date createdOn = null;

  @JsonProperty("ksd_eft_subject_name")
  private String ksdEftSubjectName = null;

  @JsonProperty("ksd_job_name")
  private String ksdJobName = null;

  @JsonProperty("ksd_name")
  private String ksdName = null;

  @JsonProperty("lotus_notes_id")
  private Long lotusNotesId = null;

  @JsonProperty("maestro_task_id")
  private Long maestroTaskId = null;

  @JsonProperty("maestro_ticket_id")
  private Long maestroTicketId = null;

  @JsonProperty("owner")
  private String owner = null;

  @JsonProperty("pjm_id")
  private Long pjmId = null;

  @JsonProperty("process_name")
  private String processName = null;

  @JsonProperty("process_type")
  private String processType = null;

  @JsonProperty("rules_id")
  private Long rulesId = null;

  @JsonProperty("tba_inquiry_id")
  private Long tbaInquiryId = null;

  @JsonProperty("tba_update_id")
  private Long tbaUpdateId = null;

  @JsonProperty("tower")
  private String tower = null;

  @JsonProperty("updated_by")
  private String updatedBy = null;

  @JsonProperty("updated_on")
  private Date updatedOn = null;

  public HWSBossKSDConfigReport approvedBy(String approvedBy) {
    this.approvedBy = approvedBy;
    return this;
  }

   /**
   * Get approvedBy
   * @return approvedBy
  **/
  @JsonProperty("approved_by")
  @ApiModelProperty(value = "")
  public String getApprovedBy() {
    return approvedBy;
  }

  public void setApprovedBy(String approvedBy) {
    this.approvedBy = approvedBy;
  }

  public HWSBossKSDConfigReport approvedOn(Date approvedOn) {
    this.approvedOn = approvedOn;
    return this;
  }

   /**
   * Get approvedOn
   * @return approvedOn
  **/
  @JsonProperty("approved_on")
  @ApiModelProperty(value = "")
  public Date getApprovedOn() {
    return approvedOn;
  }

  public void setApprovedOn(Date approvedOn) {
    this.approvedOn = approvedOn;
  }

  public HWSBossKSDConfigReport businessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("business_unit")
  @ApiModelProperty(value = "")
  public String getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
  }

  public HWSBossKSDConfigReport clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("client_code")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public HWSBossKSDConfigReport clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("client_name")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public HWSBossKSDConfigReport configStatus(String configStatus) {
    this.configStatus = configStatus;
    return this;
  }

   /**
   * Get configStatus
   * @return configStatus
  **/
  @JsonProperty("config_status")
  @ApiModelProperty(value = "")
  public String getConfigStatus() {
    return configStatus;
  }

  public void setConfigStatus(String configStatus) {
    this.configStatus = configStatus;
  }

  public HWSBossKSDConfigReport createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("created_by")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public HWSBossKSDConfigReport createdOn(Date createdOn) {
    this.createdOn = createdOn;
    return this;
  }

   /**
   * Get createdOn
   * @return createdOn
  **/
  @JsonProperty("created_on")
  @ApiModelProperty(value = "")
  public Date getCreatedOn() {
    return createdOn;
  }

  public void setCreatedOn(Date createdOn) {
    this.createdOn = createdOn;
  }

  public HWSBossKSDConfigReport ksdEftSubjectName(String ksdEftSubjectName) {
    this.ksdEftSubjectName = ksdEftSubjectName;
    return this;
  }

   /**
   * Get ksdEftSubjectName
   * @return ksdEftSubjectName
  **/
  @JsonProperty("ksd_eft_subject_name")
  @ApiModelProperty(value = "")
  public String getKsdEftSubjectName() {
    return ksdEftSubjectName;
  }

  public void setKsdEftSubjectName(String ksdEftSubjectName) {
    this.ksdEftSubjectName = ksdEftSubjectName;
  }

  public HWSBossKSDConfigReport ksdJobName(String ksdJobName) {
    this.ksdJobName = ksdJobName;
    return this;
  }

   /**
   * Get ksdJobName
   * @return ksdJobName
  **/
  @JsonProperty("ksd_job_name")
  @ApiModelProperty(value = "")
  public String getKsdJobName() {
    return ksdJobName;
  }

  public void setKsdJobName(String ksdJobName) {
    this.ksdJobName = ksdJobName;
  }

  public HWSBossKSDConfigReport ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksd_name")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public HWSBossKSDConfigReport lotusNotesId(Long lotusNotesId) {
    this.lotusNotesId = lotusNotesId;
    return this;
  }

   /**
   * Get lotusNotesId
   * @return lotusNotesId
  **/
  @JsonProperty("lotus_notes_id")
  @ApiModelProperty(value = "")
  public Long getLotusNotesId() {
    return lotusNotesId;
  }

  public void setLotusNotesId(Long lotusNotesId) {
    this.lotusNotesId = lotusNotesId;
  }

  public HWSBossKSDConfigReport maestroTaskId(Long maestroTaskId) {
    this.maestroTaskId = maestroTaskId;
    return this;
  }

   /**
   * Get maestroTaskId
   * @return maestroTaskId
  **/
  @JsonProperty("maestro_task_id")
  @ApiModelProperty(value = "")
  public Long getMaestroTaskId() {
    return maestroTaskId;
  }

  public void setMaestroTaskId(Long maestroTaskId) {
    this.maestroTaskId = maestroTaskId;
  }

  public HWSBossKSDConfigReport maestroTicketId(Long maestroTicketId) {
    this.maestroTicketId = maestroTicketId;
    return this;
  }

   /**
   * Get maestroTicketId
   * @return maestroTicketId
  **/
  @JsonProperty("maestro_ticket_id")
  @ApiModelProperty(value = "")
  public Long getMaestroTicketId() {
    return maestroTicketId;
  }

  public void setMaestroTicketId(Long maestroTicketId) {
    this.maestroTicketId = maestroTicketId;
  }

  public HWSBossKSDConfigReport owner(String owner) {
    this.owner = owner;
    return this;
  }

   /**
   * Get owner
   * @return owner
  **/
  @JsonProperty("owner")
  @ApiModelProperty(value = "")
  public String getOwner() {
    return owner;
  }

  public void setOwner(String owner) {
    this.owner = owner;
  }

  public HWSBossKSDConfigReport pjmId(Long pjmId) {
    this.pjmId = pjmId;
    return this;
  }

   /**
   * Get pjmId
   * @return pjmId
  **/
  @JsonProperty("pjm_id")
  @ApiModelProperty(value = "")
  public Long getPjmId() {
    return pjmId;
  }

  public void setPjmId(Long pjmId) {
    this.pjmId = pjmId;
  }

  public HWSBossKSDConfigReport processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("process_name")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public HWSBossKSDConfigReport processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("process_type")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public HWSBossKSDConfigReport rulesId(Long rulesId) {
    this.rulesId = rulesId;
    return this;
  }

   /**
   * Get rulesId
   * @return rulesId
  **/
  @JsonProperty("rules_id")
  @ApiModelProperty(value = "")
  public Long getRulesId() {
    return rulesId;
  }

  public void setRulesId(Long rulesId) {
    this.rulesId = rulesId;
  }

  public HWSBossKSDConfigReport tbaInquiryId(Long tbaInquiryId) {
    this.tbaInquiryId = tbaInquiryId;
    return this;
  }

   /**
   * Get tbaInquiryId
   * @return tbaInquiryId
  **/
  @JsonProperty("tba_inquiry_id")
  @ApiModelProperty(value = "")
  public Long getTbaInquiryId() {
    return tbaInquiryId;
  }

  public void setTbaInquiryId(Long tbaInquiryId) {
    this.tbaInquiryId = tbaInquiryId;
  }

  public HWSBossKSDConfigReport tbaUpdateId(Long tbaUpdateId) {
    this.tbaUpdateId = tbaUpdateId;
    return this;
  }

   /**
   * Get tbaUpdateId
   * @return tbaUpdateId
  **/
  @JsonProperty("tba_update_id")
  @ApiModelProperty(value = "")
  public Long getTbaUpdateId() {
    return tbaUpdateId;
  }

  public void setTbaUpdateId(Long tbaUpdateId) {
    this.tbaUpdateId = tbaUpdateId;
  }

  public HWSBossKSDConfigReport tower(String tower) {
    this.tower = tower;
    return this;
  }

   /**
   * Get tower
   * @return tower
  **/
  @JsonProperty("tower")
  @ApiModelProperty(value = "")
  public String getTower() {
    return tower;
  }

  public void setTower(String tower) {
    this.tower = tower;
  }

  public HWSBossKSDConfigReport updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updated_by")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public HWSBossKSDConfigReport updatedOn(Date updatedOn) {
    this.updatedOn = updatedOn;
    return this;
  }

   /**
   * Get updatedOn
   * @return updatedOn
  **/
  @JsonProperty("updated_on")
  @ApiModelProperty(value = "")
  public Date getUpdatedOn() {
    return updatedOn;
  }

  public void setUpdatedOn(Date updatedOn) {
    this.updatedOn = updatedOn;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HWSBossKSDConfigReport hwSBossKSDConfigReport = (HWSBossKSDConfigReport) o;
    return Objects.equals(this.approvedBy, hwSBossKSDConfigReport.approvedBy) &&
        Objects.equals(this.approvedOn, hwSBossKSDConfigReport.approvedOn) &&
        Objects.equals(this.businessUnit, hwSBossKSDConfigReport.businessUnit) &&
        Objects.equals(this.clientCode, hwSBossKSDConfigReport.clientCode) &&
        Objects.equals(this.clientName, hwSBossKSDConfigReport.clientName) &&
        Objects.equals(this.configStatus, hwSBossKSDConfigReport.configStatus) &&
        Objects.equals(this.createdBy, hwSBossKSDConfigReport.createdBy) &&
        Objects.equals(this.createdOn, hwSBossKSDConfigReport.createdOn) &&
        Objects.equals(this.ksdEftSubjectName, hwSBossKSDConfigReport.ksdEftSubjectName) &&
        Objects.equals(this.ksdJobName, hwSBossKSDConfigReport.ksdJobName) &&
        Objects.equals(this.ksdName, hwSBossKSDConfigReport.ksdName) &&
        Objects.equals(this.lotusNotesId, hwSBossKSDConfigReport.lotusNotesId) &&
        Objects.equals(this.maestroTaskId, hwSBossKSDConfigReport.maestroTaskId) &&
        Objects.equals(this.maestroTicketId, hwSBossKSDConfigReport.maestroTicketId) &&
        Objects.equals(this.owner, hwSBossKSDConfigReport.owner) &&
        Objects.equals(this.pjmId, hwSBossKSDConfigReport.pjmId) &&
        Objects.equals(this.processName, hwSBossKSDConfigReport.processName) &&
        Objects.equals(this.processType, hwSBossKSDConfigReport.processType) &&
        Objects.equals(this.rulesId, hwSBossKSDConfigReport.rulesId) &&
        Objects.equals(this.tbaInquiryId, hwSBossKSDConfigReport.tbaInquiryId) &&
        Objects.equals(this.tbaUpdateId, hwSBossKSDConfigReport.tbaUpdateId) &&
        Objects.equals(this.tower, hwSBossKSDConfigReport.tower) &&
        Objects.equals(this.updatedBy, hwSBossKSDConfigReport.updatedBy) &&
        Objects.equals(this.updatedOn, hwSBossKSDConfigReport.updatedOn);
  }

  @Override
  public int hashCode() {
    return Objects.hash(approvedBy, approvedOn, businessUnit, clientCode, clientName, configStatus, createdBy, createdOn, ksdEftSubjectName, ksdJobName, ksdName, lotusNotesId, maestroTaskId, maestroTicketId, owner, pjmId, processName, processType, rulesId, tbaInquiryId, tbaUpdateId, tower, updatedBy, updatedOn);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HWSBossKSDConfigReport {\n");
    
    sb.append("    approvedBy: ").append(toIndentedString(approvedBy)).append("\n");
    sb.append("    approvedOn: ").append(toIndentedString(approvedOn)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    configStatus: ").append(toIndentedString(configStatus)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
    sb.append("    ksdEftSubjectName: ").append(toIndentedString(ksdEftSubjectName)).append("\n");
    sb.append("    ksdJobName: ").append(toIndentedString(ksdJobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    lotusNotesId: ").append(toIndentedString(lotusNotesId)).append("\n");
    sb.append("    maestroTaskId: ").append(toIndentedString(maestroTaskId)).append("\n");
    sb.append("    maestroTicketId: ").append(toIndentedString(maestroTicketId)).append("\n");
    sb.append("    owner: ").append(toIndentedString(owner)).append("\n");
    sb.append("    pjmId: ").append(toIndentedString(pjmId)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    rulesId: ").append(toIndentedString(rulesId)).append("\n");
    sb.append("    tbaInquiryId: ").append(toIndentedString(tbaInquiryId)).append("\n");
    sb.append("    tbaUpdateId: ").append(toIndentedString(tbaUpdateId)).append("\n");
    sb.append("    tower: ").append(toIndentedString(tower)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedOn: ").append(toIndentedString(updatedOn)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

