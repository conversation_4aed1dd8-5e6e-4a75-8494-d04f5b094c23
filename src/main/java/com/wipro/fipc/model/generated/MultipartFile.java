/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.InputStream;
import com.wipro.fipc.model.generated.Resource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * MultipartFile
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class MultipartFile   {
  @JsonProperty("bytes")
  private List<byte[]> bytes = new ArrayList<byte[]>();

  @JsonProperty("contentType")
  private String contentType = null;

  @JsonProperty("empty")
  private Boolean empty = null;

  @JsonProperty("inputStream")
  private InputStream inputStream = null;

  @JsonProperty("name")
  private String name = null;

  @JsonProperty("originalFilename")
  private String originalFilename = null;

  @JsonProperty("resource")
  private Resource resource = null;

  @JsonProperty("size")
  private Long size = null;

  public MultipartFile bytes(List<byte[]> bytes) {
    this.bytes = bytes;
    return this;
  }

  public MultipartFile addBytesItem(byte[] bytesItem) {
    this.bytes.add(bytesItem);
    return this;
  }

   /**
   * Get bytes
   * @return bytes
  **/
  @JsonProperty("bytes")
  @ApiModelProperty(value = "")
  public List<byte[]> getBytes() {
    return bytes;
  }

  public void setBytes(List<byte[]> bytes) {
    this.bytes = bytes;
  }

  public MultipartFile contentType(String contentType) {
    this.contentType = contentType;
    return this;
  }

   /**
   * Get contentType
   * @return contentType
  **/
  @JsonProperty("contentType")
  @ApiModelProperty(value = "")
  public String getContentType() {
    return contentType;
  }

  public void setContentType(String contentType) {
    this.contentType = contentType;
  }

  public MultipartFile empty(Boolean empty) {
    this.empty = empty;
    return this;
  }

   /**
   * Get empty
   * @return empty
  **/
  @JsonProperty("empty")
  @ApiModelProperty(value = "")
  public Boolean getEmpty() {
    return empty;
  }

  public void setEmpty(Boolean empty) {
    this.empty = empty;
  }

  public MultipartFile inputStream(InputStream inputStream) {
    this.inputStream = inputStream;
    return this;
  }

   /**
   * Get inputStream
   * @return inputStream
  **/
  @JsonProperty("inputStream")
  @ApiModelProperty(value = "")
  public InputStream getInputStream() {
    return inputStream;
  }

  public void setInputStream(InputStream inputStream) {
    this.inputStream = inputStream;
  }

  public MultipartFile name(String name) {
    this.name = name;
    return this;
  }

   /**
   * Get name
   * @return name
  **/
  @JsonProperty("name")
  @ApiModelProperty(value = "")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public MultipartFile originalFilename(String originalFilename) {
    this.originalFilename = originalFilename;
    return this;
  }

   /**
   * Get originalFilename
   * @return originalFilename
  **/
  @JsonProperty("originalFilename")
  @ApiModelProperty(value = "")
  public String getOriginalFilename() {
    return originalFilename;
  }

  public void setOriginalFilename(String originalFilename) {
    this.originalFilename = originalFilename;
  }

  public MultipartFile resource(Resource resource) {
    this.resource = resource;
    return this;
  }

   /**
   * Get resource
   * @return resource
  **/
  @JsonProperty("resource")
  @ApiModelProperty(value = "")
  public Resource getResource() {
    return resource;
  }

  public void setResource(Resource resource) {
    this.resource = resource;
  }

  public MultipartFile size(Long size) {
    this.size = size;
    return this;
  }

   /**
   * Get size
   * @return size
  **/
  @JsonProperty("size")
  @ApiModelProperty(value = "")
  public Long getSize() {
    return size;
  }

  public void setSize(Long size) {
    this.size = size;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MultipartFile multipartFile = (MultipartFile) o;
    return Objects.equals(this.bytes, multipartFile.bytes) &&
        Objects.equals(this.contentType, multipartFile.contentType) &&
        Objects.equals(this.empty, multipartFile.empty) &&
        Objects.equals(this.inputStream, multipartFile.inputStream) &&
        Objects.equals(this.name, multipartFile.name) &&
        Objects.equals(this.originalFilename, multipartFile.originalFilename) &&
        Objects.equals(this.resource, multipartFile.resource) &&
        Objects.equals(this.size, multipartFile.size);
  }

  @Override
  public int hashCode() {
    return Objects.hash(bytes, contentType, empty, inputStream, name, originalFilename, resource, size);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MultipartFile {\n");
    
    sb.append("    bytes: ").append(toIndentedString(bytes)).append("\n");
    sb.append("    contentType: ").append(toIndentedString(contentType)).append("\n");
    sb.append("    empty: ").append(toIndentedString(empty)).append("\n");
    sb.append("    inputStream: ").append(toIndentedString(inputStream)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    originalFilename: ").append(toIndentedString(originalFilename)).append("\n");
    sb.append("    resource: ").append(toIndentedString(resource)).append("\n");
    sb.append("    size: ").append(toIndentedString(size)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

