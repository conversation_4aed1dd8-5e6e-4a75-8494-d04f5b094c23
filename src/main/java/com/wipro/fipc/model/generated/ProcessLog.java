/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import javax.validation.constraints.*;

/**
 * ProcessLog
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ProcessLog   {
  @JsonProperty("botId")
  private String botId = null;

  @JsonProperty("elementType")
  private String elementType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("timestamp")
  private Timestamp timestamp = null;

  @JsonProperty("uid")
  private String uid = null;

  @JsonProperty("value")
  private String value = null;

  public ProcessLog botId(String botId) {
    this.botId = botId;
    return this;
  }

   /**
   * Get botId
   * @return botId
  **/
  @JsonProperty("botId")
  @ApiModelProperty(value = "")
  public String getBotId() {
    return botId;
  }

  public void setBotId(String botId) {
    this.botId = botId;
  }

  public ProcessLog elementType(String elementType) {
    this.elementType = elementType;
    return this;
  }

   /**
   * Get elementType
   * @return elementType
  **/
  @JsonProperty("elementType")
  @ApiModelProperty(value = "")
  public String getElementType() {
    return elementType;
  }

  public void setElementType(String elementType) {
    this.elementType = elementType;
  }

  public ProcessLog id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ProcessLog processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public ProcessLog timestamp(Timestamp timestamp) {
    this.timestamp = timestamp;
    return this;
  }

   /**
   * Get timestamp
   * @return timestamp
  **/
  @JsonProperty("timestamp")
  @ApiModelProperty(value = "")
  public Timestamp getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Timestamp timestamp) {
    this.timestamp = timestamp;
  }

  public ProcessLog uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public ProcessLog value(String value) {
    this.value = value;
    return this;
  }

   /**
   * Get value
   * @return value
  **/
  @JsonProperty("value")
  @ApiModelProperty(value = "")
  public String getValue() {
    return value;
  }

  public void setValue(String value) {
    this.value = value;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessLog processLog = (ProcessLog) o;
    return Objects.equals(this.botId, processLog.botId) &&
        Objects.equals(this.elementType, processLog.elementType) &&
        Objects.equals(this.id, processLog.id) &&
        Objects.equals(this.processJobMappingId, processLog.processJobMappingId) &&
        Objects.equals(this.timestamp, processLog.timestamp) &&
        Objects.equals(this.uid, processLog.uid) &&
        Objects.equals(this.value, processLog.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(botId, elementType, id, processJobMappingId, timestamp, uid, value);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessLog {\n");
    
    sb.append("    botId: ").append(toIndentedString(botId)).append("\n");
    sb.append("    elementType: ").append(toIndentedString(elementType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    timestamp: ").append(toIndentedString(timestamp)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

