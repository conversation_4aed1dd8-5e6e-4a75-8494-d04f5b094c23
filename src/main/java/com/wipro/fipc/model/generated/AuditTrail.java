/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * AuditTrail
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class AuditTrail   {
  @JsonProperty("action")
  private String action = null;

  @JsonProperty("actionTimestamp")
  private Date actionTimestamp = null;

  @JsonProperty("actionType")
  private String actionType = null;

  @JsonProperty("appName")
  private String appName = null;

  @JsonProperty("description")
  private String description = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("identifierType")
  private String identifierType = null;

  @JsonProperty("misc")
  private String misc = null;

  @JsonProperty("origin")
  private String origin = null;

  public AuditTrail action(String action) {
    this.action = action;
    return this;
  }

   /**
   * Get action
   * @return action
  **/
  @JsonProperty("action")
  @ApiModelProperty(value = "")
  public String getAction() {
    return action;
  }

  public void setAction(String action) {
    this.action = action;
  }

  public AuditTrail actionTimestamp(Date actionTimestamp) {
    this.actionTimestamp = actionTimestamp;
    return this;
  }

   /**
   * Get actionTimestamp
   * @return actionTimestamp
  **/
  @JsonProperty("actionTimestamp")
  @ApiModelProperty(value = "")
  public Date getActionTimestamp() {
    return actionTimestamp;
  }

  public void setActionTimestamp(Date actionTimestamp) {
    this.actionTimestamp = actionTimestamp;
  }

  public AuditTrail actionType(String actionType) {
    this.actionType = actionType;
    return this;
  }

   /**
   * Get actionType
   * @return actionType
  **/
  @JsonProperty("actionType")
  @ApiModelProperty(value = "")
  public String getActionType() {
    return actionType;
  }

  public void setActionType(String actionType) {
    this.actionType = actionType;
  }

  public AuditTrail appName(String appName) {
    this.appName = appName;
    return this;
  }

   /**
   * Get appName
   * @return appName
  **/
  @JsonProperty("appName")
  @ApiModelProperty(value = "")
  public String getAppName() {
    return appName;
  }

  public void setAppName(String appName) {
    this.appName = appName;
  }

  public AuditTrail description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Get description
   * @return description
  **/
  @JsonProperty("description")
  @ApiModelProperty(value = "")
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public AuditTrail id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public AuditTrail identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public AuditTrail identifierType(String identifierType) {
    this.identifierType = identifierType;
    return this;
  }

   /**
   * Get identifierType
   * @return identifierType
  **/
  @JsonProperty("identifierType")
  @ApiModelProperty(value = "")
  public String getIdentifierType() {
    return identifierType;
  }

  public void setIdentifierType(String identifierType) {
    this.identifierType = identifierType;
  }

  public AuditTrail misc(String misc) {
    this.misc = misc;
    return this;
  }

   /**
   * Get misc
   * @return misc
  **/
  @JsonProperty("misc")
  @ApiModelProperty(value = "")
  public String getMisc() {
    return misc;
  }

  public void setMisc(String misc) {
    this.misc = misc;
  }

  public AuditTrail origin(String origin) {
    this.origin = origin;
    return this;
  }

   /**
   * Get origin
   * @return origin
  **/
  @JsonProperty("origin")
  @ApiModelProperty(value = "")
  public String getOrigin() {
    return origin;
  }

  public void setOrigin(String origin) {
    this.origin = origin;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AuditTrail auditTrail = (AuditTrail) o;
    return Objects.equals(this.action, auditTrail.action) &&
        Objects.equals(this.actionTimestamp, auditTrail.actionTimestamp) &&
        Objects.equals(this.actionType, auditTrail.actionType) &&
        Objects.equals(this.appName, auditTrail.appName) &&
        Objects.equals(this.description, auditTrail.description) &&
        Objects.equals(this.id, auditTrail.id) &&
        Objects.equals(this.identifier, auditTrail.identifier) &&
        Objects.equals(this.identifierType, auditTrail.identifierType) &&
        Objects.equals(this.misc, auditTrail.misc) &&
        Objects.equals(this.origin, auditTrail.origin);
  }

  @Override
  public int hashCode() {
    return Objects.hash(action, actionTimestamp, actionType, appName, description, id, identifier, identifierType, misc, origin);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AuditTrail {\n");
    
    sb.append("    action: ").append(toIndentedString(action)).append("\n");
    sb.append("    actionTimestamp: ").append(toIndentedString(actionTimestamp)).append("\n");
    sb.append("    actionType: ").append(toIndentedString(actionType)).append("\n");
    sb.append("    appName: ").append(toIndentedString(appName)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    identifierType: ").append(toIndentedString(identifierType)).append("\n");
    sb.append("    misc: ").append(toIndentedString(misc)).append("\n");
    sb.append("    origin: ").append(toIndentedString(origin)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

