/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.Character;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TemplateReportUploadPojo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class TemplateReportUploadPojo   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("buId")
  private Integer buId = null;

  @JsonProperty("clientId")
  private String clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("reportFlag")
  private Character reportFlag = null;

  @JsonProperty("templateReportName")
  private String templateReportName = null;

  @JsonProperty("templateReportNameWs")
  private String templateReportNameWs = null;

  @JsonProperty("type")
  private String type = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("uploadedBy")
  private String uploadedBy = null;

  @JsonProperty("uploadedDate")
  private Date uploadedDate = null;

  public TemplateReportUploadPojo activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TemplateReportUploadPojo buId(Integer buId) {
    this.buId = buId;
    return this;
  }

   /**
   * Get buId
   * @return buId
  **/
  @JsonProperty("buId")
  @ApiModelProperty(value = "")
  public Integer getBuId() {
    return buId;
  }

  public void setBuId(Integer buId) {
    this.buId = buId;
  }

  public TemplateReportUploadPojo clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public TemplateReportUploadPojo clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public TemplateReportUploadPojo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TemplateReportUploadPojo createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TemplateReportUploadPojo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TemplateReportUploadPojo reportFlag(Character reportFlag) {
    this.reportFlag = reportFlag;
    return this;
  }

   /**
   * Get reportFlag
   * @return reportFlag
  **/
  @JsonProperty("reportFlag")
  @ApiModelProperty(value = "")
  public Character getReportFlag() {
    return reportFlag;
  }

  public void setReportFlag(Character reportFlag) {
    this.reportFlag = reportFlag;
  }

  public TemplateReportUploadPojo templateReportName(String templateReportName) {
    this.templateReportName = templateReportName;
    return this;
  }

   /**
   * Get templateReportName
   * @return templateReportName
  **/
  @JsonProperty("templateReportName")
  @ApiModelProperty(value = "")
  public String getTemplateReportName() {
    return templateReportName;
  }

  public void setTemplateReportName(String templateReportName) {
    this.templateReportName = templateReportName;
  }

  public TemplateReportUploadPojo templateReportNameWs(String templateReportNameWs) {
    this.templateReportNameWs = templateReportNameWs;
    return this;
  }

   /**
   * Get templateReportNameWs
   * @return templateReportNameWs
  **/
  @JsonProperty("templateReportNameWs")
  @ApiModelProperty(value = "")
  public String getTemplateReportNameWs() {
    return templateReportNameWs;
  }

  public void setTemplateReportNameWs(String templateReportNameWs) {
    this.templateReportNameWs = templateReportNameWs;
  }

  public TemplateReportUploadPojo type(String type) {
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @JsonProperty("type")
  @ApiModelProperty(value = "")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public TemplateReportUploadPojo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TemplateReportUploadPojo updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public TemplateReportUploadPojo uploadedBy(String uploadedBy) {
    this.uploadedBy = uploadedBy;
    return this;
  }

   /**
   * Get uploadedBy
   * @return uploadedBy
  **/
  @JsonProperty("uploadedBy")
  @ApiModelProperty(value = "")
  public String getUploadedBy() {
    return uploadedBy;
  }

  public void setUploadedBy(String uploadedBy) {
    this.uploadedBy = uploadedBy;
  }

  public TemplateReportUploadPojo uploadedDate(Date uploadedDate) {
    this.uploadedDate = uploadedDate;
    return this;
  }

   /**
   * Get uploadedDate
   * @return uploadedDate
  **/
  @JsonProperty("uploadedDate")
  @ApiModelProperty(value = "")
  public Date getUploadedDate() {
    return uploadedDate;
  }

  public void setUploadedDate(Date uploadedDate) {
    this.uploadedDate = uploadedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TemplateReportUploadPojo templateReportUploadPojo = (TemplateReportUploadPojo) o;
    return Objects.equals(this.activeFlag, templateReportUploadPojo.activeFlag) &&
        Objects.equals(this.buId, templateReportUploadPojo.buId) &&
        Objects.equals(this.clientId, templateReportUploadPojo.clientId) &&
        Objects.equals(this.clientName, templateReportUploadPojo.clientName) &&
        Objects.equals(this.createdBy, templateReportUploadPojo.createdBy) &&
        Objects.equals(this.createdDate, templateReportUploadPojo.createdDate) &&
        Objects.equals(this.id, templateReportUploadPojo.id) &&
        Objects.equals(this.reportFlag, templateReportUploadPojo.reportFlag) &&
        Objects.equals(this.templateReportName, templateReportUploadPojo.templateReportName) &&
        Objects.equals(this.templateReportNameWs, templateReportUploadPojo.templateReportNameWs) &&
        Objects.equals(this.type, templateReportUploadPojo.type) &&
        Objects.equals(this.updatedBy, templateReportUploadPojo.updatedBy) &&
        Objects.equals(this.updatedDate, templateReportUploadPojo.updatedDate) &&
        Objects.equals(this.uploadedBy, templateReportUploadPojo.uploadedBy) &&
        Objects.equals(this.uploadedDate, templateReportUploadPojo.uploadedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, buId, clientId, clientName, createdBy, createdDate, id, reportFlag, templateReportName, templateReportNameWs, type, updatedBy, updatedDate, uploadedBy, uploadedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TemplateReportUploadPojo {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    buId: ").append(toIndentedString(buId)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    reportFlag: ").append(toIndentedString(reportFlag)).append("\n");
    sb.append("    templateReportName: ").append(toIndentedString(templateReportName)).append("\n");
    sb.append("    templateReportNameWs: ").append(toIndentedString(templateReportNameWs)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    uploadedBy: ").append(toIndentedString(uploadedBy)).append("\n");
    sb.append("    uploadedDate: ").append(toIndentedString(uploadedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

