/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * NotificationMailConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class NotificationMailConfig   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("appendSubject")
  private String appendSubject = null;

  @JsonProperty("attachmentName")
  private String attachmentName = null;

  @JsonProperty("ccList")
  private String ccList = null;

  @JsonProperty("condition")
  private String condition = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("interestedFields")
  private String interestedFields = null;

  @JsonProperty("mailBody")
  private String mailBody = null;

  @JsonProperty("password")
  private String password = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("remarks")
  private String remarks = null;

  @JsonProperty("subject")
  private String subject = null;

  @JsonProperty("toList")
  private String toList = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public NotificationMailConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public NotificationMailConfig appendSubject(String appendSubject) {
    this.appendSubject = appendSubject;
    return this;
  }

   /**
   * Get appendSubject
   * @return appendSubject
  **/
  @JsonProperty("appendSubject")
  @ApiModelProperty(value = "")
  public String getAppendSubject() {
    return appendSubject;
  }

  public void setAppendSubject(String appendSubject) {
    this.appendSubject = appendSubject;
  }

  public NotificationMailConfig attachmentName(String attachmentName) {
    this.attachmentName = attachmentName;
    return this;
  }

   /**
   * Get attachmentName
   * @return attachmentName
  **/
  @JsonProperty("attachmentName")
  @ApiModelProperty(value = "")
  public String getAttachmentName() {
    return attachmentName;
  }

  public void setAttachmentName(String attachmentName) {
    this.attachmentName = attachmentName;
  }

  public NotificationMailConfig ccList(String ccList) {
    this.ccList = ccList;
    return this;
  }

   /**
   * Get ccList
   * @return ccList
  **/
  @JsonProperty("ccList")
  @ApiModelProperty(value = "")
  public String getCcList() {
    return ccList;
  }

  public void setCcList(String ccList) {
    this.ccList = ccList;
  }

  public NotificationMailConfig condition(String condition) {
    this.condition = condition;
    return this;
  }

   /**
   * Get condition
   * @return condition
  **/
  @JsonProperty("condition")
  @ApiModelProperty(value = "")
  public String getCondition() {
    return condition;
  }

  public void setCondition(String condition) {
    this.condition = condition;
  }

  public NotificationMailConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public NotificationMailConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public NotificationMailConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public NotificationMailConfig interestedFields(String interestedFields) {
    this.interestedFields = interestedFields;
    return this;
  }

   /**
   * Get interestedFields
   * @return interestedFields
  **/
  @JsonProperty("interestedFields")
  @ApiModelProperty(value = "")
  public String getInterestedFields() {
    return interestedFields;
  }

  public void setInterestedFields(String interestedFields) {
    this.interestedFields = interestedFields;
  }

  public NotificationMailConfig mailBody(String mailBody) {
    this.mailBody = mailBody;
    return this;
  }

   /**
   * Get mailBody
   * @return mailBody
  **/
  @JsonProperty("mailBody")
  @ApiModelProperty(value = "")
  public String getMailBody() {
    return mailBody;
  }

  public void setMailBody(String mailBody) {
    this.mailBody = mailBody;
  }

  public NotificationMailConfig password(String password) {
    this.password = password;
    return this;
  }

   /**
   * Get password
   * @return password
  **/
  @JsonProperty("password")
  @ApiModelProperty(value = "")
  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public NotificationMailConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public NotificationMailConfig remarks(String remarks) {
    this.remarks = remarks;
    return this;
  }

   /**
   * Get remarks
   * @return remarks
  **/
  @JsonProperty("remarks")
  @ApiModelProperty(value = "")
  public String getRemarks() {
    return remarks;
  }

  public void setRemarks(String remarks) {
    this.remarks = remarks;
  }

  public NotificationMailConfig subject(String subject) {
    this.subject = subject;
    return this;
  }

   /**
   * Get subject
   * @return subject
  **/
  @JsonProperty("subject")
  @ApiModelProperty(value = "")
  public String getSubject() {
    return subject;
  }

  public void setSubject(String subject) {
    this.subject = subject;
  }

  public NotificationMailConfig toList(String toList) {
    this.toList = toList;
    return this;
  }

   /**
   * Get toList
   * @return toList
  **/
  @JsonProperty("toList")
  @ApiModelProperty(value = "")
  public String getToList() {
    return toList;
  }

  public void setToList(String toList) {
    this.toList = toList;
  }

  public NotificationMailConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public NotificationMailConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NotificationMailConfig notificationMailConfig = (NotificationMailConfig) o;
    return Objects.equals(this.activeFlag, notificationMailConfig.activeFlag) &&
        Objects.equals(this.appendSubject, notificationMailConfig.appendSubject) &&
        Objects.equals(this.attachmentName, notificationMailConfig.attachmentName) &&
        Objects.equals(this.ccList, notificationMailConfig.ccList) &&
        Objects.equals(this.condition, notificationMailConfig.condition) &&
        Objects.equals(this.createdBy, notificationMailConfig.createdBy) &&
        Objects.equals(this.createdDate, notificationMailConfig.createdDate) &&
        Objects.equals(this.id, notificationMailConfig.id) &&
        Objects.equals(this.interestedFields, notificationMailConfig.interestedFields) &&
        Objects.equals(this.mailBody, notificationMailConfig.mailBody) &&
        Objects.equals(this.password, notificationMailConfig.password) &&
        Objects.equals(this.processJobMapping, notificationMailConfig.processJobMapping) &&
        Objects.equals(this.remarks, notificationMailConfig.remarks) &&
        Objects.equals(this.subject, notificationMailConfig.subject) &&
        Objects.equals(this.toList, notificationMailConfig.toList) &&
        Objects.equals(this.updatedBy, notificationMailConfig.updatedBy) &&
        Objects.equals(this.updatedDate, notificationMailConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, appendSubject, attachmentName, ccList, condition, createdBy, createdDate, id, interestedFields, mailBody, password, processJobMapping, remarks, subject, toList, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NotificationMailConfig {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    appendSubject: ").append(toIndentedString(appendSubject)).append("\n");
    sb.append("    attachmentName: ").append(toIndentedString(attachmentName)).append("\n");
    sb.append("    ccList: ").append(toIndentedString(ccList)).append("\n");
    sb.append("    condition: ").append(toIndentedString(condition)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    interestedFields: ").append(toIndentedString(interestedFields)).append("\n");
    sb.append("    mailBody: ").append(toIndentedString(mailBody)).append("\n");
    sb.append("    password: ").append(toIndentedString(password)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    remarks: ").append(toIndentedString(remarks)).append("\n");
    sb.append("    subject: ").append(toIndentedString(subject)).append("\n");
    sb.append("    toList: ").append(toIndentedString(toList)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

