/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaProcessInquiry
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaProcessInquiry   {
  @JsonProperty("clientId")
  private Integer clientId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("inquiryName")
  private String inquiryName = null;

  @JsonProperty("panelId")
  private Integer panelId = null;

  @JsonProperty("parNM")
  private String parNM = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaProcessInquiry clientId(Integer clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Integer getClientId() {
    return clientId;
  }

  public void setClientId(Integer clientId) {
    this.clientId = clientId;
  }

  public TbaProcessInquiry createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaProcessInquiry createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaProcessInquiry id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaProcessInquiry inquiryName(String inquiryName) {
    this.inquiryName = inquiryName;
    return this;
  }

   /**
   * Get inquiryName
   * @return inquiryName
  **/
  @JsonProperty("inquiryName")
  @ApiModelProperty(value = "")
  public String getInquiryName() {
    return inquiryName;
  }

  public void setInquiryName(String inquiryName) {
    this.inquiryName = inquiryName;
  }

  public TbaProcessInquiry panelId(Integer panelId) {
    this.panelId = panelId;
    return this;
  }

   /**
   * Get panelId
   * @return panelId
  **/
  @JsonProperty("panelId")
  @ApiModelProperty(value = "")
  public Integer getPanelId() {
    return panelId;
  }

  public void setPanelId(Integer panelId) {
    this.panelId = panelId;
  }

  public TbaProcessInquiry parNM(String parNM) {
    this.parNM = parNM;
    return this;
  }

   /**
   * Get parNM
   * @return parNM
  **/
  @JsonProperty("parNM")
  @ApiModelProperty(value = "")
  public String getParNM() {
    return parNM;
  }

  public void setParNM(String parNM) {
    this.parNM = parNM;
  }

  public TbaProcessInquiry processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public TbaProcessInquiry updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaProcessInquiry updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaProcessInquiry tbaProcessInquiry = (TbaProcessInquiry) o;
    return Objects.equals(this.clientId, tbaProcessInquiry.clientId) &&
        Objects.equals(this.createdBy, tbaProcessInquiry.createdBy) &&
        Objects.equals(this.createdDate, tbaProcessInquiry.createdDate) &&
        Objects.equals(this.id, tbaProcessInquiry.id) &&
        Objects.equals(this.inquiryName, tbaProcessInquiry.inquiryName) &&
        Objects.equals(this.panelId, tbaProcessInquiry.panelId) &&
        Objects.equals(this.parNM, tbaProcessInquiry.parNM) &&
        Objects.equals(this.processName, tbaProcessInquiry.processName) &&
        Objects.equals(this.updatedBy, tbaProcessInquiry.updatedBy) &&
        Objects.equals(this.updatedDate, tbaProcessInquiry.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(clientId, createdBy, createdDate, id, inquiryName, panelId, parNM, processName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaProcessInquiry {\n");
    
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    inquiryName: ").append(toIndentedString(inquiryName)).append("\n");
    sb.append("    panelId: ").append(toIndentedString(panelId)).append("\n");
    sb.append("    parNM: ").append(toIndentedString(parNM)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

