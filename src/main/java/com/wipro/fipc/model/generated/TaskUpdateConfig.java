/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TaskUpdateConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2024-11-07T16:38:48.053+05:30")
public class TaskUpdateConfig   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("attachment")
  private String attachment = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("interestedFields")
  private String interestedFields = null;

  @JsonProperty("maestroTaskName")
  private String maestroTaskName = null;

  @JsonProperty("newDiscussion")
  private String newDiscussion = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("questionnaire")
  private String questionnaire = null;

  @JsonProperty("type")
  private String type = null;

  @JsonProperty("unsecuredAttachment")
  private String unsecuredAttachment = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TaskUpdateConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TaskUpdateConfig attachment(String attachment) {
    this.attachment = attachment;
    return this;
  }

   /**
   * Get attachment
   * @return attachment
  **/
  @JsonProperty("attachment")
  @ApiModelProperty(value = "")
  public String getAttachment() {
    return attachment;
  }

  public void setAttachment(String attachment) {
    this.attachment = attachment;
  }

  public TaskUpdateConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TaskUpdateConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TaskUpdateConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TaskUpdateConfig interestedFields(String interestedFields) {
    this.interestedFields = interestedFields;
    return this;
  }

   /**
   * Get interestedFields
   * @return interestedFields
  **/
  @JsonProperty("interestedFields")
  @ApiModelProperty(value = "")
  public String getInterestedFields() {
    return interestedFields;
  }

  public void setInterestedFields(String interestedFields) {
    this.interestedFields = interestedFields;
  }

  public TaskUpdateConfig maestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
    return this;
  }

   /**
   * Get maestroTaskName
   * @return maestroTaskName
  **/
  @JsonProperty("maestroTaskName")
  @ApiModelProperty(value = "")
  public String getMaestroTaskName() {
    return maestroTaskName;
  }

  public void setMaestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
  }

  public TaskUpdateConfig newDiscussion(String newDiscussion) {
    this.newDiscussion = newDiscussion;
    return this;
  }

   /**
   * Get newDiscussion
   * @return newDiscussion
  **/
  @JsonProperty("newDiscussion")
  @ApiModelProperty(value = "")
  public String getNewDiscussion() {
    return newDiscussion;
  }

  public void setNewDiscussion(String newDiscussion) {
    this.newDiscussion = newDiscussion;
  }

  public TaskUpdateConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TaskUpdateConfig questionnaire(String questionnaire) {
    this.questionnaire = questionnaire;
    return this;
  }

   /**
   * Get questionnaire
   * @return questionnaire
  **/
  @JsonProperty("questionnaire")
  @ApiModelProperty(value = "")
  public String getQuestionnaire() {
    return questionnaire;
  }

  public void setQuestionnaire(String questionnaire) {
    this.questionnaire = questionnaire;
  }

  public TaskUpdateConfig type(String type) {
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @JsonProperty("type")
  @ApiModelProperty(value = "")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public TaskUpdateConfig unsecuredAttachment(String unsecuredAttachment) {
    this.unsecuredAttachment = unsecuredAttachment;
    return this;
  }

   /**
   * Get unsecuredAttachment
   * @return unsecuredAttachment
  **/
  @JsonProperty("unsecuredAttachment")
  @ApiModelProperty(value = "")
  public String getUnsecuredAttachment() {
    return unsecuredAttachment;
  }

  public void setUnsecuredAttachment(String unsecuredAttachment) {
    this.unsecuredAttachment = unsecuredAttachment;
  }

  public TaskUpdateConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TaskUpdateConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskUpdateConfig taskUpdateConfig = (TaskUpdateConfig) o;
    return Objects.equals(this.activeFlag, taskUpdateConfig.activeFlag) &&
        Objects.equals(this.attachment, taskUpdateConfig.attachment) &&
        Objects.equals(this.createdBy, taskUpdateConfig.createdBy) &&
        Objects.equals(this.createdDate, taskUpdateConfig.createdDate) &&
        Objects.equals(this.id, taskUpdateConfig.id) &&
        Objects.equals(this.interestedFields, taskUpdateConfig.interestedFields) &&
        Objects.equals(this.maestroTaskName, taskUpdateConfig.maestroTaskName) &&
        Objects.equals(this.newDiscussion, taskUpdateConfig.newDiscussion) &&
        Objects.equals(this.processJobMapping, taskUpdateConfig.processJobMapping) &&
        Objects.equals(this.questionnaire, taskUpdateConfig.questionnaire) &&
        Objects.equals(this.type, taskUpdateConfig.type) &&
        Objects.equals(this.unsecuredAttachment, taskUpdateConfig.unsecuredAttachment) &&
        Objects.equals(this.updatedBy, taskUpdateConfig.updatedBy) &&
        Objects.equals(this.updatedDate, taskUpdateConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, attachment, createdBy, createdDate, id, interestedFields, maestroTaskName, newDiscussion, processJobMapping, questionnaire, type, unsecuredAttachment, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskUpdateConfig {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    attachment: ").append(toIndentedString(attachment)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    interestedFields: ").append(toIndentedString(interestedFields)).append("\n");
    sb.append("    maestroTaskName: ").append(toIndentedString(maestroTaskName)).append("\n");
    sb.append("    newDiscussion: ").append(toIndentedString(newDiscussion)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    questionnaire: ").append(toIndentedString(questionnaire)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    unsecuredAttachment: ").append(toIndentedString(unsecuredAttachment)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

