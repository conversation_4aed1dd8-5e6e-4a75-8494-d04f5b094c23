/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * CustomBusinessUnitBO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class CustomBusinessUnitBO   {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("unitCode")
  private String unitCode = null;

  @JsonProperty("unitName")
  private String unitName = null;

  public CustomBusinessUnitBO id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public CustomBusinessUnitBO unitCode(String unitCode) {
    this.unitCode = unitCode;
    return this;
  }

   /**
   * Get unitCode
   * @return unitCode
  **/
  @JsonProperty("unitCode")
  @ApiModelProperty(value = "")
  public String getUnitCode() {
    return unitCode;
  }

  public void setUnitCode(String unitCode) {
    this.unitCode = unitCode;
  }

  public CustomBusinessUnitBO unitName(String unitName) {
    this.unitName = unitName;
    return this;
  }

   /**
   * Get unitName
   * @return unitName
  **/
  @JsonProperty("unitName")
  @ApiModelProperty(value = "")
  public String getUnitName() {
    return unitName;
  }

  public void setUnitName(String unitName) {
    this.unitName = unitName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomBusinessUnitBO customBusinessUnitBO = (CustomBusinessUnitBO) o;
    return Objects.equals(this.id, customBusinessUnitBO.id) &&
        Objects.equals(this.unitCode, customBusinessUnitBO.unitCode) &&
        Objects.equals(this.unitName, customBusinessUnitBO.unitName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, unitCode, unitName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomBusinessUnitBO {\n");
    
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    unitCode: ").append(toIndentedString(unitCode)).append("\n");
    sb.append("    unitName: ").append(toIndentedString(unitName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

