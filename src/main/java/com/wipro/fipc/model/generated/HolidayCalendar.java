/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ClientDetails;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * HolidayCalendar
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class HolidayCalendar   {
  @JsonProperty("clientDetails")
  private ClientDetails clientDetails = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("holidayDate")
  private String holidayDate = null;

  @JsonProperty("holidayDesc")
  private String holidayDesc = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public HolidayCalendar clientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
    return this;
  }

   /**
   * Get clientDetails
   * @return clientDetails
  **/
  @JsonProperty("clientDetails")
  @ApiModelProperty(value = "")
  public ClientDetails getClientDetails() {
    return clientDetails;
  }

  public void setClientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
  }

  public HolidayCalendar createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public HolidayCalendar createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public HolidayCalendar holidayDate(String holidayDate) {
    this.holidayDate = holidayDate;
    return this;
  }

   /**
   * Get holidayDate
   * @return holidayDate
  **/
  @JsonProperty("holidayDate")
  @ApiModelProperty(value = "")
  public String getHolidayDate() {
    return holidayDate;
  }

  public void setHolidayDate(String holidayDate) {
    this.holidayDate = holidayDate;
  }

  public HolidayCalendar holidayDesc(String holidayDesc) {
    this.holidayDesc = holidayDesc;
    return this;
  }

   /**
   * Get holidayDesc
   * @return holidayDesc
  **/
  @JsonProperty("holidayDesc")
  @ApiModelProperty(value = "")
  public String getHolidayDesc() {
    return holidayDesc;
  }

  public void setHolidayDesc(String holidayDesc) {
    this.holidayDesc = holidayDesc;
  }

  public HolidayCalendar id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public HolidayCalendar updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public HolidayCalendar updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HolidayCalendar holidayCalendar = (HolidayCalendar) o;
    return Objects.equals(this.clientDetails, holidayCalendar.clientDetails) &&
        Objects.equals(this.createdBy, holidayCalendar.createdBy) &&
        Objects.equals(this.createdDate, holidayCalendar.createdDate) &&
        Objects.equals(this.holidayDate, holidayCalendar.holidayDate) &&
        Objects.equals(this.holidayDesc, holidayCalendar.holidayDesc) &&
        Objects.equals(this.id, holidayCalendar.id) &&
        Objects.equals(this.updatedBy, holidayCalendar.updatedBy) &&
        Objects.equals(this.updatedDate, holidayCalendar.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(clientDetails, createdBy, createdDate, holidayDate, holidayDesc, id, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HolidayCalendar {\n");
    
    sb.append("    clientDetails: ").append(toIndentedString(clientDetails)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    holidayDate: ").append(toIndentedString(holidayDate)).append("\n");
    sb.append("    holidayDesc: ").append(toIndentedString(holidayDesc)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

