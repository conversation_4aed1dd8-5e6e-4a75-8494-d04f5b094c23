/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * OutputReport
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class OutputReport   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("addOn")
  private String addOn = null;

  @JsonProperty("cellName")
  private String cellName = null;

  @JsonProperty("cellValue")
  private String cellValue = null;

  @JsonProperty("childElement")
  private String childElement = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("dataElement")
  private String dataElement = null;

  @JsonProperty("dataElementWoutSpace")
  private String dataElementWoutSpace = null;

  @JsonProperty("dataFormat")
  private String dataFormat = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("total")
  private String total = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public OutputReport activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public OutputReport addOn(String addOn) {
    this.addOn = addOn;
    return this;
  }

   /**
   * Get addOn
   * @return addOn
  **/
  @JsonProperty("addOn")
  @ApiModelProperty(value = "")
  public String getAddOn() {
    return addOn;
  }

  public void setAddOn(String addOn) {
    this.addOn = addOn;
  }

  public OutputReport cellName(String cellName) {
    this.cellName = cellName;
    return this;
  }

   /**
   * Get cellName
   * @return cellName
  **/
  @JsonProperty("cellName")
  @ApiModelProperty(value = "")
  public String getCellName() {
    return cellName;
  }

  public void setCellName(String cellName) {
    this.cellName = cellName;
  }

  public OutputReport cellValue(String cellValue) {
    this.cellValue = cellValue;
    return this;
  }

   /**
   * Get cellValue
   * @return cellValue
  **/
  @JsonProperty("cellValue")
  @ApiModelProperty(value = "")
  public String getCellValue() {
    return cellValue;
  }

  public void setCellValue(String cellValue) {
    this.cellValue = cellValue;
  }

  public OutputReport childElement(String childElement) {
    this.childElement = childElement;
    return this;
  }

   /**
   * Get childElement
   * @return childElement
  **/
  @JsonProperty("childElement")
  @ApiModelProperty(value = "")
  public String getChildElement() {
    return childElement;
  }

  public void setChildElement(String childElement) {
    this.childElement = childElement;
  }

  public OutputReport createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OutputReport createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public OutputReport dataElement(String dataElement) {
    this.dataElement = dataElement;
    return this;
  }

   /**
   * Get dataElement
   * @return dataElement
  **/
  @JsonProperty("dataElement")
  @ApiModelProperty(value = "")
  public String getDataElement() {
    return dataElement;
  }

  public void setDataElement(String dataElement) {
    this.dataElement = dataElement;
  }

  public OutputReport dataElementWoutSpace(String dataElementWoutSpace) {
    this.dataElementWoutSpace = dataElementWoutSpace;
    return this;
  }

   /**
   * Get dataElementWoutSpace
   * @return dataElementWoutSpace
  **/
  @JsonProperty("dataElementWoutSpace")
  @ApiModelProperty(value = "")
  public String getDataElementWoutSpace() {
    return dataElementWoutSpace;
  }

  public void setDataElementWoutSpace(String dataElementWoutSpace) {
    this.dataElementWoutSpace = dataElementWoutSpace;
  }

  public OutputReport dataFormat(String dataFormat) {
    this.dataFormat = dataFormat;
    return this;
  }

   /**
   * Get dataFormat
   * @return dataFormat
  **/
  @JsonProperty("dataFormat")
  @ApiModelProperty(value = "")
  public String getDataFormat() {
    return dataFormat;
  }

  public void setDataFormat(String dataFormat) {
    this.dataFormat = dataFormat;
  }

  public OutputReport fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public OutputReport id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public OutputReport processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public OutputReport recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public OutputReport total(String total) {
    this.total = total;
    return this;
  }

   /**
   * Get total
   * @return total
  **/
  @JsonProperty("total")
  @ApiModelProperty(value = "")
  public String getTotal() {
    return total;
  }

  public void setTotal(String total) {
    this.total = total;
  }

  public OutputReport updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OutputReport updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OutputReport outputReport = (OutputReport) o;
    return Objects.equals(this.activeFlag, outputReport.activeFlag) &&
        Objects.equals(this.addOn, outputReport.addOn) &&
        Objects.equals(this.cellName, outputReport.cellName) &&
        Objects.equals(this.cellValue, outputReport.cellValue) &&
        Objects.equals(this.childElement, outputReport.childElement) &&
        Objects.equals(this.createdBy, outputReport.createdBy) &&
        Objects.equals(this.createdDate, outputReport.createdDate) &&
        Objects.equals(this.dataElement, outputReport.dataElement) &&
        Objects.equals(this.dataElementWoutSpace, outputReport.dataElementWoutSpace) &&
        Objects.equals(this.dataFormat, outputReport.dataFormat) &&
        Objects.equals(this.fileName, outputReport.fileName) &&
        Objects.equals(this.id, outputReport.id) &&
        Objects.equals(this.processJobMapping, outputReport.processJobMapping) &&
        Objects.equals(this.recordIdentifier, outputReport.recordIdentifier) &&
        Objects.equals(this.total, outputReport.total) &&
        Objects.equals(this.updatedBy, outputReport.updatedBy) &&
        Objects.equals(this.updatedDate, outputReport.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, addOn, cellName, cellValue, childElement, createdBy, createdDate, dataElement, dataElementWoutSpace, dataFormat, fileName, id, processJobMapping, recordIdentifier, total, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OutputReport {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    addOn: ").append(toIndentedString(addOn)).append("\n");
    sb.append("    cellName: ").append(toIndentedString(cellName)).append("\n");
    sb.append("    cellValue: ").append(toIndentedString(cellValue)).append("\n");
    sb.append("    childElement: ").append(toIndentedString(childElement)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    dataElement: ").append(toIndentedString(dataElement)).append("\n");
    sb.append("    dataElementWoutSpace: ").append(toIndentedString(dataElementWoutSpace)).append("\n");
    sb.append("    dataFormat: ").append(toIndentedString(dataFormat)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

