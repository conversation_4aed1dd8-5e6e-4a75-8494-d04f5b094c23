/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * CustomEftJobNameBO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class CustomEftJobNameBO   {
  @JsonProperty("eftSubject")
  private String eftSubject = null;

  @JsonProperty("jobName")
  private String jobName = null;

  public CustomEftJobNameBO eftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
    return this;
  }

   /**
   * Get eftSubject
   * @return eftSubject
  **/
  @JsonProperty("eftSubject")
  @ApiModelProperty(value = "")
  public String getEftSubject() {
    return eftSubject;
  }

  public void setEftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
  }

  public CustomEftJobNameBO jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomEftJobNameBO customEftJobNameBO = (CustomEftJobNameBO) o;
    return Objects.equals(this.eftSubject, customEftJobNameBO.eftSubject) &&
        Objects.equals(this.jobName, customEftJobNameBO.jobName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(eftSubject, jobName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomEftJobNameBO {\n");
    
    sb.append("    eftSubject: ").append(toIndentedString(eftSubject)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

