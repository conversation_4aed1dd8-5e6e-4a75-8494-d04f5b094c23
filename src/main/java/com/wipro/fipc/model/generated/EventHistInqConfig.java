/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * EventHistInqConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class EventHistInqConfig   {
  @JsonProperty("actLongDesc")
  private String actLongDesc = null;

  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("activityId")
  private Integer activityId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("effFromDate")
  private String effFromDate = null;

  @JsonProperty("effToDate")
  private String effToDate = null;

  @JsonProperty("eventHistDefName")
  private String eventHistDefName = null;

  @JsonProperty("eventName")
  private String eventName = null;

  @JsonProperty("filedType")
  private String filedType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("manualFlag")
  private String manualFlag = null;

  @JsonProperty("parNm")
  private String parNm = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("tbaFiledName")
  private String tbaFiledName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public EventHistInqConfig actLongDesc(String actLongDesc) {
    this.actLongDesc = actLongDesc;
    return this;
  }

   /**
   * Get actLongDesc
   * @return actLongDesc
  **/
  @JsonProperty("actLongDesc")
  @ApiModelProperty(value = "")
  public String getActLongDesc() {
    return actLongDesc;
  }

  public void setActLongDesc(String actLongDesc) {
    this.actLongDesc = actLongDesc;
  }

  public EventHistInqConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public EventHistInqConfig activityId(Integer activityId) {
    this.activityId = activityId;
    return this;
  }

   /**
   * Get activityId
   * @return activityId
  **/
  @JsonProperty("activityId")
  @ApiModelProperty(value = "")
  public Integer getActivityId() {
    return activityId;
  }

  public void setActivityId(Integer activityId) {
    this.activityId = activityId;
  }

  public EventHistInqConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public EventHistInqConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public EventHistInqConfig effFromDate(String effFromDate) {
    this.effFromDate = effFromDate;
    return this;
  }

   /**
   * Get effFromDate
   * @return effFromDate
  **/
  @JsonProperty("effFromDate")
  @ApiModelProperty(value = "")
  public String getEffFromDate() {
    return effFromDate;
  }

  public void setEffFromDate(String effFromDate) {
    this.effFromDate = effFromDate;
  }

  public EventHistInqConfig effToDate(String effToDate) {
    this.effToDate = effToDate;
    return this;
  }

   /**
   * Get effToDate
   * @return effToDate
  **/
  @JsonProperty("effToDate")
  @ApiModelProperty(value = "")
  public String getEffToDate() {
    return effToDate;
  }

  public void setEffToDate(String effToDate) {
    this.effToDate = effToDate;
  }

  public EventHistInqConfig eventHistDefName(String eventHistDefName) {
    this.eventHistDefName = eventHistDefName;
    return this;
  }

   /**
   * Get eventHistDefName
   * @return eventHistDefName
  **/
  @JsonProperty("eventHistDefName")
  @ApiModelProperty(value = "")
  public String getEventHistDefName() {
    return eventHistDefName;
  }

  public void setEventHistDefName(String eventHistDefName) {
    this.eventHistDefName = eventHistDefName;
  }

  public EventHistInqConfig eventName(String eventName) {
    this.eventName = eventName;
    return this;
  }

   /**
   * Get eventName
   * @return eventName
  **/
  @JsonProperty("eventName")
  @ApiModelProperty(value = "")
  public String getEventName() {
    return eventName;
  }

  public void setEventName(String eventName) {
    this.eventName = eventName;
  }

  public EventHistInqConfig filedType(String filedType) {
    this.filedType = filedType;
    return this;
  }

   /**
   * Get filedType
   * @return filedType
  **/
  @JsonProperty("filedType")
  @ApiModelProperty(value = "")
  public String getFiledType() {
    return filedType;
  }

  public void setFiledType(String filedType) {
    this.filedType = filedType;
  }

  public EventHistInqConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public EventHistInqConfig jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public EventHistInqConfig manualFlag(String manualFlag) {
    this.manualFlag = manualFlag;
    return this;
  }

   /**
   * Get manualFlag
   * @return manualFlag
  **/
  @JsonProperty("manualFlag")
  @ApiModelProperty(value = "")
  public String getManualFlag() {
    return manualFlag;
  }

  public void setManualFlag(String manualFlag) {
    this.manualFlag = manualFlag;
  }

  public EventHistInqConfig parNm(String parNm) {
    this.parNm = parNm;
    return this;
  }

   /**
   * Get parNm
   * @return parNm
  **/
  @JsonProperty("parNm")
  @ApiModelProperty(value = "")
  public String getParNm() {
    return parNm;
  }

  public void setParNm(String parNm) {
    this.parNm = parNm;
  }

  public EventHistInqConfig processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public EventHistInqConfig tbaFiledName(String tbaFiledName) {
    this.tbaFiledName = tbaFiledName;
    return this;
  }

   /**
   * Get tbaFiledName
   * @return tbaFiledName
  **/
  @JsonProperty("tbaFiledName")
  @ApiModelProperty(value = "")
  public String getTbaFiledName() {
    return tbaFiledName;
  }

  public void setTbaFiledName(String tbaFiledName) {
    this.tbaFiledName = tbaFiledName;
  }

  public EventHistInqConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public EventHistInqConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EventHistInqConfig eventHistInqConfig = (EventHistInqConfig) o;
    return Objects.equals(this.actLongDesc, eventHistInqConfig.actLongDesc) &&
        Objects.equals(this.activeFlag, eventHistInqConfig.activeFlag) &&
        Objects.equals(this.activityId, eventHistInqConfig.activityId) &&
        Objects.equals(this.createdBy, eventHistInqConfig.createdBy) &&
        Objects.equals(this.createdDate, eventHistInqConfig.createdDate) &&
        Objects.equals(this.effFromDate, eventHistInqConfig.effFromDate) &&
        Objects.equals(this.effToDate, eventHistInqConfig.effToDate) &&
        Objects.equals(this.eventHistDefName, eventHistInqConfig.eventHistDefName) &&
        Objects.equals(this.eventName, eventHistInqConfig.eventName) &&
        Objects.equals(this.filedType, eventHistInqConfig.filedType) &&
        Objects.equals(this.id, eventHistInqConfig.id) &&
        Objects.equals(this.jsonKey, eventHistInqConfig.jsonKey) &&
        Objects.equals(this.manualFlag, eventHistInqConfig.manualFlag) &&
        Objects.equals(this.parNm, eventHistInqConfig.parNm) &&
        Objects.equals(this.processJobMappingId, eventHistInqConfig.processJobMappingId) &&
        Objects.equals(this.tbaFiledName, eventHistInqConfig.tbaFiledName) &&
        Objects.equals(this.updatedBy, eventHistInqConfig.updatedBy) &&
        Objects.equals(this.updatedDate, eventHistInqConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actLongDesc, activeFlag, activityId, createdBy, createdDate, effFromDate, effToDate, eventHistDefName, eventName, filedType, id, jsonKey, manualFlag, parNm, processJobMappingId, tbaFiledName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EventHistInqConfig {\n");
    
    sb.append("    actLongDesc: ").append(toIndentedString(actLongDesc)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    activityId: ").append(toIndentedString(activityId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    effFromDate: ").append(toIndentedString(effFromDate)).append("\n");
    sb.append("    effToDate: ").append(toIndentedString(effToDate)).append("\n");
    sb.append("    eventHistDefName: ").append(toIndentedString(eventHistDefName)).append("\n");
    sb.append("    eventName: ").append(toIndentedString(eventName)).append("\n");
    sb.append("    filedType: ").append(toIndentedString(filedType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    manualFlag: ").append(toIndentedString(manualFlag)).append("\n");
    sb.append("    parNm: ").append(toIndentedString(parNm)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    tbaFiledName: ").append(toIndentedString(tbaFiledName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

