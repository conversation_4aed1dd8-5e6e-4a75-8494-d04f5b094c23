/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * PdmDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class PdmDetails   {
  @JsonProperty("clientid")
  private String clientid = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("insertTimestamp")
  private String insertTimestamp = null;

  @JsonProperty("jobcompletionResponse")
  private String jobcompletionResponse = null;

  @JsonProperty("ksdname")
  private String ksdname = null;

  @JsonProperty("maskStatus")
  private String maskStatus = null;

  @JsonProperty("pdmStatus")
  private String pdmStatus = null;

  @JsonProperty("submitjobResponse")
  private String submitjobResponse = null;

  @JsonProperty("targetDatabaseid")
  private String targetDatabaseid = null;

  public PdmDetails clientid(String clientid) {
    this.clientid = clientid;
    return this;
  }

   /**
   * Get clientid
   * @return clientid
  **/
  @JsonProperty("clientid")
  @ApiModelProperty(value = "")
  public String getClientid() {
    return clientid;
  }

  public void setClientid(String clientid) {
    this.clientid = clientid;
  }

  public PdmDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public PdmDetails insertTimestamp(String insertTimestamp) {
    this.insertTimestamp = insertTimestamp;
    return this;
  }

   /**
   * Get insertTimestamp
   * @return insertTimestamp
  **/
  @JsonProperty("insertTimestamp")
  @ApiModelProperty(value = "")
  public String getInsertTimestamp() {
    return insertTimestamp;
  }

  public void setInsertTimestamp(String insertTimestamp) {
    this.insertTimestamp = insertTimestamp;
  }

  public PdmDetails jobcompletionResponse(String jobcompletionResponse) {
    this.jobcompletionResponse = jobcompletionResponse;
    return this;
  }

   /**
   * Get jobcompletionResponse
   * @return jobcompletionResponse
  **/
  @JsonProperty("jobcompletionResponse")
  @ApiModelProperty(value = "")
  public String getJobcompletionResponse() {
    return jobcompletionResponse;
  }

  public void setJobcompletionResponse(String jobcompletionResponse) {
    this.jobcompletionResponse = jobcompletionResponse;
  }

  public PdmDetails ksdname(String ksdname) {
    this.ksdname = ksdname;
    return this;
  }

   /**
   * Get ksdname
   * @return ksdname
  **/
  @JsonProperty("ksdname")
  @ApiModelProperty(value = "")
  public String getKsdname() {
    return ksdname;
  }

  public void setKsdname(String ksdname) {
    this.ksdname = ksdname;
  }

  public PdmDetails maskStatus(String maskStatus) {
    this.maskStatus = maskStatus;
    return this;
  }

   /**
   * Get maskStatus
   * @return maskStatus
  **/
  @JsonProperty("maskStatus")
  @ApiModelProperty(value = "")
  public String getMaskStatus() {
    return maskStatus;
  }

  public void setMaskStatus(String maskStatus) {
    this.maskStatus = maskStatus;
  }

  public PdmDetails pdmStatus(String pdmStatus) {
    this.pdmStatus = pdmStatus;
    return this;
  }

   /**
   * Get pdmStatus
   * @return pdmStatus
  **/
  @JsonProperty("pdmStatus")
  @ApiModelProperty(value = "")
  public String getPdmStatus() {
    return pdmStatus;
  }

  public void setPdmStatus(String pdmStatus) {
    this.pdmStatus = pdmStatus;
  }

  public PdmDetails submitjobResponse(String submitjobResponse) {
    this.submitjobResponse = submitjobResponse;
    return this;
  }

   /**
   * Get submitjobResponse
   * @return submitjobResponse
  **/
  @JsonProperty("submitjobResponse")
  @ApiModelProperty(value = "")
  public String getSubmitjobResponse() {
    return submitjobResponse;
  }

  public void setSubmitjobResponse(String submitjobResponse) {
    this.submitjobResponse = submitjobResponse;
  }

  public PdmDetails targetDatabaseid(String targetDatabaseid) {
    this.targetDatabaseid = targetDatabaseid;
    return this;
  }

   /**
   * Get targetDatabaseid
   * @return targetDatabaseid
  **/
  @JsonProperty("targetDatabaseid")
  @ApiModelProperty(value = "")
  public String getTargetDatabaseid() {
    return targetDatabaseid;
  }

  public void setTargetDatabaseid(String targetDatabaseid) {
    this.targetDatabaseid = targetDatabaseid;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PdmDetails pdmDetails = (PdmDetails) o;
    return Objects.equals(this.clientid, pdmDetails.clientid) &&
        Objects.equals(this.id, pdmDetails.id) &&
        Objects.equals(this.insertTimestamp, pdmDetails.insertTimestamp) &&
        Objects.equals(this.jobcompletionResponse, pdmDetails.jobcompletionResponse) &&
        Objects.equals(this.ksdname, pdmDetails.ksdname) &&
        Objects.equals(this.maskStatus, pdmDetails.maskStatus) &&
        Objects.equals(this.pdmStatus, pdmDetails.pdmStatus) &&
        Objects.equals(this.submitjobResponse, pdmDetails.submitjobResponse) &&
        Objects.equals(this.targetDatabaseid, pdmDetails.targetDatabaseid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(clientid, id, insertTimestamp, jobcompletionResponse, ksdname, maskStatus, pdmStatus, submitjobResponse, targetDatabaseid);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PdmDetails {\n");
    
    sb.append("    clientid: ").append(toIndentedString(clientid)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    insertTimestamp: ").append(toIndentedString(insertTimestamp)).append("\n");
    sb.append("    jobcompletionResponse: ").append(toIndentedString(jobcompletionResponse)).append("\n");
    sb.append("    ksdname: ").append(toIndentedString(ksdname)).append("\n");
    sb.append("    maskStatus: ").append(toIndentedString(maskStatus)).append("\n");
    sb.append("    pdmStatus: ").append(toIndentedString(pdmStatus)).append("\n");
    sb.append("    submitjobResponse: ").append(toIndentedString(submitjobResponse)).append("\n");
    sb.append("    targetDatabaseid: ").append(toIndentedString(targetDatabaseid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

