/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * ResultMapper
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ResultMapper   {
  @JsonProperty("opsCode")
  private String opsCode = null;

  @JsonProperty("opsName")
  private String opsName = null;

  @JsonProperty("unitCode")
  private String unitCode = null;

  @JsonProperty("unitName")
  private String unitName = null;

  public ResultMapper opsCode(String opsCode) {
    this.opsCode = opsCode;
    return this;
  }

   /**
   * Get opsCode
   * @return opsCode
  **/
  @JsonProperty("opsCode")
  @ApiModelProperty(value = "")
  public String getOpsCode() {
    return opsCode;
  }

  public void setOpsCode(String opsCode) {
    this.opsCode = opsCode;
  }

  public ResultMapper opsName(String opsName) {
    this.opsName = opsName;
    return this;
  }

   /**
   * Get opsName
   * @return opsName
  **/
  @JsonProperty("opsName")
  @ApiModelProperty(value = "")
  public String getOpsName() {
    return opsName;
  }

  public void setOpsName(String opsName) {
    this.opsName = opsName;
  }

  public ResultMapper unitCode(String unitCode) {
    this.unitCode = unitCode;
    return this;
  }

   /**
   * Get unitCode
   * @return unitCode
  **/
  @JsonProperty("unitCode")
  @ApiModelProperty(value = "")
  public String getUnitCode() {
    return unitCode;
  }

  public void setUnitCode(String unitCode) {
    this.unitCode = unitCode;
  }

  public ResultMapper unitName(String unitName) {
    this.unitName = unitName;
    return this;
  }

   /**
   * Get unitName
   * @return unitName
  **/
  @JsonProperty("unitName")
  @ApiModelProperty(value = "")
  public String getUnitName() {
    return unitName;
  }

  public void setUnitName(String unitName) {
    this.unitName = unitName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResultMapper resultMapper = (ResultMapper) o;
    return Objects.equals(this.opsCode, resultMapper.opsCode) &&
        Objects.equals(this.opsName, resultMapper.opsName) &&
        Objects.equals(this.unitCode, resultMapper.unitCode) &&
        Objects.equals(this.unitName, resultMapper.unitName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(opsCode, opsName, unitCode, unitName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResultMapper {\n");
    
    sb.append("    opsCode: ").append(toIndentedString(opsCode)).append("\n");
    sb.append("    opsName: ").append(toIndentedString(opsName)).append("\n");
    sb.append("    unitCode: ").append(toIndentedString(unitCode)).append("\n");
    sb.append("    unitName: ").append(toIndentedString(unitName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

