/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * AnalystDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class AnalystDetails   {
  @JsonProperty("adid")
  private String adid = null;

  @JsonProperty("displayData")
  private String displayData = null;

  @JsonProperty("fullName")
  private String fullName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("role")
  private String role = null;

  @JsonProperty("type")
  private String type = null;

  public AnalystDetails adid(String adid) {
    this.adid = adid;
    return this;
  }

   /**
   * Get adid
   * @return adid
  **/
  @JsonProperty("adid")
  @ApiModelProperty(value = "")
  public String getAdid() {
    return adid;
  }

  public void setAdid(String adid) {
    this.adid = adid;
  }

  public AnalystDetails displayData(String displayData) {
    this.displayData = displayData;
    return this;
  }

   /**
   * Get displayData
   * @return displayData
  **/
  @JsonProperty("displayData")
  @ApiModelProperty(value = "")
  public String getDisplayData() {
    return displayData;
  }

  public void setDisplayData(String displayData) {
    this.displayData = displayData;
  }

  public AnalystDetails fullName(String fullName) {
    this.fullName = fullName;
    return this;
  }

   /**
   * Get fullName
   * @return fullName
  **/
  @JsonProperty("fullName")
  @ApiModelProperty(value = "")
  public String getFullName() {
    return fullName;
  }

  public void setFullName(String fullName) {
    this.fullName = fullName;
  }

  public AnalystDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public AnalystDetails role(String role) {
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public String getRole() {
    return role;
  }

  public void setRole(String role) {
    this.role = role;
  }

  public AnalystDetails type(String type) {
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @JsonProperty("type")
  @ApiModelProperty(value = "")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AnalystDetails analystDetails = (AnalystDetails) o;
    return Objects.equals(this.adid, analystDetails.adid) &&
        Objects.equals(this.displayData, analystDetails.displayData) &&
        Objects.equals(this.fullName, analystDetails.fullName) &&
        Objects.equals(this.id, analystDetails.id) &&
        Objects.equals(this.role, analystDetails.role) &&
        Objects.equals(this.type, analystDetails.type);
  }

  @Override
  public int hashCode() {
    return Objects.hash(adid, displayData, fullName, id, role, type);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AnalystDetails {\n");
    
    sb.append("    adid: ").append(toIndentedString(adid)).append("\n");
    sb.append("    displayData: ").append(toIndentedString(displayData)).append("\n");
    sb.append("    fullName: ").append(toIndentedString(fullName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

