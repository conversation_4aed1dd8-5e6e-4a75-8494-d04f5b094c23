/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.RoleConfigBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * RoleDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class RoleDetails   {
  @JsonProperty("admin")
  private List<RoleConfigBo> admin = new ArrayList<RoleConfigBo>();

  @JsonProperty("analyst")
  private List<RoleConfigBo> analyst = new ArrayList<RoleConfigBo>();

  @JsonProperty("manager")
  private List<RoleConfigBo> manager = new ArrayList<RoleConfigBo>();

  @JsonProperty("role")
  private List<String> role = new ArrayList<String>();

  public RoleDetails admin(List<RoleConfigBo> admin) {
    this.admin = admin;
    return this;
  }

  public RoleDetails addAdminItem(RoleConfigBo adminItem) {
    this.admin.add(adminItem);
    return this;
  }

   /**
   * Get admin
   * @return admin
  **/
  @JsonProperty("admin")
  @ApiModelProperty(value = "")
  public List<RoleConfigBo> getAdmin() {
    return admin;
  }

  public void setAdmin(List<RoleConfigBo> admin) {
    this.admin = admin;
  }

  public RoleDetails analyst(List<RoleConfigBo> analyst) {
    this.analyst = analyst;
    return this;
  }

  public RoleDetails addAnalystItem(RoleConfigBo analystItem) {
    this.analyst.add(analystItem);
    return this;
  }

   /**
   * Get analyst
   * @return analyst
  **/
  @JsonProperty("analyst")
  @ApiModelProperty(value = "")
  public List<RoleConfigBo> getAnalyst() {
    return analyst;
  }

  public void setAnalyst(List<RoleConfigBo> analyst) {
    this.analyst = analyst;
  }

  public RoleDetails manager(List<RoleConfigBo> manager) {
    this.manager = manager;
    return this;
  }

  public RoleDetails addManagerItem(RoleConfigBo managerItem) {
    this.manager.add(managerItem);
    return this;
  }

   /**
   * Get manager
   * @return manager
  **/
  @JsonProperty("manager")
  @ApiModelProperty(value = "")
  public List<RoleConfigBo> getManager() {
    return manager;
  }

  public void setManager(List<RoleConfigBo> manager) {
    this.manager = manager;
  }

  public RoleDetails role(List<String> role) {
    this.role = role;
    return this;
  }

  public RoleDetails addRoleItem(String roleItem) {
    this.role.add(roleItem);
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public List<String> getRole() {
    return role;
  }

  public void setRole(List<String> role) {
    this.role = role;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RoleDetails roleDetails = (RoleDetails) o;
    return Objects.equals(this.admin, roleDetails.admin) &&
        Objects.equals(this.analyst, roleDetails.analyst) &&
        Objects.equals(this.manager, roleDetails.manager) &&
        Objects.equals(this.role, roleDetails.role);
  }

  @Override
  public int hashCode() {
    return Objects.hash(admin, analyst, manager, role);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RoleDetails {\n");
    
    sb.append("    admin: ").append(toIndentedString(admin)).append("\n");
    sb.append("    analyst: ").append(toIndentedString(analyst)).append("\n");
    sb.append("    manager: ").append(toIndentedString(manager)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

