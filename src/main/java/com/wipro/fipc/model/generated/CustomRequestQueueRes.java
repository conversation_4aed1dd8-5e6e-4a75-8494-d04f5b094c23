/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import org.joda.time.*;
import javax.validation.constraints.*;

/**
 * CustomRequestQueueRes
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class CustomRequestQueueRes   {
  @JsonProperty("adxScriptId")
  private String adxScriptId = null;

  @JsonProperty("allocateDateTime")
  private Date allocateDateTime = null;

  @JsonProperty("allocateTimestamp")
  private Long allocateTimestamp = null;

  @JsonProperty("assignedBy")
  private String assignedBy = null;

  @JsonProperty("assignedDate")
  private LocalDateTime assignedDate = null;

  @JsonProperty("assignee")
  private String assignee = null;

  @JsonProperty("batchData")
  private String batchData = null;

  @JsonProperty("batchStatus")
  private String batchStatus = null;

  @JsonProperty("botId")
  private String botId = null;

  @JsonProperty("businessOps")
  private String businessOps = null;

  @JsonProperty("businessUnit")
  private String businessUnit = null;

  @JsonProperty("clientId")
  private String clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("comments")
  private String comments = null;

  @JsonProperty("configJson")
  private String configJson = null;

  @JsonProperty("createDateTime")
  private Date createDateTime = null;

  @JsonProperty("createTimestamp")
  private Long createTimestamp = null;

  @JsonProperty("defectId")
  private String defectId = null;

  @JsonProperty("eftDateTime")
  private String eftDateTime = null;

  @JsonProperty("eftStatus")
  private String eftStatus = null;

  @JsonProperty("eft_subj")
  private String eftSubj = null;

  @JsonProperty("endDateTime")
  private Date endDateTime = null;

  @JsonProperty("endTimestamp")
  private Long endTimestamp = null;

  @JsonProperty("frequency")
  private String frequency = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("newAssignee")
  private String newAssignee = null;

  @JsonProperty("phase")
  private Integer phase = null;

  @JsonProperty("pjmId")
  private Long pjmId = null;

  @JsonProperty("pluginName")
  private String pluginName = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  @JsonProperty("request")
  private String request = null;

  @JsonProperty("requestType")
  private String requestType = null;

  @JsonProperty("sla")
  private Integer sla = null;

  @JsonProperty("source")
  private String source = null;

  @JsonProperty("startDateTime")
  private Date startDateTime = null;

  @JsonProperty("startTimestamp")
  private Long startTimestamp = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("taskId")
  private String taskId = null;

  @JsonProperty("tower")
  private String tower = null;

  @JsonProperty("uid")
  private String uid = null;

  @JsonProperty("userName")
  private String userName = null;

  public CustomRequestQueueRes adxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
    return this;
  }

   /**
   * Get adxScriptId
   * @return adxScriptId
  **/
  @JsonProperty("adxScriptId")
  @ApiModelProperty(value = "")
  public String getAdxScriptId() {
    return adxScriptId;
  }

  public void setAdxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
  }

  public CustomRequestQueueRes allocateDateTime(Date allocateDateTime) {
    this.allocateDateTime = allocateDateTime;
    return this;
  }

   /**
   * Get allocateDateTime
   * @return allocateDateTime
  **/
  @JsonProperty("allocateDateTime")
  @ApiModelProperty(value = "")
  public Date getAllocateDateTime() {
    return allocateDateTime;
  }

  public void setAllocateDateTime(Date allocateDateTime) {
    this.allocateDateTime = allocateDateTime;
  }

  public CustomRequestQueueRes allocateTimestamp(Long allocateTimestamp) {
    this.allocateTimestamp = allocateTimestamp;
    return this;
  }

   /**
   * Get allocateTimestamp
   * @return allocateTimestamp
  **/
  @JsonProperty("allocateTimestamp")
  @ApiModelProperty(value = "")
  public Long getAllocateTimestamp() {
    return allocateTimestamp;
  }

  public void setAllocateTimestamp(Long allocateTimestamp) {
    this.allocateTimestamp = allocateTimestamp;
  }

  public CustomRequestQueueRes assignedBy(String assignedBy) {
    this.assignedBy = assignedBy;
    return this;
  }

   /**
   * Get assignedBy
   * @return assignedBy
  **/
  @JsonProperty("assignedBy")
  @ApiModelProperty(value = "")
  public String getAssignedBy() {
    return assignedBy;
  }

  public void setAssignedBy(String assignedBy) {
    this.assignedBy = assignedBy;
  }

  public CustomRequestQueueRes assignedDate(LocalDateTime assignedDate) {
    this.assignedDate = assignedDate;
    return this;
  }

   /**
   * Get assignedDate
   * @return assignedDate
  **/
  @JsonProperty("assignedDate")
  @ApiModelProperty(value = "")
  public LocalDateTime getAssignedDate() {
    return assignedDate;
  }

  public void setAssignedDate(LocalDateTime assignedDate) {
    this.assignedDate = assignedDate;
  }

  public CustomRequestQueueRes assignee(String assignee) {
    this.assignee = assignee;
    return this;
  }

   /**
   * Get assignee
   * @return assignee
  **/
  @JsonProperty("assignee")
  @ApiModelProperty(value = "")
  public String getAssignee() {
    return assignee;
  }

  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public CustomRequestQueueRes batchData(String batchData) {
    this.batchData = batchData;
    return this;
  }

   /**
   * Get batchData
   * @return batchData
  **/
  @JsonProperty("batchData")
  @ApiModelProperty(value = "")
  public String getBatchData() {
    return batchData;
  }

  public void setBatchData(String batchData) {
    this.batchData = batchData;
  }

  public CustomRequestQueueRes batchStatus(String batchStatus) {
    this.batchStatus = batchStatus;
    return this;
  }

   /**
   * Get batchStatus
   * @return batchStatus
  **/
  @JsonProperty("batchStatus")
  @ApiModelProperty(value = "")
  public String getBatchStatus() {
    return batchStatus;
  }

  public void setBatchStatus(String batchStatus) {
    this.batchStatus = batchStatus;
  }

  public CustomRequestQueueRes botId(String botId) {
    this.botId = botId;
    return this;
  }

   /**
   * Get botId
   * @return botId
  **/
  @JsonProperty("botId")
  @ApiModelProperty(value = "")
  public String getBotId() {
    return botId;
  }

  public void setBotId(String botId) {
    this.botId = botId;
  }

  public CustomRequestQueueRes businessOps(String businessOps) {
    this.businessOps = businessOps;
    return this;
  }

   /**
   * Get businessOps
   * @return businessOps
  **/
  @JsonProperty("businessOps")
  @ApiModelProperty(value = "")
  public String getBusinessOps() {
    return businessOps;
  }

  public void setBusinessOps(String businessOps) {
    this.businessOps = businessOps;
  }

  public CustomRequestQueueRes businessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public String getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
  }

  public CustomRequestQueueRes clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public CustomRequestQueueRes clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public CustomRequestQueueRes comments(String comments) {
    this.comments = comments;
    return this;
  }

   /**
   * Get comments
   * @return comments
  **/
  @JsonProperty("comments")
  @ApiModelProperty(value = "")
  public String getComments() {
    return comments;
  }

  public void setComments(String comments) {
    this.comments = comments;
  }

  public CustomRequestQueueRes configJson(String configJson) {
    this.configJson = configJson;
    return this;
  }

   /**
   * Get configJson
   * @return configJson
  **/
  @JsonProperty("configJson")
  @ApiModelProperty(value = "")
  public String getConfigJson() {
    return configJson;
  }

  public void setConfigJson(String configJson) {
    this.configJson = configJson;
  }

  public CustomRequestQueueRes createDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
    return this;
  }

   /**
   * Get createDateTime
   * @return createDateTime
  **/
  @JsonProperty("createDateTime")
  @ApiModelProperty(value = "")
  public Date getCreateDateTime() {
    return createDateTime;
  }

  public void setCreateDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
  }

  public CustomRequestQueueRes createTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
    return this;
  }

   /**
   * Get createTimestamp
   * @return createTimestamp
  **/
  @JsonProperty("createTimestamp")
  @ApiModelProperty(value = "")
  public Long getCreateTimestamp() {
    return createTimestamp;
  }

  public void setCreateTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
  }

  public CustomRequestQueueRes defectId(String defectId) {
    this.defectId = defectId;
    return this;
  }

   /**
   * Get defectId
   * @return defectId
  **/
  @JsonProperty("defectId")
  @ApiModelProperty(value = "")
  public String getDefectId() {
    return defectId;
  }

  public void setDefectId(String defectId) {
    this.defectId = defectId;
  }

  public CustomRequestQueueRes eftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
    return this;
  }

   /**
   * Get eftDateTime
   * @return eftDateTime
  **/
  @JsonProperty("eftDateTime")
  @ApiModelProperty(value = "")
  public String getEftDateTime() {
    return eftDateTime;
  }

  public void setEftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
  }

  public CustomRequestQueueRes eftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
    return this;
  }

   /**
   * Get eftStatus
   * @return eftStatus
  **/
  @JsonProperty("eftStatus")
  @ApiModelProperty(value = "")
  public String getEftStatus() {
    return eftStatus;
  }

  public void setEftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
  }

  public CustomRequestQueueRes eftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
    return this;
  }

   /**
   * Get eftSubj
   * @return eftSubj
  **/
  @JsonProperty("eft_subj")
  @ApiModelProperty(value = "")
  public String getEftSubj() {
    return eftSubj;
  }

  public void setEftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
  }

  public CustomRequestQueueRes endDateTime(Date endDateTime) {
    this.endDateTime = endDateTime;
    return this;
  }

   /**
   * Get endDateTime
   * @return endDateTime
  **/
  @JsonProperty("endDateTime")
  @ApiModelProperty(value = "")
  public Date getEndDateTime() {
    return endDateTime;
  }

  public void setEndDateTime(Date endDateTime) {
    this.endDateTime = endDateTime;
  }

  public CustomRequestQueueRes endTimestamp(Long endTimestamp) {
    this.endTimestamp = endTimestamp;
    return this;
  }

   /**
   * Get endTimestamp
   * @return endTimestamp
  **/
  @JsonProperty("endTimestamp")
  @ApiModelProperty(value = "")
  public Long getEndTimestamp() {
    return endTimestamp;
  }

  public void setEndTimestamp(Long endTimestamp) {
    this.endTimestamp = endTimestamp;
  }

  public CustomRequestQueueRes frequency(String frequency) {
    this.frequency = frequency;
    return this;
  }

   /**
   * Get frequency
   * @return frequency
  **/
  @JsonProperty("frequency")
  @ApiModelProperty(value = "")
  public String getFrequency() {
    return frequency;
  }

  public void setFrequency(String frequency) {
    this.frequency = frequency;
  }

  public CustomRequestQueueRes id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public CustomRequestQueueRes jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public CustomRequestQueueRes ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public CustomRequestQueueRes newAssignee(String newAssignee) {
    this.newAssignee = newAssignee;
    return this;
  }

   /**
   * Get newAssignee
   * @return newAssignee
  **/
  @JsonProperty("newAssignee")
  @ApiModelProperty(value = "")
  public String getNewAssignee() {
    return newAssignee;
  }

  public void setNewAssignee(String newAssignee) {
    this.newAssignee = newAssignee;
  }

  public CustomRequestQueueRes phase(Integer phase) {
    this.phase = phase;
    return this;
  }

   /**
   * Get phase
   * @return phase
  **/
  @JsonProperty("phase")
  @ApiModelProperty(value = "")
  public Integer getPhase() {
    return phase;
  }

  public void setPhase(Integer phase) {
    this.phase = phase;
  }

  public CustomRequestQueueRes pjmId(Long pjmId) {
    this.pjmId = pjmId;
    return this;
  }

   /**
   * Get pjmId
   * @return pjmId
  **/
  @JsonProperty("pjmId")
  @ApiModelProperty(value = "")
  public Long getPjmId() {
    return pjmId;
  }

  public void setPjmId(Long pjmId) {
    this.pjmId = pjmId;
  }

  public CustomRequestQueueRes pluginName(String pluginName) {
    this.pluginName = pluginName;
    return this;
  }

   /**
   * Get pluginName
   * @return pluginName
  **/
  @JsonProperty("pluginName")
  @ApiModelProperty(value = "")
  public String getPluginName() {
    return pluginName;
  }

  public void setPluginName(String pluginName) {
    this.pluginName = pluginName;
  }

  public CustomRequestQueueRes processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public CustomRequestQueueRes processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public CustomRequestQueueRes request(String request) {
    this.request = request;
    return this;
  }

   /**
   * Get request
   * @return request
  **/
  @JsonProperty("request")
  @ApiModelProperty(value = "")
  public String getRequest() {
    return request;
  }

  public void setRequest(String request) {
    this.request = request;
  }

  public CustomRequestQueueRes requestType(String requestType) {
    this.requestType = requestType;
    return this;
  }

   /**
   * Get requestType
   * @return requestType
  **/
  @JsonProperty("requestType")
  @ApiModelProperty(value = "")
  public String getRequestType() {
    return requestType;
  }

  public void setRequestType(String requestType) {
    this.requestType = requestType;
  }

  public CustomRequestQueueRes sla(Integer sla) {
    this.sla = sla;
    return this;
  }

   /**
   * Get sla
   * @return sla
  **/
  @JsonProperty("sla")
  @ApiModelProperty(value = "")
  public Integer getSla() {
    return sla;
  }

  public void setSla(Integer sla) {
    this.sla = sla;
  }

  public CustomRequestQueueRes source(String source) {
    this.source = source;
    return this;
  }

   /**
   * Get source
   * @return source
  **/
  @JsonProperty("source")
  @ApiModelProperty(value = "")
  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public CustomRequestQueueRes startDateTime(Date startDateTime) {
    this.startDateTime = startDateTime;
    return this;
  }

   /**
   * Get startDateTime
   * @return startDateTime
  **/
  @JsonProperty("startDateTime")
  @ApiModelProperty(value = "")
  public Date getStartDateTime() {
    return startDateTime;
  }

  public void setStartDateTime(Date startDateTime) {
    this.startDateTime = startDateTime;
  }

  public CustomRequestQueueRes startTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
    return this;
  }

   /**
   * Get startTimestamp
   * @return startTimestamp
  **/
  @JsonProperty("startTimestamp")
  @ApiModelProperty(value = "")
  public Long getStartTimestamp() {
    return startTimestamp;
  }

  public void setStartTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
  }

  public CustomRequestQueueRes status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public CustomRequestQueueRes taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

   /**
   * Get taskId
   * @return taskId
  **/
  @JsonProperty("taskId")
  @ApiModelProperty(value = "")
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public CustomRequestQueueRes tower(String tower) {
    this.tower = tower;
    return this;
  }

   /**
   * Get tower
   * @return tower
  **/
  @JsonProperty("tower")
  @ApiModelProperty(value = "")
  public String getTower() {
    return tower;
  }

  public void setTower(String tower) {
    this.tower = tower;
  }

  public CustomRequestQueueRes uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public CustomRequestQueueRes userName(String userName) {
    this.userName = userName;
    return this;
  }

   /**
   * Get userName
   * @return userName
  **/
  @JsonProperty("userName")
  @ApiModelProperty(value = "")
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomRequestQueueRes customRequestQueueRes = (CustomRequestQueueRes) o;
    return Objects.equals(this.adxScriptId, customRequestQueueRes.adxScriptId) &&
        Objects.equals(this.allocateDateTime, customRequestQueueRes.allocateDateTime) &&
        Objects.equals(this.allocateTimestamp, customRequestQueueRes.allocateTimestamp) &&
        Objects.equals(this.assignedBy, customRequestQueueRes.assignedBy) &&
        Objects.equals(this.assignedDate, customRequestQueueRes.assignedDate) &&
        Objects.equals(this.assignee, customRequestQueueRes.assignee) &&
        Objects.equals(this.batchData, customRequestQueueRes.batchData) &&
        Objects.equals(this.batchStatus, customRequestQueueRes.batchStatus) &&
        Objects.equals(this.botId, customRequestQueueRes.botId) &&
        Objects.equals(this.businessOps, customRequestQueueRes.businessOps) &&
        Objects.equals(this.businessUnit, customRequestQueueRes.businessUnit) &&
        Objects.equals(this.clientId, customRequestQueueRes.clientId) &&
        Objects.equals(this.clientName, customRequestQueueRes.clientName) &&
        Objects.equals(this.comments, customRequestQueueRes.comments) &&
        Objects.equals(this.configJson, customRequestQueueRes.configJson) &&
        Objects.equals(this.createDateTime, customRequestQueueRes.createDateTime) &&
        Objects.equals(this.createTimestamp, customRequestQueueRes.createTimestamp) &&
        Objects.equals(this.defectId, customRequestQueueRes.defectId) &&
        Objects.equals(this.eftDateTime, customRequestQueueRes.eftDateTime) &&
        Objects.equals(this.eftStatus, customRequestQueueRes.eftStatus) &&
        Objects.equals(this.eftSubj, customRequestQueueRes.eftSubj) &&
        Objects.equals(this.endDateTime, customRequestQueueRes.endDateTime) &&
        Objects.equals(this.endTimestamp, customRequestQueueRes.endTimestamp) &&
        Objects.equals(this.frequency, customRequestQueueRes.frequency) &&
        Objects.equals(this.id, customRequestQueueRes.id) &&
        Objects.equals(this.jobName, customRequestQueueRes.jobName) &&
        Objects.equals(this.ksdName, customRequestQueueRes.ksdName) &&
        Objects.equals(this.newAssignee, customRequestQueueRes.newAssignee) &&
        Objects.equals(this.phase, customRequestQueueRes.phase) &&
        Objects.equals(this.pjmId, customRequestQueueRes.pjmId) &&
        Objects.equals(this.pluginName, customRequestQueueRes.pluginName) &&
        Objects.equals(this.processName, customRequestQueueRes.processName) &&
        Objects.equals(this.processType, customRequestQueueRes.processType) &&
        Objects.equals(this.request, customRequestQueueRes.request) &&
        Objects.equals(this.requestType, customRequestQueueRes.requestType) &&
        Objects.equals(this.sla, customRequestQueueRes.sla) &&
        Objects.equals(this.source, customRequestQueueRes.source) &&
        Objects.equals(this.startDateTime, customRequestQueueRes.startDateTime) &&
        Objects.equals(this.startTimestamp, customRequestQueueRes.startTimestamp) &&
        Objects.equals(this.status, customRequestQueueRes.status) &&
        Objects.equals(this.taskId, customRequestQueueRes.taskId) &&
        Objects.equals(this.tower, customRequestQueueRes.tower) &&
        Objects.equals(this.uid, customRequestQueueRes.uid) &&
        Objects.equals(this.userName, customRequestQueueRes.userName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(adxScriptId, allocateDateTime, allocateTimestamp, assignedBy, assignedDate, assignee, batchData, batchStatus, botId, businessOps, businessUnit, clientId, clientName, comments, configJson, createDateTime, createTimestamp, defectId, eftDateTime, eftStatus, eftSubj, endDateTime, endTimestamp, frequency, id, jobName, ksdName, newAssignee, phase, pjmId, pluginName, processName, processType, request, requestType, sla, source, startDateTime, startTimestamp, status, taskId, tower, uid, userName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomRequestQueueRes {\n");
    
    sb.append("    adxScriptId: ").append(toIndentedString(adxScriptId)).append("\n");
    sb.append("    allocateDateTime: ").append(toIndentedString(allocateDateTime)).append("\n");
    sb.append("    allocateTimestamp: ").append(toIndentedString(allocateTimestamp)).append("\n");
    sb.append("    assignedBy: ").append(toIndentedString(assignedBy)).append("\n");
    sb.append("    assignedDate: ").append(toIndentedString(assignedDate)).append("\n");
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    batchData: ").append(toIndentedString(batchData)).append("\n");
    sb.append("    batchStatus: ").append(toIndentedString(batchStatus)).append("\n");
    sb.append("    botId: ").append(toIndentedString(botId)).append("\n");
    sb.append("    businessOps: ").append(toIndentedString(businessOps)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    comments: ").append(toIndentedString(comments)).append("\n");
    sb.append("    configJson: ").append(toIndentedString(configJson)).append("\n");
    sb.append("    createDateTime: ").append(toIndentedString(createDateTime)).append("\n");
    sb.append("    createTimestamp: ").append(toIndentedString(createTimestamp)).append("\n");
    sb.append("    defectId: ").append(toIndentedString(defectId)).append("\n");
    sb.append("    eftDateTime: ").append(toIndentedString(eftDateTime)).append("\n");
    sb.append("    eftStatus: ").append(toIndentedString(eftStatus)).append("\n");
    sb.append("    eftSubj: ").append(toIndentedString(eftSubj)).append("\n");
    sb.append("    endDateTime: ").append(toIndentedString(endDateTime)).append("\n");
    sb.append("    endTimestamp: ").append(toIndentedString(endTimestamp)).append("\n");
    sb.append("    frequency: ").append(toIndentedString(frequency)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    newAssignee: ").append(toIndentedString(newAssignee)).append("\n");
    sb.append("    phase: ").append(toIndentedString(phase)).append("\n");
    sb.append("    pjmId: ").append(toIndentedString(pjmId)).append("\n");
    sb.append("    pluginName: ").append(toIndentedString(pluginName)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    request: ").append(toIndentedString(request)).append("\n");
    sb.append("    requestType: ").append(toIndentedString(requestType)).append("\n");
    sb.append("    sla: ").append(toIndentedString(sla)).append("\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    startDateTime: ").append(toIndentedString(startDateTime)).append("\n");
    sb.append("    startTimestamp: ").append(toIndentedString(startTimestamp)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    tower: ").append(toIndentedString(tower)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

