/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.CustomPFCClientCodeBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * ProcessCofigResBody
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ProcessCofigResBody   {
  @JsonProperty("processConfigList")
  private List<CustomPFCClientCodeBO> processConfigList = new ArrayList<CustomPFCClientCodeBO>();

  @JsonProperty("totalCount")
  private Long totalCount = null;

  public ProcessCofigResBody processConfigList(List<CustomPFCClientCodeBO> processConfigList) {
    this.processConfigList = processConfigList;
    return this;
  }

  public ProcessCofigResBody addProcessConfigListItem(CustomPFCClientCodeBO processConfigListItem) {
    this.processConfigList.add(processConfigListItem);
    return this;
  }

   /**
   * Get processConfigList
   * @return processConfigList
  **/
  @JsonProperty("processConfigList")
  @ApiModelProperty(value = "")
  public List<CustomPFCClientCodeBO> getProcessConfigList() {
    return processConfigList;
  }

  public void setProcessConfigList(List<CustomPFCClientCodeBO> processConfigList) {
    this.processConfigList = processConfigList;
  }

  public ProcessCofigResBody totalCount(Long totalCount) {
    this.totalCount = totalCount;
    return this;
  }

   /**
   * Get totalCount
   * @return totalCount
  **/
  @JsonProperty("totalCount")
  @ApiModelProperty(value = "")
  public Long getTotalCount() {
    return totalCount;
  }

  public void setTotalCount(Long totalCount) {
    this.totalCount = totalCount;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessCofigResBody processCofigResBody = (ProcessCofigResBody) o;
    return Objects.equals(this.processConfigList, processCofigResBody.processConfigList) &&
        Objects.equals(this.totalCount, processCofigResBody.totalCount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(processConfigList, totalCount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessCofigResBody {\n");
    
    sb.append("    processConfigList: ").append(toIndentedString(processConfigList)).append("\n");
    sb.append("    totalCount: ").append(toIndentedString(totalCount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

