/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ClientDetails;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * Audit
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class Audit   {
  @JsonProperty("allocatedBy")
  private String allocatedBy = null;

  @JsonProperty("botName")
  private String botName = null;

  @JsonProperty("clientDet")
  private ClientDetails clientDet = null;

  @JsonProperty("createTimestamp")
  private Double createTimestamp = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("fileType")
  private String fileType = null;

  @JsonProperty("flag")
  private Boolean flag = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("json")
  private String json = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("report")
  private List<byte[]> report = new ArrayList<byte[]>();

  @JsonProperty("ticketId")
  private String ticketId = null;

  @JsonProperty("uid")
  private String uid = null;

  public Audit allocatedBy(String allocatedBy) {
    this.allocatedBy = allocatedBy;
    return this;
  }

   /**
   * Get allocatedBy
   * @return allocatedBy
  **/
  @JsonProperty("allocatedBy")
  @ApiModelProperty(value = "")
  public String getAllocatedBy() {
    return allocatedBy;
  }

  public void setAllocatedBy(String allocatedBy) {
    this.allocatedBy = allocatedBy;
  }

  public Audit botName(String botName) {
    this.botName = botName;
    return this;
  }

   /**
   * Get botName
   * @return botName
  **/
  @JsonProperty("botName")
  @ApiModelProperty(value = "")
  public String getBotName() {
    return botName;
  }

  public void setBotName(String botName) {
    this.botName = botName;
  }

  public Audit clientDet(ClientDetails clientDet) {
    this.clientDet = clientDet;
    return this;
  }

   /**
   * Get clientDet
   * @return clientDet
  **/
  @JsonProperty("clientDet")
  @ApiModelProperty(value = "")
  public ClientDetails getClientDet() {
    return clientDet;
  }

  public void setClientDet(ClientDetails clientDet) {
    this.clientDet = clientDet;
  }

  public Audit createTimestamp(Double createTimestamp) {
    this.createTimestamp = createTimestamp;
    return this;
  }

   /**
   * Get createTimestamp
   * @return createTimestamp
  **/
  @JsonProperty("createTimestamp")
  @ApiModelProperty(value = "")
  public Double getCreateTimestamp() {
    return createTimestamp;
  }

  public void setCreateTimestamp(Double createTimestamp) {
    this.createTimestamp = createTimestamp;
  }

  public Audit fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public Audit fileType(String fileType) {
    this.fileType = fileType;
    return this;
  }

   /**
   * Get fileType
   * @return fileType
  **/
  @JsonProperty("fileType")
  @ApiModelProperty(value = "")
  public String getFileType() {
    return fileType;
  }

  public void setFileType(String fileType) {
    this.fileType = fileType;
  }

  public Audit flag(Boolean flag) {
    this.flag = flag;
    return this;
  }

   /**
   * Get flag
   * @return flag
  **/
  @JsonProperty("flag")
  @ApiModelProperty(value = "")
  public Boolean getFlag() {
    return flag;
  }

  public void setFlag(Boolean flag) {
    this.flag = flag;
  }

  public Audit id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Audit json(String json) {
    this.json = json;
    return this;
  }

   /**
   * Get json
   * @return json
  **/
  @JsonProperty("json")
  @ApiModelProperty(value = "")
  public String getJson() {
    return json;
  }

  public void setJson(String json) {
    this.json = json;
  }

  public Audit processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public Audit report(List<byte[]> report) {
    this.report = report;
    return this;
  }

  public Audit addReportItem(byte[] reportItem) {
    this.report.add(reportItem);
    return this;
  }

   /**
   * Get report
   * @return report
  **/
  @JsonProperty("report")
  @ApiModelProperty(value = "")
  public List<byte[]> getReport() {
    return report;
  }

  public void setReport(List<byte[]> report) {
    this.report = report;
  }

  public Audit ticketId(String ticketId) {
    this.ticketId = ticketId;
    return this;
  }

   /**
   * Get ticketId
   * @return ticketId
  **/
  @JsonProperty("ticketId")
  @ApiModelProperty(value = "")
  public String getTicketId() {
    return ticketId;
  }

  public void setTicketId(String ticketId) {
    this.ticketId = ticketId;
  }

  public Audit uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Audit audit = (Audit) o;
    return Objects.equals(this.allocatedBy, audit.allocatedBy) &&
        Objects.equals(this.botName, audit.botName) &&
        Objects.equals(this.clientDet, audit.clientDet) &&
        Objects.equals(this.createTimestamp, audit.createTimestamp) &&
        Objects.equals(this.fileName, audit.fileName) &&
        Objects.equals(this.fileType, audit.fileType) &&
        Objects.equals(this.flag, audit.flag) &&
        Objects.equals(this.id, audit.id) &&
        Objects.equals(this.json, audit.json) &&
        Objects.equals(this.processJobMappingId, audit.processJobMappingId) &&
        Objects.equals(this.report, audit.report) &&
        Objects.equals(this.ticketId, audit.ticketId) &&
        Objects.equals(this.uid, audit.uid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(allocatedBy, botName, clientDet, createTimestamp, fileName, fileType, flag, id, json, processJobMappingId, report, ticketId, uid);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Audit {\n");
    
    sb.append("    allocatedBy: ").append(toIndentedString(allocatedBy)).append("\n");
    sb.append("    botName: ").append(toIndentedString(botName)).append("\n");
    sb.append("    clientDet: ").append(toIndentedString(clientDet)).append("\n");
    sb.append("    createTimestamp: ").append(toIndentedString(createTimestamp)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    fileType: ").append(toIndentedString(fileType)).append("\n");
    sb.append("    flag: ").append(toIndentedString(flag)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    json: ").append(toIndentedString(json)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    report: ").append(toIndentedString(report)).append("\n");
    sb.append("    ticketId: ").append(toIndentedString(ticketId)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

