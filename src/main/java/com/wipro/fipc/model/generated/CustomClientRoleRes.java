/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * CustomClientRoleRes
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class CustomClientRoleRes   {
  @JsonProperty("adid")
  private String adid = null;

  @JsonProperty("businessUnitCode")
  private String businessUnitCode = null;

  @JsonProperty("businessUnitId")
  private Long businessUnitId = null;

  @JsonProperty("businessUnitName")
  private String businessUnitName = null;

  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientId")
  private Long clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("ownedby")
  private String ownedby = null;

  @JsonProperty("role")
  private String role = null;

  @JsonProperty("roleId")
  private Long roleId = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public CustomClientRoleRes adid(String adid) {
    this.adid = adid;
    return this;
  }

   /**
   * Get adid
   * @return adid
  **/
  @JsonProperty("adid")
  @ApiModelProperty(value = "")
  public String getAdid() {
    return adid;
  }

  public void setAdid(String adid) {
    this.adid = adid;
  }

  public CustomClientRoleRes businessUnitCode(String businessUnitCode) {
    this.businessUnitCode = businessUnitCode;
    return this;
  }

   /**
   * Get businessUnitCode
   * @return businessUnitCode
  **/
  @JsonProperty("businessUnitCode")
  @ApiModelProperty(value = "")
  public String getBusinessUnitCode() {
    return businessUnitCode;
  }

  public void setBusinessUnitCode(String businessUnitCode) {
    this.businessUnitCode = businessUnitCode;
  }

  public CustomClientRoleRes businessUnitId(Long businessUnitId) {
    this.businessUnitId = businessUnitId;
    return this;
  }

   /**
   * Get businessUnitId
   * @return businessUnitId
  **/
  @JsonProperty("businessUnitId")
  @ApiModelProperty(value = "")
  public Long getBusinessUnitId() {
    return businessUnitId;
  }

  public void setBusinessUnitId(Long businessUnitId) {
    this.businessUnitId = businessUnitId;
  }

  public CustomClientRoleRes businessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
    return this;
  }

   /**
   * Get businessUnitName
   * @return businessUnitName
  **/
  @JsonProperty("businessUnitName")
  @ApiModelProperty(value = "")
  public String getBusinessUnitName() {
    return businessUnitName;
  }

  public void setBusinessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
  }

  public CustomClientRoleRes clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public CustomClientRoleRes clientId(Long clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Long getClientId() {
    return clientId;
  }

  public void setClientId(Long clientId) {
    this.clientId = clientId;
  }

  public CustomClientRoleRes clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public CustomClientRoleRes ownedby(String ownedby) {
    this.ownedby = ownedby;
    return this;
  }

   /**
   * Get ownedby
   * @return ownedby
  **/
  @JsonProperty("ownedby")
  @ApiModelProperty(value = "")
  public String getOwnedby() {
    return ownedby;
  }

  public void setOwnedby(String ownedby) {
    this.ownedby = ownedby;
  }

  public CustomClientRoleRes role(String role) {
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public String getRole() {
    return role;
  }

  public void setRole(String role) {
    this.role = role;
  }

  public CustomClientRoleRes roleId(Long roleId) {
    this.roleId = roleId;
    return this;
  }

   /**
   * Get roleId
   * @return roleId
  **/
  @JsonProperty("roleId")
  @ApiModelProperty(value = "")
  public Long getRoleId() {
    return roleId;
  }

  public void setRoleId(Long roleId) {
    this.roleId = roleId;
  }

  public CustomClientRoleRes updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public CustomClientRoleRes updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomClientRoleRes customClientRoleRes = (CustomClientRoleRes) o;
    return Objects.equals(this.adid, customClientRoleRes.adid) &&
        Objects.equals(this.businessUnitCode, customClientRoleRes.businessUnitCode) &&
        Objects.equals(this.businessUnitId, customClientRoleRes.businessUnitId) &&
        Objects.equals(this.businessUnitName, customClientRoleRes.businessUnitName) &&
        Objects.equals(this.clientCode, customClientRoleRes.clientCode) &&
        Objects.equals(this.clientId, customClientRoleRes.clientId) &&
        Objects.equals(this.clientName, customClientRoleRes.clientName) &&
        Objects.equals(this.ownedby, customClientRoleRes.ownedby) &&
        Objects.equals(this.role, customClientRoleRes.role) &&
        Objects.equals(this.roleId, customClientRoleRes.roleId) &&
        Objects.equals(this.updatedBy, customClientRoleRes.updatedBy) &&
        Objects.equals(this.updatedDate, customClientRoleRes.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(adid, businessUnitCode, businessUnitId, businessUnitName, clientCode, clientId, clientName, ownedby, role, roleId, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomClientRoleRes {\n");
    
    sb.append("    adid: ").append(toIndentedString(adid)).append("\n");
    sb.append("    businessUnitCode: ").append(toIndentedString(businessUnitCode)).append("\n");
    sb.append("    businessUnitId: ").append(toIndentedString(businessUnitId)).append("\n");
    sb.append("    businessUnitName: ").append(toIndentedString(businessUnitName)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    ownedby: ").append(toIndentedString(ownedby)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    roleId: ").append(toIndentedString(roleId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

