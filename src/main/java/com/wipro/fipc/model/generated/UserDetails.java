/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import org.joda.time.*;
import javax.validation.constraints.*;

/**
 * UserDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class UserDetails   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("adid")
  private String adid = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("managerAdId")
  private String managerAdId = null;

  @JsonProperty("password")
  private String password = null;

  @JsonProperty("racfId")
  private String racfId = null;

  @JsonProperty("role")
  private String role = null;

  @JsonProperty("sessionDate")
  private LocalDateTime sessionDate = null;

  @JsonProperty("sessionStatus")
  private String sessionStatus = null;

  @JsonProperty("type")
  private String type = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("userName")
  private String userName = null;

  public UserDetails activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public UserDetails adid(String adid) {
    this.adid = adid;
    return this;
  }

   /**
   * Get adid
   * @return adid
  **/
  @JsonProperty("adid")
  @ApiModelProperty(value = "")
  public String getAdid() {
    return adid;
  }

  public void setAdid(String adid) {
    this.adid = adid;
  }

  public UserDetails createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public UserDetails createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public UserDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public UserDetails managerAdId(String managerAdId) {
    this.managerAdId = managerAdId;
    return this;
  }

   /**
   * Get managerAdId
   * @return managerAdId
  **/
  @JsonProperty("managerAdId")
  @ApiModelProperty(value = "")
  public String getManagerAdId() {
    return managerAdId;
  }

  public void setManagerAdId(String managerAdId) {
    this.managerAdId = managerAdId;
  }

  public UserDetails password(String password) {
    this.password = password;
    return this;
  }

   /**
   * Get password
   * @return password
  **/
  @JsonProperty("password")
  @ApiModelProperty(value = "")
  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public UserDetails racfId(String racfId) {
    this.racfId = racfId;
    return this;
  }

   /**
   * Get racfId
   * @return racfId
  **/
  @JsonProperty("racfId")
  @ApiModelProperty(value = "")
  public String getRacfId() {
    return racfId;
  }

  public void setRacfId(String racfId) {
    this.racfId = racfId;
  }

  public UserDetails role(String role) {
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public String getRole() {
    return role;
  }

  public void setRole(String role) {
    this.role = role;
  }

  public UserDetails sessionDate(LocalDateTime sessionDate) {
    this.sessionDate = sessionDate;
    return this;
  }

   /**
   * Get sessionDate
   * @return sessionDate
  **/
  @JsonProperty("sessionDate")
  @ApiModelProperty(value = "")
  public LocalDateTime getSessionDate() {
    return sessionDate;
  }

  public void setSessionDate(LocalDateTime sessionDate) {
    this.sessionDate = sessionDate;
  }

  public UserDetails sessionStatus(String sessionStatus) {
    this.sessionStatus = sessionStatus;
    return this;
  }

   /**
   * Get sessionStatus
   * @return sessionStatus
  **/
  @JsonProperty("sessionStatus")
  @ApiModelProperty(value = "")
  public String getSessionStatus() {
    return sessionStatus;
  }

  public void setSessionStatus(String sessionStatus) {
    this.sessionStatus = sessionStatus;
  }

  public UserDetails type(String type) {
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @JsonProperty("type")
  @ApiModelProperty(value = "")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public UserDetails updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public UserDetails updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public UserDetails userName(String userName) {
    this.userName = userName;
    return this;
  }

   /**
   * Get userName
   * @return userName
  **/
  @JsonProperty("userName")
  @ApiModelProperty(value = "")
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UserDetails userDetails = (UserDetails) o;
    return Objects.equals(this.activeFlag, userDetails.activeFlag) &&
        Objects.equals(this.adid, userDetails.adid) &&
        Objects.equals(this.createdBy, userDetails.createdBy) &&
        Objects.equals(this.createdDate, userDetails.createdDate) &&
        Objects.equals(this.id, userDetails.id) &&
        Objects.equals(this.managerAdId, userDetails.managerAdId) &&
        Objects.equals(this.password, userDetails.password) &&
        Objects.equals(this.racfId, userDetails.racfId) &&
        Objects.equals(this.role, userDetails.role) &&
        Objects.equals(this.sessionDate, userDetails.sessionDate) &&
        Objects.equals(this.sessionStatus, userDetails.sessionStatus) &&
        Objects.equals(this.type, userDetails.type) &&
        Objects.equals(this.updatedBy, userDetails.updatedBy) &&
        Objects.equals(this.updatedDate, userDetails.updatedDate) &&
        Objects.equals(this.userName, userDetails.userName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, adid, createdBy, createdDate, id, managerAdId, password, racfId, role, sessionDate, sessionStatus, type, updatedBy, updatedDate, userName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UserDetails {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    adid: ").append(toIndentedString(adid)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    managerAdId: ").append(toIndentedString(managerAdId)).append("\n");
    sb.append("    password: ").append(toIndentedString(password)).append("\n");
    sb.append("    racfId: ").append(toIndentedString(racfId)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    sessionDate: ").append(toIndentedString(sessionDate)).append("\n");
    sb.append("    sessionStatus: ").append(toIndentedString(sessionStatus)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

