/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ApplicationSecrets;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ApplicationSecretsDto
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ApplicationSecretsDto   {
  @JsonProperty("appCode")
  private String appCode = null;

  @JsonProperty("entity")
  private ApplicationSecrets entity = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("lastResetBy")
  private String lastResetBy = null;

  @JsonProperty("lastResetTs")
  private Date lastResetTs = null;

  @JsonProperty("lastUpdateTs")
  private Date lastUpdateTs = null;

  @JsonProperty("lastUpdatedBy")
  private String lastUpdatedBy = null;

  @JsonProperty("passwordType")
  private String passwordType = null;

  @JsonProperty("resetKey")
  private String resetKey = null;

  @JsonProperty("roboticId")
  private String roboticId = null;

  public ApplicationSecretsDto appCode(String appCode) {
    this.appCode = appCode;
    return this;
  }

   /**
   * Get appCode
   * @return appCode
  **/
  @JsonProperty("appCode")
  @ApiModelProperty(value = "")
  public String getAppCode() {
    return appCode;
  }

  public void setAppCode(String appCode) {
    this.appCode = appCode;
  }

  public ApplicationSecretsDto entity(ApplicationSecrets entity) {
    this.entity = entity;
    return this;
  }

   /**
   * Get entity
   * @return entity
  **/
  @JsonProperty("entity")
  @ApiModelProperty(value = "")
  public ApplicationSecrets getEntity() {
    return entity;
  }

  public void setEntity(ApplicationSecrets entity) {
    this.entity = entity;
  }

  public ApplicationSecretsDto id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ApplicationSecretsDto lastResetBy(String lastResetBy) {
    this.lastResetBy = lastResetBy;
    return this;
  }

   /**
   * Get lastResetBy
   * @return lastResetBy
  **/
  @JsonProperty("lastResetBy")
  @ApiModelProperty(value = "")
  public String getLastResetBy() {
    return lastResetBy;
  }

  public void setLastResetBy(String lastResetBy) {
    this.lastResetBy = lastResetBy;
  }

  public ApplicationSecretsDto lastResetTs(Date lastResetTs) {
    this.lastResetTs = lastResetTs;
    return this;
  }

   /**
   * Get lastResetTs
   * @return lastResetTs
  **/
  @JsonProperty("lastResetTs")
  @ApiModelProperty(value = "")
  public Date getLastResetTs() {
    return lastResetTs;
  }

  public void setLastResetTs(Date lastResetTs) {
    this.lastResetTs = lastResetTs;
  }

  public ApplicationSecretsDto lastUpdateTs(Date lastUpdateTs) {
    this.lastUpdateTs = lastUpdateTs;
    return this;
  }

   /**
   * Get lastUpdateTs
   * @return lastUpdateTs
  **/
  @JsonProperty("lastUpdateTs")
  @ApiModelProperty(value = "")
  public Date getLastUpdateTs() {
    return lastUpdateTs;
  }

  public void setLastUpdateTs(Date lastUpdateTs) {
    this.lastUpdateTs = lastUpdateTs;
  }

  public ApplicationSecretsDto lastUpdatedBy(String lastUpdatedBy) {
    this.lastUpdatedBy = lastUpdatedBy;
    return this;
  }

   /**
   * Get lastUpdatedBy
   * @return lastUpdatedBy
  **/
  @JsonProperty("lastUpdatedBy")
  @ApiModelProperty(value = "")
  public String getLastUpdatedBy() {
    return lastUpdatedBy;
  }

  public void setLastUpdatedBy(String lastUpdatedBy) {
    this.lastUpdatedBy = lastUpdatedBy;
  }

  public ApplicationSecretsDto passwordType(String passwordType) {
    this.passwordType = passwordType;
    return this;
  }

   /**
   * Get passwordType
   * @return passwordType
  **/
  @JsonProperty("passwordType")
  @ApiModelProperty(value = "")
  public String getPasswordType() {
    return passwordType;
  }

  public void setPasswordType(String passwordType) {
    this.passwordType = passwordType;
  }

  public ApplicationSecretsDto resetKey(String resetKey) {
    this.resetKey = resetKey;
    return this;
  }

   /**
   * Get resetKey
   * @return resetKey
  **/
  @JsonProperty("resetKey")
  @ApiModelProperty(value = "")
  public String getResetKey() {
    return resetKey;
  }

  public void setResetKey(String resetKey) {
    this.resetKey = resetKey;
  }

  public ApplicationSecretsDto roboticId(String roboticId) {
    this.roboticId = roboticId;
    return this;
  }

   /**
   * Get roboticId
   * @return roboticId
  **/
  @JsonProperty("roboticId")
  @ApiModelProperty(value = "")
  public String getRoboticId() {
    return roboticId;
  }

  public void setRoboticId(String roboticId) {
    this.roboticId = roboticId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ApplicationSecretsDto applicationSecretsDto = (ApplicationSecretsDto) o;
    return Objects.equals(this.appCode, applicationSecretsDto.appCode) &&
        Objects.equals(this.entity, applicationSecretsDto.entity) &&
        Objects.equals(this.id, applicationSecretsDto.id) &&
        Objects.equals(this.lastResetBy, applicationSecretsDto.lastResetBy) &&
        Objects.equals(this.lastResetTs, applicationSecretsDto.lastResetTs) &&
        Objects.equals(this.lastUpdateTs, applicationSecretsDto.lastUpdateTs) &&
        Objects.equals(this.lastUpdatedBy, applicationSecretsDto.lastUpdatedBy) &&
        Objects.equals(this.passwordType, applicationSecretsDto.passwordType) &&
        Objects.equals(this.resetKey, applicationSecretsDto.resetKey) &&
        Objects.equals(this.roboticId, applicationSecretsDto.roboticId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(appCode, entity, id, lastResetBy, lastResetTs, lastUpdateTs, lastUpdatedBy, passwordType, resetKey, roboticId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ApplicationSecretsDto {\n");
    
    sb.append("    appCode: ").append(toIndentedString(appCode)).append("\n");
    sb.append("    entity: ").append(toIndentedString(entity)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    lastResetBy: ").append(toIndentedString(lastResetBy)).append("\n");
    sb.append("    lastResetTs: ").append(toIndentedString(lastResetTs)).append("\n");
    sb.append("    lastUpdateTs: ").append(toIndentedString(lastUpdateTs)).append("\n");
    sb.append("    lastUpdatedBy: ").append(toIndentedString(lastUpdatedBy)).append("\n");
    sb.append("    passwordType: ").append(toIndentedString(passwordType)).append("\n");
    sb.append("    resetKey: ").append(toIndentedString(resetKey)).append("\n");
    sb.append("    roboticId: ").append(toIndentedString(roboticId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

