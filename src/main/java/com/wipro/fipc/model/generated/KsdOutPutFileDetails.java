/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.OutputReport;
import com.wipro.fipc.model.generated.ParticipantRecordIdentifier;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.model.generated.ReportDataCleanse;
import com.wipro.fipc.model.generated.ReportFormat;
import com.wipro.fipc.model.generated.ReportPivot;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.*;

/**
 * KsdOutPutFileDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class KsdOutPutFileDetails   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("appendFormat")
  private String appendFormat = null;

  @JsonProperty("appendName")
  private String appendName = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("dateFormat")
  private String dateFormat = null;

  @JsonProperty("delimiter")
  private String delimiter = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("fileNameWoutSpace")
  private String fileNameWoutSpace = null;

  @JsonProperty("fileType")
  private String fileType = null;

  @JsonProperty("generateReport")
  private String generateReport = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("maxThreshold")
  private Integer maxThreshold = null;

  @JsonProperty("minThreshold")
  private Integer minThreshold = null;

  @JsonProperty("mirrorFileName")
  private String mirrorFileName = null;

  @JsonProperty("outputReports")
  private List<OutputReport> outputReports = new ArrayList<OutputReport>();

  @JsonProperty("participantRecordIdentifiers")
  private List<ParticipantRecordIdentifier> participantRecordIdentifiers = new ArrayList<ParticipantRecordIdentifier>();

  @JsonProperty("pptIdentifier")
  private String pptIdentifier = null;

  @JsonProperty("pptIdentifierType")
  private String pptIdentifierType = null;

  @JsonProperty("prevReportFileName")
  private String prevReportFileName = null;

  @JsonProperty("prevReportFileNameWs")
  private String prevReportFileNameWs = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("referReport")
  private String referReport = null;

  @JsonProperty("reportDataCleanse")
  private List<ReportDataCleanse> reportDataCleanse = new ArrayList<ReportDataCleanse>();

  @JsonProperty("reportFormat")
  private List<ReportFormat> reportFormat = new ArrayList<ReportFormat>();

  @JsonProperty("reportPivot")
  private List<ReportPivot> reportPivot = new ArrayList<ReportPivot>();

  @JsonProperty("sender")
  private String sender = null;

  @JsonProperty("sheetName")
  private String sheetName = null;

  @JsonProperty("sheetNameWoutSpace")
  private String sheetNameWoutSpace = null;

  @JsonProperty("subject")
  private String subject = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("useTemplete")
  private String useTemplete = null;

  @JsonProperty("variation")
  private Integer variation = null;

  public KsdOutPutFileDetails activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public KsdOutPutFileDetails appendFormat(String appendFormat) {
    this.appendFormat = appendFormat;
    return this;
  }

   /**
   * Get appendFormat
   * @return appendFormat
  **/
  @JsonProperty("appendFormat")
  @ApiModelProperty(value = "")
  public String getAppendFormat() {
    return appendFormat;
  }

  public void setAppendFormat(String appendFormat) {
    this.appendFormat = appendFormat;
  }

  public KsdOutPutFileDetails appendName(String appendName) {
    this.appendName = appendName;
    return this;
  }

   /**
   * Get appendName
   * @return appendName
  **/
  @JsonProperty("appendName")
  @ApiModelProperty(value = "")
  public String getAppendName() {
    return appendName;
  }

  public void setAppendName(String appendName) {
    this.appendName = appendName;
  }

  public KsdOutPutFileDetails createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public KsdOutPutFileDetails createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public KsdOutPutFileDetails dateFormat(String dateFormat) {
    this.dateFormat = dateFormat;
    return this;
  }

   /**
   * Get dateFormat
   * @return dateFormat
  **/
  @JsonProperty("dateFormat")
  @ApiModelProperty(value = "")
  public String getDateFormat() {
    return dateFormat;
  }

  public void setDateFormat(String dateFormat) {
    this.dateFormat = dateFormat;
  }

  public KsdOutPutFileDetails delimiter(String delimiter) {
    this.delimiter = delimiter;
    return this;
  }

   /**
   * Get delimiter
   * @return delimiter
  **/
  @JsonProperty("delimiter")
  @ApiModelProperty(value = "")
  public String getDelimiter() {
    return delimiter;
  }

  public void setDelimiter(String delimiter) {
    this.delimiter = delimiter;
  }

  public KsdOutPutFileDetails fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public KsdOutPutFileDetails fileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
    return this;
  }

   /**
   * Get fileNameWoutSpace
   * @return fileNameWoutSpace
  **/
  @JsonProperty("fileNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getFileNameWoutSpace() {
    return fileNameWoutSpace;
  }

  public void setFileNameWoutSpace(String fileNameWoutSpace) {
    this.fileNameWoutSpace = fileNameWoutSpace;
  }

  public KsdOutPutFileDetails fileType(String fileType) {
    this.fileType = fileType;
    return this;
  }

   /**
   * Get fileType
   * @return fileType
  **/
  @JsonProperty("fileType")
  @ApiModelProperty(value = "")
  public String getFileType() {
    return fileType;
  }

  public void setFileType(String fileType) {
    this.fileType = fileType;
  }

  public KsdOutPutFileDetails generateReport(String generateReport) {
    this.generateReport = generateReport;
    return this;
  }

   /**
   * Get generateReport
   * @return generateReport
  **/
  @JsonProperty("generateReport")
  @ApiModelProperty(value = "")
  public String getGenerateReport() {
    return generateReport;
  }

  public void setGenerateReport(String generateReport) {
    this.generateReport = generateReport;
  }

  public KsdOutPutFileDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public KsdOutPutFileDetails maxThreshold(Integer maxThreshold) {
    this.maxThreshold = maxThreshold;
    return this;
  }

   /**
   * Get maxThreshold
   * @return maxThreshold
  **/
  @JsonProperty("maxThreshold")
  @ApiModelProperty(value = "")
  public Integer getMaxThreshold() {
    return maxThreshold;
  }

  public void setMaxThreshold(Integer maxThreshold) {
    this.maxThreshold = maxThreshold;
  }

  public KsdOutPutFileDetails minThreshold(Integer minThreshold) {
    this.minThreshold = minThreshold;
    return this;
  }

   /**
   * Get minThreshold
   * @return minThreshold
  **/
  @JsonProperty("minThreshold")
  @ApiModelProperty(value = "")
  public Integer getMinThreshold() {
    return minThreshold;
  }

  public void setMinThreshold(Integer minThreshold) {
    this.minThreshold = minThreshold;
  }

  public KsdOutPutFileDetails mirrorFileName(String mirrorFileName) {
    this.mirrorFileName = mirrorFileName;
    return this;
  }

   /**
   * Get mirrorFileName
   * @return mirrorFileName
  **/
  @JsonProperty("mirrorFileName")
  @ApiModelProperty(value = "")
  public String getMirrorFileName() {
    return mirrorFileName;
  }

  public void setMirrorFileName(String mirrorFileName) {
    this.mirrorFileName = mirrorFileName;
  }

  public KsdOutPutFileDetails outputReports(List<OutputReport> outputReports) {
    this.outputReports = outputReports;
    return this;
  }

  public KsdOutPutFileDetails addOutputReportsItem(OutputReport outputReportsItem) {
    this.outputReports.add(outputReportsItem);
    return this;
  }

   /**
   * Get outputReports
   * @return outputReports
  **/
  @JsonProperty("outputReports")
  @ApiModelProperty(value = "")
  public List<OutputReport> getOutputReports() {
    return outputReports;
  }

  public void setOutputReports(List<OutputReport> outputReports) {
    this.outputReports = outputReports;
  }

  public KsdOutPutFileDetails participantRecordIdentifiers(List<ParticipantRecordIdentifier> participantRecordIdentifiers) {
    this.participantRecordIdentifiers = participantRecordIdentifiers;
    return this;
  }

  public KsdOutPutFileDetails addParticipantRecordIdentifiersItem(ParticipantRecordIdentifier participantRecordIdentifiersItem) {
    this.participantRecordIdentifiers.add(participantRecordIdentifiersItem);
    return this;
  }

   /**
   * Get participantRecordIdentifiers
   * @return participantRecordIdentifiers
  **/
  @JsonProperty("participantRecordIdentifiers")
  @ApiModelProperty(value = "")
  public List<ParticipantRecordIdentifier> getParticipantRecordIdentifiers() {
    return participantRecordIdentifiers;
  }

  public void setParticipantRecordIdentifiers(List<ParticipantRecordIdentifier> participantRecordIdentifiers) {
    this.participantRecordIdentifiers = participantRecordIdentifiers;
  }

  public KsdOutPutFileDetails pptIdentifier(String pptIdentifier) {
    this.pptIdentifier = pptIdentifier;
    return this;
  }

   /**
   * Get pptIdentifier
   * @return pptIdentifier
  **/
  @JsonProperty("pptIdentifier")
  @ApiModelProperty(value = "")
  public String getPptIdentifier() {
    return pptIdentifier;
  }

  public void setPptIdentifier(String pptIdentifier) {
    this.pptIdentifier = pptIdentifier;
  }

  public KsdOutPutFileDetails pptIdentifierType(String pptIdentifierType) {
    this.pptIdentifierType = pptIdentifierType;
    return this;
  }

   /**
   * Get pptIdentifierType
   * @return pptIdentifierType
  **/
  @JsonProperty("pptIdentifierType")
  @ApiModelProperty(value = "")
  public String getPptIdentifierType() {
    return pptIdentifierType;
  }

  public void setPptIdentifierType(String pptIdentifierType) {
    this.pptIdentifierType = pptIdentifierType;
  }

  public KsdOutPutFileDetails prevReportFileName(String prevReportFileName) {
    this.prevReportFileName = prevReportFileName;
    return this;
  }

   /**
   * Get prevReportFileName
   * @return prevReportFileName
  **/
  @JsonProperty("prevReportFileName")
  @ApiModelProperty(value = "")
  public String getPrevReportFileName() {
    return prevReportFileName;
  }

  public void setPrevReportFileName(String prevReportFileName) {
    this.prevReportFileName = prevReportFileName;
  }

  public KsdOutPutFileDetails prevReportFileNameWs(String prevReportFileNameWs) {
    this.prevReportFileNameWs = prevReportFileNameWs;
    return this;
  }

   /**
   * Get prevReportFileNameWs
   * @return prevReportFileNameWs
  **/
  @JsonProperty("prevReportFileNameWs")
  @ApiModelProperty(value = "")
  public String getPrevReportFileNameWs() {
    return prevReportFileNameWs;
  }

  public void setPrevReportFileNameWs(String prevReportFileNameWs) {
    this.prevReportFileNameWs = prevReportFileNameWs;
  }

  public KsdOutPutFileDetails processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public KsdOutPutFileDetails referReport(String referReport) {
    this.referReport = referReport;
    return this;
  }

   /**
   * Get referReport
   * @return referReport
  **/
  @JsonProperty("referReport")
  @ApiModelProperty(value = "")
  public String getReferReport() {
    return referReport;
  }

  public void setReferReport(String referReport) {
    this.referReport = referReport;
  }

  public KsdOutPutFileDetails reportDataCleanse(List<ReportDataCleanse> reportDataCleanse) {
    this.reportDataCleanse = reportDataCleanse;
    return this;
  }

  public KsdOutPutFileDetails addReportDataCleanseItem(ReportDataCleanse reportDataCleanseItem) {
    this.reportDataCleanse.add(reportDataCleanseItem);
    return this;
  }

   /**
   * Get reportDataCleanse
   * @return reportDataCleanse
  **/
  @JsonProperty("reportDataCleanse")
  @ApiModelProperty(value = "")
  public List<ReportDataCleanse> getReportDataCleanse() {
    return reportDataCleanse;
  }

  public void setReportDataCleanse(List<ReportDataCleanse> reportDataCleanse) {
    this.reportDataCleanse = reportDataCleanse;
  }

  public KsdOutPutFileDetails reportFormat(List<ReportFormat> reportFormat) {
    this.reportFormat = reportFormat;
    return this;
  }

  public KsdOutPutFileDetails addReportFormatItem(ReportFormat reportFormatItem) {
    this.reportFormat.add(reportFormatItem);
    return this;
  }

   /**
   * Get reportFormat
   * @return reportFormat
  **/
  @JsonProperty("reportFormat")
  @ApiModelProperty(value = "")
  public List<ReportFormat> getReportFormat() {
    return reportFormat;
  }

  public void setReportFormat(List<ReportFormat> reportFormat) {
    this.reportFormat = reportFormat;
  }

  public KsdOutPutFileDetails reportPivot(List<ReportPivot> reportPivot) {
    this.reportPivot = reportPivot;
    return this;
  }

  public KsdOutPutFileDetails addReportPivotItem(ReportPivot reportPivotItem) {
    this.reportPivot.add(reportPivotItem);
    return this;
  }

   /**
   * Get reportPivot
   * @return reportPivot
  **/
  @JsonProperty("reportPivot")
  @ApiModelProperty(value = "")
  public List<ReportPivot> getReportPivot() {
    return reportPivot;
  }

  public void setReportPivot(List<ReportPivot> reportPivot) {
    this.reportPivot = reportPivot;
  }

  public KsdOutPutFileDetails sender(String sender) {
    this.sender = sender;
    return this;
  }

   /**
   * Get sender
   * @return sender
  **/
  @JsonProperty("sender")
  @ApiModelProperty(value = "")
  public String getSender() {
    return sender;
  }

  public void setSender(String sender) {
    this.sender = sender;
  }

  public KsdOutPutFileDetails sheetName(String sheetName) {
    this.sheetName = sheetName;
    return this;
  }

   /**
   * Get sheetName
   * @return sheetName
  **/
  @JsonProperty("sheetName")
  @ApiModelProperty(value = "")
  public String getSheetName() {
    return sheetName;
  }

  public void setSheetName(String sheetName) {
    this.sheetName = sheetName;
  }

  public KsdOutPutFileDetails sheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
    return this;
  }

   /**
   * Get sheetNameWoutSpace
   * @return sheetNameWoutSpace
  **/
  @JsonProperty("sheetNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getSheetNameWoutSpace() {
    return sheetNameWoutSpace;
  }

  public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
    this.sheetNameWoutSpace = sheetNameWoutSpace;
  }

  public KsdOutPutFileDetails subject(String subject) {
    this.subject = subject;
    return this;
  }

   /**
   * Get subject
   * @return subject
  **/
  @JsonProperty("subject")
  @ApiModelProperty(value = "")
  public String getSubject() {
    return subject;
  }

  public void setSubject(String subject) {
    this.subject = subject;
  }

  public KsdOutPutFileDetails updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public KsdOutPutFileDetails updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public KsdOutPutFileDetails useTemplete(String useTemplete) {
    this.useTemplete = useTemplete;
    return this;
  }

   /**
   * Get useTemplete
   * @return useTemplete
  **/
  @JsonProperty("useTemplete")
  @ApiModelProperty(value = "")
  public String getUseTemplete() {
    return useTemplete;
  }

  public void setUseTemplete(String useTemplete) {
    this.useTemplete = useTemplete;
  }

  public KsdOutPutFileDetails variation(Integer variation) {
    this.variation = variation;
    return this;
  }

   /**
   * Get variation
   * @return variation
  **/
  @JsonProperty("variation")
  @ApiModelProperty(value = "")
  public Integer getVariation() {
    return variation;
  }

  public void setVariation(Integer variation) {
    this.variation = variation;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KsdOutPutFileDetails ksdOutPutFileDetails = (KsdOutPutFileDetails) o;
    return Objects.equals(this.activeFlag, ksdOutPutFileDetails.activeFlag) &&
        Objects.equals(this.appendFormat, ksdOutPutFileDetails.appendFormat) &&
        Objects.equals(this.appendName, ksdOutPutFileDetails.appendName) &&
        Objects.equals(this.createdBy, ksdOutPutFileDetails.createdBy) &&
        Objects.equals(this.createdDate, ksdOutPutFileDetails.createdDate) &&
        Objects.equals(this.dateFormat, ksdOutPutFileDetails.dateFormat) &&
        Objects.equals(this.delimiter, ksdOutPutFileDetails.delimiter) &&
        Objects.equals(this.fileName, ksdOutPutFileDetails.fileName) &&
        Objects.equals(this.fileNameWoutSpace, ksdOutPutFileDetails.fileNameWoutSpace) &&
        Objects.equals(this.fileType, ksdOutPutFileDetails.fileType) &&
        Objects.equals(this.generateReport, ksdOutPutFileDetails.generateReport) &&
        Objects.equals(this.id, ksdOutPutFileDetails.id) &&
        Objects.equals(this.maxThreshold, ksdOutPutFileDetails.maxThreshold) &&
        Objects.equals(this.minThreshold, ksdOutPutFileDetails.minThreshold) &&
        Objects.equals(this.mirrorFileName, ksdOutPutFileDetails.mirrorFileName) &&
        Objects.equals(this.outputReports, ksdOutPutFileDetails.outputReports) &&
        Objects.equals(this.participantRecordIdentifiers, ksdOutPutFileDetails.participantRecordIdentifiers) &&
        Objects.equals(this.pptIdentifier, ksdOutPutFileDetails.pptIdentifier) &&
        Objects.equals(this.pptIdentifierType, ksdOutPutFileDetails.pptIdentifierType) &&
        Objects.equals(this.prevReportFileName, ksdOutPutFileDetails.prevReportFileName) &&
        Objects.equals(this.prevReportFileNameWs, ksdOutPutFileDetails.prevReportFileNameWs) &&
        Objects.equals(this.processJobMapping, ksdOutPutFileDetails.processJobMapping) &&
        Objects.equals(this.referReport, ksdOutPutFileDetails.referReport) &&
        Objects.equals(this.reportDataCleanse, ksdOutPutFileDetails.reportDataCleanse) &&
        Objects.equals(this.reportFormat, ksdOutPutFileDetails.reportFormat) &&
        Objects.equals(this.reportPivot, ksdOutPutFileDetails.reportPivot) &&
        Objects.equals(this.sender, ksdOutPutFileDetails.sender) &&
        Objects.equals(this.sheetName, ksdOutPutFileDetails.sheetName) &&
        Objects.equals(this.sheetNameWoutSpace, ksdOutPutFileDetails.sheetNameWoutSpace) &&
        Objects.equals(this.subject, ksdOutPutFileDetails.subject) &&
        Objects.equals(this.updatedBy, ksdOutPutFileDetails.updatedBy) &&
        Objects.equals(this.updatedDate, ksdOutPutFileDetails.updatedDate) &&
        Objects.equals(this.useTemplete, ksdOutPutFileDetails.useTemplete) &&
        Objects.equals(this.variation, ksdOutPutFileDetails.variation);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, appendFormat, appendName, createdBy, createdDate, dateFormat, delimiter, fileName, fileNameWoutSpace, fileType, generateReport, id, maxThreshold, minThreshold, mirrorFileName, outputReports, participantRecordIdentifiers, pptIdentifier, pptIdentifierType, prevReportFileName, prevReportFileNameWs, processJobMapping, referReport, reportDataCleanse, reportFormat, reportPivot, sender, sheetName, sheetNameWoutSpace, subject, updatedBy, updatedDate, useTemplete, variation);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KsdOutPutFileDetails {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    appendFormat: ").append(toIndentedString(appendFormat)).append("\n");
    sb.append("    appendName: ").append(toIndentedString(appendName)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    dateFormat: ").append(toIndentedString(dateFormat)).append("\n");
    sb.append("    delimiter: ").append(toIndentedString(delimiter)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    fileNameWoutSpace: ").append(toIndentedString(fileNameWoutSpace)).append("\n");
    sb.append("    fileType: ").append(toIndentedString(fileType)).append("\n");
    sb.append("    generateReport: ").append(toIndentedString(generateReport)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    maxThreshold: ").append(toIndentedString(maxThreshold)).append("\n");
    sb.append("    minThreshold: ").append(toIndentedString(minThreshold)).append("\n");
    sb.append("    mirrorFileName: ").append(toIndentedString(mirrorFileName)).append("\n");
    sb.append("    outputReports: ").append(toIndentedString(outputReports)).append("\n");
    sb.append("    participantRecordIdentifiers: ").append(toIndentedString(participantRecordIdentifiers)).append("\n");
    sb.append("    pptIdentifier: ").append(toIndentedString(pptIdentifier)).append("\n");
    sb.append("    pptIdentifierType: ").append(toIndentedString(pptIdentifierType)).append("\n");
    sb.append("    prevReportFileName: ").append(toIndentedString(prevReportFileName)).append("\n");
    sb.append("    prevReportFileNameWs: ").append(toIndentedString(prevReportFileNameWs)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    referReport: ").append(toIndentedString(referReport)).append("\n");
    sb.append("    reportDataCleanse: ").append(toIndentedString(reportDataCleanse)).append("\n");
    sb.append("    reportFormat: ").append(toIndentedString(reportFormat)).append("\n");
    sb.append("    reportPivot: ").append(toIndentedString(reportPivot)).append("\n");
    sb.append("    sender: ").append(toIndentedString(sender)).append("\n");
    sb.append("    sheetName: ").append(toIndentedString(sheetName)).append("\n");
    sb.append("    sheetNameWoutSpace: ").append(toIndentedString(sheetNameWoutSpace)).append("\n");
    sb.append("    subject: ").append(toIndentedString(subject)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    useTemplete: ").append(toIndentedString(useTemplete)).append("\n");
    sb.append("    variation: ").append(toIndentedString(variation)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

