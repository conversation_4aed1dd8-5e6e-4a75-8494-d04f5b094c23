/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.ValidationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * RulesDefinition
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class RulesDefinition   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("conversionType")
  private String conversionType = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("json")
  private String json = null;

  @JsonProperty("jsonWoutName")
  private String jsonWoutName = null;

  @JsonProperty("primaryFieldName")
  private String primaryFieldName = null;

  @JsonProperty("primaryFieldWoutSpace")
  private String primaryFieldWoutSpace = null;

  @JsonProperty("ruleName")
  private String ruleName = null;

  @JsonProperty("rulesConfig")
  private RulesConfig rulesConfig = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("conditionJson")
  private byte[] conditionJson = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("validationType")
  private ValidationType validationType = null;

  @JsonProperty("varOperationJson")
  private String varOperationJson = null;

  @JsonProperty("varOperationJsonWoutSpace")
  private String varOperationJsonWoutSpace = null;

  @JsonProperty("varResultJson")
  private String varResultJson = null;

  @JsonProperty("varResultJsonWoutSpace")
  private String varResultJsonWoutSpace = null;

  public RulesDefinition activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public RulesDefinition conversionType(String conversionType) {
    this.conversionType = conversionType;
    return this;
  }

   /**
   * Get conversionType
   * @return conversionType
  **/
  @JsonProperty("conversionType")
  @ApiModelProperty(value = "")
  public String getConversionType() {
    return conversionType;
  }

  public void setConversionType(String conversionType) {
    this.conversionType = conversionType;
  }

  public RulesDefinition createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public RulesDefinition createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public RulesDefinition fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public RulesDefinition id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public RulesDefinition json(String json) {
    this.json = json;
    return this;
  }

   /**
   * Get json
   * @return json
  **/
  @JsonProperty("json")
  @ApiModelProperty(value = "")
  public String getJson() {
    return json;
  }

  public void setJson(String json) {
    this.json = json;
  }

  public RulesDefinition jsonWoutName(String jsonWoutName) {
    this.jsonWoutName = jsonWoutName;
    return this;
  }

   /**
   * Get jsonWoutName
   * @return jsonWoutName
  **/
  @JsonProperty("jsonWoutName")
  @ApiModelProperty(value = "")
  public String getJsonWoutName() {
    return jsonWoutName;
  }

  public void setJsonWoutName(String jsonWoutName) {
    this.jsonWoutName = jsonWoutName;
  }

  public RulesDefinition primaryFieldName(String primaryFieldName) {
    this.primaryFieldName = primaryFieldName;
    return this;
  }

   /**
   * Get primaryFieldName
   * @return primaryFieldName
  **/
  @JsonProperty("primaryFieldName")
  @ApiModelProperty(value = "")
  public String getPrimaryFieldName() {
    return primaryFieldName;
  }

  public void setPrimaryFieldName(String primaryFieldName) {
    this.primaryFieldName = primaryFieldName;
  }

  public RulesDefinition primaryFieldWoutSpace(String primaryFieldWoutSpace) {
    this.primaryFieldWoutSpace = primaryFieldWoutSpace;
    return this;
  }

   /**
   * Get primaryFieldWoutSpace
   * @return primaryFieldWoutSpace
  **/
  @JsonProperty("primaryFieldWoutSpace")
  @ApiModelProperty(value = "")
  public String getPrimaryFieldWoutSpace() {
    return primaryFieldWoutSpace;
  }

  public void setPrimaryFieldWoutSpace(String primaryFieldWoutSpace) {
    this.primaryFieldWoutSpace = primaryFieldWoutSpace;
  }

  public RulesDefinition ruleName(String ruleName) {
    this.ruleName = ruleName;
    return this;
  }

   /**
   * Get ruleName
   * @return ruleName
  **/
  @JsonProperty("ruleName")
  @ApiModelProperty(value = "")
  public String getRuleName() {
    return ruleName;
  }

  public void setRuleName(String ruleName) {
    this.ruleName = ruleName;
  }

  public RulesDefinition rulesConfig(RulesConfig rulesConfig) {
    this.rulesConfig = rulesConfig;
    return this;
  }

   /**
   * Get rulesConfig
   * @return rulesConfig
  **/
  @JsonProperty("rulesConfig")
  @ApiModelProperty(value = "")
  public RulesConfig getRulesConfig() {
    return rulesConfig;
  }

  public void setRulesConfig(RulesConfig rulesConfig) {
    this.rulesConfig = rulesConfig;
  }

  public RulesDefinition updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public RulesDefinition conditionJson(byte[] conditionJson) {
    this.conditionJson = conditionJson;
    return this;
  }

   /**
   * Get conditionJson
   * @return conditionJson
  **/
  @JsonProperty("conditionJson")
  @ApiModelProperty(value = "")
  public byte[] getConditionJson() {
    return conditionJson;
  }

  public void setConditionJson(byte[] conditionJson) {
    this.conditionJson = conditionJson;
  }

  public RulesDefinition updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public RulesDefinition validationType(ValidationType validationType) {
    this.validationType = validationType;
    return this;
  }

   /**
   * Get validationType
   * @return validationType
  **/
  @JsonProperty("validationType")
  @ApiModelProperty(value = "")
  public ValidationType getValidationType() {
    return validationType;
  }

  public void setValidationType(ValidationType validationType) {
    this.validationType = validationType;
  }

  public RulesDefinition varOperationJson(String varOperationJson) {
    this.varOperationJson = varOperationJson;
    return this;
  }

   /**
   * Get varOperationJson
   * @return varOperationJson
  **/
  @JsonProperty("varOperationJson")
  @ApiModelProperty(value = "")
  public String getVarOperationJson() {
    return varOperationJson;
  }

  public void setVarOperationJson(String varOperationJson) {
    this.varOperationJson = varOperationJson;
  }

  public RulesDefinition varOperationJsonWoutSpace(String varOperationJsonWoutSpace) {
    this.varOperationJsonWoutSpace = varOperationJsonWoutSpace;
    return this;
  }

   /**
   * Get varOperationJsonWoutSpace
   * @return varOperationJsonWoutSpace
  **/
  @JsonProperty("varOperationJsonWoutSpace")
  @ApiModelProperty(value = "")
  public String getVarOperationJsonWoutSpace() {
    return varOperationJsonWoutSpace;
  }

  public void setVarOperationJsonWoutSpace(String varOperationJsonWoutSpace) {
    this.varOperationJsonWoutSpace = varOperationJsonWoutSpace;
  }

  public RulesDefinition varResultJson(String varResultJson) {
    this.varResultJson = varResultJson;
    return this;
  }

   /**
   * Get varResultJson
   * @return varResultJson
  **/
  @JsonProperty("varResultJson")
  @ApiModelProperty(value = "")
  public String getVarResultJson() {
    return varResultJson;
  }

  public void setVarResultJson(String varResultJson) {
    this.varResultJson = varResultJson;
  }

  public RulesDefinition varResultJsonWoutSpace(String varResultJsonWoutSpace) {
    this.varResultJsonWoutSpace = varResultJsonWoutSpace;
    return this;
  }

   /**
   * Get varResultJsonWoutSpace
   * @return varResultJsonWoutSpace
  **/
  @JsonProperty("varResultJsonWoutSpace")
  @ApiModelProperty(value = "")
  public String getVarResultJsonWoutSpace() {
    return varResultJsonWoutSpace;
  }

  public void setVarResultJsonWoutSpace(String varResultJsonWoutSpace) {
    this.varResultJsonWoutSpace = varResultJsonWoutSpace;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RulesDefinition rulesDefinition = (RulesDefinition) o;
    return Objects.equals(this.activeFlag, rulesDefinition.activeFlag) &&
        Objects.equals(this.conversionType, rulesDefinition.conversionType) &&
        Objects.equals(this.createdBy, rulesDefinition.createdBy) &&
        Objects.equals(this.createdDate, rulesDefinition.createdDate) &&
        Objects.equals(this.fileName, rulesDefinition.fileName) &&
        Objects.equals(this.id, rulesDefinition.id) &&
        Objects.equals(this.json, rulesDefinition.json) &&
        Objects.equals(this.jsonWoutName, rulesDefinition.jsonWoutName) &&
        Objects.equals(this.primaryFieldName, rulesDefinition.primaryFieldName) &&
        Objects.equals(this.primaryFieldWoutSpace, rulesDefinition.primaryFieldWoutSpace) &&
        Objects.equals(this.ruleName, rulesDefinition.ruleName) &&
        Objects.equals(this.rulesConfig, rulesDefinition.rulesConfig) &&
        Objects.equals(this.updatedBy, rulesDefinition.updatedBy) &&
        Objects.equals(this.conditionJson, rulesDefinition.conditionJson) &&
        Objects.equals(this.updatedDate, rulesDefinition.updatedDate) &&
        Objects.equals(this.validationType, rulesDefinition.validationType) &&
        Objects.equals(this.varOperationJson, rulesDefinition.varOperationJson) &&
        Objects.equals(this.varOperationJsonWoutSpace, rulesDefinition.varOperationJsonWoutSpace) &&
        Objects.equals(this.varResultJson, rulesDefinition.varResultJson) &&
        Objects.equals(this.varResultJsonWoutSpace, rulesDefinition.varResultJsonWoutSpace);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, conversionType, createdBy, createdDate, fileName, id, json, jsonWoutName, primaryFieldName, primaryFieldWoutSpace, ruleName, rulesConfig, updatedBy, conditionJson, updatedDate, validationType, varOperationJson, varOperationJsonWoutSpace, varResultJson, varResultJsonWoutSpace);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RulesDefinition {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    conversionType: ").append(toIndentedString(conversionType)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    json: ").append(toIndentedString(json)).append("\n");
    sb.append("    jsonWoutName: ").append(toIndentedString(jsonWoutName)).append("\n");
    sb.append("    primaryFieldName: ").append(toIndentedString(primaryFieldName)).append("\n");
    sb.append("    primaryFieldWoutSpace: ").append(toIndentedString(primaryFieldWoutSpace)).append("\n");
    sb.append("    ruleName: ").append(toIndentedString(ruleName)).append("\n");
    sb.append("    rulesConfig: ").append(toIndentedString(rulesConfig)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    conditionJson: ").append(toIndentedString(conditionJson)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    validationType: ").append(toIndentedString(validationType)).append("\n");
    sb.append("    varOperationJson: ").append(toIndentedString(varOperationJson)).append("\n");
    sb.append("    varOperationJsonWoutSpace: ").append(toIndentedString(varOperationJsonWoutSpace)).append("\n");
    sb.append("    varResultJson: ").append(toIndentedString(varResultJson)).append("\n");
    sb.append("    varResultJsonWoutSpace: ").append(toIndentedString(varResultJsonWoutSpace)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

