/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ProcessFeatureConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ProcessFeatureConfig   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("approvedBy")
  private String approvedBy = null;

  @JsonProperty("approvedDate")
  private Date approvedDate = null;

  @JsonProperty("businessOpsName")
  private String businessOpsName = null;

  @JsonProperty("businessUnitName")
  private String businessUnitName = null;

  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("configStatus")
  private String configStatus = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eftSubject")
  private String eftSubject = null;

  @JsonProperty("eftSubjectCopied")
  private String eftSubjectCopied = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobCopied")
  private String jobCopied = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("phaseNames")
  private String phaseNames = null;

  @JsonProperty("pjmIdCopied")
  private Long pjmIdCopied = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public ProcessFeatureConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ProcessFeatureConfig approvedBy(String approvedBy) {
    this.approvedBy = approvedBy;
    return this;
  }

   /**
   * Get approvedBy
   * @return approvedBy
  **/
  @JsonProperty("approvedBy")
  @ApiModelProperty(value = "")
  public String getApprovedBy() {
    return approvedBy;
  }

  public void setApprovedBy(String approvedBy) {
    this.approvedBy = approvedBy;
  }

  public ProcessFeatureConfig approvedDate(Date approvedDate) {
    this.approvedDate = approvedDate;
    return this;
  }

   /**
   * Get approvedDate
   * @return approvedDate
  **/
  @JsonProperty("approvedDate")
  @ApiModelProperty(value = "")
  public Date getApprovedDate() {
    return approvedDate;
  }

  public void setApprovedDate(Date approvedDate) {
    this.approvedDate = approvedDate;
  }

  public ProcessFeatureConfig businessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
    return this;
  }

   /**
   * Get businessOpsName
   * @return businessOpsName
  **/
  @JsonProperty("businessOpsName")
  @ApiModelProperty(value = "")
  public String getBusinessOpsName() {
    return businessOpsName;
  }

  public void setBusinessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
  }

  public ProcessFeatureConfig businessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
    return this;
  }

   /**
   * Get businessUnitName
   * @return businessUnitName
  **/
  @JsonProperty("businessUnitName")
  @ApiModelProperty(value = "")
  public String getBusinessUnitName() {
    return businessUnitName;
  }

  public void setBusinessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
  }

  public ProcessFeatureConfig clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public ProcessFeatureConfig clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public ProcessFeatureConfig configStatus(String configStatus) {
    this.configStatus = configStatus;
    return this;
  }

   /**
   * Get configStatus
   * @return configStatus
  **/
  @JsonProperty("configStatus")
  @ApiModelProperty(value = "")
  public String getConfigStatus() {
    return configStatus;
  }

  public void setConfigStatus(String configStatus) {
    this.configStatus = configStatus;
  }

  public ProcessFeatureConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ProcessFeatureConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ProcessFeatureConfig eftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
    return this;
  }

   /**
   * Get eftSubject
   * @return eftSubject
  **/
  @JsonProperty("eftSubject")
  @ApiModelProperty(value = "")
  public String getEftSubject() {
    return eftSubject;
  }

  public void setEftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
  }

  public ProcessFeatureConfig eftSubjectCopied(String eftSubjectCopied) {
    this.eftSubjectCopied = eftSubjectCopied;
    return this;
  }

   /**
   * Get eftSubjectCopied
   * @return eftSubjectCopied
  **/
  @JsonProperty("eftSubjectCopied")
  @ApiModelProperty(value = "")
  public String getEftSubjectCopied() {
    return eftSubjectCopied;
  }

  public void setEftSubjectCopied(String eftSubjectCopied) {
    this.eftSubjectCopied = eftSubjectCopied;
  }

  public ProcessFeatureConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ProcessFeatureConfig jobCopied(String jobCopied) {
    this.jobCopied = jobCopied;
    return this;
  }

   /**
   * Get jobCopied
   * @return jobCopied
  **/
  @JsonProperty("jobCopied")
  @ApiModelProperty(value = "")
  public String getJobCopied() {
    return jobCopied;
  }

  public void setJobCopied(String jobCopied) {
    this.jobCopied = jobCopied;
  }

  public ProcessFeatureConfig jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public ProcessFeatureConfig ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public ProcessFeatureConfig phaseNames(String phaseNames) {
    this.phaseNames = phaseNames;
    return this;
  }

   /**
   * Get phaseNames
   * @return phaseNames
  **/
  @JsonProperty("phaseNames")
  @ApiModelProperty(value = "")
  public String getPhaseNames() {
    return phaseNames;
  }

  public void setPhaseNames(String phaseNames) {
    this.phaseNames = phaseNames;
  }

  public ProcessFeatureConfig pjmIdCopied(Long pjmIdCopied) {
    this.pjmIdCopied = pjmIdCopied;
    return this;
  }

   /**
   * Get pjmIdCopied
   * @return pjmIdCopied
  **/
  @JsonProperty("pjmIdCopied")
  @ApiModelProperty(value = "")
  public Long getPjmIdCopied() {
    return pjmIdCopied;
  }

  public void setPjmIdCopied(Long pjmIdCopied) {
    this.pjmIdCopied = pjmIdCopied;
  }

  public ProcessFeatureConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public ProcessFeatureConfig processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public ProcessFeatureConfig processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public ProcessFeatureConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ProcessFeatureConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessFeatureConfig processFeatureConfig = (ProcessFeatureConfig) o;
    return Objects.equals(this.activeFlag, processFeatureConfig.activeFlag) &&
        Objects.equals(this.approvedBy, processFeatureConfig.approvedBy) &&
        Objects.equals(this.approvedDate, processFeatureConfig.approvedDate) &&
        Objects.equals(this.businessOpsName, processFeatureConfig.businessOpsName) &&
        Objects.equals(this.businessUnitName, processFeatureConfig.businessUnitName) &&
        Objects.equals(this.clientCode, processFeatureConfig.clientCode) &&
        Objects.equals(this.clientName, processFeatureConfig.clientName) &&
        Objects.equals(this.configStatus, processFeatureConfig.configStatus) &&
        Objects.equals(this.createdBy, processFeatureConfig.createdBy) &&
        Objects.equals(this.createdDate, processFeatureConfig.createdDate) &&
        Objects.equals(this.eftSubject, processFeatureConfig.eftSubject) &&
        Objects.equals(this.eftSubjectCopied, processFeatureConfig.eftSubjectCopied) &&
        Objects.equals(this.id, processFeatureConfig.id) &&
        Objects.equals(this.jobCopied, processFeatureConfig.jobCopied) &&
        Objects.equals(this.jobName, processFeatureConfig.jobName) &&
        Objects.equals(this.ksdName, processFeatureConfig.ksdName) &&
        Objects.equals(this.phaseNames, processFeatureConfig.phaseNames) &&
        Objects.equals(this.pjmIdCopied, processFeatureConfig.pjmIdCopied) &&
        Objects.equals(this.processJobMapping, processFeatureConfig.processJobMapping) &&
        Objects.equals(this.processName, processFeatureConfig.processName) &&
        Objects.equals(this.processType, processFeatureConfig.processType) &&
        Objects.equals(this.updatedBy, processFeatureConfig.updatedBy) &&
        Objects.equals(this.updatedDate, processFeatureConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, approvedBy, approvedDate, businessOpsName, businessUnitName, clientCode, clientName, configStatus, createdBy, createdDate, eftSubject, eftSubjectCopied, id, jobCopied, jobName, ksdName, phaseNames, pjmIdCopied, processJobMapping, processName, processType, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessFeatureConfig {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    approvedBy: ").append(toIndentedString(approvedBy)).append("\n");
    sb.append("    approvedDate: ").append(toIndentedString(approvedDate)).append("\n");
    sb.append("    businessOpsName: ").append(toIndentedString(businessOpsName)).append("\n");
    sb.append("    businessUnitName: ").append(toIndentedString(businessUnitName)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    configStatus: ").append(toIndentedString(configStatus)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eftSubject: ").append(toIndentedString(eftSubject)).append("\n");
    sb.append("    eftSubjectCopied: ").append(toIndentedString(eftSubjectCopied)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobCopied: ").append(toIndentedString(jobCopied)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    phaseNames: ").append(toIndentedString(phaseNames)).append("\n");
    sb.append("    pjmIdCopied: ").append(toIndentedString(pjmIdCopied)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

