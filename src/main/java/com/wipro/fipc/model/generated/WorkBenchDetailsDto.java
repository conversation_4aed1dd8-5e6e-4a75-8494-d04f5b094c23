/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * WorkBenchDetailsDto
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class WorkBenchDetailsDto   {
  @JsonProperty("reqStatusList")
  private List<String> reqStatusList = new ArrayList<String>();

  @JsonProperty("source")
  private String source = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("uidList")
  private List<String> uidList = new ArrayList<String>();

  public WorkBenchDetailsDto reqStatusList(List<String> reqStatusList) {
    this.reqStatusList = reqStatusList;
    return this;
  }

  public WorkBenchDetailsDto addReqStatusListItem(String reqStatusListItem) {
    this.reqStatusList.add(reqStatusListItem);
    return this;
  }

   /**
   * Get reqStatusList
   * @return reqStatusList
  **/
  @JsonProperty("reqStatusList")
  @ApiModelProperty(value = "")
  public List<String> getReqStatusList() {
    return reqStatusList;
  }

  public void setReqStatusList(List<String> reqStatusList) {
    this.reqStatusList = reqStatusList;
  }

  public WorkBenchDetailsDto source(String source) {
    this.source = source;
    return this;
  }

   /**
   * Get source
   * @return source
  **/
  @JsonProperty("source")
  @ApiModelProperty(value = "")
  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public WorkBenchDetailsDto status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public WorkBenchDetailsDto uidList(List<String> uidList) {
    this.uidList = uidList;
    return this;
  }

  public WorkBenchDetailsDto addUidListItem(String uidListItem) {
    this.uidList.add(uidListItem);
    return this;
  }

   /**
   * Get uidList
   * @return uidList
  **/
  @JsonProperty("uidList")
  @ApiModelProperty(value = "")
  public List<String> getUidList() {
    return uidList;
  }

  public void setUidList(List<String> uidList) {
    this.uidList = uidList;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WorkBenchDetailsDto workBenchDetailsDto = (WorkBenchDetailsDto) o;
    return Objects.equals(this.reqStatusList, workBenchDetailsDto.reqStatusList) &&
        Objects.equals(this.source, workBenchDetailsDto.source) &&
        Objects.equals(this.status, workBenchDetailsDto.status) &&
        Objects.equals(this.uidList, workBenchDetailsDto.uidList);
  }

  @Override
  public int hashCode() {
    return Objects.hash(reqStatusList, source, status, uidList);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WorkBenchDetailsDto {\n");
    
    sb.append("    reqStatusList: ").append(toIndentedString(reqStatusList)).append("\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    uidList: ").append(toIndentedString(uidList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

