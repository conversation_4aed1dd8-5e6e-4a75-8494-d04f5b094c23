/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * CustomClientDetailsBO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class CustomClientDetailsBO   {
  @JsonProperty("buOpsId")
  private Long buOpsId = null;

  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientId")
  private Long clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("opsCode")
  private String opsCode = null;

  @JsonProperty("opsName")
  private String opsName = null;

  public CustomClientDetailsBO buOpsId(Long buOpsId) {
    this.buOpsId = buOpsId;
    return this;
  }

   /**
   * Get buOpsId
   * @return buOpsId
  **/
  @JsonProperty("buOpsId")
  @ApiModelProperty(value = "")
  public Long getBuOpsId() {
    return buOpsId;
  }

  public void setBuOpsId(Long buOpsId) {
    this.buOpsId = buOpsId;
  }

  public CustomClientDetailsBO clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public CustomClientDetailsBO clientId(Long clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Long getClientId() {
    return clientId;
  }

  public void setClientId(Long clientId) {
    this.clientId = clientId;
  }

  public CustomClientDetailsBO clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public CustomClientDetailsBO opsCode(String opsCode) {
    this.opsCode = opsCode;
    return this;
  }

   /**
   * Get opsCode
   * @return opsCode
  **/
  @JsonProperty("opsCode")
  @ApiModelProperty(value = "")
  public String getOpsCode() {
    return opsCode;
  }

  public void setOpsCode(String opsCode) {
    this.opsCode = opsCode;
  }

  public CustomClientDetailsBO opsName(String opsName) {
    this.opsName = opsName;
    return this;
  }

   /**
   * Get opsName
   * @return opsName
  **/
  @JsonProperty("opsName")
  @ApiModelProperty(value = "")
  public String getOpsName() {
    return opsName;
  }

  public void setOpsName(String opsName) {
    this.opsName = opsName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomClientDetailsBO customClientDetailsBO = (CustomClientDetailsBO) o;
    return Objects.equals(this.buOpsId, customClientDetailsBO.buOpsId) &&
        Objects.equals(this.clientCode, customClientDetailsBO.clientCode) &&
        Objects.equals(this.clientId, customClientDetailsBO.clientId) &&
        Objects.equals(this.clientName, customClientDetailsBO.clientName) &&
        Objects.equals(this.opsCode, customClientDetailsBO.opsCode) &&
        Objects.equals(this.opsName, customClientDetailsBO.opsName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(buOpsId, clientCode, clientId, clientName, opsCode, opsName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomClientDetailsBO {\n");
    
    sb.append("    buOpsId: ").append(toIndentedString(buOpsId)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    opsCode: ").append(toIndentedString(opsCode)).append("\n");
    sb.append("    opsName: ").append(toIndentedString(opsName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

