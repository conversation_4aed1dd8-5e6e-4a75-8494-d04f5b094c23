/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * BusinessOpsBo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class BusinessOpsBo   {
  @JsonProperty("opsCode")
  private String opsCode = null;

  @JsonProperty("opsId")
  private Long opsId = null;

  @JsonProperty("opsName")
  private String opsName = null;

  public BusinessOpsBo opsCode(String opsCode) {
    this.opsCode = opsCode;
    return this;
  }

   /**
   * Get opsCode
   * @return opsCode
  **/
  @JsonProperty("opsCode")
  @ApiModelProperty(value = "")
  public String getOpsCode() {
    return opsCode;
  }

  public void setOpsCode(String opsCode) {
    this.opsCode = opsCode;
  }

  public BusinessOpsBo opsId(Long opsId) {
    this.opsId = opsId;
    return this;
  }

   /**
   * Get opsId
   * @return opsId
  **/
  @JsonProperty("opsId")
  @ApiModelProperty(value = "")
  public Long getOpsId() {
    return opsId;
  }

  public void setOpsId(Long opsId) {
    this.opsId = opsId;
  }

  public BusinessOpsBo opsName(String opsName) {
    this.opsName = opsName;
    return this;
  }

   /**
   * Get opsName
   * @return opsName
  **/
  @JsonProperty("opsName")
  @ApiModelProperty(value = "")
  public String getOpsName() {
    return opsName;
  }

  public void setOpsName(String opsName) {
    this.opsName = opsName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BusinessOpsBo businessOpsBo = (BusinessOpsBo) o;
    return Objects.equals(this.opsCode, businessOpsBo.opsCode) &&
        Objects.equals(this.opsId, businessOpsBo.opsId) &&
        Objects.equals(this.opsName, businessOpsBo.opsName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(opsCode, opsId, opsName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BusinessOpsBo {\n");
    
    sb.append("    opsCode: ").append(toIndentedString(opsCode)).append("\n");
    sb.append("    opsId: ").append(toIndentedString(opsId)).append("\n");
    sb.append("    opsName: ").append(toIndentedString(opsName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

