/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * ModelApiResponse
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ModelApiResponse   {
  @JsonProperty("MESSAGE")
  private String MESSAGE = null;

  @JsonProperty("STATUS")
  private String STATUS = null;

  public ModelApiResponse MESSAGE(String MESSAGE) {
    this.MESSAGE = MESSAGE;
    return this;
  }

   /**
   * Get MESSAGE
   * @return MESSAGE
  **/
  @JsonProperty("MESSAGE")
  @ApiModelProperty(value = "")
  public String getMESSAGE() {
    return MESSAGE;
  }

  public void setMESSAGE(String MESSAGE) {
    this.MESSAGE = MESSAGE;
  }

  public ModelApiResponse STATUS(String STATUS) {
    this.STATUS = STATUS;
    return this;
  }

   /**
   * Get STATUS
   * @return STATUS
  **/
  @JsonProperty("STATUS")
  @ApiModelProperty(value = "")
  public String getSTATUS() {
    return STATUS;
  }

  public void setSTATUS(String STATUS) {
    this.STATUS = STATUS;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModelApiResponse _apiResponse = (ModelApiResponse) o;
    return Objects.equals(this.MESSAGE, _apiResponse.MESSAGE) &&
        Objects.equals(this.STATUS, _apiResponse.STATUS);
  }

  @Override
  public int hashCode() {
    return Objects.hash(MESSAGE, STATUS);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModelApiResponse {\n");
    
    sb.append("    MESSAGE: ").append(toIndentedString(MESSAGE)).append("\n");
    sb.append("    STATUS: ").append(toIndentedString(STATUS)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

