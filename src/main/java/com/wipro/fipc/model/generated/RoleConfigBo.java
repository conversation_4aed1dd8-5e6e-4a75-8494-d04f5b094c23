/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.BusinessOpsBo;
import com.wipro.fipc.model.generated.BusinessUnitBo;
import com.wipro.fipc.model.generated.ClientDetailsBo;
import com.wipro.fipc.model.generated.ProcessBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * RoleConfigBo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class RoleConfigBo   {
  @JsonProperty("businessOps")
  private BusinessOpsBo businessOps = null;

  @JsonProperty("businessUnit")
  private BusinessUnitBo businessUnit = null;

  @JsonProperty("clientDetails")
  private ClientDetailsBo clientDetails = null;

  @JsonProperty("pjmId")
  private List<Long> pjmId = new ArrayList<Long>();

  @JsonProperty("processType")
  private ProcessBo processType = null;

  public RoleConfigBo businessOps(BusinessOpsBo businessOps) {
    this.businessOps = businessOps;
    return this;
  }

   /**
   * Get businessOps
   * @return businessOps
  **/
  @JsonProperty("businessOps")
  @ApiModelProperty(value = "")
  public BusinessOpsBo getBusinessOps() {
    return businessOps;
  }

  public void setBusinessOps(BusinessOpsBo businessOps) {
    this.businessOps = businessOps;
  }

  public RoleConfigBo businessUnit(BusinessUnitBo businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public BusinessUnitBo getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(BusinessUnitBo businessUnit) {
    this.businessUnit = businessUnit;
  }

  public RoleConfigBo clientDetails(ClientDetailsBo clientDetails) {
    this.clientDetails = clientDetails;
    return this;
  }

   /**
   * Get clientDetails
   * @return clientDetails
  **/
  @JsonProperty("clientDetails")
  @ApiModelProperty(value = "")
  public ClientDetailsBo getClientDetails() {
    return clientDetails;
  }

  public void setClientDetails(ClientDetailsBo clientDetails) {
    this.clientDetails = clientDetails;
  }

  public RoleConfigBo pjmId(List<Long> pjmId) {
    this.pjmId = pjmId;
    return this;
  }

  public RoleConfigBo addPjmIdItem(Long pjmIdItem) {
    this.pjmId.add(pjmIdItem);
    return this;
  }

   /**
   * Get pjmId
   * @return pjmId
  **/
  @JsonProperty("pjmId")
  @ApiModelProperty(value = "")
  public List<Long> getPjmId() {
    return pjmId;
  }

  public void setPjmId(List<Long> pjmId) {
    this.pjmId = pjmId;
  }

  public RoleConfigBo processType(ProcessBo processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public ProcessBo getProcessType() {
    return processType;
  }

  public void setProcessType(ProcessBo processType) {
    this.processType = processType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RoleConfigBo roleConfigBo = (RoleConfigBo) o;
    return Objects.equals(this.businessOps, roleConfigBo.businessOps) &&
        Objects.equals(this.businessUnit, roleConfigBo.businessUnit) &&
        Objects.equals(this.clientDetails, roleConfigBo.clientDetails) &&
        Objects.equals(this.pjmId, roleConfigBo.pjmId) &&
        Objects.equals(this.processType, roleConfigBo.processType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessOps, businessUnit, clientDetails, pjmId, processType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RoleConfigBo {\n");
    
    sb.append("    businessOps: ").append(toIndentedString(businessOps)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientDetails: ").append(toIndentedString(clientDetails)).append("\n");
    sb.append("    pjmId: ").append(toIndentedString(pjmId)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

