/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * ProcessCofigReqBody
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ProcessCofigReqBody   {
  @JsonProperty("adid")
  private String adid = null;

  @JsonProperty("filterType")
  private String filterType = null;

  @JsonProperty("filterValue")
  private String filterValue = null;

  @JsonProperty("pageNumber")
  private Integer pageNumber = null;

  @JsonProperty("rowCount")
  private Integer rowCount = null;

  public ProcessCofigReqBody adid(String adid) {
    this.adid = adid;
    return this;
  }

   /**
   * Get adid
   * @return adid
  **/
  @JsonProperty("adid")
  @ApiModelProperty(value = "")
  public String getAdid() {
    return adid;
  }

  public void setAdid(String adid) {
    this.adid = adid;
  }

  public ProcessCofigReqBody filterType(String filterType) {
    this.filterType = filterType;
    return this;
  }

   /**
   * Get filterType
   * @return filterType
  **/
  @JsonProperty("filterType")
  @ApiModelProperty(value = "")
  public String getFilterType() {
    return filterType;
  }

  public void setFilterType(String filterType) {
    this.filterType = filterType;
  }

  public ProcessCofigReqBody filterValue(String filterValue) {
    this.filterValue = filterValue;
    return this;
  }

   /**
   * Get filterValue
   * @return filterValue
  **/
  @JsonProperty("filterValue")
  @ApiModelProperty(value = "")
  public String getFilterValue() {
    return filterValue;
  }

  public void setFilterValue(String filterValue) {
    this.filterValue = filterValue;
  }

  public ProcessCofigReqBody pageNumber(Integer pageNumber) {
    this.pageNumber = pageNumber;
    return this;
  }

   /**
   * Get pageNumber
   * @return pageNumber
  **/
  @JsonProperty("pageNumber")
  @ApiModelProperty(value = "")
  public Integer getPageNumber() {
    return pageNumber;
  }

  public void setPageNumber(Integer pageNumber) {
    this.pageNumber = pageNumber;
  }

  public ProcessCofigReqBody rowCount(Integer rowCount) {
    this.rowCount = rowCount;
    return this;
  }

   /**
   * Get rowCount
   * @return rowCount
  **/
  @JsonProperty("rowCount")
  @ApiModelProperty(value = "")
  public Integer getRowCount() {
    return rowCount;
  }

  public void setRowCount(Integer rowCount) {
    this.rowCount = rowCount;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessCofigReqBody processCofigReqBody = (ProcessCofigReqBody) o;
    return Objects.equals(this.adid, processCofigReqBody.adid) &&
        Objects.equals(this.filterType, processCofigReqBody.filterType) &&
        Objects.equals(this.filterValue, processCofigReqBody.filterValue) &&
        Objects.equals(this.pageNumber, processCofigReqBody.pageNumber) &&
        Objects.equals(this.rowCount, processCofigReqBody.rowCount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(adid, filterType, filterValue, pageNumber, rowCount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessCofigReqBody {\n");
    
    sb.append("    adid: ").append(toIndentedString(adid)).append("\n");
    sb.append("    filterType: ").append(toIndentedString(filterType)).append("\n");
    sb.append("    filterValue: ").append(toIndentedString(filterValue)).append("\n");
    sb.append("    pageNumber: ").append(toIndentedString(pageNumber)).append("\n");
    sb.append("    rowCount: ").append(toIndentedString(rowCount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

