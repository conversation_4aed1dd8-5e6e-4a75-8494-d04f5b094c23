/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.BusinessOps;
import com.wipro.fipc.model.generated.BusinessUnit;
import com.wipro.fipc.model.generated.ClientDetails;
import com.wipro.fipc.model.generated.Process;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * RoleConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class RoleConfig   {
  @JsonProperty("active")
  private Boolean active = null;

  @JsonProperty("adid")
  private String adid = null;

  @JsonProperty("businessOps")
  private BusinessOps businessOps = null;

  @JsonProperty("businessUnit")
  private BusinessUnit businessUnit = null;

  @JsonProperty("clientDetails")
  private ClientDetails clientDetails = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("ownedBy")
  private String ownedBy = null;

  @JsonProperty("process")
  private Process process = null;

  @JsonProperty("role")
  private String role = null;

  @JsonProperty("type")
  private String type = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public RoleConfig active(Boolean active) {
    this.active = active;
    return this;
  }

   /**
   * Get active
   * @return active
  **/
  @JsonProperty("active")
  @ApiModelProperty(value = "")
  public Boolean getActive() {
    return active;
  }

  public void setActive(Boolean active) {
    this.active = active;
  }

  public RoleConfig adid(String adid) {
    this.adid = adid;
    return this;
  }

   /**
   * Get adid
   * @return adid
  **/
  @JsonProperty("adid")
  @ApiModelProperty(value = "")
  public String getAdid() {
    return adid;
  }

  public void setAdid(String adid) {
    this.adid = adid;
  }

  public RoleConfig businessOps(BusinessOps businessOps) {
    this.businessOps = businessOps;
    return this;
  }

   /**
   * Get businessOps
   * @return businessOps
  **/
  @JsonProperty("businessOps")
  @ApiModelProperty(value = "")
  public BusinessOps getBusinessOps() {
    return businessOps;
  }

  public void setBusinessOps(BusinessOps businessOps) {
    this.businessOps = businessOps;
  }

  public RoleConfig businessUnit(BusinessUnit businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public BusinessUnit getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(BusinessUnit businessUnit) {
    this.businessUnit = businessUnit;
  }

  public RoleConfig clientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
    return this;
  }

   /**
   * Get clientDetails
   * @return clientDetails
  **/
  @JsonProperty("clientDetails")
  @ApiModelProperty(value = "")
  public ClientDetails getClientDetails() {
    return clientDetails;
  }

  public void setClientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
  }

  public RoleConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public RoleConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public RoleConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public RoleConfig ownedBy(String ownedBy) {
    this.ownedBy = ownedBy;
    return this;
  }

   /**
   * Get ownedBy
   * @return ownedBy
  **/
  @JsonProperty("ownedBy")
  @ApiModelProperty(value = "")
  public String getOwnedBy() {
    return ownedBy;
  }

  public void setOwnedBy(String ownedBy) {
    this.ownedBy = ownedBy;
  }

  public RoleConfig process(Process process) {
    this.process = process;
    return this;
  }

   /**
   * Get process
   * @return process
  **/
  @JsonProperty("process")
  @ApiModelProperty(value = "")
  public Process getProcess() {
    return process;
  }

  public void setProcess(Process process) {
    this.process = process;
  }

  public RoleConfig role(String role) {
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public String getRole() {
    return role;
  }

  public void setRole(String role) {
    this.role = role;
  }

  public RoleConfig type(String type) {
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @JsonProperty("type")
  @ApiModelProperty(value = "")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public RoleConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public RoleConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RoleConfig roleConfig = (RoleConfig) o;
    return Objects.equals(this.active, roleConfig.active) &&
        Objects.equals(this.adid, roleConfig.adid) &&
        Objects.equals(this.businessOps, roleConfig.businessOps) &&
        Objects.equals(this.businessUnit, roleConfig.businessUnit) &&
        Objects.equals(this.clientDetails, roleConfig.clientDetails) &&
        Objects.equals(this.createdBy, roleConfig.createdBy) &&
        Objects.equals(this.createdDate, roleConfig.createdDate) &&
        Objects.equals(this.id, roleConfig.id) &&
        Objects.equals(this.ownedBy, roleConfig.ownedBy) &&
        Objects.equals(this.process, roleConfig.process) &&
        Objects.equals(this.role, roleConfig.role) &&
        Objects.equals(this.type, roleConfig.type) &&
        Objects.equals(this.updatedBy, roleConfig.updatedBy) &&
        Objects.equals(this.updatedDate, roleConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(active, adid, businessOps, businessUnit, clientDetails, createdBy, createdDate, id, ownedBy, process, role, type, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RoleConfig {\n");
    
    sb.append("    active: ").append(toIndentedString(active)).append("\n");
    sb.append("    adid: ").append(toIndentedString(adid)).append("\n");
    sb.append("    businessOps: ").append(toIndentedString(businessOps)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientDetails: ").append(toIndentedString(clientDetails)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    ownedBy: ").append(toIndentedString(ownedBy)).append("\n");
    sb.append("    process: ").append(toIndentedString(process)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

