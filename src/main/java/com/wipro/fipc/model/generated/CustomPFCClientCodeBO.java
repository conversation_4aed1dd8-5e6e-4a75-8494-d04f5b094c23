/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * CustomPFCClientCodeBO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class CustomPFCClientCodeBO   {
  @JsonProperty("approvedBy")
  private String approvedBy = null;

  @JsonProperty("approvedDate")
  private Date approvedDate = null;

  @JsonProperty("businessOpsName")
  private String businessOpsName = null;

  @JsonProperty("businessUnitName")
  private String businessUnitName = null;

  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("configStatus")
  private String configStatus = null;

  @JsonProperty("eftSubject")
  private String eftSubject = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("phaseNames")
  private String phaseNames = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public CustomPFCClientCodeBO approvedBy(String approvedBy) {
    this.approvedBy = approvedBy;
    return this;
  }

   /**
   * Get approvedBy
   * @return approvedBy
  **/
  @JsonProperty("approvedBy")
  @ApiModelProperty(value = "")
  public String getApprovedBy() {
    return approvedBy;
  }

  public void setApprovedBy(String approvedBy) {
    this.approvedBy = approvedBy;
  }

  public CustomPFCClientCodeBO approvedDate(Date approvedDate) {
    this.approvedDate = approvedDate;
    return this;
  }

   /**
   * Get approvedDate
   * @return approvedDate
  **/
  @JsonProperty("approvedDate")
  @ApiModelProperty(value = "")
  public Date getApprovedDate() {
    return approvedDate;
  }

  public void setApprovedDate(Date approvedDate) {
    this.approvedDate = approvedDate;
  }

  public CustomPFCClientCodeBO businessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
    return this;
  }

   /**
   * Get businessOpsName
   * @return businessOpsName
  **/
  @JsonProperty("businessOpsName")
  @ApiModelProperty(value = "")
  public String getBusinessOpsName() {
    return businessOpsName;
  }

  public void setBusinessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
  }

  public CustomPFCClientCodeBO businessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
    return this;
  }

   /**
   * Get businessUnitName
   * @return businessUnitName
  **/
  @JsonProperty("businessUnitName")
  @ApiModelProperty(value = "")
  public String getBusinessUnitName() {
    return businessUnitName;
  }

  public void setBusinessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
  }

  public CustomPFCClientCodeBO clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public CustomPFCClientCodeBO clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public CustomPFCClientCodeBO configStatus(String configStatus) {
    this.configStatus = configStatus;
    return this;
  }

   /**
   * Get configStatus
   * @return configStatus
  **/
  @JsonProperty("configStatus")
  @ApiModelProperty(value = "")
  public String getConfigStatus() {
    return configStatus;
  }

  public void setConfigStatus(String configStatus) {
    this.configStatus = configStatus;
  }

  public CustomPFCClientCodeBO eftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
    return this;
  }

   /**
   * Get eftSubject
   * @return eftSubject
  **/
  @JsonProperty("eftSubject")
  @ApiModelProperty(value = "")
  public String getEftSubject() {
    return eftSubject;
  }

  public void setEftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
  }

  public CustomPFCClientCodeBO id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public CustomPFCClientCodeBO jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public CustomPFCClientCodeBO ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public CustomPFCClientCodeBO phaseNames(String phaseNames) {
    this.phaseNames = phaseNames;
    return this;
  }

   /**
   * Get phaseNames
   * @return phaseNames
  **/
  @JsonProperty("phaseNames")
  @ApiModelProperty(value = "")
  public String getPhaseNames() {
    return phaseNames;
  }

  public void setPhaseNames(String phaseNames) {
    this.phaseNames = phaseNames;
  }

  public CustomPFCClientCodeBO processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public CustomPFCClientCodeBO processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public CustomPFCClientCodeBO processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public CustomPFCClientCodeBO updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public CustomPFCClientCodeBO updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomPFCClientCodeBO customPFCClientCodeBO = (CustomPFCClientCodeBO) o;
    return Objects.equals(this.approvedBy, customPFCClientCodeBO.approvedBy) &&
        Objects.equals(this.approvedDate, customPFCClientCodeBO.approvedDate) &&
        Objects.equals(this.businessOpsName, customPFCClientCodeBO.businessOpsName) &&
        Objects.equals(this.businessUnitName, customPFCClientCodeBO.businessUnitName) &&
        Objects.equals(this.clientCode, customPFCClientCodeBO.clientCode) &&
        Objects.equals(this.clientName, customPFCClientCodeBO.clientName) &&
        Objects.equals(this.configStatus, customPFCClientCodeBO.configStatus) &&
        Objects.equals(this.eftSubject, customPFCClientCodeBO.eftSubject) &&
        Objects.equals(this.id, customPFCClientCodeBO.id) &&
        Objects.equals(this.jobName, customPFCClientCodeBO.jobName) &&
        Objects.equals(this.ksdName, customPFCClientCodeBO.ksdName) &&
        Objects.equals(this.phaseNames, customPFCClientCodeBO.phaseNames) &&
        Objects.equals(this.processJobMappingId, customPFCClientCodeBO.processJobMappingId) &&
        Objects.equals(this.processName, customPFCClientCodeBO.processName) &&
        Objects.equals(this.processType, customPFCClientCodeBO.processType) &&
        Objects.equals(this.updatedBy, customPFCClientCodeBO.updatedBy) &&
        Objects.equals(this.updatedDate, customPFCClientCodeBO.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(approvedBy, approvedDate, businessOpsName, businessUnitName, clientCode, clientName, configStatus, eftSubject, id, jobName, ksdName, phaseNames, processJobMappingId, processName, processType, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomPFCClientCodeBO {\n");
    
    sb.append("    approvedBy: ").append(toIndentedString(approvedBy)).append("\n");
    sb.append("    approvedDate: ").append(toIndentedString(approvedDate)).append("\n");
    sb.append("    businessOpsName: ").append(toIndentedString(businessOpsName)).append("\n");
    sb.append("    businessUnitName: ").append(toIndentedString(businessUnitName)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    configStatus: ").append(toIndentedString(configStatus)).append("\n");
    sb.append("    eftSubject: ").append(toIndentedString(eftSubject)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    phaseNames: ").append(toIndentedString(phaseNames)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

