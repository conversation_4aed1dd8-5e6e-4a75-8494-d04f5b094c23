/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.BusinessUnitClient;
import com.wipro.fipc.model.generated.BusinessUnitOps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.*;

/**
 * BusinessUnit
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class BusinessUnit   {
  @JsonProperty("businessUnitClients")
  private List<BusinessUnitClient> businessUnitClients = new ArrayList<BusinessUnitClient>();

  @JsonProperty("businessUnitOps")
  private List<BusinessUnitOps> businessUnitOps = new ArrayList<BusinessUnitOps>();

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("unitCode")
  private String unitCode = null;

  @JsonProperty("unitName")
  private String unitName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public BusinessUnit businessUnitClients(List<BusinessUnitClient> businessUnitClients) {
    this.businessUnitClients = businessUnitClients;
    return this;
  }

  public BusinessUnit addBusinessUnitClientsItem(BusinessUnitClient businessUnitClientsItem) {
    this.businessUnitClients.add(businessUnitClientsItem);
    return this;
  }

   /**
   * Get businessUnitClients
   * @return businessUnitClients
  **/
  @JsonProperty("businessUnitClients")
  @ApiModelProperty(value = "")
  public List<BusinessUnitClient> getBusinessUnitClients() {
    return businessUnitClients;
  }

  public void setBusinessUnitClients(List<BusinessUnitClient> businessUnitClients) {
    this.businessUnitClients = businessUnitClients;
  }

  public BusinessUnit businessUnitOps(List<BusinessUnitOps> businessUnitOps) {
    this.businessUnitOps = businessUnitOps;
    return this;
  }

  public BusinessUnit addBusinessUnitOpsItem(BusinessUnitOps businessUnitOpsItem) {
    this.businessUnitOps.add(businessUnitOpsItem);
    return this;
  }

   /**
   * Get businessUnitOps
   * @return businessUnitOps
  **/
  @JsonProperty("businessUnitOps")
  @ApiModelProperty(value = "")
  public List<BusinessUnitOps> getBusinessUnitOps() {
    return businessUnitOps;
  }

  public void setBusinessUnitOps(List<BusinessUnitOps> businessUnitOps) {
    this.businessUnitOps = businessUnitOps;
  }

  public BusinessUnit createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public BusinessUnit createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public BusinessUnit id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public BusinessUnit unitCode(String unitCode) {
    this.unitCode = unitCode;
    return this;
  }

   /**
   * Get unitCode
   * @return unitCode
  **/
  @JsonProperty("unitCode")
  @ApiModelProperty(value = "")
  public String getUnitCode() {
    return unitCode;
  }

  public void setUnitCode(String unitCode) {
    this.unitCode = unitCode;
  }

  public BusinessUnit unitName(String unitName) {
    this.unitName = unitName;
    return this;
  }

   /**
   * Get unitName
   * @return unitName
  **/
  @JsonProperty("unitName")
  @ApiModelProperty(value = "")
  public String getUnitName() {
    return unitName;
  }

  public void setUnitName(String unitName) {
    this.unitName = unitName;
  }

  public BusinessUnit updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public BusinessUnit updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BusinessUnit businessUnit = (BusinessUnit) o;
    return Objects.equals(this.businessUnitClients, businessUnit.businessUnitClients) &&
        Objects.equals(this.businessUnitOps, businessUnit.businessUnitOps) &&
        Objects.equals(this.createdBy, businessUnit.createdBy) &&
        Objects.equals(this.createdDate, businessUnit.createdDate) &&
        Objects.equals(this.id, businessUnit.id) &&
        Objects.equals(this.unitCode, businessUnit.unitCode) &&
        Objects.equals(this.unitName, businessUnit.unitName) &&
        Objects.equals(this.updatedBy, businessUnit.updatedBy) &&
        Objects.equals(this.updatedDate, businessUnit.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessUnitClients, businessUnitOps, createdBy, createdDate, id, unitCode, unitName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BusinessUnit {\n");
    
    sb.append("    businessUnitClients: ").append(toIndentedString(businessUnitClients)).append("\n");
    sb.append("    businessUnitOps: ").append(toIndentedString(businessUnitOps)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    unitCode: ").append(toIndentedString(unitCode)).append("\n");
    sb.append("    unitName: ").append(toIndentedString(unitName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

