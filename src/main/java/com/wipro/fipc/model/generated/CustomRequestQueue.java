/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * CustomRequestQueue
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class CustomRequestQueue   {
  @JsonProperty("businessOps")
  private String businessOps = null;

  @JsonProperty("businessUnit")
  private String businessUnit = null;

  @JsonProperty("clientId")
  private List<String> clientId = new ArrayList<String>();

  @JsonProperty("endTimestamp")
  private Long endTimestamp = null;

  @JsonProperty("processId")
  private List<Long> processId = new ArrayList<Long>();

  @JsonProperty("startTimestamp")
  private Long startTimestamp = null;

  public CustomRequestQueue businessOps(String businessOps) {
    this.businessOps = businessOps;
    return this;
  }

   /**
   * Get businessOps
   * @return businessOps
  **/
  @JsonProperty("businessOps")
  @ApiModelProperty(value = "")
  public String getBusinessOps() {
    return businessOps;
  }

  public void setBusinessOps(String businessOps) {
    this.businessOps = businessOps;
  }

  public CustomRequestQueue businessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public String getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
  }

  public CustomRequestQueue clientId(List<String> clientId) {
    this.clientId = clientId;
    return this;
  }

  public CustomRequestQueue addClientIdItem(String clientIdItem) {
    this.clientId.add(clientIdItem);
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public List<String> getClientId() {
    return clientId;
  }

  public void setClientId(List<String> clientId) {
    this.clientId = clientId;
  }

  public CustomRequestQueue endTimestamp(Long endTimestamp) {
    this.endTimestamp = endTimestamp;
    return this;
  }

   /**
   * Get endTimestamp
   * @return endTimestamp
  **/
  @JsonProperty("endTimestamp")
  @ApiModelProperty(value = "")
  public Long getEndTimestamp() {
    return endTimestamp;
  }

  public void setEndTimestamp(Long endTimestamp) {
    this.endTimestamp = endTimestamp;
  }

  public CustomRequestQueue processId(List<Long> processId) {
    this.processId = processId;
    return this;
  }

  public CustomRequestQueue addProcessIdItem(Long processIdItem) {
    this.processId.add(processIdItem);
    return this;
  }

   /**
   * Get processId
   * @return processId
  **/
  @JsonProperty("processId")
  @ApiModelProperty(value = "")
  public List<Long> getProcessId() {
    return processId;
  }

  public void setProcessId(List<Long> processId) {
    this.processId = processId;
  }

  public CustomRequestQueue startTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
    return this;
  }

   /**
   * Get startTimestamp
   * @return startTimestamp
  **/
  @JsonProperty("startTimestamp")
  @ApiModelProperty(value = "")
  public Long getStartTimestamp() {
    return startTimestamp;
  }

  public void setStartTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomRequestQueue customRequestQueue = (CustomRequestQueue) o;
    return Objects.equals(this.businessOps, customRequestQueue.businessOps) &&
        Objects.equals(this.businessUnit, customRequestQueue.businessUnit) &&
        Objects.equals(this.clientId, customRequestQueue.clientId) &&
        Objects.equals(this.endTimestamp, customRequestQueue.endTimestamp) &&
        Objects.equals(this.processId, customRequestQueue.processId) &&
        Objects.equals(this.startTimestamp, customRequestQueue.startTimestamp);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessOps, businessUnit, clientId, endTimestamp, processId, startTimestamp);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomRequestQueue {\n");
    
    sb.append("    businessOps: ").append(toIndentedString(businessOps)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    endTimestamp: ").append(toIndentedString(endTimestamp)).append("\n");
    sb.append("    processId: ").append(toIndentedString(processId)).append("\n");
    sb.append("    startTimestamp: ").append(toIndentedString(startTimestamp)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

