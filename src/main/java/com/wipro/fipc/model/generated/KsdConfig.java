/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ClientConfigJobs;
import com.wipro.fipc.model.generated.JobSchedule;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.*;

/**
 * KsdConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-11-30T13:33:57.272+05:30")
public class KsdConfig   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("adxScriptId")
  private String adxScriptId = null;

  @JsonProperty("skipControlMReport")
  private Boolean skipControlMReport = null;

  @JsonProperty("taskCreationOnly")
  private Boolean taskCreationOnly = null;

  @JsonProperty("clientConfigJobs")
  private List<ClientConfigJobs> clientConfigJobs = new ArrayList<ClientConfigJobs>();

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("dailyTaskReportSubjectNameOutLook")
  private String dailyTaskReportSubjectNameOutLook = null;

  @JsonProperty("eftName")
  private String eftName = null;

  @JsonProperty("eftTriggerTime")
  private String eftTriggerTime = null;

  @JsonProperty("fileType")
  private String fileType = null;

  @JsonProperty("frequency")
  private String frequency = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobCutOffTime")
  private String jobCutOffTime = null;

  @JsonProperty("jobScheduleTime")
  private String jobScheduleTime = null;

  @JsonProperty("jobSchedules")
  private List<JobSchedule> jobSchedules = new ArrayList<JobSchedule>();

  @JsonProperty("jobStream")
  private String jobStream = null;

  @JsonProperty("maestroTaskName")
  private String maestroTaskName = null;

  @JsonProperty("primaryJobName")
  private String primaryJobName = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("turnaroundTime")
  private String turnaroundTime = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("directTBAUpdate")
  private Boolean directTBAUpdate = null;

  @JsonProperty("isJiraUsed")
  private Boolean isJiraUsed = null;

  @JsonProperty("namedType")
  private Boolean namedType = null;

  public KsdConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public KsdConfig adxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
    return this;
  }

   /**
   * Get adxScriptId
   * @return adxScriptId
  **/
  @JsonProperty("adxScriptId")
  @ApiModelProperty(value = "")
  public String getAdxScriptId() {
    return adxScriptId;
  }

  public void setAdxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
  }

  public KsdConfig skipControlMReport(Boolean skipControlMReport) {
    this.skipControlMReport = skipControlMReport;
    return this;
  }

   /**
   * Get skipControlMReport
   * @return skipControlMReport
  **/
  @JsonProperty("skipControlMReport")
  @ApiModelProperty(value = "")
  public Boolean getSkipControlMReport() {
    return skipControlMReport;
  }

  public void setSkipControlMReport(Boolean skipControlMReport) {
    this.skipControlMReport = skipControlMReport;
  }

  public KsdConfig taskCreationOnly(Boolean taskCreationOnly) {
    this.taskCreationOnly = taskCreationOnly;
    return this;
  }

   /**
   * Get taskCreationOnly
   * @return taskCreationOnly
  **/
  @JsonProperty("taskCreationOnly")
  @ApiModelProperty(value = "")
  public Boolean getTaskCreationOnly() {
    return taskCreationOnly;
  }

  public void setTaskCreationOnly(Boolean taskCreationOnly) {
    this.taskCreationOnly = taskCreationOnly;
  }

  public KsdConfig clientConfigJobs(List<ClientConfigJobs> clientConfigJobs) {
    this.clientConfigJobs = clientConfigJobs;
    return this;
  }

  public KsdConfig addClientConfigJobsItem(ClientConfigJobs clientConfigJobsItem) {
    this.clientConfigJobs.add(clientConfigJobsItem);
    return this;
  }

   /**
   * Get clientConfigJobs
   * @return clientConfigJobs
  **/
  @JsonProperty("clientConfigJobs")
  @ApiModelProperty(value = "")
  public List<ClientConfigJobs> getClientConfigJobs() {
    return clientConfigJobs;
  }

  public void setClientConfigJobs(List<ClientConfigJobs> clientConfigJobs) {
    this.clientConfigJobs = clientConfigJobs;
  }

  public KsdConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public KsdConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public KsdConfig dailyTaskReportSubjectNameOutLook(String dailyTaskReportSubjectNameOutLook) {
    this.dailyTaskReportSubjectNameOutLook = dailyTaskReportSubjectNameOutLook;
    return this;
  }

   /**
   * Get dailyTaskReportSubjectNameOutLook
   * @return dailyTaskReportSubjectNameOutLook
  **/
  @JsonProperty("dailyTaskReportSubjectNameOutLook")
  @ApiModelProperty(value = "")
  public String getDailyTaskReportSubjectNameOutLook() {
    return dailyTaskReportSubjectNameOutLook;
  }

  public void setDailyTaskReportSubjectNameOutLook(String dailyTaskReportSubjectNameOutLook) {
    this.dailyTaskReportSubjectNameOutLook = dailyTaskReportSubjectNameOutLook;
  }

  public KsdConfig eftName(String eftName) {
    this.eftName = eftName;
    return this;
  }

   /**
   * Get eftName
   * @return eftName
  **/
  @JsonProperty("eftName")
  @ApiModelProperty(value = "")
  public String getEftName() {
    return eftName;
  }

  public void setEftName(String eftName) {
    this.eftName = eftName;
  }

  public KsdConfig eftTriggerTime(String eftTriggerTime) {
    this.eftTriggerTime = eftTriggerTime;
    return this;
  }

   /**
   * Get eftTriggerTime
   * @return eftTriggerTime
  **/
  @JsonProperty("eftTriggerTime")
  @ApiModelProperty(value = "")
  public String getEftTriggerTime() {
    return eftTriggerTime;
  }

  public void setEftTriggerTime(String eftTriggerTime) {
    this.eftTriggerTime = eftTriggerTime;
  }

  public KsdConfig fileType(String fileType) {
    this.fileType = fileType;
    return this;
  }

   /**
   * Get fileType
   * @return fileType
  **/
  @JsonProperty("fileType")
  @ApiModelProperty(value = "")
  public String getFileType() {
    return fileType;
  }

  public void setFileType(String fileType) {
    this.fileType = fileType;
  }

  public KsdConfig frequency(String frequency) {
    this.frequency = frequency;
    return this;
  }

   /**
   * Get frequency
   * @return frequency
  **/
  @JsonProperty("frequency")
  @ApiModelProperty(value = "")
  public String getFrequency() {
    return frequency;
  }

  public void setFrequency(String frequency) {
    this.frequency = frequency;
  }

  public KsdConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public KsdConfig jobCutOffTime(String jobCutOffTime) {
    this.jobCutOffTime = jobCutOffTime;
    return this;
  }

   /**
   * Get jobCutOffTime
   * @return jobCutOffTime
  **/
  @JsonProperty("jobCutOffTime")
  @ApiModelProperty(value = "")
  public String getJobCutOffTime() {
    return jobCutOffTime;
  }

  public void setJobCutOffTime(String jobCutOffTime) {
    this.jobCutOffTime = jobCutOffTime;
  }

  public KsdConfig jobScheduleTime(String jobScheduleTime) {
    this.jobScheduleTime = jobScheduleTime;
    return this;
  }

   /**
   * Get jobScheduleTime
   * @return jobScheduleTime
  **/
  @JsonProperty("jobScheduleTime")
  @ApiModelProperty(value = "")
  public String getJobScheduleTime() {
    return jobScheduleTime;
  }

  public void setJobScheduleTime(String jobScheduleTime) {
    this.jobScheduleTime = jobScheduleTime;
  }

  public KsdConfig jobSchedules(List<JobSchedule> jobSchedules) {
    this.jobSchedules = jobSchedules;
    return this;
  }

  public KsdConfig addJobSchedulesItem(JobSchedule jobSchedulesItem) {
    this.jobSchedules.add(jobSchedulesItem);
    return this;
  }

   /**
   * Get jobSchedules
   * @return jobSchedules
  **/
  @JsonProperty("jobSchedules")
  @ApiModelProperty(value = "")
  public List<JobSchedule> getJobSchedules() {
    return jobSchedules;
  }

  public void setJobSchedules(List<JobSchedule> jobSchedules) {
    this.jobSchedules = jobSchedules;
  }

  public KsdConfig jobStream(String jobStream) {
    this.jobStream = jobStream;
    return this;
  }

   /**
   * Get jobStream
   * @return jobStream
  **/
  @JsonProperty("jobStream")
  @ApiModelProperty(value = "")
  public String getJobStream() {
    return jobStream;
  }

  public void setJobStream(String jobStream) {
    this.jobStream = jobStream;
  }

  public KsdConfig maestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
    return this;
  }

   /**
   * Get maestroTaskName
   * @return maestroTaskName
  **/
  @JsonProperty("maestroTaskName")
  @ApiModelProperty(value = "")
  public String getMaestroTaskName() {
    return maestroTaskName;
  }

  public void setMaestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
  }

  public KsdConfig primaryJobName(String primaryJobName) {
    this.primaryJobName = primaryJobName;
    return this;
  }

   /**
   * Get primaryJobName
   * @return primaryJobName
  **/
  @JsonProperty("primaryJobName")
  @ApiModelProperty(value = "")
  public String getPrimaryJobName() {
    return primaryJobName;
  }

  public void setPrimaryJobName(String primaryJobName) {
    this.primaryJobName = primaryJobName;
  }

  public KsdConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public KsdConfig processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public KsdConfig turnaroundTime(String turnaroundTime) {
    this.turnaroundTime = turnaroundTime;
    return this;
  }

   /**
   * Get turnaroundTime
   * @return turnaroundTime
  **/
  @JsonProperty("turnaroundTime")
  @ApiModelProperty(value = "")
  public String getTurnaroundTime() {
    return turnaroundTime;
  }

  public void setTurnaroundTime(String turnaroundTime) {
    this.turnaroundTime = turnaroundTime;
  }

  public KsdConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public KsdConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public KsdConfig directTBAUpdate(Boolean directTBAUpdate) {
    this.directTBAUpdate = directTBAUpdate;
    return this;
  }

   /**
   * Get directTBAUpdate
   * @return directTBAUpdate
  **/
  @JsonProperty("directTBAUpdate")
  @ApiModelProperty(value = "")
  public Boolean getDirectTBAUpdate() {
    return directTBAUpdate;
  }

  public void setDirectTBAUpdate(Boolean directTBAUpdate) {
    this.directTBAUpdate = directTBAUpdate;
  }

  public KsdConfig isJiraUsed(Boolean isJiraUsed) {
    this.isJiraUsed = isJiraUsed;
    return this;
  }

   /**
   * Get isJiraUsed
   * @return isJiraUsed
  **/
  @JsonProperty("isJiraUsed")
  @ApiModelProperty(value = "")
  public Boolean getIsJiraUsed() {
    return isJiraUsed;
  }

  public void setIsJiraUsed(Boolean isJiraUsed) {
    this.isJiraUsed = isJiraUsed;
  }

  public KsdConfig namedType(Boolean namedType) {
    this.namedType = namedType;
    return this;
  }

   /**
   * Get namedType
   * @return namedType
  **/
  @JsonProperty("namedType")
  @ApiModelProperty(value = "")
  public Boolean getNamedType() {
    return namedType;
  }

  public void setNamedType(Boolean namedType) {
    this.namedType = namedType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KsdConfig ksdConfig = (KsdConfig) o;
    return Objects.equals(this.activeFlag, ksdConfig.activeFlag) &&
        Objects.equals(this.adxScriptId, ksdConfig.adxScriptId) &&
        Objects.equals(this.skipControlMReport, ksdConfig.skipControlMReport) &&
        Objects.equals(this.taskCreationOnly, ksdConfig.taskCreationOnly) &&
        Objects.equals(this.clientConfigJobs, ksdConfig.clientConfigJobs) &&
        Objects.equals(this.createdBy, ksdConfig.createdBy) &&
        Objects.equals(this.createdDate, ksdConfig.createdDate) &&
        Objects.equals(this.dailyTaskReportSubjectNameOutLook, ksdConfig.dailyTaskReportSubjectNameOutLook) &&
        Objects.equals(this.eftName, ksdConfig.eftName) &&
        Objects.equals(this.eftTriggerTime, ksdConfig.eftTriggerTime) &&
        Objects.equals(this.fileType, ksdConfig.fileType) &&
        Objects.equals(this.frequency, ksdConfig.frequency) &&
        Objects.equals(this.id, ksdConfig.id) &&
        Objects.equals(this.jobCutOffTime, ksdConfig.jobCutOffTime) &&
        Objects.equals(this.jobScheduleTime, ksdConfig.jobScheduleTime) &&
        Objects.equals(this.jobSchedules, ksdConfig.jobSchedules) &&
        Objects.equals(this.jobStream, ksdConfig.jobStream) &&
        Objects.equals(this.maestroTaskName, ksdConfig.maestroTaskName) &&
        Objects.equals(this.primaryJobName, ksdConfig.primaryJobName) &&
        Objects.equals(this.processJobMapping, ksdConfig.processJobMapping) &&
        Objects.equals(this.processJobMappingId, ksdConfig.processJobMappingId) &&
        Objects.equals(this.turnaroundTime, ksdConfig.turnaroundTime) &&
        Objects.equals(this.updatedBy, ksdConfig.updatedBy) &&
        Objects.equals(this.updatedDate, ksdConfig.updatedDate) &&
        Objects.equals(this.directTBAUpdate, ksdConfig.directTBAUpdate) &&
        Objects.equals(this.isJiraUsed, ksdConfig.isJiraUsed) &&
        Objects.equals(this.namedType, ksdConfig.namedType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, adxScriptId, skipControlMReport, taskCreationOnly, clientConfigJobs, createdBy, createdDate, dailyTaskReportSubjectNameOutLook, eftName, eftTriggerTime, fileType, frequency, id, jobCutOffTime, jobScheduleTime, jobSchedules, jobStream, maestroTaskName, primaryJobName, processJobMapping, processJobMappingId, turnaroundTime, updatedBy, updatedDate, directTBAUpdate, isJiraUsed, namedType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KsdConfig {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    adxScriptId: ").append(toIndentedString(adxScriptId)).append("\n");
    sb.append("    skipControlMReport: ").append(toIndentedString(skipControlMReport)).append("\n");
    sb.append("    taskCreationOnly: ").append(toIndentedString(taskCreationOnly)).append("\n");
    sb.append("    clientConfigJobs: ").append(toIndentedString(clientConfigJobs)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    dailyTaskReportSubjectNameOutLook: ").append(toIndentedString(dailyTaskReportSubjectNameOutLook)).append("\n");
    sb.append("    eftName: ").append(toIndentedString(eftName)).append("\n");
    sb.append("    eftTriggerTime: ").append(toIndentedString(eftTriggerTime)).append("\n");
    sb.append("    fileType: ").append(toIndentedString(fileType)).append("\n");
    sb.append("    frequency: ").append(toIndentedString(frequency)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobCutOffTime: ").append(toIndentedString(jobCutOffTime)).append("\n");
    sb.append("    jobScheduleTime: ").append(toIndentedString(jobScheduleTime)).append("\n");
    sb.append("    jobSchedules: ").append(toIndentedString(jobSchedules)).append("\n");
    sb.append("    jobStream: ").append(toIndentedString(jobStream)).append("\n");
    sb.append("    maestroTaskName: ").append(toIndentedString(maestroTaskName)).append("\n");
    sb.append("    primaryJobName: ").append(toIndentedString(primaryJobName)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    turnaroundTime: ").append(toIndentedString(turnaroundTime)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    directTBAUpdate: ").append(toIndentedString(directTBAUpdate)).append("\n");
    sb.append("    isJiraUsed: ").append(toIndentedString(isJiraUsed)).append("\n");
    sb.append("    namedType: ").append(toIndentedString(namedType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

