/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ClientDetailsBo;
import com.wipro.fipc.model.generated.CustomBusinessUnitBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * UserDetailsResponse
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class UserDetailsResponse   {
  @JsonProperty("businessUnit")
  private List<CustomBusinessUnitBO> businessUnit = new ArrayList<CustomBusinessUnitBO>();

  @JsonProperty("clientDetails")
  private List<ClientDetailsBo> clientDetails = new ArrayList<ClientDetailsBo>();

  @JsonProperty("roles")
  private List<String> roles = new ArrayList<String>();

  public UserDetailsResponse businessUnit(List<CustomBusinessUnitBO> businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

  public UserDetailsResponse addBusinessUnitItem(CustomBusinessUnitBO businessUnitItem) {
    this.businessUnit.add(businessUnitItem);
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public List<CustomBusinessUnitBO> getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(List<CustomBusinessUnitBO> businessUnit) {
    this.businessUnit = businessUnit;
  }

  public UserDetailsResponse clientDetails(List<ClientDetailsBo> clientDetails) {
    this.clientDetails = clientDetails;
    return this;
  }

  public UserDetailsResponse addClientDetailsItem(ClientDetailsBo clientDetailsItem) {
    this.clientDetails.add(clientDetailsItem);
    return this;
  }

   /**
   * Get clientDetails
   * @return clientDetails
  **/
  @JsonProperty("clientDetails")
  @ApiModelProperty(value = "")
  public List<ClientDetailsBo> getClientDetails() {
    return clientDetails;
  }

  public void setClientDetails(List<ClientDetailsBo> clientDetails) {
    this.clientDetails = clientDetails;
  }

  public UserDetailsResponse roles(List<String> roles) {
    this.roles = roles;
    return this;
  }

  public UserDetailsResponse addRolesItem(String rolesItem) {
    this.roles.add(rolesItem);
    return this;
  }

   /**
   * Get roles
   * @return roles
  **/
  @JsonProperty("roles")
  @ApiModelProperty(value = "")
  public List<String> getRoles() {
    return roles;
  }

  public void setRoles(List<String> roles) {
    this.roles = roles;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UserDetailsResponse userDetailsResponse = (UserDetailsResponse) o;
    return Objects.equals(this.businessUnit, userDetailsResponse.businessUnit) &&
        Objects.equals(this.clientDetails, userDetailsResponse.clientDetails) &&
        Objects.equals(this.roles, userDetailsResponse.roles);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessUnit, clientDetails, roles);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UserDetailsResponse {\n");
    
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientDetails: ").append(toIndentedString(clientDetails)).append("\n");
    sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

