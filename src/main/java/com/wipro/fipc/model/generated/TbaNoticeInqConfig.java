/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wipro.fipc.utils.CustomClientIdDeserializer;

import io.swagger.annotations.ApiModelProperty;

/**
 * TbaNoticeInqConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2024-04-25T15:47:05.008+05:30")
public class TbaNoticeInqConfig   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("addManualFlag")
  private String addManualFlag = null;

  @JsonProperty("clientId")
  @JsonDeserialize(using = CustomClientIdDeserializer.class)
  private Integer clientId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("fieldType")
  private String fieldType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("inquiryDefName")
  private String inquiryDefName = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("metadata")
  private String metadata = null;

  @JsonProperty("noticeId")
  private Integer noticeId = null;

  @JsonProperty("noticeName")
  private String noticeName = null;

  @JsonProperty("parNm")
  private String parNm = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("subJsonKey")
  private String subJsonKey = null;

  @JsonProperty("tba_field_name")
  private String tbaFieldName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaNoticeInqConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TbaNoticeInqConfig addManualFlag(String addManualFlag) {
    this.addManualFlag = addManualFlag;
    return this;
  }

   /**
   * Get addManualFlag
   * @return addManualFlag
  **/
  @JsonProperty("addManualFlag")
  @ApiModelProperty(value = "")
  public String getAddManualFlag() {
    return addManualFlag;
  }

  public void setAddManualFlag(String addManualFlag) {
    this.addManualFlag = addManualFlag;
  }

  public TbaNoticeInqConfig clientId(Integer clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Integer getClientId() {
    return clientId;
  }

  public void setClientId(Integer clientId) {
    this.clientId = clientId;
  }

  public TbaNoticeInqConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaNoticeInqConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaNoticeInqConfig fieldType(String fieldType) {
    this.fieldType = fieldType;
    return this;
  }

   /**
   * Get fieldType
   * @return fieldType
  **/
  @JsonProperty("fieldType")
  @ApiModelProperty(value = "")
  public String getFieldType() {
    return fieldType;
  }

  public void setFieldType(String fieldType) {
    this.fieldType = fieldType;
  }

  public TbaNoticeInqConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaNoticeInqConfig identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public TbaNoticeInqConfig inquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
    return this;
  }

   /**
   * Get inquiryDefName
   * @return inquiryDefName
  **/
  @JsonProperty("inquiryDefName")
  @ApiModelProperty(value = "")
  public String getInquiryDefName() {
    return inquiryDefName;
  }

  public void setInquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
  }

  public TbaNoticeInqConfig jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public TbaNoticeInqConfig metadata(String metadata) {
    this.metadata = metadata;
    return this;
  }

   /**
   * Get metadata
   * @return metadata
  **/
  @JsonProperty("metadata")
  @ApiModelProperty(value = "")
  public String getMetadata() {
    return metadata;
  }

  public void setMetadata(String metadata) {
    this.metadata = metadata;
  }

  public TbaNoticeInqConfig noticeId(Integer noticeId) {
    this.noticeId = noticeId;
    return this;
  }

   /**
   * Get noticeId
   * @return noticeId
  **/
  @JsonProperty("noticeId")
  @ApiModelProperty(value = "")
  public Integer getNoticeId() {
    return noticeId;
  }

  public void setNoticeId(Integer noticeId) {
    this.noticeId = noticeId;
  }

  public TbaNoticeInqConfig noticeName(String noticeName) {
    this.noticeName = noticeName;
    return this;
  }

   /**
   * Get noticeName
   * @return noticeName
  **/
  @JsonProperty("noticeName")
  @ApiModelProperty(value = "")
  public String getNoticeName() {
    return noticeName;
  }

  public void setNoticeName(String noticeName) {
    this.noticeName = noticeName;
  }

  public TbaNoticeInqConfig parNm(String parNm) {
    this.parNm = parNm;
    return this;
  }

   /**
   * Get parNm
   * @return parNm
  **/
  @JsonProperty("parNm")
  @ApiModelProperty(value = "")
  public String getParNm() {
    return parNm;
  }

  public void setParNm(String parNm) {
    this.parNm = parNm;
  }

  public TbaNoticeInqConfig processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public TbaNoticeInqConfig recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public TbaNoticeInqConfig subJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
    return this;
  }

   /**
   * Get subJsonKey
   * @return subJsonKey
  **/
  @JsonProperty("subJsonKey")
  @ApiModelProperty(value = "")
  public String getSubJsonKey() {
    return subJsonKey;
  }

  public void setSubJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
  }

  public TbaNoticeInqConfig tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tba_field_name")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaNoticeInqConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaNoticeInqConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaNoticeInqConfig tbaNoticeInqConfig = (TbaNoticeInqConfig) o;
    return Objects.equals(this.activeFlag, tbaNoticeInqConfig.activeFlag) &&
        Objects.equals(this.addManualFlag, tbaNoticeInqConfig.addManualFlag) &&
        Objects.equals(this.clientId, tbaNoticeInqConfig.clientId) &&
        Objects.equals(this.createdBy, tbaNoticeInqConfig.createdBy) &&
        Objects.equals(this.createdDate, tbaNoticeInqConfig.createdDate) &&
        Objects.equals(this.fieldType, tbaNoticeInqConfig.fieldType) &&
        Objects.equals(this.id, tbaNoticeInqConfig.id) &&
        Objects.equals(this.identifier, tbaNoticeInqConfig.identifier) &&
        Objects.equals(this.inquiryDefName, tbaNoticeInqConfig.inquiryDefName) &&
        Objects.equals(this.jsonKey, tbaNoticeInqConfig.jsonKey) &&
        Objects.equals(this.metadata, tbaNoticeInqConfig.metadata) &&
        Objects.equals(this.noticeId, tbaNoticeInqConfig.noticeId) &&
        Objects.equals(this.noticeName, tbaNoticeInqConfig.noticeName) &&
        Objects.equals(this.parNm, tbaNoticeInqConfig.parNm) &&
        Objects.equals(this.processJobMappingId, tbaNoticeInqConfig.processJobMappingId) &&
        Objects.equals(this.recordIdentifier, tbaNoticeInqConfig.recordIdentifier) &&
        Objects.equals(this.subJsonKey, tbaNoticeInqConfig.subJsonKey) &&
        Objects.equals(this.tbaFieldName, tbaNoticeInqConfig.tbaFieldName) &&
        Objects.equals(this.updatedBy, tbaNoticeInqConfig.updatedBy) &&
        Objects.equals(this.updatedDate, tbaNoticeInqConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, addManualFlag, clientId, createdBy, createdDate, fieldType, id, identifier, inquiryDefName, jsonKey, metadata, noticeId, noticeName, parNm, processJobMappingId, recordIdentifier, subJsonKey, tbaFieldName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaNoticeInqConfig {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    addManualFlag: ").append(toIndentedString(addManualFlag)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    fieldType: ").append(toIndentedString(fieldType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    inquiryDefName: ").append(toIndentedString(inquiryDefName)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    noticeId: ").append(toIndentedString(noticeId)).append("\n");
    sb.append("    noticeName: ").append(toIndentedString(noticeName)).append("\n");
    sb.append("    parNm: ").append(toIndentedString(parNm)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    subJsonKey: ").append(toIndentedString(subJsonKey)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

