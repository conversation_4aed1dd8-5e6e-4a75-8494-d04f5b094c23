/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * MaskingDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class MaskingDetails   {
  @JsonProperty("fieldMapping")
  private String fieldMapping = null;

  @JsonProperty("flag")
  private String flag = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("maskingValue")
  private String maskingValue = null;

  @JsonProperty("name")
  private String name = null;

  public MaskingDetails fieldMapping(String fieldMapping) {
    this.fieldMapping = fieldMapping;
    return this;
  }

   /**
   * Get fieldMapping
   * @return fieldMapping
  **/
  @JsonProperty("fieldMapping")
  @ApiModelProperty(value = "")
  public String getFieldMapping() {
    return fieldMapping;
  }

  public void setFieldMapping(String fieldMapping) {
    this.fieldMapping = fieldMapping;
  }

  public MaskingDetails flag(String flag) {
    this.flag = flag;
    return this;
  }

   /**
   * Get flag
   * @return flag
  **/
  @JsonProperty("flag")
  @ApiModelProperty(value = "")
  public String getFlag() {
    return flag;
  }

  public void setFlag(String flag) {
    this.flag = flag;
  }

  public MaskingDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public MaskingDetails maskingValue(String maskingValue) {
    this.maskingValue = maskingValue;
    return this;
  }

   /**
   * Get maskingValue
   * @return maskingValue
  **/
  @JsonProperty("maskingValue")
  @ApiModelProperty(value = "")
  public String getMaskingValue() {
    return maskingValue;
  }

  public void setMaskingValue(String maskingValue) {
    this.maskingValue = maskingValue;
  }

  public MaskingDetails name(String name) {
    this.name = name;
    return this;
  }

   /**
   * Get name
   * @return name
  **/
  @JsonProperty("name")
  @ApiModelProperty(value = "")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MaskingDetails maskingDetails = (MaskingDetails) o;
    return Objects.equals(this.fieldMapping, maskingDetails.fieldMapping) &&
        Objects.equals(this.flag, maskingDetails.flag) &&
        Objects.equals(this.id, maskingDetails.id) &&
        Objects.equals(this.maskingValue, maskingDetails.maskingValue) &&
        Objects.equals(this.name, maskingDetails.name);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fieldMapping, flag, id, maskingValue, name);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MaskingDetails {\n");
    
    sb.append("    fieldMapping: ").append(toIndentedString(fieldMapping)).append("\n");
    sb.append("    flag: ").append(toIndentedString(flag)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    maskingValue: ").append(toIndentedString(maskingValue)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

