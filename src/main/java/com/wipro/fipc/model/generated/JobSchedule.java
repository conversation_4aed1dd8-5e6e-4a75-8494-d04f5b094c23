/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * JobSchedule
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class JobSchedule   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("bimonthlyDays")
  private String bimonthlyDays = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("customDates")
  private String customDates = null;

  @JsonProperty("dayOfMonth")
  private Integer dayOfMonth = null;

  @JsonProperty("daysOfWeek")
  private String daysOfWeek = null;

  @JsonProperty("frequency")
  private String frequency = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("index")
  private String index = null;

  @JsonProperty("interval")
  private Integer interval = null;

  @JsonProperty("month")
  private Integer month = null;

  @JsonProperty("skipHolidays")
  private String skipHolidays = null;

  @JsonProperty("skipWeekends")
  private String skipWeekends = null;

  @JsonProperty("timeZone")
  private String timeZone = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public JobSchedule activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public JobSchedule bimonthlyDays(String bimonthlyDays) {
    this.bimonthlyDays = bimonthlyDays;
    return this;
  }

   /**
   * Get bimonthlyDays
   * @return bimonthlyDays
  **/
  @JsonProperty("bimonthlyDays")
  @ApiModelProperty(value = "")
  public String getBimonthlyDays() {
    return bimonthlyDays;
  }

  public void setBimonthlyDays(String bimonthlyDays) {
    this.bimonthlyDays = bimonthlyDays;
  }

  public JobSchedule createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public JobSchedule createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public JobSchedule customDates(String customDates) {
    this.customDates = customDates;
    return this;
  }

   /**
   * Get customDates
   * @return customDates
  **/
  @JsonProperty("customDates")
  @ApiModelProperty(value = "")
  public String getCustomDates() {
    return customDates;
  }

  public void setCustomDates(String customDates) {
    this.customDates = customDates;
  }

  public JobSchedule dayOfMonth(Integer dayOfMonth) {
    this.dayOfMonth = dayOfMonth;
    return this;
  }

   /**
   * Get dayOfMonth
   * @return dayOfMonth
  **/
  @JsonProperty("dayOfMonth")
  @ApiModelProperty(value = "")
  public Integer getDayOfMonth() {
    return dayOfMonth;
  }

  public void setDayOfMonth(Integer dayOfMonth) {
    this.dayOfMonth = dayOfMonth;
  }

  public JobSchedule daysOfWeek(String daysOfWeek) {
    this.daysOfWeek = daysOfWeek;
    return this;
  }

   /**
   * Get daysOfWeek
   * @return daysOfWeek
  **/
  @JsonProperty("daysOfWeek")
  @ApiModelProperty(value = "")
  public String getDaysOfWeek() {
    return daysOfWeek;
  }

  public void setDaysOfWeek(String daysOfWeek) {
    this.daysOfWeek = daysOfWeek;
  }

  public JobSchedule frequency(String frequency) {
    this.frequency = frequency;
    return this;
  }

   /**
   * Get frequency
   * @return frequency
  **/
  @JsonProperty("frequency")
  @ApiModelProperty(value = "")
  public String getFrequency() {
    return frequency;
  }

  public void setFrequency(String frequency) {
    this.frequency = frequency;
  }

  public JobSchedule id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public JobSchedule index(String index) {
    this.index = index;
    return this;
  }

   /**
   * Get index
   * @return index
  **/
  @JsonProperty("index")
  @ApiModelProperty(value = "")
  public String getIndex() {
    return index;
  }

  public void setIndex(String index) {
    this.index = index;
  }

  public JobSchedule interval(Integer interval) {
    this.interval = interval;
    return this;
  }

   /**
   * Get interval
   * @return interval
  **/
  @JsonProperty("interval")
  @ApiModelProperty(value = "")
  public Integer getInterval() {
    return interval;
  }

  public void setInterval(Integer interval) {
    this.interval = interval;
  }

  public JobSchedule month(Integer month) {
    this.month = month;
    return this;
  }

   /**
   * Get month
   * @return month
  **/
  @JsonProperty("month")
  @ApiModelProperty(value = "")
  public Integer getMonth() {
    return month;
  }

  public void setMonth(Integer month) {
    this.month = month;
  }

  public JobSchedule skipHolidays(String skipHolidays) {
    this.skipHolidays = skipHolidays;
    return this;
  }

   /**
   * Get skipHolidays
   * @return skipHolidays
  **/
  @JsonProperty("skipHolidays")
  @ApiModelProperty(value = "")
  public String getSkipHolidays() {
    return skipHolidays;
  }

  public void setSkipHolidays(String skipHolidays) {
    this.skipHolidays = skipHolidays;
  }

  public JobSchedule skipWeekends(String skipWeekends) {
    this.skipWeekends = skipWeekends;
    return this;
  }

   /**
   * Get skipWeekends
   * @return skipWeekends
  **/
  @JsonProperty("skipWeekends")
  @ApiModelProperty(value = "")
  public String getSkipWeekends() {
    return skipWeekends;
  }

  public void setSkipWeekends(String skipWeekends) {
    this.skipWeekends = skipWeekends;
  }

  public JobSchedule timeZone(String timeZone) {
    this.timeZone = timeZone;
    return this;
  }

   /**
   * Get timeZone
   * @return timeZone
  **/
  @JsonProperty("timeZone")
  @ApiModelProperty(value = "")
  public String getTimeZone() {
    return timeZone;
  }

  public void setTimeZone(String timeZone) {
    this.timeZone = timeZone;
  }

  public JobSchedule updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public JobSchedule updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    JobSchedule jobSchedule = (JobSchedule) o;
    return Objects.equals(this.activeFlag, jobSchedule.activeFlag) &&
        Objects.equals(this.bimonthlyDays, jobSchedule.bimonthlyDays) &&
        Objects.equals(this.createdBy, jobSchedule.createdBy) &&
        Objects.equals(this.createdDate, jobSchedule.createdDate) &&
        Objects.equals(this.customDates, jobSchedule.customDates) &&
        Objects.equals(this.dayOfMonth, jobSchedule.dayOfMonth) &&
        Objects.equals(this.daysOfWeek, jobSchedule.daysOfWeek) &&
        Objects.equals(this.frequency, jobSchedule.frequency) &&
        Objects.equals(this.id, jobSchedule.id) &&
        Objects.equals(this.index, jobSchedule.index) &&
        Objects.equals(this.interval, jobSchedule.interval) &&
        Objects.equals(this.month, jobSchedule.month) &&
        Objects.equals(this.skipHolidays, jobSchedule.skipHolidays) &&
        Objects.equals(this.skipWeekends, jobSchedule.skipWeekends) &&
        Objects.equals(this.timeZone, jobSchedule.timeZone) &&
        Objects.equals(this.updatedBy, jobSchedule.updatedBy) &&
        Objects.equals(this.updatedDate, jobSchedule.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, bimonthlyDays, createdBy, createdDate, customDates, dayOfMonth, daysOfWeek, frequency, id, index, interval, month, skipHolidays, skipWeekends, timeZone, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class JobSchedule {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    bimonthlyDays: ").append(toIndentedString(bimonthlyDays)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    customDates: ").append(toIndentedString(customDates)).append("\n");
    sb.append("    dayOfMonth: ").append(toIndentedString(dayOfMonth)).append("\n");
    sb.append("    daysOfWeek: ").append(toIndentedString(daysOfWeek)).append("\n");
    sb.append("    frequency: ").append(toIndentedString(frequency)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    index: ").append(toIndentedString(index)).append("\n");
    sb.append("    interval: ").append(toIndentedString(interval)).append("\n");
    sb.append("    month: ").append(toIndentedString(month)).append("\n");
    sb.append("    skipHolidays: ").append(toIndentedString(skipHolidays)).append("\n");
    sb.append("    skipWeekends: ").append(toIndentedString(skipWeekends)).append("\n");
    sb.append("    timeZone: ").append(toIndentedString(timeZone)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

