/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * Phase
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class Phase   {
  @JsonProperty("exclusive")
  private Integer exclusive = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("phase")
  private Integer phase = null;

  @JsonProperty("phaseName")
  private String phaseName = null;

  @JsonProperty("pluginsInParallel")
  private Integer pluginsInParallel = null;

  @JsonProperty("subPhaseInParallel")
  private Integer subPhaseInParallel = null;

  @JsonProperty("timeout")
  private Long timeout = null;

  public Phase exclusive(Integer exclusive) {
    this.exclusive = exclusive;
    return this;
  }

   /**
   * Get exclusive
   * @return exclusive
  **/
  @JsonProperty("exclusive")
  @ApiModelProperty(value = "")
  public Integer getExclusive() {
    return exclusive;
  }

  public void setExclusive(Integer exclusive) {
    this.exclusive = exclusive;
  }

  public Phase id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Phase phase(Integer phase) {
    this.phase = phase;
    return this;
  }

   /**
   * Get phase
   * @return phase
  **/
  @JsonProperty("phase")
  @ApiModelProperty(value = "")
  public Integer getPhase() {
    return phase;
  }

  public void setPhase(Integer phase) {
    this.phase = phase;
  }

  public Phase phaseName(String phaseName) {
    this.phaseName = phaseName;
    return this;
  }

   /**
   * Get phaseName
   * @return phaseName
  **/
  @JsonProperty("phaseName")
  @ApiModelProperty(value = "")
  public String getPhaseName() {
    return phaseName;
  }

  public void setPhaseName(String phaseName) {
    this.phaseName = phaseName;
  }

  public Phase pluginsInParallel(Integer pluginsInParallel) {
    this.pluginsInParallel = pluginsInParallel;
    return this;
  }

   /**
   * Get pluginsInParallel
   * @return pluginsInParallel
  **/
  @JsonProperty("pluginsInParallel")
  @ApiModelProperty(value = "")
  public Integer getPluginsInParallel() {
    return pluginsInParallel;
  }

  public void setPluginsInParallel(Integer pluginsInParallel) {
    this.pluginsInParallel = pluginsInParallel;
  }

  public Phase subPhaseInParallel(Integer subPhaseInParallel) {
    this.subPhaseInParallel = subPhaseInParallel;
    return this;
  }

   /**
   * Get subPhaseInParallel
   * @return subPhaseInParallel
  **/
  @JsonProperty("subPhaseInParallel")
  @ApiModelProperty(value = "")
  public Integer getSubPhaseInParallel() {
    return subPhaseInParallel;
  }

  public void setSubPhaseInParallel(Integer subPhaseInParallel) {
    this.subPhaseInParallel = subPhaseInParallel;
  }

  public Phase timeout(Long timeout) {
    this.timeout = timeout;
    return this;
  }

   /**
   * Get timeout
   * @return timeout
  **/
  @JsonProperty("timeout")
  @ApiModelProperty(value = "")
  public Long getTimeout() {
    return timeout;
  }

  public void setTimeout(Long timeout) {
    this.timeout = timeout;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Phase phase = (Phase) o;
    return Objects.equals(this.exclusive, phase.exclusive) &&
        Objects.equals(this.id, phase.id) &&
        Objects.equals(this.phase, phase.phase) &&
        Objects.equals(this.phaseName, phase.phaseName) &&
        Objects.equals(this.pluginsInParallel, phase.pluginsInParallel) &&
        Objects.equals(this.subPhaseInParallel, phase.subPhaseInParallel) &&
        Objects.equals(this.timeout, phase.timeout);
  }

  @Override
  public int hashCode() {
    return Objects.hash(exclusive, id, phase, phaseName, pluginsInParallel, subPhaseInParallel, timeout);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Phase {\n");
    
    sb.append("    exclusive: ").append(toIndentedString(exclusive)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    phase: ").append(toIndentedString(phase)).append("\n");
    sb.append("    phaseName: ").append(toIndentedString(phaseName)).append("\n");
    sb.append("    pluginsInParallel: ").append(toIndentedString(pluginsInParallel)).append("\n");
    sb.append("    subPhaseInParallel: ").append(toIndentedString(subPhaseInParallel)).append("\n");
    sb.append("    timeout: ").append(toIndentedString(timeout)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

