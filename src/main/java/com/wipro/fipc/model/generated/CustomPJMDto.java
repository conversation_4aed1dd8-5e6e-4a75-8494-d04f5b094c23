/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * CustomPJMDto
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class CustomPJMDto   {
  @JsonProperty("businessOpsId")
  private Long businessOpsId = null;

  @JsonProperty("businessOpsName")
  private String businessOpsName = null;

  @JsonProperty("businessUnitName")
  private String businessUnitName = null;

  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientId")
  private Long clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("eftSubject")
  private String eftSubject = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("processId")
  private Long processId = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  public CustomPJMDto businessOpsId(Long businessOpsId) {
    this.businessOpsId = businessOpsId;
    return this;
  }

   /**
   * Get businessOpsId
   * @return businessOpsId
  **/
  @JsonProperty("businessOpsId")
  @ApiModelProperty(value = "")
  public Long getBusinessOpsId() {
    return businessOpsId;
  }

  public void setBusinessOpsId(Long businessOpsId) {
    this.businessOpsId = businessOpsId;
  }

  public CustomPJMDto businessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
    return this;
  }

   /**
   * Get businessOpsName
   * @return businessOpsName
  **/
  @JsonProperty("businessOpsName")
  @ApiModelProperty(value = "")
  public String getBusinessOpsName() {
    return businessOpsName;
  }

  public void setBusinessOpsName(String businessOpsName) {
    this.businessOpsName = businessOpsName;
  }

  public CustomPJMDto businessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
    return this;
  }

   /**
   * Get businessUnitName
   * @return businessUnitName
  **/
  @JsonProperty("businessUnitName")
  @ApiModelProperty(value = "")
  public String getBusinessUnitName() {
    return businessUnitName;
  }

  public void setBusinessUnitName(String businessUnitName) {
    this.businessUnitName = businessUnitName;
  }

  public CustomPJMDto clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public CustomPJMDto clientId(Long clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Long getClientId() {
    return clientId;
  }

  public void setClientId(Long clientId) {
    this.clientId = clientId;
  }

  public CustomPJMDto clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public CustomPJMDto eftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
    return this;
  }

   /**
   * Get eftSubject
   * @return eftSubject
  **/
  @JsonProperty("eftSubject")
  @ApiModelProperty(value = "")
  public String getEftSubject() {
    return eftSubject;
  }

  public void setEftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
  }

  public CustomPJMDto jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public CustomPJMDto ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public CustomPJMDto processId(Long processId) {
    this.processId = processId;
    return this;
  }

   /**
   * Get processId
   * @return processId
  **/
  @JsonProperty("processId")
  @ApiModelProperty(value = "")
  public Long getProcessId() {
    return processId;
  }

  public void setProcessId(Long processId) {
    this.processId = processId;
  }

  public CustomPJMDto processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public CustomPJMDto processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public CustomPJMDto processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomPJMDto customPJMDto = (CustomPJMDto) o;
    return Objects.equals(this.businessOpsId, customPJMDto.businessOpsId) &&
        Objects.equals(this.businessOpsName, customPJMDto.businessOpsName) &&
        Objects.equals(this.businessUnitName, customPJMDto.businessUnitName) &&
        Objects.equals(this.clientCode, customPJMDto.clientCode) &&
        Objects.equals(this.clientId, customPJMDto.clientId) &&
        Objects.equals(this.clientName, customPJMDto.clientName) &&
        Objects.equals(this.eftSubject, customPJMDto.eftSubject) &&
        Objects.equals(this.jobName, customPJMDto.jobName) &&
        Objects.equals(this.ksdName, customPJMDto.ksdName) &&
        Objects.equals(this.processId, customPJMDto.processId) &&
        Objects.equals(this.processJobMappingId, customPJMDto.processJobMappingId) &&
        Objects.equals(this.processName, customPJMDto.processName) &&
        Objects.equals(this.processType, customPJMDto.processType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessOpsId, businessOpsName, businessUnitName, clientCode, clientId, clientName, eftSubject, jobName, ksdName, processId, processJobMappingId, processName, processType);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomPJMDto {\n");
    
    sb.append("    businessOpsId: ").append(toIndentedString(businessOpsId)).append("\n");
    sb.append("    businessOpsName: ").append(toIndentedString(businessOpsName)).append("\n");
    sb.append("    businessUnitName: ").append(toIndentedString(businessUnitName)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    eftSubject: ").append(toIndentedString(eftSubject)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    processId: ").append(toIndentedString(processId)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

