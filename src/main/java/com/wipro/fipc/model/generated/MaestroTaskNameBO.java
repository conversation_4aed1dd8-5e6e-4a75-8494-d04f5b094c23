/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * MaestroTaskNameBO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class MaestroTaskNameBO   {
  @JsonProperty("maestroTaskName")
  private String maestroTaskName = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  public MaestroTaskNameBO maestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
    return this;
  }

   /**
   * Get maestroTaskName
   * @return maestroTaskName
  **/
  @JsonProperty("maestroTaskName")
  @ApiModelProperty(value = "")
  public String getMaestroTaskName() {
    return maestroTaskName;
  }

  public void setMaestroTaskName(String maestroTaskName) {
    this.maestroTaskName = maestroTaskName;
  }

  public MaestroTaskNameBO processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public MaestroTaskNameBO updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MaestroTaskNameBO maestroTaskNameBO = (MaestroTaskNameBO) o;
    return Objects.equals(this.maestroTaskName, maestroTaskNameBO.maestroTaskName) &&
        Objects.equals(this.processJobMappingId, maestroTaskNameBO.processJobMappingId) &&
        Objects.equals(this.updatedBy, maestroTaskNameBO.updatedBy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(maestroTaskName, processJobMappingId, updatedBy);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MaestroTaskNameBO {\n");
    
    sb.append("    maestroTaskName: ").append(toIndentedString(maestroTaskName)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

