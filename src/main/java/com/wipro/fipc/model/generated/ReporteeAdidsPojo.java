/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * ReporteeAdidsPojo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ReporteeAdidsPojo   {
  @JsonProperty("adid")
  private String adid = null;

  @JsonProperty("racfId")
  private String racfId = null;

  public ReporteeAdidsPojo adid(String adid) {
    this.adid = adid;
    return this;
  }

   /**
   * Get adid
   * @return adid
  **/
  @JsonProperty("adid")
  @ApiModelProperty(value = "")
  public String getAdid() {
    return adid;
  }

  public void setAdid(String adid) {
    this.adid = adid;
  }

  public ReporteeAdidsPojo racfId(String racfId) {
    this.racfId = racfId;
    return this;
  }

   /**
   * Get racfId
   * @return racfId
  **/
  @JsonProperty("racfId")
  @ApiModelProperty(value = "")
  public String getRacfId() {
    return racfId;
  }

  public void setRacfId(String racfId) {
    this.racfId = racfId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReporteeAdidsPojo reporteeAdidsPojo = (ReporteeAdidsPojo) o;
    return Objects.equals(this.adid, reporteeAdidsPojo.adid) &&
        Objects.equals(this.racfId, reporteeAdidsPojo.racfId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(adid, racfId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReporteeAdidsPojo {\n");
    
    sb.append("    adid: ").append(toIndentedString(adid)).append("\n");
    sb.append("    racfId: ").append(toIndentedString(racfId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

