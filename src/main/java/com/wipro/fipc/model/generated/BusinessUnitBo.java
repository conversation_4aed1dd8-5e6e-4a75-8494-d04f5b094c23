/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * BusinessUnitBo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class BusinessUnitBo   {
  @JsonProperty("unitCode")
  private String unitCode = null;

  @JsonProperty("unitId")
  private Long unitId = null;

  @JsonProperty("unitName")
  private String unitName = null;

  public BusinessUnitBo unitCode(String unitCode) {
    this.unitCode = unitCode;
    return this;
  }

   /**
   * Get unitCode
   * @return unitCode
  **/
  @JsonProperty("unitCode")
  @ApiModelProperty(value = "")
  public String getUnitCode() {
    return unitCode;
  }

  public void setUnitCode(String unitCode) {
    this.unitCode = unitCode;
  }

  public BusinessUnitBo unitId(Long unitId) {
    this.unitId = unitId;
    return this;
  }

   /**
   * Get unitId
   * @return unitId
  **/
  @JsonProperty("unitId")
  @ApiModelProperty(value = "")
  public Long getUnitId() {
    return unitId;
  }

  public void setUnitId(Long unitId) {
    this.unitId = unitId;
  }

  public BusinessUnitBo unitName(String unitName) {
    this.unitName = unitName;
    return this;
  }

   /**
   * Get unitName
   * @return unitName
  **/
  @JsonProperty("unitName")
  @ApiModelProperty(value = "")
  public String getUnitName() {
    return unitName;
  }

  public void setUnitName(String unitName) {
    this.unitName = unitName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BusinessUnitBo businessUnitBo = (BusinessUnitBo) o;
    return Objects.equals(this.unitCode, businessUnitBo.unitCode) &&
        Objects.equals(this.unitId, businessUnitBo.unitId) &&
        Objects.equals(this.unitName, businessUnitBo.unitName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(unitCode, unitId, unitName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BusinessUnitBo {\n");
    
    sb.append("    unitCode: ").append(toIndentedString(unitCode)).append("\n");
    sb.append("    unitId: ").append(toIndentedString(unitId)).append("\n");
    sb.append("    unitName: ").append(toIndentedString(unitName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

