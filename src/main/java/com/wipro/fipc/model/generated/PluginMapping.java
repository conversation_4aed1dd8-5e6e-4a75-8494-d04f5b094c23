/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * PluginMapping
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class PluginMapping   {
  @JsonProperty("active")
  private Integer active = null;

  @JsonProperty("depthApplicable")
  private Integer depthApplicable = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("orderBy")
  private Integer orderBy = null;

  @JsonProperty("parentPhase")
  private Integer parentPhase = null;

  @JsonProperty("phase")
  private Integer phase = null;

  @JsonProperty("plugin")
  private Integer plugin = null;

  @JsonProperty("role")
  private String role = null;

  @JsonProperty("selected")
  private Integer selected = null;

  public PluginMapping active(Integer active) {
    this.active = active;
    return this;
  }

   /**
   * Get active
   * @return active
  **/
  @JsonProperty("active")
  @ApiModelProperty(value = "")
  public Integer getActive() {
    return active;
  }

  public void setActive(Integer active) {
    this.active = active;
  }

  public PluginMapping depthApplicable(Integer depthApplicable) {
    this.depthApplicable = depthApplicable;
    return this;
  }

   /**
   * Get depthApplicable
   * @return depthApplicable
  **/
  @JsonProperty("depthApplicable")
  @ApiModelProperty(value = "")
  public Integer getDepthApplicable() {
    return depthApplicable;
  }

  public void setDepthApplicable(Integer depthApplicable) {
    this.depthApplicable = depthApplicable;
  }

  public PluginMapping id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public PluginMapping orderBy(Integer orderBy) {
    this.orderBy = orderBy;
    return this;
  }

   /**
   * Get orderBy
   * @return orderBy
  **/
  @JsonProperty("orderBy")
  @ApiModelProperty(value = "")
  public Integer getOrderBy() {
    return orderBy;
  }

  public void setOrderBy(Integer orderBy) {
    this.orderBy = orderBy;
  }

  public PluginMapping parentPhase(Integer parentPhase) {
    this.parentPhase = parentPhase;
    return this;
  }

   /**
   * Get parentPhase
   * @return parentPhase
  **/
  @JsonProperty("parentPhase")
  @ApiModelProperty(value = "")
  public Integer getParentPhase() {
    return parentPhase;
  }

  public void setParentPhase(Integer parentPhase) {
    this.parentPhase = parentPhase;
  }

  public PluginMapping phase(Integer phase) {
    this.phase = phase;
    return this;
  }

   /**
   * Get phase
   * @return phase
  **/
  @JsonProperty("phase")
  @ApiModelProperty(value = "")
  public Integer getPhase() {
    return phase;
  }

  public void setPhase(Integer phase) {
    this.phase = phase;
  }

  public PluginMapping plugin(Integer plugin) {
    this.plugin = plugin;
    return this;
  }

   /**
   * Get plugin
   * @return plugin
  **/
  @JsonProperty("plugin")
  @ApiModelProperty(value = "")
  public Integer getPlugin() {
    return plugin;
  }

  public void setPlugin(Integer plugin) {
    this.plugin = plugin;
  }

  public PluginMapping role(String role) {
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public String getRole() {
    return role;
  }

  public void setRole(String role) {
    this.role = role;
  }

  public PluginMapping selected(Integer selected) {
    this.selected = selected;
    return this;
  }

   /**
   * Get selected
   * @return selected
  **/
  @JsonProperty("selected")
  @ApiModelProperty(value = "")
  public Integer getSelected() {
    return selected;
  }

  public void setSelected(Integer selected) {
    this.selected = selected;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PluginMapping pluginMapping = (PluginMapping) o;
    return Objects.equals(this.active, pluginMapping.active) &&
        Objects.equals(this.depthApplicable, pluginMapping.depthApplicable) &&
        Objects.equals(this.id, pluginMapping.id) &&
        Objects.equals(this.orderBy, pluginMapping.orderBy) &&
        Objects.equals(this.parentPhase, pluginMapping.parentPhase) &&
        Objects.equals(this.phase, pluginMapping.phase) &&
        Objects.equals(this.plugin, pluginMapping.plugin) &&
        Objects.equals(this.role, pluginMapping.role) &&
        Objects.equals(this.selected, pluginMapping.selected);
  }

  @Override
  public int hashCode() {
    return Objects.hash(active, depthApplicable, id, orderBy, parentPhase, phase, plugin, role, selected);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PluginMapping {\n");
    
    sb.append("    active: ").append(toIndentedString(active)).append("\n");
    sb.append("    depthApplicable: ").append(toIndentedString(depthApplicable)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    orderBy: ").append(toIndentedString(orderBy)).append("\n");
    sb.append("    parentPhase: ").append(toIndentedString(parentPhase)).append("\n");
    sb.append("    phase: ").append(toIndentedString(phase)).append("\n");
    sb.append("    plugin: ").append(toIndentedString(plugin)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    selected: ").append(toIndentedString(selected)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

