/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ParticipantRecordIdentifier
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ParticipantRecordIdentifier   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("recordIdentifierName")
  private String recordIdentifierName = null;

  @JsonProperty("reoccurance")
  private String reoccurance = null;

  @JsonProperty("sequence")
  private String sequence = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public ParticipantRecordIdentifier activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ParticipantRecordIdentifier createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ParticipantRecordIdentifier createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ParticipantRecordIdentifier id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ParticipantRecordIdentifier processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public ParticipantRecordIdentifier recordIdentifierName(String recordIdentifierName) {
    this.recordIdentifierName = recordIdentifierName;
    return this;
  }

   /**
   * Get recordIdentifierName
   * @return recordIdentifierName
  **/
  @JsonProperty("recordIdentifierName")
  @ApiModelProperty(value = "")
  public String getRecordIdentifierName() {
    return recordIdentifierName;
  }

  public void setRecordIdentifierName(String recordIdentifierName) {
    this.recordIdentifierName = recordIdentifierName;
  }

  public ParticipantRecordIdentifier reoccurance(String reoccurance) {
    this.reoccurance = reoccurance;
    return this;
  }

   /**
   * Get reoccurance
   * @return reoccurance
  **/
  @JsonProperty("reoccurance")
  @ApiModelProperty(value = "")
  public String getReoccurance() {
    return reoccurance;
  }

  public void setReoccurance(String reoccurance) {
    this.reoccurance = reoccurance;
  }

  public ParticipantRecordIdentifier sequence(String sequence) {
    this.sequence = sequence;
    return this;
  }

   /**
   * Get sequence
   * @return sequence
  **/
  @JsonProperty("sequence")
  @ApiModelProperty(value = "")
  public String getSequence() {
    return sequence;
  }

  public void setSequence(String sequence) {
    this.sequence = sequence;
  }

  public ParticipantRecordIdentifier updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ParticipantRecordIdentifier updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ParticipantRecordIdentifier participantRecordIdentifier = (ParticipantRecordIdentifier) o;
    return Objects.equals(this.activeFlag, participantRecordIdentifier.activeFlag) &&
        Objects.equals(this.createdBy, participantRecordIdentifier.createdBy) &&
        Objects.equals(this.createdDate, participantRecordIdentifier.createdDate) &&
        Objects.equals(this.id, participantRecordIdentifier.id) &&
        Objects.equals(this.processJobMapping, participantRecordIdentifier.processJobMapping) &&
        Objects.equals(this.recordIdentifierName, participantRecordIdentifier.recordIdentifierName) &&
        Objects.equals(this.reoccurance, participantRecordIdentifier.reoccurance) &&
        Objects.equals(this.sequence, participantRecordIdentifier.sequence) &&
        Objects.equals(this.updatedBy, participantRecordIdentifier.updatedBy) &&
        Objects.equals(this.updatedDate, participantRecordIdentifier.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, createdBy, createdDate, id, processJobMapping, recordIdentifierName, reoccurance, sequence, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ParticipantRecordIdentifier {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    recordIdentifierName: ").append(toIndentedString(recordIdentifierName)).append("\n");
    sb.append("    reoccurance: ").append(toIndentedString(reoccurance)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

