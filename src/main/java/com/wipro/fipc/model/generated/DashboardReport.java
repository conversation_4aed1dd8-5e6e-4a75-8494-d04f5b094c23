/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * DashboardReport
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class DashboardReport   {
  @JsonProperty("assignedBy")
  private String assignedBy = null;

  @JsonProperty("assignedDate")
  private Date assignedDate = null;

  @JsonProperty("assignee")
  private String assignee = null;

  @JsonProperty("botId")
  private String botId = null;

  @JsonProperty("businessOps")
  private String businessOps = null;

  @JsonProperty("businessUnit")
  private String businessUnit = null;

  @JsonProperty("clientId")
  private String clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("createDateTime")
  private Date createDateTime = null;

  @JsonProperty("createTimestamp")
  private Long createTimestamp = null;

  @JsonProperty("eftDateTime")
  private String eftDateTime = null;

  @JsonProperty("eftStatus")
  private String eftStatus = null;

  @JsonProperty("eftSubj")
  private String eftSubj = null;

  @JsonProperty("endDateTime")
  private Date endDateTime = null;

  @JsonProperty("endTimestamp")
  private Integer endTimestamp = null;

  @JsonProperty("frequency")
  private String frequency = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("newAssignee")
  private String newAssignee = null;

  @JsonProperty("pjmId")
  private Long pjmId = null;

  @JsonProperty("pluginName")
  private String pluginName = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  @JsonProperty("sla")
  private Integer sla = null;

  @JsonProperty("startDateTime")
  private Date startDateTime = null;

  @JsonProperty("startTimestamp")
  private Long startTimestamp = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("taskId")
  private String taskId = null;

  @JsonProperty("uid")
  private String uid = null;

  public DashboardReport assignedBy(String assignedBy) {
    this.assignedBy = assignedBy;
    return this;
  }

   /**
   * Get assignedBy
   * @return assignedBy
  **/
  @JsonProperty("assignedBy")
  @ApiModelProperty(value = "")
  public String getAssignedBy() {
    return assignedBy;
  }

  public void setAssignedBy(String assignedBy) {
    this.assignedBy = assignedBy;
  }

  public DashboardReport assignedDate(Date assignedDate) {
    this.assignedDate = assignedDate;
    return this;
  }

   /**
   * Get assignedDate
   * @return assignedDate
  **/
  @JsonProperty("assignedDate")
  @ApiModelProperty(value = "")
  public Date getAssignedDate() {
    return assignedDate;
  }

  public void setAssignedDate(Date assignedDate) {
    this.assignedDate = assignedDate;
  }

  public DashboardReport assignee(String assignee) {
    this.assignee = assignee;
    return this;
  }

   /**
   * Get assignee
   * @return assignee
  **/
  @JsonProperty("assignee")
  @ApiModelProperty(value = "")
  public String getAssignee() {
    return assignee;
  }

  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public DashboardReport botId(String botId) {
    this.botId = botId;
    return this;
  }

   /**
   * Get botId
   * @return botId
  **/
  @JsonProperty("botId")
  @ApiModelProperty(value = "")
  public String getBotId() {
    return botId;
  }

  public void setBotId(String botId) {
    this.botId = botId;
  }

  public DashboardReport businessOps(String businessOps) {
    this.businessOps = businessOps;
    return this;
  }

   /**
   * Get businessOps
   * @return businessOps
  **/
  @JsonProperty("businessOps")
  @ApiModelProperty(value = "")
  public String getBusinessOps() {
    return businessOps;
  }

  public void setBusinessOps(String businessOps) {
    this.businessOps = businessOps;
  }

  public DashboardReport businessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public String getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
  }

  public DashboardReport clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public DashboardReport clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public DashboardReport createDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
    return this;
  }

   /**
   * Get createDateTime
   * @return createDateTime
  **/
  @JsonProperty("createDateTime")
  @ApiModelProperty(value = "")
  public Date getCreateDateTime() {
    return createDateTime;
  }

  public void setCreateDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
  }

  public DashboardReport createTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
    return this;
  }

   /**
   * Get createTimestamp
   * @return createTimestamp
  **/
  @JsonProperty("createTimestamp")
  @ApiModelProperty(value = "")
  public Long getCreateTimestamp() {
    return createTimestamp;
  }

  public void setCreateTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
  }

  public DashboardReport eftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
    return this;
  }

   /**
   * Get eftDateTime
   * @return eftDateTime
  **/
  @JsonProperty("eftDateTime")
  @ApiModelProperty(value = "")
  public String getEftDateTime() {
    return eftDateTime;
  }

  public void setEftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
  }

  public DashboardReport eftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
    return this;
  }

   /**
   * Get eftStatus
   * @return eftStatus
  **/
  @JsonProperty("eftStatus")
  @ApiModelProperty(value = "")
  public String getEftStatus() {
    return eftStatus;
  }

  public void setEftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
  }

  public DashboardReport eftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
    return this;
  }

   /**
   * Get eftSubj
   * @return eftSubj
  **/
  @JsonProperty("eftSubj")
  @ApiModelProperty(value = "")
  public String getEftSubj() {
    return eftSubj;
  }

  public void setEftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
  }

  public DashboardReport endDateTime(Date endDateTime) {
    this.endDateTime = endDateTime;
    return this;
  }

   /**
   * Get endDateTime
   * @return endDateTime
  **/
  @JsonProperty("endDateTime")
  @ApiModelProperty(value = "")
  public Date getEndDateTime() {
    return endDateTime;
  }

  public void setEndDateTime(Date endDateTime) {
    this.endDateTime = endDateTime;
  }

  public DashboardReport endTimestamp(Integer endTimestamp) {
    this.endTimestamp = endTimestamp;
    return this;
  }

   /**
   * Get endTimestamp
   * @return endTimestamp
  **/
  @JsonProperty("endTimestamp")
  @ApiModelProperty(value = "")
  public Integer getEndTimestamp() {
    return endTimestamp;
  }

  public void setEndTimestamp(Integer endTimestamp) {
    this.endTimestamp = endTimestamp;
  }

  public DashboardReport frequency(String frequency) {
    this.frequency = frequency;
    return this;
  }

   /**
   * Get frequency
   * @return frequency
  **/
  @JsonProperty("frequency")
  @ApiModelProperty(value = "")
  public String getFrequency() {
    return frequency;
  }

  public void setFrequency(String frequency) {
    this.frequency = frequency;
  }

  public DashboardReport id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public DashboardReport jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public DashboardReport ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public DashboardReport newAssignee(String newAssignee) {
    this.newAssignee = newAssignee;
    return this;
  }

   /**
   * Get newAssignee
   * @return newAssignee
  **/
  @JsonProperty("newAssignee")
  @ApiModelProperty(value = "")
  public String getNewAssignee() {
    return newAssignee;
  }

  public void setNewAssignee(String newAssignee) {
    this.newAssignee = newAssignee;
  }

  public DashboardReport pjmId(Long pjmId) {
    this.pjmId = pjmId;
    return this;
  }

   /**
   * Get pjmId
   * @return pjmId
  **/
  @JsonProperty("pjmId")
  @ApiModelProperty(value = "")
  public Long getPjmId() {
    return pjmId;
  }

  public void setPjmId(Long pjmId) {
    this.pjmId = pjmId;
  }

  public DashboardReport pluginName(String pluginName) {
    this.pluginName = pluginName;
    return this;
  }

   /**
   * Get pluginName
   * @return pluginName
  **/
  @JsonProperty("pluginName")
  @ApiModelProperty(value = "")
  public String getPluginName() {
    return pluginName;
  }

  public void setPluginName(String pluginName) {
    this.pluginName = pluginName;
  }

  public DashboardReport processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public DashboardReport processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public DashboardReport sla(Integer sla) {
    this.sla = sla;
    return this;
  }

   /**
   * Get sla
   * @return sla
  **/
  @JsonProperty("sla")
  @ApiModelProperty(value = "")
  public Integer getSla() {
    return sla;
  }

  public void setSla(Integer sla) {
    this.sla = sla;
  }

  public DashboardReport startDateTime(Date startDateTime) {
    this.startDateTime = startDateTime;
    return this;
  }

   /**
   * Get startDateTime
   * @return startDateTime
  **/
  @JsonProperty("startDateTime")
  @ApiModelProperty(value = "")
  public Date getStartDateTime() {
    return startDateTime;
  }

  public void setStartDateTime(Date startDateTime) {
    this.startDateTime = startDateTime;
  }

  public DashboardReport startTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
    return this;
  }

   /**
   * Get startTimestamp
   * @return startTimestamp
  **/
  @JsonProperty("startTimestamp")
  @ApiModelProperty(value = "")
  public Long getStartTimestamp() {
    return startTimestamp;
  }

  public void setStartTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
  }

  public DashboardReport status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public DashboardReport taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

   /**
   * Get taskId
   * @return taskId
  **/
  @JsonProperty("taskId")
  @ApiModelProperty(value = "")
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public DashboardReport uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DashboardReport dashboardReport = (DashboardReport) o;
    return Objects.equals(this.assignedBy, dashboardReport.assignedBy) &&
        Objects.equals(this.assignedDate, dashboardReport.assignedDate) &&
        Objects.equals(this.assignee, dashboardReport.assignee) &&
        Objects.equals(this.botId, dashboardReport.botId) &&
        Objects.equals(this.businessOps, dashboardReport.businessOps) &&
        Objects.equals(this.businessUnit, dashboardReport.businessUnit) &&
        Objects.equals(this.clientId, dashboardReport.clientId) &&
        Objects.equals(this.clientName, dashboardReport.clientName) &&
        Objects.equals(this.createDateTime, dashboardReport.createDateTime) &&
        Objects.equals(this.createTimestamp, dashboardReport.createTimestamp) &&
        Objects.equals(this.eftDateTime, dashboardReport.eftDateTime) &&
        Objects.equals(this.eftStatus, dashboardReport.eftStatus) &&
        Objects.equals(this.eftSubj, dashboardReport.eftSubj) &&
        Objects.equals(this.endDateTime, dashboardReport.endDateTime) &&
        Objects.equals(this.endTimestamp, dashboardReport.endTimestamp) &&
        Objects.equals(this.frequency, dashboardReport.frequency) &&
        Objects.equals(this.id, dashboardReport.id) &&
        Objects.equals(this.jobName, dashboardReport.jobName) &&
        Objects.equals(this.ksdName, dashboardReport.ksdName) &&
        Objects.equals(this.newAssignee, dashboardReport.newAssignee) &&
        Objects.equals(this.pjmId, dashboardReport.pjmId) &&
        Objects.equals(this.pluginName, dashboardReport.pluginName) &&
        Objects.equals(this.processName, dashboardReport.processName) &&
        Objects.equals(this.processType, dashboardReport.processType) &&
        Objects.equals(this.sla, dashboardReport.sla) &&
        Objects.equals(this.startDateTime, dashboardReport.startDateTime) &&
        Objects.equals(this.startTimestamp, dashboardReport.startTimestamp) &&
        Objects.equals(this.status, dashboardReport.status) &&
        Objects.equals(this.taskId, dashboardReport.taskId) &&
        Objects.equals(this.uid, dashboardReport.uid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(assignedBy, assignedDate, assignee, botId, businessOps, businessUnit, clientId, clientName, createDateTime, createTimestamp, eftDateTime, eftStatus, eftSubj, endDateTime, endTimestamp, frequency, id, jobName, ksdName, newAssignee, pjmId, pluginName, processName, processType, sla, startDateTime, startTimestamp, status, taskId, uid);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DashboardReport {\n");
    
    sb.append("    assignedBy: ").append(toIndentedString(assignedBy)).append("\n");
    sb.append("    assignedDate: ").append(toIndentedString(assignedDate)).append("\n");
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    botId: ").append(toIndentedString(botId)).append("\n");
    sb.append("    businessOps: ").append(toIndentedString(businessOps)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    createDateTime: ").append(toIndentedString(createDateTime)).append("\n");
    sb.append("    createTimestamp: ").append(toIndentedString(createTimestamp)).append("\n");
    sb.append("    eftDateTime: ").append(toIndentedString(eftDateTime)).append("\n");
    sb.append("    eftStatus: ").append(toIndentedString(eftStatus)).append("\n");
    sb.append("    eftSubj: ").append(toIndentedString(eftSubj)).append("\n");
    sb.append("    endDateTime: ").append(toIndentedString(endDateTime)).append("\n");
    sb.append("    endTimestamp: ").append(toIndentedString(endTimestamp)).append("\n");
    sb.append("    frequency: ").append(toIndentedString(frequency)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    newAssignee: ").append(toIndentedString(newAssignee)).append("\n");
    sb.append("    pjmId: ").append(toIndentedString(pjmId)).append("\n");
    sb.append("    pluginName: ").append(toIndentedString(pluginName)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    sla: ").append(toIndentedString(sla)).append("\n");
    sb.append("    startDateTime: ").append(toIndentedString(startDateTime)).append("\n");
    sb.append("    startTimestamp: ").append(toIndentedString(startTimestamp)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

