/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ProcessControlConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ProcessControlConfig   {
  @JsonProperty("actions")
  private String actions = null;

  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("byteaActions")
  private byte[] byteaActions = null;

  @JsonProperty("application")
  private String application = null;

  @JsonProperty("applicationWoutSpace")
  private String applicationWoutSpace = null;

  @JsonProperty("correctiveAction")
  private String correctiveAction = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("fieldName")
  private String fieldName = null;

  @JsonProperty("fieldNameWoutSpace")
  private String fieldNameWoutSpace = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("ruleName")
  private String ruleName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public ProcessControlConfig actions(String actions) {
    this.actions = actions;
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  @JsonProperty("actions")
  @ApiModelProperty(value = "")
  public String getActions() {
    return actions;
  }

  public void setActions(String actions) {
    this.actions = actions;
  }

  public ProcessControlConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ProcessControlConfig byteaActions(byte[] byteaActions) {
    this.byteaActions = byteaActions;
    return this;
  }

   /**
   * Get byteaActions
   * @return byteaActions
  **/
  @JsonProperty("byteaActions")
  @ApiModelProperty(value = "")
  public byte[] getByteaActions() {
    return byteaActions;
  }

  public void setByteaActions(byte[] byteaActions) {
    this.byteaActions = byteaActions;
  }

  public ProcessControlConfig application(String application) {
    this.application = application;
    return this;
  }

   /**
   * Get application
   * @return application
  **/
  @JsonProperty("application")
  @ApiModelProperty(value = "")
  public String getApplication() {
    return application;
  }

  public void setApplication(String application) {
    this.application = application;
  }

  public ProcessControlConfig applicationWoutSpace(String applicationWoutSpace) {
    this.applicationWoutSpace = applicationWoutSpace;
    return this;
  }

   /**
   * Get applicationWoutSpace
   * @return applicationWoutSpace
  **/
  @JsonProperty("applicationWoutSpace")
  @ApiModelProperty(value = "")
  public String getApplicationWoutSpace() {
    return applicationWoutSpace;
  }

  public void setApplicationWoutSpace(String applicationWoutSpace) {
    this.applicationWoutSpace = applicationWoutSpace;
  }

  public ProcessControlConfig correctiveAction(String correctiveAction) {
    this.correctiveAction = correctiveAction;
    return this;
  }

   /**
   * Get correctiveAction
   * @return correctiveAction
  **/
  @JsonProperty("correctiveAction")
  @ApiModelProperty(value = "")
  public String getCorrectiveAction() {
    return correctiveAction;
  }

  public void setCorrectiveAction(String correctiveAction) {
    this.correctiveAction = correctiveAction;
  }

  public ProcessControlConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ProcessControlConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ProcessControlConfig fieldName(String fieldName) {
    this.fieldName = fieldName;
    return this;
  }

   /**
   * Get fieldName
   * @return fieldName
  **/
  @JsonProperty("fieldName")
  @ApiModelProperty(value = "")
  public String getFieldName() {
    return fieldName;
  }

  public void setFieldName(String fieldName) {
    this.fieldName = fieldName;
  }

  public ProcessControlConfig fieldNameWoutSpace(String fieldNameWoutSpace) {
    this.fieldNameWoutSpace = fieldNameWoutSpace;
    return this;
  }

   /**
   * Get fieldNameWoutSpace
   * @return fieldNameWoutSpace
  **/
  @JsonProperty("fieldNameWoutSpace")
  @ApiModelProperty(value = "")
  public String getFieldNameWoutSpace() {
    return fieldNameWoutSpace;
  }

  public void setFieldNameWoutSpace(String fieldNameWoutSpace) {
    this.fieldNameWoutSpace = fieldNameWoutSpace;
  }

  public ProcessControlConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ProcessControlConfig identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public ProcessControlConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public ProcessControlConfig ruleName(String ruleName) {
    this.ruleName = ruleName;
    return this;
  }

   /**
   * Get ruleName
   * @return ruleName
  **/
  @JsonProperty("ruleName")
  @ApiModelProperty(value = "")
  public String getRuleName() {
    return ruleName;
  }

  public void setRuleName(String ruleName) {
    this.ruleName = ruleName;
  }

  public ProcessControlConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ProcessControlConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessControlConfig processControlConfig = (ProcessControlConfig) o;
    return Objects.equals(this.actions, processControlConfig.actions) &&
        Objects.equals(this.activeFlag, processControlConfig.activeFlag) &&
        Objects.equals(this.byteaActions, processControlConfig.byteaActions) &&
        Objects.equals(this.application, processControlConfig.application) &&
        Objects.equals(this.applicationWoutSpace, processControlConfig.applicationWoutSpace) &&
        Objects.equals(this.correctiveAction, processControlConfig.correctiveAction) &&
        Objects.equals(this.createdBy, processControlConfig.createdBy) &&
        Objects.equals(this.createdDate, processControlConfig.createdDate) &&
        Objects.equals(this.fieldName, processControlConfig.fieldName) &&
        Objects.equals(this.fieldNameWoutSpace, processControlConfig.fieldNameWoutSpace) &&
        Objects.equals(this.id, processControlConfig.id) &&
        Objects.equals(this.identifier, processControlConfig.identifier) &&
        Objects.equals(this.processJobMapping, processControlConfig.processJobMapping) &&
        Objects.equals(this.ruleName, processControlConfig.ruleName) &&
        Objects.equals(this.updatedBy, processControlConfig.updatedBy) &&
        Objects.equals(this.updatedDate, processControlConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, activeFlag, byteaActions, application, applicationWoutSpace, correctiveAction, createdBy, createdDate, fieldName, fieldNameWoutSpace, id, identifier, processJobMapping, ruleName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessControlConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    byteaActions: ").append(toIndentedString(byteaActions)).append("\n");
    sb.append("    application: ").append(toIndentedString(application)).append("\n");
    sb.append("    applicationWoutSpace: ").append(toIndentedString(applicationWoutSpace)).append("\n");
    sb.append("    correctiveAction: ").append(toIndentedString(correctiveAction)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    fieldName: ").append(toIndentedString(fieldName)).append("\n");
    sb.append("    fieldNameWoutSpace: ").append(toIndentedString(fieldNameWoutSpace)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    ruleName: ").append(toIndentedString(ruleName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

