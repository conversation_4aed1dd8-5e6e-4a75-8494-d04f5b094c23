/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * CustomNRCBo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class CustomNRCBo   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("appendSubject")
  private String appendSubject = null;

  @JsonProperty("application")
  private String application = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("file")
  private String file = null;

  @JsonProperty("fileType")
  private String fileType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("path")
  private String path = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("remarks")
  private String remarks = null;

  @JsonProperty("reportName")
  private String reportName = null;

  @JsonProperty("subfolder")
  private String subfolder = null;

  @JsonProperty("subject")
  private String subject = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public CustomNRCBo activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public CustomNRCBo appendSubject(String appendSubject) {
    this.appendSubject = appendSubject;
    return this;
  }

   /**
   * Get appendSubject
   * @return appendSubject
  **/
  @JsonProperty("appendSubject")
  @ApiModelProperty(value = "")
  public String getAppendSubject() {
    return appendSubject;
  }

  public void setAppendSubject(String appendSubject) {
    this.appendSubject = appendSubject;
  }

  public CustomNRCBo application(String application) {
    this.application = application;
    return this;
  }

   /**
   * Get application
   * @return application
  **/
  @JsonProperty("application")
  @ApiModelProperty(value = "")
  public String getApplication() {
    return application;
  }

  public void setApplication(String application) {
    this.application = application;
  }

  public CustomNRCBo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public CustomNRCBo createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public CustomNRCBo file(String file) {
    this.file = file;
    return this;
  }

   /**
   * Get file
   * @return file
  **/
  @JsonProperty("file")
  @ApiModelProperty(value = "")
  public String getFile() {
    return file;
  }

  public void setFile(String file) {
    this.file = file;
  }

  public CustomNRCBo fileType(String fileType) {
    this.fileType = fileType;
    return this;
  }

   /**
   * Get fileType
   * @return fileType
  **/
  @JsonProperty("fileType")
  @ApiModelProperty(value = "")
  public String getFileType() {
    return fileType;
  }

  public void setFileType(String fileType) {
    this.fileType = fileType;
  }

  public CustomNRCBo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public CustomNRCBo path(String path) {
    this.path = path;
    return this;
  }

   /**
   * Get path
   * @return path
  **/
  @JsonProperty("path")
  @ApiModelProperty(value = "")
  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public CustomNRCBo processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public CustomNRCBo remarks(String remarks) {
    this.remarks = remarks;
    return this;
  }

   /**
   * Get remarks
   * @return remarks
  **/
  @JsonProperty("remarks")
  @ApiModelProperty(value = "")
  public String getRemarks() {
    return remarks;
  }

  public void setRemarks(String remarks) {
    this.remarks = remarks;
  }

  public CustomNRCBo reportName(String reportName) {
    this.reportName = reportName;
    return this;
  }

   /**
   * Get reportName
   * @return reportName
  **/
  @JsonProperty("reportName")
  @ApiModelProperty(value = "")
  public String getReportName() {
    return reportName;
  }

  public void setReportName(String reportName) {
    this.reportName = reportName;
  }

  public CustomNRCBo subfolder(String subfolder) {
    this.subfolder = subfolder;
    return this;
  }

   /**
   * Get subfolder
   * @return subfolder
  **/
  @JsonProperty("subfolder")
  @ApiModelProperty(value = "")
  public String getSubfolder() {
    return subfolder;
  }

  public void setSubfolder(String subfolder) {
    this.subfolder = subfolder;
  }

  public CustomNRCBo subject(String subject) {
    this.subject = subject;
    return this;
  }

   /**
   * Get subject
   * @return subject
  **/
  @JsonProperty("subject")
  @ApiModelProperty(value = "")
  public String getSubject() {
    return subject;
  }

  public void setSubject(String subject) {
    this.subject = subject;
  }

  public CustomNRCBo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public CustomNRCBo updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomNRCBo customNRCBo = (CustomNRCBo) o;
    return Objects.equals(this.activeFlag, customNRCBo.activeFlag) &&
        Objects.equals(this.appendSubject, customNRCBo.appendSubject) &&
        Objects.equals(this.application, customNRCBo.application) &&
        Objects.equals(this.createdBy, customNRCBo.createdBy) &&
        Objects.equals(this.createdDate, customNRCBo.createdDate) &&
        Objects.equals(this.file, customNRCBo.file) &&
        Objects.equals(this.fileType, customNRCBo.fileType) &&
        Objects.equals(this.id, customNRCBo.id) &&
        Objects.equals(this.path, customNRCBo.path) &&
        Objects.equals(this.processJobMappingId, customNRCBo.processJobMappingId) &&
        Objects.equals(this.remarks, customNRCBo.remarks) &&
        Objects.equals(this.reportName, customNRCBo.reportName) &&
        Objects.equals(this.subfolder, customNRCBo.subfolder) &&
        Objects.equals(this.subject, customNRCBo.subject) &&
        Objects.equals(this.updatedBy, customNRCBo.updatedBy) &&
        Objects.equals(this.updatedDate, customNRCBo.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, appendSubject, application, createdBy, createdDate, file, fileType, id, path, processJobMappingId, remarks, reportName, subfolder, subject, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomNRCBo {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    appendSubject: ").append(toIndentedString(appendSubject)).append("\n");
    sb.append("    application: ").append(toIndentedString(application)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    file: ").append(toIndentedString(file)).append("\n");
    sb.append("    fileType: ").append(toIndentedString(fileType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    path: ").append(toIndentedString(path)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    remarks: ").append(toIndentedString(remarks)).append("\n");
    sb.append("    reportName: ").append(toIndentedString(reportName)).append("\n");
    sb.append("    subfolder: ").append(toIndentedString(subfolder)).append("\n");
    sb.append("    subject: ").append(toIndentedString(subject)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

