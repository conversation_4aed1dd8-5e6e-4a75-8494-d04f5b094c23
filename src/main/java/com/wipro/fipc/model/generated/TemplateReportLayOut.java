/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.TemplateReportUpload;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TemplateReportLayOut
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TemplateReportLayOut   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("dataElement")
  private String dataElement = null;

  @JsonProperty("dataElementWs")
  private String dataElementWs = null;

  @JsonProperty("filedType")
  private String filedType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("identifierId")
  private String identifierId = null;

  @JsonProperty("identifierWs")
  private String identifierWs = null;

  @JsonProperty("labellingReportRecord")
  private String labellingReportRecord = null;

  @JsonProperty("sequence")
  private String sequence = null;

  @JsonProperty("sheetName")
  private String sheetName = null;

  @JsonProperty("sheetNameWs")
  private String sheetNameWs = null;

  @JsonProperty("templateReportName")
  private String templateReportName = null;

  @JsonProperty("templateReportNameWs")
  private String templateReportNameWs = null;

  @JsonProperty("templateReportUpload")
  private TemplateReportUpload templateReportUpload = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TemplateReportLayOut activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TemplateReportLayOut createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TemplateReportLayOut createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TemplateReportLayOut dataElement(String dataElement) {
    this.dataElement = dataElement;
    return this;
  }

   /**
   * Get dataElement
   * @return dataElement
  **/
  @JsonProperty("dataElement")
  @ApiModelProperty(value = "")
  public String getDataElement() {
    return dataElement;
  }

  public void setDataElement(String dataElement) {
    this.dataElement = dataElement;
  }

  public TemplateReportLayOut dataElementWs(String dataElementWs) {
    this.dataElementWs = dataElementWs;
    return this;
  }

   /**
   * Get dataElementWs
   * @return dataElementWs
  **/
  @JsonProperty("dataElementWs")
  @ApiModelProperty(value = "")
  public String getDataElementWs() {
    return dataElementWs;
  }

  public void setDataElementWs(String dataElementWs) {
    this.dataElementWs = dataElementWs;
  }

  public TemplateReportLayOut filedType(String filedType) {
    this.filedType = filedType;
    return this;
  }

   /**
   * Get filedType
   * @return filedType
  **/
  @JsonProperty("filedType")
  @ApiModelProperty(value = "")
  public String getFiledType() {
    return filedType;
  }

  public void setFiledType(String filedType) {
    this.filedType = filedType;
  }

  public TemplateReportLayOut id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TemplateReportLayOut identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public TemplateReportLayOut identifierId(String identifierId) {
    this.identifierId = identifierId;
    return this;
  }

   /**
   * Get identifierId
   * @return identifierId
  **/
  @JsonProperty("identifierId")
  @ApiModelProperty(value = "")
  public String getIdentifierId() {
    return identifierId;
  }

  public void setIdentifierId(String identifierId) {
    this.identifierId = identifierId;
  }

  public TemplateReportLayOut identifierWs(String identifierWs) {
    this.identifierWs = identifierWs;
    return this;
  }

   /**
   * Get identifierWs
   * @return identifierWs
  **/
  @JsonProperty("identifierWs")
  @ApiModelProperty(value = "")
  public String getIdentifierWs() {
    return identifierWs;
  }

  public void setIdentifierWs(String identifierWs) {
    this.identifierWs = identifierWs;
  }

  public TemplateReportLayOut labellingReportRecord(String labellingReportRecord) {
    this.labellingReportRecord = labellingReportRecord;
    return this;
  }

   /**
   * Get labellingReportRecord
   * @return labellingReportRecord
  **/
  @JsonProperty("labellingReportRecord")
  @ApiModelProperty(value = "")
  public String getLabellingReportRecord() {
    return labellingReportRecord;
  }

  public void setLabellingReportRecord(String labellingReportRecord) {
    this.labellingReportRecord = labellingReportRecord;
  }

  public TemplateReportLayOut sequence(String sequence) {
    this.sequence = sequence;
    return this;
  }

   /**
   * Get sequence
   * @return sequence
  **/
  @JsonProperty("sequence")
  @ApiModelProperty(value = "")
  public String getSequence() {
    return sequence;
  }

  public void setSequence(String sequence) {
    this.sequence = sequence;
  }

  public TemplateReportLayOut sheetName(String sheetName) {
    this.sheetName = sheetName;
    return this;
  }

   /**
   * Get sheetName
   * @return sheetName
  **/
  @JsonProperty("sheetName")
  @ApiModelProperty(value = "")
  public String getSheetName() {
    return sheetName;
  }

  public void setSheetName(String sheetName) {
    this.sheetName = sheetName;
  }

  public TemplateReportLayOut sheetNameWs(String sheetNameWs) {
    this.sheetNameWs = sheetNameWs;
    return this;
  }

   /**
   * Get sheetNameWs
   * @return sheetNameWs
  **/
  @JsonProperty("sheetNameWs")
  @ApiModelProperty(value = "")
  public String getSheetNameWs() {
    return sheetNameWs;
  }

  public void setSheetNameWs(String sheetNameWs) {
    this.sheetNameWs = sheetNameWs;
  }

  public TemplateReportLayOut templateReportName(String templateReportName) {
    this.templateReportName = templateReportName;
    return this;
  }

   /**
   * Get templateReportName
   * @return templateReportName
  **/
  @JsonProperty("templateReportName")
  @ApiModelProperty(value = "")
  public String getTemplateReportName() {
    return templateReportName;
  }

  public void setTemplateReportName(String templateReportName) {
    this.templateReportName = templateReportName;
  }

  public TemplateReportLayOut templateReportNameWs(String templateReportNameWs) {
    this.templateReportNameWs = templateReportNameWs;
    return this;
  }

   /**
   * Get templateReportNameWs
   * @return templateReportNameWs
  **/
  @JsonProperty("templateReportNameWs")
  @ApiModelProperty(value = "")
  public String getTemplateReportNameWs() {
    return templateReportNameWs;
  }

  public void setTemplateReportNameWs(String templateReportNameWs) {
    this.templateReportNameWs = templateReportNameWs;
  }

  public TemplateReportLayOut templateReportUpload(TemplateReportUpload templateReportUpload) {
    this.templateReportUpload = templateReportUpload;
    return this;
  }

   /**
   * Get templateReportUpload
   * @return templateReportUpload
  **/
  @JsonProperty("templateReportUpload")
  @ApiModelProperty(value = "")
  public TemplateReportUpload getTemplateReportUpload() {
    return templateReportUpload;
  }

  public void setTemplateReportUpload(TemplateReportUpload templateReportUpload) {
    this.templateReportUpload = templateReportUpload;
  }

  public TemplateReportLayOut updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TemplateReportLayOut updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TemplateReportLayOut templateReportLayOut = (TemplateReportLayOut) o;
    return Objects.equals(this.activeFlag, templateReportLayOut.activeFlag) &&
        Objects.equals(this.createdBy, templateReportLayOut.createdBy) &&
        Objects.equals(this.createdDate, templateReportLayOut.createdDate) &&
        Objects.equals(this.dataElement, templateReportLayOut.dataElement) &&
        Objects.equals(this.dataElementWs, templateReportLayOut.dataElementWs) &&
        Objects.equals(this.filedType, templateReportLayOut.filedType) &&
        Objects.equals(this.id, templateReportLayOut.id) &&
        Objects.equals(this.identifier, templateReportLayOut.identifier) &&
        Objects.equals(this.identifierId, templateReportLayOut.identifierId) &&
        Objects.equals(this.identifierWs, templateReportLayOut.identifierWs) &&
        Objects.equals(this.labellingReportRecord, templateReportLayOut.labellingReportRecord) &&
        Objects.equals(this.sequence, templateReportLayOut.sequence) &&
        Objects.equals(this.sheetName, templateReportLayOut.sheetName) &&
        Objects.equals(this.sheetNameWs, templateReportLayOut.sheetNameWs) &&
        Objects.equals(this.templateReportName, templateReportLayOut.templateReportName) &&
        Objects.equals(this.templateReportNameWs, templateReportLayOut.templateReportNameWs) &&
        Objects.equals(this.templateReportUpload, templateReportLayOut.templateReportUpload) &&
        Objects.equals(this.updatedBy, templateReportLayOut.updatedBy) &&
        Objects.equals(this.updatedDate, templateReportLayOut.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, createdBy, createdDate, dataElement, dataElementWs, filedType, id, identifier, identifierId, identifierWs, labellingReportRecord, sequence, sheetName, sheetNameWs, templateReportName, templateReportNameWs, templateReportUpload, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TemplateReportLayOut {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    dataElement: ").append(toIndentedString(dataElement)).append("\n");
    sb.append("    dataElementWs: ").append(toIndentedString(dataElementWs)).append("\n");
    sb.append("    filedType: ").append(toIndentedString(filedType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    identifierId: ").append(toIndentedString(identifierId)).append("\n");
    sb.append("    identifierWs: ").append(toIndentedString(identifierWs)).append("\n");
    sb.append("    labellingReportRecord: ").append(toIndentedString(labellingReportRecord)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    sheetName: ").append(toIndentedString(sheetName)).append("\n");
    sb.append("    sheetNameWs: ").append(toIndentedString(sheetNameWs)).append("\n");
    sb.append("    templateReportName: ").append(toIndentedString(templateReportName)).append("\n");
    sb.append("    templateReportNameWs: ").append(toIndentedString(templateReportNameWs)).append("\n");
    sb.append("    templateReportUpload: ").append(toIndentedString(templateReportUpload)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

