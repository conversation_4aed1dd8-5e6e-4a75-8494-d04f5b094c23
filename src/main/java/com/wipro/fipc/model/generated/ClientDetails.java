/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.BusinessUnitClient;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.*;

/**
 * ClientDetails
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ClientDetails   {
  @JsonProperty("businessUnitClients")
  private List<BusinessUnitClient> businessUnitClients = new ArrayList<BusinessUnitClient>();

  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("groupCode")
  private String groupCode = null;

  @JsonProperty("groupName")
  private String groupName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("wbClientCode")
  private String wbClientCode = null;

  public ClientDetails businessUnitClients(List<BusinessUnitClient> businessUnitClients) {
    this.businessUnitClients = businessUnitClients;
    return this;
  }

  public ClientDetails addBusinessUnitClientsItem(BusinessUnitClient businessUnitClientsItem) {
    this.businessUnitClients.add(businessUnitClientsItem);
    return this;
  }

   /**
   * Get businessUnitClients
   * @return businessUnitClients
  **/
  @JsonProperty("businessUnitClients")
  @ApiModelProperty(value = "")
  public List<BusinessUnitClient> getBusinessUnitClients() {
    return businessUnitClients;
  }

  public void setBusinessUnitClients(List<BusinessUnitClient> businessUnitClients) {
    this.businessUnitClients = businessUnitClients;
  }

  public ClientDetails clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public ClientDetails clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public ClientDetails createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ClientDetails createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ClientDetails groupCode(String groupCode) {
    this.groupCode = groupCode;
    return this;
  }

   /**
   * Get groupCode
   * @return groupCode
  **/
  @JsonProperty("groupCode")
  @ApiModelProperty(value = "")
  public String getGroupCode() {
    return groupCode;
  }

  public void setGroupCode(String groupCode) {
    this.groupCode = groupCode;
  }

  public ClientDetails groupName(String groupName) {
    this.groupName = groupName;
    return this;
  }

   /**
   * Get groupName
   * @return groupName
  **/
  @JsonProperty("groupName")
  @ApiModelProperty(value = "")
  public String getGroupName() {
    return groupName;
  }

  public void setGroupName(String groupName) {
    this.groupName = groupName;
  }

  public ClientDetails id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ClientDetails updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ClientDetails updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public ClientDetails wbClientCode(String wbClientCode) {
    this.wbClientCode = wbClientCode;
    return this;
  }

   /**
   * Get wbClientCode
   * @return wbClientCode
  **/
  @JsonProperty("wbClientCode")
  @ApiModelProperty(value = "")
  public String getWbClientCode() {
    return wbClientCode;
  }

  public void setWbClientCode(String wbClientCode) {
    this.wbClientCode = wbClientCode;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ClientDetails clientDetails = (ClientDetails) o;
    return Objects.equals(this.businessUnitClients, clientDetails.businessUnitClients) &&
        Objects.equals(this.clientCode, clientDetails.clientCode) &&
        Objects.equals(this.clientName, clientDetails.clientName) &&
        Objects.equals(this.createdBy, clientDetails.createdBy) &&
        Objects.equals(this.createdDate, clientDetails.createdDate) &&
        Objects.equals(this.groupCode, clientDetails.groupCode) &&
        Objects.equals(this.groupName, clientDetails.groupName) &&
        Objects.equals(this.id, clientDetails.id) &&
        Objects.equals(this.updatedBy, clientDetails.updatedBy) &&
        Objects.equals(this.updatedDate, clientDetails.updatedDate) &&
        Objects.equals(this.wbClientCode, clientDetails.wbClientCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessUnitClients, clientCode, clientName, createdBy, createdDate, groupCode, groupName, id, updatedBy, updatedDate, wbClientCode);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ClientDetails {\n");
    
    sb.append("    businessUnitClients: ").append(toIndentedString(businessUnitClients)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    groupCode: ").append(toIndentedString(groupCode)).append("\n");
    sb.append("    groupName: ").append(toIndentedString(groupName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    wbClientCode: ").append(toIndentedString(wbClientCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

