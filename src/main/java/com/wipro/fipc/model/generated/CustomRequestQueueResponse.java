/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * CustomRequestQueueResponse
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class CustomRequestQueueResponse   {
  @JsonProperty("assignee")
  private String assignee = null;

  @JsonProperty("clientId")
  private String clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("createDateTime")
  private Date createDateTime = null;

  @JsonProperty("createTimestamp")
  private Long createTimestamp = null;

  @JsonProperty("defectId")
  private String defectId = null;

  @JsonProperty("eftDateTime")
  private String eftDateTime = null;

  @JsonProperty("eftStatus")
  private String eftStatus = null;

  @JsonProperty("eft_subj")
  private String eftSubj = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("phase")
  private Integer phase = null;

  @JsonProperty("pluginName")
  private String pluginName = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  @JsonProperty("sla")
  private Integer sla = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("taskId")
  private String taskId = null;

  @JsonProperty("uid")
  private String uid = null;

  public CustomRequestQueueResponse assignee(String assignee) {
    this.assignee = assignee;
    return this;
  }

   /**
   * Get assignee
   * @return assignee
  **/
  @JsonProperty("assignee")
  @ApiModelProperty(value = "")
  public String getAssignee() {
    return assignee;
  }

  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public CustomRequestQueueResponse clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public CustomRequestQueueResponse clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public CustomRequestQueueResponse createDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
    return this;
  }

   /**
   * Get createDateTime
   * @return createDateTime
  **/
  @JsonProperty("createDateTime")
  @ApiModelProperty(value = "")
  public Date getCreateDateTime() {
    return createDateTime;
  }

  public void setCreateDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
  }

  public CustomRequestQueueResponse createTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
    return this;
  }

   /**
   * Get createTimestamp
   * @return createTimestamp
  **/
  @JsonProperty("createTimestamp")
  @ApiModelProperty(value = "")
  public Long getCreateTimestamp() {
    return createTimestamp;
  }

  public void setCreateTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
  }

  public CustomRequestQueueResponse defectId(String defectId) {
    this.defectId = defectId;
    return this;
  }

   /**
   * Get defectId
   * @return defectId
  **/
  @JsonProperty("defectId")
  @ApiModelProperty(value = "")
  public String getDefectId() {
    return defectId;
  }

  public void setDefectId(String defectId) {
    this.defectId = defectId;
  }

  public CustomRequestQueueResponse eftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
    return this;
  }

   /**
   * Get eftDateTime
   * @return eftDateTime
  **/
  @JsonProperty("eftDateTime")
  @ApiModelProperty(value = "")
  public String getEftDateTime() {
    return eftDateTime;
  }

  public void setEftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
  }

  public CustomRequestQueueResponse eftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
    return this;
  }

   /**
   * Get eftStatus
   * @return eftStatus
  **/
  @JsonProperty("eftStatus")
  @ApiModelProperty(value = "")
  public String getEftStatus() {
    return eftStatus;
  }

  public void setEftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
  }

  public CustomRequestQueueResponse eftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
    return this;
  }

   /**
   * Get eftSubj
   * @return eftSubj
  **/
  @JsonProperty("eft_subj")
  @ApiModelProperty(value = "")
  public String getEftSubj() {
    return eftSubj;
  }

  public void setEftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
  }

  public CustomRequestQueueResponse id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public CustomRequestQueueResponse jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public CustomRequestQueueResponse ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public CustomRequestQueueResponse phase(Integer phase) {
    this.phase = phase;
    return this;
  }

   /**
   * Get phase
   * @return phase
  **/
  @JsonProperty("phase")
  @ApiModelProperty(value = "")
  public Integer getPhase() {
    return phase;
  }

  public void setPhase(Integer phase) {
    this.phase = phase;
  }

  public CustomRequestQueueResponse pluginName(String pluginName) {
    this.pluginName = pluginName;
    return this;
  }

   /**
   * Get pluginName
   * @return pluginName
  **/
  @JsonProperty("pluginName")
  @ApiModelProperty(value = "")
  public String getPluginName() {
    return pluginName;
  }

  public void setPluginName(String pluginName) {
    this.pluginName = pluginName;
  }

  public CustomRequestQueueResponse processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public CustomRequestQueueResponse processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public CustomRequestQueueResponse sla(Integer sla) {
    this.sla = sla;
    return this;
  }

   /**
   * Get sla
   * @return sla
  **/
  @JsonProperty("sla")
  @ApiModelProperty(value = "")
  public Integer getSla() {
    return sla;
  }

  public void setSla(Integer sla) {
    this.sla = sla;
  }

  public CustomRequestQueueResponse status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public CustomRequestQueueResponse taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

   /**
   * Get taskId
   * @return taskId
  **/
  @JsonProperty("taskId")
  @ApiModelProperty(value = "")
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public CustomRequestQueueResponse uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomRequestQueueResponse customRequestQueueResponse = (CustomRequestQueueResponse) o;
    return Objects.equals(this.assignee, customRequestQueueResponse.assignee) &&
        Objects.equals(this.clientId, customRequestQueueResponse.clientId) &&
        Objects.equals(this.clientName, customRequestQueueResponse.clientName) &&
        Objects.equals(this.createDateTime, customRequestQueueResponse.createDateTime) &&
        Objects.equals(this.createTimestamp, customRequestQueueResponse.createTimestamp) &&
        Objects.equals(this.defectId, customRequestQueueResponse.defectId) &&
        Objects.equals(this.eftDateTime, customRequestQueueResponse.eftDateTime) &&
        Objects.equals(this.eftStatus, customRequestQueueResponse.eftStatus) &&
        Objects.equals(this.eftSubj, customRequestQueueResponse.eftSubj) &&
        Objects.equals(this.id, customRequestQueueResponse.id) &&
        Objects.equals(this.jobName, customRequestQueueResponse.jobName) &&
        Objects.equals(this.ksdName, customRequestQueueResponse.ksdName) &&
        Objects.equals(this.phase, customRequestQueueResponse.phase) &&
        Objects.equals(this.pluginName, customRequestQueueResponse.pluginName) &&
        Objects.equals(this.processName, customRequestQueueResponse.processName) &&
        Objects.equals(this.processType, customRequestQueueResponse.processType) &&
        Objects.equals(this.sla, customRequestQueueResponse.sla) &&
        Objects.equals(this.status, customRequestQueueResponse.status) &&
        Objects.equals(this.taskId, customRequestQueueResponse.taskId) &&
        Objects.equals(this.uid, customRequestQueueResponse.uid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(assignee, clientId, clientName, createDateTime, createTimestamp, defectId, eftDateTime, eftStatus, eftSubj, id, jobName, ksdName, phase, pluginName, processName, processType, sla, status, taskId, uid);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomRequestQueueResponse {\n");
    
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    createDateTime: ").append(toIndentedString(createDateTime)).append("\n");
    sb.append("    createTimestamp: ").append(toIndentedString(createTimestamp)).append("\n");
    sb.append("    defectId: ").append(toIndentedString(defectId)).append("\n");
    sb.append("    eftDateTime: ").append(toIndentedString(eftDateTime)).append("\n");
    sb.append("    eftStatus: ").append(toIndentedString(eftStatus)).append("\n");
    sb.append("    eftSubj: ").append(toIndentedString(eftSubj)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    phase: ").append(toIndentedString(phase)).append("\n");
    sb.append("    pluginName: ").append(toIndentedString(pluginName)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    sla: ").append(toIndentedString(sla)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

