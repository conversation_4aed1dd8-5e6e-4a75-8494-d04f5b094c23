/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ClientDetail;
import com.wipro.fipc.model.generated.ProcessType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * ClientInformation
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ClientInformation   {
  @JsonProperty("BusinessOpsId")
  private Long businessOpsId = null;

  @JsonProperty("BusinessUnitId")
  private Long businessUnitId = null;

  @JsonProperty("adid")
  private String adid = null;

  @JsonProperty("clientDetails")
  private List<ClientDetail> clientDetails = new ArrayList<ClientDetail>();

  @JsonProperty("opsName")
  private String opsName = null;

  @JsonProperty("processTypes")
  private List<ProcessType> processTypes = new ArrayList<ProcessType>();

  @JsonProperty("role")
  private String role = null;

  @JsonProperty("unitName")
  private String unitName = null;

  public ClientInformation businessOpsId(Long businessOpsId) {
    this.businessOpsId = businessOpsId;
    return this;
  }

   /**
   * Get businessOpsId
   * @return businessOpsId
  **/
  @JsonProperty("BusinessOpsId")
  @ApiModelProperty(value = "")
  public Long getBusinessOpsId() {
    return businessOpsId;
  }

  public void setBusinessOpsId(Long businessOpsId) {
    this.businessOpsId = businessOpsId;
  }

  public ClientInformation businessUnitId(Long businessUnitId) {
    this.businessUnitId = businessUnitId;
    return this;
  }

   /**
   * Get businessUnitId
   * @return businessUnitId
  **/
  @JsonProperty("BusinessUnitId")
  @ApiModelProperty(value = "")
  public Long getBusinessUnitId() {
    return businessUnitId;
  }

  public void setBusinessUnitId(Long businessUnitId) {
    this.businessUnitId = businessUnitId;
  }

  public ClientInformation adid(String adid) {
    this.adid = adid;
    return this;
  }

   /**
   * Get adid
   * @return adid
  **/
  @JsonProperty("adid")
  @ApiModelProperty(value = "")
  public String getAdid() {
    return adid;
  }

  public void setAdid(String adid) {
    this.adid = adid;
  }

  public ClientInformation clientDetails(List<ClientDetail> clientDetails) {
    this.clientDetails = clientDetails;
    return this;
  }

  public ClientInformation addClientDetailsItem(ClientDetail clientDetailsItem) {
    this.clientDetails.add(clientDetailsItem);
    return this;
  }

   /**
   * Get clientDetails
   * @return clientDetails
  **/
  @JsonProperty("clientDetails")
  @ApiModelProperty(value = "")
  public List<ClientDetail> getClientDetails() {
    return clientDetails;
  }

  public void setClientDetails(List<ClientDetail> clientDetails) {
    this.clientDetails = clientDetails;
  }

  public ClientInformation opsName(String opsName) {
    this.opsName = opsName;
    return this;
  }

   /**
   * Get opsName
   * @return opsName
  **/
  @JsonProperty("opsName")
  @ApiModelProperty(value = "")
  public String getOpsName() {
    return opsName;
  }

  public void setOpsName(String opsName) {
    this.opsName = opsName;
  }

  public ClientInformation processTypes(List<ProcessType> processTypes) {
    this.processTypes = processTypes;
    return this;
  }

  public ClientInformation addProcessTypesItem(ProcessType processTypesItem) {
    this.processTypes.add(processTypesItem);
    return this;
  }

   /**
   * Get processTypes
   * @return processTypes
  **/
  @JsonProperty("processTypes")
  @ApiModelProperty(value = "")
  public List<ProcessType> getProcessTypes() {
    return processTypes;
  }

  public void setProcessTypes(List<ProcessType> processTypes) {
    this.processTypes = processTypes;
  }

  public ClientInformation role(String role) {
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public String getRole() {
    return role;
  }

  public void setRole(String role) {
    this.role = role;
  }

  public ClientInformation unitName(String unitName) {
    this.unitName = unitName;
    return this;
  }

   /**
   * Get unitName
   * @return unitName
  **/
  @JsonProperty("unitName")
  @ApiModelProperty(value = "")
  public String getUnitName() {
    return unitName;
  }

  public void setUnitName(String unitName) {
    this.unitName = unitName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ClientInformation clientInformation = (ClientInformation) o;
    return Objects.equals(this.businessOpsId, clientInformation.businessOpsId) &&
        Objects.equals(this.businessUnitId, clientInformation.businessUnitId) &&
        Objects.equals(this.adid, clientInformation.adid) &&
        Objects.equals(this.clientDetails, clientInformation.clientDetails) &&
        Objects.equals(this.opsName, clientInformation.opsName) &&
        Objects.equals(this.processTypes, clientInformation.processTypes) &&
        Objects.equals(this.role, clientInformation.role) &&
        Objects.equals(this.unitName, clientInformation.unitName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessOpsId, businessUnitId, adid, clientDetails, opsName, processTypes, role, unitName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ClientInformation {\n");
    
    sb.append("    businessOpsId: ").append(toIndentedString(businessOpsId)).append("\n");
    sb.append("    businessUnitId: ").append(toIndentedString(businessUnitId)).append("\n");
    sb.append("    adid: ").append(toIndentedString(adid)).append("\n");
    sb.append("    clientDetails: ").append(toIndentedString(clientDetails)).append("\n");
    sb.append("    opsName: ").append(toIndentedString(opsName)).append("\n");
    sb.append("    processTypes: ").append(toIndentedString(processTypes)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    unitName: ").append(toIndentedString(unitName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

