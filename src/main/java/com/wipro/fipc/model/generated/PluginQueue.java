/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import javax.validation.constraints.*;

/**
 * PluginQueue
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class PluginQueue   {
  @JsonProperty("allocateDateTime")
  private Timestamp allocateDateTime = null;

  @JsonProperty("allocateTimestamp")
  private Long allocateTimestamp = null;

  @JsonProperty("botRole")
  private String botRole = null;

  @JsonProperty("botServer")
  private String botServer = null;

  @JsonProperty("createDateTime")
  private Timestamp createDateTime = null;

  @JsonProperty("createTimestamp")
  private Long createTimestamp = null;

  @JsonProperty("endDateTime")
  private Timestamp endDateTime = null;

  @JsonProperty("endTimestamp")
  private Long endTimestamp = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("parentPhase")
  private Integer parentPhase = null;

  @JsonProperty("phase")
  private Integer phase = null;

  @JsonProperty("plugin")
  private Integer plugin = null;

  @JsonProperty("pluginBean")
  private String pluginBean = null;

  @JsonProperty("request")
  private String request = null;

  @JsonProperty("response")
  private String response = null;

  @JsonProperty("source")
  private String source = null;

  @JsonProperty("startDateTime")
  private Timestamp startDateTime = null;

  @JsonProperty("startTimestamp")
  private Long startTimestamp = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("statusMessage")
  private String statusMessage = null;

  @JsonProperty("uid")
  private String uid = null;

  @JsonProperty("userName")
  private String userName = null;

  public PluginQueue allocateDateTime(Timestamp allocateDateTime) {
    this.allocateDateTime = allocateDateTime;
    return this;
  }

   /**
   * Get allocateDateTime
   * @return allocateDateTime
  **/
  @JsonProperty("allocateDateTime")
  @ApiModelProperty(value = "")
  public Timestamp getAllocateDateTime() {
    return allocateDateTime;
  }

  public void setAllocateDateTime(Timestamp allocateDateTime) {
    this.allocateDateTime = allocateDateTime;
  }

  public PluginQueue allocateTimestamp(Long allocateTimestamp) {
    this.allocateTimestamp = allocateTimestamp;
    return this;
  }

   /**
   * Get allocateTimestamp
   * @return allocateTimestamp
  **/
  @JsonProperty("allocateTimestamp")
  @ApiModelProperty(value = "")
  public Long getAllocateTimestamp() {
    return allocateTimestamp;
  }

  public void setAllocateTimestamp(Long allocateTimestamp) {
    this.allocateTimestamp = allocateTimestamp;
  }

  public PluginQueue botRole(String botRole) {
    this.botRole = botRole;
    return this;
  }

   /**
   * Get botRole
   * @return botRole
  **/
  @JsonProperty("botRole")
  @ApiModelProperty(value = "")
  public String getBotRole() {
    return botRole;
  }

  public void setBotRole(String botRole) {
    this.botRole = botRole;
  }

  public PluginQueue botServer(String botServer) {
    this.botServer = botServer;
    return this;
  }

   /**
   * Get botServer
   * @return botServer
  **/
  @JsonProperty("botServer")
  @ApiModelProperty(value = "")
  public String getBotServer() {
    return botServer;
  }

  public void setBotServer(String botServer) {
    this.botServer = botServer;
  }

  public PluginQueue createDateTime(Timestamp createDateTime) {
    this.createDateTime = createDateTime;
    return this;
  }

   /**
   * Get createDateTime
   * @return createDateTime
  **/
  @JsonProperty("createDateTime")
  @ApiModelProperty(value = "")
  public Timestamp getCreateDateTime() {
    return createDateTime;
  }

  public void setCreateDateTime(Timestamp createDateTime) {
    this.createDateTime = createDateTime;
  }

  public PluginQueue createTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
    return this;
  }

   /**
   * Get createTimestamp
   * @return createTimestamp
  **/
  @JsonProperty("createTimestamp")
  @ApiModelProperty(value = "")
  public Long getCreateTimestamp() {
    return createTimestamp;
  }

  public void setCreateTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
  }

  public PluginQueue endDateTime(Timestamp endDateTime) {
    this.endDateTime = endDateTime;
    return this;
  }

   /**
   * Get endDateTime
   * @return endDateTime
  **/
  @JsonProperty("endDateTime")
  @ApiModelProperty(value = "")
  public Timestamp getEndDateTime() {
    return endDateTime;
  }

  public void setEndDateTime(Timestamp endDateTime) {
    this.endDateTime = endDateTime;
  }

  public PluginQueue endTimestamp(Long endTimestamp) {
    this.endTimestamp = endTimestamp;
    return this;
  }

   /**
   * Get endTimestamp
   * @return endTimestamp
  **/
  @JsonProperty("endTimestamp")
  @ApiModelProperty(value = "")
  public Long getEndTimestamp() {
    return endTimestamp;
  }

  public void setEndTimestamp(Long endTimestamp) {
    this.endTimestamp = endTimestamp;
  }

  public PluginQueue id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public PluginQueue parentPhase(Integer parentPhase) {
    this.parentPhase = parentPhase;
    return this;
  }

   /**
   * Get parentPhase
   * @return parentPhase
  **/
  @JsonProperty("parentPhase")
  @ApiModelProperty(value = "")
  public Integer getParentPhase() {
    return parentPhase;
  }

  public void setParentPhase(Integer parentPhase) {
    this.parentPhase = parentPhase;
  }

  public PluginQueue phase(Integer phase) {
    this.phase = phase;
    return this;
  }

   /**
   * Get phase
   * @return phase
  **/
  @JsonProperty("phase")
  @ApiModelProperty(value = "")
  public Integer getPhase() {
    return phase;
  }

  public void setPhase(Integer phase) {
    this.phase = phase;
  }

  public PluginQueue plugin(Integer plugin) {
    this.plugin = plugin;
    return this;
  }

   /**
   * Get plugin
   * @return plugin
  **/
  @JsonProperty("plugin")
  @ApiModelProperty(value = "")
  public Integer getPlugin() {
    return plugin;
  }

  public void setPlugin(Integer plugin) {
    this.plugin = plugin;
  }

  public PluginQueue pluginBean(String pluginBean) {
    this.pluginBean = pluginBean;
    return this;
  }

   /**
   * Get pluginBean
   * @return pluginBean
  **/
  @JsonProperty("pluginBean")
  @ApiModelProperty(value = "")
  public String getPluginBean() {
    return pluginBean;
  }

  public void setPluginBean(String pluginBean) {
    this.pluginBean = pluginBean;
  }

  public PluginQueue request(String request) {
    this.request = request;
    return this;
  }

   /**
   * Get request
   * @return request
  **/
  @JsonProperty("request")
  @ApiModelProperty(value = "")
  public String getRequest() {
    return request;
  }

  public void setRequest(String request) {
    this.request = request;
  }

  public PluginQueue response(String response) {
    this.response = response;
    return this;
  }

   /**
   * Get response
   * @return response
  **/
  @JsonProperty("response")
  @ApiModelProperty(value = "")
  public String getResponse() {
    return response;
  }

  public void setResponse(String response) {
    this.response = response;
  }

  public PluginQueue source(String source) {
    this.source = source;
    return this;
  }

   /**
   * Get source
   * @return source
  **/
  @JsonProperty("source")
  @ApiModelProperty(value = "")
  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public PluginQueue startDateTime(Timestamp startDateTime) {
    this.startDateTime = startDateTime;
    return this;
  }

   /**
   * Get startDateTime
   * @return startDateTime
  **/
  @JsonProperty("startDateTime")
  @ApiModelProperty(value = "")
  public Timestamp getStartDateTime() {
    return startDateTime;
  }

  public void setStartDateTime(Timestamp startDateTime) {
    this.startDateTime = startDateTime;
  }

  public PluginQueue startTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
    return this;
  }

   /**
   * Get startTimestamp
   * @return startTimestamp
  **/
  @JsonProperty("startTimestamp")
  @ApiModelProperty(value = "")
  public Long getStartTimestamp() {
    return startTimestamp;
  }

  public void setStartTimestamp(Long startTimestamp) {
    this.startTimestamp = startTimestamp;
  }

  public PluginQueue status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public PluginQueue statusMessage(String statusMessage) {
    this.statusMessage = statusMessage;
    return this;
  }

   /**
   * Get statusMessage
   * @return statusMessage
  **/
  @JsonProperty("statusMessage")
  @ApiModelProperty(value = "")
  public String getStatusMessage() {
    return statusMessage;
  }

  public void setStatusMessage(String statusMessage) {
    this.statusMessage = statusMessage;
  }

  public PluginQueue uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public PluginQueue userName(String userName) {
    this.userName = userName;
    return this;
  }

   /**
   * Get userName
   * @return userName
  **/
  @JsonProperty("userName")
  @ApiModelProperty(value = "")
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PluginQueue pluginQueue = (PluginQueue) o;
    return Objects.equals(this.allocateDateTime, pluginQueue.allocateDateTime) &&
        Objects.equals(this.allocateTimestamp, pluginQueue.allocateTimestamp) &&
        Objects.equals(this.botRole, pluginQueue.botRole) &&
        Objects.equals(this.botServer, pluginQueue.botServer) &&
        Objects.equals(this.createDateTime, pluginQueue.createDateTime) &&
        Objects.equals(this.createTimestamp, pluginQueue.createTimestamp) &&
        Objects.equals(this.endDateTime, pluginQueue.endDateTime) &&
        Objects.equals(this.endTimestamp, pluginQueue.endTimestamp) &&
        Objects.equals(this.id, pluginQueue.id) &&
        Objects.equals(this.parentPhase, pluginQueue.parentPhase) &&
        Objects.equals(this.phase, pluginQueue.phase) &&
        Objects.equals(this.plugin, pluginQueue.plugin) &&
        Objects.equals(this.pluginBean, pluginQueue.pluginBean) &&
        Objects.equals(this.request, pluginQueue.request) &&
        Objects.equals(this.response, pluginQueue.response) &&
        Objects.equals(this.source, pluginQueue.source) &&
        Objects.equals(this.startDateTime, pluginQueue.startDateTime) &&
        Objects.equals(this.startTimestamp, pluginQueue.startTimestamp) &&
        Objects.equals(this.status, pluginQueue.status) &&
        Objects.equals(this.statusMessage, pluginQueue.statusMessage) &&
        Objects.equals(this.uid, pluginQueue.uid) &&
        Objects.equals(this.userName, pluginQueue.userName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(allocateDateTime, allocateTimestamp, botRole, botServer, createDateTime, createTimestamp, endDateTime, endTimestamp, id, parentPhase, phase, plugin, pluginBean, request, response, source, startDateTime, startTimestamp, status, statusMessage, uid, userName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PluginQueue {\n");
    
    sb.append("    allocateDateTime: ").append(toIndentedString(allocateDateTime)).append("\n");
    sb.append("    allocateTimestamp: ").append(toIndentedString(allocateTimestamp)).append("\n");
    sb.append("    botRole: ").append(toIndentedString(botRole)).append("\n");
    sb.append("    botServer: ").append(toIndentedString(botServer)).append("\n");
    sb.append("    createDateTime: ").append(toIndentedString(createDateTime)).append("\n");
    sb.append("    createTimestamp: ").append(toIndentedString(createTimestamp)).append("\n");
    sb.append("    endDateTime: ").append(toIndentedString(endDateTime)).append("\n");
    sb.append("    endTimestamp: ").append(toIndentedString(endTimestamp)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    parentPhase: ").append(toIndentedString(parentPhase)).append("\n");
    sb.append("    phase: ").append(toIndentedString(phase)).append("\n");
    sb.append("    plugin: ").append(toIndentedString(plugin)).append("\n");
    sb.append("    pluginBean: ").append(toIndentedString(pluginBean)).append("\n");
    sb.append("    request: ").append(toIndentedString(request)).append("\n");
    sb.append("    response: ").append(toIndentedString(response)).append("\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    startDateTime: ").append(toIndentedString(startDateTime)).append("\n");
    sb.append("    startTimestamp: ").append(toIndentedString(startTimestamp)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    statusMessage: ").append(toIndentedString(statusMessage)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

