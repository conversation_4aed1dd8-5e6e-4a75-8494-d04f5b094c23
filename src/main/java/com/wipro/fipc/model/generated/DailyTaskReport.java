/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * DailyTaskReport
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class DailyTaskReport   {
  @JsonProperty("adxScriptId")
  private String adxScriptId = null;

  @JsonProperty("businessUnit")
  private String businessUnit = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("endTimeInReport")
  private String endTimeInReport = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobCutOffTime")
  private String jobCutOffTime = null;

  @JsonProperty("jobFailureId")
  private String jobFailureId = null;

  @JsonProperty("jobNameInReport")
  private String jobNameInReport = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("startTimeInReport")
  private String startTimeInReport = null;

  @JsonProperty("statusInReport")
  private String statusInReport = null;

  @JsonProperty("titleOfDailyTaskReport")
  private String titleOfDailyTaskReport = null;

  @JsonProperty("uid")
  private String uid = null;

  public DailyTaskReport adxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
    return this;
  }

   /**
   * Get adxScriptId
   * @return adxScriptId
  **/
  @JsonProperty("adxScriptId")
  @ApiModelProperty(value = "")
  public String getAdxScriptId() {
    return adxScriptId;
  }

  public void setAdxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
  }

  public DailyTaskReport businessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public String getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
  }

  public DailyTaskReport createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public DailyTaskReport endTimeInReport(String endTimeInReport) {
    this.endTimeInReport = endTimeInReport;
    return this;
  }

   /**
   * Get endTimeInReport
   * @return endTimeInReport
  **/
  @JsonProperty("endTimeInReport")
  @ApiModelProperty(value = "")
  public String getEndTimeInReport() {
    return endTimeInReport;
  }

  public void setEndTimeInReport(String endTimeInReport) {
    this.endTimeInReport = endTimeInReport;
  }

  public DailyTaskReport id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public DailyTaskReport jobCutOffTime(String jobCutOffTime) {
    this.jobCutOffTime = jobCutOffTime;
    return this;
  }

   /**
   * Get jobCutOffTime
   * @return jobCutOffTime
  **/
  @JsonProperty("jobCutOffTime")
  @ApiModelProperty(value = "")
  public String getJobCutOffTime() {
    return jobCutOffTime;
  }

  public void setJobCutOffTime(String jobCutOffTime) {
    this.jobCutOffTime = jobCutOffTime;
  }

  public DailyTaskReport jobFailureId(String jobFailureId) {
    this.jobFailureId = jobFailureId;
    return this;
  }

   /**
   * Get jobFailureId
   * @return jobFailureId
  **/
  @JsonProperty("jobFailureId")
  @ApiModelProperty(value = "")
  public String getJobFailureId() {
    return jobFailureId;
  }

  public void setJobFailureId(String jobFailureId) {
    this.jobFailureId = jobFailureId;
  }

  public DailyTaskReport jobNameInReport(String jobNameInReport) {
    this.jobNameInReport = jobNameInReport;
    return this;
  }

   /**
   * Get jobNameInReport
   * @return jobNameInReport
  **/
  @JsonProperty("jobNameInReport")
  @ApiModelProperty(value = "")
  public String getJobNameInReport() {
    return jobNameInReport;
  }

  public void setJobNameInReport(String jobNameInReport) {
    this.jobNameInReport = jobNameInReport;
  }

  public DailyTaskReport processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public DailyTaskReport startTimeInReport(String startTimeInReport) {
    this.startTimeInReport = startTimeInReport;
    return this;
  }

   /**
   * Get startTimeInReport
   * @return startTimeInReport
  **/
  @JsonProperty("startTimeInReport")
  @ApiModelProperty(value = "")
  public String getStartTimeInReport() {
    return startTimeInReport;
  }

  public void setStartTimeInReport(String startTimeInReport) {
    this.startTimeInReport = startTimeInReport;
  }

  public DailyTaskReport statusInReport(String statusInReport) {
    this.statusInReport = statusInReport;
    return this;
  }

   /**
   * Get statusInReport
   * @return statusInReport
  **/
  @JsonProperty("statusInReport")
  @ApiModelProperty(value = "")
  public String getStatusInReport() {
    return statusInReport;
  }

  public void setStatusInReport(String statusInReport) {
    this.statusInReport = statusInReport;
  }

  public DailyTaskReport titleOfDailyTaskReport(String titleOfDailyTaskReport) {
    this.titleOfDailyTaskReport = titleOfDailyTaskReport;
    return this;
  }

   /**
   * Get titleOfDailyTaskReport
   * @return titleOfDailyTaskReport
  **/
  @JsonProperty("titleOfDailyTaskReport")
  @ApiModelProperty(value = "")
  public String getTitleOfDailyTaskReport() {
    return titleOfDailyTaskReport;
  }

  public void setTitleOfDailyTaskReport(String titleOfDailyTaskReport) {
    this.titleOfDailyTaskReport = titleOfDailyTaskReport;
  }

  public DailyTaskReport uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DailyTaskReport dailyTaskReport = (DailyTaskReport) o;
    return Objects.equals(this.adxScriptId, dailyTaskReport.adxScriptId) &&
        Objects.equals(this.businessUnit, dailyTaskReport.businessUnit) &&
        Objects.equals(this.createdDate, dailyTaskReport.createdDate) &&
        Objects.equals(this.endTimeInReport, dailyTaskReport.endTimeInReport) &&
        Objects.equals(this.id, dailyTaskReport.id) &&
        Objects.equals(this.jobCutOffTime, dailyTaskReport.jobCutOffTime) &&
        Objects.equals(this.jobFailureId, dailyTaskReport.jobFailureId) &&
        Objects.equals(this.jobNameInReport, dailyTaskReport.jobNameInReport) &&
        Objects.equals(this.processJobMappingId, dailyTaskReport.processJobMappingId) &&
        Objects.equals(this.startTimeInReport, dailyTaskReport.startTimeInReport) &&
        Objects.equals(this.statusInReport, dailyTaskReport.statusInReport) &&
        Objects.equals(this.titleOfDailyTaskReport, dailyTaskReport.titleOfDailyTaskReport) &&
        Objects.equals(this.uid, dailyTaskReport.uid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(adxScriptId, businessUnit, createdDate, endTimeInReport, id, jobCutOffTime, jobFailureId, jobNameInReport, processJobMappingId, startTimeInReport, statusInReport, titleOfDailyTaskReport, uid);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DailyTaskReport {\n");
    
    sb.append("    adxScriptId: ").append(toIndentedString(adxScriptId)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    endTimeInReport: ").append(toIndentedString(endTimeInReport)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobCutOffTime: ").append(toIndentedString(jobCutOffTime)).append("\n");
    sb.append("    jobFailureId: ").append(toIndentedString(jobFailureId)).append("\n");
    sb.append("    jobNameInReport: ").append(toIndentedString(jobNameInReport)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    startTimeInReport: ").append(toIndentedString(startTimeInReport)).append("\n");
    sb.append("    statusInReport: ").append(toIndentedString(statusInReport)).append("\n");
    sb.append("    titleOfDailyTaskReport: ").append(toIndentedString(titleOfDailyTaskReport)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

