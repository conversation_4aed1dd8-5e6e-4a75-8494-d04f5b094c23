/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.BusinessUnitOps;
import com.wipro.fipc.model.generated.ClientDetails;
import com.wipro.fipc.model.generated.Process;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ProcessJobMapping
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ProcessJobMapping   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("businessUnitOps")
  private BusinessUnitOps businessUnitOps = null;

  @JsonProperty("clientDetails")
  private ClientDetails clientDetails = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eftSubject")
  private String eftSubject = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("process")
  private Process process = null;

  @JsonProperty("tower")
  private String tower = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public ProcessJobMapping activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ProcessJobMapping businessUnitOps(BusinessUnitOps businessUnitOps) {
    this.businessUnitOps = businessUnitOps;
    return this;
  }

   /**
   * Get businessUnitOps
   * @return businessUnitOps
  **/
  @JsonProperty("businessUnitOps")
  @ApiModelProperty(value = "")
  public BusinessUnitOps getBusinessUnitOps() {
    return businessUnitOps;
  }

  public void setBusinessUnitOps(BusinessUnitOps businessUnitOps) {
    this.businessUnitOps = businessUnitOps;
  }

  public ProcessJobMapping clientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
    return this;
  }

   /**
   * Get clientDetails
   * @return clientDetails
  **/
  @JsonProperty("clientDetails")
  @ApiModelProperty(value = "")
  public ClientDetails getClientDetails() {
    return clientDetails;
  }

  public void setClientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
  }

  public ProcessJobMapping createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ProcessJobMapping createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ProcessJobMapping eftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
    return this;
  }

   /**
   * Get eftSubject
   * @return eftSubject
  **/
  @JsonProperty("eftSubject")
  @ApiModelProperty(value = "")
  public String getEftSubject() {
    return eftSubject;
  }

  public void setEftSubject(String eftSubject) {
    this.eftSubject = eftSubject;
  }

  public ProcessJobMapping id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ProcessJobMapping jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public ProcessJobMapping ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public ProcessJobMapping process(Process process) {
    this.process = process;
    return this;
  }

   /**
   * Get process
   * @return process
  **/
  @JsonProperty("process")
  @ApiModelProperty(value = "")
  public Process getProcess() {
    return process;
  }

  public void setProcess(Process process) {
    this.process = process;
  }

  public ProcessJobMapping tower(String tower) {
    this.tower = tower;
    return this;
  }

   /**
   * Get tower
   * @return tower
  **/
  @JsonProperty("tower")
  @ApiModelProperty(value = "")
  public String getTower() {
    return tower;
  }

  public void setTower(String tower) {
    this.tower = tower;
  }

  public ProcessJobMapping updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ProcessJobMapping updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessJobMapping processJobMapping = (ProcessJobMapping) o;
    return Objects.equals(this.activeFlag, processJobMapping.activeFlag) &&
        Objects.equals(this.businessUnitOps, processJobMapping.businessUnitOps) &&
        Objects.equals(this.clientDetails, processJobMapping.clientDetails) &&
        Objects.equals(this.createdBy, processJobMapping.createdBy) &&
        Objects.equals(this.createdDate, processJobMapping.createdDate) &&
        Objects.equals(this.eftSubject, processJobMapping.eftSubject) &&
        Objects.equals(this.id, processJobMapping.id) &&
        Objects.equals(this.jobName, processJobMapping.jobName) &&
        Objects.equals(this.ksdName, processJobMapping.ksdName) &&
        Objects.equals(this.process, processJobMapping.process) &&
        Objects.equals(this.tower, processJobMapping.tower) &&
        Objects.equals(this.updatedBy, processJobMapping.updatedBy) &&
        Objects.equals(this.updatedDate, processJobMapping.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, businessUnitOps, clientDetails, createdBy, createdDate, eftSubject, id, jobName, ksdName, process, tower, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessJobMapping {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    businessUnitOps: ").append(toIndentedString(businessUnitOps)).append("\n");
    sb.append("    clientDetails: ").append(toIndentedString(clientDetails)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eftSubject: ").append(toIndentedString(eftSubject)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    process: ").append(toIndentedString(process)).append("\n");
    sb.append("    tower: ").append(toIndentedString(tower)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

