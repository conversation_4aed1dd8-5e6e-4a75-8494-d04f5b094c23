/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaMetaData
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaMetaData   {
  @JsonProperty("clientId")
  private Integer clientId = null;

  @JsonProperty("column_value")
  private String columnValue = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("metaData")
  private String metaData = null;

  @JsonProperty("panelId")
  private Integer panelId = null;

  @JsonProperty("row_value")
  private String rowValue = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaMetaData clientId(Integer clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Integer getClientId() {
    return clientId;
  }

  public void setClientId(Integer clientId) {
    this.clientId = clientId;
  }

  public TbaMetaData columnValue(String columnValue) {
    this.columnValue = columnValue;
    return this;
  }

   /**
   * Get columnValue
   * @return columnValue
  **/
  @JsonProperty("column_value")
  @ApiModelProperty(value = "")
  public String getColumnValue() {
    return columnValue;
  }

  public void setColumnValue(String columnValue) {
    this.columnValue = columnValue;
  }

  public TbaMetaData createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaMetaData createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaMetaData id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaMetaData metaData(String metaData) {
    this.metaData = metaData;
    return this;
  }

   /**
   * Get metaData
   * @return metaData
  **/
  @JsonProperty("metaData")
  @ApiModelProperty(value = "")
  public String getMetaData() {
    return metaData;
  }

  public void setMetaData(String metaData) {
    this.metaData = metaData;
  }

  public TbaMetaData panelId(Integer panelId) {
    this.panelId = panelId;
    return this;
  }

   /**
   * Get panelId
   * @return panelId
  **/
  @JsonProperty("panelId")
  @ApiModelProperty(value = "")
  public Integer getPanelId() {
    return panelId;
  }

  public void setPanelId(Integer panelId) {
    this.panelId = panelId;
  }

  public TbaMetaData rowValue(String rowValue) {
    this.rowValue = rowValue;
    return this;
  }

   /**
   * Get rowValue
   * @return rowValue
  **/
  @JsonProperty("row_value")
  @ApiModelProperty(value = "")
  public String getRowValue() {
    return rowValue;
  }

  public void setRowValue(String rowValue) {
    this.rowValue = rowValue;
  }

  public TbaMetaData updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaMetaData updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaMetaData tbaMetaData = (TbaMetaData) o;
    return Objects.equals(this.clientId, tbaMetaData.clientId) &&
        Objects.equals(this.columnValue, tbaMetaData.columnValue) &&
        Objects.equals(this.createdBy, tbaMetaData.createdBy) &&
        Objects.equals(this.createdDate, tbaMetaData.createdDate) &&
        Objects.equals(this.id, tbaMetaData.id) &&
        Objects.equals(this.metaData, tbaMetaData.metaData) &&
        Objects.equals(this.panelId, tbaMetaData.panelId) &&
        Objects.equals(this.rowValue, tbaMetaData.rowValue) &&
        Objects.equals(this.updatedBy, tbaMetaData.updatedBy) &&
        Objects.equals(this.updatedDate, tbaMetaData.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(clientId, columnValue, createdBy, createdDate, id, metaData, panelId, rowValue, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaMetaData {\n");
    
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    columnValue: ").append(toIndentedString(columnValue)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    metaData: ").append(toIndentedString(metaData)).append("\n");
    sb.append("    panelId: ").append(toIndentedString(panelId)).append("\n");
    sb.append("    rowValue: ").append(toIndentedString(rowValue)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

