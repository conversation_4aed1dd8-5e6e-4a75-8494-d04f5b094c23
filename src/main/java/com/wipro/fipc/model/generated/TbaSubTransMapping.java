/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * TbaSubTransMapping
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2024-12-19T18:46:06.190+05:30")
public class TbaSubTransMapping   {
  @JsonProperty("subTransCode")
  private String subTransCode = null;

  @JsonProperty("subTransCodeDesc")
  private String subTransCodeDesc = null;

  public TbaSubTransMapping subTransCode(String subTransCode) {
    this.subTransCode = subTransCode;
    return this;
  }

   /**
   * Get subTransCode
   * @return subTransCode
  **/
  @JsonProperty("subTransCode")
  @ApiModelProperty(value = "")
  public String getSubTransCode() {
    return subTransCode;
  }

  public void setSubTransCode(String subTransCode) {
    this.subTransCode = subTransCode;
  }

  public TbaSubTransMapping subTransCodeDesc(String subTransCodeDesc) {
    this.subTransCodeDesc = subTransCodeDesc;
    return this;
  }

   /**
   * Get subTransCodeDesc
   * @return subTransCodeDesc
  **/
  @JsonProperty("subTransCodeDesc")
  @ApiModelProperty(value = "")
  public String getSubTransCodeDesc() {
    return subTransCodeDesc;
  }

  public void setSubTransCodeDesc(String subTransCodeDesc) {
    this.subTransCodeDesc = subTransCodeDesc;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaSubTransMapping tbaSubTransMapping = (TbaSubTransMapping) o;
    return Objects.equals(this.subTransCode, tbaSubTransMapping.subTransCode) &&
        Objects.equals(this.subTransCodeDesc, tbaSubTransMapping.subTransCodeDesc);
  }

  @Override
  public int hashCode() {
    return Objects.hash(subTransCode, subTransCodeDesc);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaSubTransMapping {\n");
    
    sb.append("    subTransCode: ").append(toIndentedString(subTransCode)).append("\n");
    sb.append("    subTransCodeDesc: ").append(toIndentedString(subTransCodeDesc)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

