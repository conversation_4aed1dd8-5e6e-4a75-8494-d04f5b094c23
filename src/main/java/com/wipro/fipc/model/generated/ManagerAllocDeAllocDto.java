/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.AnalystDetails;
import com.wipro.fipc.model.generated.ClientDetailsDto;
import com.wipro.fipc.model.generated.RequestQueue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.*;

/**
 * ManagerAllocDeAllocDto
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class ManagerAllocDeAllocDto   {
  @JsonProperty("analystList")
  private List<AnalystDetails> analystList = new ArrayList<AnalystDetails>();

  @JsonProperty("clientDetailsList")
  private List<ClientDetailsDto> clientDetailsList = new ArrayList<ClientDetailsDto>();

  @JsonProperty("requestQueueList")
  private List<RequestQueue> requestQueueList = new ArrayList<RequestQueue>();

  public ManagerAllocDeAllocDto analystList(List<AnalystDetails> analystList) {
    this.analystList = analystList;
    return this;
  }

  public ManagerAllocDeAllocDto addAnalystListItem(AnalystDetails analystListItem) {
    this.analystList.add(analystListItem);
    return this;
  }

   /**
   * Get analystList
   * @return analystList
  **/
  @JsonProperty("analystList")
  @ApiModelProperty(value = "")
  public List<AnalystDetails> getAnalystList() {
    return analystList;
  }

  public void setAnalystList(List<AnalystDetails> analystList) {
    this.analystList = analystList;
  }

  public ManagerAllocDeAllocDto clientDetailsList(List<ClientDetailsDto> clientDetailsList) {
    this.clientDetailsList = clientDetailsList;
    return this;
  }

  public ManagerAllocDeAllocDto addClientDetailsListItem(ClientDetailsDto clientDetailsListItem) {
    this.clientDetailsList.add(clientDetailsListItem);
    return this;
  }

   /**
   * Get clientDetailsList
   * @return clientDetailsList
  **/
  @JsonProperty("clientDetailsList")
  @ApiModelProperty(value = "")
  public List<ClientDetailsDto> getClientDetailsList() {
    return clientDetailsList;
  }

  public void setClientDetailsList(List<ClientDetailsDto> clientDetailsList) {
    this.clientDetailsList = clientDetailsList;
  }

  public ManagerAllocDeAllocDto requestQueueList(List<RequestQueue> requestQueueList) {
    this.requestQueueList = requestQueueList;
    return this;
  }

  public ManagerAllocDeAllocDto addRequestQueueListItem(RequestQueue requestQueueListItem) {
    this.requestQueueList.add(requestQueueListItem);
    return this;
  }

   /**
   * Get requestQueueList
   * @return requestQueueList
  **/
  @JsonProperty("requestQueueList")
  @ApiModelProperty(value = "")
  public List<RequestQueue> getRequestQueueList() {
    return requestQueueList;
  }

  public void setRequestQueueList(List<RequestQueue> requestQueueList) {
    this.requestQueueList = requestQueueList;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ManagerAllocDeAllocDto managerAllocDeAllocDto = (ManagerAllocDeAllocDto) o;
    return Objects.equals(this.analystList, managerAllocDeAllocDto.analystList) &&
        Objects.equals(this.clientDetailsList, managerAllocDeAllocDto.clientDetailsList) &&
        Objects.equals(this.requestQueueList, managerAllocDeAllocDto.requestQueueList);
  }

  @Override
  public int hashCode() {
    return Objects.hash(analystList, clientDetailsList, requestQueueList);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ManagerAllocDeAllocDto {\n");
    
    sb.append("    analystList: ").append(toIndentedString(analystList)).append("\n");
    sb.append("    clientDetailsList: ").append(toIndentedString(clientDetailsList)).append("\n");
    sb.append("    requestQueueList: ").append(toIndentedString(requestQueueList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

