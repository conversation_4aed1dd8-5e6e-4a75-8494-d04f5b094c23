/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * PreRequestQueue
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class PreRequestQueue   {
  @JsonProperty("adxScriptId")
  private String adxScriptId = null;

  @JsonProperty("batchData")
  private String batchData = null;

  @JsonProperty("batchStatus")
  private String batchStatus = null;

  @JsonProperty("batchUid")
  private String batchUid = null;

  @JsonProperty("businessOps")
  private String businessOps = null;

  @JsonProperty("businessUnit")
  private String businessUnit = null;

  @JsonProperty("clientId")
  private String clientId = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("configJson")
  private String configJson = null;

  @JsonProperty("createDateTime")
  private Date createDateTime = null;

  @JsonProperty("createTimestamp")
  private Long createTimestamp = null;

  @JsonProperty("defectId")
  private String defectId = null;

  @JsonProperty("eftDateTime")
  private String eftDateTime = null;

  @JsonProperty("eftStatus")
  private String eftStatus = null;

  @JsonProperty("eft_subj")
  private String eftSubj = null;

  @JsonProperty("frequency")
  private String frequency = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("isBypass")
  private Boolean isBypass = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("ksdName")
  private String ksdName = null;

  @JsonProperty("pjmId")
  private Long pjmId = null;

  @JsonProperty("processName")
  private String processName = null;

  @JsonProperty("processType")
  private String processType = null;

  @JsonProperty("sla")
  private Integer sla = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("taskId")
  private String taskId = null;

  @JsonProperty("uid")
  private String uid = null;

  @JsonProperty("userName")
  private String userName = null;

  public PreRequestQueue adxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
    return this;
  }

   /**
   * Get adxScriptId
   * @return adxScriptId
  **/
  @JsonProperty("adxScriptId")
  @ApiModelProperty(value = "")
  public String getAdxScriptId() {
    return adxScriptId;
  }

  public void setAdxScriptId(String adxScriptId) {
    this.adxScriptId = adxScriptId;
  }

  public PreRequestQueue batchData(String batchData) {
    this.batchData = batchData;
    return this;
  }

   /**
   * Get batchData
   * @return batchData
  **/
  @JsonProperty("batchData")
  @ApiModelProperty(value = "")
  public String getBatchData() {
    return batchData;
  }

  public void setBatchData(String batchData) {
    this.batchData = batchData;
  }

  public PreRequestQueue batchStatus(String batchStatus) {
    this.batchStatus = batchStatus;
    return this;
  }

   /**
   * Get batchStatus
   * @return batchStatus
  **/
  @JsonProperty("batchStatus")
  @ApiModelProperty(value = "")
  public String getBatchStatus() {
    return batchStatus;
  }

  public void setBatchStatus(String batchStatus) {
    this.batchStatus = batchStatus;
  }

  public PreRequestQueue batchUid(String batchUid) {
    this.batchUid = batchUid;
    return this;
  }

   /**
   * Get batchUid
   * @return batchUid
  **/
  @JsonProperty("batchUid")
  @ApiModelProperty(value = "")
  public String getBatchUid() {
    return batchUid;
  }

  public void setBatchUid(String batchUid) {
    this.batchUid = batchUid;
  }

  public PreRequestQueue businessOps(String businessOps) {
    this.businessOps = businessOps;
    return this;
  }

   /**
   * Get businessOps
   * @return businessOps
  **/
  @JsonProperty("businessOps")
  @ApiModelProperty(value = "")
  public String getBusinessOps() {
    return businessOps;
  }

  public void setBusinessOps(String businessOps) {
    this.businessOps = businessOps;
  }

  public PreRequestQueue businessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public String getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
  }

  public PreRequestQueue clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public PreRequestQueue clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public PreRequestQueue configJson(String configJson) {
    this.configJson = configJson;
    return this;
  }

   /**
   * Get configJson
   * @return configJson
  **/
  @JsonProperty("configJson")
  @ApiModelProperty(value = "")
  public String getConfigJson() {
    return configJson;
  }

  public void setConfigJson(String configJson) {
    this.configJson = configJson;
  }

  public PreRequestQueue createDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
    return this;
  }

   /**
   * Get createDateTime
   * @return createDateTime
  **/
  @JsonProperty("createDateTime")
  @ApiModelProperty(value = "")
  public Date getCreateDateTime() {
    return createDateTime;
  }

  public void setCreateDateTime(Date createDateTime) {
    this.createDateTime = createDateTime;
  }

  public PreRequestQueue createTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
    return this;
  }

   /**
   * Get createTimestamp
   * @return createTimestamp
  **/
  @JsonProperty("createTimestamp")
  @ApiModelProperty(value = "")
  public Long getCreateTimestamp() {
    return createTimestamp;
  }

  public void setCreateTimestamp(Long createTimestamp) {
    this.createTimestamp = createTimestamp;
  }

  public PreRequestQueue defectId(String defectId) {
    this.defectId = defectId;
    return this;
  }

   /**
   * Get defectId
   * @return defectId
  **/
  @JsonProperty("defectId")
  @ApiModelProperty(value = "")
  public String getDefectId() {
    return defectId;
  }

  public void setDefectId(String defectId) {
    this.defectId = defectId;
  }

  public PreRequestQueue eftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
    return this;
  }

   /**
   * Get eftDateTime
   * @return eftDateTime
  **/
  @JsonProperty("eftDateTime")
  @ApiModelProperty(value = "")
  public String getEftDateTime() {
    return eftDateTime;
  }

  public void setEftDateTime(String eftDateTime) {
    this.eftDateTime = eftDateTime;
  }

  public PreRequestQueue eftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
    return this;
  }

   /**
   * Get eftStatus
   * @return eftStatus
  **/
  @JsonProperty("eftStatus")
  @ApiModelProperty(value = "")
  public String getEftStatus() {
    return eftStatus;
  }

  public void setEftStatus(String eftStatus) {
    this.eftStatus = eftStatus;
  }

  public PreRequestQueue eftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
    return this;
  }

   /**
   * Get eftSubj
   * @return eftSubj
  **/
  @JsonProperty("eft_subj")
  @ApiModelProperty(value = "")
  public String getEftSubj() {
    return eftSubj;
  }

  public void setEftSubj(String eftSubj) {
    this.eftSubj = eftSubj;
  }

  public PreRequestQueue frequency(String frequency) {
    this.frequency = frequency;
    return this;
  }

   /**
   * Get frequency
   * @return frequency
  **/
  @JsonProperty("frequency")
  @ApiModelProperty(value = "")
  public String getFrequency() {
    return frequency;
  }

  public void setFrequency(String frequency) {
    this.frequency = frequency;
  }

  public PreRequestQueue id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public PreRequestQueue isBypass(Boolean isBypass) {
    this.isBypass = isBypass;
    return this;
  }

   /**
   * Get isBypass
   * @return isBypass
  **/
  @JsonProperty("isBypass")
  @ApiModelProperty(value = "")
  public Boolean getIsBypass() {
    return isBypass;
  }

  public void setIsBypass(Boolean isBypass) {
    this.isBypass = isBypass;
  }

  public PreRequestQueue jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public PreRequestQueue ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksdName")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public PreRequestQueue pjmId(Long pjmId) {
    this.pjmId = pjmId;
    return this;
  }

   /**
   * Get pjmId
   * @return pjmId
  **/
  @JsonProperty("pjmId")
  @ApiModelProperty(value = "")
  public Long getPjmId() {
    return pjmId;
  }

  public void setPjmId(Long pjmId) {
    this.pjmId = pjmId;
  }

  public PreRequestQueue processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("processName")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public PreRequestQueue processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public PreRequestQueue sla(Integer sla) {
    this.sla = sla;
    return this;
  }

   /**
   * Get sla
   * @return sla
  **/
  @JsonProperty("sla")
  @ApiModelProperty(value = "")
  public Integer getSla() {
    return sla;
  }

  public void setSla(Integer sla) {
    this.sla = sla;
  }

  public PreRequestQueue status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public PreRequestQueue taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

   /**
   * Get taskId
   * @return taskId
  **/
  @JsonProperty("taskId")
  @ApiModelProperty(value = "")
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public PreRequestQueue uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public PreRequestQueue userName(String userName) {
    this.userName = userName;
    return this;
  }

   /**
   * Get userName
   * @return userName
  **/
  @JsonProperty("userName")
  @ApiModelProperty(value = "")
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PreRequestQueue preRequestQueue = (PreRequestQueue) o;
    return Objects.equals(this.adxScriptId, preRequestQueue.adxScriptId) &&
        Objects.equals(this.batchData, preRequestQueue.batchData) &&
        Objects.equals(this.batchStatus, preRequestQueue.batchStatus) &&
        Objects.equals(this.batchUid, preRequestQueue.batchUid) &&
        Objects.equals(this.businessOps, preRequestQueue.businessOps) &&
        Objects.equals(this.businessUnit, preRequestQueue.businessUnit) &&
        Objects.equals(this.clientId, preRequestQueue.clientId) &&
        Objects.equals(this.clientName, preRequestQueue.clientName) &&
        Objects.equals(this.configJson, preRequestQueue.configJson) &&
        Objects.equals(this.createDateTime, preRequestQueue.createDateTime) &&
        Objects.equals(this.createTimestamp, preRequestQueue.createTimestamp) &&
        Objects.equals(this.defectId, preRequestQueue.defectId) &&
        Objects.equals(this.eftDateTime, preRequestQueue.eftDateTime) &&
        Objects.equals(this.eftStatus, preRequestQueue.eftStatus) &&
        Objects.equals(this.eftSubj, preRequestQueue.eftSubj) &&
        Objects.equals(this.frequency, preRequestQueue.frequency) &&
        Objects.equals(this.id, preRequestQueue.id) &&
        Objects.equals(this.isBypass, preRequestQueue.isBypass) &&
        Objects.equals(this.jobName, preRequestQueue.jobName) &&
        Objects.equals(this.ksdName, preRequestQueue.ksdName) &&
        Objects.equals(this.pjmId, preRequestQueue.pjmId) &&
        Objects.equals(this.processName, preRequestQueue.processName) &&
        Objects.equals(this.processType, preRequestQueue.processType) &&
        Objects.equals(this.sla, preRequestQueue.sla) &&
        Objects.equals(this.status, preRequestQueue.status) &&
        Objects.equals(this.taskId, preRequestQueue.taskId) &&
        Objects.equals(this.uid, preRequestQueue.uid) &&
        Objects.equals(this.userName, preRequestQueue.userName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(adxScriptId, batchData, batchStatus, batchUid, businessOps, businessUnit, clientId, clientName, configJson, createDateTime, createTimestamp, defectId, eftDateTime, eftStatus, eftSubj, frequency, id, isBypass, jobName, ksdName, pjmId, processName, processType, sla, status, taskId, uid, userName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PreRequestQueue {\n");
    
    sb.append("    adxScriptId: ").append(toIndentedString(adxScriptId)).append("\n");
    sb.append("    batchData: ").append(toIndentedString(batchData)).append("\n");
    sb.append("    batchStatus: ").append(toIndentedString(batchStatus)).append("\n");
    sb.append("    batchUid: ").append(toIndentedString(batchUid)).append("\n");
    sb.append("    businessOps: ").append(toIndentedString(businessOps)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    configJson: ").append(toIndentedString(configJson)).append("\n");
    sb.append("    createDateTime: ").append(toIndentedString(createDateTime)).append("\n");
    sb.append("    createTimestamp: ").append(toIndentedString(createTimestamp)).append("\n");
    sb.append("    defectId: ").append(toIndentedString(defectId)).append("\n");
    sb.append("    eftDateTime: ").append(toIndentedString(eftDateTime)).append("\n");
    sb.append("    eftStatus: ").append(toIndentedString(eftStatus)).append("\n");
    sb.append("    eftSubj: ").append(toIndentedString(eftSubj)).append("\n");
    sb.append("    frequency: ").append(toIndentedString(frequency)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    isBypass: ").append(toIndentedString(isBypass)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    pjmId: ").append(toIndentedString(pjmId)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    sla: ").append(toIndentedString(sla)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

