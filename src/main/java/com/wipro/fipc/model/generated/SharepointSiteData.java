/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ClientDetails;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * SharepointSiteData
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class SharepointSiteData   {
  @JsonProperty("clientDetails")
  private ClientDetails clientDetails = null;

  @JsonProperty("driveId")
  private String driveId = null;

  @JsonProperty("driveName")
  private String driveName = null;

  @JsonProperty("driveRootpath")
  private String driveRootpath = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("siteId")
  private String siteId = null;

  @JsonProperty("siteName")
  private String siteName = null;

  public SharepointSiteData clientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
    return this;
  }

   /**
   * Get clientDetails
   * @return clientDetails
  **/
  @JsonProperty("clientDetails")
  @ApiModelProperty(value = "")
  public ClientDetails getClientDetails() {
    return clientDetails;
  }

  public void setClientDetails(ClientDetails clientDetails) {
    this.clientDetails = clientDetails;
  }

  public SharepointSiteData driveId(String driveId) {
    this.driveId = driveId;
    return this;
  }

   /**
   * Get driveId
   * @return driveId
  **/
  @JsonProperty("driveId")
  @ApiModelProperty(value = "")
  public String getDriveId() {
    return driveId;
  }

  public void setDriveId(String driveId) {
    this.driveId = driveId;
  }

  public SharepointSiteData driveName(String driveName) {
    this.driveName = driveName;
    return this;
  }

   /**
   * Get driveName
   * @return driveName
  **/
  @JsonProperty("driveName")
  @ApiModelProperty(value = "")
  public String getDriveName() {
    return driveName;
  }

  public void setDriveName(String driveName) {
    this.driveName = driveName;
  }

  public SharepointSiteData driveRootpath(String driveRootpath) {
    this.driveRootpath = driveRootpath;
    return this;
  }

   /**
   * Get driveRootpath
   * @return driveRootpath
  **/
  @JsonProperty("driveRootpath")
  @ApiModelProperty(value = "")
  public String getDriveRootpath() {
    return driveRootpath;
  }

  public void setDriveRootpath(String driveRootpath) {
    this.driveRootpath = driveRootpath;
  }

  public SharepointSiteData id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public SharepointSiteData siteId(String siteId) {
    this.siteId = siteId;
    return this;
  }

   /**
   * Get siteId
   * @return siteId
  **/
  @JsonProperty("siteId")
  @ApiModelProperty(value = "")
  public String getSiteId() {
    return siteId;
  }

  public void setSiteId(String siteId) {
    this.siteId = siteId;
  }

  public SharepointSiteData siteName(String siteName) {
    this.siteName = siteName;
    return this;
  }

   /**
   * Get siteName
   * @return siteName
  **/
  @JsonProperty("siteName")
  @ApiModelProperty(value = "")
  public String getSiteName() {
    return siteName;
  }

  public void setSiteName(String siteName) {
    this.siteName = siteName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SharepointSiteData sharepointSiteData = (SharepointSiteData) o;
    return Objects.equals(this.clientDetails, sharepointSiteData.clientDetails) &&
        Objects.equals(this.driveId, sharepointSiteData.driveId) &&
        Objects.equals(this.driveName, sharepointSiteData.driveName) &&
        Objects.equals(this.driveRootpath, sharepointSiteData.driveRootpath) &&
        Objects.equals(this.id, sharepointSiteData.id) &&
        Objects.equals(this.siteId, sharepointSiteData.siteId) &&
        Objects.equals(this.siteName, sharepointSiteData.siteName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(clientDetails, driveId, driveName, driveRootpath, id, siteId, siteName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SharepointSiteData {\n");
    
    sb.append("    clientDetails: ").append(toIndentedString(clientDetails)).append("\n");
    sb.append("    driveId: ").append(toIndentedString(driveId)).append("\n");
    sb.append("    driveName: ").append(toIndentedString(driveName)).append("\n");
    sb.append("    driveRootpath: ").append(toIndentedString(driveRootpath)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    siteId: ").append(toIndentedString(siteId)).append("\n");
    sb.append("    siteName: ").append(toIndentedString(siteName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

