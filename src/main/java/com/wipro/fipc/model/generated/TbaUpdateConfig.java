/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaUpdateConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-04-04T15:21:03.227+05:30")
public class TbaUpdateConfig   {
  @JsonProperty("actLngDesc")
  private String actLngDesc = null;

  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("activityId")
  private Integer activityId = null;

  @JsonProperty("addManualFlag")
  private String addManualFlag = null;

  @JsonProperty("baseKey")
  private String baseKey = null;

  @JsonProperty("basicInfo")
  private String basicInfo = null;

  @JsonProperty("classId")
  private Integer classId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eventName")
  private String eventName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("metaData")
  private String metaData = null;

  @JsonProperty("subMetaData")
  private String subMetaData = null;

  @JsonProperty("additionalMetaData")
  private String additionalMetaData = null;

  @JsonProperty("overrideEdits")
  private String overrideEdits = null;

  @JsonProperty("panelId")
  private Integer panelId = null;

  @JsonProperty("parNm")
  private String parNm = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("rerunFlag")
  private String rerunFlag = null;

  @JsonProperty("sequence")
  private String sequence = null;

  @JsonProperty("subKey")
  private String subKey = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("tbaUpdateAction")
  private String tbaUpdateAction = null;

  @JsonProperty("transId")
  private String transId = null;

  @JsonProperty("updateName")
  private String updateName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("value")
  private String value = null;

  @JsonProperty("eventLongDesc")
  private String eventLongDesc = null;

  @JsonProperty("estimateTbaInquiries")
  private String estimateTbaInquiries = null;

  @JsonProperty("groupRelatedPanels")
  private Boolean groupRelatedPanels = null;

  @JsonProperty("pickFromPendingEvent")
  private Boolean pickFromPendingEvent = null;

  @JsonProperty("parentTbaUpdateId")
  private Integer parentTbaUpdateId = null;

  @JsonProperty("subMetaDataId")
  private String subMetaDataId = null;

  public TbaUpdateConfig actLngDesc(String actLngDesc) {
    this.actLngDesc = actLngDesc;
    return this;
  }

   /**
   * Get actLngDesc
   * @return actLngDesc
  **/
  @JsonProperty("actLngDesc")
  @ApiModelProperty(value = "")
  public String getActLngDesc() {
    return actLngDesc;
  }

  public void setActLngDesc(String actLngDesc) {
    this.actLngDesc = actLngDesc;
  }

  public TbaUpdateConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TbaUpdateConfig activityId(Integer activityId) {
    this.activityId = activityId;
    return this;
  }

   /**
   * Get activityId
   * @return activityId
  **/
  @JsonProperty("activityId")
  @ApiModelProperty(value = "")
  public Integer getActivityId() {
    return activityId;
  }

  public void setActivityId(Integer activityId) {
    this.activityId = activityId;
  }

  public TbaUpdateConfig addManualFlag(String addManualFlag) {
    this.addManualFlag = addManualFlag;
    return this;
  }

   /**
   * Get addManualFlag
   * @return addManualFlag
  **/
  @JsonProperty("addManualFlag")
  @ApiModelProperty(value = "")
  public String getAddManualFlag() {
    return addManualFlag;
  }

  public void setAddManualFlag(String addManualFlag) {
    this.addManualFlag = addManualFlag;
  }

  public TbaUpdateConfig baseKey(String baseKey) {
    this.baseKey = baseKey;
    return this;
  }

   /**
   * Get baseKey
   * @return baseKey
  **/
  @JsonProperty("baseKey")
  @ApiModelProperty(value = "")
  public String getBaseKey() {
    return baseKey;
  }

  public void setBaseKey(String baseKey) {
    this.baseKey = baseKey;
  }

  public TbaUpdateConfig basicInfo(String basicInfo) {
    this.basicInfo = basicInfo;
    return this;
  }

   /**
   * Get basicInfo
   * @return basicInfo
  **/
  @JsonProperty("basicInfo")
  @ApiModelProperty(value = "")
  public String getBasicInfo() {
    return basicInfo;
  }

  public void setBasicInfo(String basicInfo) {
    this.basicInfo = basicInfo;
  }

  public TbaUpdateConfig classId(Integer classId) {
    this.classId = classId;
    return this;
  }

   /**
   * Get classId
   * @return classId
  **/
  @JsonProperty("classId")
  @ApiModelProperty(value = "")
  public Integer getClassId() {
    return classId;
  }

  public void setClassId(Integer classId) {
    this.classId = classId;
  }

  public TbaUpdateConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaUpdateConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaUpdateConfig eventName(String eventName) {
    this.eventName = eventName;
    return this;
  }

   /**
   * Get eventName
   * @return eventName
  **/
  @JsonProperty("eventName")
  @ApiModelProperty(value = "")
  public String getEventName() {
    return eventName;
  }

  public void setEventName(String eventName) {
    this.eventName = eventName;
  }

  public TbaUpdateConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaUpdateConfig identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public TbaUpdateConfig jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public TbaUpdateConfig metaData(String metaData) {
    this.metaData = metaData;
    return this;
  }

   /**
   * Get metaData
   * @return metaData
  **/
  @JsonProperty("metaData")
  @ApiModelProperty(value = "")
  public String getMetaData() {
    return metaData;
  }

  public void setMetaData(String metaData) {
    this.metaData = metaData;
  }

  public TbaUpdateConfig subMetaData(String subMetaData) {
    this.subMetaData = subMetaData;
    return this;
  }

   /**
   * Get subMetaData
   * @return subMetaData
  **/
  @JsonProperty("subMetaData")
  @ApiModelProperty(value = "")
  public String getSubMetaData() {
    return subMetaData;
  }

  public void setSubMetaData(String subMetaData) {
    this.subMetaData = subMetaData;
  }

  public TbaUpdateConfig additionalMetaData(String additionalMetaData) {
    this.additionalMetaData = additionalMetaData;
    return this;
  }

   /**
   * Get additionalMetaData
   * @return additionalMetaData
  **/
  @JsonProperty("additionalMetaData")
  @ApiModelProperty(value = "")
  public String getAdditionalMetaData() {
    return additionalMetaData;
  }

  public void setAdditionalMetaData(String additionalMetaData) {
    this.additionalMetaData = additionalMetaData;
  }

  public TbaUpdateConfig overrideEdits(String overrideEdits) {
    this.overrideEdits = overrideEdits;
    return this;
  }

   /**
   * Get overrideEdits
   * @return overrideEdits
  **/
  @JsonProperty("overrideEdits")
  @ApiModelProperty(value = "")
  public String getOverrideEdits() {
    return overrideEdits;
  }

  public void setOverrideEdits(String overrideEdits) {
    this.overrideEdits = overrideEdits;
  }

  public TbaUpdateConfig panelId(Integer panelId) {
    this.panelId = panelId;
    return this;
  }

   /**
   * Get panelId
   * @return panelId
  **/
  @JsonProperty("panelId")
  @ApiModelProperty(value = "")
  public Integer getPanelId() {
    return panelId;
  }

  public void setPanelId(Integer panelId) {
    this.panelId = panelId;
  }

  public TbaUpdateConfig parNm(String parNm) {
    this.parNm = parNm;
    return this;
  }

   /**
   * Get parNm
   * @return parNm
  **/
  @JsonProperty("parNm")
  @ApiModelProperty(value = "")
  public String getParNm() {
    return parNm;
  }

  public void setParNm(String parNm) {
    this.parNm = parNm;
  }

  public TbaUpdateConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TbaUpdateConfig recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public TbaUpdateConfig rerunFlag(String rerunFlag) {
    this.rerunFlag = rerunFlag;
    return this;
  }

   /**
   * Get rerunFlag
   * @return rerunFlag
  **/
  @JsonProperty("rerunFlag")
  @ApiModelProperty(value = "")
  public String getRerunFlag() {
    return rerunFlag;
  }

  public void setRerunFlag(String rerunFlag) {
    this.rerunFlag = rerunFlag;
  }

  public TbaUpdateConfig sequence(String sequence) {
    this.sequence = sequence;
    return this;
  }

   /**
   * Get sequence
   * @return sequence
  **/
  @JsonProperty("sequence")
  @ApiModelProperty(value = "")
  public String getSequence() {
    return sequence;
  }

  public void setSequence(String sequence) {
    this.sequence = sequence;
  }

  public TbaUpdateConfig subKey(String subKey) {
    this.subKey = subKey;
    return this;
  }

   /**
   * Get subKey
   * @return subKey
  **/
  @JsonProperty("subKey")
  @ApiModelProperty(value = "")
  public String getSubKey() {
    return subKey;
  }

  public void setSubKey(String subKey) {
    this.subKey = subKey;
  }

  public TbaUpdateConfig tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaUpdateConfig tbaUpdateAction(String tbaUpdateAction) {
    this.tbaUpdateAction = tbaUpdateAction;
    return this;
  }

   /**
   * Get tbaUpdateAction
   * @return tbaUpdateAction
  **/
  @JsonProperty("tbaUpdateAction")
  @ApiModelProperty(value = "")
  public String getTbaUpdateAction() {
    return tbaUpdateAction;
  }

  public void setTbaUpdateAction(String tbaUpdateAction) {
    this.tbaUpdateAction = tbaUpdateAction;
  }

  public TbaUpdateConfig transId(String transId) {
    this.transId = transId;
    return this;
  }

   /**
   * Get transId
   * @return transId
  **/
  @JsonProperty("transId")
  @ApiModelProperty(value = "")
  public String getTransId() {
    return transId;
  }

  public void setTransId(String transId) {
    this.transId = transId;
  }

  public TbaUpdateConfig updateName(String updateName) {
    this.updateName = updateName;
    return this;
  }

   /**
   * Get updateName
   * @return updateName
  **/
  @JsonProperty("updateName")
  @ApiModelProperty(value = "")
  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public TbaUpdateConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaUpdateConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public TbaUpdateConfig value(String value) {
    this.value = value;
    return this;
  }

   /**
   * Get value
   * @return value
  **/
  @JsonProperty("value")
  @ApiModelProperty(value = "")
  public String getValue() {
    return value;
  }

  public void setValue(String value) {
    this.value = value;
  }

  public TbaUpdateConfig eventLongDesc(String eventLongDesc) {
    this.eventLongDesc = eventLongDesc;
    return this;
  }

   /**
   * Get eventLongDesc
   * @return eventLongDesc
  **/
  @JsonProperty("eventLongDesc")
  @ApiModelProperty(value = "")
  public String getEventLongDesc() {
    return eventLongDesc;
  }

  public void setEventLongDesc(String eventLongDesc) {
    this.eventLongDesc = eventLongDesc;
  }

  public TbaUpdateConfig estimateTbaInquiries(String estimateTbaInquiries) {
    this.estimateTbaInquiries = estimateTbaInquiries;
    return this;
  }

   /**
   * Get estimateTbaInquiries
   * @return estimateTbaInquiries
  **/
  @JsonProperty("estimateTbaInquiries")
  @ApiModelProperty(value = "")
  public String getEstimateTbaInquiries() {
    return estimateTbaInquiries;
  }

  public void setEstimateTbaInquiries(String estimateTbaInquiries) {
    this.estimateTbaInquiries = estimateTbaInquiries;
  }

  public TbaUpdateConfig groupRelatedPanels(Boolean groupRelatedPanels) {
    this.groupRelatedPanels = groupRelatedPanels;
    return this;
  }

   /**
   * Get groupRelatedPanels
   * @return groupRelatedPanels
  **/
  @JsonProperty("groupRelatedPanels")
  @ApiModelProperty(value = "")
  public Boolean getGroupRelatedPanels() {
    return groupRelatedPanels;
  }

  public void setGroupRelatedPanels(Boolean groupRelatedPanels) {
    this.groupRelatedPanels = groupRelatedPanels;
  }

  public TbaUpdateConfig pickFromPendingEvent(Boolean pickFromPendingEvent) {
    this.pickFromPendingEvent = pickFromPendingEvent;
    return this;
  }

   /**
   * Get pickFromPendingEvent
   * @return pickFromPendingEvent
  **/
  @JsonProperty("pickFromPendingEvent")
  @ApiModelProperty(value = "")
  public Boolean getPickFromPendingEvent() {
    return pickFromPendingEvent;
  }

  public void setPickFromPendingEvent(Boolean pickFromPendingEvent) {
    this.pickFromPendingEvent = pickFromPendingEvent;
  }

  public TbaUpdateConfig parentTbaUpdateId(Integer parentTbaUpdateId) {
    this.parentTbaUpdateId = parentTbaUpdateId;
    return this;
  }

   /**
   * Get parentTbaUpdateId
   * @return parentTbaUpdateId
  **/
  @JsonProperty("parentTbaUpdateId")
  @ApiModelProperty(value = "")
  public Integer getParentTbaUpdateId() {
    return parentTbaUpdateId;
  }

  public void setParentTbaUpdateId(Integer parentTbaUpdateId) {
    this.parentTbaUpdateId = parentTbaUpdateId;
  }

  public TbaUpdateConfig subMetaDataId(String subMetaDataId) {
    this.subMetaDataId = subMetaDataId;
    return this;
  }

   /**
   * Get subMetaDataId
   * @return subMetaDataId
  **/
  @JsonProperty("subMetaDataId")
  @ApiModelProperty(value = "")
  public String getSubMetaDataId() {
    return subMetaDataId;
  }

  public void setSubMetaDataId(String subMetaDataId) {
    this.subMetaDataId = subMetaDataId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaUpdateConfig tbaUpdateConfig = (TbaUpdateConfig) o;
    return Objects.equals(this.actLngDesc, tbaUpdateConfig.actLngDesc) &&
        Objects.equals(this.activeFlag, tbaUpdateConfig.activeFlag) &&
        Objects.equals(this.activityId, tbaUpdateConfig.activityId) &&
        Objects.equals(this.addManualFlag, tbaUpdateConfig.addManualFlag) &&
        Objects.equals(this.baseKey, tbaUpdateConfig.baseKey) &&
        Objects.equals(this.basicInfo, tbaUpdateConfig.basicInfo) &&
        Objects.equals(this.classId, tbaUpdateConfig.classId) &&
        Objects.equals(this.createdBy, tbaUpdateConfig.createdBy) &&
        Objects.equals(this.createdDate, tbaUpdateConfig.createdDate) &&
        Objects.equals(this.eventName, tbaUpdateConfig.eventName) &&
        Objects.equals(this.id, tbaUpdateConfig.id) &&
        Objects.equals(this.identifier, tbaUpdateConfig.identifier) &&
        Objects.equals(this.jsonKey, tbaUpdateConfig.jsonKey) &&
        Objects.equals(this.metaData, tbaUpdateConfig.metaData) &&
        Objects.equals(this.subMetaData, tbaUpdateConfig.subMetaData) &&
        Objects.equals(this.additionalMetaData, tbaUpdateConfig.additionalMetaData) &&
        Objects.equals(this.overrideEdits, tbaUpdateConfig.overrideEdits) &&
        Objects.equals(this.panelId, tbaUpdateConfig.panelId) &&
        Objects.equals(this.parNm, tbaUpdateConfig.parNm) &&
        Objects.equals(this.processJobMapping, tbaUpdateConfig.processJobMapping) &&
        Objects.equals(this.recordIdentifier, tbaUpdateConfig.recordIdentifier) &&
        Objects.equals(this.rerunFlag, tbaUpdateConfig.rerunFlag) &&
        Objects.equals(this.sequence, tbaUpdateConfig.sequence) &&
        Objects.equals(this.subKey, tbaUpdateConfig.subKey) &&
        Objects.equals(this.tbaFieldName, tbaUpdateConfig.tbaFieldName) &&
        Objects.equals(this.tbaUpdateAction, tbaUpdateConfig.tbaUpdateAction) &&
        Objects.equals(this.transId, tbaUpdateConfig.transId) &&
        Objects.equals(this.updateName, tbaUpdateConfig.updateName) &&
        Objects.equals(this.updatedBy, tbaUpdateConfig.updatedBy) &&
        Objects.equals(this.updatedDate, tbaUpdateConfig.updatedDate) &&
        Objects.equals(this.value, tbaUpdateConfig.value) &&
        Objects.equals(this.eventLongDesc, tbaUpdateConfig.eventLongDesc) &&
        Objects.equals(this.estimateTbaInquiries, tbaUpdateConfig.estimateTbaInquiries) &&
        Objects.equals(this.groupRelatedPanels, tbaUpdateConfig.groupRelatedPanels) &&
        Objects.equals(this.pickFromPendingEvent, tbaUpdateConfig.pickFromPendingEvent) &&
        Objects.equals(this.parentTbaUpdateId, tbaUpdateConfig.parentTbaUpdateId) &&
        Objects.equals(this.subMetaDataId, tbaUpdateConfig.subMetaDataId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actLngDesc, activeFlag, activityId, addManualFlag, baseKey, basicInfo, classId, createdBy, createdDate, eventName, id, identifier, jsonKey, metaData, subMetaData, additionalMetaData, overrideEdits, panelId, parNm, processJobMapping, recordIdentifier, rerunFlag, sequence, subKey, tbaFieldName, tbaUpdateAction, transId, updateName, updatedBy, updatedDate, value, eventLongDesc, estimateTbaInquiries, groupRelatedPanels, pickFromPendingEvent, parentTbaUpdateId, subMetaDataId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaUpdateConfig {\n");
    
    sb.append("    actLngDesc: ").append(toIndentedString(actLngDesc)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    activityId: ").append(toIndentedString(activityId)).append("\n");
    sb.append("    addManualFlag: ").append(toIndentedString(addManualFlag)).append("\n");
    sb.append("    baseKey: ").append(toIndentedString(baseKey)).append("\n");
    sb.append("    basicInfo: ").append(toIndentedString(basicInfo)).append("\n");
    sb.append("    classId: ").append(toIndentedString(classId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eventName: ").append(toIndentedString(eventName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    metaData: ").append(toIndentedString(metaData)).append("\n");
    sb.append("    subMetaData: ").append(toIndentedString(subMetaData)).append("\n");
    sb.append("    additionalMetaData: ").append(toIndentedString(additionalMetaData)).append("\n");
    sb.append("    overrideEdits: ").append(toIndentedString(overrideEdits)).append("\n");
    sb.append("    panelId: ").append(toIndentedString(panelId)).append("\n");
    sb.append("    parNm: ").append(toIndentedString(parNm)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    rerunFlag: ").append(toIndentedString(rerunFlag)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    subKey: ").append(toIndentedString(subKey)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    tbaUpdateAction: ").append(toIndentedString(tbaUpdateAction)).append("\n");
    sb.append("    transId: ").append(toIndentedString(transId)).append("\n");
    sb.append("    updateName: ").append(toIndentedString(updateName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("    eventLongDesc: ").append(toIndentedString(eventLongDesc)).append("\n");
    sb.append("    estimateTbaInquiries: ").append(toIndentedString(estimateTbaInquiries)).append("\n");
    sb.append("    groupRelatedPanels: ").append(toIndentedString(groupRelatedPanels)).append("\n");
    sb.append("    pickFromPendingEvent: ").append(toIndentedString(pickFromPendingEvent)).append("\n");
    sb.append("    parentTbaUpdateId: ").append(toIndentedString(parentTbaUpdateId)).append("\n");
    sb.append("    subMetaDataId: ").append(toIndentedString(subMetaDataId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

