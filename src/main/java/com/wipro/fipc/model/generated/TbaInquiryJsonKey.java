/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.Character;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaInquiryJsonKey
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaInquiryJsonKey   {
  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("fieldType")
  private String fieldType = null;

  @JsonProperty("flag")
  private Character flag = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("parNM")
  private String parNM = null;

  @JsonProperty("subJsonKey")
  private String subJsonKey = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaInquiryJsonKey createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaInquiryJsonKey createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaInquiryJsonKey fieldType(String fieldType) {
    this.fieldType = fieldType;
    return this;
  }

   /**
   * Get fieldType
   * @return fieldType
  **/
  @JsonProperty("fieldType")
  @ApiModelProperty(value = "")
  public String getFieldType() {
    return fieldType;
  }

  public void setFieldType(String fieldType) {
    this.fieldType = fieldType;
  }

  public TbaInquiryJsonKey flag(Character flag) {
    this.flag = flag;
    return this;
  }

   /**
   * Get flag
   * @return flag
  **/
  @JsonProperty("flag")
  @ApiModelProperty(value = "")
  public Character getFlag() {
    return flag;
  }

  public void setFlag(Character flag) {
    this.flag = flag;
  }

  public TbaInquiryJsonKey id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaInquiryJsonKey jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public TbaInquiryJsonKey parNM(String parNM) {
    this.parNM = parNM;
    return this;
  }

   /**
   * Get parNM
   * @return parNM
  **/
  @JsonProperty("parNM")
  @ApiModelProperty(value = "")
  public String getParNM() {
    return parNM;
  }

  public void setParNM(String parNM) {
    this.parNM = parNM;
  }

  public TbaInquiryJsonKey subJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
    return this;
  }

   /**
   * Get subJsonKey
   * @return subJsonKey
  **/
  @JsonProperty("subJsonKey")
  @ApiModelProperty(value = "")
  public String getSubJsonKey() {
    return subJsonKey;
  }

  public void setSubJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
  }

  public TbaInquiryJsonKey tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaInquiryJsonKey updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaInquiryJsonKey updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaInquiryJsonKey tbaInquiryJsonKey = (TbaInquiryJsonKey) o;
    return Objects.equals(this.createdBy, tbaInquiryJsonKey.createdBy) &&
        Objects.equals(this.createdDate, tbaInquiryJsonKey.createdDate) &&
        Objects.equals(this.fieldType, tbaInquiryJsonKey.fieldType) &&
        Objects.equals(this.flag, tbaInquiryJsonKey.flag) &&
        Objects.equals(this.id, tbaInquiryJsonKey.id) &&
        Objects.equals(this.jsonKey, tbaInquiryJsonKey.jsonKey) &&
        Objects.equals(this.parNM, tbaInquiryJsonKey.parNM) &&
        Objects.equals(this.subJsonKey, tbaInquiryJsonKey.subJsonKey) &&
        Objects.equals(this.tbaFieldName, tbaInquiryJsonKey.tbaFieldName) &&
        Objects.equals(this.updatedBy, tbaInquiryJsonKey.updatedBy) &&
        Objects.equals(this.updatedDate, tbaInquiryJsonKey.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(createdBy, createdDate, fieldType, flag, id, jsonKey, parNM, subJsonKey, tbaFieldName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaInquiryJsonKey {\n");
    
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    fieldType: ").append(toIndentedString(fieldType)).append("\n");
    sb.append("    flag: ").append(toIndentedString(flag)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    parNM: ").append(toIndentedString(parNM)).append("\n");
    sb.append("    subJsonKey: ").append(toIndentedString(subJsonKey)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

