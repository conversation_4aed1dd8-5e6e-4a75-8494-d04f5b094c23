/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaNoticeMaster
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class TbaNoticeMaster   {
  @JsonProperty("clientId")
  private Integer clientId = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("metadata")
  private String metadata = null;

  @JsonProperty("noticeId")
  private Integer noticeId = null;

  @JsonProperty("noticeName")
  private String noticeName = null;

  @JsonProperty("tbaFieldType")
  private String tbaFieldType = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaNoticeMaster clientId(Integer clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public Integer getClientId() {
    return clientId;
  }

  public void setClientId(Integer clientId) {
    this.clientId = clientId;
  }

  public TbaNoticeMaster createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaNoticeMaster createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaNoticeMaster id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaNoticeMaster metadata(String metadata) {
    this.metadata = metadata;
    return this;
  }

   /**
   * Get metadata
   * @return metadata
  **/
  @JsonProperty("metadata")
  @ApiModelProperty(value = "")
  public String getMetadata() {
    return metadata;
  }

  public void setMetadata(String metadata) {
    this.metadata = metadata;
  }

  public TbaNoticeMaster noticeId(Integer noticeId) {
    this.noticeId = noticeId;
    return this;
  }

   /**
   * Get noticeId
   * @return noticeId
  **/
  @JsonProperty("noticeId")
  @ApiModelProperty(value = "")
  public Integer getNoticeId() {
    return noticeId;
  }

  public void setNoticeId(Integer noticeId) {
    this.noticeId = noticeId;
  }

  public TbaNoticeMaster noticeName(String noticeName) {
    this.noticeName = noticeName;
    return this;
  }

   /**
   * Get noticeName
   * @return noticeName
  **/
  @JsonProperty("noticeName")
  @ApiModelProperty(value = "")
  public String getNoticeName() {
    return noticeName;
  }

  public void setNoticeName(String noticeName) {
    this.noticeName = noticeName;
  }

  public TbaNoticeMaster tbaFieldType(String tbaFieldType) {
    this.tbaFieldType = tbaFieldType;
    return this;
  }

   /**
   * Get tbaFieldType
   * @return tbaFieldType
  **/
  @JsonProperty("tbaFieldType")
  @ApiModelProperty(value = "")
  public String getTbaFieldType() {
    return tbaFieldType;
  }

  public void setTbaFieldType(String tbaFieldType) {
    this.tbaFieldType = tbaFieldType;
  }

  public TbaNoticeMaster updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaNoticeMaster updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaNoticeMaster tbaNoticeMaster = (TbaNoticeMaster) o;
    return Objects.equals(this.clientId, tbaNoticeMaster.clientId) &&
        Objects.equals(this.createdBy, tbaNoticeMaster.createdBy) &&
        Objects.equals(this.createdDate, tbaNoticeMaster.createdDate) &&
        Objects.equals(this.id, tbaNoticeMaster.id) &&
        Objects.equals(this.metadata, tbaNoticeMaster.metadata) &&
        Objects.equals(this.noticeId, tbaNoticeMaster.noticeId) &&
        Objects.equals(this.noticeName, tbaNoticeMaster.noticeName) &&
        Objects.equals(this.tbaFieldType, tbaNoticeMaster.tbaFieldType) &&
        Objects.equals(this.updatedBy, tbaNoticeMaster.updatedBy) &&
        Objects.equals(this.updatedDate, tbaNoticeMaster.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(clientId, createdBy, createdDate, id, metadata, noticeId, noticeName, tbaFieldType, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaNoticeMaster {\n");
    
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    noticeId: ").append(toIndentedString(noticeId)).append("\n");
    sb.append("    noticeName: ").append(toIndentedString(noticeName)).append("\n");
    sb.append("    tbaFieldType: ").append(toIndentedString(tbaFieldType)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

