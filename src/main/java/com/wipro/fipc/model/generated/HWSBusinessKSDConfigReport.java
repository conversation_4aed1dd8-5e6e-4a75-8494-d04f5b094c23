/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * HWSBusinessKSDConfigReport
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class HWSBusinessKSDConfigReport   {
  @JsonProperty("approved_by")
  private String approvedBy = null;

  @JsonProperty("approved_on")
  private Date approvedOn = null;

  @JsonProperty("business")
  private String business = null;

  @JsonProperty("business_unit")
  private String businessUnit = null;

  @JsonProperty("client_code")
  private String clientCode = null;

  @JsonProperty("client_name")
  private String clientName = null;

  @JsonProperty("config_status")
  private String configStatus = null;

  @JsonProperty("created_by")
  private String createdBy = null;

  @JsonProperty("created_on")
  private Date createdOn = null;

  @JsonProperty("ksd_eft_subject_name")
  private String ksdEftSubjectName = null;

  @JsonProperty("ksd_job_name")
  private String ksdJobName = null;

  @JsonProperty("ksd_name")
  private String ksdName = null;

  @JsonProperty("process_name")
  private String processName = null;

  @JsonProperty("process_type")
  private String processType = null;

  @JsonProperty("tower")
  private String tower = null;

  @JsonProperty("updated_by")
  private String updatedBy = null;

  @JsonProperty("updated_on")
  private Date updatedOn = null;

  @JsonProperty("user_group")
  private String userGroup = null;

  @JsonProperty("user_group_status")
  private String userGroupStatus = null;

  public HWSBusinessKSDConfigReport approvedBy(String approvedBy) {
    this.approvedBy = approvedBy;
    return this;
  }

   /**
   * Get approvedBy
   * @return approvedBy
  **/
  @JsonProperty("approved_by")
  @ApiModelProperty(value = "")
  public String getApprovedBy() {
    return approvedBy;
  }

  public void setApprovedBy(String approvedBy) {
    this.approvedBy = approvedBy;
  }

  public HWSBusinessKSDConfigReport approvedOn(Date approvedOn) {
    this.approvedOn = approvedOn;
    return this;
  }

   /**
   * Get approvedOn
   * @return approvedOn
  **/
  @JsonProperty("approved_on")
  @ApiModelProperty(value = "")
  public Date getApprovedOn() {
    return approvedOn;
  }

  public void setApprovedOn(Date approvedOn) {
    this.approvedOn = approvedOn;
  }

  public HWSBusinessKSDConfigReport business(String business) {
    this.business = business;
    return this;
  }

   /**
   * Get business
   * @return business
  **/
  @JsonProperty("business")
  @ApiModelProperty(value = "")
  public String getBusiness() {
    return business;
  }

  public void setBusiness(String business) {
    this.business = business;
  }

  public HWSBusinessKSDConfigReport businessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("business_unit")
  @ApiModelProperty(value = "")
  public String getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(String businessUnit) {
    this.businessUnit = businessUnit;
  }

  public HWSBusinessKSDConfigReport clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("client_code")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public HWSBusinessKSDConfigReport clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("client_name")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public HWSBusinessKSDConfigReport configStatus(String configStatus) {
    this.configStatus = configStatus;
    return this;
  }

   /**
   * Get configStatus
   * @return configStatus
  **/
  @JsonProperty("config_status")
  @ApiModelProperty(value = "")
  public String getConfigStatus() {
    return configStatus;
  }

  public void setConfigStatus(String configStatus) {
    this.configStatus = configStatus;
  }

  public HWSBusinessKSDConfigReport createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("created_by")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public HWSBusinessKSDConfigReport createdOn(Date createdOn) {
    this.createdOn = createdOn;
    return this;
  }

   /**
   * Get createdOn
   * @return createdOn
  **/
  @JsonProperty("created_on")
  @ApiModelProperty(value = "")
  public Date getCreatedOn() {
    return createdOn;
  }

  public void setCreatedOn(Date createdOn) {
    this.createdOn = createdOn;
  }

  public HWSBusinessKSDConfigReport ksdEftSubjectName(String ksdEftSubjectName) {
    this.ksdEftSubjectName = ksdEftSubjectName;
    return this;
  }

   /**
   * Get ksdEftSubjectName
   * @return ksdEftSubjectName
  **/
  @JsonProperty("ksd_eft_subject_name")
  @ApiModelProperty(value = "")
  public String getKsdEftSubjectName() {
    return ksdEftSubjectName;
  }

  public void setKsdEftSubjectName(String ksdEftSubjectName) {
    this.ksdEftSubjectName = ksdEftSubjectName;
  }

  public HWSBusinessKSDConfigReport ksdJobName(String ksdJobName) {
    this.ksdJobName = ksdJobName;
    return this;
  }

   /**
   * Get ksdJobName
   * @return ksdJobName
  **/
  @JsonProperty("ksd_job_name")
  @ApiModelProperty(value = "")
  public String getKsdJobName() {
    return ksdJobName;
  }

  public void setKsdJobName(String ksdJobName) {
    this.ksdJobName = ksdJobName;
  }

  public HWSBusinessKSDConfigReport ksdName(String ksdName) {
    this.ksdName = ksdName;
    return this;
  }

   /**
   * Get ksdName
   * @return ksdName
  **/
  @JsonProperty("ksd_name")
  @ApiModelProperty(value = "")
  public String getKsdName() {
    return ksdName;
  }

  public void setKsdName(String ksdName) {
    this.ksdName = ksdName;
  }

  public HWSBusinessKSDConfigReport processName(String processName) {
    this.processName = processName;
    return this;
  }

   /**
   * Get processName
   * @return processName
  **/
  @JsonProperty("process_name")
  @ApiModelProperty(value = "")
  public String getProcessName() {
    return processName;
  }

  public void setProcessName(String processName) {
    this.processName = processName;
  }

  public HWSBusinessKSDConfigReport processType(String processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("process_type")
  @ApiModelProperty(value = "")
  public String getProcessType() {
    return processType;
  }

  public void setProcessType(String processType) {
    this.processType = processType;
  }

  public HWSBusinessKSDConfigReport tower(String tower) {
    this.tower = tower;
    return this;
  }

   /**
   * Get tower
   * @return tower
  **/
  @JsonProperty("tower")
  @ApiModelProperty(value = "")
  public String getTower() {
    return tower;
  }

  public void setTower(String tower) {
    this.tower = tower;
  }

  public HWSBusinessKSDConfigReport updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updated_by")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public HWSBusinessKSDConfigReport updatedOn(Date updatedOn) {
    this.updatedOn = updatedOn;
    return this;
  }

   /**
   * Get updatedOn
   * @return updatedOn
  **/
  @JsonProperty("updated_on")
  @ApiModelProperty(value = "")
  public Date getUpdatedOn() {
    return updatedOn;
  }

  public void setUpdatedOn(Date updatedOn) {
    this.updatedOn = updatedOn;
  }

  public HWSBusinessKSDConfigReport userGroup(String userGroup) {
    this.userGroup = userGroup;
    return this;
  }

   /**
   * Get userGroup
   * @return userGroup
  **/
  @JsonProperty("user_group")
  @ApiModelProperty(value = "")
  public String getUserGroup() {
    return userGroup;
  }

  public void setUserGroup(String userGroup) {
    this.userGroup = userGroup;
  }

  public HWSBusinessKSDConfigReport userGroupStatus(String userGroupStatus) {
    this.userGroupStatus = userGroupStatus;
    return this;
  }

   /**
   * Get userGroupStatus
   * @return userGroupStatus
  **/
  @JsonProperty("user_group_status")
  @ApiModelProperty(value = "")
  public String getUserGroupStatus() {
    return userGroupStatus;
  }

  public void setUserGroupStatus(String userGroupStatus) {
    this.userGroupStatus = userGroupStatus;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HWSBusinessKSDConfigReport hwSBusinessKSDConfigReport = (HWSBusinessKSDConfigReport) o;
    return Objects.equals(this.approvedBy, hwSBusinessKSDConfigReport.approvedBy) &&
        Objects.equals(this.approvedOn, hwSBusinessKSDConfigReport.approvedOn) &&
        Objects.equals(this.business, hwSBusinessKSDConfigReport.business) &&
        Objects.equals(this.businessUnit, hwSBusinessKSDConfigReport.businessUnit) &&
        Objects.equals(this.clientCode, hwSBusinessKSDConfigReport.clientCode) &&
        Objects.equals(this.clientName, hwSBusinessKSDConfigReport.clientName) &&
        Objects.equals(this.configStatus, hwSBusinessKSDConfigReport.configStatus) &&
        Objects.equals(this.createdBy, hwSBusinessKSDConfigReport.createdBy) &&
        Objects.equals(this.createdOn, hwSBusinessKSDConfigReport.createdOn) &&
        Objects.equals(this.ksdEftSubjectName, hwSBusinessKSDConfigReport.ksdEftSubjectName) &&
        Objects.equals(this.ksdJobName, hwSBusinessKSDConfigReport.ksdJobName) &&
        Objects.equals(this.ksdName, hwSBusinessKSDConfigReport.ksdName) &&
        Objects.equals(this.processName, hwSBusinessKSDConfigReport.processName) &&
        Objects.equals(this.processType, hwSBusinessKSDConfigReport.processType) &&
        Objects.equals(this.tower, hwSBusinessKSDConfigReport.tower) &&
        Objects.equals(this.updatedBy, hwSBusinessKSDConfigReport.updatedBy) &&
        Objects.equals(this.updatedOn, hwSBusinessKSDConfigReport.updatedOn) &&
        Objects.equals(this.userGroup, hwSBusinessKSDConfigReport.userGroup) &&
        Objects.equals(this.userGroupStatus, hwSBusinessKSDConfigReport.userGroupStatus);
  }

  @Override
  public int hashCode() {
    return Objects.hash(approvedBy, approvedOn, business, businessUnit, clientCode, clientName, configStatus, createdBy, createdOn, ksdEftSubjectName, ksdJobName, ksdName, processName, processType, tower, updatedBy, updatedOn, userGroup, userGroupStatus);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HWSBusinessKSDConfigReport {\n");
    
    sb.append("    approvedBy: ").append(toIndentedString(approvedBy)).append("\n");
    sb.append("    approvedOn: ").append(toIndentedString(approvedOn)).append("\n");
    sb.append("    business: ").append(toIndentedString(business)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    configStatus: ").append(toIndentedString(configStatus)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
    sb.append("    ksdEftSubjectName: ").append(toIndentedString(ksdEftSubjectName)).append("\n");
    sb.append("    ksdJobName: ").append(toIndentedString(ksdJobName)).append("\n");
    sb.append("    ksdName: ").append(toIndentedString(ksdName)).append("\n");
    sb.append("    processName: ").append(toIndentedString(processName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    tower: ").append(toIndentedString(tower)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedOn: ").append(toIndentedString(updatedOn)).append("\n");
    sb.append("    userGroup: ").append(toIndentedString(userGroup)).append("\n");
    sb.append("    userGroupStatus: ").append(toIndentedString(userGroupStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

