/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * Allocation
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class Allocation   {
  @JsonProperty("allocatedById")
  private Long allocatedById = null;

  @JsonProperty("allocatedToId")
  private Long allocatedToId = null;

  @JsonProperty("assignee")
  private String assignee = null;

  @JsonProperty("clientName")
  private String clientName = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("timestamp")
  private Long timestamp = null;

  @JsonProperty("uid")
  private String uid = null;

  public Allocation allocatedById(Long allocatedById) {
    this.allocatedById = allocatedById;
    return this;
  }

   /**
   * Get allocatedById
   * @return allocatedById
  **/
  @JsonProperty("allocatedById")
  @ApiModelProperty(value = "")
  public Long getAllocatedById() {
    return allocatedById;
  }

  public void setAllocatedById(Long allocatedById) {
    this.allocatedById = allocatedById;
  }

  public Allocation allocatedToId(Long allocatedToId) {
    this.allocatedToId = allocatedToId;
    return this;
  }

   /**
   * Get allocatedToId
   * @return allocatedToId
  **/
  @JsonProperty("allocatedToId")
  @ApiModelProperty(value = "")
  public Long getAllocatedToId() {
    return allocatedToId;
  }

  public void setAllocatedToId(Long allocatedToId) {
    this.allocatedToId = allocatedToId;
  }

  public Allocation assignee(String assignee) {
    this.assignee = assignee;
    return this;
  }

   /**
   * Get assignee
   * @return assignee
  **/
  @JsonProperty("assignee")
  @ApiModelProperty(value = "")
  public String getAssignee() {
    return assignee;
  }

  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public Allocation clientName(String clientName) {
    this.clientName = clientName;
    return this;
  }

   /**
   * Get clientName
   * @return clientName
  **/
  @JsonProperty("clientName")
  @ApiModelProperty(value = "")
  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public Allocation fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public Allocation id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Allocation timestamp(Long timestamp) {
    this.timestamp = timestamp;
    return this;
  }

   /**
   * Get timestamp
   * @return timestamp
  **/
  @JsonProperty("timestamp")
  @ApiModelProperty(value = "")
  public Long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }

  public Allocation uid(String uid) {
    this.uid = uid;
    return this;
  }

   /**
   * Get uid
   * @return uid
  **/
  @JsonProperty("uid")
  @ApiModelProperty(value = "")
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Allocation allocation = (Allocation) o;
    return Objects.equals(this.allocatedById, allocation.allocatedById) &&
        Objects.equals(this.allocatedToId, allocation.allocatedToId) &&
        Objects.equals(this.assignee, allocation.assignee) &&
        Objects.equals(this.clientName, allocation.clientName) &&
        Objects.equals(this.fileName, allocation.fileName) &&
        Objects.equals(this.id, allocation.id) &&
        Objects.equals(this.timestamp, allocation.timestamp) &&
        Objects.equals(this.uid, allocation.uid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(allocatedById, allocatedToId, assignee, clientName, fileName, id, timestamp, uid);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Allocation {\n");
    
    sb.append("    allocatedById: ").append(toIndentedString(allocatedById)).append("\n");
    sb.append("    allocatedToId: ").append(toIndentedString(allocatedToId)).append("\n");
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    clientName: ").append(toIndentedString(clientName)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    timestamp: ").append(toIndentedString(timestamp)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

