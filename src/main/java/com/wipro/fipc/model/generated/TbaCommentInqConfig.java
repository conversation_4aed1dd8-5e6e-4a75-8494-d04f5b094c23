/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TbaCommentInqConfig
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class TbaCommentInqConfig   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("updateFlag")
  private Boolean updateFlag = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("eftFromDate")
  private String eftFromDate = null;

  @JsonProperty("eftToDate")
  private String eftToDate = null;

  @JsonProperty("fieldType")
  private String fieldType = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("inquiryDefName")
  private String inquiryDefName = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("parNM")
  private String parNM = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("subJsonKey")
  private String subJsonKey = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  public TbaCommentInqConfig activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TbaCommentInqConfig updateFlag(Boolean updateFlag) {
    this.updateFlag = updateFlag;
    return this;
  }

   /**
   * Get updateFlag
   * @return updateFlag
  **/
  @JsonProperty("updateFlag")
  @ApiModelProperty(value = "")
  public Boolean getUpdateFlag() {
    return updateFlag;
  }

  public void setUpdateFlag(Boolean updateFlag) {
    this.updateFlag = updateFlag;
  }

  public TbaCommentInqConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaCommentInqConfig createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaCommentInqConfig eftFromDate(String eftFromDate) {
    this.eftFromDate = eftFromDate;
    return this;
  }

   /**
   * Get eftFromDate
   * @return eftFromDate
  **/
  @JsonProperty("eftFromDate")
  @ApiModelProperty(value = "")
  public String getEftFromDate() {
    return eftFromDate;
  }

  public void setEftFromDate(String eftFromDate) {
    this.eftFromDate = eftFromDate;
  }

  public TbaCommentInqConfig eftToDate(String eftToDate) {
    this.eftToDate = eftToDate;
    return this;
  }

   /**
   * Get eftToDate
   * @return eftToDate
  **/
  @JsonProperty("eftToDate")
  @ApiModelProperty(value = "")
  public String getEftToDate() {
    return eftToDate;
  }

  public void setEftToDate(String eftToDate) {
    this.eftToDate = eftToDate;
  }

  public TbaCommentInqConfig fieldType(String fieldType) {
    this.fieldType = fieldType;
    return this;
  }

   /**
   * Get fieldType
   * @return fieldType
  **/
  @JsonProperty("fieldType")
  @ApiModelProperty(value = "")
  public String getFieldType() {
    return fieldType;
  }

  public void setFieldType(String fieldType) {
    this.fieldType = fieldType;
  }

  public TbaCommentInqConfig id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaCommentInqConfig inquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
    return this;
  }

   /**
   * Get inquiryDefName
   * @return inquiryDefName
  **/
  @JsonProperty("inquiryDefName")
  @ApiModelProperty(value = "")
  public String getInquiryDefName() {
    return inquiryDefName;
  }

  public void setInquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
  }

  public TbaCommentInqConfig jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public TbaCommentInqConfig parNM(String parNM) {
    this.parNM = parNM;
    return this;
  }

   /**
   * Get parNM
   * @return parNM
  **/
  @JsonProperty("parNM")
  @ApiModelProperty(value = "")
  public String getParNM() {
    return parNM;
  }

  public void setParNM(String parNM) {
    this.parNM = parNM;
  }

  public TbaCommentInqConfig processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TbaCommentInqConfig subJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
    return this;
  }

   /**
   * Get subJsonKey
   * @return subJsonKey
  **/
  @JsonProperty("subJsonKey")
  @ApiModelProperty(value = "")
  public String getSubJsonKey() {
    return subJsonKey;
  }

  public void setSubJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
  }

  public TbaCommentInqConfig tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaCommentInqConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaCommentInqConfig updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaCommentInqConfig tbaCommentInqConfig = (TbaCommentInqConfig) o;
    return Objects.equals(this.activeFlag, tbaCommentInqConfig.activeFlag) &&
        Objects.equals(this.updateFlag, tbaCommentInqConfig.updateFlag) &&
        Objects.equals(this.createdBy, tbaCommentInqConfig.createdBy) &&
        Objects.equals(this.createdDate, tbaCommentInqConfig.createdDate) &&
        Objects.equals(this.eftFromDate, tbaCommentInqConfig.eftFromDate) &&
        Objects.equals(this.eftToDate, tbaCommentInqConfig.eftToDate) &&
        Objects.equals(this.fieldType, tbaCommentInqConfig.fieldType) &&
        Objects.equals(this.id, tbaCommentInqConfig.id) &&
        Objects.equals(this.inquiryDefName, tbaCommentInqConfig.inquiryDefName) &&
        Objects.equals(this.jsonKey, tbaCommentInqConfig.jsonKey) &&
        Objects.equals(this.parNM, tbaCommentInqConfig.parNM) &&
        Objects.equals(this.processJobMapping, tbaCommentInqConfig.processJobMapping) &&
        Objects.equals(this.subJsonKey, tbaCommentInqConfig.subJsonKey) &&
        Objects.equals(this.tbaFieldName, tbaCommentInqConfig.tbaFieldName) &&
        Objects.equals(this.updatedBy, tbaCommentInqConfig.updatedBy) &&
        Objects.equals(this.updatedDate, tbaCommentInqConfig.updatedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, updateFlag, createdBy, createdDate, eftFromDate, eftToDate, fieldType, id, inquiryDefName, jsonKey, parNM, processJobMapping, subJsonKey, tbaFieldName, updatedBy, updatedDate);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaCommentInqConfig {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    updateFlag: ").append(toIndentedString(updateFlag)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    eftFromDate: ").append(toIndentedString(eftFromDate)).append("\n");
    sb.append("    eftToDate: ").append(toIndentedString(eftToDate)).append("\n");
    sb.append("    fieldType: ").append(toIndentedString(fieldType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    inquiryDefName: ").append(toIndentedString(inquiryDefName)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    parNM: ").append(toIndentedString(parNM)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    subJsonKey: ").append(toIndentedString(subJsonKey)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

