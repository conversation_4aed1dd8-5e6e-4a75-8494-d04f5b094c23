/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * JobScheduleBO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class JobScheduleBO   {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("jobName")
  private String jobName = null;

  @JsonProperty("jobScheduleTime")
  private String jobScheduleTime = null;

  public JobScheduleBO id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public JobScheduleBO jobName(String jobName) {
    this.jobName = jobName;
    return this;
  }

   /**
   * Get jobName
   * @return jobName
  **/
  @JsonProperty("jobName")
  @ApiModelProperty(value = "")
  public String getJobName() {
    return jobName;
  }

  public void setJobName(String jobName) {
    this.jobName = jobName;
  }

  public JobScheduleBO jobScheduleTime(String jobScheduleTime) {
    this.jobScheduleTime = jobScheduleTime;
    return this;
  }

   /**
   * Get jobScheduleTime
   * @return jobScheduleTime
  **/
  @JsonProperty("jobScheduleTime")
  @ApiModelProperty(value = "")
  public String getJobScheduleTime() {
    return jobScheduleTime;
  }

  public void setJobScheduleTime(String jobScheduleTime) {
    this.jobScheduleTime = jobScheduleTime;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    JobScheduleBO jobScheduleBO = (JobScheduleBO) o;
    return Objects.equals(this.id, jobScheduleBO.id) &&
        Objects.equals(this.jobName, jobScheduleBO.jobName) &&
        Objects.equals(this.jobScheduleTime, jobScheduleBO.jobScheduleTime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, jobName, jobScheduleTime);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class JobScheduleBO {\n");
    
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    jobName: ").append(toIndentedString(jobName)).append("\n");
    sb.append("    jobScheduleTime: ").append(toIndentedString(jobScheduleTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

