/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * ValidationType
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class ValidationType   {
  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("valTypeCode")
  private String valTypeCode = null;

  @JsonProperty("valTypeName")
  private String valTypeName = null;

  public ValidationType createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ValidationType createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ValidationType id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ValidationType updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ValidationType updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public ValidationType valTypeCode(String valTypeCode) {
    this.valTypeCode = valTypeCode;
    return this;
  }

   /**
   * Get valTypeCode
   * @return valTypeCode
  **/
  @JsonProperty("valTypeCode")
  @ApiModelProperty(value = "")
  public String getValTypeCode() {
    return valTypeCode;
  }

  public void setValTypeCode(String valTypeCode) {
    this.valTypeCode = valTypeCode;
  }

  public ValidationType valTypeName(String valTypeName) {
    this.valTypeName = valTypeName;
    return this;
  }

   /**
   * Get valTypeName
   * @return valTypeName
  **/
  @JsonProperty("valTypeName")
  @ApiModelProperty(value = "")
  public String getValTypeName() {
    return valTypeName;
  }

  public void setValTypeName(String valTypeName) {
    this.valTypeName = valTypeName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ValidationType validationType = (ValidationType) o;
    return Objects.equals(this.createdBy, validationType.createdBy) &&
        Objects.equals(this.createdDate, validationType.createdDate) &&
        Objects.equals(this.id, validationType.id) &&
        Objects.equals(this.updatedBy, validationType.updatedBy) &&
        Objects.equals(this.updatedDate, validationType.updatedDate) &&
        Objects.equals(this.valTypeCode, validationType.valTypeCode) &&
        Objects.equals(this.valTypeName, validationType.valTypeName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(createdBy, createdDate, id, updatedBy, updatedDate, valTypeCode, valTypeName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ValidationType {\n");
    
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    valTypeCode: ").append(toIndentedString(valTypeCode)).append("\n");
    sb.append("    valTypeName: ").append(toIndentedString(valTypeName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

