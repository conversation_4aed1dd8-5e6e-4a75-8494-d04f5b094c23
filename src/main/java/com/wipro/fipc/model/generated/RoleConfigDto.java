/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * RoleConfigDto
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2025-06-20T12:30:13.810+05:30")
public class RoleConfigDto   {
  @JsonProperty("businessOps")
  private Integer businessOps = null;

  @JsonProperty("businessUnit")
  private Integer businessUnit = null;

  @JsonProperty("clientCode")
  private String clientCode = null;

  @JsonProperty("clientId")
  private String clientId = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("opsName")
  private String opsName = null;

  @JsonProperty("processType")
  private Long processType = null;

  @JsonProperty("role")
  private String role = null;

  @JsonProperty("type")
  private String type = null;

  @JsonProperty("unitName")
  private String unitName = null;

  public RoleConfigDto businessOps(Integer businessOps) {
    this.businessOps = businessOps;
    return this;
  }

   /**
   * Get businessOps
   * @return businessOps
  **/
  @JsonProperty("businessOps")
  @ApiModelProperty(value = "")
  public Integer getBusinessOps() {
    return businessOps;
  }

  public void setBusinessOps(Integer businessOps) {
    this.businessOps = businessOps;
  }

  public RoleConfigDto businessUnit(Integer businessUnit) {
    this.businessUnit = businessUnit;
    return this;
  }

   /**
   * Get businessUnit
   * @return businessUnit
  **/
  @JsonProperty("businessUnit")
  @ApiModelProperty(value = "")
  public Integer getBusinessUnit() {
    return businessUnit;
  }

  public void setBusinessUnit(Integer businessUnit) {
    this.businessUnit = businessUnit;
  }

  public RoleConfigDto clientCode(String clientCode) {
    this.clientCode = clientCode;
    return this;
  }

   /**
   * Get clientCode
   * @return clientCode
  **/
  @JsonProperty("clientCode")
  @ApiModelProperty(value = "")
  public String getClientCode() {
    return clientCode;
  }

  public void setClientCode(String clientCode) {
    this.clientCode = clientCode;
  }

  public RoleConfigDto clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

   /**
   * Get clientId
   * @return clientId
  **/
  @JsonProperty("clientId")
  @ApiModelProperty(value = "")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public RoleConfigDto id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public RoleConfigDto opsName(String opsName) {
    this.opsName = opsName;
    return this;
  }

   /**
   * Get opsName
   * @return opsName
  **/
  @JsonProperty("opsName")
  @ApiModelProperty(value = "")
  public String getOpsName() {
    return opsName;
  }

  public void setOpsName(String opsName) {
    this.opsName = opsName;
  }

  public RoleConfigDto processType(Long processType) {
    this.processType = processType;
    return this;
  }

   /**
   * Get processType
   * @return processType
  **/
  @JsonProperty("processType")
  @ApiModelProperty(value = "")
  public Long getProcessType() {
    return processType;
  }

  public void setProcessType(Long processType) {
    this.processType = processType;
  }

  public RoleConfigDto role(String role) {
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @JsonProperty("role")
  @ApiModelProperty(value = "")
  public String getRole() {
    return role;
  }

  public void setRole(String role) {
    this.role = role;
  }

  public RoleConfigDto type(String type) {
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @JsonProperty("type")
  @ApiModelProperty(value = "")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public RoleConfigDto unitName(String unitName) {
    this.unitName = unitName;
    return this;
  }

   /**
   * Get unitName
   * @return unitName
  **/
  @JsonProperty("unitName")
  @ApiModelProperty(value = "")
  public String getUnitName() {
    return unitName;
  }

  public void setUnitName(String unitName) {
    this.unitName = unitName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RoleConfigDto roleConfigDto = (RoleConfigDto) o;
    return Objects.equals(this.businessOps, roleConfigDto.businessOps) &&
        Objects.equals(this.businessUnit, roleConfigDto.businessUnit) &&
        Objects.equals(this.clientCode, roleConfigDto.clientCode) &&
        Objects.equals(this.clientId, roleConfigDto.clientId) &&
        Objects.equals(this.id, roleConfigDto.id) &&
        Objects.equals(this.opsName, roleConfigDto.opsName) &&
        Objects.equals(this.processType, roleConfigDto.processType) &&
        Objects.equals(this.role, roleConfigDto.role) &&
        Objects.equals(this.type, roleConfigDto.type) &&
        Objects.equals(this.unitName, roleConfigDto.unitName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessOps, businessUnit, clientCode, clientId, id, opsName, processType, role, type, unitName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RoleConfigDto {\n");
    
    sb.append("    businessOps: ").append(toIndentedString(businessOps)).append("\n");
    sb.append("    businessUnit: ").append(toIndentedString(businessUnit)).append("\n");
    sb.append("    clientCode: ").append(toIndentedString(clientCode)).append("\n");
    sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    opsName: ").append(toIndentedString(opsName)).append("\n");
    sb.append("    processType: ").append(toIndentedString(processType)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    unitName: ").append(toIndentedString(unitName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

