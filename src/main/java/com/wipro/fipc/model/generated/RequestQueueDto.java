/*
 * Generic REST API
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model.generated;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.*;

/**
 * RequestQueueDto
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2023-10-06T15:30:30.496+05:30")
public class RequestQueueDto   {
  @JsonProperty("assignedBy")
  private String assignedBy = null;

  @JsonProperty("currentAssignee")
  private String currentAssignee = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("previousAssignee")
  private String previousAssignee = null;

  public RequestQueueDto assignedBy(String assignedBy) {
    this.assignedBy = assignedBy;
    return this;
  }

   /**
   * Get assignedBy
   * @return assignedBy
  **/
  @JsonProperty("assignedBy")
  @ApiModelProperty(value = "")
  public String getAssignedBy() {
    return assignedBy;
  }

  public void setAssignedBy(String assignedBy) {
    this.assignedBy = assignedBy;
  }

  public RequestQueueDto currentAssignee(String currentAssignee) {
    this.currentAssignee = currentAssignee;
    return this;
  }

   /**
   * Get currentAssignee
   * @return currentAssignee
  **/
  @JsonProperty("currentAssignee")
  @ApiModelProperty(value = "")
  public String getCurrentAssignee() {
    return currentAssignee;
  }

  public void setCurrentAssignee(String currentAssignee) {
    this.currentAssignee = currentAssignee;
  }

  public RequestQueueDto id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public RequestQueueDto previousAssignee(String previousAssignee) {
    this.previousAssignee = previousAssignee;
    return this;
  }

   /**
   * Get previousAssignee
   * @return previousAssignee
  **/
  @JsonProperty("previousAssignee")
  @ApiModelProperty(value = "")
  public String getPreviousAssignee() {
    return previousAssignee;
  }

  public void setPreviousAssignee(String previousAssignee) {
    this.previousAssignee = previousAssignee;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RequestQueueDto requestQueueDto = (RequestQueueDto) o;
    return Objects.equals(this.assignedBy, requestQueueDto.assignedBy) &&
        Objects.equals(this.currentAssignee, requestQueueDto.currentAssignee) &&
        Objects.equals(this.id, requestQueueDto.id) &&
        Objects.equals(this.previousAssignee, requestQueueDto.previousAssignee);
  }

  @Override
  public int hashCode() {
    return Objects.hash(assignedBy, currentAssignee, id, previousAssignee);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RequestQueueDto {\n");
    
    sb.append("    assignedBy: ").append(toIndentedString(assignedBy)).append("\n");
    sb.append("    currentAssignee: ").append(toIndentedString(currentAssignee)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    previousAssignee: ").append(toIndentedString(previousAssignee)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

