package com.wipro.fipc.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.model.generated.ProcessJobMapping;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.*;

/**
 * TicketCreationConfigCustom
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2020-03-02T16:44:52.246+05:30")
public class TicketCreationConfigCustom   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("assignee")
  private String assignee = null;

  @JsonProperty("attachment")
  private String[] attachment = null;
  
  @JsonProperty("unsecuredAttachment")
  private String[] unsecuredAttachment = null;

  @JsonProperty("billingNumDesc")
  private String billingNumDesc = null;

  @JsonProperty("billingNumber")
  private String billingNumber = null;

  @JsonProperty("businessArea")
  private String businessArea = null;

  @JsonProperty("complexity")
  private String complexity = null;

  @JsonProperty("controlAccount")
  private String controlAccount = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("division")
  private String division = null;

  @JsonProperty("dueDays")
  private Integer dueDays = null;

  @JsonProperty("estimatedWorkHours")
  private Double estimatedWorkHours = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("interestedFields")
  private String[] interestedFields = null;

  @JsonProperty("interestedParties")
  private String interestedParties = null;

  @JsonProperty("iteration")
  private String iteration = null;

  @JsonProperty("newDiscussion")
  private String newDiscussion = null;

  @JsonProperty("percentComplete")
  private String percentComplete = null;

  @JsonProperty("priority")
  private String priority = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("projectId")
  private String projectId = null;

  @JsonProperty("responsibleParty")
  private String responsibleParty = null;

  @JsonProperty("sdlcDiscipline")
  private String sdlcDiscipline = null;

  @JsonProperty("serviceGroup")
  private String serviceGroup = null;

  @JsonProperty("status")
  private String status = null;

  @JsonProperty("taskOwner")
  private String taskOwner = null;

  @JsonProperty("taskType")
  private String taskType = null;

  @JsonProperty("ticketType")
  private String ticketType = null;

  @JsonProperty("title")
  private String title = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("workPackage")
  private String workPackage = null;

  public TicketCreationConfigCustom activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TicketCreationConfigCustom assignee(String assignee) {
    this.assignee = assignee;
    return this;
  }

   /**
   * Get assignee
   * @return assignee
  **/
  @JsonProperty("assignee")
  @ApiModelProperty(value = "")
  public String getAssignee() {
    return assignee;
  }

  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public TicketCreationConfigCustom attachment(String[] attachment) {
    this.attachment = attachment;
    return this;
  }

   /**
   * Get attachment
   * @return attachment
  **/
  @JsonProperty("attachment")
  @ApiModelProperty(value = "")
  public String[] getAttachment() {
    return attachment;
  }

  public void setAttachment(String[] attachment) {
    this.attachment = attachment;
  }
  public TicketCreationConfigCustom unsecuredAttachment(String[] unsecuredAttachment) {
	  this.unsecuredAttachment = unsecuredAttachment;
	  return this;
  }
  
  /**
   * Get attachment
   * @return attachment
   **/
  @JsonProperty("unsecuredAttachment")
  @ApiModelProperty(value = "")
  public String[] getUnsecuredAttachment() {
	  return unsecuredAttachment;
  }
  
  public void setUnsecuredAttachment(String[] unsecuredAttachment) {
	  this.unsecuredAttachment = unsecuredAttachment;
  }

  public TicketCreationConfigCustom billingNumDesc(String billingNumDesc) {
    this.billingNumDesc = billingNumDesc;
    return this;
  }

   /**
   * Get billingNumDesc
   * @return billingNumDesc
  **/
  @JsonProperty("billingNumDesc")
  @ApiModelProperty(value = "")
  public String getBillingNumDesc() {
    return billingNumDesc;
  }

  public void setBillingNumDesc(String billingNumDesc) {
    this.billingNumDesc = billingNumDesc;
  }

  public TicketCreationConfigCustom billingNumber(String billingNumber) {
    this.billingNumber = billingNumber;
    return this;
  }

   /**
   * Get billingNumber
   * @return billingNumber
  **/
  @JsonProperty("billingNumber")
  @ApiModelProperty(value = "")
  public String getBillingNumber() {
    return billingNumber;
  }

  public void setBillingNumber(String billingNumber) {
    this.billingNumber = billingNumber;
  }

  public TicketCreationConfigCustom businessArea(String businessArea) {
    this.businessArea = businessArea;
    return this;
  }

   /**
   * Get businessArea
   * @return businessArea
  **/
  @JsonProperty("businessArea")
  @ApiModelProperty(value = "")
  public String getBusinessArea() {
    return businessArea;
  }

  public void setBusinessArea(String businessArea) {
    this.businessArea = businessArea;
  }

  public TicketCreationConfigCustom complexity(String complexity) {
    this.complexity = complexity;
    return this;
  }

   /**
   * Get complexity
   * @return complexity
  **/
  @JsonProperty("complexity")
  @ApiModelProperty(value = "")
  public String getComplexity() {
    return complexity;
  }

  public void setComplexity(String complexity) {
    this.complexity = complexity;
  }

  public TicketCreationConfigCustom controlAccount(String controlAccount) {
    this.controlAccount = controlAccount;
    return this;
  }

   /**
   * Get controlAccount
   * @return controlAccount
  **/
  @JsonProperty("controlAccount")
  @ApiModelProperty(value = "")
  public String getControlAccount() {
    return controlAccount;
  }

  public void setControlAccount(String controlAccount) {
    this.controlAccount = controlAccount;
  }

  public TicketCreationConfigCustom createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TicketCreationConfigCustom createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TicketCreationConfigCustom division(String division) {
    this.division = division;
    return this;
  }

   /**
   * Get division
   * @return division
  **/
  @JsonProperty("division")
  @ApiModelProperty(value = "")
  public String getDivision() {
    return division;
  }

  public void setDivision(String division) {
    this.division = division;
  }

  public TicketCreationConfigCustom dueDays(Integer dueDays) {
    this.dueDays = dueDays;
    return this;
  }

   /**
   * Get dueDays
   * @return dueDays
  **/
  @JsonProperty("dueDays")
  @ApiModelProperty(value = "")
  public Integer getDueDays() {
    return dueDays;
  }

  public void setDueDays(Integer dueDays) {
    this.dueDays = dueDays;
  }

  public TicketCreationConfigCustom estimatedWorkHours(Double estimatedWorkHours) {
    this.estimatedWorkHours = estimatedWorkHours;
    return this;
  }

   /**
   * Get estimatedWorkHours
   * @return estimatedWorkHours
  **/
  @JsonProperty("estimatedWorkHours")
  @ApiModelProperty(value = "")
  public Double getEstimatedWorkHours() {
    return estimatedWorkHours;
  }

  public void setEstimatedWorkHours(Double estimatedWorkHours) {
    this.estimatedWorkHours = estimatedWorkHours;
  }

  public TicketCreationConfigCustom id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TicketCreationConfigCustom interestedFields(String[] interestedFields) {
    this.interestedFields = interestedFields;
    return this;
  }

   /**
   * Get interestedFields
   * @return interestedFields
  **/
  @JsonProperty("interestedFields")
  @ApiModelProperty(value = "")
  public String[] getInterestedFields() {
    return interestedFields;
  }

  public void setInterestedFields(String[] interestedFields) {
    this.interestedFields = interestedFields;
  }

  public TicketCreationConfigCustom interestedParties(String interestedParties) {
    this.interestedParties = interestedParties;
    return this;
  }

   /**
   * Get interestedParties
   * @return interestedParties
  **/
  @JsonProperty("interestedParties")
  @ApiModelProperty(value = "")
  public String getInterestedParties() {
    return interestedParties;
  }

  public void setInterestedParties(String interestedParties) {
    this.interestedParties = interestedParties;
  }

  public TicketCreationConfigCustom iteration(String iteration) {
    this.iteration = iteration;
    return this;
  }

   /**
   * Get iteration
   * @return iteration
  **/
  @JsonProperty("iteration")
  @ApiModelProperty(value = "")
  public String getIteration() {
    return iteration;
  }

  public void setIteration(String iteration) {
    this.iteration = iteration;
  }

  public TicketCreationConfigCustom newDiscussion(String newDiscussion) {
    this.newDiscussion = newDiscussion;
    return this;
  }

   /**
   * Get newDiscussion
   * @return newDiscussion
  **/
  @JsonProperty("newDiscussion")
  @ApiModelProperty(value = "")
  public String getNewDiscussion() {
    return newDiscussion;
  }

  public void setNewDiscussion(String newDiscussion) {
    this.newDiscussion = newDiscussion;
  }

  public TicketCreationConfigCustom percentComplete(String percentComplete) {
    this.percentComplete = percentComplete;
    return this;
  }

   /**
   * Get percentComplete
   * @return percentComplete
  **/
  @JsonProperty("percentComplete")
  @ApiModelProperty(value = "")
  public String getPercentComplete() {
    return percentComplete;
  }

  public void setPercentComplete(String percentComplete) {
    this.percentComplete = percentComplete;
  }

  public TicketCreationConfigCustom priority(String priority) {
    this.priority = priority;
    return this;
  }

   /**
   * Get priority
   * @return priority
  **/
  @JsonProperty("priority")
  @ApiModelProperty(value = "")
  public String getPriority() {
    return priority;
  }

  public void setPriority(String priority) {
    this.priority = priority;
  }

  public TicketCreationConfigCustom processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TicketCreationConfigCustom projectId(String projectId) {
    this.projectId = projectId;
    return this;
  }

   /**
   * Get projectId
   * @return projectId
  **/
  @JsonProperty("projectId")
  @ApiModelProperty(value = "")
  public String getProjectId() {
    return projectId;
  }

  public void setProjectId(String projectId) {
    this.projectId = projectId;
  }

  public TicketCreationConfigCustom responsibleParty(String responsibleParty) {
    this.responsibleParty = responsibleParty;
    return this;
  }

   /**
   * Get responsibleParty
   * @return responsibleParty
  **/
  @JsonProperty("responsibleParty")
  @ApiModelProperty(value = "")
  public String getResponsibleParty() {
    return responsibleParty;
  }

  public void setResponsibleParty(String responsibleParty) {
    this.responsibleParty = responsibleParty;
  }

  public TicketCreationConfigCustom sdlcDiscipline(String sdlcDiscipline) {
    this.sdlcDiscipline = sdlcDiscipline;
    return this;
  }

   /**
   * Get sdlcDiscipline
   * @return sdlcDiscipline
  **/
  @JsonProperty("sdlcDiscipline")
  @ApiModelProperty(value = "")
  public String getSdlcDiscipline() {
    return sdlcDiscipline;
  }

  public void setSdlcDiscipline(String sdlcDiscipline) {
    this.sdlcDiscipline = sdlcDiscipline;
  }

  public TicketCreationConfigCustom serviceGroup(String serviceGroup) {
    this.serviceGroup = serviceGroup;
    return this;
  }

   /**
   * Get serviceGroup
   * @return serviceGroup
  **/
  @JsonProperty("serviceGroup")
  @ApiModelProperty(value = "")
  public String getServiceGroup() {
    return serviceGroup;
  }

  public void setServiceGroup(String serviceGroup) {
    this.serviceGroup = serviceGroup;
  }

  public TicketCreationConfigCustom status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @JsonProperty("status")
  @ApiModelProperty(value = "")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public TicketCreationConfigCustom taskOwner(String taskOwner) {
    this.taskOwner = taskOwner;
    return this;
  }

   /**
   * Get taskOwner
   * @return taskOwner
  **/
  @JsonProperty("taskOwner")
  @ApiModelProperty(value = "")
  public String getTaskOwner() {
    return taskOwner;
  }

  public void setTaskOwner(String taskOwner) {
    this.taskOwner = taskOwner;
  }

  public TicketCreationConfigCustom taskType(String taskType) {
    this.taskType = taskType;
    return this;
  }

   /**
   * Get taskType
   * @return taskType
  **/
  @JsonProperty("taskType")
  @ApiModelProperty(value = "")
  public String getTaskType() {
    return taskType;
  }

  public void setTaskType(String taskType) {
    this.taskType = taskType;
  }

  public TicketCreationConfigCustom ticketType(String ticketType) {
    this.ticketType = ticketType;
    return this;
  }

   /**
   * Get ticketType
   * @return ticketType
  **/
  @JsonProperty("ticketType")
  @ApiModelProperty(value = "")
  public String getTicketType() {
    return ticketType;
  }

  public void setTicketType(String ticketType) {
    this.ticketType = ticketType;
  }

  public TicketCreationConfigCustom title(String title) {
    this.title = title;
    return this;
  }

   /**
   * Get title
   * @return title
  **/
  @JsonProperty("title")
  @ApiModelProperty(value = "")
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public TicketCreationConfigCustom updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TicketCreationConfigCustom updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public TicketCreationConfigCustom workPackage(String workPackage) {
    this.workPackage = workPackage;
    return this;
  }

   /**
   * Get workPackage
   * @return workPackage
  **/
  @JsonProperty("workPackage")
  @ApiModelProperty(value = "")
  public String getWorkPackage() {
    return workPackage;
  }

  public void setWorkPackage(String workPackage) {
    this.workPackage = workPackage;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TicketCreationConfigCustom TicketCreationConfigCustom = (TicketCreationConfigCustom) o;
    return Objects.equals(this.activeFlag, TicketCreationConfigCustom.activeFlag) &&
        Objects.equals(this.assignee, TicketCreationConfigCustom.assignee) &&
        Objects.equals(this.attachment, TicketCreationConfigCustom.attachment) &&
        Objects.equals(this.unsecuredAttachment, TicketCreationConfigCustom.unsecuredAttachment) &&
        Objects.equals(this.billingNumDesc, TicketCreationConfigCustom.billingNumDesc) &&
        Objects.equals(this.billingNumber, TicketCreationConfigCustom.billingNumber) &&
        Objects.equals(this.businessArea, TicketCreationConfigCustom.businessArea) &&
        Objects.equals(this.complexity, TicketCreationConfigCustom.complexity) &&
        Objects.equals(this.controlAccount, TicketCreationConfigCustom.controlAccount) &&
        Objects.equals(this.createdBy, TicketCreationConfigCustom.createdBy) &&
        Objects.equals(this.createdDate, TicketCreationConfigCustom.createdDate) &&
        Objects.equals(this.division, TicketCreationConfigCustom.division) &&
        Objects.equals(this.dueDays, TicketCreationConfigCustom.dueDays) &&
        Objects.equals(this.estimatedWorkHours, TicketCreationConfigCustom.estimatedWorkHours) &&
        Objects.equals(this.id, TicketCreationConfigCustom.id) &&
        Objects.equals(this.interestedFields, TicketCreationConfigCustom.interestedFields) &&
        Objects.equals(this.interestedParties, TicketCreationConfigCustom.interestedParties) &&
        Objects.equals(this.iteration, TicketCreationConfigCustom.iteration) &&
        Objects.equals(this.newDiscussion, TicketCreationConfigCustom.newDiscussion) &&
        Objects.equals(this.percentComplete, TicketCreationConfigCustom.percentComplete) &&
        Objects.equals(this.priority, TicketCreationConfigCustom.priority) &&
        Objects.equals(this.processJobMapping, TicketCreationConfigCustom.processJobMapping) &&
        Objects.equals(this.projectId, TicketCreationConfigCustom.projectId) &&
        Objects.equals(this.responsibleParty, TicketCreationConfigCustom.responsibleParty) &&
        Objects.equals(this.sdlcDiscipline, TicketCreationConfigCustom.sdlcDiscipline) &&
        Objects.equals(this.serviceGroup, TicketCreationConfigCustom.serviceGroup) &&
        Objects.equals(this.status, TicketCreationConfigCustom.status) &&
        Objects.equals(this.taskOwner, TicketCreationConfigCustom.taskOwner) &&
        Objects.equals(this.taskType, TicketCreationConfigCustom.taskType) &&
        Objects.equals(this.ticketType, TicketCreationConfigCustom.ticketType) &&
        Objects.equals(this.title, TicketCreationConfigCustom.title) &&
        Objects.equals(this.updatedBy, TicketCreationConfigCustom.updatedBy) &&
        Objects.equals(this.updatedDate, TicketCreationConfigCustom.updatedDate) &&
        Objects.equals(this.workPackage, TicketCreationConfigCustom.workPackage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, assignee, attachment,unsecuredAttachment, billingNumDesc, billingNumber, businessArea, complexity, controlAccount, createdBy, createdDate, division, dueDays, estimatedWorkHours, id, interestedFields, interestedParties, iteration, newDiscussion, percentComplete, priority, processJobMapping, projectId, responsibleParty, sdlcDiscipline, serviceGroup, status, taskOwner, taskType, ticketType, title, updatedBy, updatedDate, workPackage);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TicketCreationConfigCustom {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    attachment: ").append(toIndentedString(attachment)).append("\n");
    sb.append("    unsecuredAttachment: ").append(toIndentedString(unsecuredAttachment)).append("\n");
    sb.append("    billingNumDesc: ").append(toIndentedString(billingNumDesc)).append("\n");
    sb.append("    billingNumber: ").append(toIndentedString(billingNumber)).append("\n");
    sb.append("    businessArea: ").append(toIndentedString(businessArea)).append("\n");
    sb.append("    complexity: ").append(toIndentedString(complexity)).append("\n");
    sb.append("    controlAccount: ").append(toIndentedString(controlAccount)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    division: ").append(toIndentedString(division)).append("\n");
    sb.append("    dueDays: ").append(toIndentedString(dueDays)).append("\n");
    sb.append("    estimatedWorkHours: ").append(toIndentedString(estimatedWorkHours)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    interestedFields: ").append(toIndentedString(interestedFields)).append("\n");
    sb.append("    interestedParties: ").append(toIndentedString(interestedParties)).append("\n");
    sb.append("    iteration: ").append(toIndentedString(iteration)).append("\n");
    sb.append("    newDiscussion: ").append(toIndentedString(newDiscussion)).append("\n");
    sb.append("    percentComplete: ").append(toIndentedString(percentComplete)).append("\n");
    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    projectId: ").append(toIndentedString(projectId)).append("\n");
    sb.append("    responsibleParty: ").append(toIndentedString(responsibleParty)).append("\n");
    sb.append("    sdlcDiscipline: ").append(toIndentedString(sdlcDiscipline)).append("\n");
    sb.append("    serviceGroup: ").append(toIndentedString(serviceGroup)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    taskOwner: ").append(toIndentedString(taskOwner)).append("\n");
    sb.append("    taskType: ").append(toIndentedString(taskType)).append("\n");
    sb.append("    ticketType: ").append(toIndentedString(ticketType)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    workPackage: ").append(toIndentedString(workPackage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

