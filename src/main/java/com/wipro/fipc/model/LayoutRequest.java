package com.wipro.fipc.model;


import java.util.List;

import lombok.Data;

@Data
public class LayoutRequest extends BaseRequest {
	private String id;
	private String processJobMappingId;	
	private String fileName;
	private String fileFormat;
	private String thresholdMin;
	private String thresholdMax;
	private String dateFormat;
	private String subject;
	private String sender;
	private String variance;
	private String fileType;
	private String delimiter;
	private String pptIdentifier;
	private String identifier;
	private String recordIdentifierCol;
	private String sheetName;
	private String sheetNameWoutSpace;
	private String fileNameWoutSpace;
	private String source;
	private String path;
	private String tool;
	private String domain;
	private String queryJCLName;
	private String dateGenerated;
	private String dateFrequency;
	private String datePeriod;
	private String dateInterval;
	private String prevReportFileName;
	private String prevReportFileNameWs;
	private String subfolder;
	private String action;
	private List useLabellingRpt;
	private List<String> whitelistSSN;
	private String client;
	private String database;
	private String sqlQuery;
	private String recordCntCheck;
	private List<LayoutRecord> headerRecords;
	private List<LayoutRecord> detailRecords;
	private List<LayoutRecord> trailerRecords;
	private String samplingCount;
	private String amountFormat;
	private String benePptIdentifier;
	private String benePptIdentifierType;
	private boolean verifyFileDateDetailRecord;
}
