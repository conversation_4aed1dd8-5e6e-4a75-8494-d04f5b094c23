/*
 * Generic REST API
 * Simple REST API Generation
 *
 * OpenAPI spec version: 0.0.1-SNAPSHOT
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


package com.wipro.fipc.model;

import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.model.generated.ProcessJobMapping;

import io.swagger.annotations.ApiModelProperty;

/**
 * ReportDataCleanse
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.JavaJerseyServerCodegen", date = "2020-06-26T11:04:43.234+05:30")
public class ReportDataCleanse   {
  @JsonProperty("action")
  private String action = null;

  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("dataElement")
  private String dataElement = null;

  @JsonProperty("fileName")
  private String fileName = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("length")
  private String length = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;

  @JsonProperty("values")
  private String values = null;

  public ReportDataCleanse action(String action) {
    this.action = action;
    return this;
  }

   /**
   * Get action
   * @return action
  **/
  @JsonProperty("action")
  @ApiModelProperty(value = "")
  public String getAction() {
    return action;
  }

  public void setAction(String action) {
    this.action = action;
  }

  public ReportDataCleanse activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public ReportDataCleanse createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ReportDataCleanse createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public ReportDataCleanse dataElement(String dataElement) {
    this.dataElement = dataElement;
    return this;
  }

   /**
   * Get dataElement
   * @return dataElement
  **/
  @JsonProperty("dataElement")
  @ApiModelProperty(value = "")
  public String getDataElement() {
    return dataElement;
  }

  public void setDataElement(String dataElement) {
    this.dataElement = dataElement;
  }

  public ReportDataCleanse fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @JsonProperty("fileName")
  @ApiModelProperty(value = "")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public ReportDataCleanse id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ReportDataCleanse length(String length) {
    this.length = length;
    return this;
  }

   /**
   * Get length
   * @return length
  **/
  @JsonProperty("length")
  @ApiModelProperty(value = "")
  public String getLength() {
    return length;
  }

  public void setLength(String length) {
    this.length = length;
  }

  public ReportDataCleanse processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public ReportDataCleanse recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public ReportDataCleanse updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ReportDataCleanse updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }

  public ReportDataCleanse values(String values) {
    this.values = values;
    return this;
  }

   /**
   * Get values
   * @return values
  **/
  @JsonProperty("values")
  @ApiModelProperty(value = "")
  public String getValues() {
    return values;
  }

  public void setValues(String values) {
    this.values = values;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReportDataCleanse reportDataCleanse = (ReportDataCleanse) o;
    return Objects.equals(this.action, reportDataCleanse.action) &&
        Objects.equals(this.activeFlag, reportDataCleanse.activeFlag) &&
        Objects.equals(this.createdBy, reportDataCleanse.createdBy) &&
        Objects.equals(this.createdDate, reportDataCleanse.createdDate) &&
        Objects.equals(this.dataElement, reportDataCleanse.dataElement) &&
        Objects.equals(this.fileName, reportDataCleanse.fileName) &&
        Objects.equals(this.id, reportDataCleanse.id) &&
        Objects.equals(this.length, reportDataCleanse.length) &&
        Objects.equals(this.processJobMapping, reportDataCleanse.processJobMapping) &&
        Objects.equals(this.recordIdentifier, reportDataCleanse.recordIdentifier) &&
        Objects.equals(this.updatedBy, reportDataCleanse.updatedBy) &&
        Objects.equals(this.updatedDate, reportDataCleanse.updatedDate) &&
        Objects.equals(this.values, reportDataCleanse.values);
  }

  @Override
  public int hashCode() {
    return Objects.hash(action, activeFlag, createdBy, createdDate, dataElement, fileName, id, length, processJobMapping, recordIdentifier, updatedBy, updatedDate, values);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReportDataCleanse {\n");
    
    sb.append("    action: ").append(toIndentedString(action)).append("\n");
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    dataElement: ").append(toIndentedString(dataElement)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    length: ").append(toIndentedString(length)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    values: ").append(toIndentedString(values)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

