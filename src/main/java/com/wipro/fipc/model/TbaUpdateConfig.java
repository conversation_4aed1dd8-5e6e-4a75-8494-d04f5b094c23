package com.wipro.fipc.model;

import java.sql.Date;

import lombok.Data;

@Data
public class TbaUpdateConfig {
	private Long id;

	private ProcessJobMapping processJobMapping;

	private String updateName;

	private char activeFlag;

	private String tbaFieldName;

	private String eventName;

	private int activityId;

	private int panelId;

	private int classId;

	private String jsonKey;

	private String baseKey;

	private String subKey;

	private String metadata;

	private int transId;

	private String value;

	private Date createdDate;

	private String createdBy;

	private String updatedBy;

	private Date updatedDate;
	
	private String overrideEdits;

}
