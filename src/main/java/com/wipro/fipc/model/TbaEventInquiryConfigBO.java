package com.wipro.fipc.model;

import java.util.Date;

import lombok.Data;

@Data
public class TbaEventInquiryConfigBO {
	private Long id;
	private Long processJobMappingId;
	private String eventName;
	private String eventInquiryDefName;
	private Integer panelId;
	private String tbaFieldName;
	private String jsonKey;
	private String metadata;
	private String baseKey;
	private String subKey;
	private String transId;
	private String parNm;
	private String effDateType;
	private String effFromDate;
	private String effToDate;
	private String effectiveDate;
	private String activeFlag;
	private String recordIdentifier;
	private String sequence;
	private Date createdDate;
	private String createdBy;
	private Date updatedDate;
	private String updatedBy;
	private String fieldType;
}