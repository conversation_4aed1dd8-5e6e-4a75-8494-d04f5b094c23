package com.wipro.fipc.model;

import java.util.List;

import com.wipro.fipc.model.generated.KsdFileDetails;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.TbaInquiryConfig;
import com.wipro.fipc.model.generated.TbaUpdateConfig;

import lombok.Data;

@Data
public class ProcessControlAttributeResponse {
	
	private List<KsdFileDetails> KsdList;
	private List<TbaUpdateConfig> mismatchActionList;
	private List<TbaInquiryConfig> tbaConfigList;
	private List<RulesConfig> rulesConfigList;
}
