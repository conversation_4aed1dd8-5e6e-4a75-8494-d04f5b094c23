package com.wipro.fipc.model;

import java.util.Date;
import java.util.HashMap;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class TemplateReportLayout {
	
	@JsonProperty("id")
	private String id;
	
	@JsonProperty("dataElement")
	private String dataElement;

	@JsonProperty("templateReportName")
	private String templateReportName;

	@JsonProperty("sheetName")
	private String sheetName;

	@JsonProperty("identifier")
	private String identifier;

	@JsonProperty("identifierId")
	private String identifierId;

	@JsonProperty("sequence")
	private String sequence;

	@JsonProperty("filedType")
	String filedType;

	@JsonProperty("activeFlag")
	private char activeFlag;

	@JsonProperty("createdDate")
	private Date createdDate;

	@JsonProperty("createdBy")
	private String createdBy;

	@JsonProperty("updatedBy")
	private String updatedBy;

	@JsonProperty("updatedDate")
	private Date updatedDate;
	
	@JsonProperty("sheetNameWs")
	private String sheetNameWs;
	       
	@JsonProperty("templateReportNameWs")
	private String templateReportNameWs;
	       
	@JsonProperty("dataElementWs")
	private String dataElementWs;
	       
	@JsonProperty("identifierWs")
	private String identifierWs;

	@JsonProperty("labellingReportRecord")
	private String labellingReportRecord = null;

	@JsonProperty
	private HashMap<String, String> templateReportUpload = new HashMap<>();

	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @return the dataElement
	 */
	public String getDataElement() {
		return dataElement;
	}

	/**
	 * @param dataElement the dataElement to set
	 */
	public void setDataElement(String dataElement) {
		this.dataElement = dataElement;
	}

	/**
	 * @return the templateReportName
	 */
	public String getTemplateReportName() {
		return templateReportName;
	}

	/**
	 * @param templateReportName the templateReportName to set
	 */
	public void setTemplateReportName(String templateReportName) {
		this.templateReportName = templateReportName;
	}

	/**
	 * @return the sheetName
	 */
	public String getSheetName() {
		return sheetName;
	}

	/**
	 * @param sheetName the sheetName to set
	 */
	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}

	/**
	 * @return the identifier
	 */
	public String getIdentifier() {
		return identifier;
	}

	/**
	 * @param identifier the identifier to set
	 */
	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	/**
	 * @return the identifierId
	 */
	public String getIdentifierId() {
		return identifierId;
	}

	/**
	 * @param identifierId the identifierId to set
	 */
	public void setIdentifierId(String identifierId) {
		this.identifierId = identifierId;
	}

	/**
	 * @return the sequence
	 */
	public String getSequence() {
		return sequence;
	}

	/**
	 * @param sequence the sequence to set
	 */
	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	/**
	 * @return the filedType
	 */
	public String getFiledType() {
		return filedType;
	}

	/**
	 * @param filedType the filedType to set
	 */
	public void setFiledType(String filedType) {
		this.filedType = filedType;
	}

	/**
	 * @return the activeFlag
	 */
	public char getActiveFlag() {
		return activeFlag;
	}

	/**
	 * @param activeFlag the activeFlag to set
	 */
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	/**
	 * @return the createdDate
	 */
	public Date getCreatedDate() {
		return createdDate;
	}

	/**
	 * @param createdDate the createdDate to set
	 */
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	/**
	 * @return the createdBy
	 */
	public String getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the updatedDate
	 */
	public Date getUpdatedDate() {
		return updatedDate;
	}

	/**
	 * @param updatedDate the updatedDate to set
	 */
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	/**
	 * @return the sheetNameWs
	 */
	public String getSheetNameWs() {
		return sheetNameWs;
	}

	/**
	 * @param sheetNameWs the sheetNameWs to set
	 */
	public void setSheetNameWs(String sheetNameWs) {
		this.sheetNameWs = sheetNameWs;
	}

	/**
	 * @return the templateReportNameWs
	 */
	public String getTemplateReportNameWs() {
		return templateReportNameWs;
	}

	/**
	 * @param templateReportNameWs the templateReportNameWs to set
	 */
	public void setTemplateReportNameWs(String templateReportNameWs) {
		this.templateReportNameWs = templateReportNameWs;
	}

	/**
	 * @return the dataElementWs
	 */
	public String getDataElementWs() {
		return dataElementWs;
	}

	/**
	 * @param dataElementWs the dataElementWs to set
	 */
	public void setDataElementWs(String dataElementWs) {
		this.dataElementWs = dataElementWs;
	}

	/**
	 * @return the identifierWs
	 */
	public String getIdentifierWs() {
		return identifierWs;
	}

	/**
	 * @param identifierWs the identifierWs to set
	 */
	public void setIdentifierWs(String identifierWs) {
		this.identifierWs = identifierWs;
	}

	/**
	 * @return the labellingReportRecord
	 */
	public String getLabellingReportRecord() {
		return labellingReportRecord;
	}

	/**
	 * @param labellingReportRecord the labellingReportRecord to set
	 */
	public void setLabellingReportRecord(String labellingReportRecord) {
		this.labellingReportRecord = labellingReportRecord;
	}

	/**
	 * @return the templateReportUpload
	 */
	public HashMap<String, String> getTemplateReportUpload() {
		return templateReportUpload;
	}

	/**
	 * @param templateReportUpload the templateReportUpload to set
	 */
	public void setTemplateReportUpload(HashMap<String, String> templateReportUpload) {
		this.templateReportUpload = templateReportUpload;
	}

//	@JsonProperty("templateReportUploadId")
	
}
