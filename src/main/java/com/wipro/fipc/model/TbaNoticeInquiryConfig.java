package com.wipro.fipc.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wipro.fipc.utils.CustomClientIdDeserializer;

import lombok.Data;

@Data
public class TbaNoticeInquiryConfig {
	
	private char activeFlag;
	private char addManualFlag;
	@JsonProperty("clientId")
    @JsonDeserialize(using = CustomClientIdDeserializer.class)
	private Integer clientId;
	private String createdBy;
	private Date createdDate;
	private String fieldType;
	private Long id;
	private String identifier;
	private String inquiryDefName;
	private String jsonKey;
	private String metadata;
	private Integer noticeId;
	private String noticeName;
	private String parNm;
	private Long processJobMappingId;
	private String recordIdentifier;
	private String subJsonKey;
	@JsonProperty("tba_field_name")
	private String tbaFieldName;
	private String updatedBy;
	private Date updatedDate;
	
	public TbaNoticeInquiryConfig() {
	}

	public TbaNoticeInquiryConfig( com.wipro.fipc.entity.tba.TbaNoticeInqConfig entity) {
		this.id = entity.getId();
		this.processJobMappingId = entity.getProcessJobMappingId();
		this.addManualFlag = entity.getAddManualFlag();
		this.noticeName = entity.getNoticeName();
		this.noticeId = entity.getNoticeId();
		this.clientId = entity.getClientId();
		this.inquiryDefName = entity.getInquiryDefName();
		this.tbaFieldName = entity.getTba_field_name();
		this.jsonKey = entity.getJsonKey();
		this.subJsonKey = entity.getSubJsonKey();
		this.metadata = entity.getMetadata();
		this.fieldType = entity.getFieldType();
		this.parNm = entity.getParNm();
		this.recordIdentifier = entity.getRecordIdentifier();
		this.identifier = entity.getIdentifier();
		this.activeFlag = entity.getActiveFlag();
		this.createdDate = entity.getCreatedDate();
		this.createdBy = entity.getCreatedBy();
		this.updatedDate = entity.getUpdatedDate();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public char getAddManualFlag() {
		return addManualFlag;
	}

	public void setAddManualFlag(char addManualFlag) {
		this.addManualFlag = addManualFlag;
	}

	public String getNoticeName() {
		return noticeName;
	}

	public void setNoticeName(String noticeName) {
		this.noticeName = noticeName;
	}

	public int getNoticeId() {
		return noticeId;
	}

	public void setNoticeId(int noticeId) {
		this.noticeId = noticeId;
	}

	public int getClientId() {
		return clientId;
	}

	public void setClientId(int clientId) {
		this.clientId = clientId;
	}

	public String getInquiryDefName() {
		return inquiryDefName;
	}

	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}

	public String getTbaFieldName() {
		return tbaFieldName;
	}

	public void setTbaFieldName(String tba_field_name) {
		this.tbaFieldName = tba_field_name;
	}

	public String getJsonKey() {
		return jsonKey;
	}

	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}

	public String getSubJsonKey() {
		return subJsonKey;
	}

	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}

	public String getMetadata() {
		return metadata;
	}

	public void setMetadata(String metadata) {
		this.metadata = metadata;
	}

	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}

	public String getParNm() {
		return parNm;
	}

	public void setParNm(String parNm) {
		this.parNm = parNm;
	}

	public String getRecordIdentifier() {
		return recordIdentifier;
	}

	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	@Override
	public String toString() {
		return "TbaNoticeInquiryConfig [id=" + id + ", processJobMappingId=" + processJobMappingId + ", addManualFlag="
				+ addManualFlag + ", noticeName=" + noticeName + ", noticeId=" + noticeId + ", clientId=" + clientId
				+ ", inquiryDefName=" + inquiryDefName + ", tbaFieldName=" + tbaFieldName + ", jsonKey=" + jsonKey
				+ ", subJsonKey=" + subJsonKey + ", metadata=" + metadata + ", fieldType=" + fieldType + ", parNm="
				+ parNm + ", recordIdentifier=" + recordIdentifier + ", identifier=" + identifier + ", activeFlag="
				+ activeFlag + ", createdDate=" + createdDate + ", createdBy=" + createdBy + ", updatedDate="
				+ updatedDate + "]";
	}

}
