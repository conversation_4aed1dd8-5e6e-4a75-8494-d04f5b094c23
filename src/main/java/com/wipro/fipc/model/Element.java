










package com.wipro.fipc.model;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Element {

	@JsonProperty("patternIdentification")
	private String patternIdentification;
	
	@JsonProperty("checkBoxSelection")
	private boolean checkBoxSelection = false;
	
	@JsonProperty("patternValue")
	private List<String> patternValue = new ArrayList<String>();
	
	@JsonProperty("recordIdentifier")
	private String recordIdentifier;

	/**
	 * @return the patternIdentification
	 */
	public String getPatternIdentification() {
		return patternIdentification;
	}

	/**
	 * @param patternIdentification the patternIdentification to set
	 */
	public void setPatternIdentification(String patternIdentification) {
		this.patternIdentification = patternIdentification;
	}

	/**
	 * @return the checkBoxSelection
	 */
	public boolean isCheckBoxSelection() {
		return checkBoxSelection;
	}

	/**
	 * @param checkBoxSelection the checkBoxSelection to set
	 */
	public void setCheckBoxSelection(boolean checkBoxSelection) {
		this.checkBoxSelection = checkBoxSelection;
	}

	/**
	 * @return the patternValue
	 */
	public List<String> getPatternValue() {
		return patternValue;
	}

	/**
	 * @param patternValue the patternValue to set
	 */
	public void setPatternValue(List<String> patternValue) {
		this.patternValue = patternValue;
	}

	/**
	 * @return the recordIdentifier
	 */
	public String getRecordIdentifier() {
		return recordIdentifier;
	}

	/**
	 * @param recordIdentifier the recordIdentifier to set
	 */
	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}
}