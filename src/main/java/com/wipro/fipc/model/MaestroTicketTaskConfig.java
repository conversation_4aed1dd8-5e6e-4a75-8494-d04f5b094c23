package com.wipro.fipc.model;

import java.util.List;

public class MaestroTicketTaskConfig {

	private List<TicketCreationConfigCustom> listTicketCreationConfigCustom;
	private List<TaskUpdateConfigCustom> listTaskUpdateConfigCustom;
	private MaestroTaskNameBO maestroTaskNameBO;



	public List<TicketCreationConfigCustom> getListTicketCreationConfigCustom() {
		return listTicketCreationConfigCustom;
	}

	public void setListTicketCreationConfigCustom(List<TicketCreationConfigCustom> listTicketCreationConfigCustom) {
		this.listTicketCreationConfigCustom = listTicketCreationConfigCustom;
	}

	public List<TaskUpdateConfigCustom> getListTaskUpdateConfigCustom() {
		return listTaskUpdateConfigCustom;
	}

	public void setListTaskUpdateConfigCustom(List<TaskUpdateConfigCustom> listTaskUpdateConfigCustom) {
		this.listTaskUpdateConfigCustom = listTaskUpdateConfigCustom;
	}

	public MaestroTaskNameBO getMaestroTaskNameBO() {
		return maestroTaskNameBO;
	}

	public void setMaestroTaskNameBO(MaestroTaskNameBO maestroTaskNameBO) {
		this.maestroTaskNameBO = maestroTaskNameBO;
	}

}
