package com.wipro.fipc.model;

import java.util.List;

import lombok.Data;

@Data
public class TbaCommonReport implements Cloneable {

	private Long id;
	private Long processJobMappingId;
	private String fileName;
	private String fileNameWoutSpace;
	private String fileType;
	private String fieldType;
	private String mfFieldName;
	private String mfFieldWoutSpace;
	private String recordIdentifier;
	private String sheetName;
	private String sheetNameWoutSpace;
	private String  subMetaDataId;
	private String subMetaData;
	private String fileFormatType="";
	private int panelId;
	private String transId;
	private List<TbaSubTransMapping> tbaSubTransMapping;

	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileNameWoutSpace() {
		return fileNameWoutSpace;
	}

	public void setFileNameWoutSpace(String fileNameWoutSpace) {
		this.fileNameWoutSpace = fileNameWoutSpace;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}

	public String getMfFieldName() {
		return mfFieldName;
	}

	public void setMfFieldName(String mfFieldName) {
		this.mfFieldName = mfFieldName;
	}

	public String getMfFieldWoutSpace() {
		return mfFieldWoutSpace;
	}

	public void setMfFieldWoutSpace(String mfFieldWoutSpace) {
		this.mfFieldWoutSpace = mfFieldWoutSpace;
	}

	public String getRecordIdentifier() {
		return recordIdentifier;
	}

	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}

	public String getSheetName() {
		return sheetName;
	}

	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}

	public String getSheetNameWoutSpace() {
		return sheetNameWoutSpace;
	}

	public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
		this.sheetNameWoutSpace = sheetNameWoutSpace;
	}


	public String getSubMetaDataId() {
		return subMetaDataId;
	}


	public void setSubMetaDataId(String subMetaDataId) {
		this.subMetaDataId = subMetaDataId;
	}


	public String getSubMetaData() {
		return subMetaData;
	}


	public void setSubMetaData(String subMetaData) {
		this.subMetaData = subMetaData;
	}


	public String getFileFormatType() {
		return fileFormatType;
	}

	public void setFileFormatType(String fileFormatType) {
		this.fileFormatType = fileFormatType;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	@Override
	public String toString() {
		return "TbaCommonReport [id=" + id + ", processJobMappingId=" + processJobMappingId + ", fileName=" + fileName
				+ ", fileNameWoutSpace=" + fileNameWoutSpace + ", fileType=" + fileType + ", fieldType=" + fieldType
				+ ", mfFieldName=" + mfFieldName + ", mfFieldWoutSpace=" + mfFieldWoutSpace + ", recordIdentifier="
				+ recordIdentifier + ", sheetName=" + sheetName + ", sheetNameWoutSpace=" + sheetNameWoutSpace
				+ ", subMetaDataId=" + subMetaDataId + ", subMetaData=" + subMetaData + ", fileFormatType="
				+ fileFormatType + "]";
	}

}
