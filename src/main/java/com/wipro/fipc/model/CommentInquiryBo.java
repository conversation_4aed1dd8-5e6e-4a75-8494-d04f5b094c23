



























package com.wipro.fipc.model;

import java.util.Date;


import lombok.Data;

@Data
public class CommentInquiryBo {

	private String activeFlag;
	private String createdBy;
	private Date createdDate;
	private String eftFromDate;
	private String eftToDate;
	private String fieldType;
	private Long id;
	private String inquiryDefName;
	private String jsonKey;
	private String parNM;
	private Long processJobMappingId;
	private String subJsonKey;
	private String tbaFieldName;
	private String updatedBy;
	private Date updatedDate;
	private String identifier;
	private String sheetName;
	private String sheetNameWoutSpace;
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public String getEftFromDate() {
		return eftFromDate;
	}
	public void setEftFromDate(String eftFromDate) {
		this.eftFromDate = eftFromDate;
	}
	public String getEftToDate() {
		return eftToDate;
	}
	public void setEftToDate(String eftToDate) {
		this.eftToDate = eftToDate;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getInquiryDefName() {
		return inquiryDefName;
	}
	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}
	public String getJsonKey() {
		return jsonKey;
	}
	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}
	public String getParNM() {
		return parNM;
	}
	public void setParNM(String parNM) {
		this.parNM = parNM;
	}
	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}
	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}
	public String getSubJsonKey() {
		return subJsonKey;
	}
	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}
	public String getTbaFieldName() {
		return tbaFieldName;
	}
	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public String getIdentifier() {
		return identifier;
	}
	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}
	public String getSheetName() {
		return sheetName;
	}
	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}
	public String getSheetNameWoutSpace() {
		return sheetNameWoutSpace;
	}
	public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
		this.sheetNameWoutSpace = sheetNameWoutSpace;
	}

}