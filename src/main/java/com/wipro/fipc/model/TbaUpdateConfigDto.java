package com.wipro.fipc.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wipro.fipc.model.generated.ProcessJobMapping;
import com.wipro.fipc.utils.CustomClientIdDeserializer;

import lombok.Data;

@Data
public class TbaUpdateConfigDto {

	private Long id;
	private Long processJobMappingId;
	private String tbaFieldName;
	private String eventName;
	private String updateName;
	private int panelId;
	private String basicInfo;
	@JsonProperty("clientId")
	@JsonDeserialize(using = CustomClientIdDeserializer.class)
	private int clientId;
	private String metaData;
	private String subMetaData;
	private Object additionalMetaData;
	private String identifier;
	private String sequence;
	private String recordIdentifier;
	private String parNm;
	private String subKey;
	private String baseKey;
	private String jsonKey;
	private String addManualFlag;
	private String activeFlag;
	private String createdBy;
	private String updatedBy;
	private Date createdDate;
	private Date updatedDate;
	private ProcessJobMapping processJobMapping;
	private String rerunFlag;
	private String tbaUpdateAction;
	private String[] overrideEdits;
	private String actLngDesc;
	private String transId;
	private String jsonKeyId;
	private String subMetaDataId;
	private String eventLongDesc;
	private int parentTbaUpdateId;
	private String[] estimateTbaInquiries;
	private boolean groupRelatedPanels;
	private boolean pickFromPendingEvent;
	@JsonProperty("skipTrans")
	private boolean skipTrans;
}
