package com.wipro.fipc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Question {

	 @JsonProperty("fileName")
	  private String fileName = null;
	 
	 @JsonProperty("fileType")
	  private String fileType = null;
	 
	 @JsonProperty("flag")
	  private String flag = null;

	 /**
	public Question(String fileName, String fileType, String flag) {
		super();
		this.fileName = fileName;
		this.fileType = fileType;
		this.flag = flag;
	}
	 
	**/
}
