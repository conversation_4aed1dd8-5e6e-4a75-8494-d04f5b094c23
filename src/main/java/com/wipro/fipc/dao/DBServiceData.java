package com.wipro.fipc.dao;

import java.util.List;
import java.util.Map;
import javax.persistence.Query;

import com.wipro.fipc.entity.ColumnConditionParam;




public interface DBServiceData {
public ColumnConditionParam getSingalValue(Map<String, String> customQuerys);
public List<ColumnConditionParam> getMultiConditionValue(List<String> columnNames,List<String> columnConditions,List<String> columnValues);
public String getOperator(String operator);
public Query getMultipleData(List<String> valueList,Query query);
public Query getSingalValueParse(String value, Query query);
public Query getMultiValuesParse(List<String> listOfValue, Query query);
}