package com.wipro.fipc.dao.layoutrule;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.layoutrule.LayoutConfig;

public interface LayoutConfigDao extends BaseDao<LayoutConfig> {

	@Query("SELECT p FROM LayoutConfig p" + " WHERE p.mfFieldName  = :mfFieldName")
	List<LayoutConfig> withoutmftfield(@Param("mfFieldName") String mfFieldName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.LAYOUT_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.LAYOUT_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where u.file_name = ?2 and u.process_job_mapping_id = ?3", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by, @Param("file_name") String file_name,
			@Param("process_job_mapping_id") long processJobMappingId);

	@Query("select a from LayoutConfig a  where a.processJobMappingConfig.id =?1 and a.fileName =?2")
	List<LayoutConfig> getRequiredDetailsByLayoutConfig(Long pjmid, String fileName);
}
