package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.tba.TbaMatchConfig;

public interface TbaMatchConfigRepo
		extends CrudRepository<TbaMatchConfig, Serializable>, JpaSpecificationExecutor<TbaMatchConfig> {

	@Query("SELECT p FROM TbaMatchConfig p" + " WHERE upper(p.mfFieldName)  = :mfFieldName")
	List<TbaMatchConfig> withoutmftfieldMatchTba(@Param("mfFieldName") String mfFieldName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.TBA_MATCH_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.TBA_MATCH_CONFIG u set active_flag ='F',updated_by= ?1 where u.file_name = ?2 and u.process_job_mapping_id = ?3", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by, @Param("file_name") String file_Name,
			@Param("process_job_mapping_id") long processJobMappingId);

	@Query("select a from TbaMatchConfig a  where a.processJobMapping.id =?1 and" + "("
			+ "a.fileName like %?2% or a.fileNameDest like %?2% or a.actions like %?2%" + ")")
	List<TbaMatchConfig> getRequiredDetailsByTba(Long pjmid, String fileName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.TBA_MATCH_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2 and"
			+ "(" + "u.file_name like %?3% or u.file_name_dest like %?3% or u.actions like %?3%"
			+ ") ", nativeQuery = true)
	int updateByTbaMatchConfigs(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long processJobMappingId, @Param("fileName") String fileName);

	@Query("select count(a.id) from TbaMatchConfig a where processJobMapping.id =?1 and (fileName =?2 or fileName is null) and (reportIdentifier =?3 or reportIdentifier is null) and (mfFieldName =?4 or mfFieldName is null) and (	mfFieldWoutSpace =?5 or mfFieldWoutSpace is null) and (inquiryDefName =?6 or inquiryDefName is null) and (tbaFieldName =?7 or tbaFieldName is null) and (ruleName =?8 or ruleName is null) and (correctiveAction =?9 or correctiveAction is null) and (resultField =?10 or resultField is null) and (identifier =?11 or identifier is null) and (actions =?12 or actions is null) and (matchType =?13 or matchType is null) and (fileNameDest =?14 or fileNameDest is null) and (reportIdentifierDest =?15 or reportIdentifierDest is null) and (mfFieldNameDest =?16 or mfFieldNameDest is null) and (mfFieldWoutSpaceDest =?17 or mfFieldWoutSpaceDest is null) and (sheetName =?18 or sheetName is null) and (sheetNameWoutSpace =?19 or sheetNameWoutSpace is null) and (sheetNameDest =?20 or sheetNameDest is null) and (sheetNameDestWoutSpace =?21 or sheetNameDestWoutSpace is null) and (fileNameWoutSpace =?22 or fileNameWoutSpace is null) and (fileNameDestWoutSpace=?23 or fileNameDestWoutSpace is null) and (pptVerifyTba=?24 or pptVerifyTba is null) and activeFlag ='T'  ")
	Long checkForDuplicates(Long processJobMapping, String fileName, String reportIdentifier, String mfFieldName,
			String mfFieldWoutSpace, String inquiryDefName, String tbaFieldName, String ruleName,
			String correctiveAction, String resultField, String identifier, String actions, String matchType,
			String fileNameDest, String reportIdentifierDest, String mfFieldNameDest, String mfFieldWoutSpaceDest,
			String sheetName, String sheetNameWoutSpace, String sheetNameDest, String sheetNameDestWoutSpace,
			String fileNameWoutSpace, String fileNameDestWoutSpace, String pptVerifyTba);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.TBA_MATCH_CONFIG u set active_flag ='F',updated_by= ?1 where (u.file_name like %?2% or u.file_name_dest like %?2% or u.actions like %?2%) and u.process_job_mapping_id = ?3", nativeQuery = true)
	int commondelete2(@Param("updated_by") String updated_by, @Param("file_name") String file_Name,
			@Param("process_job_mapping_id") long process_job_mapping_id);

	@Query("select a from TbaMatchConfig a  where a.processJobMapping.id =?1 and a.ruleName =?2")
	List<TbaMatchConfig> getrulesdeleteTba(Long pjmid, String ruleName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.TBA_MATCH_CONFIG u set active_flag ='F',updated_by= ?3,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?1 and  u.rule_name =?2 and active_flag ='T'", nativeQuery = true)
	int updateByRulesTbaMatchConfigs(@Param("process_job_mapping_id") long processJobMappingId,
			@Param("rule_name") String rule_name, @Param("updated_by") String updated_by);

}