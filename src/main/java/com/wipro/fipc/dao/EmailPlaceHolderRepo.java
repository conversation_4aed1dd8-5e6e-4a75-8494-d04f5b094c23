package com.wipro.fipc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.entity.batch.EmailPlaceholderMaster;

public interface EmailPlaceHolderRepo extends JpaRepository<EmailPlaceholderMaster, Long> {

	@Query(value = "SELECT * FROM emails_scheduler.EMAIL_PLACEHOLDER_MASTER", nativeQuery = true)
	List<EmailPlaceholderMaster> getPlaceHolders();

}
