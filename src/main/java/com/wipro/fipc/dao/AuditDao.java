package com.wipro.fipc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.entity.Audit;

public interface AuditDao extends BaseDao<Audit> {
	@Query("SELECT a.createTimestamp FROM Audit a where a.processJobMappingId IN (:pjmId)")
    List<Long> getTimeStamp(@Param("pjmId")List<Long> pjmId);
}