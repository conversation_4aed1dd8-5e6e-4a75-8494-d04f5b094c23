package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.wipro.fipc.entity.common.MatchConditions;


@Repository
public interface MatchConditionRepository
		extends CrudRepository<MatchConditions, Serializable>, JpaSpecificationExecutor<MatchConditions> {

	@Query("SELECT p FROM MatchConditions p")
	public List<MatchConditions> list();
}
