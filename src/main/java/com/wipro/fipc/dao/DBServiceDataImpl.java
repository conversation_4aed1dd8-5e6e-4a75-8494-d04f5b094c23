package com.wipro.fipc.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.stereotype.Service;

import com.wipro.fipc.entity.ColumnConditionParam;


@Service("dbServiceData")
public class DBServiceDataImpl implements DBServiceData {

	@PersistenceContext
	EntityManager entityManager;

	private ColumnConditionParam colCondParam = new ColumnConditionParam();

	@Override
	public ColumnConditionParam getSingalValue(Map<String, String> customQuerys) {
		colCondParam.setColName(customQuerys.get("column_name"));
		colCondParam.setColCondion(customQuerys.get("column_condition"));
		colCondParam.setColValue(customQuerys.get("column_value"));
		colCondParam.setSort(customQuerys.get("sort"));
		return colCondParam;
	}

	@SuppressWarnings("null")
	@Override
	public List<ColumnConditionParam> getMultiConditionValue(List<String> columnNamesData,
			List<String> columnConditionsData, List<String> columnValuesData) {
		List<ColumnConditionParam> multiColCondParam = new ArrayList<>();

		for (int i = 0; i < columnNamesData.size(); i++) {
			ColumnConditionParam ccpObj = new ColumnConditionParam();
			ccpObj.setColName(columnNamesData.get(i));
			ccpObj.setColCondion(columnConditionsData.get(i));
			ccpObj.setColValue(columnValuesData.get(i));
			ccpObj.setSort("eq");
			multiColCondParam.add(ccpObj);
		}
		return multiColCondParam;
	}

	@Override
	public String getOperator(String operator) {
		switch (operator) {
		case "gt":
			operator = ">";
			break;
		case "lt":
			operator = "<";
			break;
		case "eq":
			operator = "=";
			break;
		case "gtEq":
			operator = ">=";
			break;
		case "ltEq":
			operator = "<=";
			break;
		case "ntlk":
			operator = " NOT LIKE ";
			break;
		case "lk":
			operator = " LIKE ";
			break;
		case "is":
			operator = "is";
			break;

		default:
			throw new IllegalArgumentException("Invalid operator: " + operator);
		}

		return operator;
	}

	@Override
	public Query getMultipleData(List<String> valueList, Query queryMultiValue) {
		try {

			if (valueList.equals(Integer.parseInt(valueList.get(0))) == false) {
				queryMultiValue.setParameter(1, Integer.parseInt((String) valueList.get(0)));

			}
		} catch (NumberFormatException nfe) {

			queryMultiValue.setParameter(1, valueList.get(0));
		}
		try {

			if (valueList.equals(Integer.parseInt(valueList.get(1))) == false) {
				queryMultiValue.setParameter(2, Integer.parseInt((String) valueList.get(1)));

			}
		} catch (NumberFormatException nfe) {

			queryMultiValue.setParameter(2, valueList.get(1));
		}
		return queryMultiValue;
	}

	@Override
	public Query getSingalValueParse(String value, Query query) {
		try {
			if (value.equals(Integer.parseInt(value)) == false) {
				query.setParameter(1, Integer.parseInt((String) value));
			} else {
				query.setParameter(1, Integer.parseInt((String) value));
			}
		} catch (NumberFormatException nfe) {
			query.setParameter(1, value);
		}
		return query;
	}

	@Override
	public Query getMultiValuesParse(List<String> listOfValue, Query query) {
		try {

			if (listOfValue.equals(Integer.parseInt(listOfValue.get(0))) == false) {
				query.setParameter(1, Integer.parseInt((String) listOfValue.get(0)));
			}
		} catch (NumberFormatException nfe) {
			query.setParameter(1, listOfValue.get(0));
		}
		try {

			if (listOfValue.equals(Integer.parseInt(listOfValue.get(1))) == false) {
				query.setParameter(2, Integer.parseInt((String) listOfValue.get(1)));
			}
		} catch (NumberFormatException nfe) {

			query.setParameter(2, listOfValue.get(1));
		}
		return query;
	}
	
	

}
