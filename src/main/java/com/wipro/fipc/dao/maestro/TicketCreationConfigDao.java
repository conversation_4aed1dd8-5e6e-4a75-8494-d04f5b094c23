package com.wipro.fipc.dao.maestro;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;


public interface TicketCreationConfigDao extends BaseDao<TicketCreationConfig> {
	@Transactional
	@Modifying
	@Query(value = "UPDATE MAESTRO.TICKET_CREATION_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Query("select a from TicketCreationConfig a  where a.processJobMapping.id =?1 and" + "("
			+ "a.unsecuredAttachment like %?2% or a.attachment like %?2%" + ")")
	List<TicketCreationConfig> editfileName(Long pjmid, String attachment);
}
