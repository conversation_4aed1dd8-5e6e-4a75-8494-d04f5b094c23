package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaInquiryConfig;

public interface TbaInquiryConfigDao extends BaseDao<TbaInquiryConfig> {

	@Transactional

	 @Modifying
	 @Query(value = "UPDATE tba.TBA_INQUIRY_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2",nativeQuery = true)
	 int commonupdate(@Param("updated_by") String updated_by,@Param("process_job_mapping_id") long processJobMappingId);  
	
	 @Query("select count(p.id) from TbaInquiryConfig p where  p.processJobMapping.id =?1 and (p.inquiryName = ?2 or p.inquiryName is null) and p.panelId =?3 and (p.tbaFieldName = ?4 or p.tbaFieldName is null) and (p.jsonKey = ?5 or p.jsonKey is null) and (p.subJsonKey = ?6 or p.subJsonKey is null) and (p.metaData = ?7 or p.metaData is null) and (p.identifier = ?8 or p.identifier is null) and (p.fieldType = ?9 or p.fieldType is null) and (p.parNM = ?10 or p.parNM is null) and (p.recordIdentifier = ?11 or p.recordIdentifier is null) and (p.effDateType = ?12 or p.effDateType is null) and (p.effFromDate = ?13 or p.effFromDate is null) and (p.effToDate = ?14 or p.effToDate is null) and (p.rowMatrix = ?15 or p.rowMatrix is null) and (p.columnMatrix = ?16 or p.columnMatrix is null) and (p.sequence = ?17 or p.sequence is null) and (p.inquiryDefName = ?18 or p.inquiryDefName is null) and (p.conditionJson = ?19) and p.estimateMode=?20 and activeFlag ='T'  ")
	 Long checkForDuplicates(Long id, String inquiryName, int panelId, String tbaFieldName, String jsonKey,
				String subJsonKey, String metaData, String identifier, String fieldType, String parNM,
				String recordIdentifier, String effDateType, String effFromDate, String effToDate, String rowMatrix,
				String columnMatrix, String sequence,String inquiryDefName, String conditionJson, boolean estimateMode);
}
