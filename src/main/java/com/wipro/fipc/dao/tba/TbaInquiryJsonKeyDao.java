package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.wipro.fipc.entity.tba.TbaInquiryJsonKey;
import java.util.List;

@Repository
public interface TbaInquiryJsonKeyDao extends JpaRepository<TbaInquiryJsonKey, Long> {

	@Query("SELECT jsonKey,subJsonKey,fieldType FROM TbaInquiryJsonKey "
			+ "WHERE parNM = :parNM and tbaFieldName = :tbaFieldName and filterFlag = :filterFlag")
	String findByJsonKeyNameAndParName(@Param("parNM") String parNM, @Param("tbaFieldName") String tbaFieldName, @Param("filterFlag") Character fileterFlag);

	@Query("SELECT tba FROM TbaInquiryJsonKey tba WHERE tba.parNM = :parNM and tba.filterFlag = :filterFlag")
	List<TbaInquiryJsonKey> getTbaInquiryJsonKeyByDataFilter(@Param("parNM") String parNM,
			@Param("filterFlag") Character filterFlag);

	List<TbaInquiryJsonKey> findByParNMAndFilterFlag(String parNM, Character filterFlag);
}
