package com.wipro.fipc.dao.tba;

import java.util.List;
import org.springframework.data.jpa.repository.Query;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaCDDFieldMapping;

public interface TbaCDDFieldMappingDao extends BaseDao<TbaCDDFieldMapping>{
	
	@Query("SELECT m FROM TbaCDDFieldMapping m " + "WHERE m.panelId = :panelId and m.clientId =:clientId")
	List<TbaCDDFieldMapping> getTbaCDDFieldMapping(int panelId, int clientId);

}
