package com.wipro.fipc.dao.tba;

import java.util.Set;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaUpdateProcess;
import com.wipro.fipc.pojo.tba.TbaUpdateProcessDto;

public interface TbaUpdateProcessDao extends BaseDao<TbaUpdateProcess> {

	@Query("SELECT distinct(activityId) FROM TbaUpdateProcess WHERE eventName = :eventName and clientId = :clientId")
	int findActivityId(@Param("eventName") String eventName, @Param("clientId") int clientId);
	
	@Query("SELECT new com.wipro.fipc.pojo.tba.TbaUpdateProcessDto(t.processName, t.eventName, t.activityId, t.panelId, c.clientCode , a.basicInfo,  t.parNm, t.eventLongDesc) FROM TbaUpdateProcess t, TbaUpdateActivity a, ClientDetails c  "
			+ "WHERE t.panelId = a.panelId and t.activityId=a.activityId and t.clientId =:clientId and a.clientId = t.clientId and c.id = a.clientId")
	Set<TbaUpdateProcessDto> getEMUpdateInquiryNames(@Param("clientId") int clientId);
	
	@Query("SELECT new com.wipro.fipc.pojo.tba.TbaUpdateProcessDto(t.processName, t.eventName, t.activityId, t.panelId, CAST(t.clientId as string), a.basicInfo, t.parNm, t.eventLongDesc) FROM TbaUpdateProcess t, TbaUpdateActivity a " +
		       "WHERE t.panelId = a.panelId and t.activityId = a.activityId and t.clientId = :clientId and a.clientId = t.clientId")
		Set<TbaUpdateProcessDto> getUpdateInquiryNames(@Param("clientId") int clientId);
	
	@Query("SELECT tableType FROM TbaUpdateProcess "
	+ "WHERE activityId = :activityId and panelId = :panelId and clientId = :clientId")
	String findTableTypeByEventidAndPanelId(@Param("activityId") int activityId, @Param("panelId") int panelId, @Param("clientId") int clientId);
}
