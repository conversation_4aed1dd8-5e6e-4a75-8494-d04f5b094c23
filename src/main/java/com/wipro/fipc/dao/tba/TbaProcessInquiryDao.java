package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaProcessInquiry;

public interface TbaProcessInquiryDao extends BaseDao<TbaProcessInquiry> {

	@Query("SELECT distinct i FROM TbaProcessInquiry i " + "WHERE i.clientId=:clientId")
	List<TbaProcessInquiry> getInquiryNames(@Param("clientId") int clientId);

}
