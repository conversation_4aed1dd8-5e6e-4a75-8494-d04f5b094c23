package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.EventInquiryConfig;

public interface TbaEventInquiryConfigDao extends BaseDao<EventInquiryConfig> {
	@Transactional

	@Modifying
	@Query(value = "UPDATE tba.EVENT_INQUIRY_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2",nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by,@Param("process_job_mapping_id") long processJobMappingId);  


	@Query("select count(p.id) from EventInquiryConfig p where  p.processJobMapping.id =?1 and (p.eventName = ?2 or p.eventName is null) and p.panelId =?3 and (p.tbaFieldName = ?4 or p.tbaFieldName is null) and (p.jsonKey = ?5 or p.jsonKey is null) and (p.metadata = ?6 or p.metadata is null) and (p.parNm = ?7 or p.parNm is null) and (p.recordIdentifier = ?8 or p.recordIdentifier is null) and (p.effDateType = ?9 or p.effDateType is null) and (p.effFromDate = ?10 or p.effFromDate is null) and (p.effToDate = ?11 or p.effToDate is null) and (p.sequence = ?12 or p.sequence is null) and (p.eventInquiryDefName = ?13 or p.eventInquiryDefName is null) and (p.baseKey = ?14 or p.baseKey is null)and (p.fieldType = ?15 or p.fieldType is null) and p.pendingEvent=?16 and activeFlag ='T'    ")
	Long checkForDuplicates(Long id, String eventName, int panelId,
			String tbaFieldName, String jsonKey, String metaData,
			String parNm, String recordIdentifier, String effDateType, String
			effFromDate, String effToDate, String sequence,String
			eventInquiryDefName,String baseKey,String fieldType,boolean pendingEvent);

}
