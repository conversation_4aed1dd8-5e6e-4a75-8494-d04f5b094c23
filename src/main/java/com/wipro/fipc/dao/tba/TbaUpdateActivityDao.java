package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaUpdateActivity;

public interface TbaUpdateActivityDao extends BaseDao<TbaUpdateActivity> {

	@Query("SELECT t FROM TbaUpdateActivity t "
			+ "WHERE t.activityId = :activityId and t.panelId =:panelId and t.clientId=:clientId")
	TbaUpdateActivity findByActivityIdAndPanelId(@Param("activityId") int activityId, @Param("panelId") int panelId,
			@Param("clientId") int clientId);

}
