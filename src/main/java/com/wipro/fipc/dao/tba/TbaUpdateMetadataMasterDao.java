package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaUpdateMetaDataMaster;

public interface TbaUpdateMetadataMasterDao extends BaseDao<TbaUpdateMetaDataMaster> {

	public List<TbaUpdateMetaDataMaster> findByClientId(int clientId);
	
	@Query("SELECT m FROM TbaUpdateMetaDataMaster m " + "WHERE m.panelId = :panelId and m.clientId =:clientId")
	List<TbaUpdateMetaDataMaster> findByClientIdAndPanelId(int clientId, int panelId);
}