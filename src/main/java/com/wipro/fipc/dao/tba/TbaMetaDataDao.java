package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaMetaData;
import com.wipro.fipc.entity.tba.TbaProcessInquiry;

public interface TbaMetaDataDao extends BaseDao<TbaProcessInquiry> {
	@Query("SELECT m FROM TbaMetaData m " + "WHERE m.panelId = :panelId and m.clientId =:clientId")
	List<TbaMetaData> getTbaMetaData(int panelId, int clientId);

}
