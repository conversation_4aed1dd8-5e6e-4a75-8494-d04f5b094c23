package com.wipro.fipc.dao.tba;

import java.util.List;

import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.model.TbaWelfareConfigDto;
import com.wipro.fipc.pojo.CommonRowBO;

public interface TbaWelfareInquiryConfig {

	List<CommonResRowBO> saveIfNotDuplicate(List<TbaWelfareConfigDto> entity);
	com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig findById(Long id);
	List<CommonRowBO> deleteMultipleRows(int i, List<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> newLayout);
	List<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> findByColumn(String columnName, String columnValue);
}
