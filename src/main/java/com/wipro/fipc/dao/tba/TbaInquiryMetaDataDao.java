package com.wipro.fipc.dao.tba;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster;

public interface TbaInquiryMetaDataDao extends BaseDao<TbaInquiryMetaDataMaster> {
	@Query("SELECT m FROM TbaInquiryMetaDataMaster m " + "WHERE m.panelId = :panelId and m.clientId =:clientId and m.createdBy like %:createdBy%")
	Optional<List<TbaInquiryMetaDataMaster>> findByClientIdAndPanelIdAndCreatedBy(int clientId, int panelId, String createdBy);
	
	@Query("SELECT m FROM TbaInquiryMetaDataMaster m " + "WHERE m.clientId = :clientId and m.metadataType =:metadataType and m.attributeText=:attributeText")
	Optional<List<TbaInquiryMetaDataMaster>> findByClientIdAndMetaDataTypeAndShortDescription(int clientId, String metadataType, String attributeText);
	
	@Query("SELECT m FROM TbaInquiryMetaDataMaster m " + "WHERE m.clientId = :clientId and m.metadataType =:metadataType and m.trustCode IS NOT NULL and m.trustCode<> ''")
	Optional<List<TbaInquiryMetaDataMaster>> findTrustCodeMappingRecord(int clientId, String metadataType);
}
