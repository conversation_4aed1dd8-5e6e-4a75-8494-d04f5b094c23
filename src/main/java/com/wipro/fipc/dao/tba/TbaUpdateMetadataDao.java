package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaUpdateMetaData;

public interface TbaUpdateMetadataDao extends BaseDao<TbaUpdateMetaData> {

	@Query("SELECT m FROM TbaUpdateMetaData m "
			+ "WHERE m.panelId = :panelId and m.metaData =:metaData and m.clientId =:clientId")
	TbaUpdateMetaData findByMetadataAndPanelId(@Param("panelId") int panelId, @Param("metaData") String metaData,
			@Param("clientId") int clientId);

	@Query("SELECT m FROM TbaUpdateMetaData m " + "WHERE m.panelId = :panelId and m.clientId =:clientId")
	List<TbaUpdateMetaData> getTbaUpdateMetaData(int panelId, int clientId);

}