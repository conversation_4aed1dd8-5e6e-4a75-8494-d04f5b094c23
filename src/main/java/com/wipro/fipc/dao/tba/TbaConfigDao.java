package com.wipro.fipc.dao.tba;

import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.pojo.tba.TbaUpdateConfigDto;

public interface TbaConfigDao {
//	List<TbaUpdateConfig> saveUpdateConfig(List<TbaUpdateConfigDto> entity);

	List<CommonResRowBO> saveIfNotDuplicate(List<TbaUpdateConfigDto> entity) throws JsonProcessingException;
}
