package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaPaymentDescriptor;

public interface PaymentDescriptorDao extends BaseDao<TbaPaymentDescriptor> {

	@Query("SELECT m FROM TbaPaymentDescriptor m " + "WHERE m.panelId = :panelId and m.clientId =:clientId")
	List<TbaPaymentDescriptor> getTbaPaymentDescriptorData(int panelId, int clientId);
}
