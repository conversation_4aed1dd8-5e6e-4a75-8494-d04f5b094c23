package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaCommentInqConfig;

public interface TbaCommentInqConfigDao extends BaseDao<TbaCommentInqConfig> {
	@Query("select count(p) from TbaCommentInqConfig p where  ( p.eftFromDate = ?2 or p.eftFromDate is null)  and  ( p.eftToDate = ?3 or p.eftToDate is null) and p.tbaFieldName = ?4 and  p.jsonKey = ?5  and ( p.subJsonKey = ?6 or p.subJsonKey is null)  and  p.processJobMapping.id =?1  and p.fieldType = ?7  and p.parNM = ?8   and activeFlag ='T'  ")
	Long checkForDuplicates(Long id, String eftFromDate, String eftToDate, String tbaFieldName, String jsonKey,
			String subJsonKey, String fieldType, String parNM);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.TBA_COMMENT_INQ_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long process_job_mapping_id);
	
	List<TbaCommentInqConfig> findByProcessJobMappingIdAndUpdateFlagAndActiveFlag(Long valueOf, boolean isUpdate, char c);
}
