package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.tba.JobScheduleBO;
import com.wipro.fipc.pojo.CustomKsdNameBO;
import com.wipro.fipc.pojo.CustomPJMBO;
import com.wipro.fipc.pojo.CustomPJMDto;
import com.wipro.fipc.pojo.CustomProcessBO;

public interface ProcessJobMappingDao extends BaseDao<ProcessJobMapping> {

	@Query("SELECT p FROM ProcessJobMapping p " + "WHERE p.id = :id")
	ProcessJobMapping finaByProcessJobMappingId(@Param("id") long id);

	@Query("select distinct b.jobName from BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id where a.businessUnit.id=?1 and b.activeFlag = 'T' and  a.businessOps.id=?2 and b.clientDetails.id =?3 and b.process.id=?4 ")
	List<String> getJobNames(Long businessUnitID, Long businessOpsID, Long clientid, Long processID);

	@Query("select distinct b.id from BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id where a.businessUnit.id=?1 and a.businessOps.id=?2 and b.clientDetails.id =?3 and b.process.id=?4 ")
	List<Long> getPJMIDList(Long businessUnitID, Long businessOpsID, Long clientid, Long processID);

	@Query("select distinct new com.wipro.fipc.entity.tba.JobScheduleBO(c.id,b.jobName,c.jobScheduleTime) from   BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id join KsdConfig c on b.id=c.processJobMapping.id where a.businessUnit.id=?1 and a.businessOps.id=?2 and b.clientDetails.id =?3 and b.process.id=?4 and c.activeFlag='T'")
	List<JobScheduleBO> getJobScheduleTime(Long businessUnitID, Long businessOpsID, Long clientid, Long processID);

	@Query("select distinct b.eftSubject from BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id where a.businessUnit.id=?1 and a.businessOps.id=?2 and b.activeFlag = 'T' and b.clientDetails.id =?3 and b.process.id=?4 and b.jobName=?5 ")
	List<String> getEftNames(Long businessUnitID, Long businessOpsID, Long clientid, Long processID, String jobName);

	@Query("select p.id from  ProcessJobMapping p  where p.businessUnitOps.id=?1  and p.clientDetails.id =?2 and p.process.id=?3")
	List<Long> getpjmId(Long businessUnitID, Long clientid, Long processID);

	@Query("select p.id from  ProcessJobMapping p  where p.businessUnitOps.id=?1  and p.clientDetails.id =?2")
	List<Long> getpjmIdWithoutProcessId(Long businessUnitID, Long clientid);

	@Query("select distinct b.ksdName from BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id where a.businessUnit.id=?1 and b.clientDetails.id =?2 and b.activeFlag = 'T'")
	List<String> getListOfKsdName(Long businessUnitID, Long clientid);

	@Query("select b from ProcessJobMapping b  where b.ksdName=?1  and b.activeFlag = 'T'")
	List<ProcessJobMapping> getprocessJob(String ksdName);

	@Query("select new com.wipro.fipc.pojo.CustomPJMBO(a.businessOps.id,b.id,a.businessOps.opsName,b.process.processName,b.process.processType,b.jobName,b.eftSubject) from BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id where b.ksdName=?1 and b.activeFlag='T'")
	CustomPJMBO getBusinessOpsProcessDetailsByKsd(String ksdName);

	@Query("select new com.wipro.fipc.pojo.CustomPJMDto(b.id,a.businessUnit.unitName,b.clientDetails.id,b.clientDetails.clientName,b.clientDetails.clientCode,a.businessOps.id,a.businessOps.opsName,b.process.id,b.process.processName,b.process.processType,b.eftSubject,b.jobName,b.ksdName) from BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id where b.id=?1 and b.activeFlag='T'")
	CustomPJMDto getBusinessOpsProcessDetailByPjmid(Long pjmid);

	@Query("select distinct new com.wipro.fipc.pojo.CustomKsdNameBO(b.id,b.ksdName) from BusinessUnitOps a join ProcessJobMapping b on a.id=b.businessUnitOps.id where a.businessUnit.id=?1 and a.businessOps.id=?2 and b.activeFlag = 'T' and b.clientDetails.id =?3 and b.process.id=?4 and b.jobName=?5 and b.eftSubject =?6 ")
	List<CustomKsdNameBO> getKsdNamesAndIds(Long businessUnitID, Long businessOpsID, Long clientid, Long processID,
			String jobName, String eftSubject);

	@Query("Select pjm.id  FROM ProcessJobMapping pjm where pjm.clientDetails.clientCode IN (:clientCode)")
	List<Long> getPjmIdsByClientCode(@Param("clientCode") List<String> clientCode);

	@Query("select new com.wipro.fipc.pojo.CustomProcessBO(bi.id,bi.processName,bi.processType) from RoleConfig rl join Process bi on rl.process.id = bi.id  where rl.clientDetails.id=?3 and rl.businessUnit.id=?1 and rl.businessOps.id=?2")
	List<CustomProcessBO> getRequiredDetails(Long businessUnitID, Long businessOpsID, Long clientid);

}
