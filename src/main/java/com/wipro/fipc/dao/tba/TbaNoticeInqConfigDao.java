//package com.wipro.fipc.dao.tba;
//
//import org.springframework.data.jpa.repository.Modifying;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.data.repository.query.Param;
//import org.springframework.transaction.annotation.Transactional;
//
//import com.wipro.fipc.dao.BaseDao;
//import com.wipro.fipc.entity.tba.TbaNoticeInqConfig;
//
//public interface TbaNoticeInqConfigDao extends BaseDao<TbaNoticeInqConfig> {
//
//	@Transactional
//	@Modifying
//	@Query(value = "UPDATE tba.TBA_NOTICE_INQ_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
//	int commonupdate(@Param("updated_by") String updated_by,
//			@Param("process_job_mapping_id") long process_job_mapping_id);
//
//	@Query("select count(p.id) from TbaNoticeInqConfig p where  p.processJobMappingId =?1 and (p.noticeName = ?2 or p.noticeName is null) and p.noticeId =?3 and p.clientId =?4 and (p.tba_field_name = ?5 or p.tba_field_name is null) and (p.jsonKey = ?6 or p.jsonKey is null) and (p.subJsonKey = ?7 or p.subJsonKey is null) and (p.metadata = ?8 or p.metadata is null) and (p.fieldType = ?9 or p.fieldType is null) and (p.parNm = ?10 or p.parNm is null) and (p.recordIdentifier = ?11 or p.recordIdentifier is null) and (p.identifier = ?12 or p.identifier is null) and p.addManualFlag =?13 and activeFlag ='T'  ")
//	Long checkForDuplicates(Long id, String noticeName, int noticeId, int clientId, String tba_field_name,
//			String jsonKey, String subJsonKey, String metadata, String fieldType, String parNm, String recordIdentifier,
//			String identifier, char addManualFlag);
//}
