package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig;

public interface TbaWelfareInquiryConfigDao extends BaseDao<TbaWelfareInquiryConfig> {

	@Query(value = "select count (p.id) from tba.TBA_WELFARE_INQUIRY_CONFIG p where p.process_job_mapping_id =?1 and (p.practice_area_code=?2 or p.practice_area_code is null) and (p.tba_field_name=?3 or p.tba_field_name is null) and (p.json_key=?4 or p.json_key is null) and (p.sub_json_key=?5 or p.sub_json_key is null) and (p.field_type=?6 or p.field_type is null) and (p.par_name=?7 or p.par_name is null) and (p.event_name=?8 or p.event_name is null) and (p.event_long_desc=?9 or p.event_long_desc is null) and p.active_flag='T'", nativeQuery = true)
	Long checkForDuplicates(Long id, String practiceAreaCode, String tbaFieldName, String jsonKey, String subJsonKey, String fieldType, String parName, String eventName, String eventLongDes);

}
