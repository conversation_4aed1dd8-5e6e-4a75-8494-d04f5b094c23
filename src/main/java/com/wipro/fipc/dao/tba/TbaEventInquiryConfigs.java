package com.wipro.fipc.dao.tba;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.EventInquiryConfig;
import com.wipro.fipc.pojo.tba.TbaEventInquiryConfigDto;

public interface TbaEventInquiryConfigs {
	List<EventInquiryConfig> saveInquiryConfig(List<TbaEventInquiryConfigDto> entity) throws JsonProcessingException;
	
	List<CommonResRowBO> saveIfNotDuplicate(List<TbaEventInquiryConfigDto> entity) throws IllegalAccessException,
	InvocationTargetException, JsonParseEx<PERSON>, JsonMappingException, IOException;
}
