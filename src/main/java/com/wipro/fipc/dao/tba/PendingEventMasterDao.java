package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.TbaPendingEventMaster;

public interface PendingEventMasterDao extends BaseDao<TbaPendingEventMaster>{


@Query("SELECT p FROM TbaPendingEventMaster p"
            + " WHERE p.clientId = :clientId")
List<TbaPendingEventMaster> findByclientId(@Param("clientId") int clientId);

}

