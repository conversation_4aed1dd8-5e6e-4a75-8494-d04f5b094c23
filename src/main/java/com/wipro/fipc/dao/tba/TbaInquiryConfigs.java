package com.wipro.fipc.dao.tba;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaInquiryConfig;
import com.wipro.fipc.pojo.tba.TbaInquiryConfigDto;

public interface TbaInquiryConfigs {
	List<TbaInquiryConfig> saveInquiryConfig(List<TbaInquiryConfigDto> entity) throws JsonProcessingException;

	List<CommonResRowBO> saveIfNotDuplicate(List<TbaInquiryConfigDto> entity) throws IllegalAccessException,
			InvocationTargetException, JsonParseException, JsonMappingException, IOException;
}
