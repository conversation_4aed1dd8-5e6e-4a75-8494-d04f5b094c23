package com.wipro.fipc.dao.tba;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.TbaUpdateJsonKey;

public interface TbaUpdateJsonKeyDao extends BaseDao<TbaUpdateJsonKey> {

	@Query("SELECT baseKey,subKey,jsonKey FROM TbaUpdateJsonKey "
			+ "WHERE parNm = :parNm and tbaFieldName = :tbaFieldName")
	String findByJsonKeyNameAndPanelId(@Param("tbaFieldName") String tbaFieldName, @Param("parNm") String parNm);
	
	@Query("SELECT tbaFieldName FROM TbaUpdateJsonKey WHERE id = :jsonKeyId")
	String findTbaFieldNameById(@Param("jsonKeyId") Long jsonKeyId);
	
	@Query("SELECT baseKey,subKey,jsonKey FROM TbaUpdateJsonKey "
			+ "WHERE parNm = :parNm and tbaFieldName = :tbaFieldName")
	String findByJsonKeyNameAndParName(@Param("parNm") String parNm, @Param("tbaFieldName") String tbaFieldName);

	@Query("SELECT tba FROM TbaUpdateJsonKey tba WHERE tba.parNm = :parNm")
	List<TbaUpdateJsonKey> getTbaInquiryJsonKeyByDataFilter(@Param("parNm") String parNm);
	
	@Query("SELECT jsonKey,fieldType FROM TbaUpdateJsonKey "
			+ "WHERE parNm = :parNm and tbaFieldName = :tbaFieldName")
	String findByParNameAndTBAFieldName(@Param("parNm") String parNm, @Param("tbaFieldName") String tbaFieldName);

}
