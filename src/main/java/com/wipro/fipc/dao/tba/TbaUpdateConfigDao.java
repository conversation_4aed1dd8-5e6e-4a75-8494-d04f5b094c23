package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaUpdateConfig;

public interface TbaUpdateConfigDao extends BaseDao<TbaUpdateConfig> {

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.TBA_UPDATE_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Query("select count(p.id) from TbaUpdateConfig p where  p.processJobMapping.id =?1 and (p.updateName = ?2 or p.updateName is null) and (p.tbaFieldName = ?3 or p.tbaFieldName is null) and (p.eventName = ?4 or p.eventName is null) and p.activityId = ?5 and p.panelId = ?6 and p.classId = ?7 and (p.jsonKey = ?8 or p.jsonKey is null) and (p.baseKey = ?9 or p.baseKey is null)  and (p.subKey = ?10 or p.subKey is null) and (p.metaData = ?11 or p.metaData is null) and (p.transId = ?12 or p.transId is null) and (p.value = ?13 or p.value is null) and (p.recordIdentifier = ?14 or p.recordIdentifier is null) and (p.identifier = ?15 or p.identifier is null) and (p.parNm = ?16 or p.parNm is null) and p.rerunFlag = ?17 and p.addManualFlag = ?18 and (p.tbaUpdateAction = ?19 or p.tbaUpdateAction is null) and (p.overrideEdits = ?20 or p.overrideEdits is null) and (p.actLngDesc = ?21 or p.actLngDesc is null)and p.subMetaData = ?22 and p.subMetaDataId = ?23 and p.additionalMetaData = ?24 and (p.eventLongDesc = ?25 or p.eventLongDesc is null) and (p.parentTbaUpdateId = ?26 or p.parentTbaUpdateId is null) and (p.estimateTbaInquiries= ?27 or p.estimateTbaInquiries is null) and p.groupRelatedPanels is ?28 and p.pickFromPendingEvent is ?29 and p.skipTrans is ?30 and activeFlag ='T'  ")
	Long checkForDuplicates(Long id, String updateName, String tbaFieldName, String eventName, int activityId,
			int panelId, int classId, String jsonKey, String baseKey, String subKey, String metaData, String transId,
			String value, String recordIdentifier, String identifier, String parNm, char rerunFlag, char addManualFlag,
			String tbaUpdateAction, String overrideEdits, String actLngDesc, String subMetaData, String subMetaDataId,
			String additionalMetaData, String eventLongDesc, int parentTbaUpdateId, String estimateTbaInquiries, boolean groupRelatedPanels, boolean pickFromPendingEvent, boolean skipTrans);
}
