package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.EventHistInqConfig;

public interface EventHistInqConfigDao extends BaseDao<EventHistInqConfig> {

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.EVENT_HIST_INQ_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long process_job_mapping_id);

	@Query("select count(p) from EventHistInqConfig p where  p.eventName = ?2   and ( p.jsonKey = ?3 or p.j<PERSON><PERSON><PERSON> is null) and (p.parNm = ?4 or p.parNm is null) and p.effFromDate = ?6  and p.effToDate = ?7   and  p.processJobMappingId =?1  and p.activityId = ?5  and activeFlag ='T'  ")
	Long checkForDuplicates(Long pjmId, String eventName, String jsonKey, String parNm, int activityId,
			String effFromDate, String effToDate);

}
