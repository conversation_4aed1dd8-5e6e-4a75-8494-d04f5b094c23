package com.wipro.fipc.dao.batch;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.entity.batch.DailyTaskReport;

public interface DailyTaskReportDao extends JpaRepository<DailyTaskReport, Long> {
	@Query("SELECT dtr FROM DailyTaskReport dtr left join ProcessJobMapping pjm on "
			+ "dtr.processJobMappingId=pjm.id where pjm.clientDetails in (select cd.id from ClientDetails cd where cd.clientCode in "
			+ "(:clientCode)) and pjm.process in (:processId) and dtr.businessUnit = :businessUnit")

	List<DailyTaskReport> getDailyTaskReport(String businessUnit, @Param("clientCode") List<String> clientCode,
			@Param("processId") List<Long> processId);

}