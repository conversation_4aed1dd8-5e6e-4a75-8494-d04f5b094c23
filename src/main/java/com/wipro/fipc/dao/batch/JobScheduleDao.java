package com.wipro.fipc.dao.batch;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.batch.JobSchedule;

public interface JobScheduleDao extends BaseDao<JobSchedule> {

	
	@Query("select a.ksdConfigJobSchedule.id from JobSchedule a where a.id = ?1")
	List<Long> getByJobScheduleId(Long jobScheduleId);
	
	@Transactional
	@Modifying
	 @Query(value = "UPDATE emails_scheduler.job_schedule u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where KSD_CONFIG_ID IN(select id from emails_scheduler.ksd_config kc where kc.PROCESS_JOB_MAPPING_ID=?2)",nativeQuery = true)
	    int commonupdate(@Param("updated_by") String updated_by,@Param("process_job_mapping_id") long processJobMappingId);
	
}
	
