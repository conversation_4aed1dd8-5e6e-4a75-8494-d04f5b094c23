package com.wipro.fipc.dao.batch;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.wipro.fipc.entity.batch.HolidayCalendar;

import java.util.List;

@Repository
public interface HolidayCalendarDao extends JpaRepository<HolidayCalendar, Long> {

	@Query(value = "select count(p) from EMAILS_SCHEDULER.HOLIDAY_CALENDAR p where p.client_id=?1 and p.holiday_date=?2", nativeQuery = true)
	Long checkForDuplicate(Long clientId,String holidayDate);

	@Query(value="SELECT holiday_date FROM (select holiday_date, TO_DATE(holiday_date, 'DD/MM/YYYY') AS hdc from emails_scheduler.holiday_calendar where client_id=:clientId) AS subquery_alias \n" +
			"where hdc >= CURRENT_DATE ORDER BY hdc ASC" ,nativeQuery = true)
	List<String> holidayDates(Long clientId);

}