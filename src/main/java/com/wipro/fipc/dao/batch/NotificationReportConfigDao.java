package com.wipro.fipc.dao.batch;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.common.NotificationReportConfig;
import com.wipro.fipc.pojo.CustomNRCBo;

public interface NotificationReportConfigDao extends BaseDao<NotificationReportConfig> {

	@Transactional
	  @Modifying
		 @Query(value = "UPDATE common.NOTIFICATION_REPORT_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2",nativeQuery = true)
		 int commonupdate(@Param("updated_by") String updated_by,@Param("process_job_mapping_id") long processJobMappingId);  

	  @Query("select distinct new com.wipro.fipc.pojo.CustomNRCBo(p.id,p.application,p.file,p.fileType,p.activeFlag,p.reportName,p.subject,p.appendSubject,p.processJobMapping.id,p.subfolder,p.path,p.remarks,p.createdDate,p.createdBy,p.updatedBy,p.updatedDate) from NotificationReportConfig p where p.processJobMapping.id = ?1  and activeFlag='T' ")
		List<CustomNRCBo> getNRCRows(Long id); 
	  
	  @Query("select a from NotificationReportConfig a  where a.processJobMapping.id =?1 and a.reportName like %?2%")
	  List<NotificationReportConfig> editfileName(Long pjmid,String reportName);
	  
	  @Modifying
		 @Query(value = "UPDATE common.NOTIFICATION_REPORT_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2 and id=?3 " ,nativeQuery = true)
		 int notificationDelete(@Param("updated_by") String updated_by,@Param("process_job_mapping_id") long processJobMappingId,@Param("id") long id);  
}
