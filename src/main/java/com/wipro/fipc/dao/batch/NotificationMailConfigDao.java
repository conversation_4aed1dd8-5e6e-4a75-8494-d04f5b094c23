package com.wipro.fipc.dao.batch;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.common.NotificationMailConfig;
import com.wipro.fipc.pojo.CustomNMCBo;

public interface NotificationMailConfigDao extends BaseDao<NotificationMailConfig> {

	@Transactional
	@Modifying
	@Query(value = "UPDATE common.NOTIFICATION_MAIL_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Query("select distinct new com.wipro.fipc.pojo.CustomNMCBo(p.id,p.condition,p.toList,p.ccList,p.subject,p.activeFlag,p.appendSubject,p.interestedFields,p.attachmentName,p.remarks,p.createdDate,p.createdBy,p.updatedBy,p.updatedDate,p.processJobMapping.id) from NotificationMailConfig p where  p.processJobMapping.id = ?1 and activeFlag='T' ")
	List<CustomNMCBo> getNMCRows(Long id);

	@Query("select a from NotificationMailConfig a  where a.processJobMapping.id =?1 and a.attachmentName like %?2%")
	List<NotificationMailConfig> editfileName(Long pjmid, String attachmentName);

	@Modifying
	@Query(value = "UPDATE common.NOTIFICATION_MAIL_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2 and id=?3 ", nativeQuery = true)
	int notificationMaildelete(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long processJobMappingId, @Param("id") long id);

}
