package com.wipro.fipc.dao.batch;


import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.batch.KsdMaster;
import com.wipro.fipc.pojo.HWSBossKSDConfigReport;
import com.wipro.fipc.pojo.HWSBusinessKSDConfigReport;


public interface KsdMasterDao extends BaseDao<KsdMaster> {
	@Query("select a from KsdMaster a where a.primaryJobName=?1 and a.eftName=?2")
	List<KsdMaster> findKsdMasterByJobNameAndEftSubject(String jobName, String eftSubject);

	@Query("select distinct new com.wipro.fipc.pojo.HWSBusinessKSDConfigReport(pfc.businessUnitName,pjm.ksdName ,pfc.businessOpsName ,TRIM(pfc.configStatus),TRIM(pfc.clientName) ,TRIM(pfc.clientCode) , TRIM(pfc.jobName) ,TRIM(pfc.eftSubject),pfc.createdDate , pfc.updatedDate,pfc.approvedDate,TRIM(pfc.createdBy) ,NULLIF(TRIM(pfc.updatedBy),'') , NULLIF(TRIM(pfc.approvedBy),''), 'BUSINESS',concat('BUSINESS',' : ',TRIM(pfc.configStatus)),CASE     WHEN pjm.ksdName like 'HWE%' THEN 'MM' WHEN pjm.ksdName not like 'HWE%' THEN 'LM' END , pfc.processName, pfc.processType) from ProcessFeatureConfig pfc, ProcessJobMapping pjm where pjm.id = pfc.processJobMapping.id  and pfc.activeFlag = 'T' and pfc.createdBy not in ('A1019778','A1022374','A1022376','A1019233','qa_admin','A1022375') and pfc.createdDate < ?1  order by pfc.createdDate desc ")
    List<HWSBusinessKSDConfigReport> getHWSExcelList1( Date date);
    
    @Query("select distinct new com.wipro.fipc.pojo.HWSBusinessKSDConfigReport(pfc.businessUnitName,pjm.ksdName ,pfc.businessOpsName ,TRIM(pfc.configStatus),TRIM(pfc.clientName) ,TRIM(pfc.clientCode) , TRIM(pfc.jobName) ,TRIM(pfc.eftSubject),pfc.createdDate , pfc.updatedDate,pfc.approvedDate,TRIM(pfc.createdBy) ,NULLIF(TRIM(pfc.updatedBy),'') , NULLIF(TRIM(pfc.approvedBy),''), 'HOLMES',concat('HOLMES',' : ',TRIM(pfc.configStatus)),CASE     WHEN pjm.ksdName like 'HWE%' THEN 'MM' WHEN pjm.ksdName not like 'HWE%' THEN 'LM' END , pfc.processName, pfc.processType) from ProcessFeatureConfig pfc, ProcessJobMapping pjm where pjm.id = pfc.processJobMapping.id  and pfc.activeFlag = 'T' and pfc.createdBy in ('A1022374','A1022376','A1019233','A1022375') and pfc.createdDate < ?1 order by pfc.createdDate desc")
    List<HWSBusinessKSDConfigReport> getHWSExcelList2( Date date);



       
    @Query("select distinct new com.wipro.fipc.pojo.HWSBossKSDConfigReport(pfc.businessUnitName ,pjm.ksdName,pfc.businessOpsName,pfc.processJobMapping.id ,TRIM(pfc.configStatus) ,TRIM(pfc.clientName),TRIM(pfc.clientCode) , TRIM(pfc.jobName),TRIM(pfc.eftSubject),pfc.createdDate , pfc.updatedDate ,pfc.approvedDate,TRIM(pfc.createdBy),NULLIF(TRIM(pfc.updatedBy),'') , NULLIF(TRIM(pfc.approvedBy),''),tic.id,tuc.id ,rc.id ,tskuc.id ,ticcc.id ,nrc.id ,'BUSINESS', pfc.processName, pfc.processType) from ProcessFeatureConfig pfc full JOIN TbaInquiryConfig  tic ON tic.processJobMapping.id = pfc.processJobMapping.id and tic.activeFlag='T' full  JOIN TbaUpdateConfig tuc on tuc.processJobMapping.id = pfc.processJobMapping.id and tuc.activeFlag ='T' full JOIN  RulesConfig rc on rc.processJobMapping.id = pfc.processJobMapping.id  and rc.activeFlag= 'T' full  JOIN TaskUpdateConfig tskuc on tskuc.processJobMapping.id = pfc.processJobMapping.id  and tskuc.activeFlag= 'T' full JOIN TicketCreationConfig ticcc on ticcc.processJobMapping.id = pfc.processJobMapping.id  and ticcc.activeFlag='T' full JOIN NotificationReportConfig nrc on nrc.processJobMapping.id = pfc.processJobMapping.id  and nrc.activeFlag='T' full JOIN ProcessJobMapping pjm on pjm.id = pfc.processJobMapping.id  where pfc.activeFlag = 'T' and pfc.createdBy not in ('A1019778','A1022374','A1022376','A1019233','qa_admin','A1022375') and pfc.createdDate < ?1  order by pfc.createdDate desc,pfc.processJobMapping.id desc")
    List<HWSBossKSDConfigReport> getBossHWSExcelList1(Date date1);
    @Query("select distinct new com.wipro.fipc.pojo.HWSBossKSDConfigReport(pfc.businessUnitName ,pjm.ksdName,pfc.businessOpsName,pfc.processJobMapping.id ,TRIM(pfc.configStatus) ,TRIM(pfc.clientName),TRIM(pfc.clientCode) , TRIM(pfc.jobName),TRIM(pfc.eftSubject),pfc.createdDate , pfc.updatedDate ,pfc.approvedDate,TRIM(pfc.createdBy),NULLIF(TRIM(pfc.updatedBy),'') , NULLIF(TRIM(pfc.approvedBy),''),tic.id,tuc.id ,rc.id ,tskuc.id ,ticcc.id ,nrc.id , 'HOLMES', pfc.processName, pfc.processType) from ProcessFeatureConfig pfc full  JOIN TbaInquiryConfig  tic ON tic.processJobMapping.id = pfc.processJobMapping.id and tic.activeFlag='T' full  JOIN TbaUpdateConfig tuc on tuc.processJobMapping.id = pfc.processJobMapping.id and tuc.activeFlag ='T' full JOIN RulesConfig rc on rc.processJobMapping.id = pfc.processJobMapping.id  and rc.activeFlag= 'T' full JOIN TaskUpdateConfig tskuc on tskuc.processJobMapping.id = pfc.processJobMapping.id  and tskuc.activeFlag= 'T' full  JOIN TicketCreationConfig ticcc on ticcc.processJobMapping.id = pfc.processJobMapping.id  and ticcc.activeFlag='T' full  JOIN NotificationReportConfig nrc on nrc.processJobMapping.id = pfc.processJobMapping.id  and nrc.activeFlag='T' full  JOIN ProcessJobMapping pjm on pjm.id = pfc.processJobMapping.id  where pfc.activeFlag = 'T' and pfc.createdBy  in ('A1022374','A1022376','A1019233','A1022375') and pfc.createdDate < ?1  order by pfc.createdDate desc,pfc.processJobMapping.id desc")
    List<HWSBossKSDConfigReport> getBossHWSExcelList2(Date date1);


	

	
}
