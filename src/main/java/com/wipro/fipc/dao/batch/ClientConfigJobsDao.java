package com.wipro.fipc.dao.batch;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.batch.ClientConfigJobs;

public interface ClientConfigJobsDao extends BaseDao<ClientConfigJobs> {
	
	@Transactional
	 @Modifying
	 @Query(value = "UPDATE emails_scheduler.client_config_jobs u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where ksd_config_id IN(select id from emails_scheduler.KSD_CONFIG kc where kc.PROCESS_JOB_MAPPING_ID=?2)",nativeQuery = true)
	    int commonupdate(@Param("updated_by") String updated_by,@Param("process_job_mapping_id") long processJobMappingId);
	
}