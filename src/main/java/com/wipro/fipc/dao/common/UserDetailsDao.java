package com.wipro.fipc.dao.common;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.common.UserDetails;
import com.wipro.fipc.pojo.ReporteeAdidsPojo;
import com.wipro.fipc.pojo.User;

public interface UserDetailsDao extends BaseDao<UserDetails> {

	@Query("select distinct a.type from UserDetails a where a.adid= :adid ")
	String getType(@Param("adid") String adid);

	@Query("select distinct a.adid from UserDetails a order by adid")
	List<String> getAllAdid();

	@Query("select distinct a.adid from UserDetails a where lower(a.type) not LIKE 'admin' order by adid ")
	List<String> getAdidWithoutAdmin();

	@Modifying
	@Query(value = "UPDATE UserDetails SET activeFlag ='F',"
			+ " updatedBy = :updated_by where adid IN ( :adid ) and activeFlag ='T'")
	int updateData(@Param("updated_by") String updated_by, @Param("adid") List<String> adid);

	@Modifying
	@Query(value = "UPDATE UserDetails SET managerAdId = NULL, updatedBy = :updated_by  "
			+ " where managerAdId IN ( :adid )")
	int updateManger(@Param("updated_by") String updated_by, @Param("adid") List<String> adid);

	
	boolean existsByAdidInAndActiveFlag(/* @Param("adid") */ List<String> adid, char activeFlag);

	@Query("select distinct adid from UserDetails where managerAdId = :adid  and activeFlag ='T'")
	List<String> getAllReporteeAdid(@Param("adid") String adid);

	

	@Query("SELECT distinct role from UserDetails r WHERE r.adid = :adid and activeFlag ='T'")
	List<String> findRoleByAdIdAndActiveFlag(@Param("adid") String adid);

	@Query("SELECT new com.wipro.fipc.pojo.User(id,adid as adId,role,createdBy,updatedBy,updatedDate,type,sessionStatus,sessionDate) from UserDetails where activeFlag='T'")
	List<User> findByCreatedByAndActiveFlag(@Param("createdBy") String createdBy);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE UserDetails u set activeFlag ='F' where u.adid in ?1 and u.activeFlag='T' ")
	void inactiveByadid(List<String> adid);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE UserDetails u set u.racfId = ?2  where u.adid = ?1 and u.activeFlag='T' ")
	void updateracfId(String adid, String racfid);

	

	@Query("SELECT distinct new com.wipro.fipc.pojo.ReporteeAdidsPojo(u.adid,u.racfId) from UserDetails u where u.adid = ?1 and u.activeFlag='T' ")
	ReporteeAdidsPojo getReporteeAdids2(String managerAdId);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE UserDetails u set activeFlag = 'F'  where u.managerAdId = ?1 and u.activeFlag='T' ")
	void updateByManagerAdid(String managerAdid);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE UserDetails u set u.managerAdId = ?1 , u.racfId = ?2 , u.updatedDate = current_timestamp, u.sessionStatus = ?4 , u.sessionDate = ?5 where u.adid = ?3 and u.activeFlag='T' ")
	void updateCorresManagerAdid(String managerAdid, String racfid, String adid, String sessionStatus,
			LocalDateTime sessionDate);

	@Query("SELECT u from UserDetails u where u.adid = :adid")
	List<UserDetails> findByAdid(@Param("adid") String adid);

	@Query("select u.racfId from UserDetails u where u.adid = :adid and u.activeFlag ='T' and u.racfId is not null")
	List<String> fetchRacfid(@Param("adid") String adid);

	@Query("select u.racfId from UserDetails u where u.managerAdId = :adid and u.activeFlag ='T' and u.racfId is not null")
	List<String> fetchRacfidManager(@Param("adid") String adid);

}
