package com.wipro.fipc.dao.common;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.BusinessUnitOps;
import com.wipro.fipc.pojo.CustomClientDetailsBO;

public interface BusinessUnitOpsDao extends BaseDao<BusinessUnitOps> {

	@Query("select new com.wipro.fipc.pojo.CustomClientDetailsBO(bi.id,bi.clientCode,bi.clientName,c.id,c.opsName,c.opsCode) from RoleConfig rl join ClientDetails bi on rl.clientDetails.id = bi.id join BusinessOps c on rl.businessOps.id=c.id where  rl.businessUnit.id=?1 and active =true ")
	List<CustomClientDetailsBO> getListOfClientDetails(Long buid);

}
