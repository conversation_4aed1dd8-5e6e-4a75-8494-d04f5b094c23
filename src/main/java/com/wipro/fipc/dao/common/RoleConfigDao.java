package com.wipro.fipc.dao.common;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.common.RoleConfig;
import com.wipro.fipc.pojo.BusinessOpsRoleConfig;
import com.wipro.fipc.pojo.ClientDetailsBo;
import com.wipro.fipc.pojo.CommonRoleConfig;
import com.wipro.fipc.pojo.CommonRoleConfigBo;
import com.wipro.fipc.pojo.CustomClientBO;
import com.wipro.fipc.pojo.CustomPFCClientCodeBO;
import com.wipro.fipc.pojo.ProcessRoleConfig;

public interface RoleConfigDao extends BaseDao<RoleConfig> {

	@Query("select distinct new com.wipro.fipc.pojo.ClientDetailsBo(b.id,b.clientCode,b.clientName) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id where a.businessUnit.id = ?2  and LOWER(a.role)=LOWER(?3) and a.active='true' and a.adid = ?1 ")
	List<ClientDetailsBo> getclientdetailsAnalyst(String adid, Long buid, String role);

	@Query("select distinct new com.wipro.fipc.pojo.CustomClientBO(a.id,a.clientCode,a.clientName) from  ClientDetails a join RoleConfig b on a.id=b.clientDetails.id where b.adid= ?1 and b.businessUnit.id = ?2 and b.active='true' ")
	List<CustomClientBO> getCustomClient(String adid, Long buid);

	@Query("select new com.wipro.fipc.pojo.CustomPFCClientCodeBO(d.id,d.businessOpsName,d.ksdName) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id right join BusinessUnit c on c.id=a.businessUnit.id right join ProcessFeatureConfig d on d.businessUnitName =c.unitName and d.clientCode = b.clientCode where d.activeFlag = 'T' and a.active='t' and a.adid = ?1 ")
	List<CustomPFCClientCodeBO> getPFCrowsList(String adid);

	@Query("SELECT p FROM RoleConfig p " + "WHERE p.adid = :adid ")
	List<RoleConfig> getRoleDetailByAdid(@Param("adid") String adid);

	@Query("SELECT DISTINCT p.role FROM RoleConfig p " + "WHERE p.adid IN( :adid )")
	List<String> getRole(@Param("adid") List<String> adid);

	@Query("SELECT DISTINCT p.role FROM RoleConfig p " + "WHERE p.adid IN( :adid )")
	List<String> getRoleListLogin(@Param("adid") String adid);

	@Query("select new com.wipro.fipc.pojo.CustomPFCClientCodeBO(d.id,d.businessUnitName,d.clientName,d.clientCode,d.businessOpsName,d.processName,d.jobName,d.updatedBy,d.processJobMapping.id,d.updatedDate,d.eftSubject,d.processType,d.configStatus,d.phaseNames,d.ksdName,d.approvedBy,d.approvedDate) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id right join BusinessUnit c on c.id=a.businessUnit.id right join ProcessFeatureConfig d on d.businessUnitName =c.unitName and cast(d.clientCode as int ) = cast(b.clientCode as int) where d.activeFlag = 'T' and a.active='true' and a.adid = ?1  and UPPER(d.configStatus) NOT LIKE 'APPROVED'  ")
	List<CustomPFCClientCodeBO> getPFCrows1(String adid);


	@Query("select new com.wipro.fipc.pojo.CustomPFCClientCodeBO(d.id,d.businessUnitName,d.clientName,d.clientCode,d.businessOpsName,d.processName,d.jobName,d.updatedBy,d.processJobMapping.id,d.updatedDate,d.eftSubject,d.processType,d.configStatus,d.phaseNames,d.ksdName,d.approvedBy,d.approvedDate) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id right join BusinessUnit c on c.id=a.businessUnit.id right join ProcessFeatureConfig d on d.businessUnitName =c.unitName and d.clientCode = b.clientCode where d.activeFlag = 'T' and a.active='true' and a.adid = ?1 and UPPER(d.configStatus)  LIKE 'APPROVED'  ")
	List<CustomPFCClientCodeBO> getPFCrows3(String adid);

	@Query(value = "select new com.wipro.fipc.pojo.CustomPFCClientCodeBO(d.id,d.businessUnitName,d.clientName,d.clientCode,d.businessOpsName,d.processName,d.jobName,d.updatedBy,d.processJobMapping.id,d.updatedDate,d.eftSubject,d.processType,d.configStatus,d.phaseNames,d.ksdName,d.approvedBy,d.approvedDate) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id right join BusinessUnit c on c.id=a.businessUnit.id right join ProcessFeatureConfig d on d.businessUnitName =c.unitName and Trim(d.clientCode) = Trim(b.clientCode) where d.activeFlag = 'T'  and a.active='true' and a.adid = ?1", countQuery = "select count(d) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id right join BusinessUnit c on c.id=a.businessUnit.id right join ProcessFeatureConfig d on d.businessUnitName =c.unitName and Trim(d.clientCode) = Trim(b.clientCode) where d.activeFlag = 'T'  and a.active='t' and a.adid = ?1")
	Page<CustomPFCClientCodeBO> getPFCrowsWithoutFilter(String adid, Pageable pageable);

	@Modifying
	@Query(value = "UPDATE RoleConfig  SET active ='F', updatedDate = CURRENT_TIMESTAMP, updatedBy = :updated_by "
			+ "where adid = :adid and active ='T' and client_id = :clientId ")
	int updateClientRoleConfig(@Param("updated_by") String updated_by, @Param("adid") String adid,
			@Param("clientId") Long clientID);



	@Query("select distinct new com.wipro.fipc.pojo.CustomPFCClientCodeBO(d.id,d.businessUnitName,d.clientName,d.clientCode,d.businessOpsName,d.processName,d.jobName,d.updatedBy,d.processJobMapping.id,d.updatedDate,d.eftSubject,d.processType,d.configStatus,d.phaseNames,d.ksdName,d.approvedBy,d.approvedDate) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id right join BusinessUnit c on c.id=a.businessUnit.id right join ProcessFeatureConfig d on d.businessUnitName =c.unitName and cast(d.clientCode as int ) = cast(b.clientCode as int) where d.activeFlag = 'T' and a.active='true' and a.adid in ?1 ")
	List<CustomPFCClientCodeBO> getPFCrowsManager(List<String> adids);

	@Query("select distinct new com.wipro.fipc.pojo.CustomPFCClientCodeBO(d.id,d.businessUnitName,d.clientName,d.clientCode,d.businessOpsName,d.processName,d.jobName,d.updatedBy,d.processJobMapping.id,d.updatedDate,d.eftSubject,d.processType,d.configStatus,d.phaseNames,d.ksdName,d.approvedBy,d.approvedDate) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id right join BusinessUnit c on c.id=a.businessUnit.id right join ProcessFeatureConfig d on d.businessUnitName =c.unitName and d.clientCode = b.clientCode where d.activeFlag = 'T' and a.active='true' and a.adid = ?1 ")
	List<CustomPFCClientCodeBO> getPFCrowsAnalyst(String adid);

	@Query("select distinct new com.wipro.fipc.pojo.ClientDetailsBo(b.id,b.clientCode,b.clientName) from RoleConfig a right join ClientDetails b on b.id = a.clientDetails.id where a.businessUnit.id = ?2  and  a.active='true' and a.adid in ?1 ")
	List<ClientDetailsBo> getclientdetailsManager(List<String> adids, Long buid);

	@Query("select distinct new com.wipro.fipc.pojo.ProcessRoleConfig(b.clientDetails.id,b.process.id) from RoleConfig a  join ProcessJobMapping b on b.clientDetails.id = a.clientDetails.id where  a.adid = ?1 ")
	List<ProcessRoleConfig> getprocessIdRoleConfigId(String adid);

	@Query("select distinct new com.wipro.fipc.pojo.BusinessOpsRoleConfig(a.businessUnit.id,a.businessOps.id) from RoleConfig b  join BusinessUnitOps a on b.businessUnit.id = a.businessUnit.id where  b.adid = ?1 ")
	List<BusinessOpsRoleConfig> getbusinessOpsIdRoleConfigId(String adid);

	@Query("select distinct new com.wipro.fipc.pojo.CommonRoleConfig(rc.role,rc.adid,bu.id,bu.unitName,bu.unitCode,bo.id,bo.opsCode,bo.opsName,cd.id,cd.clientCode,cd.clientName,proc.id,proc.processType,proc.processName,pjm.id) from ProcessJobMapping pjm, ClientDetails cd, RoleConfig rc, BusinessUnit bu, BusinessUnitOps buo, BusinessOps bo , Process proc where pjm.clientDetails.id = rc.clientDetails.id and pjm.clientDetails.id = cd.id and rc.clientDetails.id = cd.id and bu.id = rc.businessUnit.id and pjm.businessUnitOps.id = buo.id and pjm.activeFlag= 'T' and bo.id = buo.businessOps.id and bu.id = buo.businessUnit.id and rc.businessUnit.id = buo.businessUnit.id and rc.active='T' and proc.id = pjm.process.id and rc.adid = ?1 and LOWER(rc.role) = LOWER(?2) and bu.id = ?3 and bo.id = ?4")
	List<CommonRoleConfig> getClientInformationListData(String role, String adid, Long buid, Long boid);

	@Query("select distinct new com.wipro.fipc.pojo.CommonRoleConfigBo(bu.id,bu.unitName,bu.unitCode,bo.id,bo.opsCode,bo.opsName) from ProcessJobMapping pjm, ClientDetails cd, RoleConfig rc, BusinessUnit bu, BusinessUnitOps buo, BusinessOps bo , Process proc where pjm.clientDetails.id = rc.clientDetails.id and pjm.clientDetails.id = cd.id and rc.clientDetails.id = cd.id and bu.id = rc.businessUnit.id and pjm.businessUnitOps.id = buo.id and pjm.activeFlag= 'T' and bo.id = buo.businessOps.id and bu.id = buo.businessUnit.id and rc.businessUnit.id = buo.businessUnit.id and rc.active='T' and proc.id = pjm.process.id and rc.adid = ?1")
	List<CommonRoleConfigBo> getClientInformationBoListData(String adid);

	@Query("SELECT COUNT(a) from RoleConfig a WHERE a.adid = ?1 and a.clientDetails.id = ?2 and a.active ='true'")
	long getRowsCountRoleConfig(String adid, Long clientID);

}
