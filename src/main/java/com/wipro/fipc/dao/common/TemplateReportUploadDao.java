package com.wipro.fipc.dao.common;

import java.util.List;

import javax.transaction.Transactional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import org.springframework.data.repository.query.Param;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.TemplateReportUpload;
import com.wipro.fipc.pojo.TemplateReportUploadPojo;

@Transactional
public interface TemplateReportUploadDao extends BaseDao<TemplateReportUpload> {
	@Query("select distinct new com.wipro.fipc.pojo.TemplateReportUploadPojo(b.id,b.buId,b.activeFlag,b.type,b.clientId,b.clientName,b.templateReportName,b.uploadedDate,b.uploadedBy,b.createdDate,b.createdBy,b.updatedBy,b.updatedDate,b.templateReportNameWs,b.reportFlag) from TemplateReportUpload b") 
	List<TemplateReportUploadPojo> getListOfData();
	
	@Query("select distinct new com.wipro.fipc.pojo.TemplateReportUploadPojo(b.id,b.buId,b.activeFlag,b.type,b.clientId,b.clientName,b.templateReportName,b.uploadedDate,b.uploadedBy,b.createdDate,b.createdBy,b.updatedBy,b.updatedDate,b.templateReportNameWs,b.reportFlag) from TemplateReportUpload b  where b.clientId=?1  OR b.clientId = 'All Client'") 
	List<TemplateReportUploadPojo> getallDataWithAllClients(String clientId);
	
	@Modifying
	@Query("delete from TemplateReportUpload b where b.id=:id")
	void deleteTemplateReportUpload(@Param("id") Long id);


}
