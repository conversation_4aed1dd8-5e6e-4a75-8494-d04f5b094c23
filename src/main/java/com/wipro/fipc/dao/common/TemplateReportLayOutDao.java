package com.wipro.fipc.dao.common;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.TemplateReportLayOut;

public interface TemplateReportLayOutDao extends BaseDao<TemplateReportLayOut> {
	
	@Query("Select a from TemplateReportLayOut a  where a.templateReportUpload.id=?1 and activeFlag ='T'") 
	List<TemplateReportLayOut> getListOfTemplateReportLayout(Long templateReportId);
	
	@Query("select a from TemplateReportLayOut a left join TemplateReportUpload b on a.templateReportUpload.id = b.id where (b.clientId = ?1 or b.clientId like 'All Client') and b.templateReportName = ?2  ")  
	List<TemplateReportLayOut> getListOfData(String clientId, String templateReportName);
	
	@Modifying
	@Query(value = "UPDATE TemplateReportLayOut SET activeFlag ='F', updatedDate = CURRENT_TIMESTAMP where templateReportUpload.id=?1 and activeFlag ='T' ")
	int deleteTemplateReportLayOut( Long templateReportUpload);

}
