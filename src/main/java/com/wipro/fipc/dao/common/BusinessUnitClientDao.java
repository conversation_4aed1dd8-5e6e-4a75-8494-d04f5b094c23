package com.wipro.fipc.dao.common;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.BusinessUnitClient;
import com.wipro.fipc.pojo.CustomClientDetailsBO;

public interface BusinessUnitClientDao extends BaseDao<BusinessUnitClient> {
	@Query("select new com.wipro.fipc.pojo.CustomClientDetailsBO(bi.id,bi.clientCode,bi.clientName,c.id,c.opsName,c.opsCode) from RoleConfig rl join ClientDetails bi on rl.clientDetails.id = bi.id join BusinessOps c on rl.businessOps.id=c.id join BusinessUnit bu on rl.businessUnit.id=bu.id join BusinessUnitClient bucli on bu.id = bucli.id  where bucli.id=?1")
	List<CustomClientDetailsBO> getListclientDetails(Long buid);
}
