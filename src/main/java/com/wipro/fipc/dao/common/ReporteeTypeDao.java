package com.wipro.fipc.dao.common;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.common.ReporteeType;

public interface ReporteeTypeDao extends BaseDao<ReporteeType> {
	@Query("select distinct a.reporteeId from ReporteeType a where a.managerId= :managerId ")
	List<String> getReporteesByManagerID(@Param("managerId") String managerId);
}
