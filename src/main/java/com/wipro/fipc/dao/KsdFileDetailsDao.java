package com.wipro.fipc.dao;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.batch.KsdFileDetails;

public interface KsdFileDetailsDao extends BaseDao<KsdFileDetails> {

	@Transactional
	@Modifying
	@Query(value = "UPDATE emails_scheduler.KSD_FILE_DETAILS u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE emails_scheduler.KSD_FILE_DETAILS u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where u.file_name = ?2 and u.process_job_mapping_id = ?3", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by, @Param("file_name") String file_name,
			@Param("process_job_mapping_id") long processJobMappingId);

	@Query("select a from KsdFileDetails a  where a.processJobMapping.id =?1 and a.fileType =?2")
	List<KsdFileDetails> findByFileType(Long pjmid, String fileName);

	@Query("select a from KsdFileDetails a  where a.processJobMapping.id =?1 ")
	List<KsdFileDetails> getSingleKsdFileDetails(Long pjmid, Pageable pageable);

	@Query("select a from KsdFileDetails a  where a.processJobMapping.id =?1 and a.fileName =?2")
	List<KsdFileDetails> getRequiredDetailsByksdFileDetails(Long pjmid, String fileName);

	@Query(value = "select file_name from emails_scheduler.ksd_file_details where process_job_mapping_id =?1 and file_type=?2 and active_flag='T'", nativeQuery = true)
	List<String> findByFile(long processJobMappingId, String fileType);
	
	@Query(value = "select count(*) from emails_scheduler.ksd_file_details where process_job_mapping_id =?1 and primary_file='Y' and active_flag='T'", nativeQuery = true)
	int getPrimaryFileCount(long processJobMappingId);
	
	@Transactional
	@Modifying
	@Query(value = "UPDATE emails_scheduler.KSD_FILE_DETAILS u set primary_file =?1 where u.process_job_mapping_id = ?2 and active_flag='T'", nativeQuery = true)
	int updatePrimaryFile(@Param("primaryFileStatus") String primaryFileStatus,@Param("process_job_mapping_id") long processJobMappingId);
}
