package com.wipro.fipc.dao;


import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.entity.BusinessOps;
import com.wipro.fipc.pojo.CustomBusinessOpsBO;

public interface BusinessOpsDao extends BaseDao<BusinessOps> {
	@Query("SELECT p FROM BusinessOps p " + "WHERE p.opsName = :opsName")
	BusinessOps getBusinessOpsItem(@Param("opsName") String opsName);

	@Query("SELECT p FROM BusinessOps p " + "WHERE p.id = :id")
	BusinessOps getBusinessOpsId(@Param("id") long id);

	@Query("SELECT p FROM BusinessOps p " + "WHERE p.id IN (:idList)")
	List<BusinessOps> getBusinessOpsIds(@Param("idList") List<Long> idList);

	@Query("select distinct new com.wipro.fipc.pojo.CustomBusinessOpsBO(b.id,b.opsName,b.opsCode) from BusinessOps b where b.id IN (:idList) order by b.opsName")
	List<CustomBusinessOpsBO> getBusinessOpsByIds(@Param("idList") List<Long> idList);

}
