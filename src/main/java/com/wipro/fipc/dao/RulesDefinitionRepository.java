package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.common.RulesDefinition;


public interface RulesDefinitionRepository
		extends CrudRepository<RulesDefinition, Serializable>, JpaSpecificationExecutor<RulesDefinition> {

	@Query("select a.ruleName from RulesDefinition a join RulesConfig b on a.rulesConfig.id = b.id where b.processJobMapping.id =?1 and a.activeFlag='T' and  a.validationType=1 and ((a.fileName =?2 ) or (a.fileName = 'TBA' ))")
	List<String> getRequiredDetailsBy(Long pjmid, String filename);

	@Query("SELECT p FROM RulesDefinition p" + " WHERE p.primaryFieldName  = :primaryFieldName")
	List<RulesDefinition> withoutmftfield(@Param("primaryFieldName") String primaryFieldName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_DEFINITION u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where rule_id IN(select id from layout_rule.RULES_CONFIG kc where kc.PROCESS_JOB_MAPPING_ID=?2)", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_DEFINITION u set active_flag ='F',updated_by= ?1 where u.file_name = ?2 and rule_id IN(select id from layout_rule.RULES_CONFIG kc where kc.process_job_mapping_id=?3)", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by, @Param("file_name") String file_name,
			@Param("process_job_mapping_id") long processJobMappingId);

	@Query("select a from RulesDefinition a join RulesConfig b on a.rulesConfig.id = b.id where b.processJobMapping.id =?1 and"
			+ "(" + "a.json like %?2% or a.varOperationJson like %?2% or a.fileName like %?2%" + ")")
	List<RulesDefinition> getRequiredDetailsByRuleDifinition(Long pjmid, String fileName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_DEFINITION u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where  u.var_operation_json like %?3% or u.json like %?3% or u.file_name= ?3 and rule_id IN(select id from layout_rule.RULES_CONFIG kc where kc.process_job_mapping_id=?2)", nativeQuery = true)
	int updateRequiredDetailsByRulesDififnitions(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long processJobMappingId, @Param("file_name") String file_name);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_DEFINITION u set active_flag ='F',updated_by= ?3,updated_date = CURRENT_TIMESTAMP where u.rule_name= ?2 and rule_id IN(select id from layout_rule.RULES_CONFIG kc where kc.process_job_mapping_id=?1) and active_flag ='T'", nativeQuery = true)
	int updateRulesDetailsByRulesDififnitions(@Param("process_job_mapping_id") long processJobMappingId,
			@Param("rule_name") String rule_name, @Param("updated_by") String updated_by);

	@Query("select a from RulesDefinition a join RulesConfig b on a.rulesConfig.id = b.id where b.processJobMapping.id =?1 and a.ruleName =?2")
	List<RulesDefinition> getRulesdeletionRuleDifinition(Long pjmid, String ruleName);
}
