package com.wipro.fipc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.entity.BusinessOps;
import com.wipro.fipc.entity.BusinessUnit;
import com.wipro.fipc.entity.ClientDetails;
import com.wipro.fipc.pojo.CustomBusinessUnitBO;

public interface BusinessUnitDao extends JpaRepository<BusinessUnit, Long> {

	@Query("select a from ClientDetails a join BusinessUnitClient b on a.id=b.clientDetails.id where b.businessUnit.id=?1")
	List<ClientDetails> getClientDetailsByBusinessUnitId(long businessUnitID);
	
	@Query("select a from BusinessOps a join BusinessUnitOps b on a.id=b.businessOps.id where b.businessUnit.id=?1")
	List<BusinessOps> getBusinessOperationDetailsByBusinessUnitId(long businessUnitID);
	
	
	@Query("select distinct new com.wipro.fipc.pojo.CustomBusinessUnitBO(bu.id, bu.unitName,bu.unitCode) from RoleConfig rl join BusinessUnit bu on rl.businessUnit.id =bu.id where rl.adid =?1 and active =true")
	List<CustomBusinessUnitBO> getByAlightId(String alightId);
	
	

	
	@Query("SELECT p FROM BusinessUnit p "
		    + "WHERE p.unitName = :unitName")
	BusinessUnit getBusinessUnit(@Param("unitName") String unitName);

	@Query("SELECT p FROM BusinessUnit p "
		    + "WHERE p.id = :id")
	BusinessUnit getBusinessUnitId(@Param("id") long id);
	

}
