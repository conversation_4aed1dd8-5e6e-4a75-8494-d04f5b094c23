package com.wipro.fipc.dao;

import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import com.wipro.fipc.entity.BusinessOps;
import com.wipro.fipc.entity.BusinessUnit;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.ProcessFeatureConfigCopyJob;
import com.wipro.fipc.entity.TemplateReportLayOut;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.layoutrule.LayoutConfig;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.pojo.ProcessCofigReqBody;
import com.wipro.fipc.pojo.ProcessCofigResBody;

public interface BaseGenericDaoCustom<T> {
	List<T> findByColumn(Class<T> clazz, String schema, String table_name, String columnName, String columnValue);
	
	List<T> findByColumn(Class<T> clazz, String schema, String table_name, String columnName, String columnValue,boolean updateFlag);
	List<T> findAll(Class<T> clazz, String schema, String table_name);


	List<T> findRecordByColumn(Class<T> clazz, String schema, String table_name, String columnName, String columnValue);

	List<T> findActiveRecordByColumn(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue);

	List<T> findByMultiColumnCondition(Class<T> clazz, String schema, String table_name,
			List<ColumnConditionParam> columnConditionParamList);

	List<KsdOutPutFileDetails> getDistinctFileNames(Class<KsdOutPutFileDetails> class1, String outSchema,
			String ksdOutputFileDetails, List<ColumnConditionParam> columnConditionParams);

	List<TicketCreationConfig> saveAllTicket(List<TicketCreationConfig> ticketCreationConfigs);

	List<LayoutConfig> saveAllLayOutConfig(List<LayoutConfig> layoutConfigs);

	Boolean deleteRowsByUID(Class<T> clazz, String schema, String table_name, String columnName, String columnValue);

	int updateByColumn(Class<T> clazz, String schema, String table_name, String columnName, String columnValue);

	List<BusinessUnit> findBUnits(Long PJMId);

	List<BusinessOps> findBOps(Long PJMId);

	public List<T> fetchValInBetween(Class<T> clazz, String schema, String table_name, String startTime, String endTime,
			String columnName);

	CommonRowBO deleteRowByUpdated(Class<T> clazz, String schema, String table_name, Long id, String updatedBy,
			Date updatedDate);

	List<TemplateReportLayOut> saveAllTempalteConfig(List<TemplateReportLayOut> templateReportLayOuts);

	Long deleterowby(Class<T> clazz, String schema, String tablename, Long id, String updatedBy);

	boolean deleteMultipleRows(Class<T> clazz, String schema, String tablename, List<Long> id, String updatedBy);
	
	public boolean deleteMultipleRows(Class<T> clazz, String schema, String tablename, List<Long> ids,
			String updatedBy,boolean updateFlag);

	public List<T> fetchValInBetweenWithoutActiveFlag(Class<T> clazz, String schema, String table_name,
			String startTime, String endTime, String columnName);

	public Boolean deleteByColumn(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue);

	public Boolean copyJobPFC(ProcessFeatureConfigCopyJob p);

	public ProcessCofigResBody getAllFilterdProcessConfigDetails(ProcessCofigReqBody reqBody)
			throws IllegalAccessException, InvocationTargetException;

	public List<BigInteger> getProcessIdList(String alightID, String buID, String clientID, String buOpsID);

	public List<BigInteger> getOpsIdList(String alightID, String buID);

	public List<BigInteger> getPrimList(String adid);

	int deletePreviousFile(Class<T> clazz, String schema, String table_name, String pjmId, String fileType);

	List<String> getFileNameByFileType(String schema, String table_name, String pjmId, String fileType);

	int deletePreviousFile(Class<LayoutConfig> class1, String schema, String table_name, String pjmId,
			List<String> fileName);
	
	List<T> findByMultiColumn(Class<T> clazz, String schema, String table_name, String clientId, String panelIdParam, String transIdParam);

}
