package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.batch.KsdConfig;

public interface KsdConfigDao extends CrudRepository<KsdConfig, Serializable>, JpaSpecificationExecutor<KsdConfig> {

	@Query("SELECT p FROM KsdConfig p WHERE p.processJobMappingId = :processJobMappingId and p.activeFlag='T'")
	KsdConfig getKsdConfigdata(@Param("processJobMappingId") Long processJobMappingId);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE KsdConfig config SET config.jobScheduleTime =?2 where config.id =?1")
	void updateJobScheduleTime(Long id, String jobScheduleTime);

	@Query("select a from KsdConfig a "
			+ "where a.id IN (:idList) AND a.jobScheduleTime BETWEEN :startTime and :endTime")
	List<KsdConfig> getKsdConfigListUsingFilter(@Param("idList") List<Long> idList,
			@Param("startTime") String startTime, @Param("endTime") String endTime);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE KsdConfig p SET p.maestroTaskName =:maestroTaskName, p.maestroTaskType =:maestroTaskType, p.updatedBy =:updatedBy  WHERE p.processJobMappingId=:processJobMappingId and p.activeFlag='T'")
	int updateMaestroTaskName(Long processJobMappingId, String maestroTaskName, String maestroTaskType,
			String updatedBy);

	@Transactional
	@Modifying
	@Query(value = "UPDATE emails_scheduler.ksd_config u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Query("SELECT p FROM KsdConfig p " + "WHERE p.processJobMappingId IN (:pjmID)")
	List<KsdConfig> findByProcessJobMappingId(List<Long> pjmID);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE KsdConfig p SET p.jiraTaskName =:jiraTaskName, p.jiraTaskType =:jiraTaskType, p.jiraProjectType=:jiraProjectType, p.updatedBy =:updatedBy  WHERE p.processJobMappingId=:processJobMappingId and p.activeFlag='T'")
	int updateJiraTaskNameAndTaskType(Long processJobMappingId, String jiraTaskName, String jiraTaskType,
			String jiraProjectType, String updatedBy);
}
