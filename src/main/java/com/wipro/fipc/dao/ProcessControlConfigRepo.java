package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.filelayout.ProcessControlConfig;


public interface ProcessControlConfigRepo
		extends CrudRepository<ProcessControlConfig, Serializable>, JpaSpecificationExecutor<ProcessControlConfig> {

	@Query("SELECT p FROM ProcessControlConfig p" + " WHERE p.fieldName  = :fieldName")
	List<ProcessControlConfig> withoutmftfield(@Param("fieldName") String fieldName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?1 where actions like %?2% and u.process_job_mapping_id = ?3", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by, @Param("application") String application,
			@Param("process_job_mapping_id") long process_job_mapping_id);

	@Query("select a from ProcessControlConfig a  where a.processJobMapping.id =?1 ")
	List<ProcessControlConfig> getRequiredDetailsByPControl(Long pjmid);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where  u.process_job_mapping_id = ?2", nativeQuery = true)
	int updateByPControlConfigs(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long processJobMappingId);

	@Query("select a from ProcessControlConfig a  where a.processJobMapping.id =?1 and ruleName =?2")
	List<ProcessControlConfig> getRulesdeleteByPControl(Long pjmid, String ruleName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?3 ,updated_date = CURRENT_TIMESTAMP where  u.process_job_mapping_id = ?1 and rule_name =?2 and active_flag ='T'", nativeQuery = true)
	int updateByRulesPControlConfigs(@Param("process_job_mapping_id") long processJobMappingId,
			@Param("rule_name") String rule_name, @Param("updated_by") String updated_by);

}