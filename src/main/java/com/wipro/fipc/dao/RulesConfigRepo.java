package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.filelayout.RulesConfig;

public interface RulesConfigRepo
		extends CrudRepository<RulesConfig, Serializable>, JpaSpecificationExecutor<RulesConfig> {

	@Query("Select a from RulesConfig a join RulesDefinition b on b.rulesConfig=a.id  join ValidationType c on b.validationType.id=c.id where a.processJobMapping.id=?1 and c.valTypeCode=?3 and a.activeFlag=?2")
	List<RulesConfig> getRulesConfigDetails(Long pjmid, char activeflag, String valTypeCode);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where u.id IN(select rd.rule_id from layout_rule.RULES_DEFINITION rd where rd.var_operation_json like %?2% or rd.json like %?2% or rd.file_name like %?2%) and process_job_mapping_id = ?3", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by, @Param("file_name") String file_Name,
			@Param("process_job_mapping_id") long process_job_mapping_id);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_CONFIG u set active_flag ='F',updated_by= ?3,updated_date = CURRENT_TIMESTAMP where u.id IN(select rd.rule_id from layout_rule.RULES_DEFINITION rd where rd.rule_name =?1) and process_job_mapping_id = ?2 and active_flag ='T'", nativeQuery = true)
	int rulesdelete(@Param("rule_name") String rule_name, @Param("process_job_mapping_id") long processJobMappingId,
			@Param("updated_by") String updated_by);

}
