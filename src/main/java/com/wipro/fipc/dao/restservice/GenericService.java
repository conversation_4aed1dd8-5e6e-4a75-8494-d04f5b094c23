package com.wipro.fipc.dao.restservice;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.entity.BaseModel;

@Service
public class GenericService<T extends BaseModel> {

	private static final Logger logger = LoggerFactory.getLogger(GenericService.class);
	@Autowired
	private BaseDaoRestService<T> dao;

	public T create(T entity) {
		return dao.save(entity);
	}

	public T update(long id, T entity) {
		return dao.save(entity);
	}

	public String delete(long id) {
		return "Status : Operation Not Supported";
	}

	public List<T> list() {
		return (List<T>) dao.findAll();
	}
}
