package com.wipro.fipc.dao;

import java.time.LocalDate;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.wipro.fipc.entity.TbaCutoffDate;

@Repository
public interface TbacutoffDateDao extends JpaRepository<TbaCutoffDate,Long>{
	
	@Query(value = "SELECT t.tba_cutoff_date FROM tba.tba_cutoff_date t where cast(t.tba_cutoff_date as DATE) < :dateCriteria "
			+ " and t.client_code=:clientCode order by cast(t.tba_cutoff_date as DATE)  desc limit 1", nativeQuery = true)
	public String findPreviousTbaCutoffDate(@Param("dateCriteria") LocalDate dateCriteria, @Param("clientCode") String clientCode);
	
	@Query(value = "SELECT t.tba_cutoff_date FROM tba.tba_cutoff_date t where cast(t.tba_cutoff_date as DATE) > :dateCriteria "
			+ " and t.client_code=:clientCode order by cast(t.tba_cutoff_date as DATE)  asc limit 1", nativeQuery = true)
	public String findNextTbaCutoffDate(@Param("dateCriteria") LocalDate dateCriteria, @Param("clientCode") String clientCode);

}