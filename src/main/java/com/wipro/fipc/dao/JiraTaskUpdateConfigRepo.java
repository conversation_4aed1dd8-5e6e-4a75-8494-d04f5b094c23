package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.wipro.fipc.entity.jira.JiraTaskUpdateConfig;

public interface JiraTaskUpdateConfigRepo
		extends CrudRepository<JiraTaskUpdateConfig, Serializable>, JpaSpecificationExecutor<JiraTaskUpdateConfig> {

	@Query("SELECT p FROM JiraTaskUpdateConfig p")
	public List<JiraTaskUpdateConfig> list();

}
