package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.wipro.fipc.entity.filelayout.TaskUpdateConfig;

public interface MaestroTaskUpdateConfigRepo
		extends CrudRepository<TaskUpdateConfig, Serializable>, JpaSpecificationExecutor<TaskUpdateConfig> {

	@Query("SELECT p FROM TaskUpdateConfig p")
	public List<TaskUpdateConfig> list();

}
