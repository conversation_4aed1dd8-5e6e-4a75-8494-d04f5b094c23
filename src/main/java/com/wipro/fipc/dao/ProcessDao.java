package com.wipro.fipc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.pojo.CustomProcessDetailsBO;
import com.wipro.fipc.entity.Process;

public interface ProcessDao extends BaseDao<Process> {
	@Query("SELECT p FROM Process p " + "WHERE p.id = :id")
	Process findByUid(@Param("id") long id);

	@Query("SELECT p FROM Process p " + "WHERE p.processName = :processName")
	Process findByName(@Param("processName") String processName);

	@Query("SELECT p.processName FROM Process p " + "WHERE p.id IN (:id)")
	List<String> findByIds(@Param("id") List<Long> id);

	@Query("SELECT p FROM Process p " + "WHERE p.id IN (:idList)")
	List<Process> findByUids(@Param("idList") List<Long> idList);

	@Query("select distinct new com.wipro.fipc.pojo.CustomProcessDetailsBO(b.id,b.processName,b.processType) from Process b where b.id IN (:idList) order by b.processName")
	List<CustomProcessDetailsBO> getProcessByIds(@Param("idList") List<Long> idList);

}
