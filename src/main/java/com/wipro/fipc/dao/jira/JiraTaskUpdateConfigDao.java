package com.wipro.fipc.dao.jira;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.entity.jira.JiraTaskUpdateConfig;

public interface JiraTaskUpdateConfigDao extends JpaRepository<JiraTaskUpdateConfig, Long> {
	
	@Transactional
	@Modifying
	@Query(value = "UPDATE JIRA.TASK_UPDATE_CONFIG u set active_flag ='F' where u.process_job_mapping_id = ?1", nativeQuery = true)
	int commonupdate(@Param("process_job_mapping_id") long processJobMappingId);

}
