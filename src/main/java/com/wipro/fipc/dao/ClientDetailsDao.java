/**
 * 
 */
package com.wipro.fipc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.entity.ClientDetails;

/**
 * <AUTHOR>
 *
 */
public interface ClientDetailsDao extends JpaRepository<ClientDetails, Long> {

	public ClientDetails findByClientCode(String clientCode);

	@Query("SELECT c FROM ClientDetails c " + "WHERE c.id = :id")
	ClientDetails getClientDetailId(@Param("id") long id);

	@Query("SELECT c.clientName FROM ClientDetails c WHERE c.clientCode in (:clientCode)")
	List<String> getClientDetails(@Param("clientCode") List<String> clientCode);
	
	@Query("select c.clientCode from ClientDetails c where c.clientName like %:name%")
	String findByClientName(@Param("name") String name);
}
