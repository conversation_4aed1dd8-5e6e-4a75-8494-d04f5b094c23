package com.wipro.fipc.dao;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;


import com.wipro.fipc.entity.BaseModel;

//@Service
public class GenericRestService<T extends BaseModel> {

	private static final Logger logger = LoggerFactory.getLogger(GenericRestService.class);
	@Autowired
	private BaseDao<T> dao;

	public List<T> list() {
		return (List<T>) dao.findAll();
	}

	public T findById(long id) {
		return dao.findById(id).orElse(null);
	}

	public T create(T entity) {
		return dao.save(entity);
	}

	public T update(long id, T entity) {
		return dao.save(entity);
	}

	public String delete(long id) {

		return "Status : Operation Not Supported";
	}

	public <T> List<T> findByColumn(String columnName, String columnValue) {
		return Collections.emptyList();

	}

	public <T> List<T> findRecordByColumn(String columnName, String columnValue) {
		return Collections.emptyList();

	}

	public Boolean deleteByColumn(String column_name, String column_value) {
		return true;
	}

	public boolean saveAllEntities(List<T> entityList) {
		boolean retVal = false;
		try {
			dao.saveAll(entityList);
			retVal = true;
		} catch (Exception e) {
			logger.error("Exception in saveAllEntities>>", e);
		}
		return retVal;
	}

	public List<T> fetchValInBetween(String colName, String startTime, String endTime) {
		return Collections.emptyList();
	}

	public <T> List<T> loadEntityLazy(String column_name, String column_value) {

		return Collections.emptyList();

	}

	public List<T> newsaveAllEntities(List<T> entityList) {

		return (List<T>) dao.saveAll(entityList);

	}

	public Long deleterowby(long id, String updatedBy) {

		return null;
	}

}
