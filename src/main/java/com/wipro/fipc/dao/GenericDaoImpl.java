package com.wipro.fipc.dao;

import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.common.TemplateReportLayOutDao;
import com.wipro.fipc.dao.filelayout.RulesConfigDao;
import com.wipro.fipc.dao.filelayout.RulesDefinitionDao;
import com.wipro.fipc.dao.layoutrule.LayoutConfigDao;
import com.wipro.fipc.dao.maestro.TicketCreationConfigDao;
import com.wipro.fipc.entity.BusinessOps;
import com.wipro.fipc.entity.BusinessUnit;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.ProcessFeatureConfigCopyJob;
import com.wipro.fipc.entity.TemplateReportLayOut;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.layoutrule.LayoutConfig;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.pojo.ProcessCofigReqBody;
import com.wipro.fipc.pojo.ProcessCofigResBody;

@Repository
@Transactional(readOnly = false)
public class GenericDaoImpl<T> implements GenericDao<T> {

	@Autowired
	EntityManager entityManager;

	@Autowired
	private DBServiceData dbServiceData;

	@Autowired
	private TicketCreationConfigDao ticketCreationConfigDao;

	@Autowired
	private LayoutConfigDao layoutConfigDao;

	@Autowired
	private TemplateReportLayOutDao templateReportLayOutDao;

	@Autowired
	private RulesConfigDao rulesConfigDao;

	@Autowired
	private RulesDefinitionDao rulesDefinitionDao;

	protected static final String COPY_JOB_PFC = "copyJobPFC";
	protected static final String UNIONALL = " UNION ALL ";

	@Override
	public List<T> findByColumn(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue) {
		Query query = entityManager.createNativeQuery("SELECT em.* FROM  " + schema + "." + table_name + " as em "
				+ "WHERE em." + columnName + "= '" + columnValue + "' and active_flag='T'", clazz);
		return query.getResultList();
	}
	
	@Override
	public List<T> findByColumn(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue,boolean updateFlag) {
		Query query = entityManager.createNativeQuery("SELECT em.* FROM  " + schema + "." + table_name + " as em "
				+ "WHERE em." + columnName + "= '" + columnValue + "' and active_flag='T' and is_update="+updateFlag, clazz);
		return query.getResultList();
	}
	
	@Override
	public List<T> findAll(Class<T> clazz, String schema, String table_name)
			 {
		Query query = entityManager.createNativeQuery("SELECT em.* FROM  " + schema + "." + table_name + " as em ", clazz);
		return query.getResultList();
	}

	@Override
	public List<LayoutConfig> saveAllLayOutConfig(List<LayoutConfig> layoutConfigs) {
		List<LayoutConfig> response = (List<LayoutConfig>) layoutConfigDao.saveAll(layoutConfigs);
		return response;
	}

	@Override
	public List<KsdOutPutFileDetails> getDistinctFileNames(Class<KsdOutPutFileDetails> clazz, String schema,
			String table_name, List<ColumnConditionParam> columnConditionParamList) {
		LoggerUtil.log(getClass(), Level.INFO, "getDistinctFileNames", "In method ");
		Query query = null;
		List resultList = null;
		List<String> colName = new ArrayList<>();
		List<String> colCondition = new ArrayList<>();
		List<String> columnValues = new ArrayList<>();
		try {
			for (int i = 0; i < columnConditionParamList.size(); i++) {
				colName.add((columnConditionParamList.get(i)).getColName());
				colCondition.add((columnConditionParamList.get(i)).getColCondion());
				columnValues.add((columnConditionParamList.get(i)).getColValue());
			}
			StringBuffer strBuffer = new StringBuffer(
					"SELECT em.* FROM " + schema + "." + table_name + " as em  WHERE ");

			for (int i = 0; i < colCondition.size(); i++) {
				if (colCondition.get(i).equals("ntlk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("lk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("is")) {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + ""
							+ columnValues.get(i) + "");
				}

				else {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + "'"
							+ columnValues.get(i) + "'");
				}

				if (i != (colCondition.size() - 1)) {
					strBuffer.append(" and ");
				}
			}
			String sqlQuery = String.valueOf(strBuffer);
			sqlQuery = sqlQuery + "order by id desc";
			query = entityManager.createNativeQuery(sqlQuery, clazz);
			resultList = query.getResultList();
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getDistinctFileNames", "Exception occured - " + e.getMessage());
		}
		LoggerUtil.log(getClass(), Level.INFO, "getDistinctFileNames", " method ended ! ");
		return resultList;
	}

	@Override
	public List<T> findByMultiColumnCondition(Class<T> clazz, String schema, String table_name,
			List<ColumnConditionParam> columnConditionParamList) {
		LoggerUtil.log(getClass(), Level.INFO, "findByMultiColumnCondition", "Inside  method ");
		Query query = null;
		List resultList = null;
		List<String> colName = new ArrayList<>();
		List<String> colCondition = new ArrayList<>();
		List<String> columnValues = new ArrayList<>();
		try {

			for (int i = 0; i < columnConditionParamList.size(); i++) {
				colName.add((columnConditionParamList.get(i)).getColName());
				colCondition.add((columnConditionParamList.get(i)).getColCondion());
				columnValues.add((columnConditionParamList.get(i)).getColValue());
			}
			StringBuffer strBuffer = new StringBuffer(
					"SELECT em.* FROM " + schema + "." + table_name + " as em  WHERE ");

			for (int i = 0; i < colCondition.size(); i++) {
				if (colCondition.get(i).equals("ntlk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("lk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("is")) {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + ""
							+ columnValues.get(i) + "");
				}

				else {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + "'"
							+ columnValues.get(i) + "'");
				}

				if (i != (colCondition.size() - 1)) {
					strBuffer.append(" and ");
				}
			}
			String sqlQuery = String.valueOf(strBuffer);
			query = entityManager.createNativeQuery(sqlQuery, clazz);
			resultList = query.getResultList();
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "findByMultiColumnCondition",
					"Exception occured - " + e.getMessage());
		}
		LoggerUtil.log(getClass(), Level.INFO, "findByMultiColumnCondition", "method ended !");
		return resultList;
	}

//	@Override
//	public Boolean 
	@Override
	public Boolean deleteRowsByUID(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue) {
		String sqlStrStmt = "DELETE FROM  " + schema + "." + table_name + " as em " + "WHERE em." + columnName + "= '"
				+ columnValue + "'";
		Query query = entityManager.createNativeQuery(sqlStrStmt);
		query.executeUpdate();
		return true;
	}

	@Override
	public int updateByColumn(Class<T> clazz, String schema, String table_name, String columnName, String columnValue) {
		Query query = entityManager.createNativeQuery("UPDATE  " + schema + "." + table_name + " set  " + "WHERE em."
				+ columnName + "= '" + columnValue + "'", clazz);
		return query.executeUpdate();

	}

	@Override
	public List<BusinessUnit> findBUnits(Long PJMId) {
		Query query = entityManager.createNativeQuery(
				"select bu.* from common.business_unit bu where bu.id = (select buops.bu_id from common.business_unit_ops buops,common.process_job_mapping pjm where pjm.business_unit_ops_id=buops.id and pjm.id="
						+ PJMId + " )",
				BusinessUnit.class);
		return query.getResultList();
	}

	@Override
	public List<BusinessOps> findBOps(Long PJMId) {

		Query query = entityManager.createNativeQuery(
				"select bops.* from common.business_ops bops where bops.id=(select buops.bu_ops_id from common.business_unit_ops buops,common.process_job_mapping pjm where pjm.business_unit_ops_id=buops.id and pjm.id="
						+ PJMId + " )",
				BusinessOps.class);
		return query.getResultList();
	}

	@Override
	public List<T> fetchValInBetween(Class<T> clazz, String schema, String table_name, String startTime, String endTime,
			String columnName) {

		String sqlStrStmt = "SELECT em.* FROM  " + schema + "." + table_name + " as em " + "WHERE em." + columnName
				+ " between '" + startTime + "' and '" + endTime + "' and active_flag='T' ";
		Query query = entityManager.createNativeQuery(sqlStrStmt, clazz);
		List lst = query.getResultList();
		return query.getResultList();
	}

	@Override
	public List<T> findRecordByColumn(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue) {
		Query query = entityManager.createNativeQuery("SELECT em.* FROM  " + schema + "." + table_name + " as em "
				+ "WHERE em." + columnName + "= '" + columnValue + "'", clazz);
		return query.getResultList();
	}

	@Override
	public List<T> findActiveRecordByColumn(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue) {
		Query query = entityManager.createNativeQuery("SELECT em.* FROM  " + schema + "." + table_name + " as em "
				+ "WHERE em." + columnName + "= '" + columnValue + "' and active_flag='T'", clazz);
		return query.getResultList();
	}

	@Override
	public CommonRowBO deleteRowByUpdated(Class<T> clazz, String schema, String table_name, Long id, String updatedBy,
			Date updatedDate) {

		Query query = entityManager.createNativeQuery(
				"Update " + schema + "." + table_name + "  as em  set active_flag = 'F', updated_by = '" + updatedBy
						+ "' , updated_date= current_timestamp" + " where em.id = " + id,
				clazz);

		String status = "success";
		String message = "Record deleted succssfully";
		CommonRowBO deleterowBo = new CommonRowBO(id, message, status);

		try {

			query.executeUpdate();
			return deleterowBo;

		} catch (Exception e) {
			deleterowBo.setMessage("record has not been deleted");
			deleterowBo.setStatus("failure");
			LoggerUtil.log(getClass(), Level.ERROR, "deleteRowByUpdated", "Exception occured - " + e.getMessage());
			return deleterowBo;
		}

	}

	@Override
	public List<TemplateReportLayOut> saveAllTempalteConfig(List<TemplateReportLayOut> templateReportLayOuts) {
		List<TemplateReportLayOut> response = (List<TemplateReportLayOut>) templateReportLayOutDao
				.saveAll(templateReportLayOuts);
		return response;
	}

	@Override
	public Long deleterowby(Class<T> clazz, String schema, String tablename, Long id, String updatedBy) {

		Query query = entityManager.createNativeQuery(
				"Update " + schema + "." + tablename + "  as em  set active_flag = 'F', updated_by = '" + updatedBy
						+ "' , updated_date= current_timestamp" + " where em.id = " + id,
				clazz);

		try {

			query.executeUpdate();
			return id;

		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleterowby", "Exception occured - " + e.getMessage());
			return 0L;
		}

	}

	@Override

	public boolean deleteMultipleRows(Class<T> clazz, String schema, String tablename, List<Long> ids,
			String updatedBy) {

		Query query = entityManager.createNativeQuery(
				"Update " + schema + "." + tablename + "  as em  set active_flag = 'F', updated_by = '" + updatedBy
						+ "' , updated_date= current_timestamp" + " where em.id  IN :IDs",
				clazz);
		query.setParameter("IDs", ids);
		try {

			query.executeUpdate();
			return true;

		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteMultipleRows", "Exception occured - " + e.getMessage());
			return false;
		}

	}
	
	@Override
	public boolean deleteMultipleRows(Class<T> clazz, String schema, String tablename, List<Long> ids,
			String updatedBy,boolean updateFlag) {

		Query query = entityManager.createNativeQuery(
				"Update " + schema + "." + tablename + "  as em  set active_flag = 'F', updated_by = '" + updatedBy
						+ "' , updated_date= current_timestamp" + " where em.id  IN :IDs and em.is_update= :updateFlag",
				clazz);
		query.setParameter("IDs", ids);
		query.setParameter("updateFlag", updateFlag);
		try {

			query.executeUpdate();
			return true;

		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteMultipleRows", "Exception occured - " + e.getMessage());
			return false;
		}

	}

	@Override
	public List<T> fetchValInBetweenWithoutActiveFlag(Class<T> clazz, String schema, String table_name,
			String startTime, String endTime, String columnName) {

		String sqlStrStmt = "SELECT em.* FROM  " + schema + "." + table_name + " as em " + "WHERE em." + columnName
				+ " between '" + startTime + "' and '" + endTime + "' ";
		Query query = entityManager.createNativeQuery(sqlStrStmt, clazz);
		List lst = query.getResultList();
		return query.getResultList();
	}

	@Override
	public Boolean deleteByColumn(Class<T> clazz, String schema, String table_name, String columnName,
			String columnValue) {
		// TODO Auto-generated method stub
		Query query = entityManager.createNativeQuery("UPDATE  " + schema + "." + table_name
				+ " em set  active_flag = 'F' " + "WHERE em." + columnName + "= '" + columnValue + "'", clazz);
		try {

			query.executeUpdate();
			return true;

		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteByColumn", "Exception occured - " + e.getMessage());
			return false;
		}
	}

	@Override
	public List<TicketCreationConfig> saveAllTicket(List<TicketCreationConfig> ticketCreationConfigs) {
		List<TicketCreationConfig> response = (List<TicketCreationConfig>) ticketCreationConfigDao
				.saveAll(ticketCreationConfigs);
		return response;
	}

	@Transactional
	@Override
	public Boolean copyJobPFC(ProcessFeatureConfigCopyJob p) {
		LoggerUtil.log(this.getClass(), Level.INFO, "copyJobPFC", "Inside copyJobPFC method");
		Long oldPjmId = p.getPjmIdCopied();
		String adid = p.getCreatedBy();
		Long newPjmId = p.getProcessJobMapping().getId();
		String jobName = p.getJobName();
		String eftName = p.getEftSubject();
		boolean flag = false;

		Query queryKsdConfig = entityManager.createNativeQuery(
				"insert into emails_scheduler.ksd_config (process_job_mapping_id, primary_job_name, job_stream, frequency, job_cut_off_time, turnaround_time, job_schedule_time, eft_name, file_type, maestro_task_name, daily_task_report_subject_name_outlook, active_flag, created_date, created_by, updated_by, updated_date,adx_script_id,eft_trigger_time, maestro_task_type, is_jira_used, jira_task_name, jira_task_type, jira_project_type,skip_control_m_report,direct_tba_update) select "
						+ newPjmId + ", '" + jobName
						+ "', a.job_stream,a.frequency, a.job_cut_off_time, a.turnaround_time, a.job_schedule_time, '"
						+ eftName
						+ "', a.file_type, a.maestro_task_name, a.daily_task_report_subject_name_outlook, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.adx_script_id,a.eft_trigger_time, a.maestro_task_type, a.is_jira_used, a.jira_task_name, a.jira_task_type, a.jira_project_type, a.skip_control_m_report, a.direct_tba_update from emails_scheduler.ksd_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		Query queryKsdFileDetails = entityManager.createNativeQuery(
				"INSERT INTO emails_scheduler.ksd_file_details (process_job_mapping_id, file_name, file_type, file_format_type, variation, subject, sender, delimiter, min_threshold, max_threshold, ppt_identifier, ppt_identifier_type, active_flag, date_format, created_date, created_by, updated_by, updated_date, record_identifier_col,sheet_name_wout_space,FILE_NAME_WOUT_SPACE,SHEET_NAME, source, path, tool, domain, query_jcl_name, date_generated, date_frequency, date_period, date_interval, prev_report_file_name, prev_report_file_name_ws, subfolder, action, use_labelling_rpt, whitelist_SSN, client, database, sql_query, vertical_identifier, report_flag, append, append_format, whitelist, record_cnt_check,file_name_template,append_name_flag, sampling_count  ) select "
						+ newPjmId
						+ ", a.file_name, a.file_type, a.file_format_type, a.variation, a.subject, a.sender, a.delimiter, a.min_threshold, a.max_threshold, a.ppt_identifier, a.ppt_identifier_type, a.active_flag, a.date_format, current_timestamp,'"
						+ adid
						+ "', '', current_timestamp, a.record_identifier_col,a.sheet_name_wout_space,a.FILE_NAME_WOUT_SPACE,a.SHEET_NAME, a.source, a.path, a.tool, a.domain, a.query_jcl_name, a.date_generated, a.date_frequency, a.date_period, a.date_interval, a.prev_report_file_name, a.prev_report_file_name_ws, a.subfolder, a.action, a.use_labelling_rpt, a.whitelist_SSN, a.client, a.database, a.sql_query, a.vertical_identifier, a.report_flag, a.append, a.append_format, a.whitelist, a.record_cnt_check,a.file_name_template,a.append_name_flag, a.sampling_count   from emails_scheduler.ksd_file_details a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		Query queryTicketCreationConfig = entityManager.createNativeQuery(
				"INSERT INTO maestro.ticket_creation_config(process_job_mapping_id, task_owner, ticket_type, task_type, title, division, assignee, status, responsible_party, priority, due_days, interested_parties, iteration, project_id, complexity, service_group, business_area, percent_complete, estimated_work_hours, control_account, work_package, billing_num_desc, billing_number, sdlc_discipline, new_discussion, attachment, interested_fields, active_flag, created_date, created_by, updated_by, updated_date,unsecured_attachment) select  "
						+ newPjmId
						+ ", a.task_owner, a.ticket_type, a.task_type, a.title, a.division, a.assignee, a.status, a.responsible_party, a.priority, a.due_days, a.interested_parties, a.iteration, a.project_id, a.complexity, a.service_group, a.business_area, a.percent_complete, a.estimated_work_hours, a.control_account, a.work_package, a.billing_num_desc, a.billing_number, a.sdlc_discipline, a.new_discussion,a.attachment, a.interested_fields, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp,a.unsecured_attachment from maestro.ticket_creation_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryTaskUpdateConfig = entityManager.createNativeQuery(
				"INSERT INTO maestro.task_update_config(process_job_mapping_id, maestro_task_name, new_discussion, attachment, interested_fields, active_flag, created_date, created_by, updated_by, updated_date, type,unsecured_attachment) select  "
						+ newPjmId
						+ ",a.maestro_task_name, a.new_discussion, a.attachment,a.interested_fields, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.type,a.unsecured_attachment from maestro.task_update_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryJiraTicketCreationConfig = entityManager.createNativeQuery(
				"INSERT INTO jira.ticket_creation_config(process_job_mapping_id, reporter, summary, issue_type, priority, applicable_clients, reporting_group, description, status, attachment, active_flag, due_date, project, domain, assignee, epic_name, assignee_email, begin_date, epic_type) select  "
						+ newPjmId
						+ ", a.reporter, a.summary, a.issue_type, a.priority, a.applicable_clients, a.reporting_group, a.description, a.status, a.attachment, a.active_flag, a.due_date, a.project, a.domain, a.assignee,a.epic_name, a.assignee_email, a.begin_date, a.epic_type from jira.ticket_creation_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);
		
		// checked
		Query queryJiraTaskUpdateConfig = entityManager.createNativeQuery(
				"INSERT INTO jira.task_update_config(process_job_mapping_id, issue_type, new_comments, interested_parties, attachment, active_flag) select  "
						+ newPjmId
						+ ",a.issue_type, a.new_comments, a.interested_parties, a.attachment, a.active_flag from jira.task_update_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);
		
		// checked
		Query queryLayoutConfig = entityManager.createNativeQuery(
				"INSERT INTO layout_rule.layout_config (process_job_mapping_id, file_name, record_type, mf_field_name, start_pos, length, record_format, mf_field_wout_space, field_type, field_template, ppt_identifier, mandatory, record_identifier, date_format, field_no, active_flag, created_date, created_by, updated_by, updated_date, record_identifier_val,sheet_name_wout_space,FILE_NAME_WOUT_SPACE,SHEET_NAME, value_details,labelling_json ) select "
						+ newPjmId
						+ ", a.file_name, a.record_type, a.mf_field_name, a.start_pos, a.length, a.record_format, a.mf_field_wout_space, a.field_type, a.field_template, a.ppt_identifier, a.mandatory, a.record_identifier, a.date_format, a.field_no, a.active_flag, current_timestamp, '"
						+ adid
						+ "', '', current_timestamp, a.record_identifier_val,a.sheet_name_wout_space,a.FILE_NAME_WOUT_SPACE,a.SHEET_NAME, a.value_details,a.labelling_json  from layout_rule.layout_config  a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryTbaInquiryConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.tba_inquiry_config(process_job_mapping_id, inquiry_name, inquiry_def_name, panel_id, tba_field_name, json_key, sub_json_key, metadata, identifier, active_flag, created_date, created_by, updated_by, updated_date, field_type, par_nm, record_identifier,eff_date_type,eff_from_date,eff_to_date,row_matrix,column_matrix , sequence, condition_json, estimate_mode) select "
						+ newPjmId
						+ ", a.inquiry_name, a.inquiry_def_name, a.panel_id, a.tba_field_name, a.json_key, a.sub_json_key, a.metadata, a.identifier, a.active_flag, current_timestamp,'"
						+ adid
						+ "', null, current_timestamp, a.field_type, a.par_nm, a.record_identifier,a.eff_date_type,a.eff_from_date,a.eff_to_date,a.row_matrix,a.column_matrix , a.sequence, a.condition_json, a.estimate_mode from tba.tba_inquiry_config  a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryTbaUpdateConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.tba_update_config(process_job_mapping_id, update_name, tba_field_name, event_name, activity_id, panel_id, class_id, json_key, base_key, sub_key, meta_data, trans_id, active_flag, created_date, created_by, updated_by, updated_date, value, panel_disc, sequence, record_identifier, identifier, par_nm, rerun_flag, add_manual_flag, act_lng_desc,TBA_UPDATE_ACTION, OVERRIDE_EDITS, event_long_desc, parent_tba_update_id, estimate_tba_inquiries, group_related_panels, skip_trans) select "
						+ newPjmId
						+ ", a.update_name, a.tba_field_name, a.event_name, a.activity_id, a.panel_id, a.class_id, a.json_key, a.base_key, a.sub_key, a.meta_data, a.trans_id, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.value, a.panel_disc, a.sequence, a.record_identifier, a.identifier, a.par_nm, a.rerun_flag, a.add_manual_flag, a.act_lng_desc, a.TBA_UPDATE_ACTION, a.OVERRIDE_EDITS, a.event_long_desc, a.parent_tba_update_id, a.estimate_tba_inquiries, a.group_related_panels, a.skip_trans from tba.tba_update_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryTbaEventHistInqConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.event_hist_inq_config(process_job_mapping_id, event_hist_def_name, eff_from_date, eff_to_date, event_name, act_long_desc, active_flag, created_date, created_by, updated_by, updated_date, tba_field_name, json_key, field_type, par_nm , activity_id,    manual_flag) select "
						+ newPjmId
						+ ", a.event_hist_def_name, a.eff_from_date, a.eff_to_date, a.event_name, a.act_long_desc, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.tba_field_name, a.json_key, a.field_type, a.par_nm , a.activity_id ,    a.manual_flag  from tba.event_hist_inq_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked//to be checked in with changes
		Query queryTbaNoticeInqConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.tba_notice_inq_config(process_job_mapping_id, notice_name, notice_id, client_id, inquiry_def_name, tba_field_name, json_key, sub_json_key, metadata, field_type, par_nm, record_identifier, identifier, active_flag, created_date, created_by, updated_by, updated_date,add_manual_flag) select "
						+ newPjmId
						+ ", a.notice_name, a.notice_id, a.client_id, a.inquiry_def_name, a.tba_field_name, a.json_key, a.sub_json_key, a.metadata, a.field_type, a.par_nm, a.record_identifier, a.identifier, a.active_flag, current_timestamp,'"
						+ adid
						+ "', null, current_timestamp,a.add_manual_flag from tba.tba_notice_inq_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryTbaMatchConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.tba_match_config(process_job_mapping_id, file_name, report_identifier, mf_field_name, tba_field_name, rule_name, corrective_action, active_flag, created_date, created_by, updated_by, updated_date, mf_field_wout_space, inquiry_def_name, result_field, identifier, actions,field_name_wout_space,MATCH_TYPE,FILE_NAME_DEST,REPORT_IDENTIFIER_DEST,MF_FIELD_NAME_DEST,MF_FIELD_WOUT_SPACE_DEST,sheet_name,sheet_name_wout_space,sheet_name_dest,sheet_name_dest_wout_space,file_name_wout_space,file_name_dest_wout_space,ppt_verify_tba ) select "
						+ newPjmId
						+ ", a.file_name, a.report_identifier, a.mf_field_name, a.tba_field_name, a.rule_name, a.corrective_action, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.mf_field_wout_space, a.inquiry_def_name, a.result_field, a.identifier, a.actions, a.field_name_wout_space,a.MATCH_TYPE,a.FILE_NAME_DEST,a.REPORT_IDENTIFIER_DEST,a.MF_FIELD_NAME_DEST,a.MF_FIELD_WOUT_SPACE_DEST,a.sheet_name,a.sheet_name_wout_space,a.sheet_name_dest,a.sheet_name_dest_wout_space,a.file_name_wout_space,a.file_name_dest_wout_space,a.ppt_verify_tba from tba.tba_match_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryProcessControlConfig = entityManager.createNativeQuery(
				"       INSERT INTO tba.process_control_config( process_job_mapping_id, application, field_name, rule_name, corrective_action, active_flag, created_date, created_by, updated_by, updated_date, field_name_wout_space, identifier, actions,application_wout_space,actions_bytea) select "
						+ newPjmId
						+ ",a.application, a.field_name, a.rule_name, a.corrective_action, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.field_name_wout_space, a.identifier, a.actions, a.application_wout_space ,a.actions_bytea from tba.process_control_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryNotificationMailConfig = entityManager.createNativeQuery(
				"INSERT INTO common.notification_mail_config( process_job_mapping_id, condition, to_list, cc_list, subject, append_subject, active_flag, interested_fields, attachment_name, remarks, created_date, created_by, updated_by, updated_date,mail_body,password ) select "
						+ newPjmId
						+ ",  a.condition, a.to_list, a.cc_list, a.subject, a.append_subject, a.active_flag, a.interested_fields, a.attachment_name, a.remarks, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp ,a.mail_body,a.password  from common.notification_mail_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryNotificationReportConfig = entityManager.createNativeQuery(
				"INSERT INTO common.notification_report_config(process_job_mapping_id, application, file, file_type, report_name, subject, active_flag, append_subject, path, remarks, created_date, created_by, updated_by, updated_date, subfolder , item_column_name , action,subject_email_password,password) select "
						+ newPjmId
						+ ", a.application, a.file, a.file_type, a.report_name, a.subject, a.active_flag, a.append_subject, a.path, a.remarks, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.subfolder , a.item_column_name , a.action,a.subject_email_password,a.password from common.notification_report_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		// checked
		Query queryClientConfigJobs = entityManager.createNativeQuery(
				"INSERT INTO emails_scheduler.client_config_jobs( process_job_mapping_id,child_job_name, active_flag, created_date, created_by, updated_by, updated_date,ksd_config_id) select "
						+ newPjmId + ",a.child_job_name, a.active_flag, current_timestamp, '" + adid
						+ "', null, current_timestamp,(select h.id from emails_scheduler.ksd_config h where h.process_job_mapping_id = "
						+ newPjmId
						+ "  and h.active_flag='T' ) from emails_scheduler.client_config_jobs a where a.active_flag = 'T'  and  a.ksd_config_id =(select id from emails_scheduler.KSD_CONFIG kc where kc.PROCESS_JOB_MAPPING_ID= "
						+ oldPjmId + "  and kc.active_flag = 'T')");

		// checked
		Query queryJobSchedule = entityManager.createNativeQuery(
				"INSERT INTO emails_scheduler.job_schedule(  ksd_config_id,interval, days_of_week, day_of_month, frequency, index, month, skip_holidays, bimonthly_days, custom_dates, skip_weekends, time_zone, active_flag, created_date, created_by, updated_by, updated_date) select  (select h.id from emails_scheduler.ksd_config h where h.process_job_mapping_id = "
						+ newPjmId
						+ "  and h.active_flag='T' ),a.interval, a.days_of_week, a.day_of_month, a.frequency, a.index, a.month, a.skip_holidays, a.bimonthly_days, a.custom_dates, a.skip_weekends, a.time_zone, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp from emails_scheduler.job_schedule a where a.active_flag = 'T'  and  a.ksd_config_id =(select id from emails_scheduler.KSD_CONFIG kc where kc.PROCESS_JOB_MAPPING_ID= "
						+ oldPjmId + " and kc.active_flag = 'T')");

		Query queryCommentInqConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.tba_comment_inq_config( process_job_mapping_id, inquiry_def_name, eft_from_date, eft_to_date, tba_field_name, json_key, sub_json_key, field_type, par_nm, active_flag, created_date, created_by, updated_by, updated_date, is_update)select "
						+ newPjmId
						+ ", a.inquiry_def_name, a.eft_from_date, a.eft_to_date, a.tba_field_name, a.json_key, a.sub_json_key, a.field_type, a.par_nm, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.is_update from tba.tba_comment_inq_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		Query queryPendingEventInqConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.pending_event_inq_config(process_job_mapping_id, pendg_evnt_def_name, event_name, event_long_desc, identifier, tba_field_name, json_key, par_nm, activity_id, active_flag, created_date, created_by, updated_by, updated_date,panel_id,panel_disc,meta_data,trans_id,base_key,sub_key,class_id,manual_flag,sequence,identify_flag, process_multiple_instances, critical_edits)select "
						+ newPjmId
						+ ",  a.pendg_evnt_def_name, a.event_name, a.event_long_desc, a.identifier, a.tba_field_name, a.json_key, a.par_nm, a.activity_id, a.active_flag, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp,a.panel_id,a.panel_disc,a.meta_data,a.trans_id,a.base_key,a.sub_key,a.class_id,a.manual_flag,a.sequence,a.identify_flag, a.process_multiple_instances, a.critical_edits from tba.pending_event_inq_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		Query queryPCF = entityManager.createNativeQuery(
				"insert into  common.process_feature_config (phase_names, business_unit_name, client_name, client_code, job_copied, pjm_id_copied, business_ops_name, eft_subject, eft_subject_copied, process_type, process_name, job_name, process_job_mapping_id, active_flag, created_by, created_date, config_status, updated_by, ksd_name, updated_date, approved_by, approved_date) select "
						+ "a.phase_names,'" + p.getBusinessUnitName() + "', :clientName, '"
						+ p.getClientCode() + "','" + p.getJobCopied() + "'," + p.getPjmIdCopied() + ",'"
						+ p.getBusinessOpsName() + "', :eftSubject,:eftSubjectCopied, '"
						+ p.getProcessType() +"', :processName, '" + p.getJobName() + "'," + newPjmId
						+ ",a.active_flag,'" + p.getCreatedBy() + "',current_timestamp" + ",'In-progress', '" + adid
						+ "','" + p.getKsdName()
						+ "', current_timestamp , null , null  from common.process_feature_config a where a.active_flag='T' and  a.process_job_mapping_id = "
						+ oldPjmId);
		queryPCF.setParameter("clientName",p.getClientName());
		queryPCF.setParameter("eftSubjectCopied",p.getEftSubjectCopied());
		queryPCF.setParameter("eftSubject",p.getEftSubject());
		queryPCF.setParameter("processName",p.getProcessName());
		
		Query queryEventInqConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.event_inquiry_config(process_job_mapping_id, event_name, event_inquiry_def_name, panel_id, tba_field_name, json_key, metadata, base_key, sub_key, trans_id, par_nm, eff_date_type, eff_from_date, eff_to_date, effective_date, active_flag, record_identifier, sequence, created_date, created_by, updated_by, updated_date, field_type, pending_event)select "
						+ newPjmId
						+ ", a.event_name, a.event_inquiry_def_name, a.panel_id, a.tba_field_name, a.json_key, a.metadata, a.base_key, a.sub_key, a.trans_id, a.par_nm, a.eff_date_type, a.eff_from_date, a.eff_to_date, a.effective_date, a.active_flag, a.record_identifier, a.sequence, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp, a.field_type, a.pending_event from tba.event_inquiry_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);
		
		Query queryTbaWelfareInqConfig = entityManager.createNativeQuery(
				"INSERT INTO tba.tba_welfare_inquiry_config(process_job_mapping_id, event_name, event_long_desc, practice_area_code, tba_field_name, field_type, active_flag, json_key, sub_json_key, par_name, created_date, created_by, updated_by, updated_date)select "
						+ newPjmId
						+ ", a.event_name,a.event_long_desc, a.practice_area_code, a.tba_field_name, a.field_type, a.active_flag, a.json_key, a.sub_json_key, a.par_name, current_timestamp, '"
						+ adid
						+ "', null, current_timestamp from tba.tba_welfare_inquiry_config a where a.active_flag = 'T'  and  a.process_job_mapping_id = "
						+ oldPjmId);

		try {
			queryKsdConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC, "ksd config table is updated successfully");
			queryKsdFileDetails.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC, "ksd file details table is updated successfully");
			queryClientConfigJobs.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"client config jobs table is updated successfully");
			queryJobSchedule.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC, "job schedule table is updated successfully");
			queryLayoutConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC, "layout config table is updated successfully");
			queryTbaInquiryConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"tba inquiry config table is updated successfully");
			queryTbaUpdateConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"tba update config table is updated successfully");
			queryTbaEventHistInqConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"event hist inq config table is updated successfully");
			queryTbaNoticeInqConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"notice inq config table is updated successfully");
			queryTbaMatchConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC, "tba match config table is updated successfully");
			queryProcessControlConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"process control config table is updated successfully");
			queryNotificationMailConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"notification mail config table is updated successfully");
			queryNotificationReportConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"notification report config table is updated successfully");
			queryTicketCreationConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"ticket creation config table is updated successfully");
			queryTaskUpdateConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"task update config table is updated successfully");
			queryJiraTaskUpdateConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"jira task update config table is updated successfully");
			queryJiraTicketCreationConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"jira ticket creation config table is updated successfully");
			queryCommentInqConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"comment inq config table is updated successfully");
			queryPendingEventInqConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"pending event inq config table is updated successfully");
			queryPCF.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC,
					"process feature config table is updated successfully");
			queryEventInqConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC, "Event inq config table is updated successfully");
			
			queryTbaWelfareInqConfig.executeUpdate();
			LoggerUtil.log(this.getClass(), Level.INFO, COPY_JOB_PFC, "Welfare inq config table is updated successfully");

			flag = true;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, COPY_JOB_PFC, "exception thrown   >>>>   " + e);
			rulesConfigDao.commonupdate(adid, newPjmId);
			rulesDefinitionDao.commonupdate(adid, newPjmId);
		}
		return flag;
	}

	@Override
	public ProcessCofigResBody getAllFilterdProcessConfigDetails(ProcessCofigReqBody reqBody)
			throws IllegalAccessException, InvocationTargetException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllFilterdProcessConfigDetails",
				"getAllProcessConfigDetails for alightID: " + reqBody.getAdid());

		String countQueryString = "select count(d)";
		String dataQueryString = "select distinct new com.wipro.fipc.pojo.CustomPFCClientCodeBO(d.id,d.businessUnitName,d.clientName,d.clientCode,d.businessOpsName,d.processName,d.jobName,d.updatedBy,d.processJobMapping.id,d.updatedDate,d.eftSubject,d.processType,d.configStatus,d.phaseNames,d.ksdName,d.approvedBy,d.approvedDate)";

		String queryString = " from com.wipro.fipc.entity.common.RoleConfig"
				+ " a right join com.wipro.fipc.entity.ClientDetails b on b.id = a.clientDetails.id right join "
				+ "com.wipro.fipc.entity.BusinessUnit c on c.id=a.businessUnit.id right join "
				+ "com.wipro.fipc.entity.filelayout.ProcessFeatureConfig d on (d.businessUnitName =c.unitName and Trim(d.clientCode) = Trim(b.clientCode)) where"
				+ " d.activeFlag = 'T'";

		String orderByString = " ORDER BY d.updatedDate desc";

		boolean hasFilter = true;

		switch (reqBody.getFilterType().toUpperCase()) {
		case "APPROVEDBY":
			queryString = queryString + " and upper(d.approvedBy) like upper(:filterValue)";
			break;
		case "BUSINESSOPSNAME":
			queryString = queryString + " and upper(d.businessOpsName) like upper(:filterValue)";
			break;
		case "BUSINESSUNITNAME":
			queryString = queryString + " and upper(d.businessUnitName) like upper(:filterValue)";
			break;
		case "CLIENTCODE":
			queryString = queryString + " and upper(d.clientCode) like upper(:filterValue)";
			break;
		case "CLIENTNAME":
			queryString = queryString + " and upper(d.clientName) like upper(:filterValue)";
			break;
		case "CONFIGSTATUS":
			queryString = queryString + " and upper(d.configStatus) like upper(:filterValue)";
			break;
		case "EFTSUBJECT":
			queryString = queryString + " and upper(d.eftSubject) like upper(:filterValue)";
			break;
		case "JOBNAME":
			queryString = queryString + " and upper(d.jobName) like upper(:filterValue)";
			break;
		case "KSDNAME":
			queryString = queryString + " and upper(d.ksdName) like upper(:filterValue)";
			break;
		case "PROCESSNAME":
			queryString = queryString + " and upper(d.processName) like upper(:filterValue)";
			break;
		case "PROCESSTYPE":
			queryString = queryString + " and upper(d.processType) like upper(:filterValue)";
			break;
		case "UPDATEDBY":
			queryString = queryString + " and upper(d.updatedBy) like upper(:filterValue)";
			break;
		default:
			hasFilter = false;
			break;
		}

		queryString = queryString + " and a.active='t' and a.adid = :adid";

		Query dataQuery = entityManager.createQuery(dataQueryString + queryString + orderByString);
		Query countQuery = entityManager.createQuery(countQueryString + queryString);

		dataQuery.setParameter("adid", reqBody.getAdid());

		countQuery.setParameter("adid", reqBody.getAdid());
		if (reqBody.getRowCount() < 0) {
			return new ProcessCofigResBody((Long) countQuery.getResultList().get(0), dataQuery.getResultList());
		}

		if (hasFilter) {
			dataQuery.setParameter("filterValue", "%" + reqBody.getFilterValue() + "%");
			countQuery.setParameter("filterValue", "%" + reqBody.getFilterValue() + "%");
		}

		dataQuery.setFirstResult((reqBody.getPageNumber() - 1) * reqBody.getRowCount());
		dataQuery.setMaxResults(reqBody.getRowCount());

		return new ProcessCofigResBody((Long) countQuery.getResultList().get(0), dataQuery.getResultList());
	}
	
	@Override
	public List<BigInteger> getProcessIdList(String alightID, String buID, String clientID, String buOpsID) {
		List<BigInteger> processIdList = new ArrayList<>();
		Query query = entityManager.createNativeQuery(
				"SELECT distinct p.id from (select distinct a.id from common.process a left join common.role_config b on b.process_id = a.id where b.process_id is not null and b.adid ='"
						+ alightID + "' and b.business_unit_id = " + buID + " and b.client_id = " + clientID
						+ "  and b.active = 'true' and b.business_ops_id=" + buOpsID + UNIONALL
						+ " select distinct d.id from common.role_config a inner join common.business_unit_ops b on a.business_unit_id = b.bu_id and a.business_ops_id = b.bu_ops_id inner join common.process_job_mapping c on b.id = c.business_unit_ops_id and a.client_id = c.client_id inner join common.process d on c.process_id = d.id where a.process_id is null and a.adid = '"
						+ alightID + "' and c.active_flag = 'T' and a.business_unit_id = " + buID
						+ " and a.business_ops_id = " + buOpsID + " and a.client_id = " + clientID + " UNION ALL"
						+ " select distinct d.id from common.role_config a inner join common.business_unit_ops b on a.business_unit_id=b.bu_id inner join common.process_job_mapping c on b.id = c.business_unit_ops_id and a.client_id = c.client_id inner join common.process d on c.process_id =d.id where a.process_id is null and c.active_flag ='T' and a.adid = '"
						+ alightID + "' and a.business_unit_id = " + buID + "  and b.bu_ops_id = " + buOpsID
						+ " and a.client_id = " + clientID + " and a.business_ops_id is null )p");

		return query.getResultList();
	}

	@Override
	public List<BigInteger> getOpsIdList(String alightID, String buID) {
		Query query = entityManager
				.createNativeQuery("SELECT distinct p.id from (SELECT distinct a.id FROM common.business_ops "
						+ " as a left join common.role_config  as b on b.business_ops_id = a.id where b.business_ops_id is NOT NULL and b.active='true' and  b.adid = '"
						+ alightID + "' and  b.business_unit_id = " + buID + UNIONALL
						+ "select distinct a.id from common.business_ops a left join common.business_unit_ops b on b.bu_ops_id = a.id left join common.role_config c on c.business_unit_id = b.bu_id where  b.bu_id = "
						+ buID + " and c.active='true'  and c.adid = '" + alightID
						+ "' and c.business_ops_id is null )p");
		return query.getResultList();
	}

	@Override
	public List<BigInteger> getPrimList(String adid) {
		Query query = entityManager.createNativeQuery(
				"select distinct p.id from ( select distinct c.id from common.business_unit_ops a inner join common.role_config b on a.bu_id = b.business_unit_id and a.bu_ops_id = b.business_ops_id inner join common.process_job_mapping c on c.business_unit_ops_id = a.id and c.client_id = b.client_id and c.process_id = b.process_id where b.adid = '"

						+ adid

						+ "' and b.process_id is not null and b.business_ops_id is not null and b.active = 'true' and c.id in (select distinct g.pjm_id from orch_transaction.request_queue g )  "

						+ " UNION ALL "

						+ " select distinct c.id from common.business_unit_ops a inner join common.role_config b on a.bu_id = b.business_unit_id and a.bu_ops_id = b.business_ops_id inner join common.process_job_mapping c on c.business_unit_ops_id = a.id and c.client_id = b.client_id where b.adid = '"

						+ adid
						+ "' and b.process_id is null and b.business_ops_id is not null and b.active = 'true'  and c.id in (select distinct g.pjm_id from orch_transaction.request_queue g )"

						+ " UNION ALL "

						+ " select distinct c.id from common.business_unit_ops a inner join common.role_config b on a.bu_id = b.business_unit_id inner join common.process_job_mapping c on c.business_unit_ops_id = a.id and c.client_id = b.client_id and b.process_id = c.process_id where b.adid = '"

						+ adid
						+ "' and b.business_ops_id is null and b.process_id is not null and b.active='true'  and c.id in (select distinct g.pjm_id from orch_transaction.request_queue g )"

						+ " UNION ALL "

						+ " select distinct  c.id from common.business_unit_ops a inner join common.role_config b on a.bu_id = b.business_unit_id inner join common.process_job_mapping c on c.business_unit_ops_id = a.id and c.client_id = b.client_id where b.adid = '"

						+ adid
						+ "' and b.process_id is null and b.business_ops_id is null and b.active='true'  and c.id in (select distinct g.pjm_id from orch_transaction.request_queue g )) p");

		return query.getResultList();
	}

	@Override
	public int deletePreviousFile(Class<T> clazz, String schema, String table_name, String pjmId, String fileType) {
		Query query = entityManager.createNativeQuery(
				"UPDATE  " + schema + "." + table_name + " set active_flag='F' WHERE process_job_mapping_id= '" + pjmId
						+ "' and file_type='" + fileType + "'and active_flag='T'",
				clazz);
		return query.executeUpdate();
	}

	@Override
	public List<String> getFileNameByFileType(String schema, String table_name, String pjmId, String fileType) {
		Query query = entityManager.createNativeQuery("SELECT DISTINCT(file_name) from " + schema + "." + table_name
				+ " where process_job_mapping_id=" + pjmId + " and file_type='" + fileType + "' and active_flag='T'");
		return query.getResultList();
	}

	@Override
	public int deletePreviousFile(Class<LayoutConfig> clazz, String schema, String table_name, String pjmId,
			List<String> fileName) {
		Query query = entityManager.createNativeQuery(
				"UPDATE  " + schema + "." + table_name + " set active_flag='F' where process_job_mapping_id='" + pjmId
						+ "' and file_name IN :fileName and active_flag='T'",
				clazz);
		query.setParameter("fileName", fileName);
		return query.executeUpdate();
	}

	@Override
	public List<T> findByMultiColumn(Class<T> clazz, String schema, String table_name, String clientId,
			String panelIdParam, String transIdParam) {

		Query query = entityManager.createNativeQuery(
				"SELECT * FROM " + schema + "." + table_name + " WHERE " + "client_id" + "= '" + clientId
						+ "' AND panel_id IN (" + panelIdParam + ") " + " AND trans_id IN (" + transIdParam + ")",
				clazz);

		return query.getResultList();
	}

}
