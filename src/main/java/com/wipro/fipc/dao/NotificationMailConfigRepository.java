package com.wipro.fipc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.notification.NotificationMailConfigEntity;

@Repository
public interface NotificationMailConfigRepository extends JpaRepository<NotificationMailConfigEntity ,Long> {

	@Query(value = "select * from common.NOTIFICATION_MAIL_CONFIG   where process_job_mapping_id =?1 and attachment_name like %?2%", nativeQuery = true)
	List<NotificationMailConfigEntity> editfileName(Long pjmid, String attachmentName);

	@Transactional
	@Modifying
	@Query(value = "UPDATE common.NOTIFICATION_MAIL_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2 and id=?3 ", nativeQuery = true)
	int notificationMaildelete(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long processJobMappingId, @Param("id") long id);
}
