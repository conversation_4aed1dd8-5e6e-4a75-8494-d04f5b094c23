package com.wipro.fipc.dao;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.wipro.fipc.entity.maestro.TicketCreationConfig;

@Repository
public interface MaestroTicketConfigRepo
		extends CrudRepository<TicketCreationConfig, Serializable>, JpaSpecificationExecutor<TicketCreationConfig> {
	@Query("SELECT p FROM TicketCreationConfig p")
	public List<TicketCreationConfig> list();
}
