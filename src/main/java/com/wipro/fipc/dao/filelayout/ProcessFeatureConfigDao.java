package com.wipro.fipc.dao.filelayout;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import com.wipro.fipc.entity.filelayout.ProcessFeatureConfig;
import com.wipro.fipc.pojo.CustomProcessFeaturesBO;

public interface ProcessFeatureConfigDao extends JpaRepository<ProcessFeatureConfig, Long> {
	@Query("SELECT p FROM ProcessFeatureConfig p" + " WHERE p.createdBy IN (:createdBy) and activeFlag='T'")
	List<ProcessFeatureConfig> findcreatedBy(@Param("createdBy") List<String> createdBy);

	@Query("SELECT p FROM ProcessFeatureConfig p" + " WHERE p.createdBy =  :createdByStr and activeFlag='T'")
	List<ProcessFeatureConfig> findcreatedByStr(@Param("createdByStr") String createdByStr);

	@Transactional
	@Modifying
	@Query(value = "UPDATE common.PROCESS_FEATURE_CONFIG u set active_flag ='F',CONFIG_STATUS = 'Deleted',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying(flushAutomatically = true)

	@Query("UPDATE ProcessFeatureConfig p SET p.configStatus =?2 , p.updatedBy =?3 , p.updatedDate=current_timestamp  where p.processJobMapping.id =?1 and activeFlag ='T' ")
	void modifyConfigStatus(Long id, String configStatus, String updatedBy);

	@Modifying
	@Query(value = "UPDATE common.PROCESS_FEATURE_CONFIG u set updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where process_job_mapping_id = ?2 and active_flag ='T' ", nativeQuery = true)
	int commonaddandmodify(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long processJobMappingId);

	@Query("select count(p) from ProcessFeatureConfig p where p.processJobMapping.id =?1 and activeFlag ='T'  ")
	int checkForDuplicates(Long id);

	@Transactional
	@Modifying(flushAutomatically = true)

	@Query("UPDATE ProcessFeatureConfig p SET p.configStatus =?2 , p.approvedBy =?3 , p.approvedDate=current_timestamp  where p.processJobMapping.id =?1 and activeFlag ='T' ")
	void modifyApprovedConfigStatus(Long id, String configStatusApproved, String approvedBy);

	@Query(value = "select CONFIG_STATUS from common.process_feature_config where "
			+ " process_job_mapping_id=?1 and active_flag='T' order by id desc limit 1", nativeQuery = true)
	String getConfigStatus(@Param("processJobMappingId") Long processJobMappingId);

	@Query("select distinct new com.wipro.fipc.pojo.CustomProcessFeaturesBO(b.processJobMapping.id,b.jobName,b.eftSubject) from ProcessFeatureConfig b  where b.ksdName=?1  and b.activeFlag = 'T'")
	List<CustomProcessFeaturesBO> getprocessfeatureConfig(String ksdName);

	@Query("SELECT p.clientCode FROM ProcessFeatureConfig p where p.processJobMapping.id =?1")
	List<String> getClientCode(@Param("processJobMappingId") Long processJobMappingId);

}
