package com.wipro.fipc.dao.filelayout;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.filelayout.DatabaseConfig;

public interface DatabaseConfigDao extends BaseDao<DatabaseConfig> {

	@Query(value = "select d.id,d.database_id,d.client_code,CONCAT(d.client_name, '(', d.client_code, ')') as client_name FROM common.database_config d left join common.client_details c on d.client_code = c.client_code left join common.PROCESS_JOB_MAPPING p on c.id = p.client_id WHERE p.id = ?1", nativeQuery = true)
	List<DatabaseConfig> getDatabaseConfig(@Param("pjmid") Long pjmid);

}
