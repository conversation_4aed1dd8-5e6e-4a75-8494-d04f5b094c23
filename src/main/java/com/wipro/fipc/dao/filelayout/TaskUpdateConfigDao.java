package com.wipro.fipc.dao.filelayout;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.filelayout.TaskUpdateConfig;

public interface TaskUpdateConfigDao extends BaseDao<TaskUpdateConfig> {

	@Query(value = "select * from MAESTRO.TASK_UPDATE_CONFIG   where  process_job_mapping_id =?1 and" + "("
			+ "unsecured_attachment like %?2% or ATTACHMENT like %?2%" + ")", nativeQuery = true)
	List<TaskUpdateConfig> editfileName(Long pjmid, String attachment);

	@Transactional
	@Modifying
	@Query(value = "UPDATE MAESTRO.TASK_UPDATE_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying(flushAutomatically = true)
	@Query("UPDATE TaskUpdateConfig p SET p.maestroTaskName =:maestroTaskName, p.updatedBy =:updatedBy,updated_date = CURRENT_TIMESTAMP  WHERE p.processJobMapping.id =:processJobMappingId and p.activeFlag='T'")
	int updateTaskUpdateName(Long processJobMappingId, String maestroTaskName, String updatedBy);

}
