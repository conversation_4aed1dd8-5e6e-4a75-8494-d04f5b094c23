package com.wipro.fipc.dao.filelayout;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.filelayout.RulesConfig;

public interface RulesConfigDao extends BaseDao<RulesConfig> {

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where u.id IN(select rd.rule_id from layout_rule.RULES_DEFINITION rd where rd.var_operation_json like %?2% or rd.json like %?2% or rd.file_name like %?2%) and process_job_mapping_id = ?3", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by, @Param("file_name") String file_Name,
			@Param("process_job_mapping_id") long process_job_mapping_id);
	
	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.RULES_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where u.id =?2", nativeQuery = true)
	int commondeleteID(@Param("updated_by") String updated_by, @Param("id") long id);
}
