package com.wipro.fipc.dao.filelayout;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.filelayout.ReportDataCleanse;

public interface ReportDataCleanseDao extends BaseDao<ReportDataCleanse> {

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.REPORT_DATA_CLEANSE u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2 and active_flag='T'", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updatedBy, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE layout_rule.REPORT_DATA_CLEANSE u set active_flag ='F',updated_by= ?3,updated_date = CURRENT_TIMESTAMP where u.file_name = ?1 and u.process_job_mapping_id = ?2", nativeQuery = true)
	int commondeleteksdoutput(@Param("file_name") String file_Name,
			@Param("process_job_mapping_id") long processJobMappingId, @Param("updated_by") String updatedBy);

}
