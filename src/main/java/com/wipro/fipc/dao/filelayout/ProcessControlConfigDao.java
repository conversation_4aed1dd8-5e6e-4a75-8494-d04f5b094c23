package com.wipro.fipc.dao.filelayout;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.entity.filelayout.ProcessControlConfig;

public interface ProcessControlConfigDao extends com.wipro.fipc.dao.BaseDao<ProcessControlConfig> {

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by, @Param("process_job_mapping_id") long processJobMappingId);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?1 where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commondelete(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long process_job_mapping_id);

	@Query("select a from ProcessControlConfig a  where a.processJobMapping.id =?1")
	List<ProcessControlConfig> getRequiredDetailsByPControl(Long pjmid);

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where  u.process_job_mapping_id = ?2 ", nativeQuery = true)
	int updateByPControlConfigs(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long processJobMappingId);
	
	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PROCESS_CONTROL_CONFIG u set active_flag ='F',updated_by= ?1,updated_date = CURRENT_TIMESTAMP where  u.id = ?2 ", nativeQuery = true)
	int updateById(@Param("updated_by") String updated_by,
			@Param("id") long id);
}