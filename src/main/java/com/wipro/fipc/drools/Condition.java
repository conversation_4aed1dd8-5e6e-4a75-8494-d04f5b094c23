package com.wipro.fipc.drools;

import java.util.HashMap;
import java.util.Map;

public class Condition {

	private String field;
	private Object value;
	private Condition.Operator operator;
	private String appender;

	public String getField() {
		return field;
	}

	public void setField(String field) {
		this.field = field;
	}

	public Object getValue() {
		return value;
	}

	public String getAppender() {
		return appender;
	}

	public void setAppender(String appender) {
		this.appender = appender;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public Condition.Operator getOperator() {
		return operator;
	}

	public void setOperator(Condition.Operator operator) {
		this.operator = operator;
	}

	public enum Operator {
		NOT_EQUAL_TO("NOT_EQUAL_TO"), EQUAL_TO("EQUAL_TO"), GREATER_THAN("GREATER_THAN"), LESS_THAN("LESS_THAN"),
		GREATER_THAN_OR_EQUAL_TO("GREATER_THAN_OR_EQUAL_TO"), LESS_THAN_OR_EQUAL_TO("LESS_THAN_OR_EQUAL_TO"),
		MATCHES("MATCHES");

		private final String value;
		private static Map<String, Operator> constants = new HashMap<>();

		static {
			for (Condition.Operator c : values()) {
				constants.put(c.value, c);
			}
		}

		private Operator(String value) {
			this.value = value;
		}

		@Override
		public String toString() {
			return this.value;
		}

		public static Condition.Operator fromValue(String value) {
			Condition.Operator constant = constants.get(value);
			if (constant == null) {
				throw new IllegalArgumentException(value);
			} else {
				return constant;
			}
		}
	}

}