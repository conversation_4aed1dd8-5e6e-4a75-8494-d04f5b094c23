package com.wipro.fipc.drools;

import java.util.List;

public class Rule {

	private List<Condition> conditions;

	public List<Condition> getConditions() {
		return conditions;
	}

	public void setConditions(List<Condition> conditions) {
		this.conditions = conditions;
	}

	@Override
	public String toString() {
		StringBuilder statementBuilder = new StringBuilder();
		if (getConditions() != null) {
			ruleCondition(statementBuilder);
		}

		String statement = statementBuilder.toString();

		if (statement != null && statement.length() > 4) {
			return statement.substring(0, statement.length() - 4);
		} else {
			return statement;
		}
	}

	private void ruleCondition(StringBuilder statementBuilder) {
		for (Condition condition : getConditions()) {

			String operator = null;
			if (condition.getOperator() != null) {
				switch (condition.getOperator()) {
				case EQUAL_TO:
					operator = "==";
					break;
				case NOT_EQUAL_TO:
					operator = "!=";
					break;
				case GREATER_THAN:
					operator = ">";
					break;
				case LESS_THAN:
					operator = "<";
					break;
				case GREATER_THAN_OR_EQUAL_TO:
					operator = ">=";
					break;
				case LESS_THAN_OR_EQUAL_TO:
					operator = "<=";
					break;
				case MATCHES:
					operator = ".equalsIgnoreCase()";
					break;
				default:
					break;
				}
			}

			statementBuilder.append(condition.getField()).append(" ");
			if (operator != null) {
				statementBuilder.append(operator).append(" ");
			}

			if (condition.getValue() != null) {
				conditionValue(statementBuilder, condition);
			}
			if (condition.getAppender().equals("AND")) {
				statementBuilder.append(" && ");
			} else {
				statementBuilder.append(" || ");
			}

		}
	}

	private void conditionValue(StringBuilder statementBuilder, Condition condition) {
		if (condition.getValue() instanceof String && !condition.getValue().equals("null")) {
			statementBuilder.append("'").append(condition.getValue()).append("'");
		} else {
			statementBuilder.append(condition.getValue());
		}
	}

}
