package com.wipro.fipc.SourceTBAMatchsupport;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.filelayout.TbaMatchConfigDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaMatchConfig;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class TbaMatchConfigContService {

	@Autowired
	GenericDao<TbaMatchConfig> genericDao;

	@Autowired
	TbaMatchConfigDao tbaMatchConfigDao;

	@Autowired
	private BaseDao<TbaMatchConfig> dao;

	protected static final String TBA_MATCH_CONFIG = "TBA_MATCH_CONFIG";
	protected static final String TBA_SCHEMA = "tba";
	public List<TbaMatchConfig> findByColumn(String columnName, String columnValue) {

		List<TbaMatchConfig> tbaMatchConfig = genericDao.findByColumn(TbaMatchConfig.class, TBA_SCHEMA,
				TBA_MATCH_CONFIG, columnName, columnValue);
		List<TbaMatchConfig> tbaMatchConfiglist = new ArrayList<TbaMatchConfig>();
		String mfFieldName = null;
		for (TbaMatchConfig tbamatch : tbaMatchConfig) {
			mfFieldName = tbamatch.getMfFieldName();
			if (mfFieldName.contains("Filler") || mfFieldName.contains("FILLER")) {
				tbamatch.setMfFieldName("");
				;
				tbaMatchConfiglist.add(tbamatch);
			} else {
				tbaMatchConfiglist.add(tbamatch);
			}
		}
		return tbaMatchConfiglist;
	}


	public List<CommonRowBO> deletemultiplerows(@PathVariable int id, @RequestBody List<TbaMatchConfig> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(TbaMatchConfig.class, TBA_SCHEMA, TBA_MATCH_CONFIG, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}


	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaMatchConfig> ehicList) {

		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		int size = ehicList.size();
		List<CommonResRowBO> returnObj = new ArrayList<>();
		for (TbaMatchConfig item : ehicList) {

			if (item.getActiveFlag() == 'F') {
				TbaMatchConfig saveObj = dao.save(item);
				returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));
				continue;
			}

			Long count = tbaMatchConfigDao.checkForDuplicates(item.getProcessJobMapping().getId(), item.getFileName(),
					item.getReportIdentifier(), item.getMfFieldName(), item.getMfFieldWoutSpace(),
					item.getInquiryDefName(), item.getTbaFieldName(), item.getRuleName(), item.getCorrectiveAction(),
					item.getResultField(), item.getIdentifier(), item.getActions(), item.getMatchType(),
					item.getFileNameDest(), item.getReportIdentifierDest(), item.getMfFieldNameDest(),
					item.getMfFieldWoutSpaceDest(), item.getSheetName(), item.getSheetNameWoutSpace(),
					item.getSheetNameDest(), item.getSheetNameDestWoutSpace(), item.getFileNameWoutSpace(),
					item.getFileNameDestWoutSpace(), item.getPptVerifyTba());

			if (count.intValue() == 0) {
				TbaMatchConfig saveObj = dao.save(item);
				returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));
			} else {
				returnObj.add(new CommonResRowBO(item, messageFailure, statusFailure));
			}
		}

		return returnObj;
	}

}