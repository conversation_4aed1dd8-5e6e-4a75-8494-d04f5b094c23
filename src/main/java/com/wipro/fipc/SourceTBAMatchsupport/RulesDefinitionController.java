package com.wipro.fipc.SourceTBAMatchsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.filelayout.RulesDefinitionDao;

@Service
public class RulesDefinitionController {

	@Autowired
	private RulesDefinitionDao rulesDefinitionDao;
	protected static final String RULES_DEF = "RULES_DEFINITION";
	protected static final String LAYOUT_SCHEMA = "layout_rule";
	public static final String GETDETAILSBY = "get Required Details By";
	public static final String FINDINGBYCOLUMN = "Find by Column";


	public List<String> getRequiredDetailsBy(@PathVariable("pjmid") Long pjmid,
			@PathVariable("filename") String filename) {
		LoggerUtil.log(this.getClass(), Level.INFO, GETDETAILSBY,
				"Getting RulesDefinition value of filename: " + filename);
		LoggerUtil.log(this.getClass(), Level.INFO, GETDETAILSBY, "Getting RulesDefinition value of pjmid: " + pjmid);

		String filenames = filename;
		String remove1 = "PLUS";
		String remove2 = "PRCNT";
		String remove3 = "AMPNT";
		String remove4 = "HASH";
		if (filenames.contains(remove1))
			filenames = filenames.replaceAll("PLUS", "+");
		LoggerUtil.log(this.getClass(), Level.INFO, GETDETAILSBY, "After Replace of PLUS filenames: " + filenames);

		if (filenames.contains(remove2))
			filenames = filenames.replaceAll("PRCNT", "%");
		LoggerUtil.log(this.getClass(), Level.INFO, GETDETAILSBY, "After Replace of PRCNT filenames: " + filenames);

		if (filenames.contains(remove3))
			filenames = filenames.replaceAll("AMPNT", "&");
		LoggerUtil.log(this.getClass(), Level.INFO, GETDETAILSBY, "After Replace of AMPNT filenames: " + filenames);

		if (filenames.contains(remove4))
			filenames = filenames.replaceAll("HASH", "#");
		LoggerUtil.log(this.getClass(), Level.INFO, GETDETAILSBY, "After Replace of HASH filenames: " + filenames);

		return rulesDefinitionDao.getRequiredDetailsBy(pjmid, filenames);

	}
}
