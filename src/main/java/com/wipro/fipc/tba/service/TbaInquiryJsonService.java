package com.wipro.fipc.tba.service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Stack;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.config.model.ParClass;
import com.wipro.fipc.config.model.ParClassMapping;
import com.wipro.fipc.dao.tba.TbaInquiryJsonKeyDao;
import com.wipro.fipc.entity.tba.TbaInquiryJsonKey;
import com.wipro.fipc.utils.XMLUtil;

/**
 * <AUTHOR>
 *
 */
@Service
public class TbaInquiryJsonService {

	@Autowired	
	private ParClassMapping parClassMapping;

	@Autowired	
	private TbaInquiryJsonKeyDao tbaInquiryJsonKeyDao;

	private Stack<String> json_key_path;
	private List<ParClass> parClasses;

	public TbaInquiryJsonService() {
		json_key_path = new Stack<String>();
	}

	@PostConstruct
	private void init() {
		parClasses = parClassMapping.getParClass();
	}

	private List<Element> extractPanelInformation(InputStream inputStream, String parName) {
		List<Element> panelFields = null;
		try {
			DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
			Document doc = dBuilder.parse(inputStream);
			doc.getDocumentElement().normalize();

			Element panelElements = doc.getDocumentElement();
			List<Element> panels = XMLUtil.getChildElements(panelElements, "panel");
			if (!CollectionUtils.isEmpty(panels)) {
				for (Element panel : panels) {
					Node nameNode = panel.getElementsByTagName("name").item(0);
					String currentParName = XMLUtil.getFirstLevelTextContent(nameNode);
					if (parName.equalsIgnoreCase(currentParName)) {
						panelFields = XMLUtil.getChildElements(panel, "panelFields");
						LoggerUtil.log(this.getClass(), Level.INFO, "extractPanelInformation", "panelFields Fetched - " + panelFields.size());
						break;
					}
				}
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "extractPanelInformation", "Exception Occurred While Parsing stv-panel.xml File - " + e.getMessage());
		}

		return panelFields;
	}

	public void saveDataForFilterFlag(InputStream inputStream, String parName, String createdBy) {
		LoggerUtil.log(this.getClass(), Level.INFO, "parseSTVPanel", "parseSTVPanel Method Execution Started, parName - " + parName);
		try {
			Set<TbaInquiryJsonKey> tbaInquiryJsonKeys = new HashSet<>();

			List<Element> panelFields = extractPanelInformation(inputStream, parName);
			if (!CollectionUtils.isEmpty(panelFields)) {
				for (Element panelField : panelFields) {
					String dependsOn = StringUtils.hasText(panelField.getAttribute("dependsOn")) ? panelField.getAttribute("dependsOn") : "";
					List<Element> fields = XMLUtil.getChildElements(panelField, "field");
					processField(fields, tbaInquiryJsonKeys, parName, createdBy, "", dependsOn);
				}
			}

			if (!CollectionUtils.isEmpty(tbaInquiryJsonKeys)) {
				tbaInquiryJsonKeys.removeIf(tbaInquiryJsonKey -> tbaInquiryJsonKey.getSubJsonKey().endsWith(("Ar")));
				tbaInquiryJsonKeys.removeIf(tbaInquiryJsonKey -> tbaInquiryJsonKey.getSubJsonKey().endsWith(("Ct")));

				tbaInquiryJsonKeyDao.saveAll(tbaInquiryJsonKeys);
				LoggerUtil.log(this.getClass(), Level.INFO, "parseSTVPanel", "Data Saved Successfully, Record Count - " + tbaInquiryJsonKeys.size());
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, "parseSTVPanel", "tbaInquiryJsonKeys is Empty");
			}
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "parseSTVPanel", "Exception Occurred in parseSTVPanel" + e.getMessage());
		}
	}

	private void processField(List<Element> fields, Set<TbaInquiryJsonKey> tbaInquiryJsonKeys, String parName,
			String createdBy, String suffix, String dependsOn) throws Exception {

		TbaInquiryJsonKey tbaInquiryJsonKey = null;
		for (Element field : fields) {
			if(field.hasAttribute("required") && "false".equals(field.getAttribute("required")))
				continue;

			tbaInquiryJsonKey = new TbaInquiryJsonKey();
			tbaInquiryJsonKey.setJsonKey(json_key_path.stream().collect(Collectors.joining(".")));
			tbaInquiryJsonKey.setSubJsonKey(field.getAttribute("name"));
			tbaInquiryJsonKey.setParNM(parName);
			tbaInquiryJsonKey.setFieldType(field.getAttribute("type"));
			if (StringUtils.hasText(suffix)) {
				tbaInquiryJsonKey.setTbaFieldName(field.getAttribute("displName") + " - " + suffix);
			} else {
				tbaInquiryJsonKey.setTbaFieldName(field.getAttribute("displName"));
			}
			tbaInquiryJsonKey.setCreatedDate(new Date());
			tbaInquiryJsonKey.setCreatedBy(createdBy);
			tbaInquiryJsonKey.setFilterFlag('T');
			tbaInquiryJsonKey.setDependsOn(dependsOn);
			tbaInquiryJsonKeys.add(tbaInquiryJsonKey);
			Element innerFieldtag = XMLUtil.getChildElement(field, "innerField");
			List<Element> innerFields = XMLUtil.getChildElements(innerFieldtag, "field");
			if (innerFields.size() > 0) {
				json_key_path.push(field.getAttribute("name"));
				processField(innerFields, tbaInquiryJsonKeys, parName, createdBy, field.getAttribute("displName"), dependsOn);
				json_key_path.pop();
			}
		}
	}

	public Map<String, String> fetchArrayAttributes(InputStream inputStream, String parName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "fetchArrayAttributes", "TbaInquiryJsonKeyService", "fetchArrayAttributes Method Started");

		ParClass parClass = parClasses.stream()
			.filter(clazz -> parName.equals(clazz.getId()))
			.findFirst().orElse(null);

		if(parClass == null) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "fetchArrayAttributes", "parClass is Null");
			return null;
		}

		if(!CollectionUtils.isEmpty(parClass.getArrayAttributes()))
			return parClass.getArrayAttributes();

		Set<String> includeClassIds = parClass.getIncludeClasses();
		if(CollectionUtils.isEmpty(includeClassIds)) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "fetchArrayAttributes", "includeClassIds is Empty");
			return null;
		}

		List<Element> panelFields = extractPanelInformation(inputStream, parName);
		if(CollectionUtils.isEmpty(panelFields)) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "fetchArrayAttributes", "panelFields is Empty");
			return null;
		}

		List<Element> fields = new ArrayList<>();
		panelFields.stream()
			.filter(panelField -> includeClassIds.contains(panelField.getAttribute("classId")))
			.forEach(panelField -> {
				try {
					fields.addAll(XMLUtil.getChildElements(panelField, "field"));
				} catch (Exception e) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "fetchArrayAttributes", "Exception Occurred While Calling getChildElements - " + e.getMessage());
				}
			});

		Map<String, String> panelDatas = new HashMap<>();
		try {
			processField(fields, panelDatas);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "fetchArrayAttributes", "Exception Occurred While Calling processField - " + e.getMessage());
		}

		return panelDatas;
	}

	private void processField(List<Element> fields, Map<String, String> panelDatas) throws Exception {
		for (Element field : fields) {
			String type = field.getAttribute("type");
			if("array".equalsIgnoreCase(type))
				panelDatas.put(field.getAttribute("name").trim(), field.getAttribute("displName").trim());

			Element innerFieldtag = XMLUtil.getChildElement(field, "innerField");
			List<Element> innerFields = XMLUtil.getChildElements(innerFieldtag, "field");
			if (!CollectionUtils.isEmpty(innerFields)) {
				processField(innerFields, panelDatas);
			}
		}
	}
}
