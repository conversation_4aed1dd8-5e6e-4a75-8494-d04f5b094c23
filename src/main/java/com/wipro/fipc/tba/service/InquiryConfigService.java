package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.TbaMetaDataDao;
import com.wipro.fipc.dao.tba.TbaProcessInquiryDao;
import com.wipro.fipc.entity.tba.TbaInquiryConfig;
import com.wipro.fipc.entity.tba.TbaInquiryJsonKey;
import com.wipro.fipc.entity.tba.TbaMetaData;
import com.wipro.fipc.entity.tba.TbaProcessInquiry;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class InquiryConfigService {

	@Autowired
	TbaMetaDataDao tbaMetadataDao;

	@Autowired
	TbaProcessInquiryDao tbaProcessInquiryDao;

	@Autowired
	GenericDao<TbaInquiryConfig> genericDao;
	@Autowired
	private BaseDao<TbaInquiryConfig> dao;

	@Autowired
	protected GenericDao<TbaInquiryJsonKey> tbaInquiryJsonDao;

	protected static final String TBA_INQUIRY_CONFIG = "tba_inquiry_config";
	protected static final String SCHEMA = "tba";

	protected static final String TBA_INQUIRY_JSON_KEY = "tba_inquiry_json_key";

	public List<TbaMetaData> getTbaMetaData(int panel_id, int client_id) {

		return tbaMetadataDao.getTbaMetaData(panel_id, client_id);
	}

	public List<CommonRowBO> deletemultiplerows(int id, List<TbaInquiryConfig> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(TbaInquiryConfig.class, SCHEMA, TBA_INQUIRY_CONFIG, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	public List<TbaInquiryJsonKey> findRecordByColumn(String columnName, String columnValue) {

		return tbaInquiryJsonDao.findRecordByColumn(TbaInquiryJsonKey.class, SCHEMA, TBA_INQUIRY_JSON_KEY, columnName,
				columnValue);
	}

	public TbaInquiryConfig findById(long id) {
		return dao.findById(id).orElse(null);
	}

	public List<TbaInquiryConfig> findByColumn(String columnName, String columnValue) {

		return genericDao.findByColumn(TbaInquiryConfig.class, SCHEMA, TBA_INQUIRY_CONFIG, columnName, columnValue);
	}

	public List<TbaInquiryConfig> list() {
		return (List<TbaInquiryConfig>) dao.findAll();
	}

	public List<TbaProcessInquiry> getInquiryNames(int clientId) {

		List<TbaProcessInquiry> processInquiries = tbaProcessInquiryDao.getInquiryNames(clientId);
		LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryNames", "TbaProcessInquiry    {}: " + processInquiries);

		return processInquiries;
	}
}
