package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaInquiryMetaDataDao;
import com.wipro.fipc.dao.tba.TbaUpdateMetadataDao;
import com.wipro.fipc.dao.tba.TbaUpdateMetadataMasterDao;
import com.wipro.fipc.entity.tba.TbaUpdateMetaDataMaster;
import com.wipro.fipc.model.PanelDataBean;

@Service
public class TbaUpdateMetaDataService {

	@Autowired
	private TbaUpdateMetadataDao tbaUpdateMetadataDao;

	@Autowired
	private TbaUpdateMetadataMasterDao tbaUpdateMetadataMaster;

	@Autowired
	private TbaInquiryMetaDataDao tbaInquiryMetaDataDao;

	public List<PanelDataBean> getUpdateMetaDataPanelWise(int clientId, int panelId, Map<String, String> arrayAttributes) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getUpdateMetaDataPanelWise", "Service Method Started");

		List<PanelDataBean> panelDatas = new ArrayList<>();

		arrayAttributes.forEach((displayCode, displayName) -> {
			switch (displayCode) {
			case "subMetaData":
				List<com.wipro.fipc.entity.tba.TbaUpdateMetaData> updateMetaDatas = tbaUpdateMetadataDao.getTbaUpdateMetaData(panelId, clientId);
				if(!CollectionUtils.isEmpty(updateMetaDatas)) {
					List<Map<String, String>> updateMetaDataMap = new ArrayList<>();
					updateMetaDatas.forEach(updateMetaData -> {
						Map<String, String> dataMap = new HashMap<>();
						dataMap.put("attributeId", updateMetaData.getTransId());
						dataMap.put("attributeText", updateMetaData.getMetaData());
						updateMetaDataMap.add(dataMap);
					});

					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataMap));
				} else
					LoggerUtil.log(this.getClass(), Level.INFO, "getUpdateMetaDataPanelWise", "updateMetaData Received Empty");
				break;

			case "pyscAr":
				Optional<List<com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster>> inquiryMetaDataOptional = tbaInquiryMetaDataDao.findByClientIdAndPanelIdAndCreatedBy(clientId, 5388, HolmesAppConstants.CREATOR_NAME);
				if(inquiryMetaDataOptional.isPresent() && !CollectionUtils.isEmpty(inquiryMetaDataOptional.get())) {
					List<Map<String, String>> inquiryMetaDataMap = new ArrayList<>();
					inquiryMetaDataOptional.get().stream()
					.filter(inquiryMetaData -> displayCode.trim().equals(inquiryMetaData.getMetadataTag().trim()))
					.forEach(inquiryMetaData -> {
						Map<String, String> dataMap = new HashMap<>();
						dataMap.put("attributeId", inquiryMetaData.getAttributeId());
						dataMap.put("attributeText", inquiryMetaData.getAttributeText());
						inquiryMetaDataMap.add(dataMap);
					});

					panelDatas.add(new PanelDataBean(displayCode, displayName, null, inquiryMetaDataMap));
				} else
					LoggerUtil.log(this.getClass(), Level.INFO, "getUpdateMetaDataPanelWise", "inquiryMetaData Received Empty");
				break;

			case "catAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMaster= new ArrayList<>();
				if (CollectionUtils.isEmpty(updateMetaDataMaster))
					updateMetaDataMaster.addAll(tbaUpdateMetadataMaster.findByClientId(clientId));

				if (!CollectionUtils.isEmpty(updateMetaDataMaster)) {
					List<Map<String, String>> updateMetaDataMasterCategory = new ArrayList<>();

					updateMetaDataMaster.stream()
					.filter(updateData -> displayCode.trim().equals(updateData.getMetadataTag().trim()))
					.collect(Collectors.toMap(updateData -> updateData.getCategoryId().trim(),
							updateData -> updateData.getCategoryDescription().trim(), (e1, e2) -> e1))
					.forEach((id, desc) -> {
						Map<String, String> panelData = new HashMap<>();
						panelData.put("attributeId", id);
						panelData.put("attributeText", desc.concat("("+id+")"));
						updateMetaDataMasterCategory.add(panelData);
					});
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataMasterCategory));

				}
				break;
			case "statusAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMaster1= new ArrayList<>();
				if (CollectionUtils.isEmpty(updateMetaDataMaster1))
					updateMetaDataMaster1.addAll(tbaUpdateMetadataMaster.findByClientId(clientId));

				if (!CollectionUtils.isEmpty(updateMetaDataMaster1)) {
					List<Map<String, String>> updateMetaDataStatus = new ArrayList<>();
					updateMetaDataMaster1.stream().forEach(statusData -> {
						Map<String, String> panelData = new HashMap<>();
						panelData.put("attributeId", statusData.getStatusCode());
						panelData.put("attributeText", statusData.getStatusDescription());
						panelData.put("CategoryId", statusData.getCategoryId());

						updateMetaDataStatus.add(panelData);
					});
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataStatus));
				}
				break;
			case "plansbalAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMaster2= new ArrayList<>();
				if (CollectionUtils.isEmpty(updateMetaDataMaster2))
					updateMetaDataMaster2.addAll(tbaUpdateMetadataMaster.findByClientIdAndPanelId(clientId, panelId));

				if (!CollectionUtils.isEmpty(updateMetaDataMaster2)) {
					List<Map<String, String>> updateMetaDataStatus = new ArrayList<>();
					updateMetaDataMaster2.stream().filter(updateMetaData -> displayCode.trim().equals(updateMetaData.getMetadataTag().trim())).forEach(statusData -> {
						Map<String, String> panelData = new HashMap<>();
						panelData.put("attributeId", statusData.getStatusCode());
						panelData.put("attributeText", statusData.getStatusDescription());
						panelData.put("CategoryId", statusData.getTransId());

						updateMetaDataStatus.add(panelData);
					});
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataStatus));
				}
				break;
			case "plansrcAr_planfundAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMasterPlanList= new ArrayList<>();
				if (CollectionUtils.isEmpty(updateMetaDataMasterPlanList))
					updateMetaDataMasterPlanList.addAll(tbaUpdateMetadataMaster.findByClientId(clientId));

				if (!CollectionUtils.isEmpty(updateMetaDataMasterPlanList)) {
					List<Map<String, String>> updateMetaDataMasterCategory = new ArrayList<>();

					updateMetaDataMasterPlanList.stream()
							.filter(updateData -> displayCode.trim().equals(updateData.getMetadataTag().trim()))

							.forEach((id) -> {
								Map<String, String> panelData = new HashMap<>();
								panelData.put("attributeId", id.getCategoryId());
								panelData.put("attributeText", id.getCategoryDescription());
								updateMetaDataMasterCategory.add(panelData);
							});
				
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataMasterCategory));

				}
				break;

			case "subfundstrcAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMasterFundList= new ArrayList<>();
				if (CollectionUtils.isEmpty(updateMetaDataMasterFundList))
					updateMetaDataMasterFundList.addAll(tbaUpdateMetadataMaster.findByClientId(clientId));

				if (!CollectionUtils.isEmpty(updateMetaDataMasterFundList)) {
					List<Map<String, String>> updateMetaDataMasterCategory = new ArrayList<>();

					updateMetaDataMasterFundList.stream()
							.filter(updateData -> displayCode.trim().equals(updateData.getMetadataTag().trim()))
							.forEach((id) -> {
								Map<String, String> panelData = new HashMap<>();
								panelData.put("CategoryId", id.getCategoryId());
								panelData.put("attributeId", id.getStatusCode());
								panelData.put("attributeText", id.getStatusDescription());
								updateMetaDataMasterCategory.add(panelData);
							});
					
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataMasterCategory));

				}
				break;
			case "planacctAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMasterAccountList= new ArrayList<>();
				if (CollectionUtils.isEmpty(updateMetaDataMasterAccountList))
					updateMetaDataMasterAccountList.addAll(tbaUpdateMetadataMaster.findByClientId(clientId));

				if (!CollectionUtils.isEmpty(updateMetaDataMasterAccountList)) {
					List<Map<String, String>> updateMetaDataMasterCategory = new ArrayList<>();

					updateMetaDataMasterAccountList.stream()
							.filter(updateData -> displayCode.trim().equals(updateData.getMetadataTag().trim()))
							.forEach((id) -> {
								Map<String, String> panelData = new HashMap<>();
								panelData.put("CategoryId", id.getCategoryId());
								panelData.put("attributeId", id.getStatusCode());
								panelData.put("attributeText", id.getStatusDescription());
								updateMetaDataMasterCategory.add(panelData);
							});
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataMasterCategory));

				}
				break;
			case "plansoftbalAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMasterSoftbalanceList= new ArrayList<>();
				if (CollectionUtils.isEmpty(updateMetaDataMasterSoftbalanceList))
					updateMetaDataMasterSoftbalanceList.addAll(tbaUpdateMetadataMaster.findByClientId(clientId));

				if (!CollectionUtils.isEmpty(updateMetaDataMasterSoftbalanceList)) {
					List<Map<String, String>> updateMetaDataMasterCategory = new ArrayList<>();

					updateMetaDataMasterSoftbalanceList.stream()
							.filter(updateData -> displayCode.trim().equals(updateData.getMetadataTag().trim()))

							.forEach((id) -> {
								Map<String, String> panelData = new HashMap<>();
								panelData.put("CategoryId", id.getCategoryId());
								panelData.put("attributeId", id.getStatusCode());
								panelData.put("attributeText", id.getStatusDescription());
								updateMetaDataMasterCategory.add(panelData);
							});
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataMasterCategory));

				}
				break;
			

			case "actCdAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMaster3= new ArrayList<>();
				updateMetaDataMaster3.addAll(tbaUpdateMetadataMaster.findByClientIdAndPanelId(clientId, panelId));
				if (!CollectionUtils.isEmpty(updateMetaDataMaster3)) {
					List<Map<String, String>> updateMetaDataStatus = new ArrayList<>();
					updateMetaDataMaster3.stream().filter(updateMetaData -> displayCode.trim().equals(updateMetaData.getMetadataTag().trim())).forEach(statusData -> {
						Map<String, String> panelData = new HashMap<>();
						panelData.put("attributeId", statusData.getStatusCode());
						panelData.put("attributeText", statusData.getStatusDescription());
						panelData.put("CategoryId", statusData.getTransId());

						updateMetaDataStatus.add(panelData);
					});
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataStatus));
				}
				break;
			case "cashInkindAr":
				List<TbaUpdateMetaDataMaster> updateMetaDataMaster4= new ArrayList<>();
				updateMetaDataMaster4.addAll(tbaUpdateMetadataMaster.findByClientIdAndPanelId(clientId, panelId));
				if (!CollectionUtils.isEmpty(updateMetaDataMaster4)) {
					List<Map<String, String>> updateMetaDataStatus = new ArrayList<>();
					updateMetaDataMaster4.stream().filter(updateMetaData -> displayCode.trim().equals(updateMetaData.getMetadataTag().trim())).forEach(statusData -> {
						Map<String, String> panelData = new HashMap<>();
						panelData.put("attributeId", statusData.getStatusCode());
						panelData.put("attributeText", statusData.getStatusDescription());
						panelData.put("CategoryId", statusData.getTransId());

						updateMetaDataStatus.add(panelData);
					});
					panelDatas.add(new PanelDataBean(displayCode, displayName, null, updateMetaDataStatus));
				}

				break;
			default:
				LoggerUtil.log(this.getClass(), Level.INFO, "getUpdateMetaDataPanelWise", "Unknown Type - " + displayCode);
				break;
			}
		});

		return panelDatas;
	}
}
