package com.wipro.fipc.tba.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.tba.TbaRerunEventMaster;
import com.wipro.fipc.entity.tba.TbaUpdateConfig;
@Service
public class TbarerunService {

	protected static final String TBA_RERUN_EVENT_MASTER = "TBA_RERUN_EVENT_MASTER";
	protected static final String SCHEMA = "tba";
	 protected static final String TBA_UPDATE_CONFIG = "TBA_UPDATE_CONFIG";
	
	@Autowired
	GenericDao<TbaRerunEventMaster> genericDao;
	
	@Autowired
    GenericDao<TbaUpdateConfig> tbaUpdateConfigDao;
	
	public List<TbaRerunEventMaster> findRecordByColumn( String columnName,String columnValue) {
		
		
		return genericDao.findRecordByColumn(TbaRerunEventMaster.class,SCHEMA,TBA_RERUN_EVENT_MASTER,columnName, columnValue);
	}
	public List<TbaRerunEventMaster> findByColumn(String columnName, String columnValue) {
		

		return genericDao.findByColumn(TbaRerunEventMaster.class, SCHEMA, TBA_RERUN_EVENT_MASTER, columnName,
				columnValue);
	}
	public List<TbaUpdateConfig> findByColumnByKSD(String columnName, String columnValue) {
        



       return tbaUpdateConfigDao.findByColumn(TbaUpdateConfig.class, SCHEMA, TBA_UPDATE_CONFIG, columnName,
                columnValue);
    }
}