package com.wipro.fipc.tba.service;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.wipro.fipc.common.utils.JsonUtils;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.tba.ProcessJobMappingDao;
import com.wipro.fipc.dao.tba.TbaInquiryConfigDao;
import com.wipro.fipc.dao.tba.TbaInquiryConfigs;
import com.wipro.fipc.dao.tba.TbaInquiryJsonKeyDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaInquiryConfig;
import com.wipro.fipc.pojo.tba.TbaInquiryConfigDto;

@Service
public class TbaInquiryConfigServicess implements TbaInquiryConfigs {
	@Autowired
	@Qualifier("tbaInquiryConfigDao")
	TbaInquiryConfigDao tbaInquiryConfigDao;

	@Autowired
	@Qualifier("tbaInquiryJsonKeyDao")
	TbaInquiryJsonKeyDao tbaInquiryJsonKeyDao;

	@Autowired
	@Qualifier("processJobMappingDao")
	ProcessJobMappingDao processJobMappingDao;

	@Autowired
	private Gson gson;

	@Override
	public List<TbaInquiryConfig> saveInquiryConfig(List<TbaInquiryConfigDto> entity) throws JsonProcessingException {
		List<String> jsonDataList = new ArrayList<>();
		List<TbaInquiryConfig> configs = new ArrayList<>();
		ProcessJobMapping processJobMapping = null;
		for (TbaInquiryConfigDto configDto : entity) {

			TbaInquiryConfig inquiryConfig = new TbaInquiryConfig();

			if (configDto.getId() != null) {
				inquiryConfig.setId(configDto.getId());
				inquiryConfig.setUpdatedDate(new Date());
				inquiryConfig.setActiveFlag(configDto.getActiveFlag());
				inquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				inquiryConfig.setCreatedDate(configDto.getCreatedDate());
				inquiryConfig.setCreatedBy(configDto.getCreatedBy());
				processJobMapping = configDto.getProcessJobMapping();
			} else {
				inquiryConfig.setCreatedDate(new Date());
				inquiryConfig.setCreatedBy(configDto.getCreatedBy());
				inquiryConfig.setActiveFlag("T");
				processJobMapping = processJobMappingDao.finaByProcessJobMappingId(configDto.getProcessJobMappingId());
			}
			inquiryConfig.setPanelId(configDto.getPanelId());
			String conditionJson=gson.toJson(configDto.getConditionJson());
			Character filterFalg='F';
//			if(conditionJson.length()>2) {
//				filterFalg='T';
//			}
			String jsonKeyData = tbaInquiryJsonKeyDao.findByJsonKeyNameAndParName(configDto.getParNM(),
					configDto.getTbaFieldName(), filterFalg);
			if (jsonKeyData != null) {
				jsonDataList = Arrays.asList(jsonKeyData.split(","));
			}
			inquiryConfig.setProcessJobMapping(processJobMapping);
			inquiryConfig.setTbaFieldName(configDto.getTbaFieldName());
			inquiryConfig.setInquiryName(configDto.getInquiryName());
			if (configDto.getMetaData() != null && !configDto.getMetaData().equals("")) {
				String metaData = null;
				String metaDataJson = new ObjectMapper().writeValueAsString(configDto.getMetaData());
				if(JsonUtils.isValidJson(metaDataJson))
					metaData = metaDataJson;
				else
					metaData = String.valueOf(configDto.getMetaData());

				inquiryConfig.setMetaData(metaData);
			} else {
				inquiryConfig.setMetaData("");
			}
			if (!jsonDataList.isEmpty()) {
				inquiryConfig.setJsonKey(jsonDataList.get(0));
				if (jsonDataList.size() > 1) {
					inquiryConfig.setSubJsonKey(jsonDataList.get(1));
				} else {
					inquiryConfig.setSubJsonKey("");
				}
				inquiryConfig.setFieldType(jsonDataList.get(2));
			} else {
				inquiryConfig.setJsonKey("");
				inquiryConfig.setFieldType("");
				inquiryConfig.setSubJsonKey("");
			}

			inquiryConfig.setParNM(configDto.getParNM());
			inquiryConfig.setIdentifier(configDto.getIdentifier());
			inquiryConfig.setInquiryDefName(configDto.getInquiryDefName());
			inquiryConfig.setRecordIdentifier(configDto.getRecordIdentifier());
			inquiryConfig.setColumnMatrix(configDto.getColumnMatrix());
			inquiryConfig.setEffDateType(configDto.getEffDateType());
			inquiryConfig.setEffFromDate(gson.toJson(configDto.getEffFromDate()));
			inquiryConfig.setEffToDate(gson.toJson(configDto.getEffToDate()));
			inquiryConfig.setRowMatrix(configDto.getRowMatrix());
			inquiryConfig.setSequence(configDto.getSequence());
			inquiryConfig.setFlag(configDto.getFlag());
			inquiryConfig.setConditionJson(conditionJson);
			TbaInquiryConfig inquiryConfig1 = tbaInquiryConfigDao.save(inquiryConfig);

			configs.add(inquiryConfig1);
		}
		return configs;
	}

	@Override
	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaInquiryConfigDto> entity)
			throws IllegalAccessException, InvocationTargetException, JsonParseException, JsonMappingException, IOException {
		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		List<CommonResRowBO> commonRowBOs = new ArrayList<>();
		List<String> jsonDataList = new ArrayList<>();
		ProcessJobMapping processJobMapping = null;
		for (TbaInquiryConfigDto configDto : entity) {

			TbaInquiryConfig inquiryConfig = new TbaInquiryConfig();

			if (configDto.getId() != null) {
				inquiryConfig.setId(configDto.getId());
				inquiryConfig.setUpdatedDate(new Date());
				inquiryConfig.setActiveFlag(configDto.getActiveFlag());
				inquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				inquiryConfig.setCreatedDate(configDto.getCreatedDate());
				inquiryConfig.setCreatedBy(configDto.getCreatedBy());
				processJobMapping = configDto.getProcessJobMapping();
			} else {
				inquiryConfig.setCreatedDate(new Date());
				inquiryConfig.setCreatedBy(configDto.getCreatedBy());
				inquiryConfig.setActiveFlag("T");
				inquiryConfig.setUpdatedDate(new Date());
				inquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				processJobMapping = processJobMappingDao.finaByProcessJobMappingId(configDto.getProcessJobMappingId());
			}
			
			inquiryConfig.setPanelId(configDto.getPanelId());
			Character filterFalg='F';
			String conditionJson=new ObjectMapper().writeValueAsString(configDto.getConditionJson());//gson.toJson(configDto.getConditionJson());
//			if(conditionJson.length()>2) {
//				filterFalg='T';
//			}
			String jsonKeyData = tbaInquiryJsonKeyDao.findByJsonKeyNameAndParName(configDto.getParNM(),
					configDto.getTbaFieldName(), filterFalg);
			if (jsonKeyData != null) {
				jsonDataList = Arrays.asList(jsonKeyData.split(","));
			}
			inquiryConfig.setProcessJobMapping(processJobMapping);
			inquiryConfig.setTbaFieldName(configDto.getTbaFieldName());
			inquiryConfig.setInquiryName(configDto.getInquiryName());
			inquiryConfig.setEstimateMode(configDto.getEstimateMode());
			if (configDto.getMetaData() != null && !configDto.getMetaData().equals("")) {
				String metaData = null;
				String metaDataJson = new ObjectMapper().writeValueAsString(configDto.getMetaData());
				if(JsonUtils.isValidJson(metaDataJson))
					metaData = metaDataJson;
				else
					metaData = String.valueOf(configDto.getMetaData());

				inquiryConfig.setMetaData(metaData);
			} else {
				inquiryConfig.setMetaData("");
			}
			if (!jsonDataList.isEmpty()) {
				inquiryConfig.setJsonKey(jsonDataList.get(0));
				if (jsonDataList.size() > 1) {
					inquiryConfig.setSubJsonKey(jsonDataList.get(1));
				} else {
					inquiryConfig.setSubJsonKey("");
				}
				inquiryConfig.setFieldType(jsonDataList.get(2));
			} else {
				inquiryConfig.setJsonKey("");
				inquiryConfig.setFieldType("");
				inquiryConfig.setSubJsonKey("");
			}

			inquiryConfig.setParNM(configDto.getParNM());
			inquiryConfig.setIdentifier(configDto.getIdentifier());
			inquiryConfig.setInquiryDefName(configDto.getInquiryDefName());
			inquiryConfig.setRecordIdentifier(configDto.getRecordIdentifier());
			inquiryConfig.setColumnMatrix(configDto.getColumnMatrix());
			inquiryConfig.setEffDateType(configDto.getEffDateType());
			inquiryConfig.setEffFromDate(gson.toJson(configDto.getEffFromDate()));
			inquiryConfig.setEffToDate(gson.toJson(configDto.getEffToDate()));
			inquiryConfig.setRowMatrix(configDto.getRowMatrix());
			inquiryConfig.setSequence(configDto.getSequence());
			inquiryConfig.setFlag(configDto.getFlag());
			inquiryConfig.setConditionJson(conditionJson);
			Long forDuplicates = tbaInquiryConfigDao.checkForDuplicates(inquiryConfig.getProcessJobMapping().getId(),
					inquiryConfig.getInquiryName(), inquiryConfig.getPanelId(), inquiryConfig.getTbaFieldName(),
					inquiryConfig.getJsonKey(), inquiryConfig.getSubJsonKey(), inquiryConfig.getMetaData(),
					inquiryConfig.getIdentifier(), inquiryConfig.getFieldType(), inquiryConfig.getParNM(),
					inquiryConfig.getRecordIdentifier(), inquiryConfig.getEffDateType(), inquiryConfig.getEffFromDate(),
					inquiryConfig.getEffToDate(), inquiryConfig.getRowMatrix(), inquiryConfig.getColumnMatrix(),
					inquiryConfig.getSequence(),inquiryConfig.getInquiryDefName(),conditionJson, inquiryConfig.getEstimateMode());
			if (forDuplicates == 0) {

				TbaInquiryConfig inquiryConfig1 = tbaInquiryConfigDao.save(inquiryConfig);
				LoggerUtil.log(getClass(), Level.INFO, "saveIfNotDuplicate", "ID {} \n" + inquiryConfig1.getId());
				ObjectMapper mapper = new ObjectMapper();
				TbaInquiryConfigDto configDto2 = new TbaInquiryConfigDto();

				BeanUtils.copyProperties(configDto2, inquiryConfig1);

				if (inquiryConfig.getEffFromDate() != null) {

					configDto2.setEffFromDate(mapper.readValue(inquiryConfig1.getEffFromDate(), Object.class));
				

				} else 
					inquiryConfig1.setEffFromDate("");
				if (inquiryConfig.getEffToDate() != null) {
					configDto2.setEffToDate(mapper.readValue(inquiryConfig1.getEffToDate(), Object.class));
				} else 
					inquiryConfig1.setEffToDate("");
				configDto2.setConditionJson(mapper.readValue(inquiryConfig1.getConditionJson(), Object.class));
				commonRowBOs.add(new CommonResRowBO(configDto2, messageSuccess, statusSuccess));

			}else 
				commonRowBOs.add(new CommonResRowBO(inquiryConfig, messageFailure, statusFailure));

		}
		return commonRowBOs;
	}

}