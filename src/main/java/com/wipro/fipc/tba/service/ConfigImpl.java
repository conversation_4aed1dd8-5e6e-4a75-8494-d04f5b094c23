package com.wipro.fipc.tba.service;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.JsonUtils;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.ProcessJobMappingDao;
import com.wipro.fipc.dao.tba.TbaConfigDao;
import com.wipro.fipc.dao.tba.TbaUpdateActivityDao;
import com.wipro.fipc.dao.tba.TbaUpdateConfigDao;
import com.wipro.fipc.dao.tba.TbaUpdateJsonKeyDao;
import com.wipro.fipc.dao.tba.TbaUpdateMetadataDao;
import com.wipro.fipc.dao.tba.TbaUpdateProcessDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaUpdateActivity;
import com.wipro.fipc.entity.tba.TbaUpdateConfig;
import com.wipro.fipc.entity.tba.TbaUpdateMetaData;
import com.wipro.fipc.pojo.tba.TbaUpdateConfigDto;
import com.wipro.fipc.utils.ActiveFlag;
import com.wipro.fipc.utils.CustomBeanUtils;
import com.wipro.fipc.utils.TableType;

@Service
public class ConfigImpl implements TbaConfigDao {

	@Autowired
	TbaUpdateConfigDao tbaUpdateConfigDao;

	@Autowired
	TbaUpdateMetadataDao tbaUpdateMetadataDao;

	@Autowired
	TbaUpdateActivityDao tbaUpdateActivityDao;

	@Autowired
	TbaUpdateProcessDao tbaUpdateProcessDao;

	@Autowired
	TbaUpdateJsonKeyDao tbaUpdateJsonKeyDao;

	@Autowired
	@Qualifier("processJobMappingDao")
	ProcessJobMappingDao processJobMappingDao;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	/* Save the UpdateConfig Detals */
/*
 commented this method as it's not in use anymore.
  	@Override
	public List<TbaUpdateConfig> saveUpdateConfig(List<TbaUpdateConfigDto> entity) {
		TbaUpdateMetaData tbaUpdateMetaData = null;
		ProcessJobMapping processJobMapping = null;
		List<TbaUpdateConfig> configs = new ArrayList<>();
		for (TbaUpdateConfigDto configDto : entity) {
			TbaUpdateConfig tbaUpdateConfig = new TbaUpdateConfig();
			if (configDto.getId() != null) {
				tbaUpdateConfig.setId(configDto.getId());
				tbaUpdateConfig.setUpdatedDate(new Date());
				tbaUpdateConfig.setUpdateName(configDto.getUpdateName());
				tbaUpdateConfig.setActiveFlag(configDto.getActiveFlag());
				tbaUpdateConfig.setUpdatedBy(configDto.getUpdatedBy());
				tbaUpdateConfig.setCreatedDate(configDto.getCreatedDate());
				tbaUpdateConfig.setCreatedBy(configDto.getCreatedBy());
				processJobMapping = configDto.getProcessJobMapping();
				tbaUpdateConfig.setRerunFlag(configDto.getRerunFlag());
			} else {
				tbaUpdateConfig.setCreatedDate(new Date());
				tbaUpdateConfig.setCreatedBy(configDto.getCreatedBy());
				tbaUpdateConfig.setUpdateName(configDto.getUpdateName());
				tbaUpdateConfig.setActiveFlag(ActiveFlag.T.activeFlag());
				processJobMapping = processJobMappingDao.finaByProcessJobMappingId(configDto.getProcessJobMappingId());
				tbaUpdateConfig.setRerunFlag(configDto.getRerunFlag());
			}
			List<String> jsonDataList = new ArrayList<>();
			if (configDto.getAddManualFlag() != 'Y') {
				int activityId = tbaUpdateProcessDao.findActivityId(configDto.getEventName(),
						configDto.getClientId());
				// classId from tbaUpdateActivity
				TbaUpdateActivity tbaUpdateActivity = tbaUpdateActivityDao.findByActivityIdAndPanelId(activityId,
						configDto.getPanelId(), configDto.getClientId());
				if (!StringUtils.equalsIgnoreCase("Validate", configDto.getTbaUpdateAction())) {
					// get transId from metadata
					if (configDto.getMetaData() != null) {
						tbaUpdateMetaData = tbaUpdateMetadataDao.findByMetadataAndPanelId(configDto.getPanelId(),
								configDto.getMetaData(), configDto.getClientId());
					}

					if (StringUtils.isNotBlank(configDto.getTbaFieldName())) {
						String data = tbaUpdateJsonKeyDao.findByJsonKeyNameAndPanelId(configDto.getTbaFieldName(),
								configDto.getParNm());
						if (data != null) {
							jsonDataList = Arrays.asList(data.split(","));
						}
					}
				}
				tbaUpdateConfig.setActivityId(activityId);
				if (tbaUpdateActivity != null) {
					tbaUpdateConfig.setClassId(tbaUpdateActivity.getClassId());
				}
			}

			tbaUpdateConfig.setProcessJobMapping(processJobMapping);
			tbaUpdateConfig.setTbaFieldName(configDto.getTbaFieldName());

			if (!jsonDataList.isEmpty()) {
				if (jsonDataList.get(0) != null) {
					tbaUpdateConfig.setBaseKey(jsonDataList.get(0));
				} else {
					tbaUpdateConfig.setBaseKey("");
				}
				if (jsonDataList.size() > 1) {
					tbaUpdateConfig.setSubKey(jsonDataList.get(1));
				} else {
					tbaUpdateConfig.setSubKey("");
				}
				tbaUpdateConfig.setJsonKey(jsonDataList.get(2));
			} else {
				tbaUpdateConfig.setBaseKey("");
				tbaUpdateConfig.setSubKey("");
				tbaUpdateConfig.setJsonKey("");
			}

			if (configDto.getMetaData() != null) {
				tbaUpdateConfig.setMetaData(configDto.getMetaData());
			} else {
				tbaUpdateConfig.setMetaData("");
			}
			tbaUpdateConfig.setPanelId(configDto.getPanelId());
			if (tbaUpdateMetaData != null) {
				tbaUpdateConfig.setTransId(tbaUpdateMetaData.getTransId());
			}
			tbaUpdateConfig.setBasicInfo(configDto.getBasicInfo());

			tbaUpdateConfig.setEventName(configDto.getEventName());
			tbaUpdateConfig.setParNm(configDto.getParNm());
			tbaUpdateConfig.setSequence(configDto.getSequence());
			tbaUpdateConfig.setRecordIdentifier(configDto.getRecordIdentifier());
			tbaUpdateConfig.setIdentifier(configDto.getIdentifier());
			tbaUpdateConfig.setTbaUpdateAction(configDto.getTbaUpdateAction());
			tbaUpdateConfig.setOverrideEdits(String.join(",", configDto.getOverrideEdits()));

			tbaUpdateConfig.setAddManualFlag(configDto.getAddManualFlag());
			TbaUpdateConfig config = tbaUpdateConfigDao.save(tbaUpdateConfig);
			configs.add(config);
		}
		return configs;
	}*/

	/* Save the IfNotDuplicate Details */

	@Override
	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaUpdateConfigDto> entity) throws JsonProcessingException {
		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		TbaUpdateMetaData tbaUpdateMetaData = null;
		ProcessJobMapping processJobMapping = null;
		List<CommonResRowBO> commonRowBOs = new ArrayList<>();
		for (TbaUpdateConfigDto configDto : entity) {
			TbaUpdateConfig tbaUpdateConfig = new TbaUpdateConfig();
			if (configDto.getId() != null) {
				tbaUpdateConfig.setId(configDto.getId());
				tbaUpdateConfig.setUpdatedDate(new Date());
				tbaUpdateConfig.setUpdateName(configDto.getUpdateName());
				tbaUpdateConfig.setActiveFlag(configDto.getActiveFlag());
				tbaUpdateConfig.setUpdatedBy(configDto.getUpdatedBy());
				tbaUpdateConfig.setCreatedDate(configDto.getCreatedDate());
				tbaUpdateConfig.setCreatedBy(configDto.getCreatedBy());
				processJobMapping = configDto.getProcessJobMapping();
				tbaUpdateConfig.setRerunFlag(configDto.getRerunFlag());
			} else {
				tbaUpdateConfig.setCreatedDate(new Date());
				tbaUpdateConfig.setCreatedBy(configDto.getCreatedBy());
				tbaUpdateConfig.setUpdateName(configDto.getUpdateName());
				tbaUpdateConfig.setActiveFlag(ActiveFlag.T.activeFlag());
				tbaUpdateConfig.setUpdatedDate(new Date());
				tbaUpdateConfig.setUpdatedBy(configDto.getUpdatedBy());
				if (configDto.getProcessJobMappingId() != null) {
					processJobMapping = processJobMappingDao
							.finaByProcessJobMappingId(configDto.getProcessJobMappingId());
				} else
					processJobMapping = configDto.getProcessJobMapping();
				tbaUpdateConfig.setRerunFlag(configDto.getRerunFlag());
			}
			if (processJobMapping != null && null != processJobMapping.getClientDetails()) {
				String clientCode = processJobMapping.getClientDetails().getClientCode();
				
				configDto.setClientId(customBeanUtils.checkForClientCode(clientCode));
				LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateConfig()",
						"Client Code Being Set = " + clientCode);
			}
			if (configDto.getRerunFlag() != 'Y' && configDto.getAddManualFlag() != 'Y') {
				int activityId = tbaUpdateProcessDao.findActivityId(configDto.getEventName(), configDto.getClientId());
				tbaUpdateConfig.setActivityId(activityId);
				// classId from tbaUpdateActivity
				if (configDto.getPanelId() != 0) {
					TbaUpdateActivity tbaUpdateActivity = tbaUpdateActivityDao.findByActivityIdAndPanelId(activityId,
							configDto.getPanelId(), configDto.getClientId());
					if (tbaUpdateActivity != null)
						tbaUpdateConfig.setClassId(tbaUpdateActivity.getClassId());

					if (!StringUtils.equalsIgnoreCase("Validate", configDto.getTbaUpdateAction())) {
						// get transId from metadata
						if (configDto.getMetaData() != null && StringUtils.isEmpty(configDto.getTransId())) {
							tbaUpdateMetaData = tbaUpdateMetadataDao.findByMetadataAndPanelId(configDto.getPanelId(),
									configDto.getMetaData(), configDto.getClientId());
							if (tbaUpdateMetaData != null)
								tbaUpdateConfig.setTransId(tbaUpdateMetaData.getTransId());
							else {
								String tableType = tbaUpdateProcessDao.findTableTypeByEventidAndPanelId(activityId,
										configDto.getPanelId(), configDto.getClientId());
								if (tableType != null && (tableType.equalsIgnoreCase(TableType.PYMNT_DED.getType())
										|| tableType.equals(TableType.PYMNT_PLAN_ATTR.getType())
										|| tableType.equals(TableType.PYMNT_PLAN_BENE.getType()))) {
									tbaUpdateConfig.setTransId(configDto.getTransId());
								}
							}
						}

					}

				}
			}
			tbaUpdateConfig.setProcessJobMapping(processJobMapping);
			if (configDto.getJsonKeyId() != null) {
				String tbaFieldName = tbaUpdateJsonKeyDao
						.findTbaFieldNameById(Long.parseLong(configDto.getJsonKeyId()));
				if (tbaFieldName != null)
					tbaUpdateConfig.setTbaFieldName(tbaFieldName);
			}
			if (configDto.getTbaFieldName() != null)
				tbaUpdateConfig.setTbaFieldName(configDto.getTbaFieldName());

			if (configDto.getBaseKey() != null)
				tbaUpdateConfig.setBaseKey(configDto.getBaseKey());
			else
				tbaUpdateConfig.setBaseKey("");

			if (configDto.getSubKey() != null)
				tbaUpdateConfig.setSubKey(configDto.getSubKey());
			else
				tbaUpdateConfig.setSubKey("");

			if (configDto.getJsonKey() != null)
				tbaUpdateConfig.setJsonKey(configDto.getJsonKey());
			else
				tbaUpdateConfig.setJsonKey("");

			if (configDto.getMetaData() != null)
				tbaUpdateConfig.setMetaData(configDto.getMetaData());
			else
				tbaUpdateConfig.setMetaData("");
			
			if (configDto.getSubMetaData() != null) 
				tbaUpdateConfig.setSubMetaData(configDto.getSubMetaData());
			 else 
				tbaUpdateConfig.setSubMetaData("");

			if (configDto.getSubMetaDataId() != null)
				tbaUpdateConfig.setSubMetaDataId(configDto.getSubMetaDataId());
			else
				tbaUpdateConfig.setSubMetaDataId("");

			if (configDto.getAdditionalMetaData() != null) {
				String metaDataJson = new ObjectMapper().writeValueAsString(configDto.getAdditionalMetaData());

				String metaData = (JsonUtils.isValidJson(metaDataJson)) ? metaDataJson : "";
				tbaUpdateConfig.setAdditionalMetaData(metaData);
			} else
				tbaUpdateConfig.setAdditionalMetaData("");

			tbaUpdateConfig.setPanelId(configDto.getPanelId());
			
			if(!StringUtils.isEmpty(configDto.getTransId())) 
				tbaUpdateConfig.setTransId(configDto.getTransId());
			
			if (org.springframework.util.StringUtils.hasText(configDto.getEventLongDesc()))
				tbaUpdateConfig.setEventLongDesc(configDto.getEventLongDesc());
			
			tbaUpdateConfig.setParentTbaUpdateId(configDto.getParentTbaUpdateId());
			if (!ObjectUtils.isEmpty(configDto.getEstimateTbaInquiries()))
				tbaUpdateConfig.setEstimateTbaInquiries(String.join(",", configDto.getEstimateTbaInquiries()));
			
			tbaUpdateConfig.setGroupRelatedPanels(configDto.getGroupRelatedPanels());
			
			tbaUpdateConfig.setBasicInfo(configDto.getBasicInfo());
			tbaUpdateConfig.setEventName(configDto.getEventName());
			tbaUpdateConfig.setParNm(configDto.getParNm());
			tbaUpdateConfig.setSequence(configDto.getSequence());
			tbaUpdateConfig.setRecordIdentifier(configDto.getRecordIdentifier());
			tbaUpdateConfig.setIdentifier(configDto.getIdentifier());
			tbaUpdateConfig.setTbaUpdateAction(configDto.getTbaUpdateAction());
			tbaUpdateConfig.setOverrideEdits(String.join(",", configDto.getOverrideEdits()));
			tbaUpdateConfig.setActLngDesc(configDto.getActLngDesc());
			tbaUpdateConfig.setAddManualFlag(configDto.getAddManualFlag());
			tbaUpdateConfig.setPickFromPendingEvent(configDto.getPickFromPendingEvent());
			tbaUpdateConfig.setSkipTrans(configDto.getSkipTrans());
			if (tbaUpdateConfigDao.checkForDuplicates(tbaUpdateConfig.getProcessJobMapping().getId(),
					tbaUpdateConfig.getUpdateName(), tbaUpdateConfig.getTbaFieldName(), tbaUpdateConfig.getEventName(),
					tbaUpdateConfig.getActivityId(), tbaUpdateConfig.getPanelId(), tbaUpdateConfig.getClassId(),
					tbaUpdateConfig.getJsonKey(), tbaUpdateConfig.getBaseKey(), tbaUpdateConfig.getSubKey(),
					tbaUpdateConfig.getMetaData(), tbaUpdateConfig.getTransId(), tbaUpdateConfig.getValue(),
					tbaUpdateConfig.getRecordIdentifier(), tbaUpdateConfig.getIdentifier(), tbaUpdateConfig.getParNm(),
					tbaUpdateConfig.getRerunFlag(), tbaUpdateConfig.getAddManualFlag(),
					tbaUpdateConfig.getTbaUpdateAction(), tbaUpdateConfig.getOverrideEdits(),
					tbaUpdateConfig.getActLngDesc(), tbaUpdateConfig.getSubMetaData(),tbaUpdateConfig.getSubMetaDataId(), tbaUpdateConfig.getAdditionalMetaData(), tbaUpdateConfig.getEventLongDesc(), tbaUpdateConfig.getParentTbaUpdateId(), tbaUpdateConfig.getEstimateTbaInquiries(), tbaUpdateConfig.getGroupRelatedPanels(), tbaUpdateConfig.isPickFromPendingEvent(), tbaUpdateConfig.isSkipTrans()) == 0) {
				
				TbaUpdateConfig config = tbaUpdateConfigDao.save(tbaUpdateConfig);
				TbaUpdateConfigDto eventConfigDto = convertEventConfigToDto(config);
				commonRowBOs.add(new CommonResRowBO(eventConfigDto, messageSuccess, statusSuccess));

			} else if (configDto.getId() != null) {

				TbaUpdateConfig tempConfig = tbaUpdateConfigDao.findById(configDto.getId()).get();
				if (tempConfig.getSequence().equals(configDto.getSequence())) {
					TbaUpdateConfigDto eventConfigDto = convertEventConfigToDto(tempConfig);
					commonRowBOs.add(new CommonResRowBO(eventConfigDto, messageFailure, statusFailure));
				} else {
					tempConfig.setSequence(configDto.getSequence());
					TbaUpdateConfig config = tbaUpdateConfigDao.save(tempConfig);
					TbaUpdateConfigDto eventConfigDto = convertEventConfigToDto(config);
					commonRowBOs.add(new CommonResRowBO(eventConfigDto, messageSuccess, statusSuccess));
				}
			} else {
				TbaUpdateConfigDto eventConfigDto = convertEventConfigToDto(tbaUpdateConfig);
				commonRowBOs.add(new CommonResRowBO(eventConfigDto, messageFailure, statusFailure));
			}
		}
		return commonRowBOs;
	}

	private TbaUpdateConfigDto convertEventConfigToDto(TbaUpdateConfig tbaUpdateConfig) {
		TbaUpdateConfigDto eventConfigDto = new TbaUpdateConfigDto();
		try {
			BeanUtils.copyProperties(eventConfigDto, tbaUpdateConfig);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "convertEventConfigToDto", "IllegalAccessException: " + e);
		} catch (InvocationTargetException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "convertEventConfigToDto", "InvocationTargetException: " + e);
		}
		updateEventName(eventConfigDto);
		return eventConfigDto;

	}

	private void updateEventName(TbaUpdateConfigDto eventConfigDto) {
		if (org.springframework.util.StringUtils.hasText(eventConfigDto.getEventName())
				&& org.springframework.util.StringUtils.hasText(eventConfigDto.getEventLongDesc()))
			eventConfigDto.setEventName(eventConfigDto.getEventName().concat(HolmesAppConstants.SEPARATOR)
					.concat(eventConfigDto.getEventLongDesc()));
	}
	
}
