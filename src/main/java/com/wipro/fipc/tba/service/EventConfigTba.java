package com.wipro.fipc.tba.service;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.EventHistInqConfigDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.EventHistInqConfig;
import com.wipro.fipc.entity.tba.EventHistoryEffectiveDate;
import com.wipro.fipc.entity.tba.EventHistoryMaster;
import com.wipro.fipc.entity.tba.EventHistoryUI;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class EventConfigTba {
	
	@Autowired
	private BaseDao<EventHistInqConfig> dao;
	@Autowired
	GenericDao<EventHistInqConfig> genericDao;
	
	@Autowired
	GenericDao<EventHistoryMaster> genericDao1;
	@Autowired
	EventHistInqConfigDao ehicDao;
	
	protected static final String EVENT_HISTORY_MASTER = "EVENT_HISTORY_MASTER";
	protected static final String SCHEMA = "tba";
	
	protected static final String EVENT_HIST_INQ_CONFIG = "EVENT_HIST_INQ_CONFIG";


	
	public List<EventHistInqConfig> findByColumn( String columnName, String columnValue) {
		

		return genericDao.findByColumn(EventHistInqConfig.class, SCHEMA, EVENT_HIST_INQ_CONFIG, columnName,
				columnValue);
	}
	public List<EventHistoryMaster> findRecordByColumn( String columnName,String columnValue) {
		
		
		return genericDao1.findRecordByColumn(EventHistoryMaster.class,SCHEMA,EVENT_HISTORY_MASTER,columnName, columnValue);
	}
	
	public List<CommonResRowBO> saveIfNotDuplicate(List<EventHistInqConfig> ehicList) {

		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		int size = ehicList.size();
		List<CommonResRowBO> returnObj = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			Long count = ehicDao.checkForDuplicates(ehicList.get(i).getProcessJobMappingId(),
					ehicList.get(i).getEventName(), ehicList.get(i).getJsonKey(), ehicList.get(i).getParNm(),
					ehicList.get(i).getActivityId(), ehicList.get(i).getEffFromDate(), ehicList.get(i).getEffToDate());

			if (count.intValue() == 0) {
				EventHistInqConfig saveObj = dao.save(ehicList.get(i));
				EventHistoryUI historyUI=seteventHistoryUIFromConfig(saveObj);
					returnObj.add(new CommonResRowBO(historyUI, messageSuccess, statusSuccess));
			} else {
					returnObj.add(new CommonResRowBO(ehicList, messageFailure, statusFailure));
			}
		}

		return returnObj;
	}
	public EventHistoryUI seteventHistoryUIFromConfig(EventHistInqConfig eventConfig) {

	

			EventHistoryUI eventUI = new EventHistoryUI();
			try {
				BeanUtils.copyProperties(eventUI, eventConfig);
			} catch (IllegalAccessException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "seteventHistoryUIFromConfig()1",
						"IllegalAccessException: " + e);
			} catch (InvocationTargetException e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "seteventHistoryUIFromConfig()2",
						"InvocationTargetException: " + e);
			}

			eventUI.setFromDate(splitEffectiveDateBySpace(eventConfig.getEffFromDate()));
			eventUI.setToDate(splitEffectiveDateBySpace(eventConfig.getEffToDate()));
			eventUI.setFieldType(eventConfig.getFiledType());
			eventUI.setTbaFieldName(eventConfig.getTbaFiledName());


	return eventUI;
	}
	public EventHistoryEffectiveDate splitEffectiveDateBySpace(String effectiveDateInString) {
		String[] dbList = effectiveDateInString.split(" ");
		EventHistoryEffectiveDate dateInObject = new EventHistoryEffectiveDate();

		dateInObject.setDatePeriod(dbList[0]);
		dateInObject.setDateFrequency(dbList[1]);
		dateInObject.setDateInterval(dbList[2]);

		return dateInObject;

	}
	public List<CommonRowBO> deletemultiplerows(int id,List<EventHistInqConfig> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(EventHistInqConfig.class, SCHEMA, EVENT_HIST_INQ_CONFIG, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}
	
}
