package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.PaymentDescriptorDao;
import com.wipro.fipc.dao.tba.TbaCDDFieldMappingDao;
import com.wipro.fipc.dao.tba.TbaPlanDescriptorDao;
import com.wipro.fipc.dao.tba.TbaUpdateMetadataDao;
import com.wipro.fipc.dao.tba.TbaUpdateProcessDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.TbaUpdateJsonKey;
import com.wipro.fipc.entity.filelayout.LayoutIdentifier;
import com.wipro.fipc.entity.tba.TbaUpdateConfig;
import com.wipro.fipc.entity.tba.TbaCDDFieldMapping;
import com.wipro.fipc.entity.tba.TbaEditsMaster;
import com.wipro.fipc.entity.tba.TbaPaymentDescriptor;
import com.wipro.fipc.entity.tba.TbaPlanDescriptor;
import com.wipro.fipc.entity.tba.TbaUpdateMetaData;
import com.wipro.fipc.entity.tba.TbaUpdateProcess;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.pojo.tba.TbaUpdateProcessDto;

@Service
public class UpdateConfigService {

	protected static final String TBA_UPDATE_CONFIG = "tba_update_config";
	protected static final String SCHEMA = "tba";
	protected static final String TBA_UPDATE_JSON_KEY = "tba_update_json_key";
	protected static final String TBA_EDITS_MASTER = "tba_edits_master";
	private static final String ACTIVE_FLAG = "T";

	@Autowired
	TbaUpdateMetadataDao tbaUpdateMetadataDao;

	@Autowired
	GenericDao<TbaUpdateConfig> genericDao;

	@Autowired
	GenericDao<TbaEditsMaster> genericDao2;

	@Autowired
	GenericDao<TbaUpdateJsonKey> genericDao1;
	@Autowired
	TbaUpdateProcessDao tbaUpdateProcessDao;
	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	PaymentDescriptorDao paymentDescriptorDao;

	@Autowired
	private TbaPlanDescriptorDao planDescriptorDao;

	@Autowired
	TbaCDDFieldMappingDao tbaCDDFieldMappingDao;

	@Autowired
	private BaseDao<TbaUpdateConfig> dao;
	@Autowired
	private BaseDao<TbaUpdateProcess> dao1;
	@Autowired
	private BaseDao<LayoutIdentifier> dao2;

	public TbaUpdateConfig findById(long id) {
		return dao.findById(id).orElse(null);
	}

	public List<TbaUpdateConfig> findByColumn(String columnName, String columnValue) {

		return genericDao.findByColumn(TbaUpdateConfig.class, SCHEMA, TBA_UPDATE_CONFIG, columnName, columnValue);
	}

	public List<TbaUpdateConfig> list() {
		return (List<TbaUpdateConfig>) dao.findAll();
	}

	public List<TbaUpdateProcess> list1() {
		return (List<TbaUpdateProcess>) dao1.findAll();
	}

	public List<LayoutIdentifier> list2() {
		return (List<LayoutIdentifier>) dao2.findAll();
	}

	public List<CommonRowBO> deletemultiplerows(int id, List<TbaUpdateConfig> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(TbaUpdateConfig.class, SCHEMA, TBA_UPDATE_CONFIG, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	public List<TbaUpdateJsonKey> findRecordByColumn(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, " method - findRecordByColumn", "TBA Update   {}: ");
		return genericDao1.findRecordByColumn(TbaUpdateJsonKey.class, SCHEMA, TBA_UPDATE_JSON_KEY, columnName,
				columnValue);
	}

	public List<TbaUpdateMetaData> getTbaUpdateMetaData(int panel_id, int client_id) {
		LoggerUtil.log(this.getClass(), Level.INFO, " method - getTbaUpdateMetaData", "panel_id {}: " + panel_id);
		LoggerUtil.log(this.getClass(), Level.INFO, " method - getTbaUpdateMetaData",
				"Get client_id   {}: " + client_id);
		return tbaUpdateMetadataDao.getTbaUpdateMetaData(panel_id, client_id);
	}

	public List<TbaPaymentDescriptor> getTbaPaymentDescriptorData(int panel_id, int client_id) {
		LoggerUtil.log(this.getClass(), Level.INFO, " method - getTbaUpdateMetaData ", "panel_id {}: " + panel_id);
		LoggerUtil.log(this.getClass(), Level.INFO, "method -  getTbaUpdateMetaData",
				"Get client_id   {}: " + client_id);
		return paymentDescriptorDao.getTbaPaymentDescriptorData(panel_id, client_id);
	}

	public List<TbaPlanDescriptor> getTbaPlanDescriptorData(int panel_id, int client_id) {
		LoggerUtil.log(this.getClass(), Level.INFO, " method - getTbaPlanDescriptorData ",
				"panel_id {}: " + panel_id + "and  client_id   {}: " + client_id);
		return planDescriptorDao.findByPanelIdAndClientIdAndActiveFlag(panel_id, client_id, ACTIVE_FLAG);
	}

	/**
	 * This Method get cddfield data as metadata from TbaCDDFieldMapping table
	 * 
	 * @param panel_id
	 * @param client_id
	 * @return
	 */
	public List<TbaCDDFieldMapping> getTbaCDDFieldMappingData(int panel_id, int client_id) {
		LoggerUtil.log(this.getClass(), Level.INFO, " method - getTbaUpdateMetaData ", "panel_id {}: " + panel_id);
		LoggerUtil.log(this.getClass(), Level.INFO, "method -  getTbaUpdateMetaData",
				"Get client_id   {}: " + client_id);
		return tbaCDDFieldMappingDao.getTbaCDDFieldMapping(panel_id, client_id);
	}

	public Set<TbaUpdateProcessDto> getUpdateInquiryNames(int clientId, boolean check) {

		if (check) {
			return tbaUpdateProcessDao.getEMUpdateInquiryNames(clientId);
		} else {
			return tbaUpdateProcessDao.getUpdateInquiryNames(clientId);
		}
	}

	public List<TbaEditsMaster> findByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		return genericDao2.findByMultiColumnCondition(TbaEditsMaster.class, SCHEMA, TBA_EDITS_MASTER,
				columnConditionParams);
	}
}
