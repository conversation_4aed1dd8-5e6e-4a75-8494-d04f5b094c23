package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.tba.TbaInquiryMetaDataDao;
import com.wipro.fipc.model.PanelDataBean;
import com.wipro.fipc.model.TbaInquiryMetaDataMaster;

@Service
public class TbaInquiryMetaDataService {

	@Autowired
	private TbaInquiryMetaDataDao tbaInquiryMetaDataDao;

	public List<PanelDataBean> getInquiryMetaDataPanelWise(int clientId, int panelId, Map<String, String> arrayAttributes) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaDataPanelWise", "Service Method Started");

		List<TbaInquiryMetaDataMaster> inquiryMetaDatas = getInquiryMetaData(clientId, panelId);
		if (!CollectionUtils.isEmpty(inquiryMetaDatas)) {
			
			formatAttributeText(inquiryMetaDatas);

			Map<String, List<TbaInquiryMetaDataMaster>> inquiryMetaDataMap = inquiryMetaDatas.stream()
					.collect(Collectors.groupingBy(e -> e.getMetadataTag().trim()));

			List<PanelDataBean> panelDatas = new ArrayList<>();
			arrayAttributes.entrySet().stream().forEach(entry -> {
				panelDatas.add(new PanelDataBean(entry.getKey(), entry.getValue(),
						inquiryMetaDataMap.get(entry.getKey().trim()), null));
			});

			return panelDatas;
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaDataPanelWise", "inquiryMetaData Received Empty");
			return Collections.emptyList();
		}
	}

	public List<TbaInquiryMetaDataMaster> getInquiryMetaData(int clientId, int panelId) {
		Optional<List<com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster>> result = tbaInquiryMetaDataDao.findByClientIdAndPanelIdAndCreatedBy(clientId, panelId, HolmesAppConstants.CREATOR_NAME);

		if(result.isPresent() && result.get().size() > 0) {
			LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaData", "inquiryMetaData List is Present");

			List<com.wipro.fipc.entity.tba.TbaInquiryMetaDataMaster> entities = result.get();
			List<TbaInquiryMetaDataMaster> models = new ArrayList<>();
			entities.stream().forEach(entity -> {
				TbaInquiryMetaDataMaster model = new TbaInquiryMetaDataMaster();
				BeanUtils.copyProperties(entity, model);
				models.add(model);
			});

			return models;
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaData", "inquiryMetaData List Fetched is Empty");
			return Collections.emptyList();
		}
	}
	
	private void formatAttributeText(List<TbaInquiryMetaDataMaster> inquiryMetaDatas) {
		inquiryMetaDatas.stream()
				.filter(inquiryMetaData -> "DEDUCTION".equalsIgnoreCase(inquiryMetaData.getMetadataType())
						&& StringUtils.hasText(inquiryMetaData.getAttributeCode()))
				.forEach(inquiryMetaData -> {
					inquiryMetaData.setAttributeText(
							inquiryMetaData.getAttributeCode() + "-" + inquiryMetaData.getAttributeText());
				});

		inquiryMetaDatas.stream()
				.filter(inquiryMetaData -> "PAYMENT".equalsIgnoreCase(inquiryMetaData.getMetadataType())
						&& StringUtils.hasText(inquiryMetaData.getAttributeId()))
				.forEach(inquiryMetaData -> {
					inquiryMetaData.setAttributeText(
							inquiryMetaData.getAttributeText() + "(" + inquiryMetaData.getAttributeId() + ")");
				});
		
		inquiryMetaDatas.stream()
				.filter(inquiryMetaData -> "FUND".equalsIgnoreCase(inquiryMetaData.getMetadataType())
						&& StringUtils.hasText(inquiryMetaData.getAttributeId()))
				.forEach(inquiryMetaData -> {
					inquiryMetaData.setAttributeText(
							inquiryMetaData.getAttributeText() + "(" + inquiryMetaData.getAttributeId() + ")");
				});
		inquiryMetaDatas.stream()
		.filter(inquiryMetaData -> "ACCOUNT".equalsIgnoreCase(inquiryMetaData.getMetadataType())
				&& StringUtils.hasText(inquiryMetaData.getAttributeId()))
		.forEach(inquiryMetaData -> {
			inquiryMetaData.setAttributeText(
					inquiryMetaData.getAttributeText() + "(" + inquiryMetaData.getAttributeId() + ")");
		});
	}
}
