package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.TbaCommentInqConfigDao;
import com.wipro.fipc.dao.tba.TbaInquiryJsonKeyDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaCommentInqConfig;
import com.wipro.fipc.entity.tba.EventHistoryEffectiveDate;
import com.wipro.fipc.pojo.CommonRowBO;
import com.wipro.fipc.pojo.tba.TbaCommentInquiry;

@Service
public class DbHelperService {

	@Autowired
	GenericDao<TbaCommentInqConfig> genericDao;

	@Autowired
	TbaCommentInqConfigDao tcicDao;
	@Autowired
	private BaseDao<TbaCommentInqConfig> dao;

	protected static final String TBA_COMMENT_INQ_CONFIG = "TBA_COMMENT_INQ_CONFIG";
	protected static final String SCHEMA = "tba";
    
	@Autowired
	@Qualifier("tbaInquiryJsonKeyDao")
	TbaInquiryJsonKeyDao tbaInquiryJsonKeyDao;
	/* Save TbaCommentInqConfig IfNotDuplicate */

	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaCommentInqConfig> tcicList) {

		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		int size = tcicList.size();
		List<CommonResRowBO> returnObj = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			Long count = tcicDao.checkForDuplicates(tcicList.get(i).getProcessJobMapping().getId(),
					tcicList.get(i).getEftFromDate(), tcicList.get(i).getEftToDate(), tcicList.get(i).getTbaFieldName(),
					tcicList.get(i).getJsonKey(), tcicList.get(i).getSubJsonKey(), tcicList.get(i).getFieldType(),
					tcicList.get(i).getParNM());

			if (count.intValue() == 0) {
				TbaCommentInqConfig saveObj = dao.save(tcicList.get(i));

				TbaCommentInquiry tbaComment = setTbaCommentInquiry(saveObj);

				returnObj.add(new CommonResRowBO(tbaComment, messageSuccess, statusSuccess));

			} else {
				returnObj.add(new CommonResRowBO(tcicList, messageFailure, statusFailure));

			}
		}

		return returnObj;
	}
	/* Save TbaCommentInqConfig */

	public TbaCommentInquiry setTbaCommentInquiry(TbaCommentInqConfig commentInqConfig) {

		TbaCommentInquiry obj = null;
		obj = this.getTbaCommentInquiry(commentInqConfig);
		if (!commentInqConfig.getUpdateFlag()) {
			obj.setEftFromDate(splitEffectiveDateBySpace(commentInqConfig.getEftFromDate()));
			obj.setEftToDate(splitEffectiveDateBySpace(commentInqConfig.getEftToDate()));
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "setTbaCommentInquiry()", "setting tba comment from config ends");
		return obj;
	}

	/* Get Tba Comment Inquiry */

	private TbaCommentInquiry getTbaCommentInquiry(TbaCommentInqConfig commentInqConfig) {
		TbaCommentInquiry commentInq = new TbaCommentInquiry();
		if (String.valueOf(commentInqConfig.getActiveFlag()) != null) {
			commentInq.setActiveFlag(String.valueOf(commentInqConfig.getActiveFlag()));
		}
		if (commentInqConfig.getCreatedBy() != null) {
			commentInq.setCreatedBy(commentInqConfig.getCreatedBy());
		}
		if (commentInqConfig.getCreatedDate() != null) {
			commentInq.setCreatedDate(commentInqConfig.getCreatedDate());
		}
		if (commentInqConfig.getFieldType() != null) {
			commentInq.setFieldType(commentInqConfig.getFieldType());
		}
		if (commentInqConfig.getId() != null) {
			commentInq.setId(commentInqConfig.getId());
		}
		if (commentInqConfig.getInquiryDefName() != null) {
			commentInq.setInquiryDefName(commentInqConfig.getInquiryDefName());
		}
		if (commentInqConfig.getJsonKey() != null) {
			commentInq.setJsonKey(commentInqConfig.getJsonKey());
		}
		if (commentInqConfig.getParNM() != null) {
			commentInq.setParNM(commentInqConfig.getParNM());
		}
		if (commentInqConfig.getProcessJobMapping() != null) {
			commentInq.setProcessJobMapping(commentInqConfig.getProcessJobMapping());
		}
		if (commentInqConfig.getSubJsonKey() != null) {
			commentInq.setSubJsonKey(commentInqConfig.getSubJsonKey());
		}
		if (commentInqConfig.getTbaFieldName() != null) {
			commentInq.setTbaFieldName(commentInqConfig.getTbaFieldName());
		}
		if (commentInqConfig.getUpdatedBy() != null) {
			commentInq.setUpdatedBy(commentInqConfig.getUpdatedBy());
		}
		if (commentInqConfig.getUpdatedDate() != null) {
			commentInq.setUpdatedDate(commentInqConfig.getUpdatedDate());
		}

		return commentInq;
	}

	public EventHistoryEffectiveDate splitEffectiveDateBySpace(String effectiveDateInString) {
		String[] dbList = effectiveDateInString.split(" ");
		EventHistoryEffectiveDate dateInObject = new EventHistoryEffectiveDate();

		dateInObject.setDatePeriod(dbList[0]);
		dateInObject.setDateFrequency(dbList[1]);
		dateInObject.setDateInterval(dbList[2]);

		return dateInObject;
	}

	public List<TbaCommentInqConfig> findByColumn(@PathVariable String columnName, @PathVariable String columnValue) {

		return genericDao.findByColumn(TbaCommentInqConfig.class, SCHEMA, TBA_COMMENT_INQ_CONFIG, columnName,
				columnValue);
	}
	public List<TbaCommentInqConfig> findByColumnTba(@PathVariable String columnName, @PathVariable String columnValue,boolean updateFlag) {

		return genericDao.findByColumn(TbaCommentInqConfig.class, SCHEMA, TBA_COMMENT_INQ_CONFIG, columnName,
				columnValue, updateFlag);
	}
	/* Delete Operation Performed */

	public List<CommonRowBO> deletemultiplerows(int id, List<TbaCommentInqConfig> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		boolean updateFlag=entities.get(0).getUpdateFlag();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(TbaCommentInqConfig.class, SCHEMA, TBA_COMMENT_INQ_CONFIG, ids, updatedBy,updateFlag)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	public String getJsonKeyNameAndParName(String parNm, String tbaFieldName, char filterflag) {
		String findByJsonKeyNameAndParName = tbaInquiryJsonKeyDao.findByJsonKeyNameAndParName(parNm, tbaFieldName, 'F');
		String JsonKeyName = null;
		if (findByJsonKeyNameAndParName != null) {
			JsonKeyName = findByJsonKeyNameAndParName.split(",")[0];
		}
		return JsonKeyName;
	}
}
