package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.TbaWelfareInquiryConfig;
import com.wipro.fipc.dao.tba.TbaWelfareInquiryConfigDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.model.TbaWelfareConfigDto;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class TbaWelfareInquiryConfigService implements TbaWelfareInquiryConfig {

	protected static final String TBA_WELFARE_INQUIRY_CONFIG = "tba_welfare_inquiry_config";

	@Autowired
	private TbaWelfareInquiryConfigDao tbaWelfareInquiryConfigDao;

	@Autowired
	private BaseDao<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> dao;

	@Autowired
	GenericDao<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> genericDao;

	@Override
	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaWelfareConfigDto> entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveIfNotDuplicate",
				"TbaWelfareInquiryConfigService , saveIfNotDuplicate started on : " + System.currentTimeMillis());

		List<CommonResRowBO> commonRowBOs = new ArrayList<>();
		entity.forEach(configDto -> {
			com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig tbaWelfareInquiryConfig = new com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig();

			if (configDto.getId() != null) {
				tbaWelfareInquiryConfig.setId(configDto.getId());
				tbaWelfareInquiryConfig.setCreatedBy(configDto.getCreatedBy());
				tbaWelfareInquiryConfig.setCreatedDate(configDto.getCreatedDate());
				tbaWelfareInquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				tbaWelfareInquiryConfig.setUpdatedDate(configDto.getUpdatedDate());
				tbaWelfareInquiryConfig.setActiveFlag(configDto.getActiveFlag());
				tbaWelfareInquiryConfig.setProcessJobMappingId(configDto.getProcessJobMappingId());
			} else {
				tbaWelfareInquiryConfig.setCreatedBy(configDto.getCreatedBy());
				tbaWelfareInquiryConfig.setCreatedDate(HolmesAppConstants.getNewDate());
				tbaWelfareInquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				tbaWelfareInquiryConfig.setUpdatedDate(HolmesAppConstants.getNewDate());
				tbaWelfareInquiryConfig.setActiveFlag(HolmesAppConstants.ACTIVE_FLAG_VALUE);
				tbaWelfareInquiryConfig.setProcessJobMappingId(configDto.getProcessJobMappingId());
			}
			tbaWelfareInquiryConfig.setPracticeAreaCode(StringUtils.hasText(configDto.getPracticeAreaCode()) ? configDto.getPracticeAreaCode() : "");
			tbaWelfareInquiryConfig.setTbaFieldName(StringUtils.hasText(configDto.getTbaFieldName()) ? configDto.getTbaFieldName() : "");
			tbaWelfareInquiryConfig.setJsonKey(StringUtils.hasText(configDto.getJsonKey()) ? configDto.getJsonKey() : "");
			tbaWelfareInquiryConfig.setSubJsonKey(StringUtils.hasText(configDto.getSubJsonKey()) ? configDto.getSubJsonKey() : "");
			tbaWelfareInquiryConfig.setFieldType(StringUtils.hasText(configDto.getFieldType()) ? configDto.getFieldType() : "");
			tbaWelfareInquiryConfig.setParName(StringUtils.hasText(configDto.getParName()) ? configDto.getParName() : "");
			tbaWelfareInquiryConfig.setEventName(StringUtils.hasText(configDto.getEventName()) ? configDto.getEventName() : "");
			tbaWelfareInquiryConfig.setEventLongDesc(StringUtils.hasText(configDto.getEventLongDesc()) ? configDto.getEventLongDesc() : "");
			
			Long count = tbaWelfareInquiryConfigDao.checkForDuplicates(
					tbaWelfareInquiryConfig.getProcessJobMappingId(),
					tbaWelfareInquiryConfig.getPracticeAreaCode(), tbaWelfareInquiryConfig.getTbaFieldName(),
					tbaWelfareInquiryConfig.getJsonKey(), tbaWelfareInquiryConfig.getSubJsonKey(),
					tbaWelfareInquiryConfig.getFieldType(), tbaWelfareInquiryConfig.getParName(), tbaWelfareInquiryConfig.getEventName(), tbaWelfareInquiryConfig.getEventLongDesc());
			if (count == 0) {
				com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig saveData = tbaWelfareInquiryConfigDao
						.save(tbaWelfareInquiryConfig);
				TbaWelfareConfigDto tbaWelfareConfigDto = new ObjectMapper().convertValue(saveData, TbaWelfareConfigDto.class);
				commonRowBOs.add(new CommonResRowBO(tbaWelfareConfigDto, HolmesAppConstants.SAVE_SUCCESS_MESSAGE, HolmesAppConstants.SUCCESS_STATUS));
			} else
				commonRowBOs.add(new CommonResRowBO(tbaWelfareInquiryConfig, HolmesAppConstants.DUPLICATE_MESSAGE, HolmesAppConstants.FAILURE_STATUS));
		});
		LoggerUtil.log(this.getClass(), Level.INFO, "saveIfNotDuplicate",
				"TbaWelfareInquiryConfigService , saveIfNotDuplicate end,  commonRowBOs: " + commonRowBOs);
		return commonRowBOs;
	}

	@Override
	public com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig findById(Long id) {
		return dao.findById(id).orElse(null);
	}

	@Override
	public List<CommonRowBO> deleteMultipleRows(int id, List<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> newLayout) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteMultipleRows",
				"TbaWelfareInquiryConfigService , deleteMultipleRows started on : " + System.currentTimeMillis());
		List<Long> ids = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < newLayout.size(); i++) {
			ids.add(newLayout.get(i).getId());
			deleteMultipleRowsSuccess
					.add(new CommonRowBO(newLayout.get(i).getId(), HolmesAppConstants.DELETE_SUCCESS_MESSAGE, HolmesAppConstants.SUCCESS_STATUS));
			deleteMultipleRowsFailure
					.add(new CommonRowBO(newLayout.get(i).getId(), HolmesAppConstants.DELETE_FAILURE_MESSAGE, HolmesAppConstants.FAILURE_STATUS));
		}
		if (genericDao.deleteMultipleRows(com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig.class, HolmesAppConstants.TBA,
				TBA_WELFARE_INQUIRY_CONFIG, ids, newLayout.get(0).getUpdatedBy())) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	@Override
	public List<com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig> findByColumn(String columnName, String columnValue) {
		return genericDao.findByColumn(com.wipro.fipc.entity.tba.TbaWelfareInquiryConfig.class, HolmesAppConstants.TBA,
				TBA_WELFARE_INQUIRY_CONFIG, columnName, columnValue);
	}
}
