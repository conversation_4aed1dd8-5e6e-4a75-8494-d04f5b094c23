package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.PendingEventDao;
import com.wipro.fipc.dao.tba.PendingEventMasterDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.TbaPendingEventMaster;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaPendingEvent;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class PendingEventTbaService {

	@Autowired
	private PendingEventMasterDao pendingEventMasterDao;

	@Autowired
	PendingEventDao tpeDao;

	@Autowired
	private BaseDao<TbaPendingEvent> dao;
	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;
	@Autowired
	GenericDao<TbaPendingEvent> genericDao;
	protected static final String TBA_PENDING_EVENT = "PENDING_EVENT_INQ_CONFIG";
	protected static final String SCHEMA = "tba";

	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaPendingEvent> tpeList) {

		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		int size = tpeList.size();
		List<CommonResRowBO> returnObj = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			TbaPendingEvent item = tpeList.get(i);

			// PRIORITY: Check if this is an update first (has ID)
			if (item.getId() != null) {
				System.out.println("DEBUG: Entering update path for ID: " + item.getId());

				// UPDATE PATH - No duplicate check needed
				TbaPendingEvent tempObj = tpeDao.findById(item.getId()).get();
				System.out.println("DEBUG: Found existing record with criticalEdits: " + tempObj.isCriticalEdits());
				System.out.println("DEBUG: Incoming criticalEdits value: " + item.isCriticalEdits());

				// Update all fields including criticalEdits
				tempObj.setSequence(item.getSequence());
				System.out.println("DEBUG: About to set criticalEdits...");
				tempObj.setCriticalEdits(item.isCriticalEdits());
				System.out.println("DEBUG: Set criticalEdits to: " + tempObj.isCriticalEdits());

				if (item.getUpdatedBy() != null) {
					tempObj.setUpdatedBy(item.getUpdatedBy());
				}
				if (item.getUpdatedDate() != null) {
					tempObj.setUpdatedDate(item.getUpdatedDate());
				}

				System.out.println("DEBUG: About to save...");
				// Save and return success
				TbaPendingEvent saveObj = tpeDao.save(tempObj);
				System.out.println("DEBUG: Saved successfully with ID: " + saveObj.getId() + ", criticalEdits: " + saveObj.isCriticalEdits());
				returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));

			} else {
				// NEW RECORD PATH - Check for duplicates only for new records
				Long count = tpeDao.checkForDuplicates(item.getProcessJobMapping().getId(),
						item.getEventName(), item.getEventLongDesc(), item.getIdentifier(),
						item.getTbaFieldName(), item.getJsonKey(), item.getParNm(),
						item.getActivityId(), item.getPanelId(), item.getMetaData(),
						item.getTransId(), item.getBaseKey(), item.getSubKey(),
						item.getClassId(), item.getIdentifyFlag(), item.getManualFlag(),
						item.isProcessMultipleOccurrences(), item.isCriticalEdits(), null);

				if (count.intValue() == 0) {
					// No duplicates - save new record
					TbaPendingEvent saveObj = dao.save(item);
					returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));
				} else {
					// Duplicate found - reject
					returnObj.add(new CommonResRowBO(tpeList, messageFailure, statusFailure));
				}
			}
		}

		return returnObj;
	}

	public List<TbaPendingEvent> findByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		return genericDao.findByMultiColumnCondition(TbaPendingEvent.class, SCHEMA, TBA_PENDING_EVENT,
				columnConditionParams);
	}

	public List<CommonRowBO> deletemultiplerows(int id, List<TbaPendingEvent> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(TbaPendingEvent.class, SCHEMA, TBA_PENDING_EVENT, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	public List<TbaPendingEventMaster> getTbaPendingEventMasterData(int clientId) {

		return pendingEventMasterDao.findByclientId(clientId);
	}

	public TbaPendingEvent findById(long id) {
		return dao.findById(id).orElse(null);
	}

}