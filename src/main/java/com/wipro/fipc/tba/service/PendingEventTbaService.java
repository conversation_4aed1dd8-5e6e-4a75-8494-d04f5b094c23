package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.PendingEventDao;
import com.wipro.fipc.dao.tba.PendingEventMasterDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.TbaPendingEventMaster;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaPendingEvent;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class PendingEventTbaService {

	@Autowired
	private PendingEventMasterDao pendingEventMasterDao;

	@Autowired
	PendingEventDao tpeDao;

	@Autowired
	private BaseDao<TbaPendingEvent> dao;
	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;
	@Autowired
	GenericDao<TbaPendingEvent> genericDao;
	protected static final String TBA_PENDING_EVENT = "PENDING_EVENT_INQ_CONFIG";
	protected static final String SCHEMA = "tba";

	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaPendingEvent> tpeList) {

		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		int size = tpeList.size();
		List<CommonResRowBO> returnObj = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			TbaPendingEvent item = tpeList.get(i);

			// If the record has an ID, prioritize update logic over duplicate check
			if (item.getId() != null) {
				TbaPendingEvent tempObj = tpeDao.findById(item.getId()).get();

				boolean isSequenceChanged = !Objects.equals(tempObj.getSequence(), item.getSequence());
				boolean isCriticalEditChanged = tempObj.isCriticalEdits() != item.isCriticalEdits();

				if (isSequenceChanged || isCriticalEditChanged) {
					tempObj.setSequence(item.getSequence());
					tempObj.setCriticalEdits(item.isCriticalEdits());
					// Copy other fields that might have changed
					tempObj.setUpdatedBy(item.getUpdatedBy());
					tempObj.setUpdatedDate(item.getUpdatedDate());

					TbaPendingEvent saveObj = dao.save(tempObj);
					returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));
				} else {
					returnObj.add(new CommonResRowBO(tpeList, messageFailure, statusFailure));
				}
			} else {
				// For new records without ID, check for duplicates
				Long count = tpeDao.checkForDuplicates(item.getProcessJobMapping().getId(),
						item.getEventName(), item.getEventLongDesc(), item.getIdentifier(),
						item.getTbaFieldName(), item.getJsonKey(), item.getParNm(),
						item.getActivityId(), item.getPanelId(), item.getMetaData(),
						item.getTransId(), item.getBaseKey(), item.getSubKey(),
						item.getClassId(), item.getIdentifyFlag(), item.getManualFlag(),
						item.isProcessMultipleOccurrences(), item.isCriticalEdits());

				if (count.intValue() == 0) {
					// Only for new records without ID
					TbaPendingEvent saveObj = dao.save(item);
					returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));
				} else {
					// Duplicate found for new record without ID
					returnObj.add(new CommonResRowBO(tpeList, messageFailure, statusFailure));
				}
			}
		}

		return returnObj;
	}

	public List<TbaPendingEvent> findByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		return genericDao.findByMultiColumnCondition(TbaPendingEvent.class, SCHEMA, TBA_PENDING_EVENT,
				columnConditionParams);
	}

	public List<CommonRowBO> deletemultiplerows(int id, List<TbaPendingEvent> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(TbaPendingEvent.class, SCHEMA, TBA_PENDING_EVENT, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	public List<TbaPendingEventMaster> getTbaPendingEventMasterData(int clientId) {

		return pendingEventMasterDao.findByclientId(clientId);
	}

	public TbaPendingEvent findById(long id) {
		return dao.findById(id).orElse(null);
	}

}