package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.PendingEventDao;
import com.wipro.fipc.dao.tba.PendingEventMasterDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.TbaPendingEventMaster;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaPendingEvent;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class PendingEventTbaService {

	@Autowired
	private PendingEventMasterDao pendingEventMasterDao;

	@Autowired
	PendingEventDao tpeDao;

	@Autowired
	private BaseDao<TbaPendingEvent> dao;
	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;
	@Autowired
	GenericDao<TbaPendingEvent> genericDao;
	protected static final String TBA_PENDING_EVENT = "PENDING_EVENT_INQ_CONFIG";
	protected static final String SCHEMA = "tba";

	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaPendingEvent> tpeList) {

		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		int size = tpeList.size();
		List<CommonResRowBO> returnObj = new ArrayList<>();
		for (int i = 0; i < size; i++) {

			Long count = 0L;
			count = tpeDao.checkForDuplicates(tpeList.get(i).getProcessJobMapping().getId(),
					tpeList.get(i).getEventName(), tpeList.get(i).getEventLongDesc(), tpeList.get(i).getIdentifier(),
					tpeList.get(i).getTbaFieldName(), tpeList.get(i).getJsonKey(), tpeList.get(i).getParNm(),
					tpeList.get(i).getActivityId(), tpeList.get(i).getPanelId(), tpeList.get(i).getMetaData(),
					tpeList.get(i).getTransId(), tpeList.get(i).getBaseKey(), tpeList.get(i).getSubKey(),
					tpeList.get(i).getClassId(), tpeList.get(i).getIdentifyFlag(), tpeList.get(i).getManualFlag(),
					tpeList.get(i).isProcessMultipleOccurrences(), tpeList.get(i).isCriticalEdits());
			if (count.intValue() == 0) {
				TbaPendingEvent saveObj = dao.save(tpeList.get(i));

				returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));

			} else if (tpeList.get(i).getId() != null) {
				TbaPendingEvent tempObj = tpeDao.findById(tpeList.get(i).getId()).get();

				boolean isSequenceChanged = !Objects.equals(tempObj.getSequence(), tpeList.get(i).getSequence());
				boolean isCriticalEditChanged = tempObj.isCriticalEdits() != tpeList.get(i).isCriticalEdits();

				if (isSequenceChanged || isCriticalEditChanged) {
					tempObj.setSequence(tpeList.get(i).getSequence());
					tempObj.setCriticalEdits(tpeList.get(i).isCriticalEdits());

					TbaPendingEvent saveObj = dao.save(tempObj);
					returnObj.add(new CommonResRowBO(saveObj, messageSuccess, statusSuccess));
				} else {
					returnObj.add(new CommonResRowBO(tpeList, messageFailure, statusFailure));
				}
			} else {

				returnObj.add(new CommonResRowBO(tpeList, messageFailure, statusFailure));

			}
		}

		return returnObj;
	}

	public List<TbaPendingEvent> findByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		return genericDao.findByMultiColumnCondition(TbaPendingEvent.class, SCHEMA, TBA_PENDING_EVENT,
				columnConditionParams);
	}

	public List<CommonRowBO> deletemultiplerows(int id, List<TbaPendingEvent> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(TbaPendingEvent.class, SCHEMA, TBA_PENDING_EVENT, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	public List<TbaPendingEventMaster> getTbaPendingEventMasterData(int clientId) {

		return pendingEventMasterDao.findByclientId(clientId);
	}

	public TbaPendingEvent findById(long id) {
		return dao.findById(id).orElse(null);
	}

}