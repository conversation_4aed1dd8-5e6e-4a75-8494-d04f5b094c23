package com.wipro.fipc.tba.service;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.wipro.fipc.common.utils.JsonUtils;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.tba.ProcessJobMappingDao;
import com.wipro.fipc.dao.tba.TbaEventInquiryConfigDao;
import com.wipro.fipc.dao.tba.TbaEventInquiryConfigs;
import com.wipro.fipc.dao.tba.TbaUpdateJsonKeyDao;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.EventInquiryConfig;
import com.wipro.fipc.pojo.tba.TbaEventInquiryConfigDto;

@Service
public class TbaEventInquiryConfigServices implements TbaEventInquiryConfigs{

	@Autowired
	@Qualifier("tbaEventInquiryConfigDao")
	TbaEventInquiryConfigDao tbaEventInquiryConfigDao;


	@Autowired
	@Qualifier("tbaUpdateJsonKeyDao")
	TbaUpdateJsonKeyDao tbaUpdateJsonKeyDao;

	@Autowired
	@Qualifier("processJobMappingDao")
	ProcessJobMappingDao processJobMappingDao;

	@Autowired
	private Gson gson;

	@Override
	public List<CommonResRowBO> saveIfNotDuplicate(List<TbaEventInquiryConfigDto> entity)
			throws JsonProcessingException, IllegalAccessException, InvocationTargetException {
		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		List<CommonResRowBO> commonRowBOs = new ArrayList<>();
		List<String> jsonDataList = new ArrayList<>();
		ProcessJobMapping processJobMapping = null;
		for (TbaEventInquiryConfigDto configDto : entity) {

			EventInquiryConfig eventInquiryConfig = new EventInquiryConfig();

			if (configDto.getId() != null) {
				eventInquiryConfig.setId(configDto.getId());
				eventInquiryConfig.setUpdatedDate(new Date());
				eventInquiryConfig.setActiveFlag(configDto.getActiveFlag());
				eventInquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				eventInquiryConfig.setCreatedDate(configDto.getCreatedDate());
				eventInquiryConfig.setCreatedBy(configDto.getCreatedBy());
				processJobMapping = configDto.getProcessJobMapping();
			} else {
				eventInquiryConfig.setCreatedDate(new Date());
				eventInquiryConfig.setCreatedBy(configDto.getCreatedBy());
				eventInquiryConfig.setActiveFlag("T");
				eventInquiryConfig.setUpdatedDate(new Date());
				eventInquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				processJobMapping = processJobMappingDao.finaByProcessJobMappingId(configDto.getProcessJobMappingId());
			}

			eventInquiryConfig.setPanelId(configDto.getPanelId());
			String jsonKeyData = tbaUpdateJsonKeyDao.findByParNameAndTBAFieldName(configDto.getParNm(),
					configDto.getTbaFieldName());
			if (jsonKeyData != null) {
				jsonDataList = Arrays.asList(jsonKeyData.split(","));
			}
			eventInquiryConfig.setProcessJobMapping(processJobMapping);
			eventInquiryConfig.setTbaFieldName(configDto.getTbaFieldName());
			eventInquiryConfig.setEventName(configDto.getEventName());
			if (configDto.getMetadata() != null && !configDto.getMetadata().equals("")) {
				String metaData = null;
				String metaDataJson = new ObjectMapper().writeValueAsString(configDto.getMetadata());
				if(JsonUtils.isValidJson(metaDataJson))
					metaData = metaDataJson;
				else
					metaData = configDto.getMetadata();

				eventInquiryConfig.setMetadata(metaData);
			} else {
				eventInquiryConfig.setMetadata("");
			}
			if (!jsonDataList.isEmpty()) {
				eventInquiryConfig.setJsonKey(jsonDataList.get(0));
				eventInquiryConfig.setFieldType(jsonDataList.get(1));
			} else {
				eventInquiryConfig.setJsonKey("");
				eventInquiryConfig.setFieldType("");
			}
			eventInquiryConfig.setBaseKey(configDto.getBaseKey());
			eventInquiryConfig.setParNm(configDto.getParNm());
			eventInquiryConfig.setEventInquiryDefName(configDto.getEventInquiryDefName());
			eventInquiryConfig.setRecordIdentifier(configDto.getRecordIdentifier());
			eventInquiryConfig.setEffDateType(configDto.getEffDateType());
			eventInquiryConfig.setEffFromDate(gson.toJson(configDto.getEffFromDate()));
			eventInquiryConfig.setEffToDate(gson.toJson(configDto.getEffToDate()));
			eventInquiryConfig.setSequence(configDto.getSequence());
			eventInquiryConfig.setPendingEvent(configDto.isPendingEvent());
			eventInquiryConfig.setTransId(configDto.getTransId());
			Long forDuplicates =  tbaEventInquiryConfigDao.checkForDuplicates(eventInquiryConfig.
					getProcessJobMapping().getId(), eventInquiryConfig.getEventName(),
					eventInquiryConfig.getPanelId(), eventInquiryConfig.getTbaFieldName(),
					eventInquiryConfig.getJsonKey(),
					eventInquiryConfig.getMetadata(), eventInquiryConfig.getParNm(),
					eventInquiryConfig.getRecordIdentifier(), eventInquiryConfig.getEffDateType(),
					eventInquiryConfig.getEffFromDate(),
					eventInquiryConfig.getEffToDate(),eventInquiryConfig.getSequence(),eventInquiryConfig.
					getEventInquiryDefName(),eventInquiryConfig.getBaseKey(),
					eventInquiryConfig.getFieldType(),
					eventInquiryConfig.isPendingEvent()
					);

			if (forDuplicates == 0) {
				EventInquiryConfig inquiryConfig1 = tbaEventInquiryConfigDao.save(eventInquiryConfig);
				LoggerUtil.log(getClass(), Level.INFO, "saveIfNotDuplicate", "ID {} \n" + inquiryConfig1.getId());
				ObjectMapper mapper = new ObjectMapper();
				TbaEventInquiryConfigDto configDto2 = new TbaEventInquiryConfigDto();
				BeanUtils.copyProperties(configDto2, inquiryConfig1);
				if (eventInquiryConfig.getEffFromDate() != null) {
					configDto2.setEffFromDate(mapper.readValue(inquiryConfig1.getEffFromDate(), Object.class));
				} else 
					inquiryConfig1.setEffFromDate("");
				if (eventInquiryConfig.getEffToDate() != null) {
					configDto2.setEffToDate(mapper.readValue(inquiryConfig1.getEffToDate(), Object.class));
				} else 
					inquiryConfig1.setEffToDate("");
				commonRowBOs.add(new CommonResRowBO(configDto2, messageSuccess, statusSuccess));
			}else 
				commonRowBOs.add(new CommonResRowBO(eventInquiryConfig, messageFailure, statusFailure));
		}
		return commonRowBOs;

	}

	@Override
	public List<EventInquiryConfig> saveInquiryConfig(List<TbaEventInquiryConfigDto> entity)
			throws JsonProcessingException {
		List<String> jsonDataList = new ArrayList<>();
		List<EventInquiryConfig> configs = new ArrayList<>();
		ProcessJobMapping processJobMapping = null;
		for (TbaEventInquiryConfigDto configDto : entity) {

			EventInquiryConfig inquiryConfig = new EventInquiryConfig();

			if (configDto.getId() != null) {
				inquiryConfig.setId(configDto.getId());
				inquiryConfig.setUpdatedDate(new Date());
				inquiryConfig.setActiveFlag(configDto.getActiveFlag());
				inquiryConfig.setUpdatedBy(configDto.getUpdatedBy());
				inquiryConfig.setCreatedDate(configDto.getCreatedDate());
				inquiryConfig.setCreatedBy(configDto.getCreatedBy());
				processJobMapping = configDto.getProcessJobMapping();
			} else {
				inquiryConfig.setCreatedDate(new Date());
				inquiryConfig.setCreatedBy(configDto.getCreatedBy());
				inquiryConfig.setActiveFlag("T");
				processJobMapping = processJobMappingDao.finaByProcessJobMappingId(configDto.getProcessJobMappingId());
			}
			inquiryConfig.setPanelId(configDto.getPanelId());
			String jsonKeyData = tbaUpdateJsonKeyDao.findByJsonKeyNameAndPanelId(configDto.getParNm(),
					configDto.getTbaFieldName());
			if (jsonKeyData != null) {
				jsonDataList = Arrays.asList(jsonKeyData.split(","));
			}
			inquiryConfig.setProcessJobMapping(processJobMapping);
			inquiryConfig.setTbaFieldName(configDto.getTbaFieldName());
			inquiryConfig.setEventName(configDto.getEventName());
			if (configDto.getMetadata() != null && !configDto.getMetadata().equals("")) {
				String metaData = null;
				String metaDataJson = new ObjectMapper().writeValueAsString(configDto.getMetadata());
				if(JsonUtils.isValidJson(metaDataJson))
					metaData = metaDataJson;
				else
					metaData = String.valueOf(configDto.getMetadata());

				inquiryConfig.setMetadata(metaData);
			} else {
				inquiryConfig.setMetadata("");
			}
			inquiryConfig.setJsonKey("");
			inquiryConfig.setBaseKey(configDto.getBaseKey());
			inquiryConfig.setFieldType(configDto.getFieldType());
			inquiryConfig.setParNm(configDto.getParNm());
			inquiryConfig.setEventInquiryDefName(configDto.getEventInquiryDefName());
			inquiryConfig.setRecordIdentifier(configDto.getRecordIdentifier());
			inquiryConfig.setEffDateType(configDto.getEffDateType());
			inquiryConfig.setEffFromDate(gson.toJson(configDto.getEffFromDate()));
			inquiryConfig.setEffToDate(gson.toJson(configDto.getEffToDate()));
			inquiryConfig.setSequence(configDto.getSequence());
			EventInquiryConfig inquiryConfig1 = tbaEventInquiryConfigDao.save(inquiryConfig);

			configs.add(inquiryConfig1);
		}
		return configs;

	}

}
