package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.TbaMetaDataDao;
import com.wipro.fipc.entity.TbaUpdateJsonKey;
import com.wipro.fipc.entity.tba.EventInquiryConfig;
import com.wipro.fipc.entity.tba.TbaMetaData;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class EventInquiryConfigService {
	@Autowired
	GenericDao<EventInquiryConfig> genericDao;
	@Autowired
	private BaseDao<EventInquiryConfig> dao;

	@Autowired
	protected GenericDao<TbaUpdateJsonKey> tbaUpdateJsonKeyDao;

	protected static final String EVENT_INQUIRY_CONFIG = "event_inquiry_config";
	protected static final String SCHEMA = "tba";

	protected static final String EVENT_INQUIRY_JSON_KEY = "tba_update_json_key";

	public List<CommonRowBO> deletemultiplerows(int id, List<EventInquiryConfig> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao.deleteMultipleRows(EventInquiryConfig.class, SCHEMA, EVENT_INQUIRY_CONFIG, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}

	public List<TbaUpdateJsonKey> findRecordByColumn(String columnName, String columnValue) {

		return tbaUpdateJsonKeyDao.findRecordByColumn(TbaUpdateJsonKey.class, SCHEMA, EVENT_INQUIRY_JSON_KEY, columnName,
				columnValue);
	}

	public EventInquiryConfig findById(long id) {
		return dao.findById(id).orElse(null);
	}

	public List<EventInquiryConfig> findByColumn(String columnName, String columnValue) {

		return genericDao.findByColumn(EventInquiryConfig.class, SCHEMA, EVENT_INQUIRY_CONFIG, columnName, columnValue);
	}
	

	public List<EventInquiryConfig> list() {
		return (List<EventInquiryConfig>) dao.findAll();
	}

}