package com.wipro.fipc.tba.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.tba.NoticeServiceDao;
import com.wipro.fipc.entity.SourceMatch.CommonResRowBO;
import com.wipro.fipc.entity.tba.TbaNoticeInqConfig;
import com.wipro.fipc.entity.tba.TbaNoticeMaster;
import com.wipro.fipc.pojo.CommonRowBO;

@Service
public class NoticeTbaService {
	protected static final String TBA_NOTICE_MASTER = "TBA_NOTICE_MASTER";
	protected static final String SCHEMA = "tba";
	protected static final String TBA_NOTICE_INQ_CONFIG = "TBA_NOTICE_INQ_CONFIG";
	
	@Autowired
	GenericDao<TbaNoticeMaster> genericDao;
	
	@Autowired
	GenericDao<TbaNoticeInqConfig> genericDao1;
	
	@Autowired
	private BaseDao<TbaNoticeInqConfig> dao;
	@Autowired
	NoticeServiceDao  noticeServiceDao;
	
	public List<TbaNoticeMaster> findRecordByColumn(String columnName,String columnValue) {
		
		
		return genericDao.findRecordByColumn(TbaNoticeMaster.class,SCHEMA,TBA_NOTICE_MASTER,columnName, columnValue);
	}
	public List<TbaNoticeInqConfig> findByColumn(String columnName, String columnValue) {
		
		return genericDao1.findByColumn(TbaNoticeInqConfig.class,SCHEMA,TBA_NOTICE_INQ_CONFIG,columnName, columnValue);
	}
	public List<CommonRowBO> deletemultiplerows(int id,List<TbaNoticeInqConfig> entities) {
		String messageSuccess = "Records deleted successfully";
		String statusSuccess = "Success";
		String messageFailure = "Records deleting failed";
		String statusFailure = "Failure";
		String updatedBy = entities.get(0).getUpdatedBy();
		List<Long> ids = new ArrayList<>();
		int size = entities.size();
		List<CommonRowBO> deleteMultipleRowsSuccess = new ArrayList<>();
		List<CommonRowBO> deleteMultipleRowsFailure = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			ids.add(entities.get(i).getId());
			deleteMultipleRowsSuccess.add(new CommonRowBO(entities.get(i).getId(), messageSuccess, statusSuccess));
			deleteMultipleRowsFailure.add(new CommonRowBO(entities.get(i).getId(), messageFailure, statusFailure));
		}

		if (genericDao1.deleteMultipleRows(TbaNoticeInqConfig.class, SCHEMA, TBA_NOTICE_INQ_CONFIG, ids, updatedBy)) {
			return deleteMultipleRowsSuccess;
		} else {
			return deleteMultipleRowsFailure;
		}
	}
	public TbaNoticeInqConfig findById(long id) {
		return dao.findById(id).orElse(null);
	}
	public List<CommonResRowBO> saveIfNotDuplicate( List<TbaNoticeInqConfig> tbaNoticeInqConfigs) {

		String messageSuccess = "Records saved successfully";
		String statusSuccess = "Success";
		String messageFailure = "Duplicate record found";
		String statusFailure = "Failure";
		List<CommonResRowBO> returnObj = new ArrayList<>();

		tbaNoticeInqConfigs.forEach(tbaNoticeInqConfig -> {
			if (noticeServiceDao.checkForDuplicates(tbaNoticeInqConfig.getProcessJobMappingId(),
					tbaNoticeInqConfig.getNoticeName(), tbaNoticeInqConfig.getNoticeId(),
					tbaNoticeInqConfig.getClientId(), tbaNoticeInqConfig.getTba_field_name(),
					tbaNoticeInqConfig.getJsonKey(), tbaNoticeInqConfig.getSubJsonKey(),
					tbaNoticeInqConfig.getMetadata(), tbaNoticeInqConfig.getFieldType(), tbaNoticeInqConfig.getParNm(),
					tbaNoticeInqConfig.getRecordIdentifier(), tbaNoticeInqConfig.getIdentifier(),
					tbaNoticeInqConfig.getAddManualFlag()) == 0) {
				TbaNoticeInqConfig config = dao.save(tbaNoticeInqConfig);

				returnObj.add(new CommonResRowBO(config, messageSuccess, statusSuccess));

			} else {
				returnObj.add(new CommonResRowBO(tbaNoticeInqConfig, messageFailure, statusFailure));

			}

		});

		return returnObj;
	}
	

}
