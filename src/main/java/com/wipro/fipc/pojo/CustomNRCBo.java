package com.wipro.fipc.pojo;

import java.util.Date;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;

@Data
public class CustomNRCBo {

	private Long id;
    private String application;
	
    private String file;
	
    private String fileType;
	private char activeFlag;
	
    private String reportName;
	
    private String subject;
	
	private String appendSubject;
	 private Long processJobMappingId;
	
    private String subfolder;
	
    private String path;
	
    private String remarks;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;

	private String createdBy;

	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;

	public CustomNRCBo(Long id, String application, String file, String fileType, char activeFlag, String reportName,
			String subject, String appendSubject, Long processJobMappingId, String subfolder, String path,
			String remarks, Date createdDate, String createdBy, String updatedBy, Date updatedDate) {
		super();
		this.id = id;
		this.application = application;
		this.file = file;
		this.fileType = fileType;
		this.activeFlag = activeFlag;
		this.reportName = reportName;
		this.subject = subject;
		this.appendSubject = appendSubject;
		this.processJobMappingId = processJobMappingId;
		this.subfolder = subfolder;
		this.path = path;
		this.remarks = remarks;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDate = updatedDate;
	}

	
public CustomNRCBo()
	{}


public Long getId() {
	return id;
}


public void setId(Long id) {
	this.id = id;
}


public String getApplication() {
	return application;
}


public void setApplication(String application) {
	this.application = application;
}


public String getFile() {
	return file;
}


public void setFile(String file) {
	this.file = file;
}


public String getFileType() {
	return fileType;
}


public void setFileType(String fileType) {
	this.fileType = fileType;
}


public char getActiveFlag() {
	return activeFlag;
}


public void setActiveFlag(char activeFlag) {
	this.activeFlag = activeFlag;
}


public String getReportName() {
	return reportName;
}


public void setReportName(String reportName) {
	this.reportName = reportName;
}


public String getSubject() {
	return subject;
}


public void setSubject(String subject) {
	this.subject = subject;
}


public String getAppendSubject() {
	return appendSubject;
}


public void setAppendSubject(String appendSubject) {
	this.appendSubject = appendSubject;
}


public Long getProcessJobMappingId() {
	return processJobMappingId;
}


public void setProcessJobMappingId(Long processJobMappingId) {
	this.processJobMappingId = processJobMappingId;
}


public String getSubfolder() {
	return subfolder;
}


public void setSubfolder(String subfolder) {
	this.subfolder = subfolder;
}


public String getPath() {
	return path;
}


public void setPath(String path) {
	this.path = path;
}


public String getRemarks() {
	return remarks;
}


public void setRemarks(String remarks) {
	this.remarks = remarks;
}


public Date getCreatedDate() {
	return createdDate;
}


public void setCreatedDate(Date createdDate) {
	this.createdDate = createdDate;
}


public String getCreatedBy() {
	return createdBy;
}


public void setCreatedBy(String createdBy) {
	this.createdBy = createdBy;
}


public String getUpdatedBy() {
	return updatedBy;
}


public void setUpdatedBy(String updatedBy) {
	this.updatedBy = updatedBy;
}


public Date getUpdatedDate() {
	return updatedDate;
}


public void setUpdatedDate(Date updatedDate) {
	this.updatedDate = updatedDate;
}

	
	
}
