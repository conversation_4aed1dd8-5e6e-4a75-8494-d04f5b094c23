
package com.wipro.fipc.pojo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class RoleDetails {

    private List<String> role;
    private List<RoleConfigBo> manager;
    private List<RoleConfigBo> analyst;
    private List<RoleConfigBo> admin;
	public List<String> getRole() {
		return role;
	}
	public void setRole(List<String> role) {
		this.role = role;
	}
	public List<RoleConfigBo> getManager() {
		return manager;
	}
	public void setManager(List<RoleConfigBo> manager) {
		this.manager = manager;
	}
	public List<RoleConfigBo> getAnalyst() {
		return analyst;
	}
	public void setAnalyst(List<RoleConfigBo> analyst) {
		this.analyst = analyst;
	}
	public List<RoleConfigBo> getAdmin() {
		return admin;
	}
	public void setAdmin(List<RoleConfigBo> admin) {
		this.admin = admin;
	}
    
    
}
