package com.wipro.fipc.pojo;

import lombok.Data;

@Data
public class CustomPJMBO {
public Long businessOpsId;
public Long processJobMappingId;
public String businessOpsName;
public String processName;
public String processType;
public String jobName;
public String eftSubject;
public CustomPJMBO(Long businessOpsId, Long processJobMappingId, String businessOpsName, String processName, 
String processType, String jobName, String eftSubject) {
super();
this.businessOpsId = businessOpsId;
this.processJobMappingId = processJobMappingId;
this.businessOpsName = businessOpsName; 
this.processName = processName;
this.processType = processType; 
this.jobName = jobName; 
this.eftSubject = eftSubject; 
}
public CustomPJMBO() {

     }


}

