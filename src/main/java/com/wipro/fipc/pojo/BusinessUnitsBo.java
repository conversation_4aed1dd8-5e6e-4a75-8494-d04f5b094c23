package com.wipro.fipc.pojo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BusinessUnitsBo {
	@JsonProperty("unitId")
    private Long unitId;
    @JsonProperty("unitCode")
    private String unitCode;
    @JsonProperty("unitName")
    private String unitName;
    @JsonProperty("businessOpsBos")
    public List<BusinessOpsBo> businessOpsBos;
    
    
}
