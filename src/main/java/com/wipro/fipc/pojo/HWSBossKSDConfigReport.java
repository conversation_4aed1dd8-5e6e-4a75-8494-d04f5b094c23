package com.wipro.fipc.pojo;

import java.util.Date;

import org.springframework.lang.Nullable;

import lombok.Data;

@Data
public class HWSBossKSDConfigReport {

	private String business_unit;
	private String ksd_name;
	private String tower;
	private Long pjm_id;
	private String config_status;
	private String client_name;
	private String client_code;
	private String ksd_job_name;
	private String ksd_eft_subject_name;
	private Date created_on;
	private Date updated_on;
	private Date approved_on;
	private String created_by;
	private String updated_by;
	private String approved_by;
	
	@Nullable
	private Long tba_inquiry_id;
	@Nullable
	private Long tba_update_id;
	@Nullable
	private Long rules_id;
	@Nullable
	private Long maestro_task_id;
	@Nullable
	private Long maestro_ticket_id;
	@Nullable
	private Long lotus_notes_id;
	private String owner;
	private String process_name;
	private String process_type;
	
	
	public HWSBossKSDConfigReport() {
		
		// TODO Auto-generated constructor stub
	}


	public HWSBossKSDConfigReport(String business_unit, String ksd_name, String tower, Long pjm_id,
			String config_status, String client_name, String client_code, String ksd_job_name,
			String ksd_eft_subject_name, Date created_on, Date updated_on, Date approved_on, String created_by,
			String updated_by, String approved_by, Long tba_inquiry_id, Long tba_update_id, Long rules_id,
			Long maestro_task_id, Long maestro_ticket_id, Long lotus_notes_id, String owner, String process_name, String process_type) {
		super();
		this.business_unit = business_unit;
		this.ksd_name = ksd_name;
		this.tower = tower;
		this.pjm_id = pjm_id;
		this.config_status = config_status;
		this.client_name = client_name;
		this.client_code = client_code;
		this.ksd_job_name = ksd_job_name;
		this.ksd_eft_subject_name = ksd_eft_subject_name;
		this.created_on = created_on;
		this.updated_on = updated_on;
		this.approved_on = approved_on;
		this.created_by = created_by;
		this.updated_by = updated_by;
		this.approved_by = approved_by;
		this.tba_inquiry_id = tba_inquiry_id;
		this.tba_update_id = tba_update_id;
		this.rules_id = rules_id;
		this.maestro_task_id = maestro_task_id;
		this.maestro_ticket_id = maestro_ticket_id;
		this.lotus_notes_id = lotus_notes_id;
		this.owner = owner;
		this.process_name = process_name;
		this.process_type = process_type;
	}


	public String getBusiness_unit() {
		return business_unit;
	}


	public void setBusiness_unit(String business_unit) {
		this.business_unit = business_unit;
	}


	public String getKsd_name() {
		return ksd_name;
	}


	public void setKsd_name(String ksd_name) {
		this.ksd_name = ksd_name;
	}


	public String getTower() {
		return tower;
	}


	public void setTower(String tower) {
		this.tower = tower;
	}


	public Long getPjm_id() {
		return pjm_id;
	}


	public void setPjm_id(Long pjm_id) {
		this.pjm_id = pjm_id;
	}


	public String getConfig_status() {
		return config_status;
	}


	public void setConfig_status(String config_status) {
		this.config_status = config_status;
	}


	public String getClient_name() {
		return client_name;
	}


	public void setClient_name(String client_name) {
		this.client_name = client_name;
	}


	public String getClient_code() {
		return client_code;
	}


	public void setClient_code(String client_code) {
		this.client_code = client_code;
	}


	public String getKsd_job_name() {
		return ksd_job_name;
	}


	public void setKsd_job_name(String ksd_job_name) {
		this.ksd_job_name = ksd_job_name;
	}


	public String getKsd_eft_subject_name() {
		return ksd_eft_subject_name;
	}


	public void setKsd_eft_subject_name(String ksd_eft_subject_name) {
		this.ksd_eft_subject_name = ksd_eft_subject_name;
	}


	public Date getCreated_on() {
		return created_on;
	}


	public void setCreated_on(Date created_on) {
		this.created_on = created_on;
	}


	public Date getUpdated_on() {
		return updated_on;
	}


	public void setUpdated_on(Date updated_on) {
		this.updated_on = updated_on;
	}


	public Date getApproved_on() {
		return approved_on;
	}


	public void setApproved_on(Date approved_on) {
		this.approved_on = approved_on;
	}


	public String getCreated_by() {
		return created_by;
	}


	public void setCreated_by(String created_by) {
		this.created_by = created_by;
	}


	public String getUpdated_by() {
		return updated_by;
	}


	public void setUpdated_by(String updated_by) {
		this.updated_by = updated_by;
	}


	public String getApproved_by() {
		return approved_by;
	}


	public void setApproved_by(String approved_by) {
		this.approved_by = approved_by;
	}


	public Long getTba_inquiry_id() {
		return tba_inquiry_id;
	}


	public void setTba_inquiry_id(Long tba_inquiry_id) {
		this.tba_inquiry_id = tba_inquiry_id;
	}


	public Long getTba_update_id() {
		return tba_update_id;
	}


	public void setTba_update_id(Long tba_update_id) {
		this.tba_update_id = tba_update_id;
	}


	public Long getRules_id() {
		return rules_id;
	}


	public void setRules_id(Long rules_id) {
		this.rules_id = rules_id;
	}


	public Long getMaestro_task_id() {
		return maestro_task_id;
	}


	public void setMaestro_task_id(Long maestro_task_id) {
		this.maestro_task_id = maestro_task_id;
	}


	public Long getMaestro_ticket_id() {
		return maestro_ticket_id;
	}


	public void setMaestro_ticket_id(Long maestro_ticket_id) {
		this.maestro_ticket_id = maestro_ticket_id;
	}


	public Long getLotus_notes_id() {
		return lotus_notes_id;
	}


	public void setLotus_notes_id(Long lotus_notes_id) {
		this.lotus_notes_id = lotus_notes_id;
	}


	public String getOwner() {
		return owner;
	}


	public void setOwner(String owner) {
		this.owner = owner;
	}


	public String getProcess_name() {
		return process_name;
	}


	public void setProcess_name(String process_name) {
		this.process_name = process_name;
	}


	public String getProcess_type() {
		return process_type;
	}


	public void setProcess_type(String process_type) {
		this.process_type = process_type;
	}


	

	
	
}
