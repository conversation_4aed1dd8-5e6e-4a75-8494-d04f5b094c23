package com.wipro.fipc.pojo;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;

@Data
public class CustomTCCBo {

	private Long id;

	private String taskOwner;

	private String ticketType;

	private String taskType;

	private String title;

	private String division;

	private String assignee;

	private String status;
	private char activeFlag;
	private String responsibleParty;

	private String priority;

	private int dueDays;

	private String interestedParties;

	private String iteration;

	private String projectId;

	private String complexity;

	private String serviceGroup;

	private String businessArea;

	private String percentComplete;
	private Long processJobMappingId;

	private double estimatedWorkHours;

	private String controlAccount;

	private String workPackage;

	private String billingNumDesc;

	private String billingNumber;

	private String sdlcDiscipline;

	private String newDiscussion;

	private String attachment;
	private String interestedFields;

	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;

	private String createdBy;

	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;

	public CustomTCCBo(Long id, String taskOwner, String ticketType, String taskType, String title, String division,
			String assignee, String status, char activeFlag, String responsibleParty, String priority, int dueDays,
			String interestedParties, String iteration, String projectId, String complexity, String serviceGroup,
			String businessArea, String percentComplete, Long processJobMappingId, double estimatedWorkHours,
			String controlAccount, String workPackage, String billingNumDesc, String billingNumber,
			String sdlcDiscipline, String newDiscussion, String attachment, String interestedFields, Date createdDate,
			String createdBy, String updatedBy, Date updatedDate) {
		super();
		this.id = id;
		this.taskOwner = taskOwner;
		this.ticketType = ticketType;
		this.taskType = taskType;
		this.title = title;
		this.division = division;
		this.assignee = assignee;
		this.status = status;
		this.activeFlag = activeFlag;
		this.responsibleParty = responsibleParty;
		this.priority = priority;
		this.dueDays = dueDays;
		this.interestedParties = interestedParties;
		this.iteration = iteration;
		this.projectId = projectId;
		this.complexity = complexity;
		this.serviceGroup = serviceGroup;
		this.businessArea = businessArea;
		this.percentComplete = percentComplete;
		this.processJobMappingId = processJobMappingId;
		this.estimatedWorkHours = estimatedWorkHours;
		this.controlAccount = controlAccount;
		this.workPackage = workPackage;
		this.billingNumDesc = billingNumDesc;
		this.billingNumber = billingNumber;
		this.sdlcDiscipline = sdlcDiscipline;
		this.newDiscussion = newDiscussion;
		this.attachment = attachment;
		this.interestedFields = interestedFields;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDate = updatedDate;
	}

	public CustomTCCBo()
	{}

	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the taskOwner
	 */
	public String getTaskOwner() {
		return taskOwner;
	}

	/**
	 * @param taskOwner the taskOwner to set
	 */
	public void setTaskOwner(String taskOwner) {
		this.taskOwner = taskOwner;
	}

	/**
	 * @return the ticketType
	 */
	public String getTicketType() {
		return ticketType;
	}

	/**
	 * @param ticketType the ticketType to set
	 */
	public void setTicketType(String ticketType) {
		this.ticketType = ticketType;
	}

	/**
	 * @return the taskType
	 */
	public String getTaskType() {
		return taskType;
	}

	/**
	 * @param taskType the taskType to set
	 */
	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @param title the title to set
	 */
	public void setTitle(String title) {
		this.title = title;
	}

	/**
	 * @return the division
	 */
	public String getDivision() {
		return division;
	}

	/**
	 * @param division the division to set
	 */
	public void setDivision(String division) {
		this.division = division;
	}

	/**
	 * @return the assignee
	 */
	public String getAssignee() {
		return assignee;
	}

	/**
	 * @param assignee the assignee to set
	 */
	public void setAssignee(String assignee) {
		this.assignee = assignee;
	}

	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the activeFlag
	 */
	public char getActiveFlag() {
		return activeFlag;
	}

	/**
	 * @param activeFlag the activeFlag to set
	 */
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	/**
	 * @return the responsibleParty
	 */
	public String getResponsibleParty() {
		return responsibleParty;
	}

	/**
	 * @param responsibleParty the responsibleParty to set
	 */
	public void setResponsibleParty(String responsibleParty) {
		this.responsibleParty = responsibleParty;
	}

	/**
	 * @return the priority
	 */
	public String getPriority() {
		return priority;
	}

	/**
	 * @param priority the priority to set
	 */
	public void setPriority(String priority) {
		this.priority = priority;
	}

	/**
	 * @return the dueDays
	 */
	public int getDueDays() {
		return dueDays;
	}

	/**
	 * @param dueDays the dueDays to set
	 */
	public void setDueDays(int dueDays) {
		this.dueDays = dueDays;
	}

	/**
	 * @return the interestedParties
	 */
	public String getInterestedParties() {
		return interestedParties;
	}

	/**
	 * @param interestedParties the interestedParties to set
	 */
	public void setInterestedParties(String interestedParties) {
		this.interestedParties = interestedParties;
	}

	/**
	 * @return the iteration
	 */
	public String getIteration() {
		return iteration;
	}

	/**
	 * @param iteration the iteration to set
	 */
	public void setIteration(String iteration) {
		this.iteration = iteration;
	}

	/**
	 * @return the projectId
	 */
	public String getProjectId() {
		return projectId;
	}

	/**
	 * @param projectId the projectId to set
	 */
	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	/**
	 * @return the complexity
	 */
	public String getComplexity() {
		return complexity;
	}

	/**
	 * @param complexity the complexity to set
	 */
	public void setComplexity(String complexity) {
		this.complexity = complexity;
	}

	/**
	 * @return the serviceGroup
	 */
	public String getServiceGroup() {
		return serviceGroup;
	}

	/**
	 * @param serviceGroup the serviceGroup to set
	 */
	public void setServiceGroup(String serviceGroup) {
		this.serviceGroup = serviceGroup;
	}

	/**
	 * @return the businessArea
	 */
	public String getBusinessArea() {
		return businessArea;
	}

	/**
	 * @param businessArea the businessArea to set
	 */
	public void setBusinessArea(String businessArea) {
		this.businessArea = businessArea;
	}

	/**
	 * @return the percentComplete
	 */
	public String getPercentComplete() {
		return percentComplete;
	}

	/**
	 * @param percentComplete the percentComplete to set
	 */
	public void setPercentComplete(String percentComplete) {
		this.percentComplete = percentComplete;
	}

	/**
	 * @return the processJobMappingId
	 */
	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}

	/**
	 * @param processJobMappingId the processJobMappingId to set
	 */
	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	/**
	 * @return the estimatedWorkHours
	 */
	public double getEstimatedWorkHours() {
		return estimatedWorkHours;
	}

	/**
	 * @param estimatedWorkHours the estimatedWorkHours to set
	 */
	public void setEstimatedWorkHours(double estimatedWorkHours) {
		this.estimatedWorkHours = estimatedWorkHours;
	}

	/**
	 * @return the controlAccount
	 */
	public String getControlAccount() {
		return controlAccount;
	}

	/**
	 * @param controlAccount the controlAccount to set
	 */
	public void setControlAccount(String controlAccount) {
		this.controlAccount = controlAccount;
	}

	/**
	 * @return the workPackage
	 */
	public String getWorkPackage() {
		return workPackage;
	}

	/**
	 * @param workPackage the workPackage to set
	 */
	public void setWorkPackage(String workPackage) {
		this.workPackage = workPackage;
	}

	/**
	 * @return the billingNumDesc
	 */
	public String getBillingNumDesc() {
		return billingNumDesc;
	}

	/**
	 * @param billingNumDesc the billingNumDesc to set
	 */
	public void setBillingNumDesc(String billingNumDesc) {
		this.billingNumDesc = billingNumDesc;
	}

	/**
	 * @return the billingNumber
	 */
	public String getBillingNumber() {
		return billingNumber;
	}

	/**
	 * @param billingNumber the billingNumber to set
	 */
	public void setBillingNumber(String billingNumber) {
		this.billingNumber = billingNumber;
	}

	/**
	 * @return the sdlcDiscipline
	 */
	public String getSdlcDiscipline() {
		return sdlcDiscipline;
	}

	/**
	 * @param sdlcDiscipline the sdlcDiscipline to set
	 */
	public void setSdlcDiscipline(String sdlcDiscipline) {
		this.sdlcDiscipline = sdlcDiscipline;
	}

	/**
	 * @return the newDiscussion
	 */
	public String getNewDiscussion() {
		return newDiscussion;
	}

	/**
	 * @param newDiscussion the newDiscussion to set
	 */
	public void setNewDiscussion(String newDiscussion) {
		this.newDiscussion = newDiscussion;
	}

	/**
	 * @return the attachment
	 */
	public String getAttachment() {
		return attachment;
	}

	/**
	 * @param attachment the attachment to set
	 */
	public void setAttachment(String attachment) {
		this.attachment = attachment;
	}

	/**
	 * @return the interestedFields
	 */
	public String getInterestedFields() {
		return interestedFields;
	}

	/**
	 * @param interestedFields the interestedFields to set
	 */
	public void setInterestedFields(String interestedFields) {
		this.interestedFields = interestedFields;
	}

	/**
	 * @return the createdDate
	 */
	public Date getCreatedDate() {
		return createdDate;
	}

	/**
	 * @param createdDate the createdDate to set
	 */
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	/**
	 * @return the createdBy
	 */
	public String getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the updatedDate
	 */
	public Date getUpdatedDate() {
		return updatedDate;
	}

	/**
	 * @param updatedDate the updatedDate to set
	 */
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	
}
