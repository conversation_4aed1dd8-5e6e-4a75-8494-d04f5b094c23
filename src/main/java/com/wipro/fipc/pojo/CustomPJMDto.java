package com.wipro.fipc.pojo;

import lombok.Data;

@Data
public class CustomPJMDto {

	public Long processJobMappingId;
	public String businessUnitName;
	public Long clientId;
	public String clientName;
	public String clientCode;
	public Long businessOpsId;
	public String businessOpsName;
	public Long processId;
	public String processName;
	public String processType;
	public String eftSubject;
	public String jobName;
	private String ksdName;

	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public String getBusinessUnitName() {
		return businessUnitName;
	}

	public void setBusinessUnitName(String businessUnitName) {
		this.businessUnitName = businessUnitName;
	}

	public Long getClientId() {
		return clientId;
	}

	public void setClientId(Long clientId) {
		this.clientId = clientId;
	}

	public String getClientName() {
		return clientName;
	}

	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	public String getClientCode() {
		return clientCode;
	}

	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}

	public Long getBusinessOpsId() {
		return businessOpsId;
	}

	public void setBusinessOpsId(Long businessOpsId) {
		this.businessOpsId = businessOpsId;
	}

	public String getBusinessOpsName() {
		return businessOpsName;
	}

	public void setBusinessOpsName(String businessOpsName) {
		this.businessOpsName = businessOpsName;
	}

	public Long getProcessId() {
		return processId;
	}

	public void setProcessId(Long processId) {
		this.processId = processId;
	}

	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public String getProcessType() {
		return processType;
	}

	public void setProcessType(String processType) {
		this.processType = processType;
	}

	public String getEftSubject() {
		return eftSubject;
	}

	public void setEftSubject(String eftSubject) {
		this.eftSubject = eftSubject;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getKsdName() {
		return ksdName;
	}

	public void setKsdName(String ksdName) {
		this.ksdName = ksdName;
	}

	public CustomPJMDto(Long processJobMappingId, String businessUnitName, Long clientId, String clientName,
			String clientCode, Long businessOpsId, String businessOpsName, Long processId, String processName,
			String processType, String eftSubject, String jobName, String ksdName) {
		super();
		this.processJobMappingId = processJobMappingId;
		this.businessUnitName = businessUnitName;
		this.clientId = clientId;
		this.clientName = clientName;
		this.clientCode = clientCode;
		this.businessOpsId = businessOpsId;
		this.businessOpsName = businessOpsName;
		this.processId = processId;
		this.processName = processName;
		this.processType = processType;
		this.eftSubject = eftSubject;
		this.jobName = jobName;
		this.ksdName = ksdName;
	}

	public CustomPJMDto() {
		super();
		// TODO Auto-generated constructor stub
	}

}