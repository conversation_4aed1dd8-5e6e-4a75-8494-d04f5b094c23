package com.wipro.fipc.pojo;
import java.util.Date;

import lombok.Data;

@Data
public class HWSBusinessKSDConfigReport {
		
		private String business_unit;
		private String ksd_name;
		private String tower;
		private String config_status;
		private String client_name;
		private String client_code;
		private String ksd_job_name;
		private String ksd_eft_subject_name;
		private Date created_on;
		private Date updated_on;
		private Date approved_on;
		private String created_by;
		private String updated_by;
		private String approved_by;
		private String user_group;
		private String user_group_status;
		private String business;
		private String process_name;
		private String process_type;
		
		
		public HWSBusinessKSDConfigReport() {
		
			// TODO Auto-generated constructor stub
		}


		public HWSBusinessKSDConfigReport(String business_unit, String ksd_name, String tower, String config_status,
				String client_name, String client_code, String ksd_job_name, String ksd_eft_subject_name,
				Date created_on, Date updated_on, Date approved_on, String created_by, String updated_by,
				String approved_by, String user_group, String user_group_status, String business, String process_name, String process_type) {
			super();
			this.business_unit = business_unit;
			this.ksd_name = ksd_name;
			this.tower = tower;
			this.config_status = config_status;
			this.client_name = client_name;
			this.client_code = client_code;
			this.ksd_job_name = ksd_job_name;
			this.ksd_eft_subject_name = ksd_eft_subject_name;
			this.created_on = created_on;
			this.updated_on = updated_on;
			this.approved_on = approved_on;
			this.created_by = created_by;
			this.updated_by = updated_by;
			this.approved_by = approved_by;
			this.user_group = user_group;
			this.user_group_status = user_group_status;
			this.business = business;
			this.process_name = process_name;
			this.process_type = process_type;
		}


		public String getBusiness_unit() {
			return business_unit;
		}


		public void setBusiness_unit(String business_unit) {
			this.business_unit = business_unit;
		}


		public String getKsd_name() {
			return ksd_name;
		}


		public void setKsd_name(String ksd_name) {
			this.ksd_name = ksd_name;
		}


		public String getTower() {
			return tower;
		}


		public void setTower(String tower) {
			this.tower = tower;
		}


		public String getConfig_status() {
			return config_status;
		}


		public void setConfig_status(String config_status) {
			this.config_status = config_status;
		}


		public String getClient_name() {
			return client_name;
		}


		public void setClient_name(String client_name) {
			this.client_name = client_name;
		}


		public String getClient_code() {
			return client_code;
		}


		public void setClient_code(String client_code) {
			this.client_code = client_code;
		}


		public String getKsd_job_name() {
			return ksd_job_name;
		}


		public void setKsd_job_name(String ksd_job_name) {
			this.ksd_job_name = ksd_job_name;
		}


		public String getKsd_eft_subject_name() {
			return ksd_eft_subject_name;
		}


		public void setKsd_eft_subject_name(String ksd_eft_subject_name) {
			this.ksd_eft_subject_name = ksd_eft_subject_name;
		}


		public Date getCreated_on() {
			return created_on;
		}


		public void setCreated_on(Date created_on) {
			this.created_on = created_on;
		}


		public Date getUpdated_on() {
			return updated_on;
		}


		public void setUpdated_on(Date updated_on) {
			this.updated_on = updated_on;
		}


		public Date getApproved_on() {
			return approved_on;
		}


		public void setApproved_on(Date approved_on) {
			this.approved_on = approved_on;
		}


		public String getCreated_by() {
			return created_by;
		}


		public void setCreated_by(String created_by) {
			this.created_by = created_by;
		}


		public String getUpdated_by() {
			return updated_by;
		}


		public void setUpdated_by(String updated_by) {
			this.updated_by = updated_by;
		}


		public String getApproved_by() {
			return approved_by;
		}


		public void setApproved_by(String approved_by) {
			this.approved_by = approved_by;
		}


		public String getUser_group() {
			return user_group;
		}


		public void setUser_group(String user_group) {
			this.user_group = user_group;
		}


		public String getUser_group_status() {
			return user_group_status;
		}


		public void setUser_group_status(String user_group_status) {
			this.user_group_status = user_group_status;
		}


		public String getBusiness() {
			return business;
		}


		public void setBusiness(String business) {
			this.business = business;
		}


		public String getProcess_name() {
			return process_name;
		}


		public void setProcess_name(String process_name) {
			this.process_name = process_name;
		}


		public String getProcess_type() {
			return process_type;
		}


		public void setProcess_type(String process_type) {
			this.process_type = process_type;
		}

		
	}

