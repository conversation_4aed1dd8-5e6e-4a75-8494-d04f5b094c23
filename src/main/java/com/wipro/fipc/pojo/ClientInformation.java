package com.wipro.fipc.pojo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClientInformation {

    @JsonProperty("clientDetails")
    private List<ClientDetail> clientDetails = null;
    @JsonProperty("processTypes")
    private List<ProcessType> processTypes = null;
    @JsonProperty("adid")
    private String adid;
    @JsonProperty("role")
    private String role;
    @JsonProperty("unitName")
    private String unitName;
    @JsonProperty("opsName")
    private String opsName;
    @JsonProperty("BusinessUnitId")
    private Long businessUnitId;
    @JsonProperty("BusinessOpsId")
    private Long businessOpsId;
}

