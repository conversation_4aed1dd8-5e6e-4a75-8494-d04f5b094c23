package com.wipro.fipc.pojo;

import lombok.Data;

@Data
public class AllocationModel {

	private int id;
    private String uid;
	private String botId;
	private String status;
	private String request;
	private String requestType;
	private int phase;
	private int sla;
	private String jobName;
	private String userName;
	private String source;
	private String businessOps;
	private String pluginName;
	private String businessUnit;
	private String batchData;
	private String adxScriptId;
	private String configJson;
	private String comments;
	private String createTimestamp;
	private String createDateTime;
	private String allocateTimestamp;
	private String allocateDateTime;
	private String startTimestamp;
	private String startDateTime;
	private String endTimestamp;
	private String endDateTime;
	private String clientId;
	private String frequency;
	private String clientName;
	private String processType;
	private String taskId;
	private String defectId;
	private int pjmId;
	private String eftSubject;
	private String processName;
	private String assignee;
	private String ksdName;
	private String eftStatus;
	private String eftDateTime;
	private String newAssignee;
	private String assignedBy;
	private String assignedDate;

}