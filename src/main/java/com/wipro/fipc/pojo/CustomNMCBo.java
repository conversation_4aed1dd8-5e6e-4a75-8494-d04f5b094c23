package com.wipro.fipc.pojo;

import java.util.Date;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;

@Data
public class CustomNMCBo {
	private Long id;
    private String condition;
	
    private String toList;
	
    private String ccList;
	
    private String subject;
	
	private char activeFlag;
		
	private String appendSubject;
	
    private String interestedFields;
	
    private String attachmentName;
	
    private String remarks;
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;

	private String createdBy;

	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;
	
    private Long processJobMappingId;

	public CustomNMCBo(Long id, String condition, String toList, String ccList, String subject, char activeFlag,
			String appendSubject, String interestedFields, String attachmentName, String remarks, Date createdDate,
			String createdBy, String updatedBy, Date updatedDate, Long processJobMappingId) {
		super();
		this.id = id;
		this.condition = condition;
		this.toList = toList;
		this.ccList = ccList;
		this.subject = subject;
		this.activeFlag = activeFlag;
		this.appendSubject = appendSubject;
		this.interestedFields = interestedFields;
		this.attachmentName = attachmentName;
		this.remarks = remarks;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDate = updatedDate;
		this.processJobMappingId = processJobMappingId;
	}
	
	public CustomNMCBo()
	{}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCondition() {
		return condition;
	}

	public void setCondition(String condition) {
		this.condition = condition;
	}

	public String getToList() {
		return toList;
	}

	public void setToList(String toList) {
		this.toList = toList;
	}

	public String getCcList() {
		return ccList;
	}

	public void setCcList(String ccList) {
		this.ccList = ccList;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getAppendSubject() {
		return appendSubject;
	}

	public void setAppendSubject(String appendSubject) {
		this.appendSubject = appendSubject;
	}

	public String getInterestedFields() {
		return interestedFields;
	}

	public void setInterestedFields(String interestedFields) {
		this.interestedFields = interestedFields;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

}
