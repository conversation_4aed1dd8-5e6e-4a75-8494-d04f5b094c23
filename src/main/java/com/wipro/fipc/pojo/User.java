package com.wipro.fipc.pojo;

import java.time.LocalDateTime;
import java.util.Date;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@AllArgsConstructor
public class User {

	private Long id;
	private String adid;
	private String role;
	private String createdBy;
	private String updatedBy;
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;
	private String type;
	private String sessionStatus;
    private LocalDateTime sessionDate;
		
}
