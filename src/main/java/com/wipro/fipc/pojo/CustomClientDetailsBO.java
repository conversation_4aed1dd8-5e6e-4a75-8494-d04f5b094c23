package com.wipro.fipc.pojo;

import lombok.Data;

@Data
public class CustomClientDetailsBO {
private Long clientId;
private String clientCode;
private String clientName;
private Long buOpsId;
private String opsName;
private String opsCode;



public CustomClientDetailsBO(Long id,String clientCode, String clientName,Long buOpsId,String opsName,String opsCode ) {
	super();
	this.clientId = id;
	this.clientCode = clientCode;
	this.clientName = clientName;	
	this.buOpsId = buOpsId;
	this.opsName = opsName;
	this.opsCode = opsCode;
}
}
