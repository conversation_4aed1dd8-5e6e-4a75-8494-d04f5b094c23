package com.wipro.fipc.pojo;

import java.util.List;

import lombok.Data;

@Data
public class AllocationResponse {
	
	private List<ClientDetailsList> clientDetailsList;
	
	public List<ClientDetailsList> getClientDetailsList() {
		return clientDetailsList;
	}

	public void setClientDetailsList(List<ClientDetailsList> clientDetailsList) {
		this.clientDetailsList = clientDetailsList;
	}

	

	public List<AnalystList> getAnalystList() {
		return analystList;
	}

	public void setAnalystList(List<AnalystList> analystList) {
		this.analystList = analystList;
	}

	private List<AllocationModel> requestQueueList;
	
	

	public List<AllocationModel> getRequestQueueList() {
		return requestQueueList;
	}

	public void setRequestQueueList(List<AllocationModel> requestQueueList) {
		this.requestQueueList = requestQueueList;
	}

	private List<AnalystList> analystList;

}
