package com.wipro.fipc.pojo;

import java.util.Date;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;


@Data
public class CustomTUCBo {

	
	private Long id;
	private String maestroTaskName;
	
	private String newDiscussion;
	
	private String attachment;
	
	private Long processJobMappingId;
	
	private String type;
	
	private String interestedFields;
	
	private char activeFlag;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;

	private String createdBy;

	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;

	public CustomTUCBo(Long id, String maestroTaskName, String newDiscussion, String attachment,
			Long processJobMappingId, String type, String interestedFields, char activeFlag, Date createdDate,
			String createdBy, String updatedBy, Date updatedDate) {
		super();
		this.id = id;
		this.maestroTaskName = maestroTaskName;
		this.newDiscussion = newDiscussion;
		this.attachment = attachment;
		this.processJobMappingId = processJobMappingId;
		this.type = type;
		this.interestedFields = interestedFields;
		this.activeFlag = activeFlag;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDate = updatedDate;
	}
	
	public CustomTUCBo()
	{}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMaestroTaskName() {
		return maestroTaskName;
	}

	public void setMaestroTaskName(String maestroTaskName) {
		this.maestroTaskName = maestroTaskName;
	}

	public String getNewDiscussion() {
		return newDiscussion;
	}

	public void setNewDiscussion(String newDiscussion) {
		this.newDiscussion = newDiscussion;
	}

	public String getAttachment() {
		return attachment;
	}

	public void setAttachment(String attachment) {
		this.attachment = attachment;
	}

	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getInterestedFields() {
		return interestedFields;
	}

	public void setInterestedFields(String interestedFields) {
		this.interestedFields = interestedFields;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	
}
