package com.wipro.fipc.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ProcessType {

    @JsonProperty("processId")
    private Long processId;
    @JsonProperty("processType")
    private String processType;
    @JsonProperty("processName")
    private String processName;
 
}