package com.wipro.fipc.pojo;

import lombok.Data;

@Data
public class CommonRoleConfig {
	private String role;
	private String adid;
	private Long businessUnitId;
	private String businessUnitName;
	private String businessUnitCode;
	private Long businessOpsId;
	private String businessOpsCode;
	private String businessOpsName;	
	private Long clientDetailsId;
	private String clientDetailsCode;
	private String clientDetailsName;		
	private Long processId;
	private String processType;
	private String processName;
	private Long processJobMappingid;
	
	
	public CommonRoleConfig(){}


	
	public CommonRoleConfig(String role,String adid,Long businessUnitId, String businessUnitName, String businessUnitCode, Long businessOpsId,
			String businessOpsCode, String businessOpsName, Long clientDetailsId, String clientDetailsCode,
			String clientDetailsName, Long processId, String processType, String processName,
			Long processJobMappingid) {
		super();
		this.role = role;
		this.adid = adid;
		this.businessUnitId = businessUnitId;
		this.businessUnitName = businessUnitName;
		this.businessUnitCode = businessUnitCode;
		this.businessOpsId = businessOpsId;
		this.businessOpsCode = businessOpsCode;
		this.businessOpsName = businessOpsName;
		this.clientDetailsId = clientDetailsId;
		this.clientDetailsCode = clientDetailsCode;
		this.clientDetailsName = clientDetailsName;
		this.processId = processId;
		this.processType = processType;
		this.processName = processName;
		this.processJobMappingid = processJobMappingid;
	}
	
	
}
