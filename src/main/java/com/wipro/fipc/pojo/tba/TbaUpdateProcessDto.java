package com.wipro.fipc.pojo.tba;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wipro.fipc.common.utils.converter.StringClientIdToClientCode;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class TbaUpdateProcessDto {
	private String processName;
	private String eventName;
	private int activityId;
	private int panelId;
	@JsonSerialize(using = StringClientIdToClientCode.class)
	private String clientId;
	private String basicInfo;
	private String parNm;
	
	@JsonIgnore
	private String eventLongDesc;

}
