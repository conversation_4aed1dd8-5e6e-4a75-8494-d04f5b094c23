package com.wipro.fipc.pojo.tba;

import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.entity.ProcessJobMapping;
import io.swagger.annotations.ApiModelProperty;

/**
 * TbaInquiryConfigDto
 */

public class TbaInquiryConfigDto   {
  @JsonProperty("activeFlag")
  private String activeFlag = null;

  @JsonProperty("columnMatrix")
  private String columnMatrix = null;

  @JsonProperty("createdBy")
  private String createdBy = null;

  @JsonProperty("createdDate")
  private Date createdDate = null;

  @JsonProperty("effDateType")
  private String effDateType = null;

  @JsonProperty("effFromDate")
  private Object effFromDate = null;

  @JsonProperty("effToDate")
  private Object effToDate = null;

  @JsonProperty("fieldType")
  private String fieldType = null;

  @JsonProperty("flag")
  private Character flag = null;

  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("identifier")
  private String identifier = null;

  @JsonProperty("inquiryDefName")
  private String inquiryDefName = null;

  @JsonProperty("inquiryName")
  private String inquiryName = null;

  @JsonProperty("jsonKey")
  private String jsonKey = null;

  @JsonProperty("metaData")
  private Object metaData = null;

  @JsonProperty("panelId")
  private Integer panelId = null;

  @JsonProperty("parNM")
  private String parNM = null;

  @JsonProperty("processJobMapping")
  private ProcessJobMapping processJobMapping = null;

  @JsonProperty("processJobMappingId")
  private Long processJobMappingId = null;

  @JsonProperty("recordIdentifier")
  private String recordIdentifier = null;

  @JsonProperty("rowMatrix")
  private String rowMatrix = null;

  @JsonProperty("sequence")
  private String sequence = null;

  @JsonProperty("subJsonKey")
  private String subJsonKey = null;

  @JsonProperty("tbaFieldName")
  private String tbaFieldName = null;

  @JsonProperty("updatedBy")
  private String updatedBy = null;

  @JsonProperty("updatedDate")
  private Date updatedDate = null;
  
  @JsonProperty("conditionJson")
  private Object conditionJson=null;
  
  @JsonProperty("estimateMode")
  private Boolean estimateMode =false;

  public Boolean getEstimateMode() {
    return estimateMode;
  }

  public void setEstimateMode(Boolean estimateMode) {
    this.estimateMode = estimateMode;
  }

  @JsonProperty("conditionJson")
  @ApiModelProperty(value = "")
  public Object getConditionJson() {
	return conditionJson;
}
  


public void setConditionJson(Object conditionJson) {
	this.conditionJson = conditionJson;
}
public TbaInquiryConfigDto conditionJson(Object conditionJson) {
    this.conditionJson = conditionJson;
    return this;
  }

public TbaInquiryConfigDto activeFlag(String activeFlag) {
    this.activeFlag = activeFlag;
    return this;
  }

   /**
   * Get activeFlag
   * @return activeFlag
  **/
  @JsonProperty("activeFlag")
  @ApiModelProperty(value = "")
  public String getActiveFlag() {
    return activeFlag;
  }

  public void setActiveFlag(String activeFlag) {
    this.activeFlag = activeFlag;
  }

  public TbaInquiryConfigDto columnMatrix(String columnMatrix) {
    this.columnMatrix = columnMatrix;
    return this;
  }

   /**
   * Get columnMatrix
   * @return columnMatrix
  **/
  @JsonProperty("columnMatrix")
  @ApiModelProperty(value = "")
  public String getColumnMatrix() {
    return columnMatrix;
  }

  public void setColumnMatrix(String columnMatrix) {
    this.columnMatrix = columnMatrix;
  }

  public TbaInquiryConfigDto createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Get createdBy
   * @return createdBy
  **/
  @JsonProperty("createdBy")
  @ApiModelProperty(value = "")
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TbaInquiryConfigDto createdDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

   /**
   * Get createdDate
   * @return createdDate
  **/
  @JsonProperty("createdDate")
  @ApiModelProperty(value = "")
  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  public TbaInquiryConfigDto effDateType(String effDateType) {
    this.effDateType = effDateType;
    return this;
  }

   /**
   * Get effDateType
   * @return effDateType
  **/
  @JsonProperty("effDateType")
  @ApiModelProperty(value = "")
  public String getEffDateType() {
    return effDateType;
  }

  public void setEffDateType(String effDateType) {
    this.effDateType = effDateType;
  }

  public TbaInquiryConfigDto effFromDate(Object effFromDate) {
    this.effFromDate = effFromDate;
    return this;
  }

   /**
   * Get effFromDate
   * @return effFromDate
  **/
  @JsonProperty("effFromDate")
  @ApiModelProperty(value = "")
  public Object getEffFromDate() {
    return effFromDate;
  }

  public void setEffFromDate(Object effFromDate) {
    this.effFromDate = effFromDate;
  }

  public TbaInquiryConfigDto effToDate(Object effToDate) {
    this.effToDate = effToDate;
    return this;
  }

   /**
   * Get effToDate
   * @return effToDate
  **/
  @JsonProperty("effToDate")
  @ApiModelProperty(value = "")
  public Object getEffToDate() {
    return effToDate;
  }

  public void setEffToDate(Object effToDate) {
    this.effToDate = effToDate;
  }

  public TbaInquiryConfigDto fieldType(String fieldType) {
    this.fieldType = fieldType;
    return this;
  }

   /**
   * Get fieldType
   * @return fieldType
  **/
  @JsonProperty("fieldType")
  @ApiModelProperty(value = "")
  public String getFieldType() {
    return fieldType;
  }

  public void setFieldType(String fieldType) {
    this.fieldType = fieldType;
  }

  public TbaInquiryConfigDto flag(Character flag) {
    this.flag = flag;
    return this;
  }

   /**
   * Get flag
   * @return flag
  **/
  @JsonProperty("flag")
  @ApiModelProperty(value = "")
  public Character getFlag() {
    return flag;
  }

  public void setFlag(Character flag) {
    this.flag = flag;
  }

  public TbaInquiryConfigDto id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @JsonProperty("id")
  @ApiModelProperty(value = "")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public TbaInquiryConfigDto identifier(String identifier) {
    this.identifier = identifier;
    return this;
  }

   /**
   * Get identifier
   * @return identifier
  **/
  @JsonProperty("identifier")
  @ApiModelProperty(value = "")
  public String getIdentifier() {
    return identifier;
  }

  public void setIdentifier(String identifier) {
    this.identifier = identifier;
  }

  public TbaInquiryConfigDto inquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
    return this;
  }

   /**
   * Get inquiryDefName
   * @return inquiryDefName
  **/
  @JsonProperty("inquiryDefName")
  @ApiModelProperty(value = "")
  public String getInquiryDefName() {
    return inquiryDefName;
  }

  public void setInquiryDefName(String inquiryDefName) {
    this.inquiryDefName = inquiryDefName;
  }

  public TbaInquiryConfigDto inquiryName(String inquiryName) {
    this.inquiryName = inquiryName;
    return this;
  }

   /**
   * Get inquiryName
   * @return inquiryName
  **/
  @JsonProperty("inquiryName")
  @ApiModelProperty(value = "")
  public String getInquiryName() {
    return inquiryName;
  }

  public void setInquiryName(String inquiryName) {
    this.inquiryName = inquiryName;
  }

  public TbaInquiryConfigDto jsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
    return this;
  }

   /**
   * Get jsonKey
   * @return jsonKey
  **/
  @JsonProperty("jsonKey")
  @ApiModelProperty(value = "")
  public String getJsonKey() {
    return jsonKey;
  }

  public void setJsonKey(String jsonKey) {
    this.jsonKey = jsonKey;
  }

  public TbaInquiryConfigDto metaData(String metaData) {
    this.metaData = metaData;
    return this;
  }

   /**
   * Get metaData
   * @return metaData
  **/
  @JsonProperty("metaData")
  @ApiModelProperty(value = "")
  public Object getMetaData() {
    return metaData;
  }

  public void setMetaData(Object metaData) {
    this.metaData = metaData;
  }

  public TbaInquiryConfigDto panelId(Integer panelId) {
    this.panelId = panelId;
    return this;
  }

   /**
   * Get panelId
   * @return panelId
  **/
  @JsonProperty("panelId")
  @ApiModelProperty(value = "")
  public Integer getPanelId() {
    return panelId;
  }

  public void setPanelId(Integer panelId) {
    this.panelId = panelId;
  }

  public TbaInquiryConfigDto parNM(String parNM) {
    this.parNM = parNM;
    return this;
  }

   /**
   * Get parNM
   * @return parNM
  **/
  @JsonProperty("parNM")
  @ApiModelProperty(value = "")
  public String getParNM() {
    return parNM;
  }

  public void setParNM(String parNM) {
    this.parNM = parNM;
  }

  public TbaInquiryConfigDto processJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
    return this;
  }

   /**
   * Get processJobMapping
   * @return processJobMapping
  **/
  @JsonProperty("processJobMapping")
  @ApiModelProperty(value = "")
  public ProcessJobMapping getProcessJobMapping() {
    return processJobMapping;
  }

  public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
    this.processJobMapping = processJobMapping;
  }

  public TbaInquiryConfigDto processJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
    return this;
  }

   /**
   * Get processJobMappingId
   * @return processJobMappingId
  **/
  @JsonProperty("processJobMappingId")
  @ApiModelProperty(value = "")
  public Long getProcessJobMappingId() {
    return processJobMappingId;
  }

  public void setProcessJobMappingId(Long processJobMappingId) {
    this.processJobMappingId = processJobMappingId;
  }

  public TbaInquiryConfigDto recordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
    return this;
  }

   /**
   * Get recordIdentifier
   * @return recordIdentifier
  **/
  @JsonProperty("recordIdentifier")
  @ApiModelProperty(value = "")
  public String getRecordIdentifier() {
    return recordIdentifier;
  }

  public void setRecordIdentifier(String recordIdentifier) {
    this.recordIdentifier = recordIdentifier;
  }

  public TbaInquiryConfigDto rowMatrix(String rowMatrix) {
    this.rowMatrix = rowMatrix;
    return this;
  }

   /**
   * Get rowMatrix
   * @return rowMatrix
  **/
  @JsonProperty("rowMatrix")
  @ApiModelProperty(value = "")
  public String getRowMatrix() {
    return rowMatrix;
  }

  public void setRowMatrix(String rowMatrix) {
    this.rowMatrix = rowMatrix;
  }

  public TbaInquiryConfigDto sequence(String sequence) {
    this.sequence = sequence;
    return this;
  }

   /**
   * Get sequence
   * @return sequence
  **/
  @JsonProperty("sequence")
  @ApiModelProperty(value = "")
  public String getSequence() {
    return sequence;
  }

  public void setSequence(String sequence) {
    this.sequence = sequence;
  }

  public TbaInquiryConfigDto subJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
    return this;
  }

   /**
   * Get subJsonKey
   * @return subJsonKey
  **/
  @JsonProperty("subJsonKey")
  @ApiModelProperty(value = "")
  public String getSubJsonKey() {
    return subJsonKey;
  }

  public void setSubJsonKey(String subJsonKey) {
    this.subJsonKey = subJsonKey;
  }

  public TbaInquiryConfigDto tbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
    return this;
  }

   /**
   * Get tbaFieldName
   * @return tbaFieldName
  **/
  @JsonProperty("tbaFieldName")
  @ApiModelProperty(value = "")
  public String getTbaFieldName() {
    return tbaFieldName;
  }

  public void setTbaFieldName(String tbaFieldName) {
    this.tbaFieldName = tbaFieldName;
  }

  public TbaInquiryConfigDto updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Get updatedBy
   * @return updatedBy
  **/
  @JsonProperty("updatedBy")
  @ApiModelProperty(value = "")
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TbaInquiryConfigDto updatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
    return this;
  }

   /**
   * Get updatedDate
   * @return updatedDate
  **/
  @JsonProperty("updatedDate")
  @ApiModelProperty(value = "")
  public Date getUpdatedDate() {
    return updatedDate;
  }

  public void setUpdatedDate(Date updatedDate) {
    this.updatedDate = updatedDate;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TbaInquiryConfigDto tbaInquiryConfigDto = (TbaInquiryConfigDto) o;
    return Objects.equals(this.activeFlag, tbaInquiryConfigDto.activeFlag) &&
        Objects.equals(this.columnMatrix, tbaInquiryConfigDto.columnMatrix) &&
        Objects.equals(this.createdBy, tbaInquiryConfigDto.createdBy) &&
        Objects.equals(this.createdDate, tbaInquiryConfigDto.createdDate) &&
        Objects.equals(this.effDateType, tbaInquiryConfigDto.effDateType) &&
        Objects.equals(this.effFromDate, tbaInquiryConfigDto.effFromDate) &&
        Objects.equals(this.effToDate, tbaInquiryConfigDto.effToDate) &&
        Objects.equals(this.fieldType, tbaInquiryConfigDto.fieldType) &&
        Objects.equals(this.flag, tbaInquiryConfigDto.flag) &&
        Objects.equals(this.id, tbaInquiryConfigDto.id) &&
        Objects.equals(this.identifier, tbaInquiryConfigDto.identifier) &&
        Objects.equals(this.inquiryDefName, tbaInquiryConfigDto.inquiryDefName) &&
        Objects.equals(this.inquiryName, tbaInquiryConfigDto.inquiryName) &&
        Objects.equals(this.jsonKey, tbaInquiryConfigDto.jsonKey) &&
        Objects.equals(this.metaData, tbaInquiryConfigDto.metaData) &&
        Objects.equals(this.panelId, tbaInquiryConfigDto.panelId) &&
        Objects.equals(this.parNM, tbaInquiryConfigDto.parNM) &&
        Objects.equals(this.processJobMapping, tbaInquiryConfigDto.processJobMapping) &&
        Objects.equals(this.processJobMappingId, tbaInquiryConfigDto.processJobMappingId) &&
        Objects.equals(this.recordIdentifier, tbaInquiryConfigDto.recordIdentifier) &&
        Objects.equals(this.rowMatrix, tbaInquiryConfigDto.rowMatrix) &&
        Objects.equals(this.sequence, tbaInquiryConfigDto.sequence) &&
        Objects.equals(this.subJsonKey, tbaInquiryConfigDto.subJsonKey) &&
        Objects.equals(this.tbaFieldName, tbaInquiryConfigDto.tbaFieldName) &&
        Objects.equals(this.updatedBy, tbaInquiryConfigDto.updatedBy) &&
        Objects.equals(this.updatedDate, tbaInquiryConfigDto.updatedDate)
        &&Objects.equals(this.conditionJson, tbaInquiryConfigDto.conditionJson);
  }

  @Override
  public int hashCode() {
    return Objects.hash(activeFlag, columnMatrix, createdBy, createdDate, effDateType, effFromDate, effToDate, fieldType, flag, id, identifier, inquiryDefName, inquiryName, jsonKey, metaData, panelId, parNM, processJobMapping, processJobMappingId, recordIdentifier, rowMatrix, sequence, subJsonKey, tbaFieldName, updatedBy, updatedDate,conditionJson);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TbaInquiryConfigDto {\n");
    
    sb.append("    activeFlag: ").append(toIndentedString(activeFlag)).append("\n");
    sb.append("    columnMatrix: ").append(toIndentedString(columnMatrix)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
    sb.append("    effDateType: ").append(toIndentedString(effDateType)).append("\n");
    sb.append("    effFromDate: ").append(toIndentedString(effFromDate)).append("\n");
    sb.append("    effToDate: ").append(toIndentedString(effToDate)).append("\n");
    sb.append("    fieldType: ").append(toIndentedString(fieldType)).append("\n");
    sb.append("    flag: ").append(toIndentedString(flag)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    identifier: ").append(toIndentedString(identifier)).append("\n");
    sb.append("    inquiryDefName: ").append(toIndentedString(inquiryDefName)).append("\n");
    sb.append("    inquiryName: ").append(toIndentedString(inquiryName)).append("\n");
    sb.append("    jsonKey: ").append(toIndentedString(jsonKey)).append("\n");
    sb.append("    metaData: ").append(toIndentedString(metaData)).append("\n");
    sb.append("    panelId: ").append(toIndentedString(panelId)).append("\n");
    sb.append("    parNM: ").append(toIndentedString(parNM)).append("\n");
    sb.append("    processJobMapping: ").append(toIndentedString(processJobMapping)).append("\n");
    sb.append("    processJobMappingId: ").append(toIndentedString(processJobMappingId)).append("\n");
    sb.append("    recordIdentifier: ").append(toIndentedString(recordIdentifier)).append("\n");
    sb.append("    rowMatrix: ").append(toIndentedString(rowMatrix)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    subJsonKey: ").append(toIndentedString(subJsonKey)).append("\n");
    sb.append("    tbaFieldName: ").append(toIndentedString(tbaFieldName)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedDate: ").append(toIndentedString(updatedDate)).append("\n");
    sb.append("    conditionJson: ").append(toIndentedString(conditionJson)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
