package com.wipro.fipc.pojo.tba;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * TbaEventInquiryConfigDto
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TbaEventInquiryConfigDto {

	@JsonProperty("id")
	private Long id;

	@JsonProperty("processJobMapping")
	private ProcessJobMapping processJobMapping;

	@JsonProperty("processJobMappingId")
	private Long processJobMappingId;

	@JsonProperty("eventName")
	private String eventName;

	@JsonProperty("eventInquiryDefName")
	private String eventInquiryDefName;

	@JsonProperty("panelId")
	private Integer panelId;

	@JsonProperty("tbaFieldName")
	private String tbaFieldName;

	@JsonProperty("jsonKey")
	private String jsonKey;

	@JsonProperty("metadata")
	private String metadata;

	@JsonProperty("baseKey")
	private String baseKey;

	@JsonProperty("subKey")
	private String subKey;

	@JsonProperty("transId")
	private String transId;

	@JsonProperty("parNm")
	private String parNm;

	@JsonProperty("effDateType")
	private String effDateType;

	@JsonProperty("effFromDate")
	private Object effFromDate;

	@JsonProperty("effToDate")
	private Object effToDate;

	@JsonProperty("effectiveDate")
	private String effectiveDate;

	@JsonProperty("activeFlag")
	private String activeFlag;

	@JsonProperty("recordIdentifier")
	private String recordIdentifier;

	@JsonProperty("sequence")
	private String sequence;

	@JsonProperty("createdDate")
	private Date createdDate;

	@JsonProperty("createdBy")
	private String createdBy;

	@JsonProperty("updatedDate")
	private Date updatedDate;

	@JsonProperty("updatedBy")
	private String updatedBy;
	
	@JsonProperty("fieldType")
	private String fieldType;

	@JsonProperty("pendingEvent")
	private boolean pendingEvent;
}
