package com.wipro.fipc.pojo;

import java.util.Date;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;

@Data
public class CustomPFCClientCodeBO {

	private Long id;
	private String businessUnitName;
	private String clientName;
	private String clientCode;
	private String businessOpsName;
	private String processName;
	private String jobName;
	private Long processJobMappingId;

	private String phaseNames;


	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;

	private String eftSubject;
	private String processType;
	private String configStatus;
	private String ksdName;
	private String approvedBy;
	@Temporal(TemporalType.TIMESTAMP)
	private Date approvedDate;

	
	public CustomPFCClientCodeBO(Long id, String businessUnitName, String clientName, String clientCode,
			String businessOpsName, String processName, String jobName, String updatedBy, Long processJobMappingId,
			Date updatedDate, String eftSubject, String processType, String configStatus, String phaseNames,
			String ksdName,String approvedBy,Date approvedDate) {
		super();
		this.id = id;
		this.businessUnitName = businessUnitName;
		this.clientName = clientName;
		this.clientCode = clientCode;
		this.businessOpsName = businessOpsName;
		this.processName = processName;
		this.jobName = jobName;
		// this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.processJobMappingId = processJobMappingId;
		this.updatedDate = updatedDate;
		// this.createdDate = createdDate;
		this.eftSubject = eftSubject;
		this.processType = processType;
		this.configStatus = configStatus;
		this.phaseNames = phaseNames;
		this.ksdName = ksdName;
		this.approvedBy = approvedBy;
		this.approvedDate = approvedDate;

	}

	public CustomPFCClientCodeBO() {
	}
	
	public CustomPFCClientCodeBO(Long id, String businessOpsName, String ksdName) {
		super();
		this.id = id;
		this.businessOpsName = businessOpsName;
		this.ksdName = ksdName;
	}
}




