package com.wipro.fipc.pojo;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Lob;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;

@Data
public class TemplateReportUploadPojo {
	private Long id;
	private int buId;
	private char activeFlag;	
	private String type;
	private String clientId;
	private String clientName;
	private String templateReportName;
	@Temporal(TemporalType.TIMESTAMP)
	private Date uploadedDate;
	private String uploadedBy;	
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;
	private String createdBy;	
	private String updatedBy;	
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;
	private String templateReportNameWs;
	private Character reportFlag;
	public TemplateReportUploadPojo(Long id,int buId,char activeFlag,String type, String clientId, String clientName, String templateReportName, Date uploadedDate,
			String uploadedBy, Date createdDate, String createdBy, String updatedBy, Date updatedDate, String templateReportNameWs) {
		super();
		this.id = id;
		this.buId = buId;
		this.activeFlag = activeFlag;
		this.type = type;
		this.clientId = clientId;
		this.clientName = clientName;
		this.templateReportName = templateReportName;
		this.uploadedDate = uploadedDate;
		this.uploadedBy = uploadedBy;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDate = updatedDate;
		this.templateReportNameWs = templateReportNameWs;
	}
	public TemplateReportUploadPojo(Long id, int buId, char activeFlag, String type, String clientId, String clientName,
			String templateReportName, Date uploadedDate, String uploadedBy, Date createdDate, String createdBy,
			String updatedBy, Date updatedDate, String templateReportNameWs, Character reportFlag) {
		super();
		this.id = id;
		this.buId = buId;
		this.activeFlag = activeFlag;
		this.type = type;
		this.clientId = clientId;
		this.clientName = clientName;
		this.templateReportName = templateReportName;
		this.uploadedDate = uploadedDate;
		this.uploadedBy = uploadedBy;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDate = updatedDate;
		this.templateReportNameWs = templateReportNameWs;
		this.reportFlag = reportFlag;
	}
	public TemplateReportUploadPojo() {
		super();
		// TODO Auto-generated constructor stub
	}
	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * @return the buId
	 */
	public int getBuId() {
		return buId;
	}
	/**
	 * @param buId the buId to set
	 */
	public void setBuId(int buId) {
		this.buId = buId;
	}
	/**
	 * @return the activeFlag
	 */
	public char getActiveFlag() {
		return activeFlag;
	}
	/**
	 * @param activeFlag the activeFlag to set
	 */
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}
	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}
	/**
	 * @param type the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}
	/**
	 * @return the clientId
	 */
	public String getClientId() {
		return clientId;
	}
	/**
	 * @param clientId the clientId to set
	 */
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	/**
	 * @return the clientName
	 */
	public String getClientName() {
		return clientName;
	}
	/**
	 * @param clientName the clientName to set
	 */
	public void setClientName(String clientName) {
		this.clientName = clientName;
	}
	/**
	 * @return the templateReportName
	 */
	public String getTemplateReportName() {
		return templateReportName;
	}
	/**
	 * @param templateReportName the templateReportName to set
	 */
	public void setTemplateReportName(String templateReportName) {
		this.templateReportName = templateReportName;
	}
	/**
	 * @return the uploadedDate
	 */
	public Date getUploadedDate() {
		return uploadedDate;
	}
	/**
	 * @param uploadedDate the uploadedDate to set
	 */
	public void setUploadedDate(Date uploadedDate) {
		this.uploadedDate = uploadedDate;
	}
	/**
	 * @return the uploadedBy
	 */
	public String getUploadedBy() {
		return uploadedBy;
	}
	/**
	 * @param uploadedBy the uploadedBy to set
	 */
	public void setUploadedBy(String uploadedBy) {
		this.uploadedBy = uploadedBy;
	}
	/**
	 * @return the createdDate
	 */
	public Date getCreatedDate() {
		return createdDate;
	}
	/**
	 * @param createdDate the createdDate to set
	 */
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	/**
	 * @return the createdBy
	 */
	public String getCreatedBy() {
		return createdBy;
	}
	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	/**
	 * @return the updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}
	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	/**
	 * @return the updatedDate
	 */
	public Date getUpdatedDate() {
		return updatedDate;
	}
	/**
	 * @param updatedDate the updatedDate to set
	 */
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	/**
	 * @return the templateReportNameWs
	 */
	public String getTemplateReportNameWs() {
		return templateReportNameWs;
	}
	/**
	 * @param templateReportNameWs the templateReportNameWs to set
	 */
	public void setTemplateReportNameWs(String templateReportNameWs) {
		this.templateReportNameWs = templateReportNameWs;
	}
	/**
	 * @return the reportFlag
	 */
	public Character getReportFlag() {
		return reportFlag;
	}
	/**
	 * @param reportFlag the reportFlag to set
	 */
	public void setReportFlag(Character reportFlag) {
		this.reportFlag = reportFlag;
	}
	
	
}
