package com.wipro.fipc.pojo;

import java.util.Date;

public class TbaUpdateMetaData {

	private int clientId;

	private int panelId;

	private String metaData;

	private String transId;

	private Date createdDate;

	private String createdBy;
	
	private String subMetaDataId;

	private String subMetaData;

	private String updatedBy;

	private Date updatedDate;

	public int getClientId() {
		return clientId;
	}

	public void setClientId(int clientId) {
		this.clientId = clientId;
	}

	public int getPanelId() {
		return panelId;
	}

	public void setPanelId(int panelId) {
		this.panelId = panelId;
	}

	public String getMetaData() {
		return metaData;
	}

	public void setMetaData(String metaData) {
		this.metaData = metaData;
	}

	public String getTransId() {
		return transId;
	}

	public void setTransId(String transId) {
		this.transId = transId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getSubMetaData() {
		return subMetaData;
	}

	public void setSubMetaData(String subMetaData) {
		this.subMetaData = subMetaData;
	}

	public String getSubMetaDataId() {
		return subMetaDataId;
	}

	public void setSubMetaDataId(String subMetaDataId) {
		this.subMetaDataId = subMetaDataId;
	}

	@Override
	public String toString() {
		return "TbaUpdateMetaData [clientId=" + clientId + ", panelId=" + panelId + ", metaData=" + metaData
				+ ", transId=" + transId + ", createdDate=" + createdDate + ", createdBy=" + createdBy
				+ ", subMetaDataId=" + subMetaDataId + ", subMetaData=" + subMetaData + ", updatedBy=" + updatedBy
				+ ", updatedDate=" + updatedDate + "]";
	}
    
    
	
}
