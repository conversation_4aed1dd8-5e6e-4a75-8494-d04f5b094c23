package com.wipro.fipc.constants;

public class LayoutRuleConstants {

	private LayoutRuleConstants() {
	    throw new IllegalStateException("Utility class");
	  }
	
	public static final String COMMA_SEPARATOR = ",";
	public static final String DROOL_EXT = ".drl";
	public static final String UNDERSCORE_SEPARATOR = "_";
	public static final String SEMICOLON_SEPARATOR = ";";
	public static final String NEW_LINE = "\n";
	public static final String CONSTANT_YES = "yes";
	public static final String CONSTANT_NO = "no";
	public static final String CONSTANT_IMPORT = "import ";
	public static final String JAVA_UTIL_ARRAY = "java.util.Arrays;";
	public static final String JAVA_UTIL_LIST = "java.util.List;";
	
	public static final String DIALECT_JAVA_STMT = "dialect \"java\"";
	public static final String GLOBAL_RESPONSE_STATUS_STMT = "global java.util.HashMap respStatus;";
	public static final String IMPORT_DATE_METHOD = "import static com.wipro.ruleengine.utility.DateUtility.*;";
	public static final String IMPORT_ATTRIBUTEHELPER_METHOD = "import static com.wipro.ruleengine.utility.AttributeHelper.*;";
	
	public static final String PAYLOAD_PACKAGE_NAME = "com.wipro.ruleengine.payload.";
	public static final String BO_PACKAGE_NAME = "com.wipro.ruleengine.bo.";
	public static final String DO_PACKAGE_NAME = "com.wipro.ruleengine.dataobject.";
	public static final String PARTICIPANT_NAME = "com.wipro.ruleengine.bo.Participant";
	public static final String BO = "BO";
	public static final String PARTICIPANT = "Participant";
	
	public static final String VALIDATION_TYPE_LAYOUT = "layout";
	public static final String VALIDATION_TYPE_BUSINESS = "biz";
	public static final String VALIDATION_TYPE_BOTH = "both";
	public static final String VALIDATION_TYPE_TRANSFORMATION = "transformation";
	
	public static final String BIZ_TYPE = "BR";
	public static final String TBA_TYPE = "TR";
	public static final String PARTICIPANT_PREFIX = "com.wipro.ruleengine.bo.";
	public static final String DECIMAL_VALUE_EMPTYDECIMAL = ".trim().matches(\"(^([0-9]+\\\\.?[0-9]*|[0-9]*\\\\.[0-9]+)$)*\")";
	public static final String REGEX_ALPHABETS = ".trim().matches(\"([a-zA-Z]*$)*\")";
	public static final String REGEX_NUMBERS = ".trim().matches(\"([0-9]*$)*\")";
	public static final String REGEX_NO_NUMBERS = ".trim().matches(\"([^0-9]*$)*\")";
	public static final String REGEX_NO_SPECIAL_CHARS = ".trim().matches(\"([^$&+,:;=?@#|'<>.^*()%!-]+$)*\")";
	
	public static final String EQUALSS = "Equals";
	public static final String NOT_EQUAL_TO = "Does not equals";
	public static final String GREATER_THAN = "Is greater than";
	public static final String GREATER_EQUALS = "Is greater than or equal to";
	public static final String LESS_THAN = "Is less than";
	public static final String LESS_EQUALS = "Is less than or equal to";
	public static final String IS_EMPTY = "Is empty";
	public static final String NOT_EMPTY = "Is not empty";
	public static final String COUNT_GREATER_THAN_ZERO = "Count greater than 0";
	public static final String COUNT_EQUALS_TO_ZERO = "Count equals to 0";
	public static final String CONTAINS = "Contains";
	public static final String CONTAINS_ALPHABETS = "Contains only Alphabetical value";
	public static final String CONTAINS_NUMBERS = "Contains only Numerical Value";
	public static final String DOES_NOT_CONTAIN = "Does not contain";
	public static final String DOES_NOT_CONTAIN_NUMBERS = "Does not contain Numerical value";
	public static final String DOES_NOT_CONTAIN_SPECIAL_CHARS = "Does not contain Special characters";
	public static final String BEGINS_WITH = "Begins with";
	public static final String DOES_NOT_BEGIN_WITH = "Does not begin with";
	public static final String ENDS_WITH = "Ends with";
	public static final String DOES_NOT_END_WITH = "Does not end with";
	public static final String UNIQUE = "Unique";
	public static final String NOT_NULL = "Not Null";
	public static final String NOT_UNIQUE = "Not Unique";
	public static final String COUNT_EQUALS_TO = "Count equals to";
	public static final Object IMPORT_OPERATIONSHELPER_METHOD = "import static com.wipro.ruleengine.utility.OperationsHelper.*;";
	
	public static final String TEST_PREFIX = "test.prefix";
	public static final String SUCCESS = "Success";
	public static final String APPLICATION = "application";
	public static final String RESVARIABLE = "resVariable";
	public static final String DECIMAL = "decimal";
	
}
