package com.wipro.fipc.constants;

import java.time.format.DateTimeFormatter;
import java.util.Date;

public final class HolmesAppConstants {

	private HolmesAppConstants() {
	}

	public static final String CREATOR_NAME = "FIPC-Admin";

	public static final String POSTREQUESTMETHOD = "POST";
	public static final String PUTREQUESTMETHOD = "PUT";
	public static final String DELETEREQUESTMETHOD = "DELETE";
	public static final String GETREQUESTMETHOD = "GET";
	public static final String SUCCESS = "success";
	public static final String FAILED = "failed";
	public static final String ERROR = "error";
	public static final String TBA = "TBA";
	public static final String SQL_DATE_FORMAT = "yyyy-MM-dd";
	public static final String COLUMN_NAME = "column_name=";
	public static final String COLUMN_CONDITION = "column_condition=";
	public static final String COLUMN_VALUE = "column_value=";
	public static final String AMPERSAND = "&";
	public static final String FILE_NAMES = "file_name";
	public static final String EQUAL = "eq";
	public static final String ACTIVE_FLAG = "active_flag";
	public static final String ACTIVE_FLAG_VALUE = "T";
	public static final String FILE_TYPE = "Mainframe";

	public static final String MF_FIELD_NAME = "mf_field_name";
	public static final String RECORD_TYPE = "record_type";
	public static final String MF_FIELD_NAME_VALUE = "filler";
	public static final String NOT_LIKE = "ntlk";

	public static final String CONNECTION_REFUSED = "Connection refused: connect;";
	public static final String DB_DOWN = "DBService is down";

	public static final String CONTENT_TYPE = "Content-Type";
	public static final String APPLICATION_JSON = "application/json";
	public static final String CONTENT_TYPE_VALUE = "application/json";

	public static final String CON_REFUSED_MASSAGE = "Connection refused: connect";
	public static final String DB_SERVICE_DOWN_MASSAGE = "DBService is down";
	public static final String GET_CONDITION_NAME = "getConditionName()";
	public static final String GET_OUTPUT_REPORT = "getOutputReport()";
	public static final String GET_CORRECTIVE_ACTION_DATA = "getCorrectiveActionData()";
	public static final String GET_RULE = "getRule()";
	public static final String SUBMIT = "submit()";
	public static final String GET_REQUIRED_FIELD = "getRequiredField()";
	public static final String GET_PROCESS_CONTROL_DATA = "getProcessControlData()";
	public static final String GET_APPLICATION_MISMATCH_ACTION = "getApplicationMismatchAction()";
	public static final String DATA_ELEMENT = "dataElement";
	public static final String DATA_ELEMENT_WS = "dataElementWs";
	public static final String GET_LAYOUT_CONFIG = "getLayoutConfig()";
	public static final String GET_MISMATCH_ACTION = "getMismatch_Action()";
	public static final String GET_TBA_FIELD = "getTBAField()";
	public static final String SHEET_NAME_CONSTANT = "sheetName";
	public static final String SHEET_NAME_WOUT_SPACE_CONSTANT = "sheetNameWoutSpace";
	public static final String SHEET_NAME_WS_CONSTANT = "sheetNameWs";
	public static final String TEMPLATE_REPORT_NAME_WS_CONSTANT = "templateReportNameWs";

	public static final String STATUS = "status";
	public static final String MESSAGE = "message";
	public static final String RECORD_IDENTIFIER = "recordIdentifier";
	public static final String MFFIELD_NAME = "mfFieldName";
	public static final String PREV_REPORT_FILE_NAME = "prevReportFileName";
	public static final String FILE_NAME = "fileName";
	public static final String IDENTIFIER = "identifier";
	public static final String PROCESS_JOB_MAPPING_ID = "process_job_mapping_id";
	public static final String TBA_IDENT = "TBAIDENT";
	public static final String AMPNT = "AMPNT";
	public static final String MULTI_QUERY = "/mulitquery?";
	public static final String CLIENT_NAME = "Client Name";
	public static final String PROCESS_TYPE = "Process Type";
	public static final String PROCESS = "Process";
	public static final String READ_DATA_FROM_EXCEL = "readDataFromExcel";
	public static final String ADID = "adid";
	public static final String ROLE = "role";
	public static final String ACTIVE = "active";
	public static final String BUSINESS_UNIT_ID = "business_unit_id";
	public static final String PROCESS_ID = "process_id";
	public static final String BUSINESS_OPS_ID = "business_ops_id";
	public static final String CLIENT_ID = "client_id";

	public static final String NUMBER = "Number";
	public static final String USE_LABELLING_RPT = "useLabellingRpt";
	public static final String VALUE_DETAILS = "valueDetails";
	public static final String FIELD_TEMPLATE = "fieldTemplate";
	public static final String WHITE_LIST_SSN = "whitelistSSN";
	public static final String FILE_FORMAT = "fileFormat";
	public static final String UPDATED_DATE = "updatedDate";
	public static final String CREATED_DATE = "createdDate";
	public static final String PPT_IDENTIFIER = "pptIdentifier";
	public static final String SQL_QUERY = "sqlQuery";
	public static final String FILE_FORMAT_TYPE = "fileFormatType";
	public static final String RECORD_IDENTIFIER_COL = "recordIdentifierCol";
	public static final String START_POSITION = "startPosition";
	public static final String PREV_REPORT_FILE_NAME_WS = "prevReportFileNameWs";
	public static final String DATE_GENERATED = "dateGenerated";
	public static final String SUB_FOLDER = "subfolder";
	public static final String CLIENT = "client";
	public static final String DATE_FREQUENCY = "dateFrequency";
	public static final String RECORD_IDENTIFIER_VAL = "recordIdentifierVal";
	public static final String CREATED_BY = "createdBy";
	public static final String UPDATED_BY = "updatedBy";
	public static final String PROCESS_JOB_MAPPING_CONFIG = "processJobMappingConfig";
	public static final String ACTIVEFLAG = "activeFlag";
	public static final String RECORDTYPE = "recordType";
	public static final String DATABASE = "database";
	public static final String ACTION = "action";
	public static final String DATE_INTERVAL = "dateInterval";
	public static final String DATE_PERIOD = "datePeriod";
	public static final String QUERY_JCL_NAME = "queryJCLName";
	public static final String DOMAIN = "domain";
	public static final String SOURCE = "source";
	public static final String FILE_NAME_WOUT_SPACE = "fileNameWoutSpace";
	public static final String SUBJECT = "subject";
	public static final String SENDER = "sender";
	public static final String THRESHOLD_MIN = "thresholdMin";
	public static final String THRESHOLD_MAX = "thresholdMax";
	public static final String VARIANCE = "variance";
	public static final String DELIMETER = "delimiter";
	public static final String FILETYPE = "fileType";
	public static final String DATE_FORMAT = "dateFormat";
	public static final String VARIATION = "variation";
	public static final String MAX_THRESHOLD = "maxThreshold";
	public static final String MIN_THRESHOLD = "minThreshold";
	public static final String DATE = "Date";
	public static final String PROCESSJOBMAPPINGID = "processJobMappingId";
	public static final String ID = "id";
	public static final String TEXT = "Text";
	public static final String RECORD_FORMAT = "recordFormat";
	public static final String FORMAT = "format";
	public static final String AMOUNT_FORMAT = "amountFormat";
	public static final String SENDR = "sendr";
	public static final String SUBJ = "subj";
	public static final String PPTIDENTIFIER = "pptidentifier";
	public static final String PPTIDENTIFIERTYPE = "pptidentifierType";
	public static final String MFFIELDWOUTSPACE = "mfFieldWoutSpace";
	public static final String LENGTH = "length";
	public static final String STARTPOS = "startPos";
	public static final String PJMID = "PJMID";
	public static final String PRCNT = "PRCNT";
	public static final String PLUS = "PLUS";
	public static final String UTF8 = "UTF-8";

	public static final String FALSE = "false";
	public static final String NAME = "name";
	public static final String KEY = "key";
	public static final String VALUE = "value";

	public static final String DETAILRECORDS = "detailRecords";
	public static final String KSDFILEJSON = "ksdFileJson";

	public static final String FORMAT_ERR_INDICATOR = "formatErrIndicator";
	public static final String FORMAT_ERR_MESSAGE = "formatErrmessage";

	public static final String WHITELIST = "whitelist";
	public static final String LABELLING_REPORT = "Labelling Report";
	public static final String RECORD_CNT_CHECK = "recordCntCheck";

	public static final String APP_NAME = "appName";
	public static final String API_KEY = "apiKey";
	public static final String FNI = "FnI";
	public static final String HUMAN = "Human";
	public static final String GET_BY_ID = "getRec/";

	public static final String APPEND_NAME_FLAG = "appendNameFlag";
	public static final String FILE_NAME_TEMPLATE = "fileNameTemplate";

	public static final String PREVIOUS_MONTH = "Previous Month";
	public static final String NEXT_MONTH = "Next Month";
	public static final String PYMNT_DED = "PYMNT_DED";
	public static final String PRIMARYFILE = "primaryFile";
	public static final String SAMPLING_COUNT = "samplingCount";
	public static final String PRE_FILTER = "preFilter";
	public static final String PRE_FILTER_OPERATOR = "preFilterOperator";
	public static final String VERIFYEMAILONLY = "verifyEmailOnly";

	public static final String EMAIL_SEARCH_BY = "emailSearchBy";
	public static final String EMAIL_SUBJECT = "Subject";
	public static final String BENE_PPT_IDENTIFIER = "benePptIdentifier";
	public static final String BENE_PPT_IDENTIFIER_TYPE = "benePptIdentifierType";
//	public static final String PRE_FILTER_DATE_TYPE = "preFilterDateType";
//	public static final String PRE_FILTER_DATE_NUMBER = "preFilterDateNumber";
//	public static final String PRE_FILTER_DATE_FREQUENCY = "preFilterDateFrequency";
	public static final String VERIFY_FILE_DATE_DETAIL_RECORD = "verifyFileDateDetailRecord";
	public static final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy");

	public static final String PAYMENT = "PAYMENT";

	public static final String CLIENTID = "Client ID";
	public static final String CHECK_WRITTER_NAME = "CheckWritter Name";
	public static final String LONG_DESCRIPTION = "Long Description";
	public static final String SHORT_DESCRIPTION = "Short Description";
	public static final String TRUST_CODE = "Trust Code";

	public static final String clientId = "clientId";
	public static final String clientName = "clientName";
	public static final String checkWritterName = "checkWritterName";
	public static final String longDescription = "longDescription";
	public static final String shortDescription = "shortDescription";
	public static final String trustCode = "trustCode";
	public static final String attributeCode = "attributeCode";
	public static final String attributeText = "attributeText";

	public static final String TRUST_CODE_MAPPING_RESPONSE = "trustCodeMappingResponse";
	public static final String ROW = "row";
	public static final String SHEET_1 = "Sheet 1";
	public static final String DIGIT = "\\d+";
	public static final String HEADER_MISMATCH = "Trust Code Mapping report header mismatch.";
	public static final String COMMENTS = "comments";
	public static final String CLIENT_CODE_MISMATCH = "Client code mismatch!";
	public static final String SHORT_DESCRIPTION_MISMATCH = "Short description mismatch!";
	public static final String SEPARATOR = " - ";
	
	public static final String SAVE_FAILURE_MESSAGE="Unable to save the record.";
	public static final String UPDATE_FAILURE_MESSAGE="Unable to update the record.";
	public static final String GET_FAILURE_MESSAGE="Unable to get the record.";
	public static final String SAVE_SUCCESS_MESSAGE = "Records saved successfully";
	public static final String DUPLICATE_MESSAGE = "Duplicate record found";
	public static final String DELETE_SUCCESS_MESSAGE = "Records deleted successfully";
	public static final String DELETE_FAILURE_MESSAGE = "Records deleting failed";
	public static final String SUCCESS_STATUS = "Success";
	public static final String FAILURE_STATUS = "Failure";
	public static final String HWS = "HWS";
	public static final String ALLOWED_VALUE = "allowedValues";
	public static final String FIELDS = "fields";
	public static final String APPLICABLE_CLIENTS = "Applicable Clients";
	public static final String IMPACTED_SHARED_GROUPS = "Impacted Shared Groups";
	public static final String APPLICABLE_CLIENTS_KEY = "applicableClients";
	public static final String PROCESS_KEY = "processes";
	public static final String IMPACTED_SHARED_GROUPS_KEY = "impactedSharedGroups";
	public static final String PENDING_EVENT_INQ_CONFIG = "pending_event_inq_config";

	public static final Date getNewDate() {
		return new Date();
	}

}
