package com.wipro.fipc.fileLayoutsupport;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.layoutrule.LayoutConfig;

@Service
public class LayoutConfigContService {

	@Autowired
	GenericDao<LayoutConfig> genericDao;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	EntityManager entityManager;

	protected static final String LAYOUT_CONFIG = "LAYOUT_CONFIG";
	protected static final String LAYOUT_SCHEMA = "layout_rule";
	public static final String MULTICOLUMN = "find By MultiColumn Condition";
	public static final String INLAYOUTAFTTERREPLACE = "In  LayoutConfig After Replace: ";

	public List<LayoutConfig> saveAllData(List<LayoutConfig> layoutConfigs) {

		List<LayoutConfig> ticketCreationConfigs = (List<LayoutConfig>) genericDao.saveAllLayOutConfig(layoutConfigs);

		return ticketCreationConfigs;
	}

	public List<LayoutConfig> findByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {

		for (int j = 0; j < columnValues.size(); j++) {
			String columnValue = columnValues.get(j);
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, "In  LayoutConfig Before Replace: " + columnValue);

			String remove1 = "PLUS";
			String remove2 = "PRCNT";
			String remove3 = "AMPNT";
			if (columnValue.contains(remove1))
				columnValue = columnValue.replaceAll("PLUS", "+");
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

			if (columnValue.contains(remove2))
				columnValue = columnValue.replaceAll("PRCNT", "%");
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

			if (columnValue.contains(remove3))
				columnValue = columnValue.replaceAll("AMPNT", "&");
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

			columnValues.set(j, columnValue);
			LoggerUtil.log(this.getClass(), Level.INFO, MULTICOLUMN, INLAYOUTAFTTERREPLACE + columnValue);

		}

		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);

		List<LayoutConfig> findByMultiColumnCondition = findByMultiColumnCondition(LAYOUT_SCHEMA, LAYOUT_CONFIG,
				columnConditionParams);
		return findByMultiColumnCondition;
	}

	public List<LayoutConfig> findByColumn(String columnName, String columnValue) {

		List<LayoutConfig> layoutConfig = genericDao.findByColumn(LayoutConfig.class, LAYOUT_SCHEMA, LAYOUT_CONFIG,
				columnName, columnValue);
		List<LayoutConfig> layoutlist = new ArrayList<>();
		String mfFiledName = null;
		for (LayoutConfig layout : layoutConfig) {
			mfFiledName = layout.getMfFieldName();
			if (mfFiledName.contains("Filler") || mfFiledName.contains("FILLER")) {
				layout.setMfFieldName("");
				layoutlist.add(layout);

			} else {
				layoutlist.add(layout);
			}
		}
		return layoutlist;
	}

	public List<LayoutConfig> findByMultiColumnCondition(String schema, String table_name,
			List<ColumnConditionParam> columnConditionParamList) {
		Query query = null;
		List<LayoutConfig> resultList = null;
		List<String> colName = new ArrayList<>();
		List<String> colCondition = new ArrayList<>();
		List<String> columnValues = new ArrayList<>();
		System.out.println("In method");
		try {

			for (int i = 0; i < columnConditionParamList.size(); i++) {
				colName.add((columnConditionParamList.get(i)).getColName());
				colCondition.add((columnConditionParamList.get(i)).getColCondion());
				columnValues.add((columnConditionParamList.get(i)).getColValue());
			}
			StringBuffer strBuffer = new StringBuffer(
					"SELECT em.* FROM " + schema + "." + table_name + " as em  WHERE ");

			for (int i = 0; i < colCondition.size(); i++) {
				if (colCondition.get(i).equals("ntlk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("lk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("is")) {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + ""
							+ columnValues.get(i) + "");
				}

				else {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + "'"
							+ columnValues.get(i) + "'");
				}

				if (i != (colCondition.size() - 1)) {
					strBuffer.append(" and ");
				}
			}
			strBuffer.append("order by id");
			String sqlQuery = String.valueOf(strBuffer);

			query = entityManager.createNativeQuery(sqlQuery, LayoutConfig.class);
		
			resultList = query.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return resultList;
	}

}
