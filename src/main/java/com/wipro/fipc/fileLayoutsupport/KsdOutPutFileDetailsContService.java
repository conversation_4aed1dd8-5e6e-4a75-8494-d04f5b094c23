package com.wipro.fipc.fileLayoutsupport;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;

@Service
public class KsdOutPutFileDetailsContService {

	@Autowired
	GenericDao<KsdOutPutFileDetails> genericDao;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	EntityManager entityManager;

	protected static final String KSD_OUTPUT_FILE_DETAILS = "KSD_OUTPUT_FILE_DETAILS";
	protected static final String OUT_SCHEMA = "layout_rule";

	public List<KsdOutPutFileDetails> findByMultiColumnCondition(List<String> columnNames,
			List<String> columnConditions, List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting RulesConfig in  columnConditionParams: " + columnConditionParams);

		List<KsdOutPutFileDetails> findByMultiColumnCondition = findByMultiColumnCondition(OUT_SCHEMA,
				KSD_OUTPUT_FILE_DETAILS, columnConditionParams);

		return findByMultiColumnCondition;
	}

	public List<KsdOutPutFileDetails> findByMultiColumnCondition(String schema, String table_name,
			List<ColumnConditionParam> columnConditionParamList) {
		Query query = null;
		List<KsdOutPutFileDetails> resultList = null;
		List<String> colName = new ArrayList<>();
		List<String> colCondition = new ArrayList<>();
		List<String> columnValues = new ArrayList<>();
		System.out.println("In method");
		try {

			for (int i = 0; i < columnConditionParamList.size(); i++) {
				colName.add((columnConditionParamList.get(i)).getColName());
				colCondition.add((columnConditionParamList.get(i)).getColCondion());
				columnValues.add((columnConditionParamList.get(i)).getColValue());
			}
			StringBuffer strBuffer = new StringBuffer(
					"SELECT em.* FROM " + schema + "." + table_name + " as em  WHERE ");

			for (int i = 0; i < colCondition.size(); i++) {
				if (colCondition.get(i).equals("ntlk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("lk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("is")) {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + ""
							+ columnValues.get(i) + "");
				}

				else {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + "'"
							+ columnValues.get(i) + "'");
				}

				if (i != (colCondition.size() - 1)) {
					strBuffer.append(" and ");
				}
			}
			String sqlQuery = String.valueOf(strBuffer);
			System.out.println("SQL QUERY IS" + sqlQuery);
			query = entityManager.createNativeQuery(sqlQuery, KsdOutPutFileDetails.class);
			System.out.println("response 1");
			resultList = query.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("Respoinse list is" + resultList);
		return resultList;
	}

}
