package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import com.wipro.fipc.entity.filelayout.CustomResponse;
import com.wipro.fipc.entity.batch.KsdFileDetails;

public interface CommonUpdateService {

	public List<CustomResponse> commonMultipledelete(List<KsdFileDetails> ksdFileDetailsList);

	public String getstreamData(String attachmentFile, String FileName);

	public void commonSoftdeleteRuleDifinition(long processJobMappingId, String file_name, String adid);

	public void commonSoftdeleteTbaMatchConfig(long processJobMappingId, String file_name, String adid);

	public void commonSoftdeleletProcessControleConfig(long processJobMappingId, String file_name, String adid);

	public void commonSoftdeleletNotificationMailConfig(long processJobMappingId, String file_name, String adid);

	public void commonSoftdeleletNotificationReportConfig(long processJobMappingId, String file_name, String adid);

	public void commonSoftdeleletTaskUpdateConfig(long processJobMappingId, String file_name, String adid);

	public void commonSoftdeleletTicketCreationConfig(long processJobMappingId, String file_name, String adid);

	public void commonSoftdeleteKsdFileDetails(long processJobMappingId, String filename, String adid);

	public void commonSoftdeleteLayOutConfig(long processJobMappingId, String filename, String adid);

	public boolean commonSoftdeleteksdoutput(long processJobMappingId, String file_name, String adid);

}
