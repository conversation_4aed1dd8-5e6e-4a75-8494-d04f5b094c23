package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.tba.TbaNoticeInqConfig;


@Service
public class TbaNoticeInqConfigContService {
	
	@Autowired
	GenericDao<TbaNoticeInqConfig> genericDao;

	protected static final String TBA_NOTICE_INQ_CONFIG = "TBA_NOTICE_INQ_CONFIG";
	protected static final String SCHEMA = "tba";


	public List<TbaNoticeInqConfig> findByColumn(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn", "tbaNoticeConfig   {}: " + columnValue);
		return genericDao.findByColumn(TbaNoticeInqConfig.class, SCHEMA, TBA_NOTICE_INQ_CONFIG, columnName,
				columnValue);
	}

}
