package com.wipro.fipc.fileLayoutsupport;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.dao.KsdFileDetailsDao;
import com.wipro.fipc.dao.NotificationMailConfigRepository;
import com.wipro.fipc.dao.NotificationReportConfigRepository;
import com.wipro.fipc.dao.filelayout.KsdOutPutFileDetailsDao;
import com.wipro.fipc.dao.filelayout.OutputReportDao;
import com.wipro.fipc.dao.filelayout.ParticipantRecordIdentifierDao;
import com.wipro.fipc.dao.filelayout.ProcessControlConfigDao;
import com.wipro.fipc.dao.filelayout.ReportDataCleanseDao;
import com.wipro.fipc.dao.filelayout.ReportFormatDao;
import com.wipro.fipc.dao.filelayout.ReportPivotDao;
import com.wipro.fipc.dao.filelayout.RulesConfigDao;
import com.wipro.fipc.dao.filelayout.RulesDefinitionDao;
import com.wipro.fipc.dao.filelayout.TaskUpdateConfigDao;
import com.wipro.fipc.dao.filelayout.TbaMatchConfigDao;
import com.wipro.fipc.dao.layoutrule.LayoutConfigDao;
import com.wipro.fipc.dao.maestro.TicketCreationConfigDao;
import com.wipro.fipc.entity.common.NotificationReportConfig;
import com.wipro.fipc.entity.common.RulesDefinition;
import com.wipro.fipc.entity.filelayout.CustomResponse;
import com.wipro.fipc.entity.filelayout.ProcessControlConfig;
import com.wipro.fipc.entity.filelayout.TaskUpdateConfig;
import com.wipro.fipc.entity.tba.TbaMatchConfig;
import com.wipro.fipc.entity.layoutrule.LayoutConfig;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;
import com.wipro.fipc.entity.batch.KsdFileDetails;
import com.wipro.fipc.entity.notification.NotificationMailConfigEntity;

@Service
public class CommonUpdateServiceImpl implements CommonUpdateService {

	@Autowired
	CommonUpdateService commonUpdateService;

	@Autowired
	LayoutConfigDao layoutConfigDao;

	@Autowired
	TaskUpdateConfigDao taskUpdateConfigDao;

	@Autowired
	NotificationReportConfigRepository notificationReportConfigDao;

	@Autowired
	ProcessControlConfigDao processControlConfigDao;

	@Autowired
	TbaMatchConfigDao tbaMatchConfigDao;

	@Autowired
	RulesDefinitionDao rulesDefinitionDao;

	@Autowired
	RulesConfigDao rulesConfigDao;

	@Autowired
	TicketCreationConfigDao ticketCreationConfigDao;

	@Autowired
	KsdFileDetailsDao ksdFileDetailsDao;

	@Autowired
	NotificationMailConfigRepository notificationMailConfigDao;

	@Autowired
	KsdOutPutFileDetailsDao ksdOutPutFileDetailsDao;

	@Autowired
	OutputReportDao outputReportDao;

	@Autowired
	ReportDataCleanseDao reportDataCleanseDao;

	@Autowired
	ReportFormatDao reportFormatDao;

	@Autowired
	ReportPivotDao reportPivotDao;

	@Autowired
	ParticipantRecordIdentifierDao participantRecordIdentifierDao;

	@Autowired
	private BaseDao<NotificationMailConfigEntity> notificationdao;
	@Autowired
	private BaseDao<NotificationReportConfig> notificationReportdao;
	@Autowired
	private BaseDao<TaskUpdateConfig> taskUpdateConDao;
	@Autowired
	private BaseDao<TicketCreationConfig> ticketCreationDao;

	@Override
	public List<CustomResponse> commonMultipledelete(List<KsdFileDetails> ksdFileDetailsList) {
		// TODO Auto-generated method stub
		List<CustomResponse> responses = new ArrayList<>();

		for (KsdFileDetails ksdFileDetails : ksdFileDetailsList) {
			CustomResponse response = new CustomResponse();
			try {
				tbaMatchConfigDao.commondelete(ksdFileDetails.getUpdatedBy(), ksdFileDetails.getFileName(),
						ksdFileDetails.getProcessJobMappingId());
				processControlConfigDao.commondelete(ksdFileDetails.getUpdatedBy(),
						ksdFileDetails.getProcessJobMappingId());
				ksdFileDetailsDao.commondelete(ksdFileDetails.getUpdatedBy(), ksdFileDetails.getFileName(),
						ksdFileDetails.getProcessJobMappingId());
				layoutConfigDao.commondelete(ksdFileDetails.getUpdatedBy(), ksdFileDetails.getFileName(),
						ksdFileDetails.getProcessJobMappingId());
				rulesConfigDao.commondelete(ksdFileDetails.getUpdatedBy(), ksdFileDetails.getFileName(),
						ksdFileDetails.getProcessJobMappingId());
				rulesDefinitionDao.commondelete(ksdFileDetails.getUpdatedBy(), ksdFileDetails.getFileName(),
						ksdFileDetails.getProcessJobMappingId());
				response.setId(ksdFileDetails.getId());
				response.setMessage("Records deleted successfully");
				response.setStatus("Success");
			} catch (Exception e) {
				response.setMessage("Records not deleted");
				response.setStatus("Failed");
			}
			responses.add(response);
		}
		return responses;
	}

	@Transactional
	@Override
	public void commonSoftdeleteRuleDifinition(long processJobMappingId, String filename, String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteRuleDifinition",
				"Getting rulesDifinition adid: " + adid);
		List<RulesDefinition> rulesDefinitions = rulesDefinitionDao.getRequiredDetailsByPjmId(processJobMappingId);
		if (!CollectionUtils.isEmpty(rulesDefinitions)) {
			for (RulesDefinition rulesDefinition : rulesDefinitions) {
				if ((!StringUtils.isEmpty(rulesDefinition.getConditionJson())
						&& new String(rulesDefinition.getConditionJson()).contains(filename))
						|| (!StringUtils.isEmpty(rulesDefinition.getJson())
								&& rulesDefinition.getJson().contains(filename))
						|| (!StringUtils.isEmpty(rulesDefinition.getJson())
								&& rulesDefinition.getVarOperationJson().contains(filename))
						|| (!StringUtils.isEmpty(rulesDefinition.getJson())
								&& rulesDefinition.getFileName().contains(filename))) {
					rulesDefinitionDao.updateRequiredDetailsByRulesDififnitionsID(adid, rulesDefinition.getId());
					rulesConfigDao.commondeleteID(adid, rulesDefinition.getRulesConfig().getId());
					// rulesConfigDao.commonupdate(adid, processJobMappingId);
				}
			}
		} else
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteRuleDifinition", " rulesDifinition is empty ");
	}
	
	@Transactional
	@Override
	public void commonSoftdeleteTbaMatchConfig(long processJobMappingId, String filename, String adid) {
		List<TbaMatchConfig> tbaMatch = tbaMatchConfigDao.getRequiredDetailsByTba(processJobMappingId, filename);
		if (tbaMatch.size() > 0) {
			tbaMatchConfigDao.updateByTbaMatchConfigs(adid, processJobMappingId, filename);
		}
	}

	@Transactional
	@Override
	public void commonSoftdeleletProcessControleConfig(long processJobMappingId, String filename, String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletProcessControleConfig",
				"Getting ProcessControleConfig adid: " + adid);
		List<ProcessControlConfig> processControlConfigs = processControlConfigDao
				.getRequiredDetailsByPControl(processJobMappingId);
		if (!CollectionUtils.isEmpty(processControlConfigs)) {
			for (ProcessControlConfig pcConfig : processControlConfigs) {
				if (!StringUtils.isEmpty(pcConfig.getByteaActions())
						&& new String(pcConfig.getByteaActions()).contains(filename))
					processControlConfigDao.updateById(adid, pcConfig.getId());
				else {
					List<RulesDefinition> rulesDefinitions = rulesDefinitionDao
							.getRequiredDetailsByRuleName(processJobMappingId, pcConfig.getRuleName());
					if (!CollectionUtils.isEmpty(rulesDefinitions)) {
						for (RulesDefinition rulesDefinition : rulesDefinitions) {
							if ((!StringUtils.isEmpty(rulesDefinition.getConditionJson())
									&& new String(rulesDefinition.getConditionJson()).contains(filename))
									|| (!StringUtils.isEmpty(rulesDefinition.getJson())
											&& rulesDefinition.getJson().contains(filename))
									|| (!StringUtils.isEmpty(rulesDefinition.getJson())
											&& rulesDefinition.getVarOperationJson().contains(filename))
									|| (!StringUtils.isEmpty(rulesDefinition.getJson())
											&& rulesDefinition.getFileName().contains(filename))) {
								processControlConfigDao.updateById(adid, pcConfig.getId());
							}
						}
					} else
						LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletProcessControleConfig",
								" RulesDefinition is empty: ");
				}
			}
		} else
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletProcessControleConfig",
					" ProcessControlConfig is empty: ");
	}

	@Override
	public void commonSoftdeleletNotificationMailConfig(long processJobMappingId, String filename, String adid) {
		List<NotificationMailConfigEntity> notificationMailConfigs = notificationMailConfigDao
				.editfileName(processJobMappingId, filename);
		LoggerUtil.log(this.getClass(), Level.INFO, "NotificationMailConfig",
				"Getting NotificationMailConfig processJobMappingId: " + processJobMappingId);
		for (NotificationMailConfigEntity notificationMailConfig : notificationMailConfigs) {
			String attchmentName = notificationMailConfig.getAttachmentName();
			String finalList = commonUpdateService.getstreamData(attchmentName, filename);
			notificationMailConfig.setAttachmentName(finalList);
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletNotificationMailConfig",
					"Getting notificationMailConfig Id: " + notificationMailConfig.getId());
			notificationdao.save(notificationMailConfig);
		}
	}

	@Override
	public void commonSoftdeleletNotificationReportConfig(long processJobMappingId, String filename, String adid) {
		List<NotificationReportConfig> notificationReportConfigs = notificationReportConfigDao
				.editfileName(processJobMappingId, filename);
		LoggerUtil.log(this.getClass(), Level.INFO, "NotificationReportConfig",
				"Getting NotificationReportConfig processJobMappingId: " + processJobMappingId);
		for (NotificationReportConfig notificationReportConfig : notificationReportConfigs) {
			String attchmentName = notificationReportConfig.getReportName();
			String finalList = commonUpdateService.getstreamData(attchmentName, filename);
			notificationReportConfig.setReportName(finalList);
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletNotificationReportConfig",
					"Getting notificationReportConfig Id: " + notificationReportConfig.getId());
			notificationReportdao.save(notificationReportConfig);
		}

	}

	public void commonSoftdeleletTaskUpdateConfig(long processJobMappingId, String filename, String adid) {
		List<TaskUpdateConfig> taskUpdateConfigs = taskUpdateConfigDao.editfileName(processJobMappingId, filename);
		LoggerUtil.log(this.getClass(), Level.INFO, "TaskUpdateConfig",
				"Getting TaskUpdateConfig processJobMappingId: " + processJobMappingId);
		for (TaskUpdateConfig taskUpdateConfig : taskUpdateConfigs) {
			String attchmentName = taskUpdateConfig.getAttachment();
			String unsecuredAttachment = taskUpdateConfig.getUnsecuredAttachment();
			List<String> attchmentNameList = new ArrayList();
			List<String> unsecuredAttachmentList = new ArrayList();
			if (attchmentName != null) {
				attchmentName = attchmentName.replaceAll("\\[", "").replaceAll("\\]", "");
				attchmentNameList = Stream.of(attchmentName.split(","))
						.collect(Collectors.toCollection(ArrayList::new));
			}
			if (unsecuredAttachment != null) {
				unsecuredAttachment = unsecuredAttachment.replaceAll("\\[", "").replaceAll("\\]", "");
				unsecuredAttachmentList = Stream.of(unsecuredAttachment.split(","))
						.collect(Collectors.toCollection(ArrayList::new));
			}

			LoggerUtil.log(this.getClass(), Level.INFO, "getstreamData", "Remove  filename: " + filename);
			if (attchmentName != null || unsecuredAttachment != null) {
				if (attchmentName.contains(filename) || unsecuredAttachment.contains(filename)) {
					attchmentNameList.remove(filename);
					unsecuredAttachmentList.remove(filename);
				}
			}
			String attchmentNameLists = String.join(",", attchmentNameList);
			String unsecuredAttachmentLists = String.join(",", unsecuredAttachmentList);
			String attachment = attchmentNameLists.toString();
			String unsecuredAttachments = unsecuredAttachmentLists.toString();
			taskUpdateConfig.setAttachment(attachment);
			taskUpdateConfig.setUnsecuredAttachment(unsecuredAttachments);
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletTaskUpdateConfig",
					"Getting taskUpdateConfig finalList: " + attachment + unsecuredAttachments);
			taskUpdateConDao.save(taskUpdateConfig);
		}
	}

	@Override
	public void commonSoftdeleletTicketCreationConfig(long processJobMappingId, String filename, String adid) {
		List<TicketCreationConfig> ticketCreationConfigs = ticketCreationConfigDao.editfileName(processJobMappingId,
				filename);
		LoggerUtil.log(this.getClass(), Level.INFO, "TicketCreationConfig",
				"Getting TicketCreationConfig processJobMappingId: " + processJobMappingId);
		for (TicketCreationConfig ticketCreationConfig : ticketCreationConfigs) {
			String attchmentName = ticketCreationConfig.getAttachment();
			String unsecuredAttachment = ticketCreationConfig.getUnsecuredAttachment();

			attchmentName = attchmentName.replaceAll("\\[", "").replaceAll("\\]", "");
			unsecuredAttachment = unsecuredAttachment.replaceAll("\\[", "").replaceAll("\\]", "");
			List<String> attchmentNameList = Stream.of(attchmentName.split(","))
					.collect(Collectors.toCollection(ArrayList::new));
			List<String> unsecuredAttachmentList = Stream.of(unsecuredAttachment.split(","))
					.collect(Collectors.toCollection(ArrayList::new));

			LoggerUtil.log(this.getClass(), Level.INFO, "getstreamData", "Remove  filename: " + filename);
			if (attchmentName != null || unsecuredAttachment != null) {
				if (attchmentName.contains(filename) || unsecuredAttachment.contains(filename)) {
					attchmentNameList.remove(filename);
					unsecuredAttachmentList.remove(filename);
				}
			}
			String attchmentNameLists = String.join(",", attchmentNameList);
			String unsecuredAttachmentLists = String.join(",", unsecuredAttachmentList);
			String attachment = attchmentNameLists.toString();
			String unsecuredAttachments = unsecuredAttachmentLists.toString();
			ticketCreationConfig.setAttachment(attachment);
			ticketCreationConfig.setUnsecuredAttachment(unsecuredAttachments);
			LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleletTicketCreation",
					"Getting TicketCreationConfig finalList: " + attachment + unsecuredAttachments);
			ticketCreationDao.save(ticketCreationConfig);
		}
	}

	@Transactional
	@Override
	public void commonSoftdeleteKsdFileDetails(long processJobMappingId, String filename, String adid) {

		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteKsdFileDetails",
				"Getting KsdFileDetails adid: " + adid);
		List<KsdFileDetails> ksdFileDetails = ksdFileDetailsDao.getRequiredDetailsByksdFileDetails(processJobMappingId,
				filename);
		if (ksdFileDetails.size() > 0) {
			ksdFileDetailsDao.commondelete(adid, filename, processJobMappingId);
		}

	}

	@Transactional
	@Override
	public void commonSoftdeleteLayOutConfig(long processJobMappingId, String filename, String adid) {

		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteRuleDifinition",
				"Getting rulesDifinition adid: " + adid);
		List<LayoutConfig> layoutConfigs = layoutConfigDao.getRequiredDetailsByLayoutConfig(processJobMappingId,
				filename);
		if (layoutConfigs.size() > 0) {
			layoutConfigDao.commondelete(adid, filename, processJobMappingId);

		}

	}

	@Transactional
	@Override
	public boolean commonSoftdeleteksdoutput(long processJobMappingId, String file_name, String adid) {

		participantRecordIdentifierDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "participantRecordIdentifierDao",
				"Get participantRecordIdentifierDao of filename: " + file_name + adid);
		ksdOutPutFileDetailsDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "ksdOutPutFileDetailsDao",
				"Get ksdOutPutFileDetailsDao of filename: " + file_name + adid);
		outputReportDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "outputReportDao",
				" Get outputReportDao of filename: " + file_name + adid);
		reportFormatDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "reportFormatDao",
				"Get reportFormatDao of filename: " + file_name + adid);
		reportDataCleanseDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "reportDataCleanseDao",
				"Get reportDataCleanseDao of filename: " + file_name + adid);
		reportPivotDao.commondeleteksdoutput(file_name, processJobMappingId, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "reportPivotDao",
				"Get reportPivotDao of filename: " + file_name + adid);
		return true;
	}

	@Override
	public String getstreamData(String attchmentName, String filename) {
		attchmentName = attchmentName.replaceAll("\\[", "").replaceAll("\\]", "");
		List<String> list = Stream.of(attchmentName.split(",")).collect(Collectors.toCollection(ArrayList::new));
		filename = '"' + filename + '"';
		LoggerUtil.log(this.getClass(), Level.INFO, "getstreamData", "Remove  filename: " + filename);
		list = list.stream().map(String::trim).collect(Collectors.toList());
		if (list.contains(filename)) {
			list.remove(filename);
		}
		String finalList = list.toString();
		return finalList;

	}

}
