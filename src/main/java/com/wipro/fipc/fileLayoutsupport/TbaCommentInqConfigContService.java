package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.tba.TbaCommentInqConfig;

@Service
public class TbaCommentInqConfigContService {

	@Autowired
	GenericDao<TbaCommentInqConfig> genericDao;

	protected static final String TBA_COMMENT_INQ_CONFIG = "TBA_COMMENT_INQ_CONFIG";
	protected static final String SCHEMA = "tba";


	public List<TbaCommentInqConfig> findByColumn(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn",
				"TbaInquiryJsonKey of loadEntityLazy    {}: " + columnValue);

		return genericDao.findByColumn(TbaCommentInqConfig.class, SCHEMA, TBA_COMMENT_INQ_CONFIG, columnName,
				columnValue);
	}

}
