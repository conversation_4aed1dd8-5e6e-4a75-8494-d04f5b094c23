package com.wipro.fipc.fileLayoutsupport;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.entity.filelayout.CustomResponse;
import com.wipro.fipc.entity.batch.KsdFileDetails;

@Service
public class CommonContService {

	@Autowired
	CommonUpdateService commonUpdateService;
      
	protected static final String COPY_JOB_PFC = "copyJobPFC";
	protected static final String COMMON_SOFTDELETE_NOTIFICATION_MAIL_CONFIG = "commonSoftdeleletNotificationMailConfig";
	protected static final String COMMON_SOFTDELETE_NOTIFICATION_REPORT_CONFIG = "commonSoftdeleteNotificationReportConfig";
	protected static final String COMMON_SOFTDELETE_TASK_UPDATE_CONFIG = "commonSoftdeleteTaskUpdateConfig";
	protected static final String COMMON_SOFTDELETE_TICKET_CREATION_CONFIG = "commonSoftdeleletTicketCreationConfig";
	protected static final String COMMON_SOFTDELETE_TBA_MATCH_CONFIG = "commonSoftdeleteTbaMatchConfig";
	protected static final String COMMON_SOFTDELETE_PROCESS_CONTROL_CONFIG = "commonSoftdeleteProcessControlConfig";
	protected static final String COMMON_SOFTDELETE_RULE_DEFINITION_CONFIG = "commonSoftdeleteRuleDifinition";
	protected static final String COMMON_SOFTDELETE_KSD_OUTPUT = "commonSoftdeleteksdoutput";
	protected static final String DELETE_BY_FILE_TYPE = "deleteByFileType";
 		

		


	public Boolean commonsoftdeleteFileLayoutInputReport(long process_job_mapping_id, String file_name, String adid) {
		commonUpdateService.commonSoftdeleletNotificationMailConfig(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_MAIL_CONFIG,
				"Get commonSoftdeleletNotificationMailConfig of filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_MAIL_CONFIG,
				"Get commonSoftdeleletNotificationMailConfig of process_job_mapping_id: " + process_job_mapping_id);
		commonUpdateService.commonSoftdeleletNotificationReportConfig(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_REPORT_CONFIG,
				"Get commonSoftdeleletNotificationReportConfig of filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_NOTIFICATION_REPORT_CONFIG,
				"Get commonSoftdeleletNotificationReportConfig of process_job_mapping_id: " + process_job_mapping_id);
		commonUpdateService.commonSoftdeleletTaskUpdateConfig(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TASK_UPDATE_CONFIG,
				"Get commonSoftdeleletTaskUpdateConfig  filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TASK_UPDATE_CONFIG,
				"Get commonSoftdeleletTaskUpdateConfig process_job_mapping_id: " + process_job_mapping_id);
		commonUpdateService.commonSoftdeleletTicketCreationConfig(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TICKET_CREATION_CONFIG,
				"Get commonSoftdeleletTicketCreationConfig filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TICKET_CREATION_CONFIG,
				"Get commonSoftdeleletTicketCreationConfig process_job_mapping_id: " + process_job_mapping_id);
		commonUpdateService.commonSoftdeleteTbaMatchConfig(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TBA_MATCH_CONFIG,
				"Get commonSoftdeleteTbaMatchConfig filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_TBA_MATCH_CONFIG,
				"Get commonSoftdeleteTbaMatchConfig process_job_mapping_id: " + process_job_mapping_id);
		commonUpdateService.commonSoftdeleletProcessControleConfig(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_PROCESS_CONTROL_CONFIG,
				"Get commonSoftdeleletProcessControleConfig filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_PROCESS_CONTROL_CONFIG,
				"Get commonSoftdeleletProcessControleConfig process_job_mapping_id: " + process_job_mapping_id);
		commonUpdateService.commonSoftdeleteRuleDifinition(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_RULE_DEFINITION_CONFIG,
				"Get commonSoftdeleteRuleDifinition filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_RULE_DEFINITION_CONFIG,
				"Get commonSoftdeleteRuleDifinition process_job_mapping_id: " + process_job_mapping_id);
		commonUpdateService.commonSoftdeleteksdoutput(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_KSD_OUTPUT,
				"Get commonSoftdeleteksdoutput filename: " + file_name);
		LoggerUtil.log(this.getClass(), Level.INFO, COMMON_SOFTDELETE_KSD_OUTPUT,
				"Get commonSoftdeleteksdoutput process_job_mapping_id: " + process_job_mapping_id);
		// New Changes here
		commonUpdateService.commonSoftdeleteKsdFileDetails(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteKsdFileDetails",
				"Get commonSoftdeleteksdoutput filename and processjobmappingId: " + file_name
						+ process_job_mapping_id);
		commonUpdateService.commonSoftdeleteLayOutConfig(process_job_mapping_id, file_name, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "commonSoftdeleteLayOutConfig",
				"Get commonSoftdeleteksdoutput filename and processjobmappingId: " + file_name
						+ process_job_mapping_id);
		return true;

	}

	public List<CustomResponse> commonMultipledelete(List<KsdFileDetails> ksdFileDetailsList) {
		List<CustomResponse> value = new ArrayList<>();
		try {

			LoggerUtil.log(this.getClass(), Level.INFO, "commonMultipledelete",
					"  FileLayout = : " + ksdFileDetailsList);
			value = commonUpdateService.commonMultipledelete(ksdFileDetailsList);

		} catch (Exception e) {
		}
		return value;

	}

}
