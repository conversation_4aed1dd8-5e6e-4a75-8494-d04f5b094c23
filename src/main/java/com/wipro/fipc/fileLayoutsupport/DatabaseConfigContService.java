package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.filelayout.DatabaseConfigDao;
import com.wipro.fipc.entity.filelayout.DatabaseConfig;

@Service
public class DatabaseConfigContService {

	@Autowired
	DatabaseConfigDao databaseConfigDao;

	protected static final String DATABASE_CONFIG = "database_config";
	protected static final String SCHEMA = "common";

	public List<DatabaseConfig> getDatabaseConfig(String pjmid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDatabaseConfig", "Getting DatabaseConfig pjmid: " + pjmid);

		return databaseConfigDao.getDatabaseConfig(<PERSON>.parseLong(pjmid));
	}

}
