package com.wipro.fipc.fileLayoutsupport;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.filelayout.ProcessControlConfigDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.filelayout.ProcessControlConfig;

@Service
public class ProcessControlConfigContService {

	@Autowired
	GenericDao<ProcessControlConfig> genericDao;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	@Autowired
	ProcessControlConfigDao processControlConfigDao;

	protected static final String PROCESS_CONTROL_CONFIG = "PROCESS_CONTROL_CONFIG";
	protected static final String TBA_SCHEMA = "tba";

	public boolean saveAllEntities(List<ProcessControlConfig> entityList) {
		boolean retVal = false;
		try {
			LoggerUtil.log(this.getClass(), Level.ERROR, "saveAllEntities", "entityList - ", entityList);
			processControlConfigDao.saveAll(entityList);
			retVal = true;
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "saveAllEntities", "Exception Occurred - ", e);
		}
		return retVal;
	}


	public List<ProcessControlConfig> findByMultiColumnCondition(List<String> columnNames,
			List<String> columnConditions, List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"findByMultiColumnCondition from columnConditionParams : " + columnConditionParams);

		return genericDao.findByMultiColumnCondition(ProcessControlConfig.class, TBA_SCHEMA, PROCESS_CONTROL_CONFIG,
				columnConditionParams);
	}

	public List<ProcessControlConfig> findByColumn(String columnName, String columnValue) {

		List<ProcessControlConfig> processconfig = genericDao.findByColumn(ProcessControlConfig.class, TBA_SCHEMA,
				PROCESS_CONTROL_CONFIG, columnName, columnValue);

		List<ProcessControlConfig> processconfigList = new ArrayList<>();
		String filedName = null;
		for (ProcessControlConfig process : processconfig) {
			filedName = process.getFieldName();
			if (filedName.contains("Filler") || filedName.contains("FILLER")) {
				process.setFieldName("");
				processconfigList.add(process);
			} else {
				processconfigList.add(process);
			}
		}

		return processconfigList;
	}

}
