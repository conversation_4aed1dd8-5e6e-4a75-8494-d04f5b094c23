package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.tba.EventInquiryConfig;

@Service
public class TbaEventInquiryConfigContService {
	@Autowired
	GenericDao<EventInquiryConfig> genericDao;

	protected static final String EVENT_INQUIRY_CONFIG = "event_inquiry_config";
	protected static final String SCHEMA = "tba";
 // pass panel id as a parameter
	public List<EventInquiryConfig> findByColumn( String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn", "TbaInquiryConfig     {}: " + columnValue);

		return genericDao.findByColumn(EventInquiryConfig.class, SCHEMA, EVENT_INQUIRY_CONFIG, columnName, columnValue);
	}

}
