package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.common.TemplateReportLayOutDao;
import com.wipro.fipc.entity.TemplateReportLayOut;

@Service
public class TemplateReportLayOutContService {

	
	@Autowired
	TemplateReportLayOutDao templateReportLayOutDao;


	public List<TemplateReportLayOut> getDatas(String clientId, String templateReportName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getData",
				"Getting TemplateReportLayOut value of clientId: " + clientId);
		LoggerUtil.log(this.getClass(), Level.INFO, "getData",
				"Getting TemplateReportLayOut value of templateReportName: " + templateReportName);
		return templateReportLayOutDao.getListOfData(clientId, templateReportName);

	}

}
