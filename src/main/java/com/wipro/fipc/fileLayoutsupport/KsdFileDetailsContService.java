package com.wipro.fipc.fileLayoutsupport;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.KsdFileDetailsDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.batch.KsdFileDetails;

@Service
public class KsdFileDetailsContService {

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;
	@Autowired
	GenericDao<KsdFileDetails> genericDao;

	@Autowired
	EntityManager entityManager;
	
	@Autowired
	KsdFileDetailsDao ksdFileDetailsDao;
	
	protected static final String KSD_OUTPUT_FILE_DETAILS = "KSD_OUTPUT_FILE_DETAILS";
	protected static final String OUT_SCHEMA = "layout_rule";
	protected static final String KSD_MASTER_CONFIG = "KSD_FILE_DETAILS";
	protected static final String KSD_SCHEMA = "emails_scheduler";
	public static final String GETREQUIREDDETAILSBY = "get Required Details By";
	public static final String PLUS = "PLUS";
	public static final String PRCNT = "PRCNT";
	public static final String AMPNT = "AMPNT";


	public List<KsdFileDetails> findByColumn(String columnName, String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn",
				"Getting KsdFileDetails columnValues: " + columnValue);

		return genericDao.findByColumn(KsdFileDetails.class, KSD_SCHEMA, KSD_MASTER_CONFIG, columnName, columnValue);
	}


	public KsdFileDetails create(KsdFileDetails entity) {
		return ksdFileDetailsDao.save(entity);
	}

	public List<KsdFileDetails> findByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {

		for (int j = 0; j < columnValues.size(); j++) {
			String columnValue = columnValues.get(j);
			String remove1 = PLUS;
			String remove2 = PRCNT;
			String remove3 = AMPNT;
			if (columnValue.contains(remove1))
				columnValue = columnValue.replaceAll(PLUS, "+");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of PLUS columnValue: " + columnValue);

			if (columnValue.contains(remove2))
				columnValue = columnValue.replaceAll(PRCNT, "%");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of PRCNT columnValue: " + columnValue);

			if (columnValue.contains(remove3))
				columnValue = columnValue.replaceAll(AMPNT, "&");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of AMPNT columnValue: " + columnValue);

			columnValues.set(j, columnValue);
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of columnValue: " + columnValue);

		}
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);

		List<KsdFileDetails> findByMultiColumnCondition = findByMultiColumnCondition(KSD_SCHEMA, KSD_MASTER_CONFIG,
				columnConditionParams);
		return findByMultiColumnCondition;
	}
	

	public List<KsdOutPutFileDetails> findByMultiColumnConditionNew(List<String> columnNames,
			List<String> columnConditions,
			List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition","Getting RulesConfig in  columnConditionParams: "+ columnConditionParams);
		
		return findByMultiColumnConditionNew( OUT_SCHEMA, KSD_OUTPUT_FILE_DETAILS,columnConditionParams);
	}

	public boolean saveAllEntities(List<KsdFileDetails> entityList) {
		boolean retVal = false;
		try {
			ksdFileDetailsDao.saveAll(entityList);
			retVal = true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return retVal;
	}

	public List<KsdFileDetails> getSingleKsdFileDetails(long process_job_mapping_id) {
		List<KsdFileDetails> ksdFileDetails = ksdFileDetailsDao.getSingleKsdFileDetails(process_job_mapping_id,
				PageRequest.of(0, 1, Sort.by("id").descending()));
		for (KsdFileDetails config : ksdFileDetails) {
			config.setProcessJobMapping(null);
		}
		return ksdFileDetails;
	}

	public List<KsdFileDetails> newFindByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {

		for (int j = 0; j < columnValues.size(); j++) {
			String columnValue = columnValues.get(j);
			String remove1 = PLUS;
			String remove2 = PRCNT;
			String remove3 = AMPNT;
			if (columnValue.contains(remove1))
				columnValue = columnValue.replaceAll(PLUS, "+");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of PLUS columnValue: " + columnValue);

			if (columnValue.contains(remove2))
				columnValue = columnValue.replaceAll(PRCNT, "%");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of PRCNT columnValue: " + columnValue);

			if (columnValue.contains(remove3))
				columnValue = columnValue.replaceAll(AMPNT, "&");
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of AMPNT columnValue: " + columnValue);

			columnValues.set(j, columnValue);
			LoggerUtil.log(this.getClass(), Level.INFO, GETREQUIREDDETAILSBY,
					"After Replace of columnValue: " + columnValue);

		}
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);


		List<KsdFileDetails> originList = findByMultiColumnCondition(KSD_SCHEMA, KSD_MASTER_CONFIG,
				columnConditionParams);
		for (KsdFileDetails config : originList) {
			config.setProcessJobMapping(null);
		}

		return originList;

	}


	public List<KsdFileDetails> newFindByColumn(@PathVariable(value = "columnName") String columnName,
			@PathVariable(value = "columnValue") String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn",
				"Getting KsdFileDetails columnValues: " + columnValue);

		List<KsdFileDetails> originList = genericDao.findByColumn(KsdFileDetails.class, KSD_SCHEMA, KSD_MASTER_CONFIG,
				columnName, columnValue);
		for (KsdFileDetails config : originList) {
			config.setProcessJobMapping(null);
		}

		return originList;
	}

	public List<KsdFileDetails> findByMultiColumnCondition(String schema, String table_name,
			List<ColumnConditionParam> columnConditionParamList) {
		Query query = null;
		List<KsdFileDetails> resultList = null;
		List<String> colName = new ArrayList<>();
		List<String> colCondition = new ArrayList<>();
		List<String> columnValues = new ArrayList<>();
		System.out.println("In method");
		try {
			
			for (int i = 0; i < columnConditionParamList.size(); i++) {
				colName.add((columnConditionParamList.get(i)).getColName());
				colCondition.add((columnConditionParamList.get(i)).getColCondion());
				columnValues.add((columnConditionParamList.get(i)).getColValue());
			}
			StringBuffer strBuffer = new StringBuffer(
					"SELECT em.* FROM " + schema + "." + table_name + " as em  WHERE ");

			for (int i = 0; i < colCondition.size(); i++) {
				if (colCondition.get(i).equals("ntlk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("lk")) {
					String lower = columnValues.get(i).toLowerCase();
					strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
							+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
				} else if (colCondition.get(i).equals("is")) {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + ""
							+ columnValues.get(i) + "");
				}

				else {
					strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + "'"
							+ columnValues.get(i) + "'");
				}

				if (i != (colCondition.size() - 1)) {
					strBuffer.append(" and ");
				}
			}
			String sqlQuery = String.valueOf(strBuffer);
			System.out.println("SQL QUERY IS" + sqlQuery);
			query = entityManager.createNativeQuery(sqlQuery, KsdFileDetails.class);
			System.out.println("response 1");
			resultList = query.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("Respoinse list is" + resultList);
		return resultList;
	}
	
	public List<KsdOutPutFileDetails> findByMultiColumnConditionNew(String schema, String table_name,
			List<ColumnConditionParam> columnConditionParamList) {
		Query query = null;
		List<String> colName = new ArrayList<>();
		List<String> colCondition = new ArrayList<>();
		List<String> columnValues = new ArrayList<>();
		for (int i = 0; i < columnConditionParamList.size(); i++) {
			colName.add((columnConditionParamList.get(i)).getColName());
			colCondition.add((columnConditionParamList.get(i)).getColCondion());
			columnValues.add((columnConditionParamList.get(i)).getColValue());
		}
		StringBuffer strBuffer = new StringBuffer("SELECT em.* FROM " + schema + "." + table_name + " as em  WHERE ");

		for (int i = 0; i < colCondition.size(); i++) {
			if (colCondition.get(i).equals("ntlk")) {
				String lower = columnValues.get(i).toLowerCase();
				strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
						+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
			} else if (colCondition.get(i).equals("lk")) {
				String lower = columnValues.get(i).toLowerCase();
				strBuffer.append("lower" + "( " + "em." + colName.get(i) + ")" + " "
						+ dbServiceData.getOperator(colCondition.get(i)) + "'" + "%" + lower + "%" + "'");
			} else if (colCondition.get(i).equals("is")) {
				strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + ""
						+ columnValues.get(i) + "");
			}

			else {
				strBuffer.append("em." + colName.get(i) + " " + dbServiceData.getOperator(colCondition.get(i)) + "'"
						+ columnValues.get(i) + "'");
			}

			if (i != (colCondition.size() - 1)) {
				strBuffer.append(" and ");
			}
		}
		String sqlQuery = String.valueOf(strBuffer);
		query = entityManager.createNativeQuery(sqlQuery, KsdOutPutFileDetails.class);
		return query.getResultList();
	}

}
