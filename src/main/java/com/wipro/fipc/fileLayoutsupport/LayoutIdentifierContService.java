package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wipro.fipc.dao.filelayout.LayoutIdentifierDao;
import com.wipro.fipc.entity.filelayout.LayoutIdentifier;

@Service
public class LayoutIdentifierContService {

	@Autowired
	LayoutIdentifierDao layoutIdentifierDao;

	protected static final String LAYOUT_IDENTIFIER = "LAYOUT_IDENTIFIER";
	protected static final String LAYOUT_SCHEMA = "layout_rule";

	public List<LayoutIdentifier> list1() {
		List<LayoutIdentifier> findAll = layoutIdentifierDao.findAll();
		return findAll;
	}

}
