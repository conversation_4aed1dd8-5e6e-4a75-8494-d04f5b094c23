package com.wipro.fipc.fileLayoutsupport;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.DBServiceData;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.entity.ColumnConditionParam;
import com.wipro.fipc.entity.filelayout.OutputReport;

@Service
public class OutputReportContrService1 {

	@Autowired
	GenericDao<OutputReport> genericDao;

	@Autowired
	@Qualifier("dbServiceData")
	private DBServiceData dbServiceData;

	protected static final String OUTPUT_REPORT = "OUTPUT_REPORT";
	protected static final String OUT_SCHEMA = "layout_rule";



	public List<OutputReport> findByMultiColumnCondition(List<String> columnNames, List<String> columnConditions,
			List<String> columnValues) {
		List<ColumnConditionParam> columnConditionParams = dbServiceData.getMultiConditionValue(columnNames,
				columnConditions, columnValues);
		LoggerUtil.log(this.getClass(), Level.INFO, "findByMultiColumnCondition",
				"Getting RulesConfig in  columnConditionParams: " + columnConditionParams);

		return genericDao.findByMultiColumnCondition(OutputReport.class, OUT_SCHEMA, OUTPUT_REPORT,
				columnConditionParams);
	}

}
