package com.wipro.fipc.controller;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.pojo.tba.TbaEventInquiryConfigDto;
import com.wipro.fipc.service.IFileLayoutService;
import com.wipro.fipc.service.TbaEventInquiryConfigService;
import com.wipro.fipc.service.TbaUpdateConfigService;
import com.wipro.fipc.utils.CustomBeanUtils;

@RestController
@RequestMapping("/eventInquiryConfig")
public class TbaEventInquiryConfigController {

	@Autowired
	TbaEventInquiryConfigService tbaEventInquiryConfigService;
	
	@Autowired
	IFileLayoutService layoutService;

	@Autowired
	private TbaUpdateConfigService tbaUpdateConfigService;
	
	@Autowired
	private CustomBeanUtils customBeanUtils;

	@PostMapping(value = "/createEventInquiry")
	public ResponseEntity<String> saveTbaEventInquiryConfig(@RequestBody List<TbaEventInquiryConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveTbaEventInquiryConfig",
				"TbaEventInquiryConfigController , saveTbaEventInquiryConfig started on : " + System.currentTimeMillis());
		String response;
		try {
			response = tbaEventInquiryConfigService.createTbaEventInquiryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (IllegalAccessException | InvocationTargetException | URISyntaxException | IOException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "saveTbaEventInquiryConfig", "Unable to save due to {} \n" + e.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@PostMapping(value = "/updateEventInquiry")
	public ResponseEntity<String> updateTbaEventInquiryConfig(@RequestBody List<TbaEventInquiryConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createtbaEventinquiryconfig",
				"TbaEventInquiryConfigController , createtbaEventinquiryconfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaEventInquiryConfigService.updateTbaEventInquiryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.ACCEPTED);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "createtbaEventinquiryconfig",
					"Unable to udpate due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@GetMapping(value = "/getAllTbaEventUpdateConfig")
	public ResponseEntity<String> getAllTbaEventUpdateConfig() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllTbaUpdateConfig",
				"TbaEventInquiryConfigController , getAllTbaUpdateConfig started on : " + System.currentTimeMillis());
		return new ResponseEntity<>(tbaEventInquiryConfigService.getAllTbaEventInquiryConfig(), HttpStatus.OK);
	}
	
	@GetMapping(value = "/getTbaEventInquiryConfig")
	public Object getTbaEventInquiryConfig(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEventInquiryConfig",
				"TbaInquiryConfigController , getTbaEventInquiryConfig started on : " + System.currentTimeMillis());
		try {
			return tbaEventInquiryConfigService.getTbaEventInquiryConfig("process_job_mapping_id", pjmId);
		} catch (IOException |IllegalAccessException | InvocationTargetException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaEventInquiryConfig",
					"Exception : "+e.getMessage());
			return e.getMessage();
		}
	}
	
	@GetMapping(value = "/getTbaEventUpdateProcess")
	public ResponseEntity<String> getTbaEventUpdateProcess(@RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEventUpdateProcess",
				"TbaInquiryConfigController , getTbaEventUpdateProcess started on : " + System.currentTimeMillis());
		try {
			boolean check = clientCode.contains("_") ? true : false;
			int clientId = customBeanUtils.checkForClientCode(clientCode);
			return new ResponseEntity<>(tbaEventInquiryConfigService.getTbaEventUpdateProcess(clientId,check), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method",
					"Unable to get getTbaEventUpdateProcess due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@GetMapping(value = "/getTbaEventInquiryJsonKey")
	public ResponseEntity<String> getTbaEventInquiryJsonKey(@RequestParam String parNm) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEventInquiryJsonKey",
				"TbaEventInquiryConfigController , getTbaEventInquiryJsonKey started on : " + System.currentTimeMillis());
		return new ResponseEntity<>(tbaEventInquiryConfigService.getTbaEventInquiryJsonKey(parNm), HttpStatus.OK);
	}
	
	@GetMapping("/getAllEventRecordIdentifier")
	public String getAllRecordIdentifier() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllRecordIdentifier",
				"TbaEventInquiryConfigController , getAllRecordIdentifier started on : " + System.currentTimeMillis());
		return layoutService.getAllRecordIdentifier();
	}

	@PostMapping(value = "/deleteEventInquiryEventsnew")
	public ResponseEntity<String> deleteInquiryEventConfig(@RequestBody List<CommonDeleteDTO> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteInquiryEventConfig",
				"TbaEventInquiryConfigController , deleteInquiryEventConfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaEventInquiryConfigService.deleteInquiryEventConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	 
	@GetMapping(value = "/getEventInquiryMetaData")
	public ResponseEntity<String> getEventInquiryMetaData(@RequestParam int panelId, @RequestParam String clientCode, @RequestParam Integer activityId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventInquiryMetaData",
				"TbaEventInquiryConfigController , getEventInquiryMetaData started on : " + System.currentTimeMillis());
		int clientId = customBeanUtils.checkForClientCode(clientCode);
		return new ResponseEntity<>(tbaUpdateConfigService.getTbaUpdateMetadata(panelId, clientId, activityId),
				HttpStatus.OK);

	}


}
