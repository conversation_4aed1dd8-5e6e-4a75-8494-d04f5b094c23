package com.wipro.fipc.controller;

import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.model.RulesRequest;
import com.wipro.fipc.service.IRuleEngineBusinessService;

@RestController
@RequestMapping("/rule/business/")

public class RuleEngineBusinessController {

	private static final String UNABLE_TO_UPDATE_DUE_TO = "Unable to update due to {} \n";
	private static final String METHOD = "method";
	public static final String FAILED = "Failed";
	@Autowired
	IRuleEngineBusinessService businessService;

	@GetMapping(value = "/getall/pjmId/{pjmId}")
	public List<RulesRequest> getByPjmId(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "getByPjmId", "inside getByPjmId Controller : " + pjmId);
		return businessService.getByPjmId(pjmId);

	}

	@GetMapping(value = "/get/ruleId/{ruleId}")
	public List<RulesRequest> getByRuleConfigId(@PathVariable(value = "ruleId") String ruleId) {
		LoggerUtil.log(getClass(), Level.INFO, "getByRuleConfigId", "inside getByRuleConfigId Controller : " + ruleId);
		return businessService.getByRuleConfigId(ruleId);

	}

	@PostMapping(value = "/create")
	public RulesRequest createBusinessRules(@RequestBody RulesRequest bizRules) {
		LoggerUtil.log(getClass(), Level.INFO, "createBusinessRules",
				"inside createBusinessRules Controller : " + bizRules.toString());
		return businessService.createBusinessRules(bizRules);
	}

	@PostMapping(value = "/update/pjmId/{pjmId}/ruleId/{ruleId}")
	public RulesRequest updateBusinessRulesByRuleId(@RequestBody RulesRequest bizRules,
			@PathVariable(value = "pjmId") String pjmId, @PathVariable(value = "ruleId") String ruleId) {
		LoggerUtil.log(getClass(), Level.INFO, "updateBusinessRulesByRuleId",
				"inside updateBusinessRulesByRuleId Controller : " + bizRules + pjmId + ruleId);
		return businessService.updateBusinessRulesByRuleId(bizRules, pjmId, ruleId);
	}

	@DeleteMapping(value = "/delete/id/{id}")
	public String deleteBusinessRules(@PathVariable(value = "id") String id) {
		LoggerUtil.log(getClass(), Level.INFO, "deleteBusinessRules", "inside deleteBusinessRules Controller : " + id);
		return businessService.deleteBusinessRule(id);
	}

	@DeleteMapping(value = "/deleteAll/pjmId/{pjmId}")
	public String deleteAllBusinessRules(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "deleteAllBusinessRules",
				"inside deleteBusinessRules Controller : " + pjmId);
		return businessService.deleteAllBusinessRule(pjmId);
	}

	@GetMapping(value = "/getall/conditions")
	public List<Object> getAllConditions() {
		LoggerUtil.log(getClass(), Level.INFO, "getAllConditions", "inside getAllConditions Controller  ");
		return businessService.getAllConditions();
	}

	@PutMapping(value = "multi/update/ruleId/{ruleId}")
	public String createMultiJsonWoutSpace(@PathVariable(value = "ruleId") String ruleId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createMultiJsonWoutSpace",
				"RuleEngineBusinessController , createMultiJsonWoutSpace started on : " + System.currentTimeMillis());
		String[] listPjmId = ruleId.split(",");
		HashMap<String, String> map = new HashMap<>();
		for (String id : listPjmId) {
			try {
				String response = businessService.updateJsonWoutSpace(id);
				if (response != null && response.contains(FAILED)) {
					map.put(id, FAILED);
				} else {
					map.put(id, "Success");
				}
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "generateDrl()",
						"BusinessServiceServiceImpl-->createMultiJsonWoutSpace()--Exception: " + e.getMessage());
				map.put(id, FAILED);
			}
		}
		return map.toString();
	}

	@PutMapping(value = "multi/updateFileName/ruleId/{ruleId}")
	public String generateFileName(@PathVariable(value = "ruleId") String ruleId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "generateFileName",
				"RuleEngineBusinessController , generateFileName started on : " + System.currentTimeMillis());
		String[] listPjmId = ruleId.split(",");
		HashMap<String, String> map = new HashMap<>();
		for (String id : listPjmId) {
			try {
				String response = businessService.updateFileName(id);
				if (response != null && response.contains(FAILED)) {
					map.put(id, FAILED);
				} else {
					map.put(id, "Success");
				}
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.ERROR, "generateDrl()",
						"BusinessServiceServiceImpl-->createMultiJsonWoutSpace()--Exception: " + e.getMessage());
				map.put(id, FAILED);
			}
		}
		return map.toString();
	}

	@GetMapping(value = "/get/pjmId/{pjmId}")
	public List<Object> getAllData(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "getAllData", "inside getByFileName Controller : " + pjmId);
		return businessService.getAllData(pjmId);
	}

	@PostMapping(value = "/deleteupdateevents")
	public ResponseEntity<String> deleteUpdateEventConfig(@RequestBody String entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteUpdateEventConfig",
				"RuleEngineBusinessController , deleteUpdateEventConfig started on : " + System.currentTimeMillis());
		try {
			String response = businessService.deleteRuleConfig(entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, METHOD, UNABLE_TO_UPDATE_DUE_TO + exception.getMessage());
			return new ResponseEntity<>("api failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/get/fileName/{fileName}/pjmId/{pjmId}")
	public List<Object> getByFileName(@PathVariable(value = "fileName") String fileName,
			@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "getByFileName",
				"inside getByFileName Controller : " + fileName + pjmId);
		return businessService.getByFileName(fileName, pjmId);
	}

	@GetMapping(value = "/get/{pjmId}")
	public List<Object> getByFileNameLatest(@RequestParam String fileName,
			@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "getByFileName",
				"inside getByFileNameLatest Controller : " + fileName + pjmId);
		return businessService.getByFileName(fileName, pjmId);
	}

	@GetMapping(value = "/getall")
	public List<RulesRequest> getByPjmIdLatest(@RequestParam(value = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "getByPjmId", "inside getByPjmIdLatest Controller : " + pjmId);
		return businessService.getByPjmId(pjmId);

	}

	@GetMapping(value = "/get")
	public List<Object> getAllDataLatest(@RequestParam(name = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "getAllDataLatest", "inside getAllDataLatest Controller : " + pjmId);
		return businessService.getAllData(pjmId);
	}

	@GetMapping(value = "/deleteNewRule")
	public ResponseEntity<String> deleteRuleLatest(@RequestParam(name = "ruleName") String ruleName,
			@RequestParam(name = "pjmId") String pjmId, @RequestHeader String appName,
			@RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteRuleLatest",
				"RuleEngineBusinessController , deleteRuleLatest started on : " + System.currentTimeMillis());
		try {
			String response = businessService.deleteRuleLatest(ruleName, pjmId, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, METHOD, UNABLE_TO_UPDATE_DUE_TO + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/createRule")
	public RulesRequest createBusinessRulesLatest(@RequestBody RulesRequest bizRules, @RequestHeader String appName,
			@RequestHeader String sessionToken) {
		LoggerUtil.log(getClass(), Level.INFO, "createBusinessRules", "inside createBusinessRulesLatest Controller ");
		return businessService.createBusinessRulesData(bizRules, appName, sessionToken);
	}

	@PostMapping(value = "/updateRule")
	public RulesRequest updateBusinessRulesByRuleIdLatest(@RequestBody RulesRequest bizRules,
			@RequestParam(value = "pjmId") String pjmId, @RequestParam(value = "ruleId") String ruleId,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(getClass(), Level.INFO, "updateBusinessRulesByRuleIdLatest",
				"inside updateBusinessRulesByRuleIdLatest Controller : " + pjmId + ruleId);
		return businessService.updateBusinessRulesByRuleIdData(bizRules, pjmId, ruleId, appName, sessionToken);
	}
}