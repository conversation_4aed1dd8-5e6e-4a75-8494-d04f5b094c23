
package com.wipro.fipc.controller;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.JsonObject;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.layoutrule.LayoutConfigDao;
import com.wipro.fipc.entity.batch.KsdFileDetails;
import com.wipro.fipc.entity.filelayout.ValidationType;
import com.wipro.fipc.entity.layoutrule.LayoutConfig;
import com.wipro.fipc.model.LayoutRequest;
import com.wipro.fipc.model.ReportSheet;
import com.wipro.fipc.service.IFileLayoutService;

@RestController
@RequestMapping("/layout")

public class FileLayoutController {

	@Autowired
	IFileLayoutService layoutService;

	@Autowired
	LayoutConfigDao ovjLayoutConfigDao;

	@Autowired
	private GenericDao<ValidationType> genericDao;

	protected static final String RULES_VALIDATION_TYPE = "RULES_VALIDATION_TYPE";
	protected static final String LAYOUT_SCHEMA = "layout_rule";

	@GetMapping("/test")
	public void data(@RequestParam String processJobMappingId, @RequestParam String fileName) {
		try {
			long a = 243044;

			Optional<LayoutConfig> findById = ovjLayoutConfigDao.findById(a);
			

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@PostMapping("/downloadFile") 
	public void getFile(@RequestParam String processJobMappingId, @RequestParam String fileName,
			HttpServletResponse response) throws IOException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getFile",
				"FileLayoutController , getFile started on " + System.currentTimeMillis());
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-Disposition", "attachment; filename=LayoutTemplate.xlsx");
		LoggerUtil.log(getClass(), Level.INFO, "GetFile", "Controller FileName: " + fileName);
		Workbook workbook = layoutService.getLayoutFile(processJobMappingId, fileName);
		try {
			workbook.write(response.getOutputStream());
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getFile",
					"Unable to download LayoutTemplate due to {} \n" + ex.getMessage());
		} finally {
			if (null != workbook)
				workbook.close();
		}
	}

	@PostMapping("/create") 
	public String createLayout(@RequestBody LayoutRequest request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createLayout",
				"FileLayoutController , createLayout started on " + System.currentTimeMillis());
		return layoutService.createLayoutdata(request);

	}

	@PostMapping("/createNew") 
	public String createLayoutNew(@RequestParam String action, @RequestBody LayoutRequest request,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createLayoutNew",
				"FileLayoutController , createLayoutNew started on " + System.currentTimeMillis());
		try {
			return layoutService.createLayoutdataNew(request, appName, sessionToken, action);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "createLayoutdataNew", "Exception" + e.getMessage());
		}
		return null;
	}

	@PostMapping("/update")
	public String updateLayout(@RequestBody LayoutRequest request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateLayout",
				"FileLayoutController , updateLayout started on " + System.currentTimeMillis());
		return layoutService.updateLayoutData(request);
	}

	@PostMapping("/deleteFile/{pjmId}/{fileName}/{adid}")
	public String deleteFile(@PathVariable("pjmId") String pjmId, @PathVariable("fileName") String fileName,
			@PathVariable("adid") String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteFile",
				"FileLayoutController , deleteFile started on " + System.currentTimeMillis());
		return layoutService.deleteFiles(pjmId, fileName, adid);
	}

	@PostMapping("/deleteFile/{fileName}")
	public String deleteFileNew(@RequestParam String pjmId, @PathVariable("fileName") String fileName,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteFileNew",
				"FileLayoutController , deleteFileNew started on " + System.currentTimeMillis());
		return layoutService.deleteFilesNew(pjmId, fileName, appName, sessionToken);
	}

	// Need to remove this method
	@GetMapping("/getall/{pjmId}/{fileName}") 
	public String getLayoutRecords(@PathVariable("pjmId") String pjmId, @PathVariable("fileName") String fileName) {
		LoggerUtil.log(getClass(), Level.INFO, "getall", "FileName Controller:" + fileName);
		return layoutService.getLayoutDetails(pjmId, fileName);
	}

	@GetMapping("/getall/{fileName}") 
	public String getLayoutRecordsLatest(@RequestParam String pjmId, @PathVariable("fileName") String fileName) {
		LoggerUtil.log(getClass(), Level.INFO, "getLayoutRecordsLatest", "FileName Controller:" + fileName);
		return layoutService.getLayoutDetails(pjmId, fileName);
	}

	@GetMapping("/getmffieldnamesbyfieldtype/{processJobMappingId}/{fieldType}") 
	public String getMfFieldNamesByFieldType(@PathVariable("processJobMappingId") String processJobMappingId,
			@PathVariable("fieldType") String fieldType) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getMfFieldNamesByFieldType",
				"FileLayoutController , getMfFieldNamesByFieldType started on " + System.currentTimeMillis());
		return layoutService.getMfFieldNamesByFieldType(processJobMappingId, fieldType);
	}

	// Need to remove this method
	@GetMapping("/getfilenames/pjmId/{pjmId}") 
	public String getFileNames(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getFile",
				"FileLayoutController , getFile started on " + System.currentTimeMillis());
		return layoutService.getFileNames(pjmId);
	}

	@GetMapping("/getfilenames/pjmId") 
	public String getFileNamesLatest(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getFileNamesLatest",
				"FileLayoutController , getFileNamesLatest started on " + System.currentTimeMillis());
		return layoutService.getFileNames(pjmId);
	}

	@GetMapping("/getnames/{pjmId}/{fileType}") 
	public String getNames(@PathVariable(value = "pjmId") String pjmId,
			@PathVariable(value = "fileType") String fileType) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getNames",
				"FileLayoutController , getNames started on " + System.currentTimeMillis());
		return layoutService.getNames(pjmId, fileType);

	}

	@GetMapping("/getallfilenames/pjmId/{pjmId}") 
	public String getAllFileNames(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllFileNames",
				"FileLayoutController , getAllFileNames started on " + System.currentTimeMillis());
		return layoutService.getAllFileNames(pjmId);
	}

	// Need to remove this method
	@GetMapping("/getRecIds/{pjmId}/{fileName}") 
	public String getuniqueRecIds(@PathVariable(value = "pjmId") String pjmId,
			@PathVariable(value = "fileName") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getuniqueRecIds",
				"FileLayoutController , getuniqueRecIds started on " + System.currentTimeMillis());
		return layoutService.getRecIds(pjmId, fileName);
	}

	@GetMapping("/getRecIds/{fileName}") 
	public String getuniqueRecIdsLatest(@RequestParam String pjmId, @PathVariable(value = "fileName") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getuniqueRecIdsLatest",
				"FileLayoutController , getuniqueRecIdsLatest started on " + System.currentTimeMillis());
		return layoutService.getRecIds(pjmId, fileName);
	}

	@GetMapping("/getAllRecordIdentifier") 
	public String getAllRecordIdentifier() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllRecordIdentifier",
				"FileLayoutController , getAllRecordIdentifier started on " + System.currentTimeMillis());
		return layoutService.getAllRecordIdentifier();
	}

	@GetMapping("/getlayoutdetails/pjmId/{pjmId}") 
	public String getLayoutDetails(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getLayoutDetails",
				"FileLayoutController , getLayoutDetails started on " + System.currentTimeMillis());
		return layoutService.getLayoutDetails(pjmId);
	}

	// Need to remove this method
	@PostMapping("/downloadTemplate/{pjmId}/{fileName}") 
	public void getTemplate(@PathVariable(value = "pjmId") String pjmId,
			@PathVariable(value = "fileName") String fileName, HttpServletResponse response) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-Disposition", "attachment; filename=LayoutTemplate.xlsx");
		LoggerUtil.log(getClass(), Level.INFO, "GetFile", "Controller FileName: " + fileName);
		Workbook workbook = layoutService.getLayoutFile(pjmId, fileName);
		try {
			workbook.write(response.getOutputStream());
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplate",
					"Unable to download LayoutTemplate due to {} \n" + ex.getMessage());
		} finally {
			if (null != workbook)
				workbook.close();
		}
	}

	@PostMapping("/downloadTemplate/{fileName}") 
	public void getTemplateVaptNew(@RequestParam String pjmId, @PathVariable(value = "fileName") String fileName,
			HttpServletResponse response) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-Disposition", "attachment; filename=LayoutTemplate.xlsx");
		LoggerUtil.log(getClass(), Level.INFO, "getTemplateVaptNew GetFile", "Controller FileName: " + fileName);
		Workbook workbook = layoutService.getLayoutFile(pjmId, fileName);
		try {
			workbook.write(response.getOutputStream());
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplate",
					"Unable to download LayoutTemplate due to {} \n" + ex.getMessage());
		} finally {
			if (null != workbook)
				workbook.close();
		}
	}
	// july 10 req
	// Input Report Multiple Sheets

	@PostMapping("/createmultisheet")
	public String createMultiSheet(@RequestBody ReportSheet request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createMultiSheet",
				"FileLayoutController , createMultiSheet started on " + System.currentTimeMillis());
		return layoutService.createInputReport(request);
	}

	@PostMapping("/createmultisheetNew")
	public String createMultiSheetNew(@RequestParam String action, @RequestBody ReportSheet request,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createMultiSheetNew",
				"FileName Controller RequestData:" + request);
		try {
			return layoutService.createInputReportNew(request, appName, sessionToken, action);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "createInputReportNew", "Exception" + e.getMessage());
		}
		return null;

	}

	@PostMapping("/updatemultisheet")
	public String updateMultiSheet(@RequestBody ReportSheet request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateMultiSheet",
				"FileLayoutController , updateMultiSheet started on " + System.currentTimeMillis());
		return layoutService.updateInputReport(request);
	}

	// get api Multi sheets
	// Need to remove this method

	@GetMapping("/getMultiSheet/{pjmId}/{fileName}") 
	public String getMultiReport(@PathVariable(value = "pjmId") String pjmId,
			@PathVariable(value = "fileName") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getMultiReport",
				"FileLayoutController , getMultiReport started on " + System.currentTimeMillis());
		return layoutService.getAllMultiReport(pjmId, fileName);
	}

	@GetMapping("/getMultiSheet/{fileName}")
	public String getMultiReportLatest(@RequestParam String pjmId, @PathVariable(value = "fileName") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getMultiReportLatest",
				"FileLayoutController , getMultiReportLatest started on " + System.currentTimeMillis());
		return layoutService.getAllMultiReport(pjmId, fileName);
	}

	// Need to remove this method
	@GetMapping("/getdatabaseconfigmasterdata/{pjmId}")
	public String getDataBaseConfigMasterData(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDataBaseConfigMasterData",
				"FileLayoutController , getDataBaseConfigMasterData started on " + System.currentTimeMillis());
		return layoutService.getDataBaseConfigMasterData(pjmId);
	}

	@GetMapping("/getdatabaseconfigmasterdata")
	public String getDataBaseConfigMasterDataLatest(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDataBaseConfigMasterDataLatest",
				"FileLayoutController , getDataBaseConfigMasterDataLatest started on " + System.currentTimeMillis());
		return layoutService.getDataBaseConfigMasterData(pjmId);
	}

	@PostMapping("/deletefiles")
	public ResponseEntity<String> delFiles(List<KsdFileDetails> ksdFileDetailsList) {
		LoggerUtil.log(this.getClass(), Level.INFO, "delFiles",
				"FileLayoutController , delFiles started on " + System.currentTimeMillis());
		try {
			String response = layoutService.deleteFilesData(ksdFileDetailsList);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "delFiles",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/getlatestksdfiledetailsrecord/{pjmId}") 
	public String getLatestKsdFileDetailsRecord(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getLatestKsdFileDetailsRecord",
				"FileLayoutController , getLatestKsdFileDetailsRecord started on " + System.currentTimeMillis());
		return layoutService.getLatestKsdFileDetailsByPjmId(pjmId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "/rule/validationtype/{column_name}/{column_value}")
	public List<ValidationType> findByColumn(@PathVariable String columnName, @PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "findByColumn",
				"Getting ValidationType columnValues: " + columnValue);
		return genericDao.findByColumn(ValidationType.class, LAYOUT_SCHEMA, RULES_VALIDATION_TYPE, columnName,
				columnValue);
	}
	
	@PostMapping("/downloadTrustCodeMappingTemplate")
	public void getTrustCodeMappingTemplate(HttpServletResponse response) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-Disposition", "attachment; filename=TrustCodeMappingTemplate.xlsx");
		LoggerUtil.log(getClass(), Level.INFO, "getTrustCodeMappingTemplate",
				"getTrustCodeMappingTemplate method started.");
		Workbook workbook = layoutService.getTrustCodeMappingTemplate();
		try {
			workbook.write(response.getOutputStream());
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTrustCodeMappingTemplate",
					"Unable to download TrustCodeMappingTemplate due to {} \n" + ex.getMessage());
		} finally {
			if (null != workbook)
				workbook.close();
		}
	}

	@PostMapping("/updateTrustCodeMappingReport")
	public String updateTrustCodeMappingReport(@RequestParam("file") MultipartFile file, @RequestParam String clientCode) {
		LoggerUtil.log(getClass(), Level.INFO, "updateTrustCodeMappingReport",
				"updateTrustCodeMappingReport method started.");
		String response = "";
		try {
			response = layoutService.parseTrustCodeMappingReport(file, clientCode);
			return response;
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateTrustCodeMappingReport",
					"Unable to parse report due to {} \n", ex);
			JsonObject obj = new JsonObject();
			obj.addProperty(HolmesAppConstants.TRUST_CODE_MAPPING_RESPONSE, "");
			obj.addProperty(HolmesAppConstants.ERROR, ex.getMessage());
			response = obj.toString();
		}
		return response;
	}

	@GetMapping("/getTrustCodeMapping")
	public String getTrustCodeMappingValues(@RequestParam String clientCode, @RequestParam String clientName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTrustCodeMappingValues",
				"FileLayoutController , getTrustCodeMappingValues started.");
		return layoutService.getTrustCodeMappingValues(clientCode, clientName);
	}
}