package com.wipro.fipc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.model.Generatekey;
import com.wipro.fipc.service.IGenerateKeyService;

@RestController
@RequestMapping("/generatekey")

public class GenerateKeyController {

	@Autowired
	IGenerateKeyService generateKeyserivce;

	@PostMapping("/encryptvalue")
	public String encryptData(@RequestBody Generatekey entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "encryptData",
				"GenerateKeyController , encryptData started on " + System.currentTimeMillis());
		return generateKeyserivce.encryptValue(entity);
	}

	@PostMapping("/decryptvalue")
	public String decryptData(@RequestBody Generatekey entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "decryptData",
				"GenerateKeyController , decryptData started on : " + System.currentTimeMillis());
		return generateKeyserivce.decrptValue(entity);
	}
}
