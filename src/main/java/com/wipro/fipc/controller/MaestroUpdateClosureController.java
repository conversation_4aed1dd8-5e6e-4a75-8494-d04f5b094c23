package com.wipro.fipc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.service.MaestroUpdateClosureService;

@RestController
@RequestMapping("/maestro")

public class MaestroUpdateClosureController {

	@Autowired
	MaestroUpdateClosureService maestroUpdateClosureService;

	@GetMapping(value = "/{columnName}/{columnValue}")
	public String getMaestroTicket(@PathVariable String columnName, @PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getMaestroTicket",
				"columnName " + columnName + " columnValue " + columnValue);
		return maestroUpdateClosureService.getMaestroTicket(columnName, columnValue);
	}

	@PostMapping(value = "/createupdate")
	public String create(@RequestBody String maestroRequest) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addMaestroTicket",
				"Reached TaskUpdateConfig Service " + maestroRequest);
		return maestroUpdateClosureService.createMaestroTicket(maestroRequest);
	}

}
