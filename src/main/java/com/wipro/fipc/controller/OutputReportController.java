package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.entity.filelayout.KsdOutPutFileDetails;
import com.wipro.fipc.entity.filelayout.OutputReport;
import com.wipro.fipc.entity.filelayout.ParticipantRecordIdentifier;
import com.wipro.fipc.model.WriteOutputReportDto;
import com.wipro.fipc.service.IOutputReportService;

@RestController
@RequestMapping("/outputreport")

public class OutputReportController {

	public static final String ERROR = "Error";

	@Autowired
	IOutputReportService outputReportService;

	@Autowired
	Gson gson;

	// old
	@PostMapping(value = "/saveoutputreportdata")
	public ResponseEntity<String> saveKsdOutPutFileDetail(@RequestBody WriteOutputReportDto entity,
			@RequestParam(value = "pjmId", required = false) String pjmId,
			@RequestParam(value = "fileName", required = false) String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveKsdOutPutFileDetail",
				"OutputReportController , saveKsdOutPutFileDetail started on : " + System.currentTimeMillis());
		try {
			String response = outputReportService.saveInputReportAndOutputReportData(pjmId, fileName, entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "Records are not saved. Contact system administrator");
			exception.printStackTrace();
			LoggerUtil.log(getClass(), Level.ERROR, "saveKsdOutPutFileDetails",
					"Unable to save due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	// old
	@PostMapping(value = "/updateksdoutput")
	public ResponseEntity<String> updateKsdOutPutFileDetail(@RequestBody WriteOutputReportDto entity,
			@RequestParam(value = "pjmId", required = false) String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateKsdOutPutFileDetail",
				"OutputReportController , updateKsdOutPutFileDetail started on : " + System.currentTimeMillis());
		try {
			String response = outputReportService.updateKsdOutPutFileDetails(entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateKsdOutPutFileDetails",
					"Unable to update KsdOutPutFileDetails due to {} \n" + exception.getMessage());
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "Records are not updated. Contact system administrator");
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/updateoutputreports")
	public ResponseEntity<String> updateOutPutReportDetails(@RequestBody List<OutputReport> entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateOutPutReportDetails",
				"OutputReportController , updateOutPutReportDetails started on : " + System.currentTimeMillis());
		try {
			String response = outputReportService.updateOutPutReportDetails(entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateOutPutReportDetails",
					"Unable to update OutPutReportDetails due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("Not updated", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/updateparticipantrecordidentifiers")
	public ResponseEntity<String> updateParticipantRecordIdentifierDetails(
			@RequestBody List<ParticipantRecordIdentifier> entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateParticipantRecordIdentifierDetails",
				"OutputReportController , updateParticipantRecordIdentifierDetails started on : "
						+ System.currentTimeMillis());
		try {
			String response = outputReportService.updateParticipantRecordIdentifierDetails(entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateParticipantRecordIdentifierDetails",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("Not updated", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	// Need to be removed
	@GetMapping(value = "/getksdoutputreport/{pjmId}/{fileName}")
	public ResponseEntity<String> getKsdOutPutFileDetail(@PathVariable("pjmId") String pjmId,
			@PathVariable("fileName") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdOutPutFileDetail",
				"OutputReportController , getKsdOutPutFileDetail started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(outputReportService.getKsdOutPutFileDetails(pjmId, fileName), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getKsdOutPutFileDetails()",
					"Unable to get KsdOutPutFileDetails due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getksdoutputreport/{fileName}")
	public ResponseEntity<String> getKsdOutPutFileDetails(@PathVariable("fileName") String fileName,
			@RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdOutPutFileDetails",
				"OutputReportController , getKsdOutPutFileDetails started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(outputReportService.getKsdOutPutFileDetails(pjmId, fileName), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getKsdOutPutFileDetails()",
					"Unable to get KsdOutPutFileDetails due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getksdoutputreportData/{pjmId}")
	public ResponseEntity<String> getKsdOutPutFileDetails(@PathVariable("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdOutPutFileDetails",
				"OutputReportController , getKsdOutPutFileDetails started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(outputReportService.getKsdOutPutFileDetails(pjmId), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getKsdOutPutFileDetails()",
					"Unable to get ksdoutputreportData due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getksdoutputreportData/{pjmId}/{sheetName}")
	public ResponseEntity<String> getKsdOutPutFileDetailsBySheetName(@PathVariable("pjmId") String pjmId,
			@PathVariable("sheetName") String sheetName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getKsdOutPutFileDetailsBySheetName",
				"OutputReportController , getKsdOutPutFileDetailsBySheetName started on : "
						+ System.currentTimeMillis());
		try {
			return new ResponseEntity<>(outputReportService.getKsdOutPutFileDetailsBySheetName(pjmId, sheetName),
					HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getKsdOutPutFileDetailsBySheetName()",
					"Unable to get ksdoutputreportData due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	// need to be removed
	@GetMapping(value = "/getinputreportData/{pjmId}/{fileName}")
	public ResponseEntity<String> getInputFileDetailByFileName(@PathVariable("pjmId") String pjmId,
			@PathVariable("fileName") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getInputFileDetailByFileName",
				"OutputReportController , getInputFileDetailByFileName started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(outputReportService.getInputFileDetailsByFileName(pjmId, fileName),
					HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getInputFileDetailsByFileName()",
					"Unable to get inputreportData due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getinputreportData/{fileName}")
	public ResponseEntity<String> getInputFileDetailsByFileName(@PathVariable("fileName") String fileName,
			@RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getInputFileDetailsByFileName",
				"OutputReportController , getInputFileDetailsByFileName started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(outputReportService.getInputFileDetailsByFileName(pjmId, fileName),
					HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getInputFileDetailsByFileName()",
					"Unable to get inputreportData due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getoutputreportData/{pjmId}/{fileName}")
	public ResponseEntity<String> getOutPutFileDetails(@PathVariable("pjmId") String pjmId,
			@PathVariable("fileName") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getOutPutFileDetails",
				"OutputReportController , getOutPutFileDetails started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(outputReportService.getOutputReport(pjmId, fileName), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getOutPutFileDetails()",
					"Unable to get records due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deleteoutputreportData/{pjmId}/{fileName}/{updatedBy}")
	public ResponseEntity<String> deleteOutPutFileDetails(@PathVariable("pjmId") long pjmId,
			@PathVariable("fileName") String fileName, @PathVariable("updatedBy") String updatedBy) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutPutFileDetails",
				"OutputReportController , deleteOutPutFileDetails started on : " + System.currentTimeMillis());
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutPutFileDetails()",
					"pjmId :" + pjmId + " fileName : " + fileName + " updatedBy : " + updatedBy);
			return new ResponseEntity<>(outputReportService.deleteOutPutFileDetails(pjmId, fileName, updatedBy),
					HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteOutPutFileDetails()",
					"Unable to delete records due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	// need to be removed
	@GetMapping(value = "/getDistinctFileNames/{pjmId}")
	public ResponseEntity<String> getDistinctKsdOutPutFileDetail(@PathVariable("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDistinctKsdOutPutFileDetail",
				"OutputReportController , getDistinctKsdOutPutFileDetail started on : " + System.currentTimeMillis());
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getDistinctKsdOutPutFileDetails()", "pjmId :" + pjmId);
			return new ResponseEntity<>(outputReportService.getDistinctFileNames(pjmId), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getDistinctKsdOutPutFileDetails()",
					"Unable to get records due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getDistinctFileNames")
	public ResponseEntity<String> getDistinctKsdOutPutFileDetails(@RequestParam("pjmId") String pjmId) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getDistinctKsdOutPutFileDetails()", "pjmId :" + pjmId);
			return new ResponseEntity<>(outputReportService.getDistinctFileNames(pjmId), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getDistinctKsdOutPutFileDetails()",
					"Unable to get records due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deleteOutputReport")
	public ResponseEntity<String> deleteOutputReports(@RequestBody KsdOutPutFileDetails entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReports",
				"OutputReportController , deleteOutputReports started on : " + System.currentTimeMillis());
		try {
			String response = outputReportService.deleteOutputReport(entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteOutputReport",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	// new post methods

	@PostMapping(value = "/saveoutputreportdataNew")
	public ResponseEntity<String> saveKsdOutPutFileDetails(@RequestBody WriteOutputReportDto entity,
			@RequestParam(value = "pjmId", required = false) String pjmId,
			@RequestParam(value = "fileName", required = false) String fileName, @RequestHeader String appName,
			@RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveKsdOutPutFileDetails",
				"OutputReportController , saveKsdOutPutFileDetails started on : " + System.currentTimeMillis());
		try {
			String response = outputReportService.saveKsdOutPutFileDetails(pjmId, fileName, entity, appName,
					sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "Records are not saved. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "saveKsdOutPutFileDetails",
					"Unable to save due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/updateksdoutputNew")
	public ResponseEntity<String> updateKsdOutPutFileDetails(@RequestBody WriteOutputReportDto  entity,
			@RequestParam(value = "pjmId", required = false) String pjmId, @RequestHeader String appName,
			@RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateKsdOutPutFileDetails",
				"OutputReportController , updateKsdOutPutFileDetails started on : " + System.currentTimeMillis());
		try {
			String response = outputReportService.updateKsdOutPutFileDetails(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateKsdOutPutFileDetails",
					"Unable to update KsdOutPutFileDetails due to {} \n" + exception.getMessage());
			JsonObject jobj = new JsonObject();
			jobj.addProperty(HolmesAppConstants.STATUS, HolmesAppConstants.FAILED);
			jobj.addProperty(HolmesAppConstants.MESSAGE, "Records are not updated. Contact system administrator");
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deleteOutputReportNew")
	public ResponseEntity<String> deleteOutputReport(@RequestBody KsdOutPutFileDetails entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteOutputReport",
				"OutputReportController , deleteOutputReport started on : " + System.currentTimeMillis());
		try {
			String response = outputReportService.deleteOutputReport(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteOutputReport",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}