package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.entity.maestro.TicketCreationConfig;
import com.wipro.fipc.model.ConfigStatusBO;
import com.wipro.fipc.model.MaestroTicketTaskConfig;
import com.wipro.fipc.model.generated.ConfigStatusApproveBO;
import com.wipro.fipc.service.MaestroTicketConfigService;

@RestController
@RequestMapping("/maestro")

public class MaestroTicketConfigController {
	@Autowired
	MaestroTicketConfigService maestroAppCreateService;

	@GetMapping(value = "/test")
	public String testMaestrotaskdetails() {
		LoggerUtil.log(this.getClass(), Level.INFO, "testMaestrotaskdetails",
				"MaestroTicketConfigController , testMaestrotaskdetails started on : " + System.currentTimeMillis());
		return "MaestroTicketConfigController Service Test Call";
	}

	@PostMapping(value = "/createupdateticketConfig")
	public boolean createMaestroAppConfig(@RequestBody List<TicketCreationConfig> ticketCreationConfig) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createMaestroAppConfig",
				"MaestroTicketConfigController , createMaestroAppConfig started on : " + System.currentTimeMillis());
		return maestroAppCreateService.createupdateMaestroTicketConfigList(ticketCreationConfig);
	}

	@PostMapping(value = "/createupdate/tickettaskconfig")
	public List<String> createUpdateTicketTaskConfig(@RequestBody MaestroTicketTaskConfig ticketCreationUpdateConfig) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createUpdateTicketTaskConfig",
				"MaestroTicketConfigController , createUpdateTicketTaskConfig started on : "
						+ System.currentTimeMillis());
		List<String> createUpdateTicketTaskResp = maestroAppCreateService
				.createUpdateMaestroTicketConfig(ticketCreationUpdateConfig);
		LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigController1",
				"createUpdateTicketTaskResp :  " + createUpdateTicketTaskResp);
		return createUpdateTicketTaskResp;
	}

	@PostMapping(value = "/newcreateupdate/tickettaskconfig")
	public List<String> newcreateUpdateTicketTaskConfig(@RequestBody MaestroTicketTaskConfig ticketCreationUpdateConfig,
			@RequestHeader String appName, @RequestHeader String sessionToken, @RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "newcreateUpdateTicketTaskConfig",
				"MaestroTicketConfigController , newcreateUpdateTicketTaskConfig started on : "
						+ System.currentTimeMillis());
		List<String> createUpdateTicketTaskResp = maestroAppCreateService
				.newcreateUpdateMaestroTicketConfig(ticketCreationUpdateConfig, appName, sessionToken, pjmId);
		return createUpdateTicketTaskResp;
	}

	@PostMapping(value = "/delete/tickettaskconfig")
	public List<String> deleteTicketTaskConfig(@RequestBody MaestroTicketTaskConfig ticketCreationUpdateConfig,
			@RequestHeader String appName, @RequestHeader String sessionToken, @RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTicketTaskConfig",
				"MaestroTicketConfigController , deleteTicketTaskConfig started on : " + System.currentTimeMillis());
		List<String> createUpdateTicketTaskResp = maestroAppCreateService
				.deleteMaestroTicketConfig(ticketCreationUpdateConfig, appName, sessionToken, pjmId);
		return createUpdateTicketTaskResp;
	}

	@PostMapping(value = "/updateConfigStatus")
	public String updateProcessFeatureConfig(@RequestBody ConfigStatusBO configStatusBO) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateProcessFeatureConfig",
				"MaestroTicketConfigController , updateProcessFeatureConfig started on : "
						+ System.currentTimeMillis());
		String updateConfigStatusResp = "";
		updateConfigStatusResp = maestroAppCreateService.updateConfigStatus(configStatusBO);
		LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigController2",
				"updateConfigStatusResp :  " + updateConfigStatusResp);
		return updateConfigStatusResp;
	}

	@PostMapping(value = "/updatemaestroConfigStatus")
	public String updateProcessFeatureConfigstatus(@RequestBody ConfigStatusBO configStatusBO,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateProcessFeatureConfigstatus",
				"MaestroTicketConfigController , updateProcessFeatureConfigstatus started on : "
						+ System.currentTimeMillis());
		String updateConfigStatusResp = "";
		updateConfigStatusResp = maestroAppCreateService.updatemaestroConfigStatus(configStatusBO, appName,
				sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigController2",
				"updateConfigStatusResp :  " + updateConfigStatusResp);
		return updateConfigStatusResp;
	}

	@PostMapping(value = "/updateApprovedConfigStatus")
	public String updateApproveProcessFeatureConfig(@RequestBody ConfigStatusApproveBO configStatusApproveBO) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateApproveProcessFeatureConfig",
				"MaestroTicketConfigController , updateApproveProcessFeatureConfig started on : "
						+ System.currentTimeMillis());
		String updateApproveConfigStatusResp = "";
		updateApproveConfigStatusResp = maestroAppCreateService.updateApproveConfigStatus(configStatusApproveBO);
		LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigController3",
				"updateApproveConfigStatusResp :  " + updateApproveConfigStatusResp);
		return updateApproveConfigStatusResp;
	}

	@PostMapping(value = "/updateApprovedmaestroConfigStatus")
	public String updateApproveProcessFeatureConfig(@RequestBody ConfigStatusApproveBO configStatusApproveBO,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateApproveProcessFeatureConfig",
				"MaestroTicketConfigController , updateApproveProcessFeatureConfig started on : "
						+ System.currentTimeMillis());
		String updateApproveConfigStatusResp = "";
		updateApproveConfigStatusResp = maestroAppCreateService.updateApprovemaestroConfigStatus(configStatusApproveBO,
				appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "MaestroTicketConfigController3",
				"updateApproveConfigStatusResp :  " + updateApproveConfigStatusResp);
		return updateApproveConfigStatusResp;
	}

	@PostMapping(value = "/deleteticketconfig/{id}")
	public String delete(@PathVariable(value = "id") Long id) {
		LoggerUtil.log(this.getClass(), Level.INFO, "delete",
				"MaestroTicketConfigController , delete started on : " + System.currentTimeMillis());
		return maestroAppCreateService.deleteByIdMaestroTicketConfig(id);
	}

	@GetMapping(value = "/getallticketconfig")
	public List<TicketCreationConfig> getAllMaestroAppConfig() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllMaestroAppConfig",
				"MaestroTicketConfigController , getAllMaestroAppConfig started on : " + System.currentTimeMillis());
		return maestroAppCreateService.getAllMaestroTicketConfig();
	}

	@GetMapping(value = "/getallmaestrotickettask")
	public MaestroTicketTaskConfig getAllTikcetTask() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllTikcetTask",
				"MaestroTicketConfigController , getAllTikcetTask started on : " + System.currentTimeMillis());
		return maestroAppCreateService.getAllMaestroTicketTask();
	}

	// to be removed
	@GetMapping(value = "getbycolumntickettask/{columnName}/{columnValue}")
	public MaestroTicketTaskConfig getByColumnTikcetTask(@PathVariable String columnName,
			@PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getByColumnTikcetTask",
				"MaestroTicketConfigController , getByColumnTikcetTask started on : " + System.currentTimeMillis());
		return maestroAppCreateService.getByColumnMaestroTikcetTask(columnName, columnValue);
	}

	@GetMapping(value = "getbycolumntickettask/process_job_mapping_id")
	public MaestroTicketTaskConfig getByColumnTikcetTask(@RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getByColumnTikcetTask",
				"MaestroTicketConfigController , getByColumnTikcetTask started on : " + System.currentTimeMillis());
		return maestroAppCreateService.getByColumnMaestroTikcetTask(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
	}

	@GetMapping(value = "getbycolumnticketconfig/{columnName}/{columnValue}")
	public List<TicketCreationConfig> getMaestroTicket(@PathVariable String columnName,
			@PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getMaestroTicket",
				"columnName " + columnName + " columnValue " + columnValue);
		return maestroAppCreateService.getByColumnMaestroTicketConfig(columnName, columnValue);
	}

}
