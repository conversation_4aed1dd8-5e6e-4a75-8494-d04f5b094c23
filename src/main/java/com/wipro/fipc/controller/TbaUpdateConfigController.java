package com.wipro.fipc.controller;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.PanelDataBean;
import com.wipro.fipc.model.TbaUpdateConfigDto;
import com.wipro.fipc.service.IFileLayoutService;
import com.wipro.fipc.service.TbaUpdateConfigService;
import com.wipro.fipc.tba.service.TbaInquiryJsonService;
import com.wipro.fipc.tba.service.TbaUpdateMetaDataService;
import com.wipro.fipc.utils.CustomBeanUtils;

@RestController
@RequestMapping("/tbaupdateconfig")

public class TbaUpdateConfigController {

	public static final String FAILED = "failed";

	@Autowired
	TbaUpdateConfigService tbaUpdateConfigService;

	@Autowired
	IFileLayoutService layoutService;

	@Autowired
	private TbaInquiryJsonService tbaInquiryJsonService;

	@Autowired
	private TbaUpdateMetaDataService tbaUpdateMetaDataService;
	
	@Autowired
	private CustomBeanUtils customBeanUtils;

	public static final String PROCESS_JOB_MAPPING_ID = "process_job_mapping_id";
	public static final String PAR_NM = "par_nm";

	@PostMapping(value = "/create")
	public ResponseEntity<String> createtbaupdateconfig(@RequestBody List<TbaUpdateConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaPendingConfig",
				"TbaUpdateConfigController , createTbaPendingConfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaUpdateConfigService.createTbaUpdateConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "createtbaupdateconfig",
					"Unable to save due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/update")
	public ResponseEntity<String> updatetbaupdateconfig(@RequestBody List<TbaUpdateConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updatetbaupdateconfig",
				"TbaUpdateConfigController , updatetbaupdateconfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaUpdateConfigService.updateTbaUpdateConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.ACCEPTED);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updatetbaupdateconfig",
					"Unable to udpate due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getAllTbaUpdateConfig")
	public ResponseEntity<String> getAllTbaUpdateConfig() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllTbaUpdateConfig",
				"TbaUpdateConfigController , getAllTbaUpdateConfig started on : " + System.currentTimeMillis());
		return new ResponseEntity<>(tbaUpdateConfigService.getAllTbaUpdateConfig(), HttpStatus.OK);
	}

	@GetMapping(value = "/getTbaUpdateConfig")
	public ResponseEntity<String> getTbaUpdateConfigLatest(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateConfig",
				"TbaUpdateConfigController-->getTbaUpdateConfigLatest()-->starts for pjmID: " + pjmId);
		return new ResponseEntity<>(tbaUpdateConfigService.getTbaUpdateConfig(PROCESS_JOB_MAPPING_ID, pjmId),
				HttpStatus.OK);
	}

	@GetMapping(value = "/getTbaUpdateProcess")
	public ResponseEntity<String> getTbaUpdateProcessLatest(@RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateProcess",
				"TbaUpdateConfigController-->getTbaUpdateProcess()-->starts for clientId: " + clientCode);
		try {
			boolean check = clientCode.contains("_") ? true : false;
			int clientId = customBeanUtils.checkForClientCode(clientCode);
			LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateProcess",
					"TbaUpdateConfigController-->getTbaUpdateProcess()-->clientId after checkForClientCode: " + clientId);
			return new ResponseEntity<>(tbaUpdateConfigService.getTbaUpdateProcess(clientId,check), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTbaUpdateProcessLatest",
					"Unable to getTbaUPL due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getTbaUpdateMetadata")
	public ResponseEntity<String> getTbaUpdateMetadataLatest(@RequestParam int panelId, String clientCode,
			Integer activityId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateMetadataLatest",
				"TbaUpdateConfigController , getTbaUpdateMetadataLatest started on : " + System.currentTimeMillis());
		int clientId = customBeanUtils.checkForClientCode(clientCode);
		return new ResponseEntity<>(tbaUpdateConfigService.getTbaUpdateMetadata(panelId, clientId, activityId),
				HttpStatus.OK);

	}

	@GetMapping(value = "/getTbaUpdateJsonKey")
	public ResponseEntity<String> getTbaUpdateJsonKeyLatest(@RequestParam String parNm) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateJsonKey",
				"TbaUpdateConfigController-->getTbaUpdateJsonKey()-->starts for parNm: " + parNm);
		return new ResponseEntity<>(tbaUpdateConfigService.getTbaUpdateJsonKey(PAR_NM, parNm), HttpStatus.OK);
	}

	@GetMapping("/getAllRecordIdentifier")
	public String getAllRecordIdentifier() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllRecordIdentifier",
				"TbaUpdateConfigController , getAllRecordIdentifier started on : " + System.currentTimeMillis());
		return layoutService.getAllRecordIdentifier();
	}

	@PostMapping(value = "/deleteupdateeventsnew")
	public ResponseEntity<String> deleteUpdateEventConfig(@RequestBody List<CommonDeleteDTO> entities,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteUpdateEventConfig",
				"TbaUpdateConfigController , deleteUpdateEventConfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaUpdateConfigService.deleteUpdateEventConfig(entities, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getTbaEditsMaster/{eventName}")
	public ResponseEntity<String> getTbaEditsMasterDetailsLatest(@RequestParam String clientCode,
			@PathVariable("eventName") String eventName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEditsMasterDetailsLatest",
				"TbaUpdateConfigController , getTbaEditsMasterDetailsLatest started on : "
						+ System.currentTimeMillis());
		try {
			return new ResponseEntity<>(tbaUpdateConfigService.getTbaEditsMasterDetails(clientCode, eventName),
					HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTbaEditsMasterDetails",
					"Unable to get due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getUpdateMetaData")
	public ResponseEntity<List<PanelDataBean>> getUpdateMetaData(@RequestParam String clientCode, @RequestParam int panelId, @RequestParam String parName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getUpdateMetaData",
				"getUpdateMetaData Method Execution Started clientCode - " + clientCode + ", panelId - " + panelId + ", parName - " + parName);

		try {
			InputStream inputStream = new FileInputStream("stv-panel.xml");
			Map<String, String> arrayAttributes = tbaInquiryJsonService.fetchArrayAttributes(inputStream, parName);

			if(!CollectionUtils.isEmpty(arrayAttributes)) {
				int clientId = customBeanUtils.checkForClientCode(clientCode);
				List<PanelDataBean> updateMetaDatas = tbaUpdateMetaDataService.getUpdateMetaDataPanelWise(clientId, panelId, arrayAttributes);
				LoggerUtil.log(this.getClass(), Level.INFO, "getUpdateMetaData", "updateMetaData List Fetched Size - " + updateMetaDatas.size());

				return new ResponseEntity<>(updateMetaDatas, HttpStatus.OK);
			} else {
				LoggerUtil.log(getClass(), Level.INFO, "getUpdateMetaData", "Getting Empty panelDatas for parName - " + parName);
				return new ResponseEntity<>(Collections.emptyList(), HttpStatus.OK);
			}
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getUpdateMetaData", "Some Exception Occurred.", exception);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
