package com.wipro.fipc.controller;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.common.beans.ApiResponse;
import com.wipro.fipc.common.beans.ResponseStatus;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.model.CustomProcessConfigurationUI;
import com.wipro.fipc.model.FileData;
import com.wipro.fipc.model.JobScheduleBean;
import com.wipro.fipc.model.ProcessConfiguration;
import com.wipro.fipc.model.UpdatePhaseNames;
import com.wipro.fipc.pojo.ClientDetailsBo;
import com.wipro.fipc.pojo.CustomBusinessOpsBO;
import com.wipro.fipc.pojo.CustomBusinessUnitBO;
import com.wipro.fipc.pojo.CustomClientBO;
import com.wipro.fipc.pojo.CustomEftJobNameBO;
import com.wipro.fipc.pojo.CustomKsdNameBO;
import com.wipro.fipc.pojo.CustomPFCClientCodeBO;
import com.wipro.fipc.pojo.CustomPJMBO;
import com.wipro.fipc.pojo.CustomPJMDto;
import com.wipro.fipc.pojo.CustomProcessDetailsBO;
import com.wipro.fipc.pojo.CustomProcessFeaturesBO;
import com.wipro.fipc.pojo.ProcessCofigReqBody;
import com.wipro.fipc.pojo.ProcessCofigResBody;
import com.wipro.fipc.service.ProcessConfigurationService;
import com.wipro.fipc.utils.CommonGetAdId;

@RestController
@RequestMapping(value = "/processconfiguration")

public class ProcessConfigurationController {

	public static final String METHOD = "method";

	@Autowired
	ProcessConfigurationService processConfigService;
	@Autowired
	CommonGetAdId commonGetAdId;

	@PostMapping("/saveprocessconfig")
	public ProcessConfiguration insertProcessConfigDetails(@RequestHeader String appName,
			@RequestHeader String sessionToken, @RequestBody CustomProcessConfigurationUI processFeatureConfigUI) {

		String adId = commonGetAdId.getADID(appName, sessionToken);

		processFeatureConfigUI.setCreatedBy(adId);

		LoggerUtil.log(getClass(), Level.INFO, "insertProcessConfigDetails",
				"inside insertProcessConfigDetails Controller : ");
		CustomPJMDto customPJMDto = null;
		customPJMDto = processConfigService.getProcessJobMappingById(processFeatureConfigUI.getProcessJobMappingId());

		if (customPJMDto != null) {
			processFeatureConfigUI.setBusinessOpsName(customPJMDto.getBusinessOpsName());
			processFeatureConfigUI.setBusinessUnitName(customPJMDto.getBusinessUnitName());
			processFeatureConfigUI.setClientCode(customPJMDto.getClientCode());
			processFeatureConfigUI.setClientName(customPJMDto.getClientName());
			processFeatureConfigUI.setEftSubject(customPJMDto.getEftSubject());
			processFeatureConfigUI.setJobName(customPJMDto.getJobName());
			processFeatureConfigUI.setKsdName(customPJMDto.getKsdName());
			processFeatureConfigUI.setProcessJobMappingId(customPJMDto.getProcessJobMappingId());
			processFeatureConfigUI.setProcessName(customPJMDto.getProcessName());
			processFeatureConfigUI.setProcessType(customPJMDto.getProcessType());

		}
		LoggerUtil.log(getClass(), Level.INFO, "insertProcessConfigDetails",
				"inside insertProcessConfigDetails Controller, received request from ProcessConfigurationUI : "
						+ processFeatureConfigUI.getProcessJobMappingId());
		try {
			return processConfigService.saveProcessConfigDetails(processFeatureConfigUI);
		} catch (IllegalAccessException | InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "saveProcessConfigDetails",
					"Exception occured while Saving All ProcessConfigDetails=>>: " + e.getMessage());
		}
		return null;
	}

	// Display call in Process config page
	@GetMapping("/getprocessconfig/{AdId}")
	public List<CustomPFCClientCodeBO> getProcessConfigDetails(@PathVariable("AdId") String adId) {
		LoggerUtil.log(getClass(), Level.INFO, "getProcessConfigDetails",
				"inside getProcessConfigDetails Controller for AdId : " + adId);
		return processConfigService.getAllProcessConfigDetails(adId);
	}

	@PostMapping("/updatephasenames/{id}")
	public String updatePhaseNames(@PathVariable("id") String id, @RequestBody UpdatePhaseNames updatePhaseNames) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside updatePhaseNames Controller, updatephasenames : " + updatePhaseNames);
		return processConfigService.updatePhaseNamesDetails(id, updatePhaseNames);

	}

	@GetMapping("/getphasenames/{id}")
	public CustomPFCClientCodeBO getPhaseNames(@PathVariable("id") String id) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "inside getPhaseNames Controller, id : " + id);
		try {
			return processConfigService.getPhaseNamesDetails(id);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPhaseNamesDetails",
					"Exception occured while getting PhaseNamesDetails : " + e.getMessage());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPhaseNamesDetails",
					"Exception occured while getting PhaseNamesDetails : " + e.getMessage());
		}
		return null;
	}

	@GetMapping("/getBusinessUnits/{alightID}")
	public List<CustomBusinessUnitBO> getBusinessUnitByAlightID(@PathVariable("alightID") String alightID) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessUnitByAlightID Controller for alightID : " + alightID);

		List<CustomBusinessUnitBO> businessUnitList = processConfigService.getBusinessUnitByAlightID(alightID);

		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessUnitByAlightID Controller before businessUnitList return : " + businessUnitList);
		return businessUnitList;
	}

	@GetMapping("/getClientDetails/{alightID}/{buID}")
	public List<CustomClientBO> getClientDetails(@PathVariable("alightID") String alightID,
			@PathVariable("buID") String buID) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getClientDetails Controller for alightID and buID : " + alightID + " " + buID);

		List<CustomClientBO> customClientList = processConfigService.getClientDetails(alightID, buID);

		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getClientDetails Controller before return customClientDetails : " + customClientList);
		return customClientList;
	}

	@GetMapping("/getBusinessOps/{alightID}/{buID}")
	public List<CustomBusinessOpsBO> getBusinessOpsDetails(@PathVariable("alightID") String alightID,
			@PathVariable("buID") String buID) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessOpsDetails Controller for alightID and buID : " + alightID + " " + buID);

		List<CustomBusinessOpsBO> customBusinessOpsList = processConfigService.getBusinessOpsDetails(alightID, buID);

		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessOpsDetails Controller before return customBusinessOpsList : "
						+ customBusinessOpsList);
		return customBusinessOpsList;
	}

	@GetMapping("/getProcess/{alightID}/{buID}/{clientID}/{buOpsID}")
	public List<CustomProcessDetailsBO> getProcessDetails(@PathVariable("alightID") String alightID,
			@PathVariable("buID") String buID, @PathVariable("clientID") String clientID,
			@PathVariable("buOpsID") String buOpsID) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getProcessDetails Controller for alightID, buID, clientID, buOpsID: " + alightID + " " + buID
						+ " " + clientID + " " + buOpsID);

		List<CustomProcessDetailsBO> processList = processConfigService.getProcessDetails(alightID, buID, clientID,
				buOpsID);
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getProcessDetails Controller before return processList : " + processList);
		return processList;
	}

	@GetMapping("/getJobs/{buID}/{clientID}/{buOpsID}/{processID}")
	public List<String> getJobDetails(@PathVariable("buID") String buID, @PathVariable("clientID") String clientID,
			@PathVariable("buOpsID") String buOpsID, @PathVariable("processID") String processID) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getProcessConfigDetails Controller for buID, clientID, buOpsID, processID: " + buID + " "
						+ clientID + " " + buOpsID + " " + processID);

		List<String> jobList = processConfigService.getJobDetails(buID, clientID, buOpsID, processID);

		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getJobDetails Controller before return jobList : " + jobList);
		return jobList;
	}

	@PostMapping("/getEft/{businessUnitID}/{clientID}/{businessOpsID}/{processID}")
	public List<String> getEFTDetails(@PathVariable("businessUnitID") String buID,
			@PathVariable("clientID") String clientID, @PathVariable("businessOpsID") String buOpsID,
			@PathVariable("processID") String processID, @RequestBody CustomEftJobNameBO customEftJobNameBO) {
		LoggerUtil.log(getClass(), Level.INFO, "getEFTDetails",
				"inside getEFTDetails method for buID, clientID, buOpsID, processID, jobName: " + buID + " " + clientID
						+ " " + buOpsID + " " + processID + " " + customEftJobNameBO.getJobName());

		return processConfigService.getEFTDetails(buID, clientID, buOpsID, processID, customEftJobNameBO);
	}

	@PostMapping("/getKsd/{businessUnitID}/{clientID}/{businessOpsID}/{processID}")
	public List<CustomKsdNameBO> getKsdNamesAndIds(@PathVariable("businessUnitID") String buID,
			@PathVariable("clientID") String clientID, @PathVariable("businessOpsID") String buOpsID,
			@PathVariable("processID") String processID, @RequestBody CustomEftJobNameBO customEftJobNameBO) {
		LoggerUtil.log(getClass(), Level.INFO, "getEFTDetails",
				"inside getKsdNamesAndIds method for buID, clientID, buOpsID, processID, jobName, eftsubject: " + buID
						+ " " + clientID + " " + buOpsID + " " + processID + " " + customEftJobNameBO.getJobName() + " "
						+ customEftJobNameBO.getEftSubject());
		return processConfigService.getKsdNamesAndIds(buID, clientID, buOpsID, processID, customEftJobNameBO);
	}

	@GetMapping("/getConfiguredKsdNames/{businessOpsID}/{ADID}")
	public Set<String> getConfiguredJobList(@PathVariable("businessOpsID") String businessOps,
			@PathVariable("ADID") String adid) {
		LoggerUtil.log(getClass(), Level.INFO, "getConfiguredJobList",
				"inside getConfiguredJobList method for businessOpsID and adid: " + businessOps + ", " + adid);

		Set<String> configuredJobList = null;
		try {
			configuredJobList = processConfigService.getConfiguredJobList(businessOps, adid);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getConfiguredJobList",
					"Exception occured while getting ConfiguredJobList: " + e.getMessage());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getConfiguredJobList",
					"Exception occured while getting ConfiguredJobList: " + e.getMessage());
		}

		LoggerUtil.log(getClass(), Level.INFO, "getConfiguredJobList",
				"inside getConfiguredJobList method before return job list : " + configuredJobList);
		return configuredJobList;

	}

	@PostMapping(value = "/getConfiguredEFT/{ADID}")
	public Set<String> getConfiguredEFTList(@RequestBody CustomEftJobNameBO customEftJobNameBO,
			@PathVariable("ADID") String adid) {
		LoggerUtil.log(getClass(), Level.INFO, "getConfiguredEFTList",
				"inside getConfiguredEFTList method for job, adid: " + customEftJobNameBO.getJobName() + ", " + adid);

		return processConfigService.getConfiguredEftDetails(customEftJobNameBO, adid);
	}

	@PostMapping("/getConfiguredKSD/{ADID}")
	public Set<CustomKsdNameBO> getConfiguredKSDList(@RequestBody CustomEftJobNameBO customEftJobNameBO,
			@PathVariable("ADID") String adid) {
		LoggerUtil.log(getClass(), Level.INFO, "getConfiguredKSDList",
				"inside getConfiguredKSDList method for job, eftsubject, adid: " + customEftJobNameBO.getJobName()
						+ ", " + customEftJobNameBO.getEftSubject() + ", " + adid);
		try {
			return processConfigService.getConfiguredKsdNamesAndIds(customEftJobNameBO, adid);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getConfiguredKsdNamesAndIds",
					"Exception occured while getting ConfiguredKsdNamesAndIds : " + e.getMessage());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getConfiguredKsdNamesAndIds",
					"Exception occured while getting ConfiguredKsdNamesAndIds : " + e.getMessage());
		}
		return null;
	}

	@GetMapping("/getKsdNames/{buid}/{clientId}")
	public List<String> getKsdNameList(@PathVariable("buid") String buid, @PathVariable("clientId") String clientId) {
		LoggerUtil.log(getClass(), Level.INFO, "getKsdNameList",
				"inside getKsdNameList method for businessOpsID and adid: " + buid + ", " + clientId);

		List<String> ksdNameList = processConfigService.getKsdNameList(buid, clientId);

		LoggerUtil.log(getClass(), Level.INFO, "getKsdNameList",
				"inside getKsdNameList method before return job list : " + ksdNameList);
		return ksdNameList;

	}

	@PostMapping("/getProcessJobMapping")
	public CustomPJMBO getProcessJobMapping(@RequestBody CustomKsdNameBO customKsdNameBO) {
		LoggerUtil.log(getClass(), Level.INFO, "getProcessJobMapping",
				"inside getProcessJobMapping method for KsdName: " + customKsdNameBO.getKsdName());

		CustomPJMBO customProcessJobMapping = processConfigService.getProcessJobMapping(customKsdNameBO);

		LoggerUtil.log(getClass(), Level.INFO, "getProcessJobMapping",
				"inside getProcessJobMapping method before return processJobMapping obj : " + customProcessJobMapping);
		return customProcessJobMapping;

	}

	@PostMapping("/getProcessFeatureConfig")
	public CustomProcessFeaturesBO getProcessfeatureConfig(@RequestBody CustomKsdNameBO customKsdNameBO) {
		LoggerUtil.log(getClass(), Level.INFO, "getProcessfeatureConfig",
				"inside getProcessJobMapping method for KsdName: " + customKsdNameBO.getKsdName());

		CustomProcessFeaturesBO customProcessDetailsBO = processConfigService.getProcessfeatureConfig(customKsdNameBO);

		LoggerUtil.log(getClass(), Level.INFO, "getProcessfeatureConfig",
				"inside getProcessfeatureConfig method before return customProcessDetailsBO obj : "
						+ customProcessDetailsBO);
		return customProcessDetailsBO;

	}

	// Need to delete after vapt testing
	@GetMapping(value = "/getPFConfigDetails/{adId}/{roleOfUser}")
	public List<CustomPFCClientCodeBO> getPFCDetails(@PathVariable("adId") String adId,
			@PathVariable("roleOfUser") String roleOfUser) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getPFCDetails Controller for adId and roleOfUser : " + adId + " " + roleOfUser);

		try {
			return processConfigService.getPFCDetailsForRole(adId, roleOfUser);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPFCDetailsForRole",
					"Exception occur while get PFCDetailsForRole : " + e.getCause());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPFCDetailsForRole",
					"Exception occur while get PFCDetailsForRole : " + e.getCause());
		}
		return null;
	}

//Need to delete after vapt testing
	@GetMapping("/getClientOfManager/{adid}/{role}/{buid}")
	public List<ClientDetailsBo> getClientOfManager(@PathVariable("adid") String adid,
			@PathVariable("role") String role, @PathVariable("buid") Long buid) {
		LoggerUtil.log(getClass(), Level.INFO, "getClientOfManager",
				"inside getClientOfManager method for adid and buid: " + adid + ", " + role + ", " + buid);

		List<ClientDetailsBo> clientDetailsBoList = processConfigService.getClientOfManager(adid, role, buid);

		LoggerUtil.log(getClass(), Level.INFO, "getClientOfManager",
				"inside getClientOfManager method before return job list : " + clientDetailsBoList);
		return clientDetailsBoList;

	}

	@GetMapping(value = "/updatePhaseNameById/{id}")
	public String updatePhaseNamesById(@PathVariable String id) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "inside updatePhaseNamesById Controller for id : ");
		Map<String, String> response = new HashedMap();
		Arrays.asList(id.split(",")).parallelStream().forEach(item -> {
			try {
				processConfigService.updatePhaseNameById(item, new ArrayList<FileData>());
				response.put(item, "success");
			} catch (JsonProcessingException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			} catch (IllegalAccessException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			} catch (IOException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			} catch (InvocationTargetException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			}
		});

		return response.toString();
	}

	@PostMapping(value = "/updatePhaseNameById/{id}")
	public String updatePhaseNamesById2(@PathVariable String id, @RequestBody List<FileData> files) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD, "inside updatePhaseNamesById Controller for id : ");
		Map<String, String> response = new HashedMap();
		Arrays.asList(id.split(",")).parallelStream().forEach(item -> {

			try {
				processConfigService.updatePhaseNameById(item, files);
				response.put(item, "success");
			} catch (JsonProcessingException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			} catch (IllegalAccessException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			} catch (IOException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			} catch (InvocationTargetException e) {
				response.put(item, "failure");
				LoggerUtil.log(getClass(), Level.ERROR, METHOD, e.toString());
			}
		});

		return response.toString();
	}

	// Display call in Process config page
	@PostMapping("/getfilteredprocessconfig")
	public ProcessCofigResBody getFilteredProcessconfig(@RequestParam String ldapId,
			@RequestBody ProcessCofigReqBody processCofigReqBody) {
		processCofigReqBody.setAdid(ldapId);
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getProcessConfigDetails Controller for AdId : " + processCofigReqBody.getAdid());
		try {
			return processConfigService.getAllFilterdProcessConfigDetails(processCofigReqBody);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllFilterdProcessConfigDetails",
					"Exception occur while get AllFilterdProcessConfigDetails : " + e.getCause());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllFilterdProcessConfigDetails",
					"Exception occur while get AllFilterdProcessConfigDetails : " + e.getCause());
		}
		return null;
	}

	@GetMapping("/getProcessJobMappingById/{pjmid}")
	public CustomPJMDto getProcessJobMappingById(@PathVariable("pjmid") Long pjmid) {
		LoggerUtil.log(getClass(), Level.INFO, "getProcessJobMappingById",
				"inside getProcessJobMappingById method for pjmid: " + pjmid);
		CustomPJMDto customPJMDto = processConfigService.getProcessJobMappingById(pjmid);

		LoggerUtil.log(getClass(), Level.INFO, "getProcessJobMapping",
				"inside getProcessJobMapping method before return processJobMapping obj : " + customPJMDto);
		return customPJMDto;

	}

//Get APIs
	@GetMapping("/getBusinessUnits")
	public List<CustomBusinessUnitBO> getBusinessUnitByLdapId(@RequestParam String ldapId) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessUnitByLdapId Controller for alightID : " + ldapId);

		List<CustomBusinessUnitBO> businessUnitList = processConfigService.getBusinessUnitByAlightID(ldapId);

		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessUnitByLdapId Controller before businessUnitList return : " + businessUnitList);
		return businessUnitList;
	}

	@GetMapping("/getConfiguredKsdNames")
	public Set<String> getConfiguredJobListBopsAdid(@RequestParam String businessOpsId, @RequestParam String ldapId) {
		LoggerUtil.log(getClass(), Level.INFO, "getConfiguredJobListBopsAdid",
				"inside getConfiguredJobListBopsAdid method for businessOpsID and ldap: " + businessOpsId + ", "
						+ ldapId);
		Set<String> configuredJobList = null;
		try {
			configuredJobList = processConfigService.getConfiguredJobList(businessOpsId, ldapId);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getConfiguredJobList",
					"Exception occured while getting ConfiguredJobList: " + e.getMessage());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getConfiguredJobList",
					"Exception occured while getting ConfiguredJobList: " + e.getMessage());
		}

		LoggerUtil.log(getClass(), Level.INFO, "getConfiguredJobListBopsAdid",
				"inside getConfiguredJobListBopsAdid method before return job list : " + configuredJobList);
		return configuredJobList;

	}

	@GetMapping("/getKsdNames")
	public List<String> getKsdNameListBunit(@RequestParam String bUnit, @RequestParam String clientId) {
		LoggerUtil.log(getClass(), Level.INFO, "v",
				"inside getKsdNameListBunit method for businessOpsID and adid: " + bUnit + ", " + clientId);

		return processConfigService.getKsdNameList(bUnit, clientId);

	}

	@GetMapping(value = "/getPFConfigDetails")
	public List<CustomPFCClientCodeBO> getPFCDetailsByAdIdRole(@RequestParam String ldapId, @RequestParam String role) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getPFCDetailsByAdIdRole Controller for ldap and role : " + ldapId + " " + role);
		try {
			return processConfigService.getPFCDetailsForRole(ldapId, role);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPFCDetailsForRole",
					"Exception occured while getting PFCDetailsForRole: " + e.getMessage());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPFCDetailsForRole",
					"Exception occured while getting PFCDetailsForRole: " + e.getMessage());
		}
		return null;
	}

	@GetMapping("/getClientOfManager")
	public List<ClientDetailsBo> getClientOfManagerAdidRoleBuid(@RequestParam String ldapId, @RequestParam String role,
			@RequestParam Long bUnit) {
		LoggerUtil.log(getClass(), Level.INFO, "getClientOfManager",
				"inside getClientOfManagerAdidRoleBuid method for ldap,role and bunit: " + ldapId + ", " + role + ", "
						+ bUnit);

		List<ClientDetailsBo> clientDetailsBoList = processConfigService.getClientOfManager(ldapId, role, bUnit);

		LoggerUtil.log(getClass(), Level.INFO, "getClientOfManagerAdidRoleBuid",
				"inside getClientOfManagerAdidRoleBuid method before return job list : " + clientDetailsBoList.size());
		return clientDetailsBoList;

	}

	@GetMapping("/getBusinessOps")
	public List<CustomBusinessOpsBO> getBusinessOpsByLdapBuId(@RequestParam String ldapId, @RequestParam String bUnit) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessOpsByLdapBuId Controller for alightID and buID : " + ldapId + " " + bUnit);

		List<CustomBusinessOpsBO> customBusinessOpsList = processConfigService.getBusinessOpsDetails(ldapId, bUnit);

		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getBusinessOpsByLdapBuId Controller before return customBusinessOpsList : "
						+ customBusinessOpsList.size());
		return customBusinessOpsList;
	}

	@GetMapping("/getProcess")
	public List<CustomProcessDetailsBO> getProcessDetailsLdapIdBunitClientIdBops(@RequestParam String ldapId,
			@RequestParam String bUnit, @RequestParam String clientId, @RequestParam String businessOpsId) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getProcessDetailsLdapIdBunitClientIdBops Controller for alightID, buID, clientID, buOpsID: "
						+ ldapId + " " + bUnit + " " + clientId + " " + businessOpsId);

		List<CustomProcessDetailsBO> processList = processConfigService.getProcessDetails(ldapId, bUnit, clientId,
				businessOpsId);

		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getProcessDetailsLdapIdBunitClientIdBops Controller before return processList : "
						+ processList.size());
		return processList;
	}

	@GetMapping("/getJobs")
	public List<String> getJobDetailsBunitClientBopsProcess(@RequestParam String bUnit, @RequestParam String clientId,
			@RequestParam String businessOpsId, @RequestParam String processId) {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getJobDetailsBunitClientBopsProcess Controller for buID, clientID, buOpsID, processID: " + bUnit
						+ " " + clientId + " " + businessOpsId + " " + processId);

		List<String> jobList = processConfigService.getJobDetails(bUnit, clientId, businessOpsId, processId);
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside getJobDetailsBunitClientBopsProcess Controller before return jobLis: " + jobList);
		return jobList;
	}

	// Post APIs
	@PostMapping("/getEft/{bUnit}/{businessOpsId}/{processId}")
	public List<String> getEFTDetailsBunitBopsProClientId(@PathVariable("bUnit") String bUnit,
			@PathVariable("businessOpsId") String businessOpsId, @PathVariable("processId") String processId,
			@RequestParam String clientId, @RequestBody CustomEftJobNameBO customEftJobNameBO) {
		LoggerUtil.log(getClass(), Level.INFO, "getEFTDetails",
				"inside getEFTDetailsBunitBopsProClientId method for bUnit, clientId, businessOpsId, processId: "
						+ bUnit + " " + clientId + " " + businessOpsId + " " + processId);
		return processConfigService.getEFTDetails(bUnit, clientId, businessOpsId, processId, customEftJobNameBO);
	}

	@PostMapping("/getKsd/{bUnit}/{businessOpsId}/{processId}")
	public List<CustomKsdNameBO> getKsdNamesBunitBopsProClientId(@PathVariable("bUnit") String bUnit,
			@PathVariable("businessOpsId") String businessOpsId, @PathVariable("processId") String processId,
			@RequestParam String clientId, @RequestBody CustomEftJobNameBO customEftJobNameBO) {
		LoggerUtil.log(getClass(), Level.INFO, "getEFTDetails",
				"inside getKsdNamesBunitBopsProClientId method for bUnit, clientId, businessOpsId, processId: " + bUnit
						+ " " + clientId + " " + businessOpsId + " " + processId);

		return processConfigService.getKsdNamesAndIds(bUnit, clientId, businessOpsId, processId, customEftJobNameBO);

	}

	@PostMapping(value = "/scheduleJobsUpdated")
	public ApiResponse updateScheduledJobs(@RequestBody JobScheduleBean jobScheduleBean,@RequestHeader String appName,
			@RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateScheduledJobs", "Inside ActivityTracker UIController - " + jobScheduleBean);

		ApiResponse response = new ApiResponse();
		try {
			processConfigService.updateScheduleJobs(jobScheduleBean, appName, sessionToken);
			response.setStatus(ResponseStatus.SUCCESS);
			response.setData("job Updated SuccessFully");
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateScheduledJobs", "Exception Occured - ", ex);
			response.setStatus(ResponseStatus.FAILED);
			response.setErrors(Arrays.asList(ex.getMessage()));
		}

		return response;
	}
}
