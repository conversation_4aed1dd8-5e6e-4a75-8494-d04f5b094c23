package com.wipro.fipc.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.output.ByteArrayOutputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.pojo.HWSBossKSDConfigReport;
import com.wipro.fipc.service.IKSDConfigReportGenerator;

@RestController
@RequestMapping(value = "/ksdconfigreport")

public class MainKSDConfigReportGenerateController {
	@Autowired
	IKSDConfigReportGenerator ksdReportGenerator;

	@Autowired
	ObjectMapper objectMapper;

	@Autowired
	Environment env;
	private static final String DOWNLOAD_NON_BUSINESS_HWS_REPORT = "downloadNonBusinessHWSReport";

	@GetMapping(value = "/hwsreport/{date}/{time}")
	public ResponseEntity<ByteArrayResource> downloadNonBusinessHWSReport(@PathVariable(name = "date") String date,
			@PathVariable(name = "time") String time) throws Exception {
		LoggerUtil.log(this.getClass(), Level.INFO, DOWNLOAD_NON_BUSINESS_HWS_REPORT,
				"inside downloadNonBusinessHWSReport Controller for date/time: " + date + "/" + time);
		SimpleDateFormat oldFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat newFormat = new SimpleDateFormat("ddMMMM'('HH:mm')'");
		String dateForName = date + " " + time;
		Date date1 = oldFormat.parse(dateForName);
		dateForName = newFormat.format(date1);

		String fileLoadPath = env.getProperty("hws.report.load.path");
		String fileName = "HWS_KSDConfig_Report_" + dateForName + ".xlsx";
		fileLoadPath = fileLoadPath + fileName;
		LoggerUtil.log(this.getClass(), Level.INFO, DOWNLOAD_NON_BUSINESS_HWS_REPORT,
				"setting fileLoadPath>>>>>>>: " + fileLoadPath);
		List<HWSBossKSDConfigReport> hwsBusinessReportList = ksdReportGenerator.getHwsBossKsdConfigReport(date, time);

		if (!(hwsBusinessReportList != null && !hwsBusinessReportList.isEmpty())) {
			LoggerUtil.log(this.getClass(), Level.INFO, DOWNLOAD_NON_BUSINESS_HWS_REPORT,
					"No records found from downloadNonBusinessHWSReport");
			return new ResponseEntity<>(HttpStatus.NO_CONTENT);
		} else {
			LoggerUtil.log(this.getClass(), Level.INFO, DOWNLOAD_NON_BUSINESS_HWS_REPORT,
					"HWS KSDReportList records size:" + hwsBusinessReportList.size());
			try {
				ByteArrayOutputStream stream = ksdReportGenerator
						.writeAndSaveNonBusinessExcelKSDRecords(hwsBusinessReportList, fileLoadPath);
				HttpHeaders headers = new HttpHeaders();
				headers.setContentType(new MediaType("application", "force-download"));
				headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
				return new ResponseEntity<>(new ByteArrayResource(stream.toByteArray()), headers, HttpStatus.CREATED);
			} catch (Exception e) {
				LoggerUtil.log(this.getClass(), Level.INFO, DOWNLOAD_NON_BUSINESS_HWS_REPORT,
						"Caught Exception while downloading the report: " + e.getCause());
				return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
			}
		}
	}

}
