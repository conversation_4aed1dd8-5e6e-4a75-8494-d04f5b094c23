package com.wipro.fipc.controller;

import java.lang.reflect.InvocationTargetException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CustomProcessConfigurationUI;
import com.wipro.fipc.model.ModelApiResponse;
import com.wipro.fipc.model.ProcessConfiguration;
import com.wipro.fipc.service.AllBotsConfigService;
import com.wipro.fipc.utils.CommonGetAdId;

@RestController
@RequestMapping("/allconfigs")
public class AllBotsConfigController {

	@Autowired
	AllBotsConfigService allBotsConfigService;

	@Autowired
	ObjectMapper objectMapper;
	@Autowired
	CommonGetAdId commonGetAdId;

	@PostMapping("/delete")
	public ModelApiResponse softDeleteAllConfigs(@RequestParam String ldapId, @RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "method",
				"inside softDeleteAllConfigs Controller, received request for adid and processJobMappingID" + ldapId
						+ ", " + pjmId);
		try {
			return allBotsConfigService.deleteAllConfigs(ldapId, pjmId);
		} catch (NumberFormatException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "deleteAllConfigs",
					"Exception occured while deleteAllConfigs");
			throw new BusinessException(e.getMessage());
		}
	}

	@PostMapping(value = "/copyjob", consumes = "application/json")
	public ProcessConfiguration copyJobInAllConfigs(@RequestHeader String appName, @RequestHeader String sessionToken,
			@RequestBody CustomProcessConfigurationUI jobCopy) {
		LoggerUtil.log(this.getClass(), Level.INFO, "method",
				"inside copyJobInAllConfigs Controller, received request : ");
		String adId = commonGetAdId.getADID(appName, sessionToken);
		jobCopy.setCreatedBy(adId);
		try {
			return allBotsConfigService.copyJobToAllConfigsDetails(jobCopy);
		} catch (IllegalAccessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "copyJobToAllConfigsDetails",
					"Exception occured while copyJobToAllConfigsDetails");
			throw new BusinessException(e.getMessage());
		} catch (InvocationTargetException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "copyJobToAllConfigsDetails",
					"Exception occured while copyJobToAllConfigsDetails");
			throw new BusinessException(e.getMessage());
		}
	}
}
