package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.ProcessControl;
import com.wipro.fipc.model.ProcessControlAttributeResponse;
import com.wipro.fipc.model.TbaInquiryNoticeConfig;
import com.wipro.fipc.model.generated.KsdFileDetails;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.TbaUpdateConfig;
import com.wipro.fipc.service.IProcessControlService;
import com.wipro.fipc.utils.CustomBeanUtils;

@RestController
@RequestMapping("/process")

public class ProcessControlController {

	@Autowired
	private IProcessControlService pcService;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	@GetMapping("/get/Attributes/{columnName}/{columnValue}")
	public Object getApplicationMismatchAction(@PathVariable String columnName, @PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_APPLICATION_MISMATCH_ACTION,
				"ProcessController-->getApplicationMismatchAction()-->starts:");
		try {
			List<KsdFileDetails> ksdList = pcService.getApplication(columnName, columnValue);
			ProcessControlAttributeResponse res = new ProcessControlAttributeResponse();
			res.setKsdList(ksdList);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_APPLICATION_MISMATCH_ACTION,
					"ProcessController-->getApplicationMismatchAction()--ends.");
			return res;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_APPLICATION_MISMATCH_ACTION,
					"ProcessController-->getApplicationMismatchAction()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	/***
	 * Method to get all the records from process_control_config table.
	 * 
	 * @return
	 */
	@GetMapping("/get/processControlData/{columnName}/{columnValue}")
	public Object getProcessControlData(@PathVariable String columnName, @PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
				"ProcessController-->getProcessControlData()-->starts:");
		try {
			List<ProcessControl> pcList = this.pcService.getProcessControlData(columnName, columnValue);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
					"ProcessController-->getProcessControlData()--ends.");
			return pcList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
					"ProcessController-->getProcessControlData()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	@PostMapping("/get/fieldName/{columnName}/{columnValue}")
	public Object getRequiredField(@PathVariable String columnName, @PathVariable String columnValue,
			@RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
				"ProcessController-->getRequiredField()-->starts:");
		try {
			if (searchField.equals(HolmesAppConstants.TBA)) {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"----getting TBA Field----");
				List<TbaInquiryNoticeConfig> tbaConfigList = this.pcService.getTBAField(columnName, columnValue);
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"ProcessController-->getRequiredField()--ends.");
				return tbaConfigList;
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"----getting File field----");
				Object layoutConfigList = this.pcService.getLayoutConfig(columnName, columnValue, searchField);
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"ProcessController-->getRequiredField()--ends.");
				return layoutConfigList;
			}
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_REQUIRED_FIELD,
					"ProcessController-->getRequiredField()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	/***
	 * Method will update or save the process control data based on the flag send in
	 * the body.
	 * 
	 * @param processControl
	 * @return
	 */
	@PostMapping(value = "/submit")
	public Object submit(@RequestParam String action, @RequestBody List<ProcessControl> pcList,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
				"ProcessController-->submit()-->starts:");
		try {
			Object response = this.pcService.submit(action, pcList, appName, sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.SUBMIT,
					"ProcessController-->submit()--ends.");
			return response;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.SUBMIT,
					"ProcessController-->submit()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	/****
	 * Method to get the Rule values corresponding to the TBA field selected for
	 * process control.
	 * 
	 * @param tbaField
	 * @return
	 */
	@PostMapping("/get/ruleName/{columnName}/{columnValue}")
	public Object getRuleName(@PathVariable String columnName, @PathVariable String columnValue,
			@RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE,
				"ProcessController-->getRule()-->starts:");
		try {
			List<RulesConfig> rulesConfigList = this.pcService.getRuleName(columnName, columnValue, searchField);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE,
					"ProcessController-->getRule()--ends.");
			return rulesConfigList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_RULE,
					"ProcessController-->getRule()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}

	/****
	 * Method to get the Rule values corresponding to the TBA field selected for
	 * process control.
	 * 
	 * @param tbaField
	 * @return
	 */

	@PostMapping("/get/correctiveActionData/{columnName}/{columnValue}")
	public Object getCorrectiveActionData(@PathVariable String columnName, @PathVariable String columnValue,
			@RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
				"ProcessController-->getCorrectiveActionData()-->starts:");
		try {
			List<TbaUpdateConfig> rulesConfigList = this.pcService.getCorrectiveActionData(columnName, columnValue,
					searchField);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
					"ProcessController-->getCorrectiveActionData()--ends.");
			return rulesConfigList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
					"ProcessController-->getCorrectiveActionData()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}

	@PostMapping("/get/OutputReport/{columnName}/{columnValue}")
	public Object getOutputReport(@PathVariable String columnName, @PathVariable String columnValue,
			@RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_OUTPUT_REPORT,
				"ProcessController-->getOutputReport()-->starts:");

		try {
			List<String> fieldNameList = this.pcService.getOutputReport(columnName, columnValue, searchField);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_OUTPUT_REPORT,
					"ProcessController-->getOutputReport()-->ends");
			return fieldNameList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_OUTPUT_REPORT,
					"ProcessController-->getOutputReport()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	@PostMapping("/get/ConditionName/{columnName}/{columnValue}")
	public Object getConditionName(@PathVariable String columnName, @PathVariable String columnValue,
			@RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CONDITION_NAME,
				"ProcessController-->getConditionName()-->starts:");
		try {
			Object res = this.pcService.getConditionName(columnName, columnValue, searchField);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CONDITION_NAME,
					"ProcessController-->getConditionName()-->ends");
			return res;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_CONDITION_NAME,
					"ProcessController-->getConditionName()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	@GetMapping("/get/processControlDataNew")
	public Object getProcessControlDataLatest(@RequestParam(name = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
				"ProcessController-->getProcessControlDataLatest()-->starts:");
		try {
			List<ProcessControl> pcList = this.pcService
					.getProcessControlData(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
					"ProcessController-->getProcessControlDataLatest()--ends.");
			return pcList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_PROCESS_CONTROL_DATA,
					"ProcessController-->getProcessControlDataLatest()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	@PostMapping("/get/correctiveActionData")
	public Object getCorrectiveActionDataLatest(@RequestParam(name = "pjmId") String pjmId,
			@RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
				"ProcessController-->getCorrectiveActionData()-->starts:");
		try {
			List<TbaUpdateConfig> rulesConfigList = this.pcService
					.getCorrectiveActionData(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId, searchField);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
					"ProcessController-->getCorrectiveActionData()--ends.");
			return rulesConfigList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_CORRECTIVE_ACTION_DATA,
					"ProcessController-->getCorrectiveActionData()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}

	@PostMapping("/get/ConditionName")
	public Object getConditionNameLatest(@RequestParam(name = "pjmId") String pjmId, @RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CONDITION_NAME,
				"ProcessController-->getConditionNameLatest()-->starts:");
		try {
			Object res = this.pcService.getConditionName(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId, searchField);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_CONDITION_NAME,
					"ProcessController-->getConditionNameLatest()-->ends");
			return res;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_CONDITION_NAME,
					"ProcessController-->getConditionNameLatest()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	@PostMapping("/get/ruleName")
	public Object getRuleNameLatest(@RequestParam(name = "pjmId") String pjmId, @RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE,
				"ProcessController-->getRule()-->starts:");
		try {
			List<RulesConfig> rulesConfigList = this.pcService.getRuleName(HolmesAppConstants.PROCESS_JOB_MAPPING_ID,
					pjmId, searchField);
			LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_RULE,
					"ProcessController-->getRule()--ends.");
			return rulesConfigList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_RULE,
					"ProcessController-->getRule()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}

	@PostMapping("/get/fieldName")
	public Object getRequiredField(@RequestParam(name = "pjmId") String pjmId, @RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
				"ProcessController-->getRequiredField()-->starts:");
		try {
			if (searchField.equals(HolmesAppConstants.TBA)) {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"----getting TBA Field----");
				List<TbaInquiryNoticeConfig> tbaConfigList = this.pcService
						.getTBAField(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"ProcessController-->getRequiredField()--ends.");
				return tbaConfigList;
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"----getting File field----");
				Object layoutConfigList = this.pcService.getLayoutConfig(HolmesAppConstants.PROCESS_JOB_MAPPING_ID,
						pjmId, searchField);
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"ProcessController-->getRequiredField()--ends.");
				return layoutConfigList;
			}
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_REQUIRED_FIELD,
					"ProcessController-->getRequiredField()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}

	@PostMapping("/get/fieldNameForPreviousReport")
	public Object getRequiredFieldForPreviousReport(@RequestParam(name = "clientCode") String clientCode,
			@RequestBody String searchField) {
		LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
				"ProcessController-->getRequiredField()-->starts:");
		try {
			String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientCode));
			if (searchField.equals(HolmesAppConstants.TBA)) {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"----getting TBA Field----");
				List<TbaInquiryNoticeConfig> tbaConfigList = this.pcService.getTBAField(HolmesAppConstants.CLIENT_ID,
						clientId);
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"ProcessController-->getRequiredField()--ends.");
				return tbaConfigList;
			} else {
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"----getting File field----");
				Object layoutConfigList = this.pcService.getLayoutConfig(HolmesAppConstants.CLIENT_ID, clientId,
						searchField);
				LoggerUtil.log(this.getClass(), Level.INFO, HolmesAppConstants.GET_REQUIRED_FIELD,
						"ProcessController-->getRequiredField()--ends.");
				return layoutConfigList;
			}
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, HolmesAppConstants.GET_REQUIRED_FIELD,
					"ProcessController-->getRequiredField()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CON_REFUSED_MASSAGE))
				return HolmesAppConstants.DB_SERVICE_DOWN_MASSAGE;
			return e.getMessage();
		}
	}
}
