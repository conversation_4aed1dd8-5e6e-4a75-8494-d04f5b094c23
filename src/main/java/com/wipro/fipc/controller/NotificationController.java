
package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.EmailPlaceHolder;
import com.wipro.fipc.service.INotificationService;

@RestController
@RequestMapping(value = "/notification")

public class NotificationController {
	@Autowired
	INotificationService notificationService;

	public static final String PROCESS_JOB_MAPPING_ID = "process_job_mapping_id";

	// Need to remove this method
	@GetMapping(value = "/details/{columnName}/{columnValue}")
	public String getDetails(@PathVariable String columnName, @PathVariable String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDetails",
				"NotificationController , getDetails started on : " + System.currentTimeMillis());
		return notificationService.getDetails(columnName, columnValue);
	}

	@GetMapping(value = "/details")
	public String getDetailsLatest(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getDetailsLatest",
				"NotificationController , getDetailsLatest started on : " + System.currentTimeMillis());
		return notificationService.getDetails(PROCESS_JOB_MAPPING_ID, pjmId);
	}
	
	//Not in use
	@PostMapping(value = "/create")
	public String createNotification(@RequestHeader("sessionToken") String sessionToken, @RequestBody Object request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createNotification",
				"NotificationController , createNotification started on : " + System.currentTimeMillis());
		return notificationService.createUpdate(request, sessionToken);
	}
	//Not in use
	@PostMapping(value = "/update")
	public String updateNotification(@RequestHeader("sessionToken") String sessionToken, @RequestBody Object request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateNotification",
				"NotificationController , updateNotification started on : " + System.currentTimeMillis());
		return notificationService.updateNotification(request, sessionToken);
	}
	
	//Not in use
	@PostMapping(value = "/delete")
	public String deleteNotif(@RequestBody String request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteNotif",
				"NotificationController , deleteNotif started on : " + System.currentTimeMillis());
		return notificationService.deleteDetails(request);
	}
	
	//Not in use
	@PostMapping("/deleteNotifications/{notifType}")
	public ResponseEntity<String> deleteNotifications(@PathVariable String notifType, @RequestBody String entity) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteNotifications",
				"NotificationController , deleteNotifications started on : " + System.currentTimeMillis());
		try {
			String response = notificationService.deleteNotificationData(notifType, entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteNotifications",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping("/deleteNotificationsVaptNew/{notifType}")
	public ResponseEntity<String> deleteNotificationsVaptNew(@RequestBody List<CommonDeleteDTO> entity,
			@PathVariable String notifType, @RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteNotificationsVaptNew",
				"NotificationController , deleteNotificationsVaptNew started on : " + System.currentTimeMillis());
		try {
			String response = notificationService.deleteNotificationDataVaptNew(notifType, entity, appName,
					sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteNotifications",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	//Not in use
	@PostMapping(value = "/createNew")
	public String createNewNotification(@RequestHeader("sessionToken") String sessionToken,
			@RequestBody Object request) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createNewNotification",
				"NotificationController , createNewNotification started on : " + System.currentTimeMillis());
		return notificationService.createUpdateNotification(request, sessionToken);
	}

	@PostMapping(value = "/createVaptNew")
	public String createNewNotificationVaptNew(@RequestBody Object request, @RequestHeader String appName,
			@RequestHeader String sessionToken, @RequestParam String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "createNewNotificationVaptNew", " PjmID For This Reququest: " + pjmId);
		return notificationService.createUpdateNotificationViptNew(request, appName, sessionToken);
	}

	@PostMapping(value = "/updateNew")
	public String updateNotificationVaptNew(@RequestBody Object request, @RequestHeader String appName,
			@RequestHeader String sessionToken, @RequestParam String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "updateNotificationVaptNew", " PjmID For This Reququest: " + pjmId);
		return notificationService.updateNotificationVaptNew(request, appName, sessionToken);
	}
	
	@GetMapping(value = "/getPlaceHolder")
	public List<EmailPlaceHolder> getPlaceHolder() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getPlaceHolder", "started on : " + System.currentTimeMillis());
		return notificationService.getEmailPlaceHolder();
	}
}