package com.wipro.fipc.controller;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.exception.StatsMoniterCustomException;
import com.wipro.fipc.pojo.AllocationModel;
import com.wipro.fipc.pojo.AllocationResponse;
import com.wipro.fipc.pojo.AllocationUpdate;
import com.wipro.fipc.service.UIService;
import com.wipro.fipc.utils.UIHandler;

@RestController

@RequestMapping("/ui_query")
public class UIController {

	@Autowired
	UIService uiService;
	
	@GetMapping(value = "/")
	public String home() {

		return "I am Holmes Stats Monitor Service";
	}


	@GetMapping(value = "/testController")
	public String testController() {

		return "Holmes Stats Monitor Service is working";
	}

	



	@GetMapping(value = "/multiquery")
	public String findByMultiColumn(@RequestParam("column_value") List<Object> columnValues) {

		String type = columnValues.get(0).toString();
		try {
			Method uiHandler = UIHandler.class.getMethod(type, List.class, UIService.class);

			UIHandler handlerInstance = new UIHandler();
			return (String) uiHandler.invoke(handlerInstance, columnValues, uiService);

		} catch (NoSuchMethodException | SecurityException | IllegalAccessException | IllegalArgumentException
				| InvocationTargetException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "findByMultiColumn", e.toString());
		}
		return null;
	}

	@PostMapping(value = "/downloadExcel")
	public void getFile(@RequestBody String idJson, HttpServletResponse stepperData)
			throws StatsMoniterCustomException {
		stepperData.setContentType("application/vnd.ms-excel");
		stepperData.setHeader("Content-Disposition", "attachment; filename=Dashboard.xlsx");
		Workbook workbook = null;
		try {
			workbook = uiService.getTemplate(idJson);
			workbook.write(stepperData.getOutputStream());
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "method",
					"Unable to download Processinglog due to {} \n" + ex.getMessage());
		} finally {
			if (null != workbook)
				try {
					workbook.close();
				} catch (IOException e) {

					LoggerUtil.log(this.getClass(), Level.ERROR, "getTemplate", "Exception ", e.toString());
				}
		}
	}

	@PostMapping(value = "/downloadAllExcel")
	public void getCompleteFile(@RequestBody String idJson, HttpServletResponse stepperData)
			throws StatsMoniterCustomException {
		stepperData.setContentType("application/vnd.ms-excel");
		stepperData.setHeader("Content-Disposition", "attachment; filename=Dashboard.xlsx");
		Workbook workbook = null;
		try {
			workbook = uiService.getTemplateData(idJson);
			workbook.write(stepperData.getOutputStream());
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "method",
					"Unable to download Processinglog due to {} \n" + ex.getMessage());
		} finally {
			if (null != workbook)
				try {
					workbook.close();
				} catch (IOException e) {

					LoggerUtil.log(this.getClass(), Level.ERROR, "getTemplate", "Exception ", e.toString());
				}
		}
	}

	@PostMapping(value = "/filter")
	public String findFilterByMultiColumn(@RequestBody String requestBody) {
		String fetchResponseList = null;
		try {
			fetchResponseList = uiService.fetchFilterRequestList(requestBody);
		} catch (IOException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "", e.getMessage());
		}
		return fetchResponseList;
	}

	@PostMapping(value = "/getBarchart")
	public String getBarchart(@RequestBody String requestBody) {
		String getBarChart = null;
		try {
			getBarChart = uiService.getBarChart(requestBody);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", e.toString());
		}
		return getBarChart;
	}

	@PostMapping(value = "/getPiechart")
	public String getPieChart(@RequestBody String requestBody) {
		String getBarChart = null;
		try {
			getBarChart = uiService.getPieChart(requestBody);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "", e.toString());
		}
		return getBarChart;
	}

	@GetMapping(value = "/onLoad")
	public AllocationResponse getTaskDetails(@RequestParam("ldapId") String adid) {

		LoggerUtil.log(this.getClass(), Level.INFO, "getTaskDetails" + adid, adid);

		return uiService.getTaskDetails(adid);
	}

	@PostMapping(value = "/submit")
	public List<AllocationModel> updateRequestQueue(@RequestBody List<AllocationUpdate> request)
			throws URISyntaxException {

		return uiService.updateRequestQueue(request);

	}

	@GetMapping(value = "/reallocation")
	public AllocationResponse modifyClient(@RequestParam("clientId") String clientid,
			@RequestParam("clientCode") String clientCode) {

		LoggerUtil.log(this.getClass(), Level.INFO, "modifyClient" + clientid, clientid);
		LoggerUtil.log(this.getClass(), Level.INFO, "modifyClient client code" + clientCode, clientCode);

		return uiService.getModifyClient(clientid, clientCode);
	}
}
