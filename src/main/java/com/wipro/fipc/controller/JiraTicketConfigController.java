package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.ConfigStatusBO;
import com.wipro.fipc.model.EpicTypes;
import com.wipro.fipc.model.JiraApplicableClients;
import com.wipro.fipc.model.JiraIssueType;
import com.wipro.fipc.model.JiraProject;
import com.wipro.fipc.model.JiraTicketTaskConfig;
import com.wipro.fipc.model.JiraUserDetails;
import com.wipro.fipc.model.generated.ConfigStatusApproveBO;
import com.wipro.fipc.pojo.JiraRequestBody;
import com.wipro.fipc.service.JiraTicketConfigService;

@RestController
@RequestMapping("/jira")

public class JiraTicketConfigController {
	@Autowired
	JiraTicketConfigService jiraAppCreateService;

	@GetMapping(value = "getbycolumnticketTask/process_job_mapping_id")
	public JiraTicketTaskConfig getByColumnTikcetTask(@RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getByColumnTikcetTask",
				"JiraTicketConfigController , getByColumnTikcetTask started on : " + System.currentTimeMillis());
		return jiraAppCreateService.getByColumnJiraTikcetTask(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
	}

	@PostMapping(value = "/newcreateUpdate/ticketTaskconfig")
	public List<String> newcreateUpdateTicketTaskConfig(@RequestBody JiraTicketTaskConfig jiraTicketTaskConfig,
			@RequestHeader String appName, @RequestHeader String sessionToken, @RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "newcreateUpdateTicketTaskConfig",
				"JiraTicketConfigController , newcreateUpdateTicketTaskConfig started on : "
						+ System.currentTimeMillis());
		List<String> createUpdateTicketTaskResp = jiraAppCreateService
				.newcreateUpdateJiraTicketConfig(jiraTicketTaskConfig, appName, sessionToken, pjmId);
		return createUpdateTicketTaskResp;
	}

	@PostMapping(value = "/delete/ticketTaskconfig")
	public List<String> deleteTicketTaskConfig(@RequestBody JiraTicketTaskConfig ticketCreationUpdateConfig,
			@RequestHeader String appName, @RequestHeader String sessionToken, @RequestParam("pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTicketTaskConfig",
				"JiraTicketConfigController , deleteTicketTaskConfig started on : " + System.currentTimeMillis());
		List<String> createUpdateTicketTaskResp = jiraAppCreateService
				.deleteJiraTicketConfig(ticketCreationUpdateConfig, appName, sessionToken, pjmId);
		return createUpdateTicketTaskResp;
	}

	@PostMapping(value = "/updateJiraConfigStatus")
	public String updateProcessFeatureConfigstatus(@RequestBody ConfigStatusBO configStatusBO,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateProcessFeatureConfigstatus",
				"JiraTicketConfigController , updateProcessFeatureConfigstatus started on : "
						+ System.currentTimeMillis());
		String updateConfigStatusResp = "";
		updateConfigStatusResp = jiraAppCreateService.updateJiraConfigStatus(configStatusBO, appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigController",
				"updateConfigStatusResp :  " + updateConfigStatusResp);
		return updateConfigStatusResp;
	}

	@PostMapping(value = "/updateApprovedJiraConfigStatus")
	public String updateApproveProcessFeatureConfig(@RequestBody ConfigStatusApproveBO configStatusApproveBO,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateApproveProcessFeatureConfig",
				"JiraTicketConfigController , updateApproveProcessFeatureConfig started on : "
						+ System.currentTimeMillis());
		String updateApproveConfigStatusResp = "";
		updateApproveConfigStatusResp = jiraAppCreateService.updateApproveJiraConfigStatus(configStatusApproveBO,
				appName, sessionToken);
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigController",
				"updateApproveConfigStatusResp: " + updateApproveConfigStatusResp);
		return updateApproveConfigStatusResp;
	}

	@PostMapping(value = "/getJiraProject")
	public List<JiraProject> getProjects(@RequestBody JiraRequestBody jiraRequestBody) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getProjects",
				"JiraTicketConfigController , getProjects started on : " + System.currentTimeMillis());

		List<JiraProject> projectList = jiraAppCreateService.getProjectList(jiraRequestBody.getBusinessUnit());
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigController", "projectList: " + projectList);

		return projectList;
	}

	@PostMapping(value = "/getJiraIssueType")
	public List<JiraIssueType> getJiraIssueType(@RequestBody JiraRequestBody jiraRequestBody) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getJiraIssueType",
				"JiraTicketConfigController , getJiraIssueType started on : " + System.currentTimeMillis());

		List<JiraIssueType> issueTypeList = jiraAppCreateService.getIssueType(jiraRequestBody.getProjectKey(),
				jiraRequestBody.getBusinessUnit());
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigController", "issueTypeList: " + issueTypeList);

		return issueTypeList;
	}

	@PostMapping(value = "/getJiraApplicableClients")
	public List<JiraApplicableClients> getApplicableClients(@RequestBody JiraRequestBody jiraRequestBody) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getApplicableClients",
				"JiraTicketConfigController , getApplicableClients started on : " + System.currentTimeMillis());

		List<JiraApplicableClients> applicableClintList = jiraAppCreateService.getApplicableClients(
				jiraRequestBody.getProjectKey(), jiraRequestBody.getIssueId(), jiraRequestBody.getBusinessUnit());
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigController",
				"applicableClintList: " + applicableClintList);

		return applicableClintList;
	}

	@PostMapping(value = "/getJiraUserDetails")
	public List<JiraUserDetails> getJiraUserDetails(@RequestBody JiraRequestBody jiraRequestBody) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getJiraUserDetails",
				"JiraTicketConfigController , getJiraUserDetails started on : " + System.currentTimeMillis());

		List<JiraUserDetails> userDetailsList = jiraAppCreateService.getUserDetails(jiraRequestBody.getEmailId(),
				jiraRequestBody.getBusinessUnit());
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigController",
				"userDetailsList: " + userDetailsList);

		return userDetailsList;
	}

	@PostMapping(value = "/getJiraEpicType")
	public List<EpicTypes> getJiraEpicType(@RequestBody JiraRequestBody jiraRequestBody) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getJiraEpicType",
				"JiraTicketConfigController , getJiraEpicType started on : " + System.currentTimeMillis());

		List<EpicTypes> epicTypeList = jiraAppCreateService.getJiraEpicType(jiraRequestBody.getProjectKey(),
				jiraRequestBody.getIssueId(), jiraRequestBody.getBusinessUnit());
		LoggerUtil.log(this.getClass(), Level.INFO, "JiraTicketConfigController", "epicTypeList: " + epicTypeList);

		return epicTypeList;
	}
	
	@PostMapping(value = "/getCustomFields")
	public ResponseEntity<String> getCustomFields(@RequestBody JiraRequestBody jiraRequestBody) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getCustomFields",
				"JiraTicketConfigController , getCustomFields started on : " + System.currentTimeMillis());
		try {
			String customFields = jiraAppCreateService.getCustomFields(jiraRequestBody.getProjectKey(),
					jiraRequestBody.getIssueId(), jiraRequestBody.getBusinessUnit());
			LoggerUtil.log(this.getClass(), Level.INFO, "getCustomFields",
					"JiraTicketConfigController , getCustomFields end on : " + System.currentTimeMillis());
			return new ResponseEntity<>(customFields, HttpStatus.OK);
		} catch (Exception ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getCustomFields", "Unable to fetch the customFields.", ex);
			return new ResponseEntity<>(HolmesAppConstants.FAILED + ": " + HolmesAppConstants.GET_FAILURE_MESSAGE,
					HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
