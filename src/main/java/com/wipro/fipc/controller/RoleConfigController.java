package com.wipro.fipc.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.gson.Gson;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CustomClientBusinessOps;
import com.wipro.fipc.model.CustomRoleConfig;
import com.wipro.fipc.model.RoleConfigResponse;
import com.wipro.fipc.model.RoleConfigUI;
import com.wipro.fipc.model.generated.CustomBusinessUnitBO;
import com.wipro.fipc.pojo.ClientInformation;
import com.wipro.fipc.pojo.ClientInformationBo;
import com.wipro.fipc.pojo.CustomProcessBO;
import com.wipro.fipc.pojo.RoleDetails;
import com.wipro.fipc.service.IRoleConfigService;

@RestController
@RequestMapping("/roleConfig")

public class RoleConfigController {

	@Autowired
	IRoleConfigService roleConfigService;

	@GetMapping("/getByAdid/{adid}")
	public List<CustomRoleConfig> getRoleConfigsByADID(@PathVariable("adid") String adId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getRoleConfigsByADID", "getRoleConfigsByADID");
		LoggerUtil.log(this.getClass(), Level.INFO, "getRoleConfigsByADID", "Requested ADID= " + adId);
		return roleConfigService.getRoleConfigsByADID(adId);
	}

	@PostMapping(value = "/uploadRoleConfig/{loggedInUserId}")
	public RoleConfigResponse uploadRoleConfig(@PathVariable("loggedInUserId") String loggedInUserId,
			@RequestParam("file") MultipartFile file) {
		LoggerUtil.log(this.getClass(), Level.INFO, "uploadRoleConfig",
				"RoleConfigController , uploadRoleConfig started on : " + System.currentTimeMillis());
		return roleConfigService.uploadExcelFile(file, loggedInUserId);
	}

	@GetMapping(value = "/getAllRoleConfig/{loggedInUserId}")
	public List<RoleConfigUI> getAllRoleConfig(@PathVariable("loggedInUserId") String loggedInUserId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllRoleConfig",
				"RoleConfigController , getAllRoleConfig started on : " + System.currentTimeMillis());
		try {
			return roleConfigService.getAllRoleConfig(loggedInUserId);
		} catch (IOException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllRoleConfig", "Unable to get due to {} \n" + e.getMessage());
			throw new BusinessException(e.getMessage());
		}
	}

	@PutMapping(value = "/putRoleConfig/{loggedInUserId}")
	public Object putRoleConfig(@RequestBody String request, @PathVariable("loggedInUserId") String loggedInUserId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "putRoleConfig",
				"RoleConfigController , putRoleConfig started on : " + System.currentTimeMillis());
		Gson gson = new Gson();
		List<RoleConfigUI> roleConfigList = Arrays.asList(gson.fromJson(request, RoleConfigUI[].class));
		return roleConfigService.putRoleConfig(roleConfigList, loggedInUserId);
	}

	@DeleteMapping(value = "/deleteRoleConfig/{id}")
	public String deleteRoleConfig(@PathVariable("id") long id) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteRoleConfig",
				"RoleConfigController , deleteRoleConfig started on : " + System.currentTimeMillis());
		return roleConfigService.deleteRoleConfig(id);
	}

	@GetMapping(value = "/downloadTemplate/{loggedInUserId}")
	public void getFile(@PathVariable("loggedInUserId") String loggedInUserId, HttpServletResponse roleConfig) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getFile",
				"RoleConfigController , getFile started on : " + System.currentTimeMillis());
		roleConfig.setContentType("application/vnd.ms-excel");
		roleConfig.setHeader("Content-Disposition", "attachment; filename=RoleConfigTemplate.xlsx");
		Workbook workbook = roleConfigService.getTemplate(loggedInUserId);
		try {
			workbook.write(roleConfig.getOutputStream());
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "method",
					"Unable to download RoleConfigTemplate due to {} \n" + ex.getMessage());
		} finally {
			if (null != workbook)
				try {
					workbook.close();
				} catch (IOException e) {
					LoggerUtil.log(getClass(), Level.ERROR, "getFile", "Unable to get due to {} \n" + e.getMessage());
				}
		}
	}

	@GetMapping("/getLogin/{adid}")
	public List<RoleDetails> getUserLoginInformation(@PathVariable("adid") String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getUserLoginInformation",
				"getUserLoginInformation started on : " + System.currentTimeMillis());
		List<RoleDetails> loginInformation = roleConfigService.getLoginInformation(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformation",
				"getLoginClientInformation started on : " + System.currentTimeMillis());
		return loginInformation;
	}

	@GetMapping("/getLoginclientinfo")
	public List<ClientInformationBo> getUserLoginInformationwithoutbuid(@RequestParam("ldapId") String ldapId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getUserLoginInformationwithoutbuid",
				"getUserLoginInformationwithoutbuid started on : " + System.currentTimeMillis());
		List<ClientInformationBo> loginClientInformationBo = roleConfigService.getLoginClientInformationBo(ldapId);
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformation",
				"getLoginClientInformation started on : " + System.currentTimeMillis());
		return loginClientInformationBo;
	}

	@GetMapping("/getLoginclientinfowithbuid")
	public List<ClientInformation> getUserLoginwithbuid(@RequestParam("ldapId") String ldapId,
			@RequestParam("role") String role, @RequestParam("bUnit") Long bUnit,
			@RequestParam("businessOpsId") Long businessOpsId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getUserLoginwithbuid",
				"getUserLoginwithbuid started on : " + System.currentTimeMillis());
		List<ClientInformation> loginClientInformation = roleConfigService.getLoginClientInformation(ldapId, role,
				bUnit, businessOpsId);
		LoggerUtil.log(this.getClass(), Level.INFO, "getLoginClientInformation",
				"getLoginClientInformation started on : " + System.currentTimeMillis());
		return loginClientInformation;
	}

	@GetMapping(value = "/getReportees/{loggedInUserId}")
	public List<String> getReportees(@PathVariable("loggedInUserId") String loggedInUserId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getReportees",
				"RoleConfigController , getReportees started on : " + System.currentTimeMillis());
		return roleConfigService.getReporteeIds(loggedInUserId);
	}

	@GetMapping(value = "/getBusinessUnitAll")
	public List<CustomBusinessUnitBO> getBusinessUnitAll() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getBusinessUnitAll",
				"RoleConfigController , getBusinessUnitAll started on : " + System.currentTimeMillis());
		return roleConfigService.getBusinessUnitAll();
	}

	@GetMapping(value = "/getClientAndBusinessOps/{buID}")
	public CustomClientBusinessOps getClientAndBusinessOps(@PathVariable("buID") String buId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getClientAndBusinessOps",
				"RoleConfigController , getClientAndBusinessOps started on : " + System.currentTimeMillis());
		return roleConfigService.getClientAndBusinessOps(buId);
	}

	@GetMapping(value = "/getProcessDetails/{buID}/{buOpsID}/{clientID}")
	public Set<CustomProcessBO> getProcessDetails(@PathVariable("buID") String buId,
			@PathVariable("buOpsID") String buOpsId, @PathVariable("clientID") String clientId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getProcessDetails",
				"RoleConfigController , getProcessDetails started on : " + System.currentTimeMillis());
		return roleConfigService.getProcessDetails(buId, buOpsId, clientId);
	}

}