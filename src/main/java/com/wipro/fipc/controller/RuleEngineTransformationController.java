package com.wipro.fipc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.model.RulesRequest;
import com.wipro.fipc.service.IRuleEngineTransformationService;

@RestController

@RequestMapping("/rule/transformation/")
public class RuleEngineTransformationController {

	@Autowired
	public IRuleEngineTransformationService transformationService;

	@PostMapping(value = "/create")
	public Object createTransformationRules(@RequestBody RulesRequest transformationRules) {
		LoggerUtil.log(getClass(), Level.INFO, "createTransformationRules",
				"inside createTransformationRules Controller : " + transformationRules.toString());
		return transformationService.createTransformationRules(transformationRules);
	}

	@PostMapping(value = "/update/pjmId/{pjmId}/ruleId/{ruleId}")
	public RulesRequest updateBusinessRulesByRuleId(@RequestBody RulesRequest bizRules,
			@PathVariable(value = "pjmId") String pjmId, @PathVariable(value = "ruleId") String ruleId) {
		LoggerUtil.log(getClass(), Level.INFO, "updateBusinessRulesByRuleId",
				"inside updateBusinessRulesByRuleId Controller : " + bizRules + pjmId + ruleId);
		return transformationService.updateBusinessRulesByRuleId(bizRules, pjmId, ruleId);
	}

	@DeleteMapping(value = "/delete/id/{id}")
	public String deleteBusinessRules(@PathVariable(value = "id") String id) {
		LoggerUtil.log(getClass(), Level.INFO, "deleteBusinessRules", "inside deleteBusinessRules Controller : " + id);
		return transformationService.deleteTransformationRules(id);
	}

	@DeleteMapping(value = "/deleteAll/pjmId/{pjmId}")
	public String deleteAllBusinessRules(@PathVariable(value = "pjmId") String pjmId) {
		LoggerUtil.log(getClass(), Level.INFO, "deleteAllBusinessRules",
				"inside deleteBusinessRules Controller : " + pjmId);
		return transformationService.deleteAllTransformationRule(pjmId);
	}

	@PostMapping(value = "/createRule")
	public Object createTransformationRulesLatest(@RequestBody RulesRequest transformationRules,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(getClass(), Level.INFO, "createTransformationRulesLatest",
				"inside createTransformationRulesLatest Controller ");
		return transformationService.createTransformationRulesData(transformationRules, appName, sessionToken);
	}

	@PostMapping(value = "/updateRule")
	public RulesRequest updateBusinessRulesByRuleIdLatest(@RequestBody RulesRequest bizRules,
			@RequestParam(value = "pjmId") String pjmId, @RequestParam(value = "ruleId") String ruleId,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(getClass(), Level.INFO, "updateBusinessRulesByRuleIdLatest",
				"inside updateBusinessRulesByRuleIdLatest Controller : " + pjmId + ruleId);
		return transformationService.updateBusinessRulesByRuleIdData(bizRules, pjmId, ruleId, appName, sessionToken);
	}
}
