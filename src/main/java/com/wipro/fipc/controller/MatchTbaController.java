package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.MatchTba;
import com.wipro.fipc.service.MatchTbaService;

@RestController
@RequestMapping(value = "/matchtba")

public class MatchTbaController {

	@Autowired
	MatchTbaService matchtbaService;

	private static final String INSIDEMATCHTBACONTROLLER = "inside MatchTbaController Controller : ";
	private static final String SUBMIT = "submit()";
	private static final String GETRESULTVARIABLE = "getResultVariable()";

	@GetMapping(value = "/FileNameDetails/{column_name}/{column_value}")
	public String getFileNameandField(@PathVariable(value = "column_name") String columnName,
			@PathVariable(value = "column_value") String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getFileNameandField",
				"MatchTbaController , getFileNameandField started on : " + System.currentTimeMillis());
		return matchtbaService.getFileNameandField(columnName, columnValue);
	}

	@GetMapping(value = "/TbaFieldDetails/{column_name}/{column_value}")
	public String getTbaField(@PathVariable(value = "column_name") String columnName,
			@PathVariable(value = "column_value") String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaField",
				"MatchTbaController , getTbaField started on : " + System.currentTimeMillis());
		return matchtbaService.getTbaField(columnName, columnValue);
	}

	@GetMapping(value = "/RuleDetails/{process_job_mapping_id}/{file_name}")
	public String getRule(@PathVariable(value = "process_job_mapping_id") Long processJobMappingId,
			@PathVariable(value = "file_name") String fileName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getRule",
				"MatchTbaController , getRule started on : " + System.currentTimeMillis());
		return matchtbaService.getRule(processJobMappingId, fileName);
	}

	@GetMapping(value = "/FileFieldDetails/{column_name}/{column_value}")
	public List<String> getMismatch(@PathVariable(value = "column_name") String columnName,
			@PathVariable(value = "column_value") String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getMismatch",
				"MatchTbaController , getMismatch started on : " + System.currentTimeMillis());
		return matchtbaService.getMismatch(columnName, columnValue);
	}

	@DeleteMapping(value = "/delete/{id}")
	public String deleteMatchTba(@PathVariable(value = "id") String id) {
		LoggerUtil.log(getClass(), Level.INFO, "deleteMatchTba ", INSIDEMATCHTBACONTROLLER + id);
		return matchtbaService.deleteMatchTba(id);
	}

	@PostMapping(value = "/createMatchTba")
	public String createMatchTba(@RequestBody String request) {
		LoggerUtil.log(getClass(), Level.INFO, "createMatchTba ", INSIDEMATCHTBACONTROLLER + request);
		return matchtbaService.createMatchTbaRecord(request);
	}

	@GetMapping(value = "/tbaMatchConfig/{column_name}/{column_value}")
	public String getDetailsTbaMatchConfig(@PathVariable(value = "column_name") String columnName,
			@PathVariable(value = "column_value") String columnValue) {
		LoggerUtil.log(getClass(), Level.INFO, "getDetailsTbaMatchConfig ",
				INSIDEMATCHTBACONTROLLER + columnName + "Id Value " + columnValue);
		return matchtbaService.getDetailsTbaMatchConfig(columnName, columnValue);
	}

	@PostMapping(value = "/submit")
	public Object submit(@RequestParam String action, @RequestBody List<MatchTba> mtList, @RequestHeader String appName,
			@RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, SUBMIT, "ProcessController-->submit()-->starts:");
		try {
			Object response = this.matchtbaService.submit(action, mtList, appName, sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, SUBMIT, "ProcessController-->submit()--ends.");
			return response;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, SUBMIT,
					"ProcessController-->submit()--Exception: " + e.getMessage());
			if (e.getMessage().contains("Connection refused: connect;"))
				return "DBService is down";
			return e.getMessage();
		}
	}

	@GetMapping("/get/matchtba/{column_name}/{column_value}")
	public Object matchtba(@PathVariable(value = "column_name") String columnName,
			@PathVariable(value = "column_value") String columnValue) {
		LoggerUtil.log(this.getClass(), Level.INFO, "matchtba()", "matchtba()-->starts:");
		try {
			return this.matchtbaService.getDetailsTbaMatchConfigDetails(columnName, columnValue);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "matchtba()", "matchtbaa()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}

	@PostMapping("/get/ResultVariableDetails/{column_name}/{column_value}")
	public Object getResultVariable(@PathVariable(value = "column_name") String columnName,
			@PathVariable(value = "column_value") String columnValue, @RequestBody String ruleName) {
		LoggerUtil.log(this.getClass(), Level.INFO, GETRESULTVARIABLE,
				"MatchTbaController-->getResultVariable()-->starts:");
		try {
			Object rulesConfigList = this.matchtbaService.getResultVariable(columnName, columnValue, ruleName);
			LoggerUtil.log(this.getClass(), Level.INFO, GETRESULTVARIABLE,
					"MatchTbaController-->getResultVariable()--ends.");
			return rulesConfigList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, GETRESULTVARIABLE,
					"MatchTbaController-->getResultVariable()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}

	@PostMapping(value = "/deleteMatchTba")
	public ResponseEntity<String> deleteMatchTbaRecords(@RequestBody String entity) {
		try {
			String response = matchtbaService.deleteMatchTbaRecords(entity);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to delete due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/update")
	public Object updateMatchTba(@RequestBody List<MatchTba> mtList) {
		LoggerUtil.log(this.getClass(), Level.INFO, SUBMIT, "ProcessController-->updateMatchTba()-->starts:");
		try {
			Object response = this.matchtbaService.updateMatchTba(mtList);
			LoggerUtil.log(this.getClass(), Level.INFO, SUBMIT, "ProcessController-->updateMatchTba()--ends.");
			return response;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, SUBMIT,
					"ProcessController-->updateMatchTba()--Exception: " + e.getMessage());
			if (e.getMessage().contains("Connection refused: connect;"))
				return "DBService is down";
			return e.getMessage();
		}
	}

	@GetMapping("/get/matchtbadata")
	public Object matchtbaLatest(@RequestParam(name = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "matchtba()", "matchtbaLatest()-->starts:");
		try {
			return this.matchtbaService.getDetailsTbaMatchConfigDetails(HolmesAppConstants.PROCESS_JOB_MAPPING_ID,
					pjmId);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "matchtba()", "matchtbaa()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}

	@GetMapping(value = "/FileFieldDetailsData")
	public List<String> getMismatchLatest(@RequestParam(name = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getMismatchLatest",
				"MatchTbaController , getMismatchLatest started on : " + System.currentTimeMillis());
		return matchtbaService.getMismatch(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
	}

	@GetMapping(value = "/TbaFieldDetailsData")
	public String getTbaFieldLatest(@RequestParam(name = "pjmId") String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaFieldLatest",
				"MatchTbaController , getTbaFieldLatest started on : " + System.currentTimeMillis());
		return matchtbaService.getTbaField(HolmesAppConstants.PROCESS_JOB_MAPPING_ID, pjmId);
	}

	@PostMapping("/get/ResultVariableDetails")
	public Object getResultVariableLatest(@RequestParam(name = "pjmId") String pjmId, @RequestBody String ruleName) {
		LoggerUtil.log(this.getClass(), Level.INFO, GETRESULTVARIABLE,
				"MatchTbaController-->getResultVariable()-->starts:");
		try {
			Object rulesConfigList = this.matchtbaService.getResultVariable(HolmesAppConstants.PROCESS_JOB_MAPPING_ID,
					pjmId, ruleName);
			LoggerUtil.log(this.getClass(), Level.INFO, GETRESULTVARIABLE,
					"MatchTbaController-->getResultVariableLatest()--ends.");
			return rulesConfigList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, GETRESULTVARIABLE,
					"MatchTbaController-->getResultVariableLatest()--Exception: " + e.getMessage());
			return e.getMessage();
		}
	}
}
