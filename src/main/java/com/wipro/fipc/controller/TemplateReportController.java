
package com.wipro.fipc.controller;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.TemplateReportModel;
import com.wipro.fipc.model.TemplateReportRequest;
import com.wipro.fipc.service.ITemplateReportService;

@RestController
@RequestMapping("/templatereport")

public class TemplateReportController {

	public static final String STATUS = "status";
	public static final String MESSAGE = "message";
	public static final String SUCCESS = "success";
	public static final String FAILED = "failed";
	public static final String ERROR = "Error";

	@Autowired
	Gson gson;

	@Autowired
	ITemplateReportService templateReportService;

	// old
	@PostMapping(value = "/uploadTemplate") // Dependency on File Formatter API
	public ResponseEntity<String> saveAllTemplate(@RequestParam("report") MultipartFile file,
			TemplateReportRequest trequest) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "addTemplate()", "TemplateReportUploadPojo :" + trequest);
			String response = templateReportService.saveTemplate(file, trequest);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Template/Report is not saved. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "addTemplate()", "Unable to save due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	// old
	@PostMapping(value = "/updateTemplate") // Dependency on File Formatter , 1 Unknown API
	public ResponseEntity<String> updateTemplate(@RequestParam("report") MultipartFile file,
			TemplateReportRequest trequest) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "updateTemplate()", "TemplateReportUploadPojo :" + trequest);
			String response = templateReportService.updateTemplate(file, trequest);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Template/Report is not updated. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "updateTemplate()",
					"Unable to update due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	// old
	@PostMapping(value = "/update")
	public ResponseEntity<String> update(@RequestParam("report") MultipartFile file, TemplateReportRequest trequest) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "update()", "TemplateReportRequest :" + trequest);
			templateReportService.deleteTemplate(trequest.getId());
			String response = templateReportService.saveTemplate(file, trequest);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Template/Report is not updated.");
			LoggerUtil.log(getClass(), Level.ERROR, "update()", "Unable to update due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@GetMapping(value = "/downloadTemplate")
	public ResponseEntity<byte[]> downloadFile(@RequestParam("id") long id) throws IOException {
		LoggerUtil.log(this.getClass(), Level.INFO, "downloadFile()", "id :" + id);
		byte[] templateStream = templateReportService.downloadTemplate(id);
		return new ResponseEntity<byte[]>(templateStream, HttpStatus.OK);
	}

	@PostMapping(value = "/deleteTemplate")
	public ResponseEntity<String> deleteTemplate(@RequestParam("id") long id) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "deleteTemplate()", "id :" + id);
			return new ResponseEntity<>(templateReportService.deleteTemplate(id), HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteTemplate()",
					"Unable to delete due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getAllTemplates")
	public ResponseEntity<String> getAllTemplates() {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getAllTemplates()", "");
			ObjectMapper objectMapper=new ObjectMapper().configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
			return new ResponseEntity<>(objectMapper.writeValueAsString(templateReportService.getAllTemplates()), HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getAllTemplates()",
					"Unable to getAllTemplates due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@GetMapping(value = "/getTemplateForClient")
	public ResponseEntity<String> getTemplateForClients(@RequestParam("clientCode") String clientCode) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateForClient()", " for client " + clientCode);
			return new ResponseEntity<>(gson.toJson(templateReportService.getTemplateForClient(clientCode)),
					HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplateForClients()",
					"Unable to getTemplateForClients due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	// need to be removed
	@GetMapping(path = "/getTemplateLayout/{id}/{pjmid}")
	public ResponseEntity<String> getTemplateLayouts(@PathVariable("id") long id, @PathVariable("pjmid") long pjmid) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateLayout()", " with id " + id);
			return new ResponseEntity<>(templateReportService.getTemplateLayout(id, pjmid), HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplateLayout()",
					"Unable to getTemplateLayout due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@GetMapping(path = "/getTemplateLayout")
	public ResponseEntity<String> getTemplateLayout(@RequestParam("templateId") long id,
			@RequestParam("pjmId") long pjmid) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateLayout()", " with id " + id);
			return new ResponseEntity<>(templateReportService.getTemplateLayout(id, pjmid), HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplateLayout()",
					"Unable to getTemplateLayout due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@PostMapping(path = "/saveLabellingReportData")
	public ResponseEntity<String> saveLabellingReportData(@RequestBody TemplateReportModel data) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "saveLabellingReportData() method is called with ",
					data.toString());
			return new ResponseEntity<>(templateReportService.saveLabellingReportData(data), HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "LabellingReport is not saved. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "saveLabellingReportData()",
					"Unable to saveLabellingReportData due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@PostMapping(path = "/updateLabellingReportData/{id}")
	public ResponseEntity<String> updateLabellingReportDataa(@PathVariable("id") Long id,
			@RequestBody TemplateReportModel data) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "updateLabellingReportData() method is called with ",
					data.toString());
			return new ResponseEntity<>(templateReportService.updateLabellingReportData(id, data), HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "LabellingReport is not updated. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "updateLabellingReportData()",
					"Unable to saveLabellingReportData due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	// Need to be removed
	@GetMapping(path = "/getTemplateReportListNames/{type}/{clientId}")
	public ResponseEntity<String> getTemplateReportListNames(@PathVariable("type") String type,
			@PathVariable("clientId") String clientId) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateReportListNames() method ",
					"called with clientId " + clientId);
			return new ResponseEntity<>(gson.toJson(templateReportService.getTemplateReportNames(type, clientId)),
					HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplateReportListNames()",
					"Unable to getTemplateReportListNames due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@GetMapping(path = "/getTemplateReportListNames/whitelist")
	public ResponseEntity<String> getWhiteListNames(@RequestParam("clientCode") String clientCode) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateReportListNames() method -> TEMPLATE ",
					"called with clientId " + clientCode);
			return new ResponseEntity<>(
					gson.toJson(templateReportService.getTemplateReportNames(HolmesAppConstants.WHITELIST, clientCode)),
					HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplateReportListNames()",
					"Unable to getTemplateReportListNames due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@GetMapping(path = "/getTemplateReportListNames/labellingReport")
	public ResponseEntity<String> getLabellingReportList(@RequestParam("clientCode") String clientCode) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getTemplateReportListNames() method -> TEMPLATE ",
					"called with clientCode " + clientCode);
			return new ResponseEntity<>(gson.toJson(
					templateReportService.getTemplateReportNames(HolmesAppConstants.LABELLING_REPORT, clientCode)),
					HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getTemplateReportListNames()",
					"Unable to getTemplateReportListNames due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@GetMapping(path = "/getLabellingReportData/{id}")
	public ResponseEntity<String> getLabellingReportData(@PathVariable("id") long id) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getLabellingReportData() method ", "is called");
			return new ResponseEntity<>(templateReportService.getLabellingReportData(id), HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getLabellingReportData()",
					"Unable to getLabellingReportData due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	// Need to be removed
	@GetMapping(path = "/getListOfSSN/{id}/{clientId}")
	public ResponseEntity<String> getListOfSSNs(@PathVariable("id") long id,
			@PathVariable("clientId") String clientId) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "getListOfSSN() method ", "is called");
			return new ResponseEntity<>(templateReportService.getSSNList(id, clientId), HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getListOfSSN()",
					"Unable to getListOfSSN due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@GetMapping(path = "/getListOfSSN/{id}")
	public ResponseEntity<String> getListOfSSN(@PathVariable("id") long id,
			@RequestParam("clientCode") String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getListOfSSN",
				"TemplateReportController , getListOfSSN started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(templateReportService.getSSNList(id, clientCode), HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "getListOfSSN()",
					"Unable to getListOfSSN due to {} \n" + e.getMessage());
			return new ResponseEntity<>(ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	// new post methods

	@PostMapping(value = "/uploadTemplateNew") // Dependency on File Formatter API ,Holmes Auth API
	public ResponseEntity<String> addTemplateData(@RequestParam("report") MultipartFile file,
			TemplateReportRequest trequest, @RequestHeader String appName, @RequestHeader String sessionToken) {
		try {
			LoggerUtil.log(this.getClass(), Level.INFO, "addTemplate()", "TemplateReportUploadPojo :" + trequest);
			String response = templateReportService.saveTemplateData(file, trequest, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Template/Report is not saved. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "addTemplate()", "Unable to save due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/updateTemplateNew") // Dependency on File Formatter API ,Holmes Auth API
	public ResponseEntity<String> updateTemplateData(@RequestParam("report") MultipartFile file,
			TemplateReportRequest trequest, @RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateTemplateData",
				"TemplateReportController , updateTemplateData started on : " + System.currentTimeMillis());
		try {
			String response = templateReportService.updateTemplateData(file, trequest, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Template/Report is not updated. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "updateTemplate()",
					"Unable to update due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@PostMapping(value = "/updateNew") // Dependency on File Formatter API ,Holmes Auth API
	public ResponseEntity<String> updateNew(@RequestParam("report") MultipartFile file, TemplateReportRequest trequest,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateNew",
				"TemplateReportController , updateNew started on : " + System.currentTimeMillis());
		try {
			templateReportService.deleteTemplate(trequest.getId());
			String response = templateReportService.saveTemplateData(file, trequest, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "Template/Report is not updated.");
			LoggerUtil.log(getClass(), Level.ERROR, "update()", "Unable to update due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@PostMapping(path = "/saveLabellingReportDataNew") // // Dependency on,Holmes Auth API
	public ResponseEntity<String> addLabellingReportData(@RequestBody TemplateReportModel data,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "addLabellingReportData",
				"TemplateReportController , addLabellingReportData started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(templateReportService.addLabellingReportData(data, appName, sessionToken),
					HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "LabellingReport is not saved. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "saveLabellingReportData()",
					"Unable to saveLabellingReportData due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@PostMapping(path = "/updateLabellingReportDataNew/{id}") // Dependency on Holmes Auth API
	public ResponseEntity<String> updateLabellingReportData(@PathVariable("id") Long id,
			@RequestBody TemplateReportModel data, @RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateLabellingReportData",
				"TemplateReportController , updateLabellingReportData started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(
					templateReportService.updateLabellingReportData(id, data, appName, sessionToken), HttpStatus.OK);
		} catch (Exception e) {
			JsonObject jobj = new JsonObject();
			jobj.addProperty(STATUS, FAILED);
			jobj.addProperty(MESSAGE, "LabellingReport is not updated. Contact system administrator");
			LoggerUtil.log(getClass(), Level.ERROR, "updateLabellingReportData()",
					"Unable to saveLabellingReportData due to {} \n" + e.getMessage());
			return new ResponseEntity<>(jobj.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

}