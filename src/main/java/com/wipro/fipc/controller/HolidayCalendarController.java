package com.wipro.fipc.controller;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.HolidayCalendarResponse;
import com.wipro.fipc.service.IHolidayCalendarService;

@RestController
@RequestMapping("/holidaycalendar")

public class HolidayCalendarController {

	@Autowired
	IHolidayCalendarService holidayCalendarService;

	@PostMapping(value = "/newputHolidayRequests")
	public Object newputHolidayRequests(@RequestBody String request, @RequestParam("clientCode") String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "newputHolidayRequests",
				"HolidayCalendarController , newputHolidayRequests started on : " + System.currentTimeMillis());
		try {
			return holidayCalendarService.newputHolidayRequests(request, clientCode);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, " ", "Exception in newputHolidayRequests>> : ", e);
		}
		HolidayCalendarResponse hcr = new HolidayCalendarResponse();
		hcr.setMessage(HolmesAppConstants.FAILED + ": Exception occured while Saving/Updating the new Holiday Calendar");
		return hcr;
	}

	@GetMapping(value = "/newautoPopulateHolidayCalendar")
	public List<HolidayCalendarResponse> newautoPopulateHolidayCalendar(@RequestParam("clientCode") String clientCode,
			@RequestParam("selectedYear") String selectedYear) throws Exception {
		LoggerUtil.log(this.getClass(), Level.INFO, "newautoPopulateHolidayCalendar",
				"HolidayCalendarController , newautoPopulateHolidayCalendar started on : "
						+ System.currentTimeMillis());
		return holidayCalendarService.autoPopulateHolidayCalendarByYear(clientCode, selectedYear);
	}
	
	@GetMapping(value = "/downloadHolidayTemplate")
	public void getFileNew(@RequestParam String clientCode, HttpServletResponse holidayCalendar)
			throws IOException, ParseException {
		LoggerUtil.log(this.getClass(), Level.INFO, "getFileNew",
				"HolidayCalendarController , getFileNew started on : " + System.currentTimeMillis());
		holidayCalendar.setContentType("application/vnd.ms-excel");
		holidayCalendar.setHeader("Content-Disposition", "attachment; filename=HolidayCalendarTemplate.xlsx");
		holidayCalendarService.getTemplate(clientCode, holidayCalendar);
	}
}
