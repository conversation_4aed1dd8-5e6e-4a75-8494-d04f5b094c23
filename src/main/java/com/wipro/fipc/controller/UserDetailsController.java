package com.wipro.fipc.controller;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.dao.GenericDao;
import com.wipro.fipc.dao.common.UserDetailsDao;
import com.wipro.fipc.entity.common.UserDetails;
import com.wipro.fipc.pojo.ReporteeAdidsPojo;
import com.wipro.fipc.pojo.User;
import com.wipro.fipc.service.IUserDetailsService;

@RestController
@RequestMapping("/userdetails")

public class UserDetailsController {
	@Autowired
	GenericDao<UserDetails> genericDao;

	@Autowired
	UserDetailsDao userDetailsDao;

	@Autowired
	IUserDetailsService userDetailsService;

	protected static final String USER_DETAILS = "user_details";
	protected static final String COMMON_SCHEMA = "common";

	@GetMapping(value = "/getType/{adid}")
	public String getType(@PathVariable("adid") String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getType", "getType started on " + System.currentTimeMillis());
		String type = userDetailsDao.getType(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "getType", "getType ended with : " + System.currentTimeMillis());
		return type;
	}

	@GetMapping(value = "/getAdidWithoutAdmin")
	public List<String> getAdidWithoutAdmin() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAdidWithoutAdmin",
				"getAdidWithoutAdmin started on " + System.currentTimeMillis());
		List<String> adidWithoutAdmin = userDetailsDao.getAdidWithoutAdmin();
		LoggerUtil.log(this.getClass(), Level.INFO, "getAdidWithoutAdmin",
				"getAdidWithoutAdmin ended with : " + System.currentTimeMillis());
		return adidWithoutAdmin;
	}

	@GetMapping(value = "/getAllAdid")
	public List<String> getAllAdid() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllAdid",
				"getAllAdid started on " + System.currentTimeMillis());
		List<String> allAdid = userDetailsDao.getAllAdid();
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllAdid",
				"getAllAdid ended with : " + System.currentTimeMillis());
		return allAdid;
	}

	@PutMapping(value = "/updateUser/{updatedBy}")
	public String updateUser(@PathVariable("updatedBy") String updatedBy, @RequestBody List<String> adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUser",
				"updateUser started on " + System.currentTimeMillis());
		String updateUserRoleConfig = userDetailsService.updateUserRoleConfig(updatedBy, adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUser",
				"updateUser ended with : " + System.currentTimeMillis());
		return updateUserRoleConfig;

	}

	@GetMapping("getReportees/{role}/{adid}")
	public List<String> getAllReportees(@PathVariable("adid") String adid, @PathVariable("role") String role) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllReportees",
				"getAllReportees started on :" + System.currentTimeMillis());
		List<String> allReportees = userDetailsService.getAllReportees(adid, role);
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllReportees",
				"getAllReportees ended with : " + System.currentTimeMillis());
		return allReportees;
	}

	@PostMapping("/admin")
	public String createUserByAdmin(@RequestBody User user) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createUserByAdmin",
				"createUserByAdmin started on : " + System.currentTimeMillis());
		String createUserByAdmin = userDetailsService.createUserByAdmin(user);
		LoggerUtil.log(this.getClass(), Level.INFO, "createUserByAdmin",
				"createUserByAdmin ended with : " + System.currentTimeMillis());
		return createUserByAdmin;
	}

	@PutMapping("/admin")
	public String updateUserByAdmin(@RequestBody User user) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserByAdmin",
				"updateUserByAdmin started on : " + System.currentTimeMillis());
		String updateUserByAdmin = userDetailsService.updateUserByAdmin(user);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserByAdmin",
				"updateUserByAdmin ended with : " + System.currentTimeMillis());
		return updateUserByAdmin;
	}

	@PutMapping("/admin/{adid}")
	public String deleteUserByAdmin(@RequestBody User user, @PathVariable("adid") String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteUserByAdmin",
				"deleteUserByAdmin started on : " + System.currentTimeMillis());
		String deleteUserByAdmin = userDetailsService.deleteUserByAdmin(user);
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteUserByAdmin",
				"deleteUserByAdmin ended with : " + System.currentTimeMillis());
		return deleteUserByAdmin;
	}

	@GetMapping("/admin/{adid}")
	public List<User> getUserByAdmin(@PathVariable("adid") String adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getUserByAdmin",
				"getUserByAdmin started on : " + System.currentTimeMillis());
		List<User> userDetailsByAdmin = userDetailsService.getUserDetailsByAdmin(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "getUserByAdmin",
				"getUserByAdmin ended with : " + System.currentTimeMillis());
		return userDetailsByAdmin;
	}

	@PutMapping("/inactiveByadid")
	public void inactiveByadid(@RequestBody List<String> adid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "inactiveByadid",
				"inactiveByadid started on : " + System.currentTimeMillis());
		userDetailsDao.inactiveByadid(adid);
		LoggerUtil.log(this.getClass(), Level.INFO, "inactiveByadid",
				"inactiveByadid ended with : " + System.currentTimeMillis());

	}

	@PutMapping("/updateracfId/{adid}/{racfid}")
	public void updateracfId(@PathVariable("adid") String adid, @PathVariable("racfid") String racfid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateracfId",
				"updateracfId started on : " + System.currentTimeMillis());
		userDetailsDao.updateracfId(adid, racfid);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateracfId",
				"updateracfId ended with : " + System.currentTimeMillis());
	}

	@GetMapping("/getReporteeAdids/{managerAdId}")
	public ReporteeAdidsPojo getReporteeAdids(@PathVariable("managerAdId") String managerAdId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getReporteeAdids",
				"getReporteeAdids started on : " + System.currentTimeMillis());

		ReporteeAdidsPojo reporteeAdids = userDetailsDao.getReporteeAdids2(managerAdId);
		LoggerUtil.log(this.getClass(), Level.INFO, "getReporteeAdids",
				"getReporteeAdids ended with : " + System.currentTimeMillis());
		return reporteeAdids;

	}

	@PutMapping("/updateByManagerAdid/{managerAdid}")
	public void updateByManagerAdid(@PathVariable("managerAdid") String managerAdid) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateByManagerAdid",
				"updateByManagerAdid started on : " + System.currentTimeMillis());
		userDetailsDao.updateByManagerAdid(managerAdid);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateByManagerAdid",
				"updateByManagerAdid ended with : " + System.currentTimeMillis());
	}

	@PutMapping("/updateCorresManagerAdid/{managerAdid}/{racfid}/{adid}/{sessionStatus}/{sessionDate}")
	public void updateCorresManagerAdid(@PathVariable("managerAdid") String managerAdid,
			@PathVariable("racfid") String racfid, @PathVariable("adid") String adid,
			@PathVariable("sessionStatus") String sessionStatus,
			@PathVariable("sessionDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime sessionDate) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateCorresManagerAdid",
				"updateCorresManagerAdid started on : " + System.currentTimeMillis());
		userDetailsDao.updateCorresManagerAdid(managerAdid, racfid, adid, sessionStatus, sessionDate);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateCorresManagerAdid",
				"updateCorresManagerAdid ended with : " + System.currentTimeMillis());
	}

	@PutMapping("/userSession")
	public String updateUserSession(@RequestBody User user) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserSession",
				"updateUserSession started on : " + System.currentTimeMillis());
		String updateUserSession = userDetailsService.updateUserSession(user);
		LoggerUtil.log(this.getClass(), Level.INFO, "updateUserSession",
				"updateUserSession ended with : " + System.currentTimeMillis());
		return updateUserSession;
	}

	@GetMapping("/racfId")
	public List<String> getRacfId(@RequestParam("ldapId") String ldapId, @RequestParam("ptype") String ptype,
			@RequestParam(value = "fetchall", required = false, defaultValue = "false") Boolean fetchall) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getRacfId",
				"getRacfId started on : " + System.currentTimeMillis());

		ArrayList<String> racfList = new ArrayList<String>();

		if (ptype.equals("racf")) {
			racfList.addAll(userDetailsDao.fetchRacfid(ldapId));

			if (fetchall)
				racfList.addAll(userDetailsDao.fetchRacfidManager(ldapId));
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getRacfId",
				"getRacfId ended with : " + System.currentTimeMillis());
		return racfList;
	}

}
