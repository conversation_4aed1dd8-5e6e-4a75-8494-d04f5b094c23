package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaUpdateConfigDto;
import com.wipro.fipc.service.ITbaReRunService;
import com.wipro.fipc.utils.CustomBeanUtils;

@RestController
@RequestMapping("/tbaReRun")
public class TbaReRunController {

	@Autowired
	private ITbaReRunService tbaService;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	@PostMapping("/savenew")
	public ResponseEntity<String> saveReRun(@RequestBody List<TbaUpdateConfigDto> tbaList,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveReRun()", "TbaReRunController-->saveReRun()-->starts:");
		try {
			String response = this.tbaService.saveReRun(tbaList, appName, sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, "saveReRun()1", "TbaReRunController-->saveReRun()--ends.");
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "saveReRun()2",
					"TbaReRunController-->saveReRun()--Exception: " + e.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/get/tbaReRunData")
	public Object getTbaReRunDataVapt(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaReRunData()",
				"TbaReRunController-->getTbaReRunData()-->starts:");
		try {
			List<TbaUpdateConfigDto> tbaList = this.tbaService.getTbaReRunData("process_job_mapping_id", pjmId);
			LoggerUtil.log(this.getClass(), Level.INFO, "getTbaReRunData()1",
					"TbaReRunController-->getTbaReRunData()--ends.");
			return tbaList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaReRunData()2",
					"TbaReRunController-->getTbaReRunData()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CONNECTION_REFUSED))
				return HolmesAppConstants.DB_DOWN;
			return e.getMessage();
		}
	}

	@GetMapping("/get/eventHistoryMaster")
	public Object getEventHistoryMasterVapt(@RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "geteventHistoryMaster()",
				"TbaReRunController-->geteventHistoryMaster()-->starts:");
		try {
			String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientCode));
			String res = this.tbaService.getEventHistoryMaster("client_id", clientId);
			LoggerUtil.log(this.getClass(), Level.INFO, "geteventHistoryMaster()1",
					"TbaReRunController-->geteventHistoryMaster()--ends.");
			return res;
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "geteventHistoryMaster()2",
					"TbaReRunController-->geteventHistoryMaster()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CONNECTION_REFUSED))
				return HolmesAppConstants.DB_DOWN;
			return e.getMessage();
		}
	}

	@PostMapping(value = "/deletererunnew")
	public ResponseEntity<String> deleteReRunConfig(@RequestBody List<CommonDeleteDTO> entities,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteReRunConfig()",
				"TbaReRunController-->deleteReRunConfig()-->starts:");
		try {
			String response = tbaService.deleteReRunConfig(entities, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
