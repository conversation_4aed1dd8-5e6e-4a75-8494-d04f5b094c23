package com.wipro.fipc.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaWelfareConfigDto;
import com.wipro.fipc.service.TbaWelfareConfigService;

@RestController
@RequestMapping("/tbaWelfareConfig")
public class TbaWelfareConfigController {

	@Autowired
	private TbaWelfareConfigService tbaWelfareConfigService;

	@PostMapping(value = "/createTbaWelfareInquiryConfig")
	public ResponseEntity<String> createTbaWelfareInquiryConfig(@RequestBody List<TbaWelfareConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {

		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaWelfareInquiryConfig",
				"TbaWelfareConfigController , createTbaWelfareInquiryConfig started on : "
						+ System.currentTimeMillis());
		try {
			String response = tbaWelfareConfigService.createTbaWelfareInquiryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "createTbaWelfareConfig",
					"Unable to save TbaWelfareInquiryConfig",ex);
			return new ResponseEntity<>(HolmesAppConstants.FAILED + ": " + HolmesAppConstants.SAVE_FAILURE_MESSAGE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/updateTbaWelfareInquiryConfig")
	public ResponseEntity<String> updateTbaWelfareInquiryConfig(@RequestBody List<TbaWelfareConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updateTbaWelfareInquiryConfig",
				"TbaWelfareConfigController , updateTbaWelfareInquiryConfig started on : "
						+ System.currentTimeMillis());
		try {
			String response = tbaWelfareConfigService.updateTbaWelfareInquiryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.ACCEPTED);
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "updateTbaWelfareInquiryConfig",
					"Unable to udpate TbaWelfareInquiryConfig.",ex);
			return new ResponseEntity<>(HolmesAppConstants.FAILED + ": " + HolmesAppConstants.UPDATE_FAILURE_MESSAGE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deleteTbaWelfareInquiryConfig")
	public ResponseEntity<String> deleteTbaWelfareInquiryConfig(@RequestBody List<CommonDeleteDTO> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTbaWelfareInquiryConfig",
				"TbaWelfareConfigController , deleteTbaWelfareInquiryConfig started on : "
						+ System.currentTimeMillis());
		try {
			String response = tbaWelfareConfigService.deleteTbaWelfareInquiryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception ex) {
			LoggerUtil.log(getClass(), Level.ERROR, "deleteTbaWelfareInquiryConfig",
					"Unable to delete the TbaWelfareInquiryConfig.",ex);
			return new ResponseEntity<>(HolmesAppConstants.FAILED + ": " + HolmesAppConstants.DELETE_FAILURE_MESSAGE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getTbaWelfareInquiryConfig")
	public ResponseEntity<String> getTbaWelfareInquiryConfig(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaEventInquiryConfig",
				"TbaWelfareConfigController , getTbaEventInquiryConfig started on : " + System.currentTimeMillis());
		try {
			String response= tbaWelfareConfigService.getTbaWelfareInquiryConfig("process_job_mapping_id", pjmId);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (IllegalAccessException | InvocationTargetException | JsonProcessingException ex) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaWelfareInquiryConfig", "Unable to fetch the TbaWelfareInquiryConfig.",ex);
			return new ResponseEntity<>(HolmesAppConstants.FAILED + ": " + HolmesAppConstants.GET_FAILURE_MESSAGE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
