package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaCommentInquiry;
import com.wipro.fipc.service.ITbaCommentInquiryService;

@RestController
@RequestMapping("/tbaCommentInquiry")
public class TbaCommentInquiryController {

	@Autowired
	private ITbaCommentInquiryService commentService;

	@GetMapping("/get/tbaCommentData")
	public Object getTbaCommentDataVapt(@RequestParam String pjmId, @RequestParam boolean updateFlag) {
		LoggerUtil.log(this.getClass(), Level.INFO, "tbaCommentData()",
				"TbaCommentInquiryController-->tbaCommentData()-->starts:");
		try {
			List<TbaCommentInquiry> commentList = this.commentService.getTbaCommentData("process_job_mapping_id", pjmId,
					updateFlag);
			LoggerUtil.log(this.getClass(), Level.INFO, "tbaCommentData()1",
					"TbaCommentInquiryController-->tbaCommentData()-->ends.");
			return commentList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "tbaCommentData()2",
					"TbaCommentInquiryController-->tbaCommentData()--Exception: " + e.getMessage());
			if (e.getMessage().contains("Connection refused: connect;"))
				return "DBService is down";
			return e.getMessage();
		}
	}

	@PostMapping("/savewithoutduplicatesNew")
	public ResponseEntity<String> saveWithOutDuplicatesNew(@RequestBody List<TbaCommentInquiry> commentList,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "save()", "TbaCommentInquiryController-->save()-->starts:");
		try {
			String res = this.commentService.saveWithOutDuplicates(commentList, appName, sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, "save()1", "TbaCommentInquiryController-->save()--ends.");
			return new ResponseEntity<>(res, HttpStatus.CREATED);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "save()2",
					"TbaCommentInquiryController-->save()--Exception: " + e.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping("/modifywithoutduplicatesNew")
	public ResponseEntity<String> modifyWithOutDuplicatesNew(@RequestBody List<TbaCommentInquiry> commentList,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "modify()", "TbaCommentInquiryController-->modify()-->starts:");
		try {
			String res = this.commentService.modifyWithOutDuplicates(commentList, appName, sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, "modify()1", "TbaCommentInquiryController-->modify()--ends.");
			return new ResponseEntity<>(res, HttpStatus.OK);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "modify()2",
					"TbaCommentInquiryController-->modify()--Exception: " + e.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deleteTbaCommentNew")
	public ResponseEntity<String> deleteTbaCommentConfigs(@RequestBody List<CommonDeleteDTO> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteTbaCommentConfigs",
				"TbaCommentInquiryController , deleteTbaCommentConfigs started on : " + System.currentTimeMillis());
		try {
			String response = commentService.deleteTbaCommentConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
