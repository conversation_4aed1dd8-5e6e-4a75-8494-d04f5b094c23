package com.wipro.fipc.controller;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.entity.batch.HolidayCalendar;
import com.wipro.fipc.entity.batch.KsdConfig;
import com.wipro.fipc.exception.KsdBatchException;
import com.wipro.fipc.model.JobScheduleTimeDto;
import com.wipro.fipc.model.generated.ModelApiResponse;
import com.wipro.fipc.service.IKsdBatchService;

@RestController
@RequestMapping(value = "/ksdcontroller")

public class KsdBatchController {
	public static final String PROCESS_JOB_MAPPING_ID = "process_job_mapping_id";
	public static final String CLIENT_ID = "client_id";
	public static final String METHOD = "method";
	@Autowired
	IKsdBatchService ksdBatchService;

	@Autowired
	ObjectMapper objectMapper;

	@PostMapping(value = "/insertksdrecord", consumes = "application/json") // not found
	public String saveKsdRequestJsonUI(@RequestBody String ksdConfig)
			throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(getClass(), Level.INFO, METHOD,
				"inside insertksdrecord Controller, received request : " + ksdConfig);
		KsdConfig ksdConfigDetails = null;
		try {
			ksdConfigDetails = objectMapper.readValue(ksdConfig, KsdConfig.class);
		} catch (IOException e) {

			LoggerUtil.log(this.getClass(), Level.ERROR, "saveKsdRequestJsonUI", "Exception in saveKsdRequestJsonUI : ",
					e.getMessage());

		}
		return ksdBatchService.saveKsdAndChildDetails(ksdConfigDetails);

	}

	@PostMapping(value = "/updateksdrecords/{ksdConfigId}", consumes = "application/json", produces = "application/json")
	public String updateKsdConfiRecords(@PathVariable(name = "ksdConfigId") String ksdConfigId,
			@RequestBody String ksdConfig) throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside updateKsdMasterConfiRecords Controller, received request : ");

		KsdConfig ksdConfigDetails = null;
		try {
			ksdConfigDetails = objectMapper.readValue(ksdConfig, KsdConfig.class);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateKsdConfiRecords",
					"Exception in updateKsdConfiRecords for object mapper#####: " + e.getMessage());

		}
		return ksdBatchService.updateKsdAndChildDetails(ksdConfigId, ksdConfigDetails);
	}

	@GetMapping(value = "/getksdconfig/{processJobMappingId}")
	public List<KsdConfig> populateKsdConfigRecordsInUI(
			@PathVariable(name = "processJobMappingId") String processJobMappingId) throws KsdBatchException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside populateConfigMasterRecordsInUI Controller for processJobMappingId: ", processJobMappingId);
		return ksdBatchService.getKsdAndChildDetails(PROCESS_JOB_MAPPING_ID, processJobMappingId);

	}

	// API to get job schedule time in scheduler
	@GetMapping(value = "/getscheduletime/{processJobMappingId}")
	public JobScheduleTimeDto getJobScheduleTimeOnPjmId(
			@PathVariable(name = "processJobMappingId") String processJobMappingId) throws KsdBatchException {
		JobScheduleTimeDto jobScheduleResponse = new JobScheduleTimeDto();
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD, "inside getJobScheduleTimeOnPjmId: " + processJobMappingId);
		List<KsdConfig> populatedKsdRecordsForUI = ksdBatchService.getKsdAndChildDetails(PROCESS_JOB_MAPPING_ID,
				processJobMappingId);
		if (populatedKsdRecordsForUI != null && !populatedKsdRecordsForUI.isEmpty()) {
			String jobScheduleTimeFromDB = populatedKsdRecordsForUI.get(0).getJobScheduleTime();
			jobScheduleResponse.setId(String.valueOf(populatedKsdRecordsForUI.get(0).getId()));
			jobScheduleResponse.setJobScheduleTime(jobScheduleTimeFromDB);
		} else {

			jobScheduleResponse.setId("");
			jobScheduleResponse.setJobScheduleTime("");
		}
		return jobScheduleResponse;
	}

	// API to update job schedule time from scheduler
	@PostMapping(value = "/updatescheduletime/{ksdConfigId}", consumes = "application/json", produces = "text/plain")
	public String updateJobScheduleTime(@PathVariable(name = "ksdConfigId") Long ksdConfigId,
			@RequestBody String jobScheduleTimeRequest) throws KsdBatchException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside updateJobScheduleTime Controller, received request : ", jobScheduleTimeRequest);
		JobScheduleTimeDto jobScheduleRequest = null;
		try {
			jobScheduleRequest = objectMapper.readValue(jobScheduleTimeRequest, JobScheduleTimeDto.class);
		} catch (IOException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateJobScheduleTime",
					"Exception in updateJobScheduleTime : ", e.getMessage());

		}
		return ksdBatchService.updateJobScheduleTime(ksdConfigId, jobScheduleRequest);
	}

	// POST call for Holiday calendar table
	@PostMapping(value = "/insertholidays", consumes = "application/json")
	public void insertHolidayCalendarRequestJsonUI(@RequestBody HolidayCalendar holidayCalendar)
			throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside insertHolidayCalendarRequestJsonUI Controller for holidayCalendar: ", holidayCalendar);
		ksdBatchService.saveHolidayCalendarDetails(holidayCalendar);
	}

	// UPDATE call for Holiday calendar table
	@PutMapping(value = "/updateholidays", consumes = "application/json")
	public void updateHolidayCalendarRequestJsonUI(@RequestBody HolidayCalendar holidayCalendar)
			throws KsdBatchException, JsonProcessingException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside updateHolidayCalendarRequestJsonUI Controller for holidayCalendar: ", holidayCalendar);
		ksdBatchService.updateHolidayCalendarDetails(holidayCalendar);
	}

	// GET call for Holiday calendar table
	@GetMapping(value = "/getholidays/{column_name}/{column_value}")
	public String[] retrieveHolidayCalendarRecordsUI(@PathVariable(name = "column_name") String columnName,
			@PathVariable(name = "column_value") String columnValue) throws KsdBatchException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside retrieveHolidayCalendarRecordsUI Controller for column_value: ", columnValue);
		return ksdBatchService.getHolidayCalendarDetails(CLIENT_ID, columnValue);
	}

	@PostMapping("deleteallconfigs/{adid}/{pjmid}/{filetype}")
	public ModelApiResponse deleteAllConfigs(@PathVariable("adid") String adId, @PathVariable("pjmid") long pjmId,
			@PathVariable("filetype") String fileType) {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside deleteAllConfigs Controller for adid,pjmid and filetype: " + adId + "," + pjmId + ","
						+ fileType);
		return ksdBatchService.deleteAllConfigsFromScreen(adId, pjmId, fileType);

	}

	// Get APIs
	@GetMapping(value = "/getksdconfig")
	public List<KsdConfig> getKsdConfigRecordsUI(@RequestParam(name = "pjmId") String processJobMappingId)
			throws KsdBatchException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside getKsdConfigRecordsUI Controller for processJobMappingId: ", processJobMappingId);
		return ksdBatchService.getKsdAndChildDetails(PROCESS_JOB_MAPPING_ID, processJobMappingId);

	}

	@GetMapping(value = "/getscheduletime")
	public JobScheduleTimeDto getJobScheduleTimeBasedOnPjmId(@RequestParam String pjmId) throws KsdBatchException {
		JobScheduleTimeDto jobScheduleResponse = new JobScheduleTimeDto();
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD, "inside getJobScheduleTimeOnPjmId: " + pjmId);
		List<KsdConfig> populatedKsdRecordsForUI = ksdBatchService.getKsdAndChildDetails(PROCESS_JOB_MAPPING_ID, pjmId);
		if (populatedKsdRecordsForUI != null && !populatedKsdRecordsForUI.isEmpty()) {
			String jobScheduleTimeFromDB = populatedKsdRecordsForUI.get(0).getJobScheduleTime();
			jobScheduleResponse.setId(String.valueOf(populatedKsdRecordsForUI.get(0).getId()));
			jobScheduleResponse.setJobScheduleTime(jobScheduleTimeFromDB);
		} else {

			jobScheduleResponse.setId("");
			jobScheduleResponse.setJobScheduleTime("");
		}
		return jobScheduleResponse;
	}

	@GetMapping(value = "/getholidays/client_id")
	public String[] retrieveHolidayCalendarRecordsInUI(@RequestParam(name = "clientId") String clientId)
			throws KsdBatchException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside retrieveHolidayCalendarRecordsInUI Controller for column_value: " + clientId);
		return ksdBatchService.getHolidayCalendarDetails(CLIENT_ID, clientId);
	}

	// Post APIs
	@PostMapping(value = "/updateksdconfigdetails", consumes = "application/json", produces = "application/json")
	public String updateKsdAndClientConfigecords(@RequestBody String ksdConfig, @RequestHeader String appName,
			@RequestHeader String sessionToken) throws KsdBatchException {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD, "inside updateKsdAndClientConfigecords Controller ");

		com.wipro.fipc.model.generated.KsdConfig ksdConfigFromUI = null;
		try {
			ksdConfigFromUI = objectMapper.readValue(ksdConfig, com.wipro.fipc.model.generated.KsdConfig.class);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "updateKsdAndClientConfigecords",
					"Exception in updateKsdConfiRecords for object mapper#####: " + e.getMessage());

		}
		return ksdBatchService.updateKsdAndClientConfigDetails(ksdConfigFromUI, appName, sessionToken);

	}

	@PostMapping("deleteallconfigs/{filetype}")
	public ModelApiResponse deleteAllConfigsFromKSD(@PathVariable("filetype") String fileType,
			@RequestParam(name = "ldapId") String ldapId, @RequestParam(name = "pjmId") long pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, METHOD,
				"inside deleteAllConfigsFromKSD Controller for adid,pjmid and filetype: " + ldapId + "," + pjmId + ","
						+ fileType);
		return ksdBatchService.deleteAllConfigsFromScreen(ldapId, pjmId, fileType);
	}
}
