package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.exception.BusinessException;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.TbaNoticeInquiryConfig;
import com.wipro.fipc.model.generated.TbaNoticeInqConfig;
import com.wipro.fipc.service.ITbaNoticeService;
import com.wipro.fipc.utils.CustomBeanUtils;

@RestController
@RequestMapping("/tbaNotice")

public class TbaNoticeController {

	@Autowired
	private ITbaNoticeService tbaNoticeService;
	
	@Autowired
	CustomBeanUtils customBeanUtils;

	@GetMapping("/get/noticeMaster")
	public Object getNoticeMasterLatest(@RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getNoticeMaster()",
				"TbaNoticeController-->getNoticeMaster()-->starts:");
		try {
			String clientId = Integer.toString(customBeanUtils.checkForClientCode(clientCode));
			String noticeList = this.tbaNoticeService.getNoticeMaster("client_id", clientId);
			LoggerUtil.log(this.getClass(), Level.INFO, "getNoticeMaster()1",
					"TbaNoticeController-->getNoticeMaster()--ends.");
			return noticeList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getNoticeMaster()2",
					"TbaNoticeController-->getNoticeMaster()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CONNECTION_REFUSED))
				return HolmesAppConstants.DB_DOWN;
			return e.getMessage();
		}
	}

	@GetMapping("/get/tbaNoticeData")
	public Object getTbaNoticeDataLatest(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "gettbaNoticeData()",
				"TbaNoticeController-->gettbaNoticeData()-->starts:");
		try {
			List<TbaNoticeInqConfig> noticeList = this.tbaNoticeService.getTbaNoticeData("process_job_mapping_id",
					pjmId);
			LoggerUtil.log(this.getClass(), Level.INFO, "gettbaNoticeData()1",
					"TbaNoticeController-->gettbaNoticeData()--ends.");
			return noticeList;
		} catch (BusinessException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "gettbaNoticeData()2",
					"TbaNoticeController-->gettbaNoticeData()--Exception: " + e.getMessage());
			if (e.getMessage().contains(HolmesAppConstants.CONNECTION_REFUSED))
				return HolmesAppConstants.DB_DOWN;
			return e.getMessage();
		}
	}

	@PostMapping("/create")
	public ResponseEntity<String> saveNotice(@RequestBody List<TbaNoticeInquiryConfig> tbaNoticeList,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "submit()", "TbaNoticeController-->submit()-->starts:");
		try {
			String response = this.tbaNoticeService.saveNotice(tbaNoticeList, appName, sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, "submit()1", "TbaNoticeController-->submit()--ends.");
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (Exception e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "submit()2",
					"TbaNoticeController-->submit()--Exception: " + e.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deleteNoticeNew")
	public ResponseEntity<String> deleteNoticeConfig(@RequestBody List<CommonDeleteDTO> entities,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteNoticeConfig",
				"TbaNoticeController , deleteNoticeConfig started on : " + System.currentTimeMillis());
		try {
			String response = this.tbaNoticeService.deleteNoticeConfig(entities, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
