/**
 * 
 */
package com.wipro.fipc.controller;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.pojo.tba.TbaCutoffDateResponse;
import com.wipro.fipc.service.TbaCutOffDateService;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/tbaCutOffDate")
public class TbaCutOffDateController {
	
	@Autowired
	private TbaCutOffDateService tbaCutOffDateService;
	
	@GetMapping(value = "/insert")
	public ResponseEntity<String> insertTBACutoffDate()   {
		LoggerUtil.log(getClass(), Level.INFO, "insertTBACutoffDate", "inside insertTBACutoffDate Controller : ");		
		try {
			tbaCutOffDateService.readExcelSheet();
		} catch (IOException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "insertTBACutoffDate method in controller", "Exception: -", e);
			return new ResponseEntity<>("Failed" ,HttpStatus.OK);			
		}		
		return new ResponseEntity<>("Success" ,HttpStatus.OK);
	}

	
	@PostMapping(value = "/getTBACutoffDate")
	public List<TbaCutoffDateResponse> getTBACutoffDate(@RequestParam(name = "clientCode") String clientCode)  {
		LoggerUtil.log(getClass(), Level.INFO, "getTBACutoffDate", "inside getTBACutoffDate Controller : ");
		return tbaCutOffDateService.getTbaCutOffDate(clientCode);
		
	}

}
