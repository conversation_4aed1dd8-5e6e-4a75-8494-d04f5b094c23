package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.generated.TbaPendingEvent;
import com.wipro.fipc.service.IFileLayoutService;
import com.wipro.fipc.service.ITbaPendingEventConfigService;

@RestController
@RequestMapping("/tbapendingevent")

public class TbaPendingEventController {

	@Autowired
	ITbaPendingEventConfigService pendingEventConfigService;

	@Autowired
	IFileLayoutService layoutService;

	@PostMapping(value = "/savependingeventnew")
	public ResponseEntity<String> createTbaPendingConfig(@RequestBody List<TbaPendingEvent> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createTbaPendingConfig",
				"TbaPendingEventController , createTbaPendingConfig started on : " + System.currentTimeMillis());
		try {
			String response = pendingEventConfigService.createTbaPendingEventConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "createTbaPendingConfig",
					"Unable to save due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/updatependingeventnew")
	public ResponseEntity<String> updatePendingEventConfig(@RequestBody List<TbaPendingEvent> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "updatePendingEventConfig",
				"TbaPendingEventController , updatePendingEventConfig started on : " + System.currentTimeMillis());
		try {
			String response = pendingEventConfigService.updatePendingEventConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "updatePendingEventConfig",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/gettbapendingevent")
	public ResponseEntity<String> getPendingEventDetailsVapt(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getPendingEventDetailsVapt",
				"TbaPendingEventController , getPendingEventDetailsVapt started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(pendingEventConfigService.getPendingEventDetails(pjmId), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPendingEventDetails",
					"Unable to get records due to1 {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getpendingenventmasterdata")
	public ResponseEntity<String> getPendingEventMasterDetailsVapt(@RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getPendingEventMasterDetailsVapt",
				"TbaPendingEventController , getPendingEventMasterDetailsVapt started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(pendingEventConfigService.getPendingEventMasterDetails(clientCode),
					HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getPendingEventMasterDetails",
					"Unable to get records due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deletependingeventsnew")
	public ResponseEntity<String> deletePendingEventConfig(@RequestBody List<CommonDeleteDTO> entities,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deletePendingEventConfig",
				"TbaPendingEventController , deletePendingEventConfig started on : " + System.currentTimeMillis());
		try {
			String response = pendingEventConfigService.deletePendingEventConfig(entities, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "deletePendingEventConfig",
					"Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
