package com.wipro.fipc.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.EventHistoryUI;
import com.wipro.fipc.model.generated.EventHistoryMaster;
import com.wipro.fipc.service.ITbaEventHistoryService;

@RestController
@RequestMapping("/tbaEventHistory")

public class TbaEventHistoryController {

	@Autowired
	ITbaEventHistoryService tbaEventHistoryService;

	@GetMapping("/getConfigByPjmID")
	public List<EventHistoryUI> getEventHistInqConfigByPJMIDLatest(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByPJMID",
				"TbaEventHistoryController-->getEventHistInqConfigByPJMID()-->starts for pjmID: " + pjmId);
		List<EventHistoryUI> eventHistInqConfigList = tbaEventHistoryService.getEventHistInqConfigByPJMID(pjmId);

		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistInqConfigByPJMIDLatest",
				"TbaEventHistoryController-->getEventHistInqConfigByPJMID()-->ends: ");
		return eventHistInqConfigList;
	}

	@GetMapping("/getMasterByClientID")
	public List<EventHistoryMaster> getEventHistoryMasterByClientIDLatest(@RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistoryMasterByClientID",
				"TbaEventHistoryController--->getEventHistoryMasterByClientID()--->starts for clientID: " + clientCode);

		List<EventHistoryMaster> eventHistoryMasterList = tbaEventHistoryService
				.getEventHistoryMasterByClientID(clientCode);

		LoggerUtil.log(this.getClass(), Level.INFO, "getEventHistoryMasterByClientIDLatest",
				"TbaEventHistoryController--->getEventHistoryMasterByClientID()--->ends: ");

		return eventHistoryMasterList;
	}

	@PostMapping("/savewithoutduplicatesNew")
	public ResponseEntity<String> saveEventHistInqConfigWithOutDuplicates(@RequestBody List<EventHistoryUI> tbaList,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveEventHistInqConfig()",
				"TbaEventHistoryController-->saveEventHistInqConfig()-->starts:");
		try {
			String response = this.tbaEventHistoryService.saveEventHistInqConfigWithOutDuplicates(tbaList, appName,
					sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, "saveEventHistInqConfig()1",
					"TbaEventHistoryController-->saveEventHistInqConfig()--ends.");
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "saveEventHistInqConfig",
					"Unable to save due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping("/modifywithoutduplicatesNew")
	public ResponseEntity<String> modifyEventHistInqConfigWithOutDuplicates(@RequestBody EventHistoryUI tbaList,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "modifyEventHistInqConfig()",
				"TbaEventHistoryController-->modifyEventHistInqConfig()-->starts for input:" + tbaList);
		try {
			String response = this.tbaEventHistoryService.modifyEventHistInqConfigWithOutDuplicates(tbaList, appName,
					sessionToken);
			LoggerUtil.log(this.getClass(), Level.INFO, "modifyEventHistInqConfig()1",
					"TbaEventHistoryController-->modifyEventHistInqConfig()--ends.");
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping(value = "/deleteeventhistoryNew")
	public ResponseEntity<String> deleteEventHistoryConfig(@RequestBody List<EventHistoryUI> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteEventHistoryConfig",
				"TbaEventHistoryController , deleteEventHistoryConfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaEventHistoryService.deleteEventHistoryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
