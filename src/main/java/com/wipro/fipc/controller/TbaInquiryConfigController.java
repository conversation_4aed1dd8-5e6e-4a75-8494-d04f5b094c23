package com.wipro.fipc.controller;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.model.CommonDeleteDTO;
import com.wipro.fipc.model.PanelDataBean;
import com.wipro.fipc.pojo.tba.TbaInquiryConfigDto;
import com.wipro.fipc.service.IFileLayoutService;
import com.wipro.fipc.service.TbaInquiryConfigService;
import com.wipro.fipc.tba.service.TbaInquiryJsonService;
import com.wipro.fipc.tba.service.TbaInquiryMetaDataService;
import com.wipro.fipc.utils.CustomBeanUtils;

@RestController
@RequestMapping("/tbainquiryconfig")
//@CrossOrigin(origins = "*")
public class TbaInquiryConfigController {

	@Autowired
	TbaInquiryConfigService tbaInquiryConfigService;

	@Autowired
	IFileLayoutService layoutService;

	@Autowired
	private TbaInquiryMetaDataService tbaInquiryMetaDataService;

	@Autowired
	private TbaInquiryJsonService tbaInquiryJsonService;
	
	@Autowired
	private CustomBeanUtils customBeanUtils;

	@PostMapping(value = "/createnew")
	public ResponseEntity<String> saveTbaInquiryConfig(@RequestBody List<TbaInquiryConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveTbaInquiryConfig",
				"TbaInquiryConfigController , saveTbaInquiryConfig started on : " + System.currentTimeMillis());
		String response;
		try {
			response = tbaInquiryConfigService.createTbaInquiryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.CREATED);
		} catch (IllegalAccessException | InvocationTargetException | URISyntaxException | IOException e) {
			LoggerUtil.log(getClass(), Level.ERROR, "saveTbaInquiryConfig", "Unable to save due to {} \n" + e.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@PostMapping(value = "/updatenew")
	public ResponseEntity<String> createtbainquiryconfig(@RequestBody List<TbaInquiryConfigDto> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "createtbainquiryconfig",
				"TbaInquiryConfigController , createtbainquiryconfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaInquiryConfigService.updateTbaInquiryConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.ACCEPTED);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "createtbainquiryconfig",
					"Unable to udpate due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getAllTbaUpdateConfig")
	public ResponseEntity<String> getAllTbaUpdateConfig() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllTbaUpdateConfig",
				"TbaInquiryConfigController , getAllTbaUpdateConfig started on : " + System.currentTimeMillis());
		return new ResponseEntity<>(tbaInquiryConfigService.getAllTbaInquiryConfig(), HttpStatus.OK);
	}

	@GetMapping(value = "/getTbaInquiryConfig")
	public Object getTbaInquiryConfigVapt(@RequestParam String pjmId) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaInquiryConfigVapt",
				"TbaInquiryConfigController , getTbaInquiryConfigVapt started on : " + System.currentTimeMillis());
		try {
			return tbaInquiryConfigService.getTbaInquiryConfig("process_job_mapping_id", pjmId);
		} catch (IOException |IllegalAccessException | InvocationTargetException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaInquiryConfigVapt",
					"Exception : "+e.getMessage());
			return e.getMessage();
		}
	}

	@GetMapping(value = "/getTbaProcessInquiry")
	public ResponseEntity<String> getTbaUpdateProcessVapt(@RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateProcessVapt",
				"TbaInquiryConfigController , getTbaUpdateProcessVapt started on : " + System.currentTimeMillis());
		try {
			int clientId = customBeanUtils.checkForClientCode(clientCode);
			return new ResponseEntity<>(tbaInquiryConfigService.getTbaProcessInquiry(clientId), HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method",
					"Unable to get getTbaUpdateProcess due to {} \n" + exception.getMessage());
			return new ResponseEntity<>(HolmesAppConstants.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getTbaInquiryJsonKey")
	public ResponseEntity<String> getTbaInquiryJsonKeyVapt(@RequestParam String parNm) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaInquiryJsonKeyVapt",
				"TbaInquiryConfigController , getTbaInquiryJsonKeyVapt started on : " + System.currentTimeMillis());
		return new ResponseEntity<>(tbaInquiryConfigService.getTbaInquiryJsonKey(parNm), HttpStatus.OK);
	}

	@GetMapping(value = "/getTbaInquiryJsonKeyByDataFilter")
	public ResponseEntity<String> getTbaInquiryJsonKeyVaptByDataFilter(@RequestParam String parNm,
			@RequestParam String dataFilter) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaInquiryJsonKeyVapt",
				"TbaInquiryConfigController , getTbaInquiryJsonKeyVapt started on : " + System.currentTimeMillis());
		try {
			return new ResponseEntity<>(tbaInquiryConfigService.getTbaInquiryJsonKeyByDataFilter(parNm, dataFilter),
					HttpStatus.OK);
		} catch (JsonProcessingException e) {
			LoggerUtil.log(this.getClass(), Level.ERROR, "getTbaInquiryJsonKeyVaptByDataFilter",
					"Exception : " + e.getMessage());
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping(value = "/getTbaMetadata")
	public Object getTbaUpdateMetadataVapt(@RequestParam int panelId, @RequestParam String clientCode) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getTbaUpdateMetadataVapt",
				"TbaInquiryConfigController , getTbaUpdateMetadataVapt started on : " + System.currentTimeMillis());
		int clientId = customBeanUtils.checkForClientCode(clientCode);
		return tbaInquiryConfigService.getTbaMetadata(panelId, clientId);
	}

	@GetMapping("/getAllRecordIdentifier")
	public String getAllRecordIdentifier() {
		LoggerUtil.log(this.getClass(), Level.INFO, "getAllRecordIdentifier",
				"TbaInquiryConfigController , getAllRecordIdentifier started on : " + System.currentTimeMillis());
		return layoutService.getAllRecordIdentifier();
	}

	@PostMapping(value = "/deleteinquiryeventsnew")
	public ResponseEntity<String> deleteInquiryEventConfig(@RequestBody List<CommonDeleteDTO> entity,
			@RequestHeader String appName, @RequestHeader String sessionToken) {
		LoggerUtil.log(this.getClass(), Level.INFO, "deleteInquiryEventConfig",
				"TbaInquiryConfigController , deleteInquiryEventConfig started on : " + System.currentTimeMillis());
		try {
			String response = tbaInquiryConfigService.deleteInquiryEventConfig(entity, appName, sessionToken);
			return new ResponseEntity<>(response, HttpStatus.OK);
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "method", "Unable to update due to {} \n" + exception.getMessage());
			return new ResponseEntity<>("failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	 
	@PostMapping(value = "/save-stv-panel")
	public ResponseEntity<String> saveDataForFilterFlag(@RequestParam String parName, @RequestParam String createdBy) throws IOException {
		LoggerUtil.log(this.getClass(), Level.INFO, "saveStvPanelData",
				"TbaInquiryConfigController , saveStvPanelData started on : " + System.currentTimeMillis());

		try {
			InputStream inputStream = new FileInputStream("stv-panel.xml");
			tbaInquiryJsonService.saveDataForFilterFlag(inputStream, parName, createdBy);
		} catch (Exception e) {
			LoggerUtil.log(getClass(), Level.ERROR, "saveStvPanelData", "Unable to update due to {} \n" + e.getMessage());
			return new ResponseEntity<>("Some Error Occurred", HttpStatus.INTERNAL_SERVER_ERROR);
		}

		return new ResponseEntity<>("Service Executed Successfully", HttpStatus.OK);
	}

	@GetMapping(value = "/getInquiryMetaData")
	public ResponseEntity<List<PanelDataBean>> getInquiryMetaData(@RequestParam String clientCode, @RequestParam int panelId, @RequestParam String parName) {
		LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaData",
				"getInquiryMetaData Method Execution Started clientCode - " + clientCode + ", panelId - " + panelId + ", parName - " + parName);

		try {
			InputStream inputStream = new FileInputStream("stv-panel.xml");
			Map<String, String> arrayAttributes = tbaInquiryJsonService.fetchArrayAttributes(inputStream, parName);

			if(!CollectionUtils.isEmpty(arrayAttributes)) {
				int clientId = customBeanUtils.checkForClientCode(clientCode);
				List<PanelDataBean> inquiryMetaDatas = tbaInquiryMetaDataService.getInquiryMetaDataPanelWise(clientId, panelId, arrayAttributes);
				LoggerUtil.log(this.getClass(), Level.INFO, "getInquiryMetaData", "inquiryMetaData List Fetched Size - " + inquiryMetaDatas.size());

				return new ResponseEntity<>(inquiryMetaDatas, HttpStatus.OK);
			} else {
				LoggerUtil.log(getClass(), Level.INFO, "getInquiryMetaData", "Getting Empty panelDatas for parName - " + parName);
				return new ResponseEntity<>(null, HttpStatus.OK);
			}
		} catch (Exception exception) {
			LoggerUtil.log(getClass(), Level.ERROR, "getInquiryMetaData", "Some Exception Occurred.", exception);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}