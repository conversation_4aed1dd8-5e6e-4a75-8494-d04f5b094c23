package com.wipro.fipc.test;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.wipro.fipc.constants.HolmesAppConstants;
import com.wipro.fipc.entity.notification.NotificationMailConfigEntity;
import com.wipro.fipc.model.generated.LayoutConfig;
import com.wipro.fipc.model.generated.ProcessJobMapping;

public class testdata {

	public static void main(String[] args) {
		
		try {
//			List<LayoutConfig> layoutCongfigDOList =new ArrayList();
//			ProcessJobMapping obj1=new ProcessJobMapping();
//			LayoutConfig obj2=new LayoutConfig();
////			obj2.setId((long) 34);
////			obj2.setActiveFlag("T");
////			obj2.fieldTemplate("Test");
////			obj1.setId((long) 34);
////			obj1.setActiveFlag("T");
////			obj2.setProcessJobMappingConfig(obj1);
//			String data="{\"id\":243044,\"fileName\":\"Mainframe File \",\"recordType\":\"Header Record\",\"mfFieldName\":\"Header Label\",\"startPos\":\"1\",\"recordIdentifierVal\":\"1\",\"length\":4,\"activeFlag\":\"T\",\"recordFormat\":\"X(4)\",\"fieldType\":\"Text\",\"fieldTemplate\":\"\",\"mandatory\":null,\"recordIdentifier\":\"Job\",\"pptIdentifier\":null,\"mfFieldWoutSpace\":\"headerLabel\",\"dateFormat\":null,\"fieldNo\":0,\"createdDate\":1594600717178,\"createdBy\":\"goutami\",\"updatedBy\":\"goutami\",\"updatedDate\":1594600717178,\"fileNameWoutSpace\":\"mainframeFile\",\"sheetName\":\"\",\"processJobMappingConfig\":{\"id\":5846,\"jobName\":\"PB0285C2\",\"activeFlag\":\"T\",\"createdDate\":1587607663976,\"eftSubject\":\"DECEASED DBP PPTS WITH BILL OR DIRECT DEBIT\",\"createdBy\":\"ADMIN\",\"updatedBy\":null,\"ksdName\":\"PC12590\",\"updatedDate\":null,\"businessUnitOps\":{\"id\":2,\"createdDate\":1585867685565,\"createdBy\":\"ADMIN\",\"updatedBy\":null,\"updatedDate\":null},\"clientDetails\":{\"id\":1,\"businessUnitClients\":[{\"id\":1,\"createdDate\":1585867685674,\"createdBy\":\"ADMIN\",\"updatedBy\":null,\"updatedDate\":null}],\"wbClientCode\":\"M0285\",\"clientCode\":\"00285\",\"clientName\":\"3M Company\",\"createdDate\":1585867685604,\"createdBy\":\"ADMIN\",\"updatedBy\":null,\"groupCode\":\"NONE\",\"groupName\":null,\"updatedDate\":null},\"process\":{\"id\":52,\"processName\":\"DB&P\",\"processType\":\"PROCESS CONTROL\",\"createdDate\":1585867685597,\"createdBy\":\"ADMIN\",\"updatedBy\":null,\"updatedDate\":null},\"tower\":\"LM\"},\"sheetNameWoutSpace\":\"\",\"valueDetails\":\"\",\"labellingJson\":null}";
//			obj2=new ObjectMapper().readValue(data, LayoutConfig.class);
//			layoutCongfigDOList.add(obj2);
//			List<com.wipro.holmes.fileLayoutsupport.LayoutConfig> newLayout=new ArrayList();
//			for(LayoutConfig req:layoutCongfigDOList)
//			{
////				com.wipro.holmes.fileLayoutsupport.LayoutConfig obj=
////			new com.wipro.holmes.fileLayoutsupport.
////			LayoutConfig(req.getFileName(), req.getRecordType(), req.getMfFieldName(), req.getStartPos(),
////					req.getRecordIdentifierVal(), req.getLength(), req.getActiveFlag(), req.getRecordFormat(), req.getFieldType(),
////					req.getFieldTemplate(), req.getMandatory(), req.getRecordIdentifier(), req.getPptIdentifier(),
////					req.getMfFieldWoutSpace(), req.getDateFormat(), req.getFieldNo(), req.getCreatedDate(), req.getCreatedBy(),
////					req.getUpdatedBy(), req.getUpdatedDate(), req.getFileNameWoutSpace(), req.getSheetName(),
////					req.getProcessJobMappingConfig(), req.getSheetNameWoutSpace(), req.getValueDetails(),
////					req.getLabellingJson());
//				String convertedString=new ObjectMapper().writeValueAsString(req);
//				System.out.println("converted data is"+convertedString);
//				com.wipro.holmes.fileLayoutsupport.LayoutConfig obj=new ObjectMapper().
//						readValue(convertedString, com.wipro.holmes.fileLayoutsupport.LayoutConfig.class);
//				System.out.println("data111 is"+new ObjectMapper().writeValueAsString(obj));
//				newLayout.add(obj);
//			}
//			System.out.println("data is"+new ObjectMapper().writeValueAsString(newLayout));
			Boolean data=true;
			String data1=new ObjectMapper().writeValueAsString(data);
	System.out.println("condition is"+data1);
		}catch(Exception e)
		{
			e.printStackTrace();
		}

	}

}
