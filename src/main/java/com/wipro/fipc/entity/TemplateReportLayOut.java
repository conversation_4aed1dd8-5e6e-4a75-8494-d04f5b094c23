package com.wipro.fipc.entity;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonBackReference;
//import com.wipro.holmes.common.dbservice.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@Table(name = "TEMPLATE_REPORT_LAYOUT", schema = "common")
public class TemplateReportLayOut extends BaseModel {
	private static final long serialVersionUID = 4171213959528972220L;
	@Column(name = "data_element")
	private String dataElement;

	@Column(name = "template_report_name")
	private String templateReportName;

	@Column(name = "sheet_name")
	private String sheetName;

	@Column(name = "identifier")
	private String identifier;

	@Column(name = "identifier_id")
	private String identifierId;

	@Column(name = "sequence")
	private String sequence;

	@Column(name = "filed_type")
	private String filedType;

	@Column(name = "active_flag")
	private char activeFlag;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "template_report_name_ws")
	private String templateReportNameWs;

	@Column(name = "data_element_ws")
	private String dataElementWs;

	@Column(name = "sheet_name_ws")
	private String sheetNameWs;

	@Column(name = "identifier_ws")
	private String identifierWs;

	@ManyToOne(cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
	@JoinColumn(name = "template_report_upload_id")
	@JsonBackReference(value = "templateReportUploadRef")
	private TemplateReportUpload templateReportUpload;

	/*
	 * @ManyToOne
	 * 
	 * @JoinColumn(name="template_report_upload_id") private TemplateReportUpload
	 * templateReportUpload;
	 */

	@Column(name = "labelling_report_record")
	private String labellingReportRecord;

	public String getDataElement() {
		return dataElement;
	}

	public void setDataElement(String dataElement) {
		this.dataElement = dataElement;
	}

	public String getTemplateReportName() {
		return templateReportName;
	}

	public void setTemplateReportName(String templateReportName) {
		this.templateReportName = templateReportName;
	}

	public String getSheetName() {
		return sheetName;
	}

	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public String getIdentifierId() {
		return identifierId;
	}

	public void setIdentifierId(String identifierId) {
		this.identifierId = identifierId;
	}

	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	public String getFiledType() {
		return filedType;
	}

	public void setFiledType(String filedType) {
		this.filedType = filedType;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getTemplateReportNameWs() {
		return templateReportNameWs;
	}

	public void setTemplateReportNameWs(String templateReportNameWs) {
		this.templateReportNameWs = templateReportNameWs;
	}

	public String getDataElementWs() {
		return dataElementWs;
	}

	public void setDataElementWs(String dataElementWs) {
		this.dataElementWs = dataElementWs;
	}

	public String getSheetNameWs() {
		return sheetNameWs;
	}

	public void setSheetNameWs(String sheetNameWs) {
		this.sheetNameWs = sheetNameWs;
	}

	public String getIdentifierWs() {
		return identifierWs;
	}

	public void setIdentifierWs(String identifierWs) {
		this.identifierWs = identifierWs;
	}

	public TemplateReportUpload getTemplateReportUpload() {
		return templateReportUpload;
	}

	public void setTemplateReportUpload(TemplateReportUpload templateReportUpload) {
		this.templateReportUpload = templateReportUpload;
	}

	public String getLabellingReportRecord() {
		return labellingReportRecord;
	}

	public void setLabellingReportRecord(String labellingReportRecord) {
		this.labellingReportRecord = labellingReportRecord;
	}

}
