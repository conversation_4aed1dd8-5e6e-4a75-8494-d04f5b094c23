package com.wipro.fipc.entity.filelayout;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.wipro.fipc.entity.BaseModel;


@Entity
@Table(name = "database_config", schema = "common")
public class DatabaseConfig extends BaseModel implements Serializable {
	
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	
	@Column(name = "database_id", nullable = false)
	private String databaseId;
	
	 @Column(name = "client_code")
	private String clientCode;

	@Column(name = "client_name")
	private String clientName;

	public DatabaseConfig(Long id, String databaseId, String clientCode, String clientName) {
		super();
		this.id = id;
		this.databaseId = databaseId;
		this.clientCode = clientCode;
		this.clientName = clientName;
	}

	public DatabaseConfig() {
		super();
		
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDatabaseId() {
		return databaseId;
	}

	public void setDatabaseId(String databaseId) {
		this.databaseId = databaseId;
	}

	public String getClientCode() {
		return clientCode;
	}

	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}

	public String getClientName() {
		return clientName;
	}

	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
