package com.wipro.fipc.entity.filelayout;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;
import com.wipro.fipc.entity.common.RulesDefinition;

@Entity
@Table(name = "RULES_CONFIG", schema = "layout_rule")
@Where(clause="active_flag='T'")
public class RulesConfig extends BaseModel implements Serializable {
	private static final long serialVersionUID = 4171213959528972220L;

	@Column(name = "active")
	private String active;
	
	@Column(name = "START_DATE")
	private Date startDate;
	
	@Column(name = "end_date")
	private Date endDate;
	
	@Column(name = "active_flag")
	private char activeFlag;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@ManyToOne
    @JoinColumn(name="process_job_mapping_id")
    private ProcessJobMapping processJobMapping;
	
	
	
	@OneToMany(cascade= CascadeType.ALL, fetch = FetchType.LAZY,mappedBy="rulesConfig")//,orphanRemoval = true
	
	@JsonManagedReference(value= "rulesConfigRef")
    private List<RulesDefinition> rulesDefinitions=new ArrayList<RulesDefinition>();

	public String getActive() {
		return active;
	}

	public void setActive(String active) {
		this.active = active;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public List<RulesDefinition> getRulesDefinitions() {
		return rulesDefinitions;
	}

	public void setRulesDefinitions(List<RulesDefinition> rulesDefinitions) {
		this.rulesDefinitions = rulesDefinitions;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
		


}
