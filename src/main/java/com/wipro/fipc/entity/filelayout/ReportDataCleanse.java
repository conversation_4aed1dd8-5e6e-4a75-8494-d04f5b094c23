package com.wipro.fipc.entity.filelayout;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "REPORT_DATA_CLEANSE", schema = "layout_rule")
@Where(clause="active_flag='T'")
public class ReportDataCleanse extends BaseModel {
	@Column(name = "file_name")
	private String fileName;
	
	@Column(name = "data_element")
	private String dataElement;
	
	
	@Column(name = "record_identifier")
	private String recordIdentifier;
	
	@Column(name = "action")
	private String action;
	
	@Column(name = "values")
	private String values;
	
	@Column(name = "length")
	private String length;
	
	@Column(name = "active_flag")
	private char activeFlag;
			
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="process_job_mapping_id")
    private ProcessJobMapping processJobMapping;
	
	
	@ManyToOne (cascade=CascadeType.ALL,fetch = FetchType.LAZY)
    @JoinColumn(name="file_detail_id" )
	@JsonBackReference(value="reportDataCleanseRef")	
	private KsdOutPutFileDetails reportDataCleanse;


	public String getFileName() {
		return fileName;
	}


	public void setFileName(String fileName) {
		this.fileName = fileName;
	}


	public String getDataElement() {
		return dataElement;
	}


	public void setDataElement(String dataElement) {
		this.dataElement = dataElement;
	}


	public String getRecordIdentifier() {
		return recordIdentifier;
	}


	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}


	public String getAction() {
		return action;
	}


	public void setAction(String action) {
		this.action = action;
	}


	public String getValues() {
		return values;
	}


	public void setValues(String values) {
		this.values = values;
	}


	public String getLength() {
		return length;
	}


	public void setLength(String length) {
		this.length = length;
	}


	public char getActiveFlag() {
		return activeFlag;
	}


	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}


	public Date getCreatedDate() {
		return createdDate;
	}


	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}


	public String getCreatedBy() {
		return createdBy;
	}


	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}


	public String getUpdatedBy() {
		return updatedBy;
	}


	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}


	public Date getUpdatedDate() {
		return updatedDate;
	}


	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}


	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}


	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}


	public KsdOutPutFileDetails getReportDataCleanse() {
		return reportDataCleanse;
	}


	public void setReportDataCleanse(KsdOutPutFileDetails reportDataCleanse) {
		this.reportDataCleanse = reportDataCleanse;
	}
	
}
