package com.wipro.fipc.entity.filelayout;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.springframework.web.bind.annotation.CrossOrigin;

import com.wipro.fipc.entity.BaseModel;


@Entity
@Table(name = "RULES_VALIDATION_TYPE", schema = "layout_rule")
@CrossOrigin(origins = "*")
public class ValidationType extends BaseModel {

	@Column(name = "val_type_name")
	private String valTypeName;
	
	@Column(name = "val_type_code")
	private String valTypeCode;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	public String getValTypeName() {
		return valTypeName;
	}

	public void setValTypeName(String valTypeName) {
		this.valTypeName = valTypeName;
	}

	public String getValTypeCode() {
		return valTypeCode;
	}

	public void setValTypeCode(String valTypeCode) {
		this.valTypeCode = valTypeCode;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	
}
