package com.wipro.fipc.entity.filelayout;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "REPORT_FORMAT", schema = "layout_rule")
@Where(clause="active_flag='T'")
public class ReportFormat extends BaseModel {

	@Column(name = "file_name")
	private String fileName;
	
	@Column(name = "column_name")
	private String columnName;
	
	@JsonRawValue
	@Column(name = "font")
	private String font;

	@JsonRawValue
	@Column(name = "border")
	private String border;

	@JsonRawValue
	@Column(name = "alignment")
	private String alignment;

	@JsonRawValue
	@Column(name = "pattern_fill")
	private String patternFill;
	
	@Column(name = "freezes")
	private String freezes;
	
	@Column(name = "record_identifier")
	private String recordIdentifier;
	
	@Column(name = "active_flag")
	private char activeFlag;
	
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="process_job_mapping_id")
    private ProcessJobMapping processJobMapping;
	
	@ManyToOne (cascade=CascadeType.ALL,fetch = FetchType.LAZY)
    @JoinColumn(name="file_detail_id" )
	@JsonBackReference(value="reportFormatRef")	
	private KsdOutPutFileDetails reportFormat;

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

	public String getFont() {
		return font;
	}

	public void setFont(String font) {
		this.font = font;
	}

	public String getBorder() {
		return border;
	}

	public void setBorder(String border) {
		this.border = border;
	}

	public String getAlignment() {
		return alignment;
	}

	public void setAlignment(String alignment) {
		this.alignment = alignment;
	}

	public String getPatternFill() {
		return patternFill;
	}

	public void setPatternFill(String patternFill) {
		this.patternFill = patternFill;
	}

	public String getFreezes() {
		return freezes;
	}

	public void setFreezes(String freezes) {
		this.freezes = freezes;
	}

	public String getRecordIdentifier() {
		return recordIdentifier;
	}

	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public KsdOutPutFileDetails getReportFormat() {
		return reportFormat;
	}

	public void setReportFormat(KsdOutPutFileDetails reportFormat) {
		this.reportFormat = reportFormat;
	}

	

}
