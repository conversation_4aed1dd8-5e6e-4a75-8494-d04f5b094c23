 package com.wipro.fipc.entity.filelayout;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "PROCESS_CONTROL_CONFIG", schema = "tba")
@Where(clause="active_flag='T'")
public class ProcessControlConfig extends BaseModel implements Serializable {
	
	@Column(name = "application")
	@JsonProperty("application")
	private String application;
	
	@Column(name = "active_flag")
	@JsonProperty("activeFlag")
	private char activeFlag;
	@Column(name = "field_name")
	@JsonProperty("fieldName")
	private String fieldName;
	
	@Column(name = "field_name_wout_space")
	@JsonProperty("fieldNameWoutSpace")
	private String fieldNameWoutSpace;
	
	@Column(name = "rule_name")
	@JsonProperty("ruleName")
	private String ruleName;
	
	@Column(name = "actions")
	@JsonProperty("actions")
	private String actions;	
	
	@Column(name = "actions_bytea")
	@JsonProperty("byteaActions")
	private byte[] byteaActions;
	
	@Column(name = "identifier")
	@JsonProperty("identifier")
	private String identifier;
	
	@Column(name = "corrective_action")
	@JsonProperty("correctiveAction")
	private String correctiveAction;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	@JsonProperty("createdDate")
	private Date createdDate;

	@Column(name = "created_by")
	@JsonProperty("createdBy")
	private String createdBy;

	@Column(name = "updated_by")
	@JsonProperty("updatedBy")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	@JsonProperty("updatedDate")
	private Date updatedDate;
	
	@ManyToOne
    @JoinColumn(name="process_job_mapping_id")
	//@JsonBackReference(value="processjobmappingRef")
	@JsonProperty("processJobMapping")
    private ProcessJobMapping processJobMapping;
	

	@Column(name = "application_wout_space")
	@JsonProperty("applicationWoutSpace")
	private String applicationWoutSpace;


	public String getApplication() {
		return application;
	}


	public void setApplication(String application) {
		this.application = application;
	}


	public char getActiveFlag() {
		return activeFlag;
	}


	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}


	public String getFieldName() {
		return fieldName;
	}


	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}


	public String getFieldNameWoutSpace() {
		return fieldNameWoutSpace;
	}


	public void setFieldNameWoutSpace(String fieldNameWoutSpace) {
		this.fieldNameWoutSpace = fieldNameWoutSpace;
	}


	public String getRuleName() {
		return ruleName;
	}


	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}


	public String getActions() {
		return actions;
	}


	public void setActions(String actions) {
		this.actions = actions;
	}


	public byte[] getByteaActions() {
		return byteaActions;
	}


	public void setByteaActions(byte[] byteaActions) {
		this.byteaActions = byteaActions;
	}


	public String getIdentifier() {
		return identifier;
	}


	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}


	public String getCorrectiveAction() {
		return correctiveAction;
	}


	public void setCorrectiveAction(String correctiveAction) {
		this.correctiveAction = correctiveAction;
	}


	public Date getCreatedDate() {
		return createdDate;
	}


	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}


	public String getCreatedBy() {
		return createdBy;
	}


	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}


	public String getUpdatedBy() {
		return updatedBy;
	}


	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}


	public Date getUpdatedDate() {
		return updatedDate;
	}


	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}


	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}


	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}


	public String getApplicationWoutSpace() {
		return applicationWoutSpace;
	}


	public void setApplicationWoutSpace(String applicationWoutSpace) {
		this.applicationWoutSpace = applicationWoutSpace;
	}


	@Override
	public String toString() {
		return "ProcessControlConfig [application=" + application + ", activeFlag=" + activeFlag + ", fieldName="
				+ fieldName + ", fieldNameWoutSpace=" + fieldNameWoutSpace + ", ruleName=" + ruleName + ", actions="
				+ actions + ", byteaActions=" + Arrays.toString(byteaActions) + ", identifier=" + identifier
				+ ", correctiveAction=" + correctiveAction + ", createdDate=" + createdDate + ", createdBy=" + createdBy
				+ ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate + ", processJobMapping="
				+ processJobMapping + ", applicationWoutSpace=" + applicationWoutSpace + "]";
	}

	
}

