package com.wipro.fipc.entity.filelayout;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "KSD_OUTPUT_FILE_DETAILS", schema = "layout_rule")
@Where(clause = "active_flag='T'")
public class KsdOutPutFileDetails extends BaseModel {
	@Column(name = "file_name")
	private String fileName;

	@Column(name = "file_type")
	private String fileType;

	@Column(name = "date_format")
	private String dateFormat;

	@Column(name = "mirror_file_name")
	private String mirrorFileName;

	@Column(name = "variation")
	private int variation;

	@Column(name = "subject")
	private String subject;

	@Column(name = "sender")
	private String sender;

	@Column(name = "sheet_name")
	private String sheetName;

	@Column(name = "delimiter")
	private String delimiter;

	@Column(name = "min_threshold")
	private int minThreshold;

	@Column(name = "max_threshold")
	private int maxThreshold;

	@Column(name = "ppt_identifier")
	private String pptIdentifier;

	@Column(name = "ppt_identifier_type")
	private String pptIdentifierType;

	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "generate_report")
	private String generateReport;

	@Column(name = "refer_report")
	private String referReport;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Column(name = "sheet_name_wout_space")
	private String sheetNameWoutSpace;

	@Column(name = "file_name_wout_space")
	private String fileNameWoutSpace;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "use_templete")
	private String useTemplete;

	@ManyToOne
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMapping;

	@Column(name = "prev_report_file_name")
	private String prevReportFileName;

	@Column(name = "prev_report_File_name_ws")
	private String prevReportFileNameWs;

	@Column(name = "append_name")
	private String appendName;

	@Column(name = "append_format")
	private String appendFormat;

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "ksdOutPutFileDetails") // ,orphanRemoval
																										// =
																										// true
	// @LazyCollection(LazyCollectionOption.FALSE)
	@JsonManagedReference(value = "participantRecordIdentifierRef")
	private List<ParticipantRecordIdentifier> participantRecordIdentifiers = new ArrayList<ParticipantRecordIdentifier>();

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "outputReport") // ,orphanRemoval
																								// =
																								// true
	@JsonManagedReference(value = "outputReportRef")
	private List<OutputReport> outputReports = new ArrayList<OutputReport>();

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "reportFormat") // ,orphanRemoval
																								// =
																								// true
	@JsonManagedReference(value = "reportFormatRef")
	private List<ReportFormat> reportFormat = new ArrayList<ReportFormat>();

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "reportDataCleanse") // ,orphanRemoval
																									// =
																									// true
	@JsonManagedReference(value = "reportDataCleanseRef")
	private List<ReportDataCleanse> reportDataCleanse = new ArrayList<ReportDataCleanse>();

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "reportPivot") // ,orphanRemoval
																							// =
																							// true
	@JsonManagedReference(value = "reportPivotRef")
	private List<ReportPivot> reportPivot = new ArrayList<ReportPivot>();

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public String getDateFormat() {
		return dateFormat;
	}

	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}

	public String getMirrorFileName() {
		return mirrorFileName;
	}

	public void setMirrorFileName(String mirrorFileName) {
		this.mirrorFileName = mirrorFileName;
	}

	public int getVariation() {
		return variation;
	}

	public void setVariation(int variation) {
		this.variation = variation;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public String getSheetName() {
		return sheetName;
	}

	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}

	public String getDelimiter() {
		return delimiter;
	}

	public void setDelimiter(String delimiter) {
		this.delimiter = delimiter;
	}

	public int getMinThreshold() {
		return minThreshold;
	}

	public void setMinThreshold(int minThreshold) {
		this.minThreshold = minThreshold;
	}

	public int getMaxThreshold() {
		return maxThreshold;
	}

	public void setMaxThreshold(int maxThreshold) {
		this.maxThreshold = maxThreshold;
	}

	public String getPptIdentifier() {
		return pptIdentifier;
	}

	public void setPptIdentifier(String pptIdentifier) {
		this.pptIdentifier = pptIdentifier;
	}

	public String getPptIdentifierType() {
		return pptIdentifierType;
	}

	public void setPptIdentifierType(String pptIdentifierType) {
		this.pptIdentifierType = pptIdentifierType;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getGenerateReport() {
		return generateReport;
	}

	public void setGenerateReport(String generateReport) {
		this.generateReport = generateReport;
	}

	public String getReferReport() {
		return referReport;
	}

	public void setReferReport(String referReport) {
		this.referReport = referReport;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getSheetNameWoutSpace() {
		return sheetNameWoutSpace;
	}

	public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
		this.sheetNameWoutSpace = sheetNameWoutSpace;
	}

	public String getFileNameWoutSpace() {
		return fileNameWoutSpace;
	}

	public void setFileNameWoutSpace(String fileNameWoutSpace) {
		this.fileNameWoutSpace = fileNameWoutSpace;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getUseTemplete() {
		return useTemplete;
	}

	public void setUseTemplete(String useTemplete) {
		this.useTemplete = useTemplete;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public String getPrevReportFileName() {
		return prevReportFileName;
	}

	public void setPrevReportFileName(String prevReportFileName) {
		this.prevReportFileName = prevReportFileName;
	}

	public String getPrevReportFileNameWs() {
		return prevReportFileNameWs;
	}

	public void setPrevReportFileNameWs(String prevReportFileNameWs) {
		this.prevReportFileNameWs = prevReportFileNameWs;
	}

	public String getAppendName() {
		return appendName;
	}

	public void setAppendName(String appendName) {
		this.appendName = appendName;
	}

	public String getAppendFormat() {
		return appendFormat;
	}

	public void setAppendFormat(String appendFormat) {
		this.appendFormat = appendFormat;
	}

	public List<ParticipantRecordIdentifier> getParticipantRecordIdentifiers() {
		return participantRecordIdentifiers;
	}

	public void setParticipantRecordIdentifiers(List<ParticipantRecordIdentifier> participantRecordIdentifiers) {
		this.participantRecordIdentifiers = participantRecordIdentifiers;
	}

	public List<OutputReport> getOutputReports() {
		return outputReports;
	}

	public void setOutputReports(List<OutputReport> outputReports) {
		this.outputReports = outputReports;
	}

	public List<ReportFormat> getReportFormat() {
		return reportFormat;
	}

	public void setReportFormat(List<ReportFormat> reportFormat) {
		this.reportFormat = reportFormat;
	}

	public List<ReportDataCleanse> getReportDataCleanse() {
		return reportDataCleanse;
	}

	public void setReportDataCleanse(List<ReportDataCleanse> reportDataCleanse) {
		this.reportDataCleanse = reportDataCleanse;
	}

	public List<ReportPivot> getReportPivot() {
		return reportPivot;
	}

	public void setReportPivot(List<ReportPivot> reportPivot) {
		this.reportPivot = reportPivot;
	}

	@Override
	public String toString() {
		return "KsdOutPutFileDetails [fileName=" + fileName + ", fileType=" + fileType + ", dateFormat=" + dateFormat
				+ ", mirrorFileName=" + mirrorFileName + ", variation=" + variation + ", subject=" + subject
				+ ", sender=" + sender + ", sheetName=" + sheetName + ", delimiter=" + delimiter + ", minThreshold="
				+ minThreshold + ", maxThreshold=" + maxThreshold + ", pptIdentifier=" + pptIdentifier
				+ ", pptIdentifierType=" + pptIdentifierType + ", activeFlag=" + activeFlag + ", generateReport="
				+ generateReport + ", referReport=" + referReport + ", createdDate=" + createdDate + ", createdBy="
				+ createdBy + ", updatedBy=" + updatedBy + ", sheetNameWoutSpace=" + sheetNameWoutSpace
				+ ", fileNameWoutSpace=" + fileNameWoutSpace + ", updatedDate=" + updatedDate + ", useTemplete="
				+ useTemplete + ", processJobMapping=" + processJobMapping + ", prevReportFileName="
				+ prevReportFileName + ", prevReportFileNameWs=" + prevReportFileNameWs + ", appendName=" + appendName
				+ ", appendFormat=" + appendFormat + ", participantRecordIdentifiers=" + participantRecordIdentifiers
				+ ", outputReports=" + outputReports + ", reportFormat=" + reportFormat + ", reportDataCleanse="
				+ reportDataCleanse + ", reportPivot=" + reportPivot + "]";
	}

	
}
