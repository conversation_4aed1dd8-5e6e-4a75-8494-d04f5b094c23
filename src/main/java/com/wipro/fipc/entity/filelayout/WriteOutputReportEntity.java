package com.wipro.fipc.entity.filelayout;

import java.util.List;
import java.util.Objects;


public class WriteOutputReportEntity {
	private List<KsdOutPutFileDetails> uiJson;

	/**
	 * @return the uiJson
	 */
	public List<KsdOutPutFileDetails> getUiJson() {
		return uiJson;
	}

	/**
	 * @param uiJson the uiJson to set
	 */
	public void setUiJson(List<KsdOutPutFileDetails> uiJson) {
		this.uiJson = uiJson;
	}

	@Override
	public int hashCode() {
		return Objects.hash(uiJson);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		WriteOutputReportEntity other = (WriteOutputReportEntity) obj;
		return Objects.equals(uiJson, other.uiJson);
	}

	@Override
	public String toString() {
		return "WriteOutputReportEntity [uiJson=" + ui<PERSON><PERSON> + "]";
	}
	
}
