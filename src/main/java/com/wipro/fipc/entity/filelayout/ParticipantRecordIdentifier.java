package com.wipro.fipc.entity.filelayout;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "PARTICIPANT_RECORD_IDENTIFIER", schema = "layout_rule")
@Where(clause="active_flag='T'")
public class ParticipantRecordIdentifier extends BaseModel {	

	@Column(name = "record_identifier_name")
	private String recordIdentifierName;
	
	@Column(name = "sequence")
	private String sequence;

	@Column(name = "reoccurance")
	private String reoccurance;
	
	@Column(name = "active_flag")
	private char activeFlag;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="process_job_mapping_id")
    private ProcessJobMapping processJobMapping;
	
	@ManyToOne (cascade=CascadeType.ALL,fetch = FetchType.LAZY)
    @JoinColumn(name="file_detail_id" )
	@JsonBackReference(value="participantRecordIdentifierRef")	
	private KsdOutPutFileDetails ksdOutPutFileDetails;

	public String getRecordIdentifierName() {
		return recordIdentifierName;
	}

	public void setRecordIdentifierName(String recordIdentifierName) {
		this.recordIdentifierName = recordIdentifierName;
	}

	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	public String getReoccurance() {
		return reoccurance;
	}

	public void setReoccurance(String reoccurance) {
		this.reoccurance = reoccurance;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public KsdOutPutFileDetails getKsdOutPutFileDetails() {
		return ksdOutPutFileDetails;
	}

	public void setKsdOutPutFileDetails(KsdOutPutFileDetails ksdOutPutFileDetails) {
		this.ksdOutPutFileDetails = ksdOutPutFileDetails;
	}
	
}
