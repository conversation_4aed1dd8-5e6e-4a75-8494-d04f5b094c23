package com.wipro.fipc.entity.filelayout;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "TASK_UPDATE_CONFIG", schema = "MAESTRO")
@Where(clause="active_flag='T'")
public class TaskUpdateConfig extends BaseModel implements Serializable {
	
	
	@Column(name = "MAESTRO_TASK_NAME")
	private String maestroTaskName;
	
	@Column(name = "NEW_DISCUSSION")
	private String newDiscussion;
	
	@Column(name = "ATTACHMENT")
	private String attachment;
	
	
	@Column(name = "Type")
	private String type;
	
	@Column(name = "INTERESTED_FIELDS")
	private String interestedFields;
	
	@Column(name = "active_flag")
	private char activeFlag;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="process_job_mapping_id")
//	@JsonBackReference(value="processjobmappingRef")	
    private ProcessJobMapping processJobMapping;

	@Column(name = "unsecured_attachment")
	private String unsecuredAttachment;
	
	@Column(name = "questionnaire")
	private String questionnaire;

	public String getMaestroTaskName() {
		return maestroTaskName;
	}

	public void setMaestroTaskName(String maestroTaskName) {
		this.maestroTaskName = maestroTaskName;
	}

	public String getNewDiscussion() {
		return newDiscussion;
	}

	public void setNewDiscussion(String newDiscussion) {
		this.newDiscussion = newDiscussion;
	}

	public String getAttachment() {
		return attachment;
	}

	public void setAttachment(String attachment) {
		this.attachment = attachment;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getInterestedFields() {
		return interestedFields;
	}

	public void setInterestedFields(String interestedFields) {
		this.interestedFields = interestedFields;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public String getUnsecuredAttachment() {
		return unsecuredAttachment;
	}

	public void setUnsecuredAttachment(String unsecuredAttachment) {
		this.unsecuredAttachment = unsecuredAttachment;
	}

	public String getQuestionnaire() {
		return questionnaire;
	}

	public void setQuestionnaire(String questionnaire) {
		this.questionnaire = questionnaire;
	}
	 
	
}
