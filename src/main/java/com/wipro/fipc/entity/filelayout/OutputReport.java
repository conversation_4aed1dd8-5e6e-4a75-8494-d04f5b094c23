package com.wipro.fipc.entity.filelayout;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "OUTPUT_REPORT", schema = "layout_rule")
@Where(clause="active_flag='T'")
public class OutputReport extends BaseModel {
	
	@Column(name = "file_name")
	private String fileName;

	@Column(name = "data_element")
	private String dataElement;

	@Column(name = "data_format")
	private String dataFormat;

	@Column(name = "data_element_wout_space")
	private String dataElementWoutSpace;
	
	@Column(name = "record_identifier")
	private String recordIdentifier;
	
	@Column(name = "cell_value")
	private String cellValue;
	
	@Column(name = "child_element")
	private String childElement;

	@Column(name = "add_on")
	private String addOn;
	
	@Column(name = "cell_name")
	private String cellName;

	@Column(name = "total")
	private String total;
	
	@Column(name = "active_flag")
	private char activeFlag;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="process_job_mapping_id")
    private ProcessJobMapping processJobMapping;	
	
	
	@ManyToOne (cascade=CascadeType.ALL,fetch = FetchType.LAZY)
    @JoinColumn(name="file_detail_id" )
	@JsonBackReference(value="outputReportRef")	
	private KsdOutPutFileDetails outputReport;


	public String getFileName() {
		return fileName;
	}


	public void setFileName(String fileName) {
		this.fileName = fileName;
	}


	public String getDataElement() {
		return dataElement;
	}


	public void setDataElement(String dataElement) {
		this.dataElement = dataElement;
	}


	public String getDataFormat() {
		return dataFormat;
	}


	public void setDataFormat(String dataFormat) {
		this.dataFormat = dataFormat;
	}


	public String getDataElementWoutSpace() {
		return dataElementWoutSpace;
	}


	public void setDataElementWoutSpace(String dataElementWoutSpace) {
		this.dataElementWoutSpace = dataElementWoutSpace;
	}


	public String getRecordIdentifier() {
		return recordIdentifier;
	}


	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}


	public String getCellValue() {
		return cellValue;
	}


	public void setCellValue(String cellValue) {
		this.cellValue = cellValue;
	}


	public String getChildElement() {
		return childElement;
	}


	public void setChildElement(String childElement) {
		this.childElement = childElement;
	}


	public String getAddOn() {
		return addOn;
	}


	public void setAddOn(String addOn) {
		this.addOn = addOn;
	}


	public String getCellName() {
		return cellName;
	}


	public void setCellName(String cellName) {
		this.cellName = cellName;
	}


	public String getTotal() {
		return total;
	}


	public void setTotal(String total) {
		this.total = total;
	}


	public char getActiveFlag() {
		return activeFlag;
	}


	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}


	public Date getCreatedDate() {
		return createdDate;
	}


	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}


	public String getCreatedBy() {
		return createdBy;
	}


	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}


	public String getUpdatedBy() {
		return updatedBy;
	}


	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}


	public Date getUpdatedDate() {
		return updatedDate;
	}


	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}


	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}


	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}


	public KsdOutPutFileDetails getOutputReport() {
		return outputReport;
	}


	public void setOutputReport(KsdOutPutFileDetails outputReport) {
		this.outputReport = outputReport;
	}
	

	
	
	
}
