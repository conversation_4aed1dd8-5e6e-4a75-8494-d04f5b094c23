package com.wipro.fipc.entity.filelayout;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "PROCESS_FEATURE_CONFIG ", schema = "common")
@Where(clause = "active_flag='T'")
public class ProcessFeatureConfig extends BaseModel implements Serializable {

	@Column(name = "ksd_name")
	private String ksdName;

	@Column(name = "business_unit_name")
	private String businessUnitName;

	@Column(name = "client_name")
	private String clientName;

	@Column(name = "eft_subject")
	private String eftSubject;

	@Column(name = "EFT_SUBJECT_COPIED")
	private String eftSubjectCopied;

	@Column(name = "client_code")
	private String clientCode;

	@Column(name = "job_copied")
	private String jobCopied;

	@Column(name = "pjm_id_copied")
	private Long pjmIdCopied;

	@Column(name = "phase_names")
	private String phaseNames;

	@Column(name = "business_ops_name")
	private String businessOpsName;

	@Column(name = "process_name")
	private String processName;

	@Column(name = "job_name")
	private String jobName;

	@Column(name = "CONFIG_STATUS")
	private String configStatus;

	@Column(name = "PROCESS_TYPE")
	private String processType;

	@Column(name = "active_flag")
	private char activeFlag;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@ManyToOne
	@JoinColumn(name = "process_job_mapping_id")
	// @JsonBackReference(value="processjobmappingRef")
	private ProcessJobMapping processJobMapping;

	@Column(name = "approved_by")
	private String approvedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "approved_date")
	private Date approvedDate;

	public String getKsdName() {
		return ksdName;
	}

	public void setKsdName(String ksdName) {
		this.ksdName = ksdName;
	}

	public String getBusinessUnitName() {
		return businessUnitName;
	}

	public void setBusinessUnitName(String businessUnitName) {
		this.businessUnitName = businessUnitName;
	}

	public String getClientName() {
		return clientName;
	}

	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	public String getEftSubject() {
		return eftSubject;
	}

	public void setEftSubject(String eftSubject) {
		this.eftSubject = eftSubject;
	}

	public String getEftSubjectCopied() {
		return eftSubjectCopied;
	}

	public void setEftSubjectCopied(String eftSubjectCopied) {
		this.eftSubjectCopied = eftSubjectCopied;
	}

	public String getClientCode() {
		return clientCode;
	}

	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}

	public String getJobCopied() {
		return jobCopied;
	}

	public void setJobCopied(String jobCopied) {
		this.jobCopied = jobCopied;
	}

	public Long getPjmIdCopied() {
		return pjmIdCopied;
	}

	public void setPjmIdCopied(Long pjmIdCopied) {
		this.pjmIdCopied = pjmIdCopied;
	}

	public String getPhaseNames() {
		return phaseNames;
	}

	public void setPhaseNames(String phaseNames) {
		this.phaseNames = phaseNames;
	}

	public String getBusinessOpsName() {
		return businessOpsName;
	}

	public void setBusinessOpsName(String businessOpsName) {
		this.businessOpsName = businessOpsName;
	}

	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getConfigStatus() {
		return configStatus;
	}

	public void setConfigStatus(String configStatus) {
		this.configStatus = configStatus;
	}

	public String getProcessType() {
		return processType;
	}

	public void setProcessType(String processType) {
		this.processType = processType;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public String getApprovedBy() {
		return approvedBy;
	}

	public void setApprovedBy(String approvedBy) {
		this.approvedBy = approvedBy;
	}

	public Date getApprovedDate() {
		return approvedDate;
	}

	public void setApprovedDate(Date approvedDate) {
		this.approvedDate = approvedDate;
	}

}
