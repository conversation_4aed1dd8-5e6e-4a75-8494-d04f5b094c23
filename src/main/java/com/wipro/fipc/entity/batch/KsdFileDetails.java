package com.wipro.fipc.entity.batch;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;
import org.json.JSONException;
import org.json.JSONObject;

import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@Table(name = "KSD_FILE_DETAILS", schema = "emails_scheduler")
@Where(clause = "active_flag='T'")
public class KsdFileDetails extends BaseModel implements Serializable {

	@Column(name = "file_name")
	private String fileName;

	@Column(name = "DATE_FORMAT")
	private String dateFormat;

	@Column(name = "FILE_TYPE")
	private String fileType;

	@Column(name = "RECORD_IDENTIFIER_COL")
	private String recordIdentifierCol;

	@Column(name = "file_format_type")
	private String fileFormatType;

	@Column(name = "subject")
	private String subj;

	@Column(name = "DELIMITER")
	private String delimiter;

	@Column(name = "sender")
	private String sendr;

	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "variation")
	private String variation;

	@Column(name = "min_threshold")
	private String minThreshold;

	@Column(name = "max_threshold")
	private String maxThreshold;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Column(name = "PPT_IDENTIFIER")
	private String PPTIDentifier;

	@Column(name = "PPT_IDENTIFIER_TYPE")
	private String PPTIDentifierType;

	@Column(name = "sheet_name")
	private String sheetName;

	@Column(name = "file_name_wout_space")
	private String fileNameWoutSpace;

	@Column(name = "sheet_name_wout_space")
	private String sheetNameWoutSpace;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@ManyToOne
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMapping;

	@Column(name = "process_job_mapping_id", insertable = false, updatable = false)
	private long processJobMappingId;

	@Column(name = "source")
	private String source;

	@Column(name = "path")
	private String path;

	@Column(name = "tool")
	private String tool;

	@Column(name = "domain")
	private String domain;

	@Column(name = "query_jcl_name")
	private String queryJCLName;

	@Column(name = "date_generated")
	private String dateGenerated;

	@Column(name = "date_frequency")
	private String dateFrequency;

	@Column(name = "date_period")
	private String datePeriod;

	@Column(name = "date_interval")
	private String dateInterval;

	@Column(name = "prev_report_file_name")
	private String prevReportFileName;

	@Column(name = "prev_report_file_name_ws")
	private String prevReportFileNameWs;

	@Column(name = "subfolder")
	private String subfolder;

	@Column(name = "action")
	private String action;

	@Column(name = "use_labelling_rpt")
	private String useLabellingRpt;

	@Column(name = "whitelist_SSN")
	private String whitelistSSN;

	@Column(name = "client")
	private String client;

	@Column(name = "database")
	private String database;

	@Column(name = "sql_query")
	private String sqlQuery;

	@Column(name = "vertical_identifier")
	private String verticalIDentifier;

	@Column(name = "report_flag")
	private String reportFlag;

	@Column(name = "append")
	private String append;

	@Column(name = "append_format")
	private String appendFormat;

	@Column(name = "whitelist")
	private String whitelist;

	@Column(name = "record_cnt_check")
	private String recordCntCheck;

	@Column(name = "file_name_template")
	private String fileNameTemplate;

	@Column(name = "append_name_flag")
	private String appendNameFlag;

	@Column(name = "verify_email_only")
	private boolean verifyEmailOnly;

	@Column(name = "primary_file")
	private String primaryFile;

	@Column(name = "sampling_count")
	private String samplingCount;

	@Column(name = "email_search_by")
	private String emailSearchBy;

	@Column(name = "bene_ppt_identifier")
	private String benePptIdentifier;

	@Column(name = "bene_ppt_identifier_type")
	private String benePptIdentifierType;
	
	@Column(name = "verify_file_date_detail_record")
	private boolean verifyFileDateDetailRecord;
	
	public JSONObject toJSON() throws JSONException {
		JSONObject json = new JSONObject();
		if (this.fileName != null) {
			json.put("fileName", fileName);
			json.put("fileFormatType", fileFormatType);
			json.put("subj", subj);
			json.put("delimiter", delimiter);
			json.put("sendr", sendr);
			json.put("activeFlag", activeFlag);
			json.put("variation", variation);
			json.put("minThreshold", minThreshold);
			json.put("maxThreshold", maxThreshold);
			json.put("primaryFile", primaryFile);
			json.put("samplingCount", samplingCount);
			json.put("benePptIdentifier", benePptIdentifier);
			json.put("benePptIdentifierType", benePptIdentifierType);
		}
		return json;

	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = super.hashCode();
		result = prime * result + Objects.hash(PPTIDentifier, PPTIDentifierType, action, activeFlag, append,
				appendFormat, appendNameFlag, benePptIdentifier, benePptIdentifierType, client, createdBy, createdDate,
				database, dateFormat, dateFrequency, dateGenerated, dateInterval, datePeriod, delimiter, domain,
				emailSearchBy, fileFormatType, fileName, fileNameTemplate, fileNameWoutSpace, fileType, maxThreshold,
				minThreshold, path, prevReportFileName, prevReportFileNameWs, primaryFile, processJobMapping,
				processJobMappingId, queryJCLName, recordCntCheck, recordIdentifierCol, reportFlag, samplingCount,
				sendr, sheetName, sheetNameWoutSpace, source, sqlQuery, subfolder, subj, tool, updatedBy, updatedDate,
				useLabellingRpt, variation, verifyEmailOnly, verticalIDentifier, whitelist, whitelistSSN);
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!super.equals(obj))
			return false;
		if (getClass() != obj.getClass())
			return false;
		KsdFileDetails other = (KsdFileDetails) obj;
		return Objects.equals(PPTIDentifier, other.PPTIDentifier)
				&& Objects.equals(PPTIDentifierType, other.PPTIDentifierType) && Objects.equals(action, other.action)
				&& activeFlag == other.activeFlag && Objects.equals(append, other.append)
				&& Objects.equals(appendFormat, other.appendFormat)
				&& Objects.equals(appendNameFlag, other.appendNameFlag)
				&& Objects.equals(benePptIdentifier, other.benePptIdentifier)
				&& Objects.equals(benePptIdentifierType, other.benePptIdentifierType)
				&& Objects.equals(client, other.client) && Objects.equals(createdBy, other.createdBy)
				&& Objects.equals(createdDate, other.createdDate) && Objects.equals(database, other.database)
				&& Objects.equals(dateFormat, other.dateFormat) && Objects.equals(dateFrequency, other.dateFrequency)
				&& Objects.equals(dateGenerated, other.dateGenerated)
				&& Objects.equals(dateInterval, other.dateInterval) && Objects.equals(datePeriod, other.datePeriod)
				&& Objects.equals(delimiter, other.delimiter) && Objects.equals(domain, other.domain)
				&& Objects.equals(emailSearchBy, other.emailSearchBy)
				&& Objects.equals(fileFormatType, other.fileFormatType) && Objects.equals(fileName, other.fileName)
				&& Objects.equals(fileNameTemplate, other.fileNameTemplate)
				&& Objects.equals(fileNameWoutSpace, other.fileNameWoutSpace)
				&& Objects.equals(fileType, other.fileType) && Objects.equals(maxThreshold, other.maxThreshold)
				&& Objects.equals(minThreshold, other.minThreshold) && Objects.equals(path, other.path)
				&& Objects.equals(prevReportFileName, other.prevReportFileName)
				&& Objects.equals(prevReportFileNameWs, other.prevReportFileNameWs)
				&& Objects.equals(primaryFile, other.primaryFile)
				&& Objects.equals(processJobMapping, other.processJobMapping)
				&& processJobMappingId == other.processJobMappingId && Objects.equals(queryJCLName, other.queryJCLName)
				&& Objects.equals(recordCntCheck, other.recordCntCheck)
				&& Objects.equals(recordIdentifierCol, other.recordIdentifierCol)
				&& Objects.equals(reportFlag, other.reportFlag) && Objects.equals(samplingCount, other.samplingCount)
				&& Objects.equals(sendr, other.sendr) && Objects.equals(sheetName, other.sheetName)
				&& Objects.equals(sheetNameWoutSpace, other.sheetNameWoutSpace) && Objects.equals(source, other.source)
				&& Objects.equals(sqlQuery, other.sqlQuery) && Objects.equals(subfolder, other.subfolder)
				&& Objects.equals(subj, other.subj) && Objects.equals(tool, other.tool)
				&& Objects.equals(updatedBy, other.updatedBy) && Objects.equals(updatedDate, other.updatedDate)
				&& Objects.equals(useLabellingRpt, other.useLabellingRpt) && Objects.equals(variation, other.variation)
				&& verifyEmailOnly == other.verifyEmailOnly
				&& Objects.equals(verticalIDentifier, other.verticalIDentifier)
				&& Objects.equals(whitelist, other.whitelist) && Objects.equals(whitelistSSN, other.whitelistSSN);
	}

	@Override
	public String toString() {
		return "KsdFileDetails [fileName=" + fileName + ", dateFormat=" + dateFormat + ", fileType=" + fileType
				+ ", recordIdentifierCol=" + recordIdentifierCol + ", fileFormatType=" + fileFormatType + ", subj="
				+ subj + ", delimiter=" + delimiter + ", sendr=" + sendr + ", activeFlag=" + activeFlag + ", variation="
				+ variation + ", minThreshold=" + minThreshold + ", maxThreshold=" + maxThreshold + ", createdDate="
				+ createdDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy + ", PPTIDentifier="
				+ PPTIDentifier + ", PPTIDentifierType=" + PPTIDentifierType + ", sheetName=" + sheetName
				+ ", fileNameWoutSpace=" + fileNameWoutSpace + ", sheetNameWoutSpace=" + sheetNameWoutSpace
				+ ", updatedDate=" + updatedDate + ", processJobMapping=" + processJobMapping + ", processJobMappingId="
				+ processJobMappingId + ", source=" + source + ", path=" + path + ", tool=" + tool + ", domain="
				+ domain + ", queryJCLName=" + queryJCLName + ", dateGenerated=" + dateGenerated + ", dateFrequency="
				+ dateFrequency + ", datePeriod=" + datePeriod + ", dateInterval=" + dateInterval
				+ ", prevReportFileName=" + prevReportFileName + ", prevReportFileNameWs=" + prevReportFileNameWs
				+ ", subfolder=" + subfolder + ", action=" + action + ", useLabellingRpt=" + useLabellingRpt
				+ ", whitelistSSN=" + whitelistSSN + ", client=" + client + ", database=" + database + ", sqlQuery="
				+ sqlQuery + ", verticalIDentifier=" + verticalIDentifier + ", reportFlag=" + reportFlag + ", append="
				+ append + ", appendFormat=" + appendFormat + ", whitelist=" + whitelist + ", recordCntCheck="
				+ recordCntCheck + ", fileNameTemplate=" + fileNameTemplate + ", appendNameFlag=" + appendNameFlag
				+ ", verifyEmailOnly=" + verifyEmailOnly + ", primaryFile=" + primaryFile + ", samplingCount="
				+ samplingCount + ", emailSearchBy=" + emailSearchBy + ", benePptIdentifier=" + benePptIdentifier
				+ ", benePptIdentifierType=" + benePptIdentifierType + ", verifyFileDateDetailRecord="
				+ verifyFileDateDetailRecord + "]";
	}

}
