package com.wipro.fipc.entity.batch;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@Table(name = "ksd_config", schema = "emails_scheduler")
public class EmailPlaceholderMaster extends BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

	@Column(name = "place_holder")
	private String placeHolder;

	@Column(name = "value")
	private String value;

}
