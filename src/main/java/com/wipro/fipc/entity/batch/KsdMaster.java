package com.wipro.fipc.entity.batch;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@Table(name = "ksd_master", schema = "emails_scheduler")
public class KsdMaster extends BaseModel implements Serializable {

	private static final long serialVersionUID = 6116457453230150993L;

	@Column(name = "primary_job_name")
	private String primaryJobName;
	
	@Column(name = "ksd_name")
	private String ksdName;
	
	@Column(name = "job_stream")
	private String jobStream;

	@Column(name = "frequency")
	private String frequency;

	@Column(name = "job_cut_off_time")
	private String jobCutOffTime;
	
	@Column(name = "turnaround_time")
	private String turnaroundTime;

	@Column(name = "job_schedule_time")
	private String jobScheduleTime;
	
	@Column(name = "file_name")
	private String fileName;
	
	@Column(name = "file_format_type")
	private String fileFormatType;
	
	@Column(name = "maestro_task_name")
	private String maestroTaskName;

	@Column(name = "eft_name")
	private String eftName;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;

	public String getPrimaryJobName() {
		return primaryJobName;
	}

	public void setPrimaryJobName(String primaryJobName) {
		this.primaryJobName = primaryJobName;
	}

	public String getKsdName() {
		return ksdName;
	}

	public void setKsdName(String ksdName) {
		this.ksdName = ksdName;
	}

	public String getJobStream() {
		return jobStream;
	}

	public void setJobStream(String jobStream) {
		this.jobStream = jobStream;
	}

	public String getFrequency() {
		return frequency;
	}

	public void setFrequency(String frequency) {
		this.frequency = frequency;
	}

	public String getJobCutOffTime() {
		return jobCutOffTime;
	}

	public void setJobCutOffTime(String jobCutOffTime) {
		this.jobCutOffTime = jobCutOffTime;
	}

	public String getTurnaroundTime() {
		return turnaroundTime;
	}

	public void setTurnaroundTime(String turnaroundTime) {
		this.turnaroundTime = turnaroundTime;
	}

	public String getJobScheduleTime() {
		return jobScheduleTime;
	}

	public void setJobScheduleTime(String jobScheduleTime) {
		this.jobScheduleTime = jobScheduleTime;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileFormatType() {
		return fileFormatType;
	}

	public void setFileFormatType(String fileFormatType) {
		this.fileFormatType = fileFormatType;
	}

	public String getMaestroTaskName() {
		return maestroTaskName;
	}

	public void setMaestroTaskName(String maestroTaskName) {
		this.maestroTaskName = maestroTaskName;
	}

	public String getEftName() {
		return eftName;
	}

	public void setEftName(String eftName) {
		this.eftName = eftName;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	@Override
	public int hashCode() {
		return Objects.hash(createdBy, createdDate, eftName, fileFormatType, fileName, frequency, jobCutOffTime,
				jobScheduleTime, jobStream, ksdName, maestroTaskName, primaryJobName, turnaroundTime, updatedBy,
				updatedDate);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KsdMaster other = (KsdMaster) obj;
		return Objects.equals(createdBy, other.createdBy) && Objects.equals(createdDate, other.createdDate)
				&& Objects.equals(eftName, other.eftName) && Objects.equals(fileFormatType, other.fileFormatType)
				&& Objects.equals(fileName, other.fileName) && Objects.equals(frequency, other.frequency)
				&& Objects.equals(jobCutOffTime, other.jobCutOffTime)
				&& Objects.equals(jobScheduleTime, other.jobScheduleTime) && Objects.equals(jobStream, other.jobStream)
				&& Objects.equals(ksdName, other.ksdName) && Objects.equals(maestroTaskName, other.maestroTaskName)
				&& Objects.equals(primaryJobName, other.primaryJobName)
				&& Objects.equals(turnaroundTime, other.turnaroundTime) && Objects.equals(updatedBy, other.updatedBy)
				&& Objects.equals(updatedDate, other.updatedDate);
	}

	@Override
	public String toString() {
		return "KsdMaster [primaryJobName=" + primaryJobName + ", ksdName=" + ksdName + ", jobStream=" + jobStream
				+ ", frequency=" + frequency + ", jobCutOffTime=" + jobCutOffTime + ", turnaroundTime=" + turnaroundTime
				+ ", jobScheduleTime=" + jobScheduleTime + ", fileName=" + fileName + ", fileFormatType="
				+ fileFormatType + ", maestroTaskName=" + maestroTaskName + ", eftName=" + eftName + ", createdDate="
				+ createdDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate
				+ ", id=" + id + "]";
	}
	
	


}