package com.wipro.fipc.entity.batch;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityResult;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.json.JSONException;
import org.json.JSONObject;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Entity
@Data
@Table(name = "daily_task_report", schema = "emails_scheduler", uniqueConstraints = {
		@UniqueConstraint(name = "uk_uid", columnNames = { "uid" }) })
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@SqlResultSetMapping(name = "dailyTaskReport", entities = @EntityResult(entityClass = DailyTaskReport.class))
public class DailyTaskReport extends BaseModel implements Serializable {

	private static final long serialVersionUID = -2734486625212493400L;
	
	@Column(name = "process_job_mapping_id")
	private long processJobMappingId;

	@Column(name = "uid", unique = true)
	private String uid;
	
	@Column(name = "title_of_daily_task_report", nullable = true)
	private String titleOfDailyTaskReport;
	
	@Column(name = "job_name_in_report")
	private String jobNameInReport;
	
	@Column(name = "status_in_report", nullable = true)
	private String statusInReport;
	
	@Column(name = "start_time_in_report", nullable = true)
	private String startTimeInReport;
	
	@Column(name = "end_time_in_report", nullable = true)
	private String endTimeInReport;
	
	@Column(name = "job_cut_off_time", nullable = true)
	private String jobCutOffTime;
	
	@Column(name = "job_failure_id", nullable = true)
	private String jobFailureId;
	
	@Column(name = "adx_script_id", nullable = true)
	private String adxScriptId;
	
	@Column(name = "business_unit")
	private String businessUnit;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date  createdDate;

	
	public JSONObject toJSON() throws JSONException {
		JSONObject json = new JSONObject();
		if (this.uid != null) {
			json.put("uid", uid);
			json.put("titleOfDailyTaskReport", titleOfDailyTaskReport);
			json.put("jobNameInReport", jobNameInReport);
			json.put("statusInReport", statusInReport);
			json.put("startTimeInReport", startTimeInReport);
			json.put("endTimeInReport", endTimeInReport);
			json.put("jobCutOffTime", jobCutOffTime);
			json.put("jobFailureId", jobFailureId);

		}
		return json;

	}


	@Override
	public String toString() {
		return "DailyTaskReport [processJobMappingId=" + processJobMappingId + ", uid=" + uid
				+ ", titleOfDailyTaskReport=" + titleOfDailyTaskReport + ", jobNameInReport=" + jobNameInReport
				+ ", statusInReport=" + statusInReport + ", startTimeInReport=" + startTimeInReport
				+ ", endTimeInReport=" + endTimeInReport + ", jobCutOffTime=" + jobCutOffTime + ", jobFailureId="
				+ jobFailureId + ", adxScriptId=" + adxScriptId + ", businessUnit=" + businessUnit + ", createdDate="
				+ createdDate + "]";
	}
}
