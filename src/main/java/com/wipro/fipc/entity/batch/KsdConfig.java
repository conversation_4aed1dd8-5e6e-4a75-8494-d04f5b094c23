package com.wipro.fipc.entity.batch;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;
import org.json.JSONException;
import org.json.JSONObject;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

@Entity
@Table(name = "ksd_config", schema = "emails_scheduler")
@Where(clause = "active_flag='T'")
public class KsdConfig extends BaseModel implements Serializable {

	private static final long serialVersionUID = 6116457453230150993L;

	@Column(name = "primary_job_name")
	private String primaryJobName;

	@Column(name = "job_stream")
	private String jobStream;

	@Column(name = "FILE_TYPE")
	private String fileType;
	@Column(name = "frequency")
	private String frequency;

	@Column(name = "job_cut_off_time")
	private String jobCutOffTime;

	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "turnaround_time")
	private String turnaroundTime;

	@Column(name = "job_schedule_time")
	private String jobScheduleTime;

	@Column(name = "eft_name")
	private String eftName;

	@Column(name = "maestro_task_name")
	private String maestroTaskName;

	@Column(name = "daily_task_report_subject_name_outlook")
	private String dailyTaskReportSubjectNameOutLook;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "adx_script_id ")
	private String adxScriptId;

	@Column(name = "eft_trigger_time")
	private String eftTriggerTime;

	@Column(name = "priority")
	private boolean priority;

	@Column(name = "skip_control_m_report")
	private Boolean skipControlMReport;

	@Column(name = "task_creation_only")
	private Boolean taskCreationOnly;

	@ManyToOne
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMapping;

	@Column(name = "process_job_mapping_id", insertable = false, updatable = false)
	private long processJobMappingId;

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "ksdConfig") // ,orphanRemoval = true
	// @LazyCollection(LazyCollectionOption.FALSE)
	@JsonManagedReference(value = "ksdconfigRef")
	private List<ClientConfigJobs> clientConfigJobs = new ArrayList<ClientConfigJobs>();

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "ksdConfigJobSchedule") // ,orphanRemoval =
																										// true
	// @LazyCollection(LazyCollectionOption.FALSE)
	@JsonManagedReference(value = "ksdjobscheduleRef")
	private List<JobSchedule> jobSchedules = new ArrayList<JobSchedule>();

	@Column(name = "maestro_task_type")
	private String maestroTaskType;

	@Column(name = "direct_tba_update")
	private Boolean directTBAUpdate = true;

	@Column(name = "named_type")
	private Boolean namedType = false;

	@Column(name = "jira_task_name")
	private String jiraTaskName;

	@Column(name = "jira_task_type")
	private String jiraTaskType;

	@Column(name = "is_jira_used")
	private Boolean isJiraUsed = false;

	@Column(name = "jira_project_type")
	private String jiraProjectType;

	public String getJiraProjectType() {
		return jiraProjectType;
	}

	public void setJiraProjectType(String jiraProjectType) {
		this.jiraProjectType = jiraProjectType;
	}

	public Boolean getIsJiraUsed() {
		return isJiraUsed;
	}

	public void setIsJiraUsed(Boolean isJiraUsed) {
		this.isJiraUsed = isJiraUsed;
	}

	public String getJiraTaskName() {
		return jiraTaskName;
	}

	public void setJiraTaskName(String jiraTaskName) {
		this.jiraTaskName = jiraTaskName;
	}

	public String getJiraTaskType() {
		return jiraTaskType;
	}

	public void setJiraTaskType(String jiraTaskType) {
		this.jiraTaskType = jiraTaskType;
	}

	public Boolean getDirectTBAUpdate() {
		return directTBAUpdate == null ? true : directTBAUpdate;
	}

	public void setDirectTBAUpdate(Boolean directTBAUpdate) {
		this.directTBAUpdate = directTBAUpdate;
	}

	public Boolean getNamedType() {
		return namedType == null ? false : namedType;
	}

	public void setNamedType(Boolean namedType) {
		this.namedType = namedType;
	}

	public String getMaestroTaskType() {
		return maestroTaskType;
	}

	public void setMaestroTaskType(String maestroTaskType) {
		this.maestroTaskType = maestroTaskType;
	}

	/**
	 * @return the primaryJobName
	 */
	public String getPrimaryJobName() {
		return primaryJobName;
	}

	/**
	 * @param primaryJobName the primaryJobName to set
	 */
	public void setPrimaryJobName(String primaryJobName) {
		this.primaryJobName = primaryJobName;
	}

	/**
	 * @return the jobStream
	 */
	public String getJobStream() {
		return jobStream;
	}

	/**
	 * @param jobStream the jobStream to set
	 */
	public void setJobStream(String jobStream) {
		this.jobStream = jobStream;
	}

	/**
	 * @return the fileType
	 */
	public String getFileType() {
		return fileType;
	}

	/**
	 * @param fileType the fileType to set
	 */
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	/**
	 * @return the frequency
	 */
	public String getFrequency() {
		return frequency;
	}

	/**
	 * @param frequency the frequency to set
	 */
	public void setFrequency(String frequency) {
		this.frequency = frequency;
	}

	/**
	 * @return the jobCutOffTime
	 */
	public String getJobCutOffTime() {
		return jobCutOffTime;
	}

	/**
	 * @param jobCutOffTime the jobCutOffTime to set
	 */
	public void setJobCutOffTime(String jobCutOffTime) {
		this.jobCutOffTime = jobCutOffTime;
	}

	/**
	 * @return the activeFlag
	 */
	public char getActiveFlag() {
		return activeFlag;
	}

	/**
	 * @param activeFlag the activeFlag to set
	 */
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	/**
	 * @return the turnaroundTime
	 */
	public String getTurnaroundTime() {
		return turnaroundTime;
	}

	/**
	 * @param turnaroundTime the turnaroundTime to set
	 */
	public void setTurnaroundTime(String turnaroundTime) {
		this.turnaroundTime = turnaroundTime;
	}

	/**
	 * @return the jobScheduleTime
	 */
	public String getJobScheduleTime() {
		return jobScheduleTime;
	}

	/**
	 * @param jobScheduleTime the jobScheduleTime to set
	 */
	public void setJobScheduleTime(String jobScheduleTime) {
		this.jobScheduleTime = jobScheduleTime;
	}

	/**
	 * @return the eftName
	 */
	public String getEftName() {
		return eftName;
	}

	/**
	 * @param eftName the eftName to set
	 */
	public void setEftName(String eftName) {
		this.eftName = eftName;
	}

	/**
	 * @return the maestroTaskName
	 */
	public String getMaestroTaskName() {
		return maestroTaskName;
	}

	/**
	 * @param maestroTaskName the maestroTaskName to set
	 */
	public void setMaestroTaskName(String maestroTaskName) {
		this.maestroTaskName = maestroTaskName;
	}

	/**
	 * @return the dailyTaskReportSubjectNameOutLook
	 */
	public String getDailyTaskReportSubjectNameOutLook() {
		return dailyTaskReportSubjectNameOutLook;
	}

	/**
	 * @param dailyTaskReportSubjectNameOutLook the
	 *                                          dailyTaskReportSubjectNameOutLook to
	 *                                          set
	 */
	public void setDailyTaskReportSubjectNameOutLook(String dailyTaskReportSubjectNameOutLook) {
		this.dailyTaskReportSubjectNameOutLook = dailyTaskReportSubjectNameOutLook;
	}

	/**
	 * @return the createdDate
	 */
	public Date getCreatedDate() {
		return createdDate;
	}

	/**
	 * @param createdDate the createdDate to set
	 */
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	/**
	 * @return the createdBy
	 */
	public String getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the updatedDate
	 */
	public Date getUpdatedDate() {
		return updatedDate;
	}

	/**
	 * @param updatedDate the updatedDate to set
	 */
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	/**
	 * @return the adxScriptId
	 */
	public String getAdxScriptId() {
		return adxScriptId;
	}

	/**
	 * @param adxScriptId the adxScriptId to set
	 */
	public void setAdxScriptId(String adxScriptId) {
		this.adxScriptId = adxScriptId;
	}

	/**
	 * @return the eftTriggerTime
	 */
	public String getEftTriggerTime() {
		return eftTriggerTime;
	}

	/**
	 * @param eftTriggerTime the eftTriggerTime to set
	 */
	public void setEftTriggerTime(String eftTriggerTime) {
		this.eftTriggerTime = eftTriggerTime;
	}

	/**
	 * @return the priority
	 */
	public boolean isPriority() {
		return priority;
	}

	/**
	 * @param priority the priority to set
	 */
	public void setPriority(boolean priority) {
		this.priority = priority;
	}

	/**
	 * @return the processJobMapping
	 */
	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	/**
	 * @param processJobMapping the processJobMapping to set
	 */
	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	/**
	 * @return the processJobMappingId
	 */
	public long getProcessJobMappingId() {
		return processJobMappingId;
	}

	/**
	 * @param processJobMappingId the processJobMappingId to set
	 */
	public void setProcessJobMappingId(long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	/**
	 * @return the clientConfigJobs
	 */
	public List<ClientConfigJobs> getClientConfigJobs() {
		return clientConfigJobs;
	}

	/**
	 * @param clientConfigJobs the clientConfigJobs to set
	 */
	public void setClientConfigJobs(List<ClientConfigJobs> clientConfigJobs) {
		this.clientConfigJobs = clientConfigJobs;
	}

	/**
	 * @return the jobSchedules
	 */
	public List<JobSchedule> getJobSchedules() {
		return jobSchedules;
	}

	/**
	 * @param jobSchedules the jobSchedules to set
	 */
	public void setJobSchedules(List<JobSchedule> jobSchedules) {
		this.jobSchedules = jobSchedules;
	}

	public Boolean getSkipControlMReport() {
		return skipControlMReport;
	}

	public void setSkipControlMReport(Boolean skipControlMReport) {
		this.skipControlMReport = skipControlMReport;
	}

	public Boolean getTaskCreationOnly() {
		return taskCreationOnly;
	}

	public void setTaskCreationOnly(Boolean taskCreationOnly) {
		this.taskCreationOnly = taskCreationOnly;
	}

	public JSONObject toJSON() throws JSONException {
		JSONObject json = new JSONObject();
		if (this.primaryJobName != null) {
			json.put("primaryJobName", primaryJobName);
			json.put("jobStream", jobStream);
			json.put("fileType", fileType);
			json.put("frequency", frequency);
			json.put("jobCutOffTime", jobCutOffTime);
			json.put("activeFlag", activeFlag);
			json.put("turnaroundTime", turnaroundTime);
			json.put("jobScheduleTime", jobScheduleTime);
			json.put("eftName", eftName);
			json.put("maestroTaskName", maestroTaskName);
			json.put("dailyTaskReportSubjectNameOutLook", dailyTaskReportSubjectNameOutLook);
			json.put("priority", priority);
			json.put("skipControlMReport", skipControlMReport);
			json.put("taskCreationOnly", taskCreationOnly);
		}
		return json;

	}

	public KsdConfig() {
	}

	public KsdConfig(String primaryJobName, String jobStream, String fileType, String frequency, String jobCutOffTime,
			char activeFlag, String turnaroundTime, String jobScheduleTime, String eftName, String maestroTaskName,
			String dailyTaskReportSubjectNameOutLook, Date createdDate, String createdBy, String updatedBy,
			Date updatedDate, String adxScriptId, String eftTriggerTime, boolean priority,
			ProcessJobMapping processJobMapping, long processJobMappingId, List<ClientConfigJobs> clientConfigJobs,
			List<JobSchedule> jobSchedules) {
		super();
		this.primaryJobName = primaryJobName;
		this.jobStream = jobStream;
		this.fileType = fileType;
		this.frequency = frequency;
		this.jobCutOffTime = jobCutOffTime;
		this.activeFlag = activeFlag;
		this.turnaroundTime = turnaroundTime;
		this.jobScheduleTime = jobScheduleTime;
		this.eftName = eftName;
		this.maestroTaskName = maestroTaskName;
		this.dailyTaskReportSubjectNameOutLook = dailyTaskReportSubjectNameOutLook;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDate = updatedDate;
		this.adxScriptId = adxScriptId;
		this.eftTriggerTime = eftTriggerTime;
		this.priority = priority;
		this.processJobMapping = processJobMapping;
		this.processJobMappingId = processJobMappingId;
		this.clientConfigJobs = clientConfigJobs;
		this.jobSchedules = jobSchedules;
	}
}
