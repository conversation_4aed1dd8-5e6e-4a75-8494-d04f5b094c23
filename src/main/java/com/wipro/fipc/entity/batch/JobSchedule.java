package com.wipro.fipc.entity.batch;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@Table(name = "job_schedule", schema = "emails_scheduler")
@Where(clause = "active_flag='T'")
public class JobSchedule extends BaseModel implements Serializable {

	@Column(name = "interval")
	private Integer interval;

	@Column(name = "skip_holidays")
	private String skipHolidays;
	@Column(name = "days_of_week")
	private String daysOfWeek;

	@Column(name = "day_of_month")
	private int dayOfMonth;

	@Column(name = "index")
	private String index;

	@Column(name = "month")
	private int month;
	@Column(name = "active_flag")
	private char activeFlag;
	@Column(name = "time_zone")
	private String timeZone;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "frequency")
	private String frequency;
	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "bimonthly_days")
	private String bimonthlyDays;

	@Column(name = "custom_dates")
	private String customDates;

	@Column(name = "skip_weekends")
	private String skipWeekends;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "ksd_config_id")
	@JsonBackReference(value = "ksdjobscheduleRef")
	private KsdConfig ksdConfigJobSchedule;

	public Integer getInterval() {
		return interval;
	}

	public void setInterval(Integer interval) {
		this.interval = interval;
	}

	public String getSkipHolidays() {
		return skipHolidays;
	}

	public void setSkipHolidays(String skipHolidays) {
		this.skipHolidays = skipHolidays;
	}

	public String getDaysOfWeek() {
		return daysOfWeek;
	}

	public void setDaysOfWeek(String daysOfWeek) {
		this.daysOfWeek = daysOfWeek;
	}

	public int getDayOfMonth() {
		return dayOfMonth;
	}

	public void setDayOfMonth(int dayOfMonth) {
		this.dayOfMonth = dayOfMonth;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getFrequency() {
		return frequency;
	}

	public void setFrequency(String frequency) {
		this.frequency = frequency;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getBimonthlyDays() {
		return bimonthlyDays;
	}

	public void setBimonthlyDays(String bimonthlyDays) {
		this.bimonthlyDays = bimonthlyDays;
	}

	public String getCustomDates() {
		return customDates;
	}

	public void setCustomDates(String customDates) {
		this.customDates = customDates;
	}

	public String getSkipWeekends() {
		return skipWeekends;
	}

	public void setSkipWeekends(String skipWeekends) {
		this.skipWeekends = skipWeekends;
	}

	public KsdConfig getKsdConfigJobSchedule() {
		return ksdConfigJobSchedule;
	}

	public void setKsdConfigJobSchedule(KsdConfig ksdConfigJobSchedule) {
		this.ksdConfigJobSchedule = ksdConfigJobSchedule;
	}

	@Override
	public int hashCode() {
		return Objects.hash(activeFlag, bimonthlyDays, createdBy, createdDate, customDates, dayOfMonth, daysOfWeek,
				frequency, index, interval, ksdConfigJobSchedule, month, skipHolidays, skipWeekends, timeZone,
				updatedBy, updatedDate);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		JobSchedule other = (JobSchedule) obj;
		return activeFlag == other.activeFlag && Objects.equals(bimonthlyDays, other.bimonthlyDays)
				&& Objects.equals(createdBy, other.createdBy) && Objects.equals(createdDate, other.createdDate)
				&& Objects.equals(customDates, other.customDates) && dayOfMonth == other.dayOfMonth
				&& Objects.equals(daysOfWeek, other.daysOfWeek) && Objects.equals(frequency, other.frequency)
				&& Objects.equals(index, other.index) && interval == other.interval
				&& Objects.equals(ksdConfigJobSchedule, other.ksdConfigJobSchedule) && month == other.month
				&& Objects.equals(skipHolidays, other.skipHolidays) && Objects.equals(skipWeekends, other.skipWeekends)
				&& Objects.equals(timeZone, other.timeZone) && Objects.equals(updatedBy, other.updatedBy)
				&& Objects.equals(updatedDate, other.updatedDate);
	}

	@Override
	public String toString() {
		return "JobSchedule [interval=" + interval + ", skipHolidays=" + skipHolidays + ", daysOfWeek=" + daysOfWeek
				+ ", dayOfMonth=" + dayOfMonth + ", index=" + index + ", month=" + month + ", activeFlag=" + activeFlag
				+ ", timeZone=" + timeZone + ", createdDate=" + createdDate + ", frequency=" + frequency
				+ ", createdBy=" + createdBy + ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate
				+ ", bimonthlyDays=" + bimonthlyDays + ", customDates=" + customDates + ", skipWeekends=" + skipWeekends
				+ ", ksdConfigJobSchedule=" + ksdConfigJobSchedule + "]";
	}
}
