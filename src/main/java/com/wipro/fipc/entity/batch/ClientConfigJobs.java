package com.wipro.fipc.entity.batch;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;
import org.json.JSONException;
import org.json.JSONObject;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@Table(name = "client_config_jobs", schema = "emails_scheduler")
@Where(clause="active_flag='T'")
public class ClientConfigJobs extends BaseModel implements Serializable {

	@Column(name = "child_job_name")
	private String childJobName;
	
	@Column(name = "process_job_mapping_id")
	private long processJobMappingId;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;
	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	
	@ManyToOne (cascade=CascadeType.ALL,fetch = FetchType.LAZY)
    @JoinColumn(name="ksd_config_id")
	@JsonBackReference(value="ksdconfigRef")	
	private KsdConfig ksdConfig;
	
	public String getChildJobName() {
		return childJobName;
	}

	public void setChildJobName(String childJobName) {
		this.childJobName = childJobName;
	}

	public long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public KsdConfig getKsdConfig() {
		return ksdConfig;
	}

	public void setKsdConfig(KsdConfig ksdConfig) {
		this.ksdConfig = ksdConfig;
	}

	@Override
	public String toString() {
		return "ClientConfigJobs [childJobName=" + childJobName + ", processJobMappingId=" + processJobMappingId
				+ ", createdDate=" + createdDate + ", activeFlag=" + activeFlag + ", createdBy=" + createdBy
				+ ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate + ", ksdConfig=" + ksdConfig + ", id="
				+ id + "]";
	}

	@Override
	public int hashCode() {
		return Objects.hash(activeFlag, childJobName, createdBy, createdDate, ksdConfig, processJobMappingId, updatedBy,
				updatedDate);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ClientConfigJobs other = (ClientConfigJobs) obj;
		return activeFlag == other.activeFlag && Objects.equals(childJobName, other.childJobName)
				&& Objects.equals(createdBy, other.createdBy) && Objects.equals(createdDate, other.createdDate)
				&& Objects.equals(ksdConfig, other.ksdConfig) && processJobMappingId == other.processJobMappingId
				&& Objects.equals(updatedBy, other.updatedBy) && Objects.equals(updatedDate, other.updatedDate);
	}

	public JSONObject toJSON() throws JSONException {
		JSONObject json = new JSONObject();
		if (this.childJobName != null) {
			json.put("childJobName", childJobName);
			json.put("activeFlag", activeFlag);	
			
		}
		return json;

	}

}
