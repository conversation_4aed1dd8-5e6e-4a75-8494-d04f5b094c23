package com.wipro.fipc.entity;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;

@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "client_details", schema = "common")
public class ClientDetails extends BaseModel {

	private static final long serialVersionUID = 1L;

	@Column(name = "client_code")
	@JsonProperty("clientCode")
	private String clientCode;

	@Column(name = "client_name")
	@JsonProperty("clientName")
	private String clientName;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	@JsonProperty("createdDate")
	private Date createdDate;

	@Column(name = "created_by")
	@JsonProperty("createdBy")
	private String createdBy;

	@Column(name = "updated_by")
	@JsonProperty("updatedBy")
	private String updatedBy;

	@Column(name = "client_grp_code")
	@JsonProperty("groupCode")
	private String groupCode;

	@Column(name = "client_grp_name")
	@JsonProperty("groupName")
	private String groupName;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	@JsonProperty("updatedDate")
	private Date updatedDate;

	@OneToMany(cascade = CascadeType.ALL, mappedBy = "clientDetails")
	@LazyCollection(LazyCollectionOption.FALSE)
	@JsonManagedReference(value = "clientDetailsRef")
	private List<BusinessUnitClient> businessUnitClients = new ArrayList<BusinessUnitClient>();

	@Column(name = "wb_client_code")
	private String wbClientCode;

	public String getClientCode() {
		return clientCode;
	}

	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}

	public String getClientName() {
		return clientName;
	}

	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public List<BusinessUnitClient> getBusinessUnitClients() {
		return businessUnitClients;
	}

	public void setBusinessUnitClients(List<BusinessUnitClient> businessUnitClients) {
		this.businessUnitClients = businessUnitClients;
	}

	public String getWbClientCode() {
		return wbClientCode;
	}

	public void setWbClientCode(String wbClientCode) {
		this.wbClientCode = wbClientCode;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
