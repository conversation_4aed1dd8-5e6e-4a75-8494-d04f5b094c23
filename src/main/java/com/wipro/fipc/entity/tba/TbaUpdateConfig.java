package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;
import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TBA_UPDATE_CONFIG", schema = "tba")
public class TbaUpdateConfig extends BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

	@ManyToOne(fetch = FetchType.LAZY) // ,cascade = {CascadeType.ALL})
	@JoinColumn(name = "process_job_mapping_id")
	// @JsonBackReference(value = "processjobmappingConfigRef")
	private ProcessJobMapping processJobMapping;

	@Column(name = "update_name")
	private String updateName;

	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "tba_field_name")
	private String tbaFieldName;

	@Column(name = "panel_disc")
	private String basicInfo;

	@Column(name = "event_name")
	private String eventName;

	@Column(name = "activity_id")
	private int activityId;

	@Column(name = "panel_id")
	private int panelId;

	@Column(name = "class_id")
	private int classId;

	@Column(name = "json_key")
	private String jsonKey;

	@Column(name = "base_key")
	private String baseKey;

	@Column(name = "sub_key")
	private String subKey;

	@Column(name = "meta_data")
	private String metaData;

	@Column(name = "sub_meta_data")
	private String subMetaData;

	@Column(name = "sub_meta_data_id")
	private String subMetaDataId;

	@Column(name = "additional_meta_data")
	private String additionalMetaData;

	@Column(name = "trans_id")
	private String transId;

	@Column(name = "value")
	private String value;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "PAR_NM")
	private String parNm;

	@Column(name = "SEQUENCE")
	private String sequence;

	@Column(name = "RECORD_IDENTIFIER")
	private String recordIdentifier;

	@Column(name = "IDENTIFIER")
	private String identifier;

	@Column(name = "rerun_flag", nullable = true)
	private char rerunFlag;

	@Column(name = "add_manual_flag", nullable = true)
	private char addManualFlag;

	@Column(name = "act_lng_desc")
	private String actLngDesc;

	@Column(name = "TBA_UPDATE_ACTION")
	private String tbaUpdateAction;

	@Column(name = "OVERRIDE_EDITS")
	private String overrideEdits;

	@Column(name = "event_long_desc")
	private String eventLongDesc;

	@Column(name = "parent_tba_update_id")
	private Integer parentTbaUpdateId;
	
	@Column(name = "estimate_tba_inquiries")
	private String estimateTbaInquiries;

	@Column(name = "group_related_panels")
	private Boolean groupRelatedPanels =false;
	
	@Column(name = "pick_from_pending_event")
	private boolean pickFromPendingEvent;

	@Column(name = "skip_trans")
	private boolean skipTrans = false;
}
