package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "PENDING_EVENT_INQ_CONFIG", schema = "tba")
@Where(clause = "active_flag='T'")
public class TbaPendingEvent extends BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMapping;

	@Column(name = "PENDG_EVNT_DEF_NAME")
	private String pendgEvntDefName;

	@Column(name = "EVENT_NAME")
	private String eventName;

	@Column(name = "EVENT_LONG_DESC")
	private String eventLongDesc;

	@Column(name = "identifier")
	private String identifier;

	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "TBA_FIELD_NAME")
	private String tbaFieldName;

	
	@Column(name = "JSON_KEY")
	private String jsonKey;

	@Column(name = "PAR_NM")
	private String parNm;

	@Column(name = "ACTIVITY_ID")
	private int activityId;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "panel_id")
	private String panelId;
	
	@Column(name = "panel_disc")
	private String panelDisc;
	
	@Column(name = "meta_data")
	private String metaData;
	
	@Column(name = "trans_id")
	private String transId;
	
	@Column(name = "base_key")
	private String baseKey;
	
	@Column(name = "sub_key")
	private String subKey;
	
	@Column(name = "class_id")
	private String classId;
	@Transient
	private String clientId;
	
	@Column(name = "sequence")
	private String sequence;
	
	@Column(name = "identify_flag")
	private String identifyFlag;
	
	@Column(name = "manual_flag")
	private char manualFlag;
	
	@Column(name = "process_multiple_instances")
	private boolean processMultipleOccurrences;

	@Column(name = "critical_edits", nullable = false)
	private boolean criticalEdits=false;

	public boolean isCriticalEdits() {
		return criticalEdits;
	}

	public void setCriticalEdits(boolean criticalEdits) {
		this.criticalEdits = criticalEdits;
	}

	public boolean isProcessMultipleOccurrences() {
		return processMultipleOccurrences;
	}

	public void setProcessMultipleOccurrences(boolean processMultipleOccurrences) {
		this.processMultipleOccurrences = processMultipleOccurrences;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public String getPendgEvntDefName() {
		return pendgEvntDefName;
	}

	public void setPendgEvntDefName(String pendgEvntDefName) {
		this.pendgEvntDefName = pendgEvntDefName;
	}

	public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}

	public String getEventLongDesc() {
		return eventLongDesc;
	}

	public void setEventLongDesc(String eventLongDesc) {
		this.eventLongDesc = eventLongDesc;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getTbaFieldName() {
		return tbaFieldName;
	}

	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}

	public String getJsonKey() {
		return jsonKey;
	}

	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}

	public String getParNm() {
		return parNm;
	}

	public void setParNm(String parNm) {
		this.parNm = parNm;
	}

	public int getActivityId() {
		return activityId;
	}

	public void setActivityId(int activityId) {
		this.activityId = activityId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getPanelId() {
		return panelId;
	}

	public void setPanelId(String panelId) {
		this.panelId = panelId;
	}

	public String getPanelDisc() {
		return panelDisc;
	}

	public void setPanelDisc(String panelDisc) {
		this.panelDisc = panelDisc;
	}

	public String getMetaData() {
		return metaData;
	}

	public void setMetaData(String metaData) {
		this.metaData = metaData;
	}

	public String getTransId() {
		return transId;
	}

	public void setTransId(String transId) {
		this.transId = transId;
	}

	public String getBaseKey() {
		return baseKey;
	}

	public void setBaseKey(String baseKey) {
		this.baseKey = baseKey;
	}

	public String getSubKey() {
		return subKey;
	}

	public void setSubKey(String subKey) {
		this.subKey = subKey;
	}

	public String getClassId() {
		return classId;
	}

	public void setClassId(String classId) {
		this.classId = classId;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	public String getIdentifyFlag() {
		return identifyFlag;
	}

	public void setIdentifyFlag(String identifyFlag) {
		this.identifyFlag = identifyFlag;
	}

	public char getManualFlag() {
		return manualFlag;
	}

	public void setManualFlag(char manualFlag) {
		this.manualFlag = manualFlag;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
}
