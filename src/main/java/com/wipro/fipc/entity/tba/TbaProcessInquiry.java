package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "TBA_PROCESS_INQUIRY", schema = "tba")
public class TbaProcessInquiry extends BaseModel implements Serializable{
	
	private static final long serialVersionUID = 1L;

	@Column(name = "process_name")
	private String processName;
	
	@Column(name = "inquiry_name")
	private String inquiryName;
	
	@Column(name = "client_id")
	private int clientId;
	
	@Column(name = "panel_id")
	private int panelId;
	@Column(name="PAR_NM")
	private String parNM;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;
	
}
