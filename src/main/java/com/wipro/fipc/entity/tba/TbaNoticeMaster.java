package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.sql.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "TBA_NOTICE_MASTER", schema = "tba")
public class TbaNoticeMaster extends BaseModel implements Serializable  {
	
	@Column(name = "client_id",nullable = false)
	private int clientId;

	@Column(name = "notice_name",nullable = false)
	private String noticeName;
	
	@Column(name = "notice_id",nullable = false)
	private int noticeId;
	
	
	@Column(name="created_date", nullable = true)
	private Date createdDate;
	
	@Column(name="created_by", nullable = true)
	private String createdBy;
	
	@Column(name="updated_by", nullable = true)
	private String updatedBy;
	
	@Column(name="updated_date", nullable = true)
	private Date updatedDate;
	
	@Column(name="tba_field_type", nullable = true)
	private String tbaFieldType;
	
	@Column(name="metadata", nullable = true)
	private String metadata;

	public int getClientId() {
		return clientId;
	}

	public void setClientId(int clientId) {
		this.clientId = clientId;
	}

	public String getNoticeName() {
		return noticeName;
	}

	public void setNoticeName(String noticeName) {
		this.noticeName = noticeName;
	}

	public int getNoticeId() {
		return noticeId;
	}

	public void setNoticeId(int noticeId) {
		this.noticeId = noticeId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getTbaFieldType() {
		return tbaFieldType;
	}

	public void setTbaFieldType(String tbaFieldType) {
		this.tbaFieldType = tbaFieldType;
	}

	public String getMetadata() {
		return metadata;
	}

	public void setMetadata(String metadata) {
		this.metadata = metadata;
	}
	
	
}
