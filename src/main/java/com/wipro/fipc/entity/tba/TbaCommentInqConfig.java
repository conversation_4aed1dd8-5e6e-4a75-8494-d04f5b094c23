package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TBA_COMMENT_INQ_CONFIG", schema = "tba")
public class TbaCommentInqConfig extends BaseModel implements Serializable {

	@Column(name = "INQUIRY_DEF_NAME")
	private String inquiryDefName;

	@Column(name = "EFT_FROM_DATE")
	private String eftFromDate;

	@Column(name = "EFT_TO_DATE")
	private String eftToDate;

	@Column(name = "tba_field_name")
	private String tbaFieldName;

	@Column(name = "JSON_KEY")
	private String jsonKey;

	@Column(name = "SUB_JSON_KEY")
	private String subJsonKey;

	@Column(name = "field_type")
	private String fieldType;

	@Column(name = "PAR_NM")
	private String parNM;

	@Column(name = "active_flag")
	private char activeFlag;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@Column(name = "is_update")
	private Boolean updateFlag;

	@ManyToOne(fetch = FetchType.LAZY) // ,cascade = {CascadeType.ALL})
	@JoinColumn(name = "PROCESS_JOB_MAPPING_ID")
	// @JsonBackReference(value = "processjobmappingConfigRef")
	private ProcessJobMapping processJobMapping;

	public String getInquiryDefName() {
		return inquiryDefName;
	}

	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}

	public String getEftFromDate() {
		return eftFromDate;
	}

	public void setEftFromDate(String eftFromDate) {
		this.eftFromDate = eftFromDate;
	}

	public String getEftToDate() {
		return eftToDate;
	}

	public void setEftToDate(String eftToDate) {
		this.eftToDate = eftToDate;
	}

	public String getTbaFieldName() {
		return tbaFieldName;
	}

	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}

	public String getJsonKey() {
		return jsonKey;
	}

	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}

	public String getSubJsonKey() {
		return subJsonKey;
	}

	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}

	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}

	public String getParNM() {
		return parNM;
	}

	public void setParNM(String parNM) {
		this.parNM = parNM;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public Boolean getUpdateFlag() {
		return updateFlag;
	}

	public void setUpdateFlag(Boolean updateFlag) {
		this.updateFlag = updateFlag;
	}

	@Override
	public String toString() {
		return "TbaCommentInqConfig [inquiryDefName=" + inquiryDefName + ", eftFromDate=" + eftFromDate + ", eftToDate="
				+ eftToDate + ", tbaFieldName=" + tbaFieldName + ", jsonKey=" + jsonKey + ", subJsonKey=" + subJsonKey
				+ ", fieldType=" + fieldType + ", parNM=" + parNM + ", activeFlag=" + activeFlag + ", createdDate="
				+ createdDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate
				+ ", updateFlag=" + updateFlag + ", processJobMapping=" + processJobMapping + "]";
	}

}
