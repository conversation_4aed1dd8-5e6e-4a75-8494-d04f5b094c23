package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@SuppressWarnings("serial")
@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TBA_NOTICE_INQ_CONFIG", schema = "tba")
public class TbaNoticeInqConfig extends BaseModel implements Serializable {
	@Column(name = "process_job_mapping_id", nullable = false)
	private long processJobMappingId;

	@Column(name = "add_manual_flag", nullable = false)
	private char addManualFlag;

	@Column(name = "notice_name", nullable = false)
	private String noticeName;

	@Column(name = "notice_id", nullable = false)
	private int noticeId;

	@Column(name = "client_id", nullable = false)
	private int clientId;

	@Column(name = "inquiry_def_name", nullable = false)
	private String inquiryDefName;

	@Column(name = "tba_field_name", nullable = false)
	private String tba_field_name;

	@Column(name = "json_key", nullable = false)
	private String jsonKey;

	@Column(name = "sub_json_key", nullable = false)
	private String subJsonKey;

	@Column(name = "metadata", nullable = false)
	private String metadata;

	@Column(name = "field_type", nullable = false)
	private String fieldType;

	@Column(name = "par_nm", nullable = false)
	private String parNm;

	@Column(name = "record_identifier", nullable = false)
	private String recordIdentifier;

	@Column(name = "identifier", nullable = false)
	private String identifier;

	@Column(name = "active_flag", nullable = false)
	private char activeFlag;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date", nullable = true)
	private Date createdDate;

	@Column(name = "created_by", nullable = true)
	private String createdBy;

	@Column(name = "updated_by", nullable = true)
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date", nullable = true)
	private Date updatedDate;

	public long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public char getAddManualFlag() {
		return addManualFlag;
	}

	public void setAddManualFlag(char addManualFlag) {
		this.addManualFlag = addManualFlag;
	}

	public String getNoticeName() {
		return noticeName;
	}

	public void setNoticeName(String noticeName) {
		this.noticeName = noticeName;
	}

	public int getNoticeId() {
		return noticeId;
	}

	public void setNoticeId(int noticeId) {
		this.noticeId = noticeId;
	}

	public int getClientId() {
		return clientId;
	}

	public void setClientId(int clientId) {
		this.clientId = clientId;
	}

	public String getInquiryDefName() {
		return inquiryDefName;
	}

	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}

	public String getTba_field_name() {
		return tba_field_name;
	}

	public void setTba_field_name(String tba_field_name) {
		this.tba_field_name = tba_field_name;
	}

	public String getJsonKey() {
		return jsonKey;
	}

	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}

	public String getSubJsonKey() {
		return subJsonKey;
	}

	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}

	public String getMetadata() {
		return metadata;
	}

	public void setMetadata(String metadata) {
		this.metadata = metadata;
	}

	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}

	public String getParNm() {
		return parNm;
	}

	public void setParNm(String parNm) {
		this.parNm = parNm;
	}

	public String getRecordIdentifier() {
		return recordIdentifier;
	}

	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	@Override
	public String toString() {
		return "TbaNoticeInqConfig [processJobMappingId=" + processJobMappingId + ", addManualFlag=" + addManualFlag
				+ ", noticeName=" + noticeName + ", noticeId=" + noticeId + ", clientId=" + clientId
				+ ", inquiryDefName=" + inquiryDefName + ", tba_field_name=" + tba_field_name + ", jsonKey=" + jsonKey
				+ ", subJsonKey=" + subJsonKey + ", metadata=" + metadata + ", fieldType=" + fieldType + ", parNm="
				+ parNm + ", recordIdentifier=" + recordIdentifier + ", identifier=" + identifier + ", activeFlag="
				+ activeFlag + ", createdDate=" + createdDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy
				+ ", updatedDate=" + updatedDate + "]";
	}

}
