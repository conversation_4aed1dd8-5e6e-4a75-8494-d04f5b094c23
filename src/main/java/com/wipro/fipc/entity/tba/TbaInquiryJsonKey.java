package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "TBA_INQUIRY_JSON_KEY", schema = "tba")
public class TbaInquiryJsonKey extends BaseModel implements Serializable{
	
	private static final long serialVersionUID = 1L;


/*	@Column(name = "PANEL_ID")
	private int panelId;*/
	
	@Column(name = "JSON_KEY")
	private String jsonKey;
	
	@Column(name = "SUB_JSON_KEY")
	private String subJsonKey;
	
	@Column(name = "tba_field_name")
	private String tbaFieldName;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Column(name="field_type")
	private String fieldType;
	
	@Column(name="PAR_NM")
	private String parNM;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;
	
	@Column(name = "flag")
	private Character flag;
	
	@Column(name = "filter_flag")
	private Character filterFlag;
	
	@Column(name = "depends_on")
	private String dependsOn;

	public String getJsonKey() {
		return jsonKey;
	}

	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}

	public String getSubJsonKey() {
		return subJsonKey;
	}

	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}

	public String getTbaFieldName() {
		return tbaFieldName;
	}

	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}

	public String getParNM() {
		return parNM;
	}

	public void setParNM(String parNM) {
		this.parNM = parNM;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public Character getFlag() {
		return flag;
	}

	public void setFlag(Character flag) {
		this.flag = flag;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!super.equals(obj))
			return false;
		if (getClass() != obj.getClass())
			return false;
		TbaInquiryJsonKey other = (TbaInquiryJsonKey) obj;
		return Objects.equals(jsonKey, other.jsonKey) && Objects.equals(parNM, other.parNM)
				&& Objects.equals(subJsonKey, other.subJsonKey) && Objects.equals(tbaFieldName, other.tbaFieldName);
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = super.hashCode();
		result = prime * result + Objects.hash(jsonKey, parNM, subJsonKey, tbaFieldName);
		return result;
	}
	
	
}