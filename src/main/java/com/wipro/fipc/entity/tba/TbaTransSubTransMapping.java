package com.wipro.fipc.entity.tba;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "tba_trans_sub_trans_mapping", schema = "tba")
@Where(clause = "active_flag='T'")
public class TbaTransSubTransMapping extends BaseModel {
	private static final long serialVersionUID = 1L;

	@Column(name = "client_id")
	private int clientId;

	@Column(name = "activity_id")
	private int activityId;

	@Column(name = "panel_id")
	private int panelId;

	@Column(name = "trans_id")
	private String transId;

	@Column(name = "sub_trans_id")
	private String subTransId;

	@Column(name = "sub_trans_desc")
	private String subTransDesc;

	@Column(name = "sub_trans_code")
	private String subTransCode;

	@Column(name = "sub_trans_code_desc")
	private String subTransCodeDesc;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "updated_by")
	private String updatedBy;

	@Column(name = "active_flag")
	private String activeFlag;

}
