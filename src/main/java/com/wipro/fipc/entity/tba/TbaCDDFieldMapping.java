package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "tba_cdd_field_master", schema = "tba")
public class TbaCDDFieldMapping extends BaseModel implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	
	@Column(name = "cdd_field_id")
	private String cddFieldId;
	
	@Column(name = "client_id")
	private int clientId;
	
	@Column(name = "panel_id")
	private int panelId;
	
	@Column(name = "trans_id")
	private int transId;
	
	@Column(name = "def_id")
	private int defId;
	
	@Column(name = "cdd_field_code")
	private String cddFieldCode;
	
	@Column(name = "cdd_field_desc")
	private String cddFieldDesc;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;

	public String getCddFieldId() {
		return cddFieldId;
	}

	public void setCddFieldId(String cddFieldId) {
		this.cddFieldId = cddFieldId;
	}

	public int getClientId() {
		return clientId;
	}

	public void setClientId(int clientId) {
		this.clientId = clientId;
	}

	public int getPanelId() {
		return panelId;
	}

	public void setPanelId(int panelId) {
		this.panelId = panelId;
	}

	public int getTransId() {
		return transId;
	}

	public void setTransId(int transId) {
		this.transId = transId;
	}

	public int getDefId() {
		return defId;
	}

	public void setDefId(int defId) {
		this.defId = defId;
	}

	public String getCddFieldCode() {
		return cddFieldCode;
	}

	public void setCddFieldCode(String cddFieldCode) {
		this.cddFieldCode = cddFieldCode;
	}

	public String getCddFieldDesc() {
		return cddFieldDesc;
	}

	public void setCddFieldDesc(String cddFieldDesc) {
		this.cddFieldDesc = cddFieldDesc;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	

}
