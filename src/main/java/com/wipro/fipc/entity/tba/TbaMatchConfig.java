package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TBA_MATCH_CONFIG", schema = "tba")
@Where(clause = "active_flag='T'")
public class TbaMatchConfig extends BaseModel implements Serializable {

	@Column(name = "file_name")
	private String fileName;

	@Column(name = "match_type")
	private String matchType;

	@Column(name = "file_name_dest")
	private String fileNameDest;

	@Column(name = "report_identifier_dest")
	private String reportIdentifierDest;

	@Column(name = "mf_field_name_dest")
	private String mfFieldNameDest;

	@Column(name = "mf_field_wout_space_dest")
	private String mfFieldWoutSpaceDest;

	@Column(name = "report_identifier")
	private String reportIdentifier;

	@Column(name = "mf_field_name")
	private String mfFieldName;

	@Column(name = "mf_field_wout_space")
	private String mfFieldWoutSpace;

	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "tba_field_name")
	private String tbaFieldName;

	@Column(name = "inquiry_def_name")
	private String inquiryDefName;

	@Column(name = "rule_name")
	private String ruleName;

	@Column(name = "RESULT_FIELD ")
	private String resultField;

	@Column(name = "corrective_action")
	private String correctiveAction;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@ManyToOne
	@JoinColumn(name = "process_job_mapping_id")
	// @JsonBackReference(value="processjobmappingRef")
	private ProcessJobMapping processJobMapping;

	@Column(name = "IDENTIFIER")
	private String identifier;

	@Column(name = "ACTIONS")
	private String actions;

	@Column(name = "sheet_name")
	private String sheetName;

	@Column(name = "sheet_name_wout_space")
	private String sheetNameWoutSpace;

	@Column(name = "sheet_name_dest")
	private String sheetNameDest;

	@Column(name = "sheet_name_dest_wout_space")
	private String sheetNameDestWoutSpace;

	@Column(name = "file_name_wout_space")
	private String fileNameWoutSpace;

	@Column(name = "file_name_dest_wout_space")
	private String fileNameDestWoutSpace;

	@Column(name = "ppt_verify_tba")
	private String pptVerifyTba;

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getMatchType() {
		return matchType;
	}

	public void setMatchType(String matchType) {
		this.matchType = matchType;
	}

	public String getFileNameDest() {
		return fileNameDest;
	}

	public void setFileNameDest(String fileNameDest) {
		this.fileNameDest = fileNameDest;
	}

	public String getReportIdentifierDest() {
		return reportIdentifierDest;
	}

	public void setReportIdentifierDest(String reportIdentifierDest) {
		this.reportIdentifierDest = reportIdentifierDest;
	}

	public String getMfFieldNameDest() {
		return mfFieldNameDest;
	}

	public void setMfFieldNameDest(String mfFieldNameDest) {
		this.mfFieldNameDest = mfFieldNameDest;
	}

	public String getMfFieldWoutSpaceDest() {
		return mfFieldWoutSpaceDest;
	}

	public void setMfFieldWoutSpaceDest(String mfFieldWoutSpaceDest) {
		this.mfFieldWoutSpaceDest = mfFieldWoutSpaceDest;
	}

	public String getReportIdentifier() {
		return reportIdentifier;
	}

	public void setReportIdentifier(String reportIdentifier) {
		this.reportIdentifier = reportIdentifier;
	}

	public String getMfFieldName() {
		return mfFieldName;
	}

	public void setMfFieldName(String mfFieldName) {
		this.mfFieldName = mfFieldName;
	}

	public String getMfFieldWoutSpace() {
		return mfFieldWoutSpace;
	}

	public void setMfFieldWoutSpace(String mfFieldWoutSpace) {
		this.mfFieldWoutSpace = mfFieldWoutSpace;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getTbaFieldName() {
		return tbaFieldName;
	}

	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}

	public String getInquiryDefName() {
		return inquiryDefName;
	}

	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getResultField() {
		return resultField;
	}

	public void setResultField(String resultField) {
		this.resultField = resultField;
	}

	public String getCorrectiveAction() {
		return correctiveAction;
	}

	public void setCorrectiveAction(String correctiveAction) {
		this.correctiveAction = correctiveAction;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public String getActions() {
		return actions;
	}

	public void setActions(String actions) {
		this.actions = actions;
	}

	public String getSheetName() {
		return sheetName;
	}

	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}

	public String getSheetNameWoutSpace() {
		return sheetNameWoutSpace;
	}

	public void setSheetNameWoutSpace(String sheetNameWoutSpace) {
		this.sheetNameWoutSpace = sheetNameWoutSpace;
	}

	public String getSheetNameDest() {
		return sheetNameDest;
	}

	public void setSheetNameDest(String sheetNameDest) {
		this.sheetNameDest = sheetNameDest;
	}

	public String getSheetNameDestWoutSpace() {
		return sheetNameDestWoutSpace;
	}

	public void setSheetNameDestWoutSpace(String sheetNameDestWoutSpace) {
		this.sheetNameDestWoutSpace = sheetNameDestWoutSpace;
	}

	public String getFileNameWoutSpace() {
		return fileNameWoutSpace;
	}

	public void setFileNameWoutSpace(String fileNameWoutSpace) {
		this.fileNameWoutSpace = fileNameWoutSpace;
	}

	public String getFileNameDestWoutSpace() {
		return fileNameDestWoutSpace;
	}

	public void setFileNameDestWoutSpace(String fileNameDestWoutSpace) {
		this.fileNameDestWoutSpace = fileNameDestWoutSpace;
	}

	public String getPptVerifyTba() {
		return pptVerifyTba;
	}

	public void setPptVerifyTba(String pptVerifyTba) {
		this.pptVerifyTba = pptVerifyTba;
	}

	@Override
	public String toString() {
		return "TbaMatchConfig [fileName=" + fileName + ", matchType=" + matchType + ", fileNameDest=" + fileNameDest
				+ ", reportIdentifierDest=" + reportIdentifierDest + ", mfFieldNameDest=" + mfFieldNameDest
				+ ", mfFieldWoutSpaceDest=" + mfFieldWoutSpaceDest + ", reportIdentifier=" + reportIdentifier
				+ ", mfFieldName=" + mfFieldName + ", mfFieldWoutSpace=" + mfFieldWoutSpace + ", activeFlag="
				+ activeFlag + ", tbaFieldName=" + tbaFieldName + ", inquiryDefName=" + inquiryDefName + ", ruleName="
				+ ruleName + ", resultField=" + resultField + ", correctiveAction=" + correctiveAction
				+ ", createdDate=" + createdDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy
				+ ", updatedDate=" + updatedDate + ", processJobMapping=" + processJobMapping + ", identifier="
				+ identifier + ", actions=" + actions + ", sheetName=" + sheetName + ", sheetNameWoutSpace="
				+ sheetNameWoutSpace + ", sheetNameDest=" + sheetNameDest + ", sheetNameDestWoutSpace="
				+ sheetNameDestWoutSpace + ", fileNameWoutSpace=" + fileNameWoutSpace + ", fileNameDestWoutSpace="
				+ fileNameDestWoutSpace + ", pptVerifyTba=" + pptVerifyTba + "]";
	}

}
