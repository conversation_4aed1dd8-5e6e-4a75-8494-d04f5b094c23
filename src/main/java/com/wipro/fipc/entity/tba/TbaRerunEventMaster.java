package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.sql.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "TBA_RERUN_EVENT_MASTER", schema = "tba")
public class TbaRerunEventMaster extends BaseModel implements Serializable {

	@Column(name = "client_id",nullable = false)
	private int clientId;
	
	@Column(name = "event_name",nullable = false)
	private String eventName;
	
	@Column(name = "activity_id",nullable = false)
	private int activityId;
	
	@Column(name = "act_long_desc",nullable = false)
	private String actLongDesc;
	
	@Column(name = "act_type_cd",nullable = false)
	private int actTypeCd;
	
	@Column(name="created_date")
	private Date createdDate;

	@Column(name="created_by")
	private String createdBy;

	@Column(name="updated_by")
	private String updatedBy;

	@Column(name="updated_date")
	private Date updatedDate;

	public int getClientId() {
		return clientId;
	}

	public void setClientId(int clientId) {
		this.clientId = clientId;
	}

	public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}

	public int getActivityId() {
		return activityId;
	}

	public void setActivityId(int activityId) {
		this.activityId = activityId;
	}

	public String getActLongDesc() {
		return actLongDesc;
	}

	public void setActLongDesc(String actLongDesc) {
		this.actLongDesc = actLongDesc;
	}

	public int getActTypeCd() {
		return actTypeCd;
	}

	public void setActTypeCd(int actTypeCd) {
		this.actTypeCd = actTypeCd;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	
		
}
