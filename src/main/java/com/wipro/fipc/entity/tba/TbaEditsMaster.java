package com.wipro.fipc.entity.tba;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "TBA_EDITS_MASTER", schema = "tba")
public class TbaEditsMaster extends BaseModel {
	
	private static final long serialVersionUID = 1L;
	
	@Column(name = "client_id")
	private int clientId;
	
	@Column(name = "edit_id")
	private int editId;
	
	@Column(name="edit_desc")
	private String editDesc;
	
	@Column(name="event_name")
	private String eventName;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	
	@Column(name="updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;

}
