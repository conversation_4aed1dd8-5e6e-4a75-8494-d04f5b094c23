package com.wipro.fipc.entity.tba;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TBA_WELFARE_INQUIRY_CONFIG", schema = "tba")
@Where(clause = "active_flag='T'")
public class TbaWelfareInquiryConfig extends BaseModel{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "process_job_mapping_id")
	private long processJobMappingId;
	
	@Column(name = "practice_area_code")
	private String practiceAreaCode;
	
	@Column(name = "tba_field_name")
	private String tbaFieldName;
	
	@Column(name = "active_flag")
	private String activeFlag;
	
	@Column(name = "created_by")
	private String createdBy;
	
	@Column(name = "updated_by")
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@Column(name = "json_key")
	private String jsonKey;
	
	@Column(name = "sub_json_key")
	private String subJsonKey;
	
	@Column(name = "field_type")
	private String fieldType;
	
	@Column(name = "par_name")
	private String parName;
	
	@Column(name = "event_name")
	private String eventName;
	
	@Column(name = "event_long_desc")
	private String eventLongDesc;

}
