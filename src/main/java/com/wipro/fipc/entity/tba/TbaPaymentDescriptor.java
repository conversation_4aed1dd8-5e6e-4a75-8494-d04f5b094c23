package com.wipro.fipc.entity.tba;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "tba_payment_descriptor_master", schema = "tba")
public class TbaPaymentDescriptor extends BaseModel implements Serializable{
	
private static final long serialVersionUID = 1L;
	
	@Column(name = "client_id")
	private int clientId;

	@Column(name = "panel_id")
	private int panelId;
	
	@Column(name = "pmt_sdsc_tx")
	private String paymentShortDescription;
	
	@Column(name = "pmt_id")
	private String paymentId;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private String createdBy;
	
	@Column(name="updated_by")
	private String updatedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date")
	private Date updatedDate;
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "paymentDescriptor")
	private List<TbaPaymentDeliveryType> tbaPaymentDeliveryType = new ArrayList<TbaPaymentDeliveryType>();
	
	public int getClientId() {
		return clientId;
	}
	public void setClientId(int clientId) {
		this.clientId = clientId;
	}
	public int getPanelId() {
		return panelId;
	}
	public void setPanelId(int panelId) {
		this.panelId = panelId;
	}
	public String getPaymentShortDescription() {
		return paymentShortDescription;
	}
	public void setPaymentShortDescription(String paymentShortDescription) {
		this.paymentShortDescription = paymentShortDescription;
	}
	public String getPaymentId() {
		return paymentId;
	}
	public void setPaymentId(String paymentId) {
		this.paymentId = paymentId;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public List<TbaPaymentDeliveryType> getTbaPaymentDeliveryType() {
		return tbaPaymentDeliveryType;
	}
	public void setTbaPaymentDeliveryType(List<TbaPaymentDeliveryType> tbaPaymentDeliveryType) {
		this.tbaPaymentDeliveryType = tbaPaymentDeliveryType;
	}

	
	

}