package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "tba_plan_descriptor_master", schema = "tba")
public class TbaPlanDescriptor extends BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

	@Column(name = "client_id")
	private int clientId;

	@Column(name = "activity_id")
	private int activityId;

	@Column(name = "panel_id")
	private int panelId;

	@Column(name = "plan_id")
	private int planId;

	@Column(name = "plan_sdsc_tx")
	private String planShortDescription;

	@Column(name = "benefit_id")
	private int benefitId;

	@Column(name = "benefit_sdsc_tx")
	private String benefitShortDescription;

	@Column(name = "db_elec_id")
	private int dbElectionId;

	@Column(name = "db_elec_sdsc_tx")
	private String dbElectionShortDescription;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "active_flag")
	private String activeFlag;
	
	public String getDbElectionIdToString() {
		return String.valueOf(dbElectionId);
	}

}
