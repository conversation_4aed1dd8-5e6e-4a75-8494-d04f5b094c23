package com.wipro.fipc.entity.tba;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TBA_INQUIRY_CONFIG", schema = "tba")
@Where(clause = "active_flag='T'")
public class TbaInquiryConfig extends BaseModel {

	private static final long serialVersionUID = 1L;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "process_job_mapping_id")
	// @JsonBackReference(value="processjobmappingConfigRef")
	private ProcessJobMapping processJobMapping;

	@Column(name = "INQUIRY_NAME")
	private String inquiryName;

	@Column(name = "PAR_NM")
	private String parNM;

	@Column(name = "panel_id")
	private int panelId;

	@Column(name = "active_flag")
	private String activeFlag;

	@Column(name = "tba_field_name")
	private String tbaFieldName;

	@Column(name = "field_type")
	private String fieldType;

	@Column(name = "JSON_KEY")
	private String jsonKey;

	@Column(name = "SUB_JSON_KEY")
	private String subJsonKey;

	@Column(name = "metadata")
	private String metaData;

	@Column(name = "identifier")
	private String identifier;

	@Column(name = "record_identifier")
	private String recordIdentifier;

	@Column(name = "INQUIRY_DEF_NAME")
	private String inquiryDefName;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "sequence")
	private String sequence;

	@Column(name = "EFF_DATE_TYPE")
	private String effDateType;
	@Column(name = "EFF_FROM_DATE")
	private String effFromDate;
	@Column(name = "EFF_TO_DATE")
	private String effToDate;
	@Column(name = "ROW_MATRIX")
	private String rowMatrix;
	@Column(name = "COLUMN_MATRIX")
	private String columnMatrix;
	
	@Column(name="condition_json")
	private String conditionJson;
	
	@Column(name = "flag")
	private Character flag;
	
	@Column(name = "estimate_mode")
	private Boolean estimateMode = false;

	public Boolean getEstimateMode() {
		return estimateMode;
	}

	public void setEstimateMode(Boolean estimateMode) {
		this.estimateMode = estimateMode;
	}


	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public String getInquiryName() {
		return inquiryName;
	}

	public void setInquiryName(String inquiryName) {
		this.inquiryName = inquiryName;
	}

	public String getParNM() {
		return parNM;
	}

	public void setParNM(String parNM) {
		this.parNM = parNM;
	}

	public int getPanelId() {
		return panelId;
	}

	public void setPanelId(int panelId) {
		this.panelId = panelId;
	}

	public String getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getTbaFieldName() {
		return tbaFieldName;
	}

	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}

	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}

	public String getJsonKey() {
		return jsonKey;
	}

	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}

	public String getSubJsonKey() {
		return subJsonKey;
	}

	public void setSubJsonKey(String subJsonKey) {
		this.subJsonKey = subJsonKey;
	}

	public String getMetaData() {
		return metaData;
	}

	public void setMetaData(String metaData) {
		this.metaData = metaData;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public String getRecordIdentifier() {
		return recordIdentifier;
	}

	public void setRecordIdentifier(String recordIdentifier) {
		this.recordIdentifier = recordIdentifier;
	}

	public String getInquiryDefName() {
		return inquiryDefName;
	}

	public void setInquiryDefName(String inquiryDefName) {
		this.inquiryDefName = inquiryDefName;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	public String getEffDateType() {
		return effDateType;
	}

	public void setEffDateType(String effDateType) {
		this.effDateType = effDateType;
	}

	public String getEffFromDate() {
		return effFromDate;
	}

	public void setEffFromDate(String effFromDate) {
		this.effFromDate = effFromDate;
	}

	public String getEffToDate() {
		return effToDate;
	}

	public void setEffToDate(String effToDate) {
		this.effToDate = effToDate;
	}

	public String getRowMatrix() {
		return rowMatrix;
	}

	public void setRowMatrix(String rowMatrix) {
		this.rowMatrix = rowMatrix;
	}

	public String getColumnMatrix() {
		return columnMatrix;
	}

	public void setColumnMatrix(String columnMatrix) {
		this.columnMatrix = columnMatrix;
	}

	public Character getFlag() {
		return flag;
	}

	public void setFlag(Character flag) {
		this.flag = flag;
	}

	@Override
	public String toString() {
		return "TbaInquiryConfig [processJobMapping=" + processJobMapping + ", inquiryName=" + inquiryName + ", parNM="
				+ parNM + ", panelId=" + panelId + ", activeFlag=" + activeFlag + ", tbaFieldName=" + tbaFieldName
				+ ", fieldType=" + fieldType + ", jsonKey=" + jsonKey + ", subJsonKey=" + subJsonKey + ", metaData="
				+ metaData + ", identifier=" + identifier + ", recordIdentifier=" + recordIdentifier
				+ ", inquiryDefName=" + inquiryDefName + ", createdDate=" + createdDate + ", createdBy=" + createdBy
				+ ", updatedBy=" + updatedBy + ", updatedDate=" + updatedDate + ", sequence=" + sequence
				+ ", effDateType=" + effDateType + ", effFromDate=" + effFromDate + ", effToDate=" + effToDate
				+ ", rowMatrix=" + rowMatrix + ", columnMatrix=" + columnMatrix + ", conditionJson=" + conditionJson
				+ ", flag=" + flag + ", estimateMode=" + estimateMode + "]";
	}

}
