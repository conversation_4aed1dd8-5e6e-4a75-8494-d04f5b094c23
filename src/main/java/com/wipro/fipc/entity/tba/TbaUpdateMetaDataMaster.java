package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "tba_update_metadata_master", schema = "tba")
public class TbaUpdateMetaDataMaster extends BaseModel implements Serializable {

	public TbaUpdateMetaDataMaster() {
	}

	public TbaUpdateMetaDataMaster(int panelId, int transId, TbaUpdateMetaDataMaster updateMetaDataMaster) {
		this.clientId = updateMetaDataMaster.clientId;
		this.panelId = panelId;
		this.transId = String.valueOf(transId);
		this.metadataType = updateMetaDataMaster.metadataType;
		this.metadataTag = updateMetaDataMaster.metadataTag;
		this.categoryId = updateMetaDataMaster.categoryId;
		this.categoryDescription = updateMetaDataMaster.categoryDescription;
		this.statusCode = updateMetaDataMaster.statusCode;
		this.statusDescription = updateMetaDataMaster.statusDescription;
		this.createdBy = updateMetaDataMaster.createdBy;
		this.createdDate = updateMetaDataMaster.createdDate;
		this.updatedBy = updateMetaDataMaster.updatedBy;
		this.updatedDate = updateMetaDataMaster.updatedDate;
	}

	private static final long serialVersionUID = 1L;

	@Column(name = "client_id")
	private int clientId;

	@Column(name = "panel_id")
	private int panelId;

	@Column(name = "trans_id")
	private String transId;

	@Column(name = "metadata_type")
	private String metadataType;

	@Column(name = "metadata_tag")
	private String metadataTag;

	@Column(name = "category_id")
	private String categoryId;

	@Column(name = "category_description")
	private String categoryDescription;

	@Column(name = "status_code")
	private String statusCode;

	@Column(name = "status_description")
	private String statusDescription;

	@Column(name = "created_by")
	private String createdBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
}
