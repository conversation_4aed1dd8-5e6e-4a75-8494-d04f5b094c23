package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "EVENT_HIST_INQ_CONFIG", schema = "tba")
public class EventHistInqConfig extends BaseModel implements Serializable {
	@Column(name = "process_job_mapping_id",nullable = false)
	private long processJobMappingId;
	
	@Column(name = "event_hist_def_name",nullable = false)
	private String eventHistDefName;
	
	@Column(name = "eff_from_date",nullable = false)
	private String effFromDate;
	
	@Column(name = "eff_to_date",nullable = false)
	private String effToDate;
	
	@Column(name = "event_name",nullable = false)
	private String eventName;
	
	@Column(name = "act_long_desc",nullable = false)
	private String actLongDesc;
	
	@Column(name = "active_flag",nullable = false)
	private char activeFlag;
	
	@Column(name = "tba_field_name")
	private String tbaFiledName;
	
	@Column(name = "json_key")
	private String jsonKey;
	
	@Column(name = "field_type")
	private String filedType;
	
	@Column(name = "par_nm")
	private String parNm;
	
	@Column(name = "activity_id")
	private int activityId;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_date", nullable = true)
	private Date createdDate;
	
	@Column(name="created_by", nullable = true)
	private String createdBy;
	
	@Column(name="updated_by", nullable = true)
	private String updatedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="updated_date", nullable = true)
	private Date updatedDate;
	
	@Column(name = "manual_flag")
	private char manualFlag;

	public long getProcessJobMappingId() {
		return processJobMappingId;
	}

	public void setProcessJobMappingId(long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}

	public String getEventHistDefName() {
		return eventHistDefName;
	}

	public void setEventHistDefName(String eventHistDefName) {
		this.eventHistDefName = eventHistDefName;
	}

	public String getEffFromDate() {
		return effFromDate;
	}

	public void setEffFromDate(String effFromDate) {
		this.effFromDate = effFromDate;
	}

	public String getEffToDate() {
		return effToDate;
	}

	public void setEffToDate(String effToDate) {
		this.effToDate = effToDate;
	}

	public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}

	public String getActLongDesc() {
		return actLongDesc;
	}

	public void setActLongDesc(String actLongDesc) {
		this.actLongDesc = actLongDesc;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getTbaFiledName() {
		return tbaFiledName;
	}

	public void setTbaFiledName(String tbaFiledName) {
		this.tbaFiledName = tbaFiledName;
	}

	public String getJsonKey() {
		return jsonKey;
	}

	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}

	public String getFiledType() {
		return filedType;
	}

	public void setFiledType(String filedType) {
		this.filedType = filedType;
	}

	public String getParNm() {
		return parNm;
	}

	public void setParNm(String parNm) {
		this.parNm = parNm;
	}

	public int getActivityId() {
		return activityId;
	}

	public void setActivityId(int activityId) {
		this.activityId = activityId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public char getManualFlag() {
		return manualFlag;
	}

	public void setManualFlag(char manualFlag) {
		this.manualFlag = manualFlag;
	}
	
}
