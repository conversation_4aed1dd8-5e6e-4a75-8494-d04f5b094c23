package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@Table(name = "tba_inquiry_metadata_master ", schema = "tba")
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
public class TbaInquiryMetaDataMaster extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(name = "client_id")
	private int clientId;

	@Column(name = "panel_id")
	private int panelId;

	@Column(name = "metadata_type")
	private String metadataType;

	@Column(name = "metadata_tag")
	private String metadataTag;

	@Column(name = "attribute_id")
	private String attributeId;

	@Column(name = "attribute_code")
	private String attributeCode;

	@Column(name = "attribute_text")
	private String attributeText;

	@Column(name = "created_by")
	private String createdBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@Column(name = "trust_code")
	private String trustCode;
}
