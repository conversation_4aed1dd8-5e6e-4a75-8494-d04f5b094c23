package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.sql.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "EVENT_HISTORY_MASTER", schema = "tba")
public class EventHistoryMaster extends BaseModel implements Serializable {
	@Column(name = "event_name",nullable = false)
	private String eventName;
	
	@Column(name = "practice_area_code",nullable = false)
	private String practiceAreaCode;
	
	@Column(name = "activity_id",nullable = false)
	private int activityId;
	
	@Column(name = "client_id",nullable = false)
	private int clientId;
	
	@Column(name = "act_long_desc",nullable = false)
	private String actLongDesc;
	
	@Column(name="created_date", nullable = true)
	private Date createdDate;
	
	@Column(name="created_by", nullable = true)
	private String createdBy;
	
	@Column(name="updated_by", nullable = true)
	private String updatedBy;
	
	@Column(name="updated_date", nullable = true)
	private Date updatedDate;

	public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}

	public String getPracticeAreaCode() {
		return practiceAreaCode;
	}

	public void setPracticeAreaCode(String practiceAreaCode) {
		this.practiceAreaCode = practiceAreaCode;
	}

	public int getActivityId() {
		return activityId;
	}

	public void setActivityId(int activityId) {
		this.activityId = activityId;
	}

	public int getClientId() {
		return clientId;
	}

	public void setClientId(int clientId) {
		this.clientId = clientId;
	}

	public String getActLongDesc() {
		return actLongDesc;
	}

	public void setActLongDesc(String actLongDesc) {
		this.actLongDesc = actLongDesc;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	
	
}
