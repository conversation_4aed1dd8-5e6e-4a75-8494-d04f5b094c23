package com.wipro.fipc.entity.tba;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "tba_payment_delivery_type_master", schema = "tba")
public class TbaPaymentDeliveryType extends BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

	@Column(name = "delivery_type_id")
	private int deliveryTypeId;

	@Column(name = "delivery_type_text")
	private String deliveryTypeText;

	@Column(name = "active_flag")
	private char activeFlag;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "pmt_id")
	private TbaPaymentDescriptor paymentDescriptor;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	public int getDeliveryTypeId() {
		return deliveryTypeId;
	}

	public void setDeliveryTypeId(int deliveryTypeId) {
		this.deliveryTypeId = deliveryTypeId;
	}

	public String getDeliveryTypeText() {
		return deliveryTypeText;
	}

	public void setDeliveryTypeText(String deliveryTypeText) {
		this.deliveryTypeText = deliveryTypeText;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public TbaPaymentDescriptor getPaymentDescriptor() {
		return paymentDescriptor;
	}

	public void setPaymentDescriptor(TbaPaymentDescriptor paymentDescriptor) {
		this.paymentDescriptor = paymentDescriptor;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}