package com.wipro.fipc.entity.tba;

import java.util.Date;

import lombok.Data;

@Data
public class EventHistoryUI {
	
	private Long id;
	private String actLongDesc;
	private String activeFlag;
	private Integer activityId;
	private EventHistoryEffectiveDate fromDate;
	private EventHistoryEffectiveDate toDate;
	private String eventHistDefName;
	private String eventName;
	private String fieldType;
	private String jsonKey;
	private String parNm;
	private Long processJobMappingId;
	private String tbaFieldName;
	private String createdBy;
	private Date createdDate;
	private String updatedBy;
	private Date updatedDate;
	private String manualFlag;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getActLongDesc() {
		return actLongDesc;
	}
	public void setActLongDesc(String actLongDesc) {
		this.actLongDesc = actLongDesc;
	}
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public Integer getActivityId() {
		return activityId;
	}
	public void setActivityId(Integer activityId) {
		this.activityId = activityId;
	}
	public EventHistoryEffectiveDate getFromDate() {
		return fromDate;
	}
	public void setFromDate(EventHistoryEffectiveDate fromDate) {
		this.fromDate = fromDate;
	}
	public EventHistoryEffectiveDate getToDate() {
		return toDate;
	}
	public void setToDate(EventHistoryEffectiveDate toDate) {
		this.toDate = toDate;
	}
	public String getEventHistDefName() {
		return eventHistDefName;
	}
	public void setEventHistDefName(String eventHistDefName) {
		this.eventHistDefName = eventHistDefName;
	}
	public String getEventName() {
		return eventName;
	}
	public void setEventName(String eventName) {
		this.eventName = eventName;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public String getJsonKey() {
		return jsonKey;
	}
	public void setJsonKey(String jsonKey) {
		this.jsonKey = jsonKey;
	}
	public String getParNm() {
		return parNm;
	}
	public void setParNm(String parNm) {
		this.parNm = parNm;
	}
	public Long getProcessJobMappingId() {
		return processJobMappingId;
	}
	public void setProcessJobMappingId(Long processJobMappingId) {
		this.processJobMappingId = processJobMappingId;
	}
	public String getTbaFieldName() {
		return tbaFieldName;
	}
	public void setTbaFieldName(String tbaFieldName) {
		this.tbaFieldName = tbaFieldName;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public String getManualFlag() {
		return manualFlag;
	}
	public void setManualFlag(String manualFlag) {
		this.manualFlag = manualFlag;
	}
	
}
