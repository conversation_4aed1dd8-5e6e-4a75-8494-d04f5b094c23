package com.wipro.fipc.entity.tba;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "EVENT_INQUIRY_CONFIG", schema = "tba")
@Where(clause = "active_flag='T'")
public class EventInquiryConfig extends BaseModel
{
	private static final long serialVersionUID = 1L;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMapping;

	@Column(name = "event_name")
	private String eventName;

	@Column(name = "event_inquiry_def_name")
	private String eventInquiryDefName;

	@Column(name = "panel_id")
	private int panelId;

	@Column(name = "tba_field_name")
	private String tbaFieldName;

	@Column(name = "json_key")
	private String jsonKey;

	@Column(name = "metadata")
	private String metadata;

	@Column(name = "base_key")
	private String baseKey;

	@Column(name = "sub_key")
	private String subKey;

	@Column(name = "trans_id")
	private String transId;

	@Column(name = "par_nm")
	private String parNm;

	@Column(name = "eff_date_type")
	private String effDateType;

	@Column(name = "eff_from_date")
	private String effFromDate;

	@Column(name = "eff_to_date")
	private String effToDate;

	@Column(name = "effective_date")
	private String effectiveDate;

	@Column(name = "active_flag")
	private String activeFlag;

	@Column(name = "record_identifier")
	private String recordIdentifier;

	@Column(name = "sequence")
	private String sequence;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "updated_by")
	private String updatedBy;
	
	@Column(name = "field_type")
	private String fieldType;
	
	
	@Column(name="pending_event")
	private boolean pendingEvent;

}