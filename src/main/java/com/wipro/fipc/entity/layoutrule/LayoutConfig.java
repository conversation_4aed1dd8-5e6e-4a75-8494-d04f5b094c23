package com.wipro.fipc.entity.layoutrule;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Entity
@Data
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "LAYOUT_CONFIG", schema = "layout_rule")
@Where(clause = "active_flag='T'")
public class LayoutConfig extends BaseModel {
	private static final long serialVersionUID = 1L;

	@Column(name = "file_name")
	private String fileName;

	@Column(name = "record_type")
	private String recordType;

	@Column(name = "mf_field_name")
	private String mfFieldName;

	@Column(name = "start_pos")
	private String startPos;

	@Column(name = "RECORD_IDENTIFIER_VAL")
	private String recordIdentifierVal;

	@Column(name = "length")
	private int length;

	@Column(name = "active_flag")
	private char activeFlag;

	@Column(name = "record_format")
	private String recordFormat;

	@Column(name = "field_type")
	private String fieldType;

	@Column(name = "field_template")
	private String fieldTemplate;

	@Column(name = "mandatory")
	private String mandatory;

	@Column(name = "record_identifier")
	private String recordIdentifier;

	@Column(name = "PPT_IDENTIFIER")
	private String pptIdentifier;

	@Column(name = "MF_FIELD_WOUT_SPACE")
	private String mfFieldWoutSpace;

	@Column(name = "date_format")
	private String dateFormat;

	@Column(name = "field_no")
	private int fieldNo;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "file_name_wout_space")
	private String fileNameWoutSpace;

	@Column(name = "sheet_name")
	private String sheetName;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMappingConfig;

	@Column(name = "sheet_name_wout_space")
	private String sheetNameWoutSpace;

	@Column(name = "value_details")
	private String valueDetails;

	@Column(name = "labelling_json ")
	private String labellingJson;

	@Column(name = "pre_filter")
	private String preFilter;

	@Column(name = "pre_filter_operator")
	private String preFilterOperator;
	
	@Column(name = "amount_format")
	private String amountFormat;
	
//	@Column(name = "pre_filter_date_type")
//	private String preFilterDateType;
//	
//	@Column(name = "pre_filter_date_number")
//	private String preFilterDateNumber;
//	
//	@Column(name = "pre_filter_date_frequency")
//	private String preFilterDateFrequency;

}
