package com.wipro.fipc.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonProperty;

@Entity
@Table(name = "process", schema = "common")
public class Process extends BaseModel implements Serializable {
	@Column(name = "process_name")
	@JsonProperty("processName")
	private String processName;

	@Column(name = "process_type")
	@JsonProperty("processType")
	private String processType;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	@JsonProperty("createdDate")
	private Date createdDate;

	@Column(name = "created_by")
	@JsonProperty("createdBy")
	private String createdBy;

	@Column(name = "updated_by")
	@JsonProperty("updatedBy")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	@JsonProperty("updatedDate")
	private Date updatedDate;

	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public String getProcessType() {
		return processType;
	}

	public void setProcessType(String processType) {
		this.processType = processType;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

}
