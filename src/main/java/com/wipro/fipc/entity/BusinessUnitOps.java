package com.wipro.fipc.entity;
import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonProperty;

//import lombok.Data;
import lombok.ToString;


@ToString(exclude = {"businessUnit", "businessOps"}) 
@Entity
@Table(name = "business_unit_ops", schema = "common")
public class BusinessUnitOps extends BaseModel implements Serializable
{
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	@JsonProperty("createdDate")
	private Date createdDate;

	@Column(name = "created_by")
	@JsonProperty("createdBy")
	private String createdBy;

	@Column(name = "updated_by")
	@JsonProperty("updatedBy")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	@JsonProperty("updatedDate")
	private Date updatedDate;
	
	@ManyToOne
    @JoinColumn(name="bu_ops_id")
	@JsonBackReference(value="businessopsRef")	//changed here
	@JsonProperty("businessOps")
    private BusinessOps businessOps;
	
	@ManyToOne
    @JoinColumn(name="bu_id")
	@JsonBackReference(value="businessunitRef")	//changed here
	@JsonProperty("businessUnit")
    private BusinessUnit businessUnit;

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public BusinessOps getBusinessOps() {
		return businessOps;
	}

	public void setBusinessOps(BusinessOps businessOps) {
		this.businessOps = businessOps;
	}

	public BusinessUnit getBusinessUnit() {
		return businessUnit;
	}

	public void setBusinessUnit(BusinessUnit businessUnit) {
		this.businessUnit = businessUnit;
	}
	

	
}
