package com.wipro.fipc.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wipro.fipc.common.utils.converter.ClientIdToClientCode;

import lombok.Data;

@Data
@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "PENDING_EVENT_MASTER", schema = "tba")
public class TbaPendingEventMaster extends BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

	@Column(name = "client_id")
	@JsonSerialize(using = ClientIdToClientCode.class)
	private int clientId;

	@Column(name = "event_name")
	private String eventName;

	@Column(name = "activity_id")
	private String activityId;

	@Column(name = "ACT_LONG_DESC")
	private String actLongDesc;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

}
