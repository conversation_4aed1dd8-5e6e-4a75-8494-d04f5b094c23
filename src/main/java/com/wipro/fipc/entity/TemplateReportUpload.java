package com.wipro.fipc.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import lombok.Data;

@Data
@Entity
@Table(name = "TEMPLATE_REPORT_UPLOAD", schema = "common")

public class TemplateReportUpload extends BaseModel implements Serializable {

	private static final long serialVersionUID = 4171213959528972220L;
	@Column(name = "type")
	private String type;

	@Column(name = "bu_id")
	private int buId;

	@Column(name = "client_id")
	private String clientId;

	@Column(name = "client_name")
	private String clientName;

	@Column(name = "template_report_name")
	private String templateReportName;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "uploaded_date")
	private Date uploadedDate;

	@Column(name = "uploaded_by")
	private String uploadedBy;

	@Column(name = "report")
	@Lob
	private byte[] report;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Column(name = "active_flag")
	private char activeFlag;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "template_report_name_ws")
	private String templateReportNameWs;

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "templateReportUpload") // ,orphanRemoval =
																										// true
	// @LazyCollection(LazyCollectionOption.FALSE)
	@JsonManagedReference(value = "templateReportUploadRef")
	private List<TemplateReportLayOut> templateReportLayOut = new ArrayList<TemplateReportLayOut>();

	@Column(name = "report_flag")
	private Character reportFlag;

	public TemplateReportUpload() {
	}

	public TemplateReportUpload(String type, int buId, String clientId, String clientName, String templateReportName,
			Date uploadedDate, String uploadedBy, byte[] report, Date createdDate, String createdBy, String updatedBy,
			char activeFlag, Date updatedDate, String templateReportNameWs,
			List<TemplateReportLayOut> templateReportLayOut, Character reportFlag) {
		super();
		this.type = type;
		this.buId = buId;
		this.clientId = clientId;
		this.clientName = clientName;
		this.templateReportName = templateReportName;
		this.uploadedDate = uploadedDate;
		this.uploadedBy = uploadedBy;
		this.report = report;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.activeFlag = activeFlag;
		this.updatedDate = updatedDate;
		this.templateReportNameWs = templateReportNameWs;
		this.templateReportLayOut = templateReportLayOut;
		this.reportFlag = reportFlag;
	}

	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param type the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * @return the buId
	 */
	public int getBuId() {
		return buId;
	}

	/**
	 * @param buId the buId to set
	 */
	public void setBuId(int buId) {
		this.buId = buId;
	}

	/**
	 * @return the clientId
	 */
	public String getClientId() {
		return clientId;
	}

	/**
	 * @param clientId the clientId to set
	 */
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	/**
	 * @return the clientName
	 */
	public String getClientName() {
		return clientName;
	}

	/**
	 * @param clientName the clientName to set
	 */
	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	/**
	 * @return the templateReportName
	 */
	public String getTemplateReportName() {
		return templateReportName;
	}

	/**
	 * @param templateReportName the templateReportName to set
	 */
	public void setTemplateReportName(String templateReportName) {
		this.templateReportName = templateReportName;
	}

	/**
	 * @return the uploadedDate
	 */
	public Date getUploadedDate() {
		return uploadedDate;
	}

	/**
	 * @param uploadedDate the uploadedDate to set
	 */
	public void setUploadedDate(Date uploadedDate) {
		this.uploadedDate = uploadedDate;
	}

	/**
	 * @return the uploadedBy
	 */
	public String getUploadedBy() {
		return uploadedBy;
	}

	/**
	 * @param uploadedBy the uploadedBy to set
	 */
	public void setUploadedBy(String uploadedBy) {
		this.uploadedBy = uploadedBy;
	}

	/**
	 * @return the report
	 */
	public byte[] getReport() {
		return report;
	}

	/**
	 * @param report the report to set
	 */
	public void setReport(byte[] report) {
		this.report = report;
	}

	/**
	 * @return the createdDate
	 */
	public Date getCreatedDate() {
		return createdDate;
	}

	/**
	 * @param createdDate the createdDate to set
	 */
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	/**
	 * @return the createdBy
	 */
	public String getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public String getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the activeFlag
	 */
	public char getActiveFlag() {
		return activeFlag;
	}

	/**
	 * @param activeFlag the activeFlag to set
	 */
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	/**
	 * @return the updatedDate
	 */
	public Date getUpdatedDate() {
		return updatedDate;
	}

	/**
	 * @param updatedDate the updatedDate to set
	 */
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	/**
	 * @return the templateReportNameWs
	 */
	public String getTemplateReportNameWs() {
		return templateReportNameWs;
	}

	/**
	 * @param templateReportNameWs the templateReportNameWs to set
	 */
	public void setTemplateReportNameWs(String templateReportNameWs) {
		this.templateReportNameWs = templateReportNameWs;
	}

	/**
	 * @return the templateReportLayOut
	 */
	public List<TemplateReportLayOut> getTemplateReportLayOut() {
		return templateReportLayOut;
	}

	/**
	 * @param templateReportLayOut the templateReportLayOut to set
	 */
	public void setTemplateReportLayOut(List<TemplateReportLayOut> templateReportLayOut) {
		this.templateReportLayOut = templateReportLayOut;
	}

	/**
	 * @return the reportFlag
	 */
	public Character getReportFlag() {
		return reportFlag;
	}

	/**
	 * @param reportFlag the reportFlag to set
	 */
	public void setReportFlag(Character reportFlag) {
		this.reportFlag = reportFlag;
	}

	/**
	 * @return the serialversionuid
	 */
	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
