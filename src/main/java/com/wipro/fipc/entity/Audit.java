package com.wipro.fipc.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@Entity
@Table(name = "audit", schema = "common")
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
public class Audit extends BaseModel {

	private static final long serialVersionUID = 1L;

	@Column(name = "process_job_mapping_id")
	private long processJobMappingId;

	@Column(name = "create_timestamp", nullable = false)
	private long createTimestamp;

}
