package com.wipro.fipc.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@Entity
@Table(name = "PROCESS_JOB_MAPPING", schema = "common")
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })

@Where(clause = "active_flag='T'")
public class ProcessJobMapping extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(name = "job_name")
	@JsonProperty("jobName")
	private String jobName;

	@Column(name = "active_flag")
	@JsonProperty("activeFlag")
	private char activeFlag;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	@JsonProperty("createdDate")
	private Date createdDate;

	@Column(name = "eft_subject")
	@JsonProperty("eftSubject")
	private String eftSubject;

	@Column(name = "created_by")
	@JsonProperty("createdBy")
	private String createdBy;

	@Column(name = "updated_by")
	@JsonProperty("updatedBy")
	private String updatedBy;

	@Column(name = "ksd_name")
	@JsonProperty("ksdName")
	private String ksdName;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	@JsonProperty("updatedDate")
	private Date updatedDate;

	@ManyToOne
	@JoinColumn(name = "business_unit_ops_id")
	@JsonProperty("businessUnitOps")
	private BusinessUnitOps businessUnitOps;

	@ManyToOne
	@JoinColumn(name = "client_id")
	@JsonProperty("clientDetails")
	private ClientDetails clientDetails;

	@ManyToOne
	@JoinColumn(name = "process_id")
	@JsonProperty("process")
	private Process process;

	@Column(name = "tower")
	@JsonProperty("tower")
	private String tower;

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getEftSubject() {
		return eftSubject;
	}

	public void setEftSubject(String eftSubject) {
		this.eftSubject = eftSubject;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getKsdName() {
		return ksdName;
	}

	public void setKsdName(String ksdName) {
		this.ksdName = ksdName;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getTower() {
		return tower;
	}

	public void setTower(String tower) {
		this.tower = tower;
	}

	public BusinessUnitOps getBusinessUnitOps() {
		return businessUnitOps;
	}

	public void setBusinessUnitOps(BusinessUnitOps businessUnitOps) {
		this.businessUnitOps = businessUnitOps;
	}

	public ClientDetails getClientDetails() {
		return clientDetails;
	}

	public void setClientDetails(ClientDetails clientDetails) {
		this.clientDetails = clientDetails;
	}

	public Process getProcess() {
		return process;
	}

	public void setProcess(Process process) {
		this.process = process;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}