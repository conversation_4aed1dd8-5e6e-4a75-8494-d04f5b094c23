package com.wipro.fipc.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import lombok.Data;
//

@Data
@Entity
@Table(name = "tba_cutoff_date", schema = "tba")
@Where(clause = "active_flag='T'")
public class TbaCutoffDate implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue
	@Column(name = "id")
	private Long id;

	@Column(name = "client_code")
	private String clientCode;

	@Column(name = "start_date")
	private String startDate;

	@Column(name = "month")
	private String month;

	@Column(name = "tba_cutoff_date")
	private String tbaCutffDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "active_flag")
	private char activeFlag;

	public String getClientCode() {
		return clientCode;
	}

	public String getStartDate() {
		return startDate;
	}

	public String getTbaCutffDate() {
		return tbaCutffDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public void setTbaCutffDate(String tbaCutffDate) {
		this.tbaCutffDate = tbaCutffDate;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@Override
	public String toString() {
		return "TbaCutoffDate [id=" + id + ", clientCode=" + clientCode + ", startDate=" + startDate + ", month="
				+ month + ", tbaCutffDate=" + tbaCutffDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy
				+ ", updatedDate=" + updatedDate + ", createdDate=" + createdDate + ", activeFlag=" + activeFlag + "]";
	}

}
