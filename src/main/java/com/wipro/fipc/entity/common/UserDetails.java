package com.wipro.fipc.entity.common;

import java.time.LocalDateTime;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@DynamicUpdate
@Table(name = "user_details", schema = "common")
public class UserDetails extends BaseModel {

	private static final long serialVersionUID = 1L;

	@Column(name = "password")
	private String password;

	@Column(name = "adid", nullable = false)
	private String adid;

	@Column(name = "type")
	private String type;

	@Column(name = "role")
	private String role;

	@Column(name = "manager_adid")
	private String managerAdId;

	@Column(name = "active_flag")
	private char activeFlag;

	@Temporal(TemporalType.TIMESTAMP)
	@UpdateTimestamp
	@Column(name = "updated_date")
	private Date updatedDate;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "racfid")
	private String racfId;

	@Column(name = "session_status")
	private String sessionStatus;

	@Column(name = "session_date")
	private LocalDateTime sessionDate;

	@Column(name = "user_name")
	private String userName;

}
