package com.wipro.fipc.entity.common;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.filelayout.RulesConfig;
import com.wipro.fipc.entity.filelayout.ValidationType;

@Entity
@Table(name = "RULES_DEFINITION", schema = "layout_rule")
@Where(clause="active_flag='T'")
public class RulesDefinition extends BaseModel {
	
	@Column(name = "primary_field_name")
	private String primaryFieldName;
	
	@Column(name = "json")
	private String json;
	
	@Column(name = "json_wout_space")
	private String jsonWoutName;
	
	@Column(name = "file_name")
	private String fileName;
	
	@Column(name = "rule_name")
	private String ruleName;
	 
	@Column(name = "conversion_type")
	private String conversionType;
	@Column(name = "active_flag")
	private char activeFlag;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@Column(name = "PRIMARY_FIELD_WOUT_SPACE")
	private String primaryFieldWoutSpace;
	
	@ManyToOne (cascade=CascadeType.ALL,fetch = FetchType.LAZY)
    @JoinColumn(name="rule_id")
	@JsonBackReference(value="rulesConfigRef")	
    private RulesConfig rulesConfig;
	
	@ManyToOne
    @JoinColumn(name="val_type_id")
	private ValidationType validationType;
	

	
	@Column(name = "var_operation_json")
	private String varOperationJson;

	@Column(name = "var_operation_json_wout_space")
	private String varOperationJsonWoutSpace;

	@Column(name = "var_result_json")
	private String varResultJson;

	@Column(name = "var_result_json_wout_space")
	private String varResultJsonWoutSpace;
	
	@Column(name = "conditions_json")
	private byte[] conditionJson;

	public String getPrimaryFieldName() {
		return primaryFieldName;
	}

	public void setPrimaryFieldName(String primaryFieldName) {
		this.primaryFieldName = primaryFieldName;
	}

	public String getJson() {
		return json;
	}

	public void setJson(String json) {
		this.json = json;
	}

	public String getJsonWoutName() {
		return jsonWoutName;
	}

	public void setJsonWoutName(String jsonWoutName) {
		this.jsonWoutName = jsonWoutName;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getConversionType() {
		return conversionType;
	}

	public void setConversionType(String conversionType) {
		this.conversionType = conversionType;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getPrimaryFieldWoutSpace() {
		return primaryFieldWoutSpace;
	}

	public void setPrimaryFieldWoutSpace(String primaryFieldWoutSpace) {
		this.primaryFieldWoutSpace = primaryFieldWoutSpace;
	}

	public RulesConfig getRulesConfig() {
		return rulesConfig;
	}

	public void setRulesConfig(RulesConfig rulesConfig) {
		this.rulesConfig = rulesConfig;
	}

	public ValidationType getValidationType() {
		return validationType;
	}

	public void setValidationType(ValidationType validationType) {
		this.validationType = validationType;
	}

	public String getVarOperationJson() {
		return varOperationJson;
	}

	public void setVarOperationJson(String varOperationJson) {
		this.varOperationJson = varOperationJson;
	}

	public String getVarOperationJsonWoutSpace() {
		return varOperationJsonWoutSpace;
	}

	public void setVarOperationJsonWoutSpace(String varOperationJsonWoutSpace) {
		this.varOperationJsonWoutSpace = varOperationJsonWoutSpace;
	}

	public String getVarResultJson() {
		return varResultJson;
	}

	public void setVarResultJson(String varResultJson) {
		this.varResultJson = varResultJson;
	}

	public String getVarResultJsonWoutSpace() {
		return varResultJsonWoutSpace;
	}

	public void setVarResultJsonWoutSpace(String varResultJsonWoutSpace) {
		this.varResultJsonWoutSpace = varResultJsonWoutSpace;
	}

	public byte[] getConditionJson() {
		return conditionJson;
	}

	public void setConditionJson(byte[] conditionJson) {
		this.conditionJson = conditionJson;
	}
	
	

}
