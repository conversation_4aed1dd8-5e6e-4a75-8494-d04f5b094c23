package com.wipro.fipc.entity.common;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;

import lombok.Data;

@Data
@Entity
@Table(name = "Reportee_type", schema = "common")
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Where(clause = "active_flag='T'")
public class ReporteeType extends BaseModel implements Serializable {

	@Column(name = "manager_id", nullable = false)
	private String managerId;

	@Column(name = "reportee_id")
	private String reporteeId;

	@Column(name = "reportee_type")
	private String reporteeType;

	@Column(name = "active_flag")
	private char activeFlag;
}
