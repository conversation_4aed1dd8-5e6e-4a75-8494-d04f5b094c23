package com.wipro.fipc.entity.common;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@Table(name = "NOTIFICATION_REPORT_CONFIG",schema = "common")
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Where(clause="active_flag='T'")
public class NotificationReportConfig extends BaseModel  implements Serializable {
	
	@Column(name = "application")
    private String application;
	
	@Column(name = "file")
    private String file;
	
	@Column(name = "file_type")
    private String fileType;
	@Column(name = "active_flag")
	private char activeFlag;
	
	@Column(name = "report_name")
    private String reportName;
	
	@Column(name = "subject")
    private String subject;
	
	@Column(name="APPEND_SUBJECT")
	private String appendSubject;
	
	
	@Column(name = "subfolder")
    private String subfolder;
	
	@Column(name = "path")
    private String path;
	
	@Column(name = "remarks")
    private String remarks;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="process_job_mapping_id")
//	@JsonBackReference(value="processjobmappingRef")	
    private ProcessJobMapping processJobMapping;
	
/*	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="phase_id", nullable=false)
//	@JsonBackReference(value="phaseRef")	
	@LazyToOne(LazyToOneOption.PROXY)
    private Phase phase;*/
	
	@Column(name = "action")
	private String action;
	
	@Column(name = "subject_email_password")
	private String subjectEmailPassword;
	
	@Column(name = "password")
	private String password;
	
	@Column(name = "item_column_name")
    private String itemColName;

	public String getApplication() {
		return application;
	}

	public void setApplication(String application) {
		this.application = application;
	}

	public String getFile() {
		return file;
	}

	public void setFile(String file) {
		this.file = file;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getReportName() {
		return reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getAppendSubject() {
		return appendSubject;
	}

	public void setAppendSubject(String appendSubject) {
		this.appendSubject = appendSubject;
	}

	public String getSubfolder() {
		return subfolder;
	}

	public void setSubfolder(String subfolder) {
		this.subfolder = subfolder;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}

	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getSubjectEmailPassword() {
		return subjectEmailPassword;
	}

	public void setSubjectEmailPassword(String subjectEmailPassword) {
		this.subjectEmailPassword = subjectEmailPassword;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getItemColName() {
		return itemColName;
	}

	public void setItemColName(String itemColName) {
		this.itemColName = itemColName;
	}

	@Override
	public int hashCode() {
		return Objects.hash(action, activeFlag, appendSubject, application, createdBy, createdDate, file, fileType,
				itemColName, password, path, processJobMapping, remarks, reportName, subfolder, subject,
				subjectEmailPassword, updatedBy, updatedDate);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		NotificationReportConfig other = (NotificationReportConfig) obj;
		return Objects.equals(action, other.action) && activeFlag == other.activeFlag
				&& Objects.equals(appendSubject, other.appendSubject) && Objects.equals(application, other.application)
				&& Objects.equals(createdBy, other.createdBy) && Objects.equals(createdDate, other.createdDate)
				&& Objects.equals(file, other.file) && Objects.equals(fileType, other.fileType)
				&& Objects.equals(itemColName, other.itemColName) && Objects.equals(password, other.password)
				&& Objects.equals(path, other.path) && Objects.equals(processJobMapping, other.processJobMapping)
				&& Objects.equals(remarks, other.remarks) && Objects.equals(reportName, other.reportName)
				&& Objects.equals(subfolder, other.subfolder) && Objects.equals(subject, other.subject)
				&& Objects.equals(subjectEmailPassword, other.subjectEmailPassword)
				&& Objects.equals(updatedBy, other.updatedBy) && Objects.equals(updatedDate, other.updatedDate);
	}

	@Override
	public String toString() {
		return "NotificationReportConfig [application=" + application + ", file=" + file + ", fileType=" + fileType
				+ ", activeFlag=" + activeFlag + ", reportName=" + reportName + ", subject=" + subject
				+ ", appendSubject=" + appendSubject + ", subfolder=" + subfolder + ", path=" + path + ", remarks="
				+ remarks + ", createdDate=" + createdDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy
				+ ", updatedDate=" + updatedDate + ", processJobMapping=" + processJobMapping + ", action=" + action
				+ ", subjectEmailPassword=" + subjectEmailPassword + ", password=" + password + ", itemColName="
				+ itemColName + "]";
	}
	
	
}


