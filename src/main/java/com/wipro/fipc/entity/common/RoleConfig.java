package com.wipro.fipc.entity.common;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.BusinessOps;
import com.wipro.fipc.entity.BusinessUnit;
import com.wipro.fipc.entity.ClientDetails;
import com.wipro.fipc.entity.Process;
import lombok.Data;

@Data
@Entity
@Table(name = "role_config", schema = "common")
public class RoleConfig extends BaseModel implements Serializable {

	@Column(name = "adid")
	private String adid;

	@Column(name = "type")
	private String type;

	@Column(name = "role")
	private String role;

	@Column(name = "active")
	private Boolean active;

	@Column(name = "owned_by")
	private String ownedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@ManyToOne
	@JoinColumn(name = "business_unit_id", nullable = false)

	private BusinessUnit businessUnit;

	@ManyToOne
	@JoinColumn(name = "process_id", nullable = false)
	private Process process;

	@ManyToOne
	@JoinColumn(name = "client_id", nullable = false)
	private ClientDetails clientDetails;

	@ManyToOne
	@JoinColumn(name = "business_ops_id", nullable = false)
	private BusinessOps businessOps;
}
