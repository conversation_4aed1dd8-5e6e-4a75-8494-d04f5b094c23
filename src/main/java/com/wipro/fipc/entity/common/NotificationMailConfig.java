package com.wipro.fipc.entity.common;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
@Entity
@Table(name = "NOTIFICATION_MAIL_CONFIG",schema = "common")
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Where(clause="active_flag='T'")
public class NotificationMailConfig extends BaseModel  implements Serializable {

	
	@Column(name = "condition")
    private String condition;
	
	@Column(name = "to_list")
    private String toList;
	
	@Column(name = "cc_List")
    private String ccList;
	
	@Column(name = "subject")
    private String subject;
	
	@Column(name = "active_flag")
	private char activeFlag;
		
	@Column(name="append_subject")
	private String appendSubject;
	
	@Column(name = "interested_fields")
    private String interestedFields;
	
	@Column(name = "attachment_name")
    private String attachmentName;
	
	@Column(name = "remarks")
    private String remarks;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="process_job_mapping_id")
	//@JsonBackReference(value="processjobmappingRef")	
    private ProcessJobMapping processJobMapping;
	
	/*@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="phase_id", nullable=false)
	//@JsonBackReference(value="phaseRef")	
	@LazyToOne(LazyToOneOption.PROXY)
    private Phase phase;*/
	
	@Column(name = "password")
	private String password;
	@Column(name = "mail_body")
    private String mailBody;
	public String getCondition() {
		return condition;
	}
	public void setCondition(String condition) {
		this.condition = condition;
	}
	public String getToList() {
		return toList;
	}
	public void setToList(String toList) {
		this.toList = toList;
	}
	public String getCcList() {
		return ccList;
	}
	public void setCcList(String ccList) {
		this.ccList = ccList;
	}
	public String getSubject() {
		return subject;
	}
	public void setSubject(String subject) {
		this.subject = subject;
	}
	public char getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}
	public String getAppendSubject() {
		return appendSubject;
	}
	public void setAppendSubject(String appendSubject) {
		this.appendSubject = appendSubject;
	}
	public String getInterestedFields() {
		return interestedFields;
	}
	public void setInterestedFields(String interestedFields) {
		this.interestedFields = interestedFields;
	}
	public String getAttachmentName() {
		return attachmentName;
	}
	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public Date getUpdatedDate() {
		return updatedDate;
	}
	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}
	public ProcessJobMapping getProcessJobMapping() {
		return processJobMapping;
	}
	public void setProcessJobMapping(ProcessJobMapping processJobMapping) {
		this.processJobMapping = processJobMapping;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getMailBody() {
		return mailBody;
	}
	public void setMailBody(String mailBody) {
		this.mailBody = mailBody;
	}
	@Override
	public int hashCode() {
		return Objects.hash(activeFlag, appendSubject, attachmentName, ccList, condition, createdBy, createdDate,
				interestedFields, mailBody, password, processJobMapping, remarks, subject, toList, updatedBy,
				updatedDate);
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		NotificationMailConfig other = (NotificationMailConfig) obj;
		return activeFlag == other.activeFlag && Objects.equals(appendSubject, other.appendSubject)
				&& Objects.equals(attachmentName, other.attachmentName) && Objects.equals(ccList, other.ccList)
				&& Objects.equals(condition, other.condition) && Objects.equals(createdBy, other.createdBy)
				&& Objects.equals(createdDate, other.createdDate)
				&& Objects.equals(interestedFields, other.interestedFields) && Objects.equals(mailBody, other.mailBody)
				&& Objects.equals(password, other.password)
				&& Objects.equals(processJobMapping, other.processJobMapping) && Objects.equals(remarks, other.remarks)
				&& Objects.equals(subject, other.subject) && Objects.equals(toList, other.toList)
				&& Objects.equals(updatedBy, other.updatedBy) && Objects.equals(updatedDate, other.updatedDate);
	}
	@Override
	public String toString() {
		return "NotificationMailConfig [condition=" + condition + ", toList=" + toList + ", ccList=" + ccList
				+ ", subject=" + subject + ", activeFlag=" + activeFlag + ", appendSubject=" + appendSubject
				+ ", interestedFields=" + interestedFields + ", attachmentName=" + attachmentName + ", remarks="
				+ remarks + ", createdDate=" + createdDate + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy
				+ ", updatedDate=" + updatedDate + ", processJobMapping=" + processJobMapping + ", password=" + password
				+ ", mailBody=" + mailBody + ", id=" + id + "]";
	}

}
