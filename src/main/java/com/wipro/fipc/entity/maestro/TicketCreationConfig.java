package com.wipro.fipc.entity.maestro;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TICKET_CREATION_CONFIG", schema = "MAESTRO")
@Where(clause = "active_flag='T'")
@Data
public class TicketCreationConfig extends BaseModel implements Serializable {

	@Column(name = "TASK_OWNER")
	private String taskOwner;

	@Column(name = "TICKET_TYPE")
	private String ticketType;

	@Column(name = "TASK_TYPE")
	private String taskType;

	@Column(name = "TITLE")
	private String title;

	@Column(name = "DIVISION")
	private String division;

	@Column(name = "ASSIGNEE")
	private String assignee;

	@Column(name = "STATUS")
	private String status;
	@Column(name = "active_flag")
	private Character activeFlag;
	@Column(name = "RESPONSIBLE_PARTY")
	private String responsibleParty;

	@Column(name = "PRIORITY")
	private String priority;

	@Column(name = "DUE_DAYS")
	private int dueDays;

	@Column(name = "INTERESTED_PARTIES")
	private String interestedParties;

	@Column(name = "ITERATION")
	private String iteration;

	@Column(name = "project_id")
	private String projectId;

	@Column(name = "complexity")
	private String complexity;

	@Column(name = "SERVICE_GROUP")
	private String serviceGroup;

	@Column(name = "BUSINESS_AREA")
	private String businessArea;

	@Column(name = "PERCENT_COMPLETE")
	private String percentComplete;

	@Column(name = "ESTIMATED_WORK_HOURS")
	private double estimatedWorkHours;

	@Column(name = "CONTROL_ACCOUNT")
	private String controlAccount;

	@Column(name = "WORK_PACKAGE")
	private String workPackage;

	@Column(name = "BILLING_NUM_DESC")
	private String billingNumDesc;

	@Column(name = "BILLING_NUMBER")
	private String billingNumber;

	@Column(name = "SDLC_DISCIPLINE")
	private String sdlcDiscipline;

	@Column(name = "new_discussion")
	private String newDiscussion;

	@Column(name = "ATTACHMENT")
	private String attachment;

	@Column(name = "interested_fields")
	private String interestedFields;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	private Date createdDate;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	private Date updatedDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "process_job_mapping_id")
	// @JsonBackReference(value="processjobmappingRef")
	private ProcessJobMapping processJobMapping;

	@Column(name = "unsecured_attachment")
	private String unsecuredAttachment;

}
