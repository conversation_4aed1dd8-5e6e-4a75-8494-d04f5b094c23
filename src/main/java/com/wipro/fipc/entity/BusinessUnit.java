package com.wipro.fipc.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import com.fasterxml.jackson.annotation.JsonProperty;

@Entity
@Table(name = "business_unit", schema = "common")
public class BusinessUnit extends BaseModel implements Serializable {
	@Column(name = "unit_name")
	@JsonProperty("unitName")
	private String unitName;

	@Column(name = "unit_code")
	@JsonProperty("unitCode")
	private String unitCode;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_date")
	@JsonProperty("createdDate")
	private Date createdDate;

	@Column(name = "created_by")
	@JsonProperty("createdBy")
	private String createdBy;

	@Column(name = "updated_by")
	@JsonProperty("updatedBy")
	private String updatedBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_date")
	@JsonProperty("updatedDate")
	private Date updatedDate;

	@OneToMany(cascade = CascadeType.ALL, mappedBy = "businessUnit")
	@LazyCollection(LazyCollectionOption.FALSE)
	@JsonManagedReference(value = "businessUnitRef")
	@JsonProperty("businessUnitClients")
	private List<BusinessUnitClient> businessUnitClients = new ArrayList<BusinessUnitClient>();

	@OneToMany(cascade = CascadeType.ALL, mappedBy = "businessUnit")
	@LazyCollection(LazyCollectionOption.FALSE)
	@JsonManagedReference(value = "businessunitRef")
	@JsonProperty("businessUnitOps")
	private List<BusinessUnitOps> businessUnitOps = new ArrayList<BusinessUnitOps>();

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getUnitCode() {
		return unitCode;
	}

	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public List<BusinessUnitClient> getBusinessUnitClients() {
		return businessUnitClients;
	}

	public void setBusinessUnitClients(List<BusinessUnitClient> businessUnitClients) {
		this.businessUnitClients = businessUnitClients;
	}

	public List<BusinessUnitOps> getBusinessUnitOps() {
		return businessUnitOps;
	}

	public void setBusinessUnitOps(List<BusinessUnitOps> businessUnitOps) {
		this.businessUnitOps = businessUnitOps;
	}

}
