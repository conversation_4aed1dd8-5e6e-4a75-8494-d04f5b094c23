package com.wipro.fipc.entity.jira;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TICKET_CREATION_CONFIG", schema = "JIRA")
@Where(clause = "active_flag='T'")
@Data
public class JiraTicketCreationConfig extends BaseModel implements Serializable {

	@Column(name = "reporter")
	private String reporter;

	@Column(name = "summary")
	private String summary;

	@Column(name = "issue_type")
	private String issueType;

	@Column(name = "priority")
	private String priority;

	@Column(name = "due_date")
	private String dueDate;

	@Column(name = "project")
	private String project;

	@Column(name = "applicable_clients")
	private String applicableClients;

	@Column(name = "domain")
	private String domain;

	@Column(name = "reporting_group")
	private String reportingGroup;

	@Column(name = "description")
	private String description;

	@Column(name = "status")
	private String status;

	@Column(name = "attachment")
	private String attachment;

	@Column(name = "active_flag")
	private String activeFlag;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMapping;
	
	@Column(name = "assignee")
	private String assignee;

	@Column(name = "assignee_email")
	private String assignee_email;
	
	@Column(name = "epic_name")
	private String epicName;
	
	@Column(name = "epic_type")
	private String epicType;
	
	@Column(name = "begin_date")
	private String beginDate;
	
	@Column(name = "jira_watchers")
	private String jiraWatchers;
	
	@Column(name = "process_name")
	private String processName;
	
	@Column(name = "impacted_shared_group")
	private String impactedSharedGroup;

}
