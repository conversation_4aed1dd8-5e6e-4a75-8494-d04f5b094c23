package com.wipro.fipc.entity.jira;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wipro.fipc.entity.BaseModel;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Entity
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Table(name = "TASK_UPDATE_CONFIG", schema = "JIRA")
@Where(clause = "active_flag='T'")
@Data
public class JiraTaskUpdateConfig extends BaseModel implements Serializable {

	@Column(name = "issue_type")
	private String issueType;

	@Column(name = "new_comments")
	private String newComments;

	@Column(name = "interested_parties")
	private String interestedParties;

	@Column(name = "attachment")
	private String attachment;

	@Column(name = "active_flag")
	private String activeFlag;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "process_job_mapping_id")
	private ProcessJobMapping processJobMapping;
	
	@Column(name = "jira_watchers")
	private String jiraWatchers;

}
