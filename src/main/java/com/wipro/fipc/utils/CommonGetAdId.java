package com.wipro.fipc.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;

@Component
public class CommonGetAdId {

	RestTemplate restTemplate = new RestTemplate();
	@Autowired
	Environment env;

	public String getADID(String appName, String sessionToken) {
		String baseUrl = env.getProperty("getSessionAdid");
		LoggerUtil.log(this.getClass(), Level.INFO, "getADID", "get url to get updatedby: " + baseUrl);
		HttpHeaders headers = new HttpHeaders();
		headers.add("appName", appName);
		headers.add("sessionToken", sessionToken);
		HttpEntity request = new HttpEntity(headers);
		ResponseEntity<String> responseEntity;
		String response;
		if (baseUrl != null) {
			responseEntity = restTemplate.exchange(baseUrl, HttpMethod.POST, request, String.class);
			response = responseEntity.getBody();
			LoggerUtil.log(this.getClass(), Level.INFO, "getADID", "get AdId is not null:#### ");
			return response.substring(0, response.lastIndexOf("_"));
		}
		LoggerUtil.log(this.getClass(), Level.INFO, "getADID", "get AdId is empty:#### ");
		return "";
	}

}
