package com.wipro.fipc.utils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.BeansException;
import org.springframework.beans.FatalBeanException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;
import com.wipro.fipc.entity.ClientDetails;
import com.wipro.fipc.dao.ClientDetailsDao;



@Component
public class CustomBeanUtils extends org.springframework.beans.BeanUtils {
	
	@Autowired
	private ClientDetailsDao clientdetailsDao;
	
	public static void copyProperties(Object source, Object target) throws BeansException {
		LoggerUtil.log(CustomBeanUtils.class, Level.INFO, "CustomBeanUtils",
				"Inside customBeanUtil to copy one object into another cobject ");
		Class<?> actualEditable = target.getClass();
		PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
		for (PropertyDescriptor targetPd : targetPds) {
			if (targetPd.getWriteMethod() != null) {
				PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), targetPd.getName());
				if (sourcePd != null && sourcePd.getReadMethod() != null) {
					try {
						Method readMethod = sourcePd.getReadMethod();
						if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
							readMethod.setAccessible(true);
						}
						Object value = readMethod.invoke(source);
						// Here to determine whether the following value is empty, of course, here can
						// also perform some special requirements processing, such as format conversion
						// when binding
						// System.out.println(source.getClass());
						if (value != null) {
							if (checkDatatype(targetPd.getPropertyType(), value)) {

								Method writeMethod = targetPd.getWriteMethod();
								if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
									writeMethod.setAccessible(true);
								}
								writeMethod.invoke(target, value);
							}
						}
					} catch (Throwable ex) {
						throw new FatalBeanException(
								"Could not copy properties from source to target" + ex.getMessage());
					}
				}
			}
		}
	}

	private static Boolean checkDatatype(Class type, Object fac) {
		switch (type.toString().toLowerCase()) {
		case "list.class":
			return fac.toString().length() > 2;
		case "set.class":
			return fac.toString().length() > 2;
		case "char":
			System.out.println(fac.toString().trim().isEmpty());
			return !fac.toString().trim().isEmpty();
		case "integer":
			return (Integer) fac != 0;
		case "int":
			return (Integer) fac != 0;
		case "long":
			return (Long) fac != 0L;
		case "Long":
			return (Long) fac != 0L;
		case "double":
			return (Double) fac != 0.0d;
		case "float":
			return (Float) fac != 0.0f;
		case "short":
			return (Short) fac != 0;
		case "byte":
			return (Byte) fac != 0L;
		case "boolean":
			return (Boolean) fac;
		}
		return true;
	}

	public <T> List<T> setFieldValues(List<T> list, HashMap<String, String> map) {
		LoggerUtil.log(this.getClass(), Level.INFO, "setFieldValues", "Inside setFieldValues method ####");
		list.forEach(e -> {
			map.forEach((k, v) -> {
				PropertyDescriptor pd;
				try {
					pd = new PropertyDescriptor(k, e.getClass());
					pd.getWriteMethod().invoke(e, v);
				} catch (Exception e1) {
					LoggerUtil.log(this.getClass(), Level.ERROR, "setFieldValues",
							"Exception in setFieldValues : " + e1.getMessage());
				}

			});
		});
		return list;

	}
	
	
	public int checkForClientCode(String typeValue) {
	    LoggerUtil.log(this.getClass(), Level.DEBUG, "checkForClientCode", "############### Started checkForClientCode ");
	    
	    int clientValue = -1; 
	    try {
	        if (typeValue.contains("_")) {
	            ClientDetails clientDetails = clientdetailsDao.findByClientCode(typeValue);
	            clientValue = (clientDetails != null) ? clientDetails.getId().intValue() : -1;
	        } else {
	            clientValue = Integer.parseInt(typeValue);
	        }
	    } catch (NumberFormatException e) {
	        LoggerUtil.log(this.getClass(), Level.ERROR, "checkForClientCode", "Invalid input format: " + typeValue);
	    } catch (Exception e) {
	        LoggerUtil.log(this.getClass(), Level.ERROR, "checkForClientCode", "Exception occurred: " + e.getMessage());
	    }
	    
	    return clientValue;
	}
}