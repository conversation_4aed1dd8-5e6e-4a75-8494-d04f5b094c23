package com.wipro.fipc.utils;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.wipro.fipc.model.CustomClientBusinessOps;
import com.wipro.fipc.model.generated.LayoutConfig;
import com.wipro.fipc.model.generated.NotificationMailConfig;
import com.wipro.fipc.model.generated.NotificationReportConfig;
import com.wipro.fipc.model.generated.ProcessControlConfig;
import com.wipro.fipc.model.generated.RulesConfig;
import com.wipro.fipc.model.generated.TaskUpdateConfig;
import com.wipro.fipc.model.generated.TbaInquiryConfig;
import com.wipro.fipc.model.generated.TbaMatchConfig;
import com.wipro.fipc.model.generated.TbaUpdateConfig;
import com.wipro.fipc.model.generated.TicketCreationConfig;

@Configuration
public class ProcessConfigurationUtil {
	
	@Bean
	public CustomClientBusinessOps getClientBusinessOps()
	{
		return new CustomClientBusinessOps();
	}
	
	@Bean
	public TicketCreationConfig getTicketCreationConfig()
	{
		return new TicketCreationConfig();
	}
	
	@Bean
	public TaskUpdateConfig getTaskUpdateConfig()
	{
		return new TaskUpdateConfig();
	}
	
	@Bean
	public LayoutConfig getLayoutConfig()
	{
		return new LayoutConfig();
	}
	
	@Bean
	public TbaInquiryConfig getTbaInquiryConfig()
	{
		return new TbaInquiryConfig();
	}
	
	
	@Bean
	public TbaUpdateConfig getTbaUpdateConfig()
	{
		return new TbaUpdateConfig();
	}
	
	@Bean
	public RulesConfig getRulesConfig()
	{
		return new RulesConfig();
	}
	
	
	@Bean
	public TbaMatchConfig getTbaMatchConfig()
	{
		return new TbaMatchConfig();
	}
	
	
	
	@Bean
	public ProcessControlConfig getProcessControlConfig()
	{
		return new ProcessControlConfig();
	}
	
	
	@Bean
	public NotificationMailConfig getNotificationMailConfig()
	{
		return new NotificationMailConfig();
	}
	
	
	@Bean
	public NotificationReportConfig getNotificationReportConfig()
	{
		return new NotificationReportConfig();
	}
	


}
