package com.wipro.fipc.utils;

import java.util.List;

import com.wipro.fipc.service.UIService;

public class UIHandler {
	
	public String getRequestQueue(List<String> columnValues, UIService uiService) {
		String pjmId = columnValues.get(1);
		String frequency = columnValues.get(2);
		
		return uiService.fetchRequestList(pjmId, frequency);
	}
	public String getBarChart(List<String> columnValues, UIService uiService) {
		
		String inputType = columnValues.get(1);
		int frequency = Integer.parseInt(columnValues.get(2));
		String pjmId = columnValues.get(3);
	
	    return uiService.createBarChart(inputType, frequency, pjmId);
	}
	public String getPieChart(List<String> columnValues, UIService uiService) {
		String pjmId = columnValues.get(1);
		int frequency = Integer.parseInt(columnValues.get(2));

		return uiService.createPie<PERSON>hart(pjmId ,frequency);
	}
}