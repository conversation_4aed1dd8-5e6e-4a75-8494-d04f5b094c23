package com.wipro.fipc.utils;

import java.util.ArrayList;
import java.util.List;

import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

/**
 * <AUTHOR>
 *
 */
public class XMLUtil {


	/**
	 * getChildElement method create Element object.
	 * @param element
	 * @param tagName
	 * @return Element
	 * @throws Exception
	 */
	public static Element getChildElement(Element element, String tagName) throws Exception {

		return getChildElement(element, tagName, true);

	}

	/**
	 * getChildElement method is create Element object.
	 * @param element
	 * @param tagName
	 * @param create
	 * @return Element
	 * @throws Exception
	 */
	private static Element getChildElement(Element element, String tagName, boolean create) throws Exception {
		Node node = null;
		NodeList nodeList = element.getChildNodes();
		Element childElm = null;
		if(nodeList != null) {
			for (int i = 0; i < nodeList.getLength(); i++) {
				node = nodeList.item(i);
				if (node.getNodeType() == Node.ELEMENT_NODE && tagName.equals(node.getNodeName())) {
					childElm = (Element) node;
					break;
				}
			}
		}

		if ((childElm == null) && (create)) {
			childElm = createChildElement(element, tagName);
		}

		return childElm;
	}
	
	/**
	 * createChildElement method create Element object. 
	 * @param parentElement
	 * @param childName
	 * @return Element
	 */
	private static Element createChildElement(Element parentElement, String childName) {
		Element child = null;
		if (parentElement != null && (!"".equals(childName))) {
			child = parentElement.getOwnerDocument().createElement(childName);
			parentElement.appendChild(child);
		}
		return child;
	}

	/**
	 * getChildElements method create List of element object.
	 * @param element
	 * @param tagName
	 * @return List<Element>
	 * @throws Exception
	 */
	public static List<Element> getChildElements(Element element, String tagName) throws Exception {
		Node node = null;
		List<Element> elementList = new ArrayList<Element>();
		NodeList nodeList = element.getChildNodes();
		Element childElm = null;
		if(nodeList!= null) {
			for (int i = 0; i < nodeList.getLength(); i++) {
				node = nodeList.item(i);
				if (node.getNodeType() == Node.ELEMENT_NODE && tagName.equals(node.getNodeName())) {
					childElm = (Element) node;
					elementList.add(childElm);
				}
			}
		}

		return elementList;
	}

	/**
	 * getFirstLevelTextContent method find text of node.
	 * @param node
	 * @return String
	 */
	public static String getFirstLevelTextContent(Node node) {
		if(node != null ) {
			NodeList list = node.getChildNodes();
			StringBuilder textContent = new StringBuilder();
			textContent.append("");
			if(list != null && list.getLength() > 0) {
				for (int i = 0; i < list.getLength(); ++i) {
					Node child = list.item(i);
					if (child.getNodeType() == Node.TEXT_NODE)
						textContent.append(child.getTextContent());
				}
			}
			return textContent.toString().trim();
		}
		return "";
	}
}
