package com.wipro.fipc.utils;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;

public class CustomClientIdDeserializer extends JsonDeserializer<Object>
{
	@Autowired
	CustomBeanUtils customBeanUtils;
	
	@Override
    public Integer deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
			throws IOException {
		if (jsonParser.getCurrentToken().isNumeric()) {
			return jsonParser.getValueAsInt();
		} else {
			String clientIdString = jsonParser.getValueAsString();
			try {
				return customBeanUtils.checkForClientCode(clientIdString);
			} catch (NumberFormatException e) {
				 LoggerUtil.log(this.getClass(), Level.ERROR, "deserialize", "Invalid input format: " + clientIdString);
				 return 0;
			}
		}
	}
}
