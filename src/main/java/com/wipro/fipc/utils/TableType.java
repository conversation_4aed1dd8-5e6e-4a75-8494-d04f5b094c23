package com.wipro.fipc.utils;

import org.springframework.util.StringUtils;

public enum TableType {
	CDD("CDD"), PYMNT("PYMNT"), DEFAULT("DEFAULT"), PYMNT_DED("PYMNT_DED"), PYMNT_PLAN_ATTR("PYMNT_PLAN_ATTR"), PYMNT_PLAN_BENE("PYMNT_PLAN_BENE");

	String type;

	TableType(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	@Override
	public String toString() {
		return type;

	}

	public static TableType getTableType(String type) {
		if (!StringUtils.isEmpty(type)) {
			for (TableType s : TableType.values()) {
				if (s.type.equals(type)) {
					return s;
				}
			}
		}
		return TableType.DEFAULT;
//		throw new IllegalArgumentException("Invalid table type : " + type);
	}

}
