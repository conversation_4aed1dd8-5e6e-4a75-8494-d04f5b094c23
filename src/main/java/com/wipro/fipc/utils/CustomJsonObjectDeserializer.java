package com.wipro.fipc.utils;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class CustomJsonObjectDeserializer extends JsonDeserializer<Object>
{
	  @Override
	    public String deserialize(JsonParser jp, DeserializationContext ctxt)
	           throws IOException, JsonProcessingException {

	        ObjectMapper mapper = (ObjectMapper) jp.getCodec();
	        JsonNode node = mapper.readTree(jp);
	        return mapper.writeValueAsString(node);
    }
}
