package com.wipro.fipc.exception;

public class KsdBatchException extends Exception{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public KsdBatchException() {
		super();
	
	}

	public KsdBatchException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
		
	}

	public KsdBatchException(String message, Throwable cause) {
		super(message, cause);
	
	}

	public KsdBatchException(String message) {
		super(message);
	
	}

	public KsdBatchException(Throwable cause) {
		super(cause);
		
	}

}
