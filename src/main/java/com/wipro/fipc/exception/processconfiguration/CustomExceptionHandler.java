package com.wipro.fipc.exception.processconfiguration;

import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.wipro.fipc.common.utils.logging.Level;
import com.wipro.fipc.common.utils.logging.LoggerUtil;

@ControllerAdvice
public class CustomExceptionHandler extends ResponseEntityExceptionHandler {

	@ExceptionHandler(DBServiceException.class)
	public final ResponseEntity<ErrorResponse> handleDBServiceException(DBServiceException dBServiceException) {
		LoggerUtil.log(getClass(), Level.INFO, "handleDBServiceException",
				"Inside handling method for DBServiceException : " + dBServiceException);

		List<String> msg = new ArrayList<>();
		msg.add(dBServiceException.getMessage());
		ErrorResponse error = new ErrorResponse("1001", msg);
		return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(ResourceNotFoundException.class)
	public final ResponseEntity<ErrorResponse> handleResourceNotFoundException(
			ResourceNotFoundException resourceNotFoundException) {
		LoggerUtil.log(getClass(), Level.INFO, "handleResourceNotFoundException",
				"Inside handling method for ResourceNotFoundException : " + resourceNotFoundException);

		List<String> msg = new ArrayList<>();
		msg.add(resourceNotFoundException.getMessage());
		ErrorResponse error = new ErrorResponse("1015", msg);
		return new ResponseEntity<>(error, HttpStatus.NOT_FOUND);
	}

	@ExceptionHandler(InvalidInputException.class)
	public final ResponseEntity<ErrorResponse> handleInvalidInputException(
			InvalidInputException invalidInputException) {
		LoggerUtil.log(getClass(), Level.INFO, "handleInvalidInputException",
				"Inside handling method for InvalidInputException : " + invalidInputException);
		List<String> msg = new ArrayList<>();
		msg.add(invalidInputException.getMessage());
		ErrorResponse error = new ErrorResponse("1006", msg);
		return new ResponseEntity<>(error, HttpStatus.NOT_ACCEPTABLE);
	}

	@ExceptionHandler(ParseException.class)
	public final ResponseEntity<ErrorResponse> handleParseException(ParseException parseException) {
		LoggerUtil.log(getClass(), Level.INFO, "handleParseException",
				"Inside handling method for ParseException : " + parseException);

		List<String> msg = new ArrayList<>();
		msg.add("Given String cannot be parsed to date");
		ErrorResponse error = new ErrorResponse("1004", msg);
		return new ResponseEntity<>(error, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(URISyntaxException.class)
	public final ResponseEntity<ErrorResponse> handleURISyntaxException(URISyntaxException uRISyntaxException) {
		LoggerUtil.log(getClass(), Level.INFO, "handleURISyntaxException",
				"Inside handling method for URISyntaxException : " + uRISyntaxException);

		List<String> msg = new ArrayList<>();
		msg.add(uRISyntaxException.getMessage());
		ErrorResponse error = new ErrorResponse("1013", msg);
		return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(IllegalAccessException.class)
	public final ResponseEntity<ErrorResponse> handleIllegalAccessException(
			IllegalAccessException illegalAccessException) {
		LoggerUtil.log(getClass(), Level.INFO, "handleIllegalAccessException",
				"Inside handling method for IllegalAccessException : " + illegalAccessException);

		List<String> msg = new ArrayList<>();
		msg.add(illegalAccessException.getMessage());
		ErrorResponse error = new ErrorResponse("1017", msg);
		return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(InvocationTargetException.class)
	public final ResponseEntity<ErrorResponse> handleIllegalAccessException(
			InvocationTargetException invocationTargetException) {
		LoggerUtil.log(getClass(), Level.INFO, "handleIllegalAccessException",
				"Inside handling method for invocationTargetException : " + invocationTargetException);

		List<String> msg = new ArrayList<>();
		msg.add(invocationTargetException.getMessage());
		ErrorResponse error = new ErrorResponse("1019", msg);
		return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@Override
	public final ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException e,
			HttpHeaders headers, HttpStatus status, WebRequest request) {
		List<String> details = new ArrayList<>();
		for (ObjectError error : e.getBindingResult().getAllErrors()) {
			details.add(error.getDefaultMessage());
		}

		ErrorResponse error = new ErrorResponse("1009", details);

		return new ResponseEntity<>(error, HttpStatus.BAD_REQUEST);

	}

}
