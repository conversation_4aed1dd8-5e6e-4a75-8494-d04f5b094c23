package com.wipro.fipc.exception.processconfiguration;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class DBServiceException extends RuntimeException {
	
	public DBServiceException() {
		super();
	}
	
	public DBServiceException(String message) {
		super(message);
	}
	
	
	public DBServiceException(Throwable cause) {
		super(cause);
	}

	public DBServiceException(String message, Throwable cause) {
		super(message, cause);
	}
	
	
}
