spring:
  application:
    name: HOLMESAPPCONFIG-SERVICE # ==> This is Service-Id
  profiles:
    active: dev    
  cloud:
    config:
      label: latest
      fail-fast: true      
    
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update
    show-sql: true
    dialect: org.hibernate.dialect.PostgreSQL81Dialect

server:
  port: 3333
  servlet:
    context-path: /fipc/api/appconfig
    
hibernate:
  format_sql: true
  
trustCodeMapping.headers : Client ID,Client Name,CheckWritter Name,Long Description,Short Description,Trust Code

management:
  endpoints:
    web:
      exposure:
        include: '*'

# Adding Properties for Par Specific Information
parClassMapping:
  parClass:
    -
      id: P1572I01
      name: InquiryPanel
      includeClasses:
        - 1572103
    -
      id: P3355R00
      name: InquiryPanel
      arrayAttributes:
        subMetaData: Sub Meta Data
        pyscAr: Payment Schedule
    -
      id: P2688I00
      name: OverPaymentPanel
      arrayAttributes:
        pmntAr: Payment Type
        planAr: Plan Type
    -
      id: P1123R04
      name: PlanStatus
      arrayAttributes:
        catAr: Category
        statusAr: Status     
    -
      id: P1123R01
      name: PlanStatus
      arrayAttributes:
        catAr: Category
        statusAr: Status 
    -
      id: P0490R04
      name: PlanStatus
      arrayAttributes:
        catAr: Category
        statusAr: Status             

    -
      id: P1218I00
      name: QualifiedBalanceMatrix
      arrayAttributes:
        mtrxtypecdAr: Display Type 
        acctAr: Account Type
        qryfundAr: Fund Type
        
    -
      id: P1510R07
      name: PlanSource
      arrayAttributes:
        plansbalAr: Source 
        actCdAr: Posting Code
        cashInkindAr: Cash/Inkind Cd   
     
    -
      id: P2805R00
      name: DB_Special_Maintenance
      arrayAttributes :
        plansrcAr_planfundAr : Plan 
        subfundstrcAr : Fund 
        planacctAr :  Account
        plansoftbalAr : Softbalance   
        
    -
      id: P1510R09
      name: PlanSource
      arrayAttributes:
        plansbalAr: Source 
        actCdAr: Posting Code
        cashInkindAr: Cash/Inkind Cd       
    
    -
      id: P1510R00
      name: PlanSource
      arrayAttributes:
        plansbalAr: Source 
        actCdAr: Posting Code
        cashInkindAr: Cash/Inkind Cd       
        
#Added configuration for configurable activity
activity:
 mappings:
  -
    activityId: 5850
    eventName: DBPHMN
    clientIds: 
     - 1700
     - 7796
     - 7544
     - 16744
     - 7544
    panelIds: 
     - 4398
  -
    activityId: 5850
    eventName: D2PHMN
    clientIds: 
     - 6597
    panelIds: 
     - 4398
  -
    activityId: 7771
    eventName: DBPHMN
    clientIds: 
     - 1442
    panelIds: 
     - 8880
  -
    activityId: 8359
    eventName: DBPMEL
    clientIds: 
     - 1700
     - 7796
     - 7544
     - 16744
    panelIds: 
     - 2719
     - 6936
  -
    activityId: 1108
    eventName: D2PMEL
    clientIds: 
     - 6597
    panelIds: 
     - 9673
     - 4474
  -
    activityId: 5750
    eventName: DBPMEL
    clientIds: 
     - 1442
    panelIds: 
     - 2002
     - 4947
  -
    activityId: 2273
    eventName: DBPMTM
    clientIds: 
     - 1700
     - 7796
     - 16744
    panelIds: 
     - 2192
  -
    activityId: 995090
    eventName: DBPMTS
    clientIds: 
     - 7544
    panelIds: 
     - 208794
  -
    activityId: 9463
    eventName: DBPMTS
    clientIds: 
     - 1442
    panelIds: 
     - 6030
  -
    activityId: 994630
    eventName: DBPMTS
    clientIds: 
     - 7796
    panelIds: 
     - 208794 
  -
    activityId: 994630
    eventName: DBPMTS
    clientIds: 
     - 1700 
    panelIds: 
     - 848484   
  -
    activityId: 100
    eventName: DBPMTS
    clientIds: 
     - 16744 
    panelIds: 
     - 320         
  -
    activityId: 7493
    eventName: D2PMTS
    clientIds: 
     - 6597
    panelIds: 
     - 8214 
  -
    activityId: 2273
    eventName: D2PMTM
    clientIds: 
     - 6597
    panelIds: 
     - 2192
     - 1515
        
---
spring:
  config:
    import: configserver:https://holmes.dev.alight.com/fipc/api/configmanager
    activate:
      on-profile: dev

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ****************************************************************************************************
    username: ENC(PjdvmXrr8TOroOWasWqnllt0yVRpWFtiT5/mbe4+JRnswF3I5n7SDECR+byGU2Xn)
    password: ENC(MZui0qTXVUN2jHJknYZ+GP0qPwlDS8v1oyO+DBf35jBCoiaYQygxKV3t4X9vVFyh)

jasypt:
  encryptor:
    password: Wipro+ali@#56124

---
spring:
  config:
    import: configserver:https://holmes.qa.alight.com/fipc/api/configmanager
    activate:
      on-profile: qa

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ****************************************************************************************
    username: ENC(e9QXH4RSkUEo3BH8OIByCUDEI091VuGPKOcxYtCQF2yBeDpi5qLFTbN9ekTga8pX)
    password: ENC(rSNplrLttNmQTTF75S6BLM09gE/L3lzshNPjqYissIf9uOnqjIt3VYNVsIH46TJS)

jasypt:
  encryptor:
    password: Wipro+ali@#071320220406

---
spring:
  config:
    import: configserver:https://holmes.qc.alight.com/fipc/api/configmanager
    activate:
      on-profile: qc

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ****************************************************************************************
    username: ENC(V1UmdIz7lNA2i4dWx/IIpb/u2vu1J1Vx12+***************************+M)
    password: ENC(+hKtCHT4mhjOL8yTX0tmCfDfIe7ixrs6hozNVDpPdSPZS1BkzZNqPBiPzXUL+D9Z)

jasypt:
  encryptor:
    password: Wipro+ali@#073020221010

---
spring:
  config:
    import: configserver:https://holmes.alight.com/fipc/api/configmanager
    activate:
      on-profile: prod

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ****************************************************************************************
    username: ENC(6qm/f6s9scmnHwWxF0FCHKS5KdmRdgG7s1bJFGdgHMS2YRICXWKf2NAHQ73A9yyE)
    password: ENC(OTU/Krc6Nspt+k4A5u9DgDWK4U6zGKRP6Tt+Yw/w0thpPoIVWF54sWSSVotOaR38)

jasypt:
  encryptor:
    password: Wipro+ali@#100620221055
