swagger: '2.0'
info:
  description: Simple REST API Generation
  version: 0.0.1-SNAPSHOT
  title: Generic REST API
  contact:
    name: <PERSON><PERSON><PERSON> Maharana
    url: 'https://test.com'
    email: <EMAIL>
  license:
    name: 'GNU GENERAL PUBLIC LICENSE, Version 3'
    url: 'https://www.gnu.org/licenses/gpl-3.0.en.html'
host: 'localhost:8083'
basePath: /
tags:
  - name: rules-definitions-controller
    description: Rules Definitions Controller
  - name: notification-mail-config-controller
    description: Notification Mail Config Controller
  - name: web-mvc-links-handler
    description: Web Mvc Links Handler
  - name: operation-handler
    description: Operation Handler
  - name: process-features-mapping-controller
    description: Process Features Mapping Controller
  - name: job-schedule-controller
    description: Job Schedule Controller
  - name: basic-error-controller
    description: Basic Error Controller
  - name: business-unit-client-controller
    description: Business Unit Client Controller
  - name: holiday-calendar-controller
    description: Holiday Calendar Controller
  - name: phase-controller
    description: Phase Controller
  - name: business-ops-controller
    description: Business Ops Controller
  - name: ticket-creation-config-controller
    description: Ticket Creation Config Controller
  - name: match-conditions-controller
    description: Match Conditions Controller
  - name: task-update-config-controller
    description: Task Update Config Controller
  - name: layout-config-controller
    description: Layout Config Controller
  - name: process-feature-config-controller
    description: Process Feature Config Controller
  - name: process-job-mapping-controller
    description: Process Job Mapping Controller
  - name: notification-report-config-controller
    description: Notification Report Config Controller
  - name: error-ticket-type-config-controller
    description: Error Ticket Type Config Controller
  - name: business-unit-controller
    description: Business Unit Controller
  - name: rules-definition-controller
    description: Rules Definition Controller
  - name: all-clients-jobs-controller
    description: All Clients Jobs Controller
  - name: rules-config-controller
    description: Rules Config Controller
  - name: ksd-master-config-controller
    description: Ksd Master Config Controller
  - name: drool-file-details-controller
    description: Drool File Details Controller
  - name: client-details-controller
    description: Client Details Controller
paths:
  '/api/v1/common/businessunit/byAlightId/{alightId}':
    get:
      tags:
        - business-unit-controller
      summary: getByAlightId
      operationId: getByAlightIdUsingGET
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: alightId
          in: path
          description: alightId
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/CustomBusinessUnitBO'
  '/api/v1/common/businessunit/getDetailsBy/{alightId}/{buid}':
    get:
      tags:
        - business-unit-controller
      summary: getByAlightIdBuId
      operationId: getByAlightIdBuIdUsingGET
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: alightId
          in: path
          description: alightId
          required: true
          type: string
        - name: buid
          in: path
          description: buid
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/CustomClientDetailsBO'
  '/api/v1/common/businessunit/getRequiredDetailsBy/{alightId}/{clientid}/{buid}/{buopsid}':
    get:
      tags:
        - business-unit-controller
      summary: getRequiredDetailsBy
      operationId: getRequiredDetailsByUsingGET
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: alightId
          in: path
          description: alightId
          required: true
          type: string
        - name: clientid
          in: path
          description: clientid
          required: true
          type: integer
          format: int64
        - name: buid
          in: path
          description: buid
          required: true
          type: integer
          format: int64
        - name: buopsid
          in: path
          description: buopsid
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/CustomProcessBO'
  '/api/v1/common/processjobmapping/JobName/{buid}/{buopsid}/{clientid}/{processID}':
    get:
      tags:
        - process-job-mapping-controller
      summary: getJobNames
      operationId: getJobNamesUsingGET
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: buid
          in: path
          description: buid
          required: true
          type: integer
          format: int64
        - name: buopsid
          in: path
          description: buopsid
          required: true
          type: integer
          format: int64
        - name: clientid
          in: path
          description: clientid
          required: true
          type: integer
          format: int64
        - name: processID
          in: path
          description: processID
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/CustomProcessJobMappingBO'
definitions:
  CustomBusinessUnitBO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      unitCode:
        type: string
      unitName:
        type: string
  CustomClientDetailsBO:
    type: object
    properties:
      buOpsId:
        type: integer
        format: int64
      clientCode:
        type: string
      clientId:
        type: integer
        format: int64
      clientName:
        type: string
      opsCode:
        type: string
      opsName:
        type: string
  CustomProcessBO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      processName:
        type: string
      processType:
        type: string
  CustomProcessJobMappingBO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      jobName:
        type: string
