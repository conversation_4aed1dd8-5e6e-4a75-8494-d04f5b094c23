swagger: '2.0'
info:
  description: Simple REST API Generation
  version: 0.0.1-SNAPSHOT
  title: Generic REST API
  contact:
    name: <PERSON><PERSON><PERSON>na
    url: 'https://test.com'
    email: <EMAIL>
  license:
    name: 'GNU GENERAL PUBLIC LICENSE, Version 3'
    url: 'https://www.gnu.org/licenses/gpl-3.0.en.html'
host: 'localhost:8083'
basePath: /
tags:
  - name: rules-config-controller
    description: Rules Config Controller
paths:
  /api/v1/rulesconfig:
    get:
      tags:
        - operation-handler
      summary: handle
      operationId: handleUsingGET_8
      consumes:
        - application/json
      produces:
        - application/json
        - application/vnd.spring-boot.actuator.v2+json
      parameters:
        - in: body
          name: body
          description: body
          required: false
          schema:
            type: object
            additionalProperties:
              type: string
      responses:
        '200':
          description: OK
          schema:
            type: object
    post:
      tags:
        - operation-handler
      summary: handle
      operationId: handleUsingPOST
      consumes:
        - application/json
        - application/vnd.spring-boot.actuator.v2+json
      produces:
        - application/json
        - application/vnd.spring-boot.actuator.v2+json
      parameters:
        - in: body
          name: body
          description: body
          required: false
          schema:
            type: object
            additionalProperties:
              type: string
      responses:
        '200':
          description: OK
          schema:
            type: object
    delete:
      tags:
        - operation-handler
      summary: handle
      operationId: handleUsingDELETE_2
      consumes:
        - application/json
      produces:
        - application/json
        - application/vnd.spring-boot.actuator.v2+json
      parameters:
        - in: body
          name: body
          description: body
          required: false
          schema:
            type: object
            additionalProperties:
              type: string
      responses:
        '200':
          description: OK
          schema:
            type: object
definitions:
  RulesConfig:
    type: object
    properties:
      active:
        type: string
      createdBy:
        type: string
      createdDate:
        type: string
        format: date
      endDate:
        type: string
        format: date
      id:
        type: integer
        format: int64
      phaseName:
        type: string
      rulesDefinitions:
        type: array
        items:
          $ref: '#/definitions/RulesDefinition'
      startDate:
        type: string
        format: date
      updatedBy:
        type: string
      updatedDate:
        type: string
        format: date
  RulesDefinition:
    type: object
    properties:
      createdBy:
        type: string
      createdDate:
        type: string
        format: date
      customParam:
        type: string
      customValue:
        type: string
      filed1:
        type: string
      filed2:
        type: string
      filed3:
        type: string
      filed4:
        type: string
      filed5:
        type: string
      id:
        type: integer
        format: int64
      primaryFieldName:
        type: string
      ruleType:
        type: string
      rulesConfig:
        $ref: '#/definitions/RulesConfig'
      updatedBy:
        type: string
      updatedDate:
        type: string
        format: date
      validationType:
        $ref: '#/definitions/ValidationType'
  ValidationType:
    type: object
    properties:
      createdBy:
        type: string
      createdDate:
        type: string
        format: date
      id:
        type: integer
        format: int64
      rulesDefinitions:
        type: array
        items:
          $ref: '#/definitions/RulesDefinition'
      updatedBy:
        type: string
      updatedDate:
        type: string
        format: date
      valTypeCode:
        type: string
      valTypeName:
        type: string
