{"swagger": "2.0", "info": {"description": "Simple REST API Generation", "version": "0.0.1-SNAPSHOT", "title": "Generic REST API", "contact": {"name": "<PERSON><PERSON><PERSON>", "url": "https://test.com", "email": "<EMAIL>"}, "license": {"name": "GNU GENERAL PUBLIC LICENSE, Version 3", "url": "https://www.gnu.org/licenses/gpl-3.0.en.html"}}, "host": "localhost:8083", "basePath": "/", "tags": [{"name": "tba-inquiry-config-controller", "description": "Tba Inquiry Config Controller"}, {"name": "rules-definitions-controller", "description": "Rules Definitions Controller"}, {"name": "notification-mail-config-controller", "description": "Notification Mail Config Controller"}, {"name": "web-mvc-links-handler", "description": "Web Mvc Links Handler"}, {"name": "operation-handler", "description": "Operation Handler"}, {"name": "process-control-config-controller", "description": "Process Control Config Controller"}, {"name": "process-features-mapping-controller", "description": "Process Features Mapping Controller"}, {"name": "job-schedule-controller", "description": "Job Schedule Controller"}, {"name": "basic-error-controller", "description": "Basic Error Controller"}, {"name": "business-unit-client-controller", "description": "Business Unit Client Controller"}, {"name": "holiday-calendar-controller", "description": "Holiday Calendar Controller"}, {"name": "phase-controller", "description": "Phase Controller"}, {"name": "business-ops-controller", "description": "Business Ops Controller"}, {"name": "ticket-creation-config-controller", "description": "Ticket Creation Config Controller"}, {"name": "match-conditions-controller", "description": "Match Conditions Controller"}, {"name": "tba-meta-data-controller", "description": "Tba Meta Data Controller"}, {"name": "tba-inquiry-json-key-controller", "description": "Tba Inquiry Json Key Controller"}, {"name": "task-update-config-controller", "description": "Task Update Config Controller"}, {"name": "layout-config-controller", "description": "Layout Config Controller"}, {"name": "process-feature-config-controller", "description": "Process Feature Config Controller"}, {"name": "process-job-mapping-controller", "description": "Process Job Mapping Controller"}, {"name": "notification-report-config-controller", "description": "Notification Report Config Controller"}, {"name": "error-ticket-type-config-controller", "description": "Error Ticket Type Config Controller"}, {"name": "validation-type-controller", "description": "Validation Type Controller"}, {"name": "business-unit-controller", "description": "Business Unit Controller"}, {"name": "tba-update-activity-controller", "description": "Tba Update Activity Controller"}, {"name": "tba-update-json-key-controller", "description": "Tba Update Json Key Controller"}, {"name": "rules-definition-controller", "description": "Rules Definition Controller"}, {"name": "all-clients-jobs-controller", "description": "All Clients Jobs Controller"}, {"name": "ksd-file-details-controller", "description": "Ksd File Details Controller"}, {"name": "rules-config-controller", "description": "Rules Config Controller"}, {"name": "ksd-master-config-controller", "description": "Ksd Master Config Controller"}, {"name": "tba-update-config-controller", "description": "Tba Update Config Controller"}, {"name": "drool-file-details-controller", "description": "Drool File Details Controller"}, {"name": "tba-process-inquiry-controller", "description": "Tba Process Inquiry Controller"}, {"name": "tba-update-meta-data-controller", "description": "Tba Update Meta Data Controller"}, {"name": "client-details-controller", "description": "Client Details Controller"}, {"name": "tba-update-process-controller", "description": "Tba Update Process Controller"}, {"name": "tba-match-config-controller", "description": "Tba Match Config Controller"}], "paths": {"/actuator": {"get": {"tags": ["web-mvc-links-handler"], "summary": "links", "operationId": "linksUsingGET", "consumes": ["application/json"], "produces": ["application/json", "application/vnd.spring-boot.actuator.v2+json"], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Map«string,Link»"}}}}}}, "/actuator/health": {"get": {"tags": ["operation-handler"], "summary": "handle", "operationId": "handleUsingGET_2", "consumes": ["application/json"], "produces": ["application/json", "application/vnd.spring-boot.actuator.v2+json"], "parameters": [{"in": "body", "name": "body", "description": "body", "required": false, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/actuator/health/{component}": {"get": {"tags": ["operation-handler"], "summary": "handle", "operationId": "handleUsingGET_1", "consumes": ["application/json"], "produces": ["application/json", "application/vnd.spring-boot.actuator.v2+json"], "parameters": [{"in": "body", "name": "body", "description": "body", "required": false, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/actuator/health/{component}/{instance}": {"get": {"tags": ["operation-handler"], "summary": "handle", "operationId": "handleUsingGET", "consumes": ["application/json"], "produces": ["application/json", "application/vnd.spring-boot.actuator.v2+json"], "parameters": [{"in": "body", "name": "body", "description": "body", "required": false, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/actuator/info": {"get": {"tags": ["operation-handler"], "summary": "handle", "operationId": "handleUsingGET_3", "consumes": ["application/json"], "produces": ["application/json", "application/vnd.spring-boot.actuator.v2+json"], "parameters": [{"in": "body", "name": "body", "description": "body", "required": false, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/api/v1/common/businessops": {"get": {"tags": ["business-ops-controller"], "summary": "list", "operationId": "listUsingGET_1", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessOps"}}}}}, "post": {"tags": ["business-ops-controller"], "summary": "create", "operationId": "createUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/BusinessOps"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BusinessOps"}}}}}, "/api/v1/common/businessops/mulitquery": {"get": {"tags": ["business-ops-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessOps"}}}}}}, "/api/v1/common/businessops/{column_name}/{column_value}": {"get": {"tags": ["business-ops-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessOps"}}}}}, "delete": {"tags": ["business-ops-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/businessops/{id}": {"put": {"tags": ["business-ops-controller"], "summary": "update", "operationId": "updateUsingPUT_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/BusinessOps"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BusinessOps"}}}}, "delete": {"tags": ["business-ops-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/businessunit": {"get": {"tags": ["business-unit-controller"], "summary": "list", "operationId": "listUsingGET_3", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnit"}}}}}, "post": {"tags": ["business-unit-controller"], "summary": "create", "operationId": "createUsingPOST_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/BusinessUnit"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BusinessUnit"}}}}}, "/api/v1/common/businessunit/byAlightId/{alightId}": {"get": {"tags": ["business-unit-controller"], "summary": "getByAlightId", "operationId": "getByAlightIdUsingGET", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "alightId", "in": "path", "description": "alightId", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CustomBusinessUnitBO"}}}}}}, "/api/v1/common/businessunit/getDetailsBy/{alightId}/{buid}": {"get": {"tags": ["business-unit-controller"], "summary": "getByAlightIdBuId", "operationId": "getByAlightIdBuIdUsingGET", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "alightId", "in": "path", "description": "alightId", "required": true, "type": "string"}, {"name": "buid", "in": "path", "description": "buid", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CustomClientDetailsBO"}}}}}}, "/api/v1/common/businessunit/getRequiredDetailsBy/{alightId}/{clientid}/{buid}/{buopsid}": {"get": {"tags": ["business-unit-controller"], "summary": "getRequiredDetailsBy", "operationId": "getRequiredDetailsByUsingGET", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "alightId", "in": "path", "description": "alightId", "required": true, "type": "string"}, {"name": "clientid", "in": "path", "description": "clientid", "required": true, "type": "integer", "format": "int64"}, {"name": "buid", "in": "path", "description": "buid", "required": true, "type": "integer", "format": "int64"}, {"name": "buopsid", "in": "path", "description": "buopsid", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CustomProcessBO"}}}}}}, "/api/v1/common/businessunit/mulitquery": {"get": {"tags": ["business-unit-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnit"}}}}}}, "/api/v1/common/businessunit/{column_name}/{column_value}": {"get": {"tags": ["business-unit-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnit"}}}}}, "delete": {"tags": ["business-unit-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/businessunit/{id}": {"put": {"tags": ["business-unit-controller"], "summary": "update", "operationId": "updateUsingPUT_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/BusinessUnit"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BusinessUnit"}}}}, "delete": {"tags": ["business-unit-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/businessunitclient": {"get": {"tags": ["business-unit-client-controller"], "summary": "list", "operationId": "listUsingGET_2", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnitClient"}}}}}, "post": {"tags": ["business-unit-client-controller"], "summary": "create", "operationId": "createUsingPOST_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/BusinessUnitClient"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BusinessUnitClient"}}}}}, "/api/v1/common/businessunitclient/mulitquery": {"get": {"tags": ["business-unit-client-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnitClient"}}}}}}, "/api/v1/common/businessunitclient/{column_name}/{column_value}": {"get": {"tags": ["business-unit-client-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnitClient"}}}}}, "delete": {"tags": ["business-unit-client-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/businessunitclient/{id}": {"put": {"tags": ["business-unit-client-controller"], "summary": "update", "operationId": "updateUsingPUT_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/BusinessUnitClient"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BusinessUnitClient"}}}}, "delete": {"tags": ["business-unit-client-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/clientdetails": {"get": {"tags": ["client-details-controller"], "summary": "list", "operationId": "listUsingGET_4", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ClientDetails"}}}}}, "post": {"tags": ["client-details-controller"], "summary": "create", "operationId": "createUsingPOST_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ClientDetails"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ClientDetails"}}}}}, "/api/v1/common/clientdetails/mulitquery": {"get": {"tags": ["client-details-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ClientDetails"}}}}}}, "/api/v1/common/clientdetails/{column_name}/{column_value}": {"get": {"tags": ["client-details-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ClientDetails"}}}}}, "delete": {"tags": ["client-details-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/clientdetails/{id}": {"put": {"tags": ["client-details-controller"], "summary": "update", "operationId": "updateUsingPUT_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ClientDetails"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ClientDetails"}}}}, "delete": {"tags": ["client-details-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_4", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/errortickettypeconfig": {"get": {"tags": ["error-ticket-type-config-controller"], "summary": "list", "operationId": "listUsingGET_6", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorTicketTypeConfig"}}}}}, "post": {"tags": ["error-ticket-type-config-controller"], "summary": "create", "operationId": "createUsingPOST_6", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ErrorTicketTypeConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ErrorTicketTypeConfig"}}}}}, "/api/v1/common/errortickettypeconfig/mulitquery": {"get": {"tags": ["error-ticket-type-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_6", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorTicketTypeConfig"}}}}}}, "/api/v1/common/errortickettypeconfig/{column_name}/{column_value}": {"get": {"tags": ["error-ticket-type-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_6", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorTicketTypeConfig"}}}}}, "delete": {"tags": ["error-ticket-type-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_6", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/errortickettypeconfig/{id}": {"put": {"tags": ["error-ticket-type-config-controller"], "summary": "update", "operationId": "updateUsingPUT_6", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ErrorTicketTypeConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ErrorTicketTypeConfig"}}}}, "delete": {"tags": ["error-ticket-type-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_6", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/matchconditions": {"get": {"tags": ["match-conditions-controller"], "summary": "list", "operationId": "listUsingGET_12", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/MatchConditions"}}}}}, "post": {"tags": ["match-conditions-controller"], "summary": "create", "operationId": "createUsingPOST_12", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/MatchConditions"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MatchConditions"}}}}}, "/api/v1/common/matchconditions/mulitquery": {"get": {"tags": ["match-conditions-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_12", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/MatchConditions"}}}}}}, "/api/v1/common/matchconditions/{column_name}/{column_value}": {"get": {"tags": ["match-conditions-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_12", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/MatchConditions"}}}}}, "delete": {"tags": ["match-conditions-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_12", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/matchconditions/{id}": {"put": {"tags": ["match-conditions-controller"], "summary": "update", "operationId": "updateUsingPUT_12", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/MatchConditions"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MatchConditions"}}}}, "delete": {"tags": ["match-conditions-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_12", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/notificationReportConfig": {"get": {"tags": ["notification-report-config-controller"], "summary": "list", "operationId": "listUsingGET_14", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationReportConfig"}}}}}, "post": {"tags": ["notification-report-config-controller"], "summary": "create", "operationId": "createUsingPOST_14", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/NotificationReportConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/NotificationReportConfig"}}}}}, "/api/v1/common/notificationReportConfig/mulitquery": {"get": {"tags": ["notification-report-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_14", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationReportConfig"}}}}}}, "/api/v1/common/notificationReportConfig/{column_name}/{column_value}": {"get": {"tags": ["notification-report-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_14", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationReportConfig"}}}}}, "delete": {"tags": ["notification-report-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_14", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/notificationReportConfig/{id}": {"put": {"tags": ["notification-report-config-controller"], "summary": "update", "operationId": "updateUsingPUT_14", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/NotificationReportConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/NotificationReportConfig"}}}}, "delete": {"tags": ["notification-report-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_14", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/notificationmailconfig": {"get": {"tags": ["notification-mail-config-controller"], "summary": "list", "operationId": "listUsingGET_13", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationMailConfig"}}}}}, "post": {"tags": ["notification-mail-config-controller"], "summary": "create", "operationId": "createUsingPOST_13", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/NotificationMailConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/NotificationMailConfig"}}}}}, "/api/v1/common/notificationmailconfig/mulitquery": {"get": {"tags": ["notification-mail-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_13", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationMailConfig"}}}}}}, "/api/v1/common/notificationmailconfig/{column_name}/{column_value}": {"get": {"tags": ["notification-mail-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_13", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotificationMailConfig"}}}}}, "delete": {"tags": ["notification-mail-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_13", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/notificationmailconfig/{id}": {"put": {"tags": ["notification-mail-config-controller"], "summary": "update", "operationId": "updateUsingPUT_13", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/NotificationMailConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/NotificationMailConfig"}}}}, "delete": {"tags": ["notification-mail-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_13", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/processcontrolconfig": {"get": {"tags": ["process-control-config-controller"], "summary": "list", "operationId": "listUsingGET_16", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessControlConfig"}}}}}, "post": {"tags": ["process-control-config-controller"], "summary": "create", "operationId": "createUsingPOST_16", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessControlConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessControlConfig"}}}}}, "/api/v1/common/processcontrolconfig/mulitquery": {"get": {"tags": ["process-control-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_16", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessControlConfig"}}}}}}, "/api/v1/common/processcontrolconfig/{column_name}/{column_value}": {"get": {"tags": ["process-control-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_16", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessControlConfig"}}}}}, "delete": {"tags": ["process-control-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_16", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/processcontrolconfig/{id}": {"put": {"tags": ["process-control-config-controller"], "summary": "update", "operationId": "updateUsingPUT_16", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessControlConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessControlConfig"}}}}, "delete": {"tags": ["process-control-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_16", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/processfeatureconfig": {"get": {"tags": ["process-feature-config-controller"], "summary": "list", "operationId": "listUsingGET_17", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessFeatureConfig"}}}}}, "post": {"tags": ["process-feature-config-controller"], "summary": "create", "operationId": "createUsingPOST_17", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessFeatureConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessFeatureConfig"}}}}}, "/api/v1/common/processfeatureconfig/mulitquery": {"get": {"tags": ["process-feature-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_17", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessFeatureConfig"}}}}}}, "/api/v1/common/processfeatureconfig/{column_name}/{column_value}": {"get": {"tags": ["process-feature-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_17", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessFeatureConfig"}}}}}, "delete": {"tags": ["process-feature-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_17", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/processfeatureconfig/{id}": {"put": {"tags": ["process-feature-config-controller"], "summary": "update", "operationId": "updateUsingPUT_17", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessFeatureConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessFeatureConfig"}}}}, "delete": {"tags": ["process-feature-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_17", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/processfeaturesmapping": {"get": {"tags": ["process-features-mapping-controller"], "summary": "list", "operationId": "listUsingGET_18", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessFeaturesMapping"}}}}}, "post": {"tags": ["process-features-mapping-controller"], "summary": "create", "operationId": "createUsingPOST_18", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessFeaturesMapping"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessFeaturesMapping"}}}}}, "/api/v1/common/processfeaturesmapping/mulitquery": {"get": {"tags": ["process-features-mapping-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_18", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessFeaturesMapping"}}}}}}, "/api/v1/common/processfeaturesmapping/{column_name}/{column_value}": {"get": {"tags": ["process-features-mapping-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_18", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessFeaturesMapping"}}}}}, "delete": {"tags": ["process-features-mapping-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_18", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/processfeaturesmapping/{id}": {"put": {"tags": ["process-features-mapping-controller"], "summary": "update", "operationId": "updateUsingPUT_18", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessFeaturesMapping"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessFeaturesMapping"}}}}, "delete": {"tags": ["process-features-mapping-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_18", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/processjobmapping": {"get": {"tags": ["process-job-mapping-controller"], "summary": "list", "operationId": "listUsingGET_19", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessJobMapping"}}}}}, "post": {"tags": ["process-job-mapping-controller"], "summary": "create", "operationId": "createUsingPOST_19", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessJobMapping"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessJobMapping"}}}}}, "/api/v1/common/processjobmapping/JobName/{buid}/{buopsid}/{clientid}/{processID}": {"get": {"tags": ["process-job-mapping-controller"], "summary": "getJobNames", "operationId": "getJobNamesUsingGET", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "buid", "in": "path", "description": "buid", "required": true, "type": "integer", "format": "int64"}, {"name": "buopsid", "in": "path", "description": "buopsid", "required": true, "type": "integer", "format": "int64"}, {"name": "clientid", "in": "path", "description": "clientid", "required": true, "type": "integer", "format": "int64"}, {"name": "processID", "in": "path", "description": "processID", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CustomProcessJobMappingBO"}}}}}}, "/api/v1/common/processjobmapping/mulitquery": {"get": {"tags": ["process-job-mapping-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_19", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessJobMapping"}}}}}}, "/api/v1/common/processjobmapping/{column_name}/{column_value}": {"get": {"tags": ["process-job-mapping-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_19", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ProcessJobMapping"}}}}}, "delete": {"tags": ["process-job-mapping-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_19", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/processjobmapping/{id}": {"put": {"tags": ["process-job-mapping-controller"], "summary": "update", "operationId": "updateUsingPUT_19", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ProcessJobMapping"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessJobMapping"}}}}, "delete": {"tags": ["process-job-mapping-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_19", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/rulesdefinition": {"get": {"tags": ["rules-definition-controller"], "summary": "list", "operationId": "listUsingGET_21", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesDefinition"}}}}}, "post": {"tags": ["rules-definition-controller"], "summary": "create", "operationId": "createUsingPOST_21", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/RulesDefinition"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RulesDefinition"}}}}}, "/api/v1/common/rulesdefinition/mulitquery": {"get": {"tags": ["rules-definition-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_21", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesDefinition"}}}}}}, "/api/v1/common/rulesdefinition/{column_name}/{column_value}": {"get": {"tags": ["rules-definition-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_21", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesDefinition"}}}}}, "delete": {"tags": ["rules-definition-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_21", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/rulesdefinition/{id}": {"put": {"tags": ["rules-definition-controller"], "summary": "update", "operationId": "updateUsingPUT_21", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/RulesDefinition"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RulesDefinition"}}}}, "delete": {"tags": ["rules-definition-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_21", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/common/tbamatchconfig": {"get": {"tags": ["tba-match-config-controller"], "summary": "list", "operationId": "listUsingGET_26", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaMatchConfig"}}}}}, "post": {"tags": ["tba-match-config-controller"], "summary": "create", "operationId": "createUsingPOST_26", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaMatchConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaMatchConfig"}}}}}, "/api/v1/common/tbamatchconfig/mulitquery": {"get": {"tags": ["tba-match-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_26", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaMatchConfig"}}}}}}, "/api/v1/common/tbamatchconfig/{column_name}/{column_value}": {"get": {"tags": ["tba-match-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_26", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaMatchConfig"}}}}}, "delete": {"tags": ["tba-match-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_26", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/common/tbamatchconfig/{id}": {"put": {"tags": ["tba-match-config-controller"], "summary": "update", "operationId": "updateUsingPUT_26", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaMatchConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaMatchConfig"}}}}, "delete": {"tags": ["tba-match-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_26", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/ksdconfig/allclientjob": {"get": {"tags": ["all-clients-jobs-controller"], "summary": "list", "operationId": "listUsingGET", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/AllClientsJobs"}}}}}, "post": {"tags": ["all-clients-jobs-controller"], "summary": "create", "operationId": "createUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/AllClientsJobs"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/AllClientsJobs"}}}}}, "/api/v1/ksdconfig/allclientjob/mulitquery": {"get": {"tags": ["all-clients-jobs-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/AllClientsJobs"}}}}}}, "/api/v1/ksdconfig/allclientjob/{column_name}/{column_value}": {"get": {"tags": ["all-clients-jobs-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/AllClientsJobs"}}}}}, "delete": {"tags": ["all-clients-jobs-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/ksdconfig/allclientjob/{id}": {"put": {"tags": ["all-clients-jobs-controller"], "summary": "update", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/AllClientsJobs"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/AllClientsJobs"}}}}, "delete": {"tags": ["all-clients-jobs-controller"], "summary": "delete", "operationId": "deleteUsingDELETE", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/ksdconfig/holiday": {"get": {"tags": ["holiday-calendar-controller"], "summary": "list", "operationId": "listUsingGET_7", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/HolidayCalendar"}}}}}, "post": {"tags": ["holiday-calendar-controller"], "summary": "create", "operationId": "createUsingPOST_7", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/HolidayCalendar"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/HolidayCalendar"}}}}}, "/api/v1/ksdconfig/holiday/mulitquery": {"get": {"tags": ["holiday-calendar-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_7", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/HolidayCalendar"}}}}}}, "/api/v1/ksdconfig/holiday/{column_name}/{column_value}": {"get": {"tags": ["holiday-calendar-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_7", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/HolidayCalendar"}}}}}, "delete": {"tags": ["holiday-calendar-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_7", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/ksdconfig/holiday/{id}": {"put": {"tags": ["holiday-calendar-controller"], "summary": "update", "operationId": "updateUsingPUT_7", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/HolidayCalendar"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/HolidayCalendar"}}}}, "delete": {"tags": ["holiday-calendar-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_7", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/ksdconfig/jobschedule": {"get": {"tags": ["job-schedule-controller"], "summary": "list", "operationId": "listUsingGET_8", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/JobSchedule"}}}}}, "post": {"tags": ["job-schedule-controller"], "summary": "create", "operationId": "createUsingPOST_8", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/JobSchedule"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JobSchedule"}}}}}, "/api/v1/ksdconfig/jobschedule/mulitquery": {"get": {"tags": ["job-schedule-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_8", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/JobSchedule"}}}}}}, "/api/v1/ksdconfig/jobschedule/{column_name}/{column_value}": {"get": {"tags": ["job-schedule-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_8", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/JobSchedule"}}}}}, "delete": {"tags": ["job-schedule-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_8", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/ksdconfig/jobschedule/{id}": {"put": {"tags": ["job-schedule-controller"], "summary": "update", "operationId": "updateUsingPUT_8", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/JobSchedule"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JobSchedule"}}}}, "delete": {"tags": ["job-schedule-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_8", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/ksdconfig/ksdfiledetails": {"get": {"tags": ["ksd-file-details-controller"], "summary": "list", "operationId": "listUsingGET_9", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/KsdFileDetails"}}}}}, "post": {"tags": ["ksd-file-details-controller"], "summary": "create", "operationId": "createUsingPOST_9", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/KsdFileDetails"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/KsdFileDetails"}}}}}, "/api/v1/ksdconfig/ksdfiledetails/mulitquery": {"get": {"tags": ["ksd-file-details-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_9", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/KsdFileDetails"}}}}}}, "/api/v1/ksdconfig/ksdfiledetails/{column_name}/{column_value}": {"get": {"tags": ["ksd-file-details-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_9", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/KsdFileDetails"}}}}}, "delete": {"tags": ["ksd-file-details-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_9", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/ksdconfig/ksdfiledetails/{id}": {"put": {"tags": ["ksd-file-details-controller"], "summary": "update", "operationId": "updateUsingPUT_9", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/KsdFileDetails"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/KsdFileDetails"}}}}, "delete": {"tags": ["ksd-file-details-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_9", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/ksdconfig/ksdmasterconfig": {"get": {"tags": ["ksd-master-config-controller"], "summary": "list", "operationId": "listUsingGET_10", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/KsdMasterConfig"}}}}}, "post": {"tags": ["ksd-master-config-controller"], "summary": "create", "operationId": "createUsingPOST_10", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/KsdMasterConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/KsdMasterConfig"}}}}}, "/api/v1/ksdconfig/ksdmasterconfig/mulitquery": {"get": {"tags": ["ksd-master-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_10", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/KsdMasterConfig"}}}}}}, "/api/v1/ksdconfig/ksdmasterconfig/{column_name}/{column_value}": {"get": {"tags": ["ksd-master-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_10", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/KsdMasterConfig"}}}}}, "delete": {"tags": ["ksd-master-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_10", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/ksdconfig/ksdmasterconfig/{id}": {"put": {"tags": ["ksd-master-config-controller"], "summary": "update", "operationId": "updateUsingPUT_10", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/KsdMasterConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/KsdMasterConfig"}}}}, "delete": {"tags": ["ksd-master-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_10", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/layoutrule/ValidationType": {"get": {"tags": ["validation-type-controller"], "summary": "list", "operationId": "listUsingGET_35", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ValidationType"}}}}}, "post": {"tags": ["validation-type-controller"], "summary": "create", "operationId": "createUsingPOST_35", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ValidationType"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ValidationType"}}}}}, "/api/v1/layoutrule/ValidationType/mulitquery": {"get": {"tags": ["validation-type-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_35", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ValidationType"}}}}}}, "/api/v1/layoutrule/ValidationType/{column_name}/{column_value}": {"get": {"tags": ["validation-type-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_35", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ValidationType"}}}}}, "delete": {"tags": ["validation-type-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_35", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/layoutrule/ValidationType/{id}": {"put": {"tags": ["validation-type-controller"], "summary": "update", "operationId": "updateUsingPUT_35", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/ValidationType"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ValidationType"}}}}, "delete": {"tags": ["validation-type-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_35", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/layoutrule/droolFileDetails": {"get": {"tags": ["drool-file-details-controller"], "summary": "list", "operationId": "listUsingGET_5", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/DroolFileDetails"}}}}}, "post": {"tags": ["drool-file-details-controller"], "summary": "create", "operationId": "createUsingPOST_5", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/DroolFileDetails"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DroolFileDetails"}}}}}, "/api/v1/layoutrule/droolFileDetails/mulitquery": {"get": {"tags": ["drool-file-details-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_5", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/DroolFileDetails"}}}}}}, "/api/v1/layoutrule/droolFileDetails/{column_name}/{column_value}": {"get": {"tags": ["drool-file-details-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_5", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/DroolFileDetails"}}}}}, "delete": {"tags": ["drool-file-details-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_5", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/layoutrule/droolFileDetails/{id}": {"put": {"tags": ["drool-file-details-controller"], "summary": "update", "operationId": "updateUsingPUT_5", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/DroolFileDetails"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DroolFileDetails"}}}}, "delete": {"tags": ["drool-file-details-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_5", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/layoutrule/layoutconfig": {"get": {"tags": ["layout-config-controller"], "summary": "list", "operationId": "listUsingGET_11", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/LayoutConfig"}}}}}, "post": {"tags": ["layout-config-controller"], "summary": "create", "operationId": "createUsingPOST_11", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/LayoutConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutConfig"}}}}}, "/api/v1/layoutrule/layoutconfig/mulitquery": {"get": {"tags": ["layout-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_11", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/LayoutConfig"}}}}}}, "/api/v1/layoutrule/layoutconfig/savelayoutData": {"post": {"tags": ["layout-config-controller"], "summary": "saveAllData", "operationId": "saveAllDataUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "layoutConfigs", "description": "layoutConfigs", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/LayoutConfig"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/LayoutConfig"}}}}}}, "/api/v1/layoutrule/layoutconfig/{column_name}/{column_value}": {"get": {"tags": ["layout-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_11", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/LayoutConfig"}}}}}, "delete": {"tags": ["layout-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_11", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/layoutrule/layoutconfig/{id}": {"put": {"tags": ["layout-config-controller"], "summary": "update", "operationId": "updateUsingPUT_11", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/LayoutConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LayoutConfig"}}}}, "delete": {"tags": ["layout-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_11", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/layoutrule/ruledef": {"get": {"tags": ["rules-definitions-controller"], "summary": "list", "operationId": "listUsingGET_22", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesDefinition"}}}}}, "post": {"tags": ["rules-definitions-controller"], "summary": "create", "operationId": "createUsingPOST_22", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/RulesDefinition"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RulesDefinition"}}}}}, "/api/v1/layoutrule/ruledef/mulitquery": {"get": {"tags": ["rules-definitions-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_22", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesDefinition"}}}}}}, "/api/v1/layoutrule/ruledef/{column_name}/{column_value}": {"get": {"tags": ["rules-definitions-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_22", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesDefinition"}}}}}, "delete": {"tags": ["rules-definitions-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_22", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/layoutrule/ruledef/{id}": {"put": {"tags": ["rules-definitions-controller"], "summary": "update", "operationId": "updateUsingPUT_22", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/RulesDefinition"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RulesDefinition"}}}}, "delete": {"tags": ["rules-definitions-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_22", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/layoutrule/rulesconfig": {"get": {"tags": ["rules-config-controller"], "summary": "list", "operationId": "listUsingGET_20", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesConfig"}}}}}, "post": {"tags": ["rules-config-controller"], "summary": "create", "operationId": "createUsingPOST_20", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/RulesConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RulesConfig"}}}}}, "/api/v1/layoutrule/rulesconfig/mulitquery": {"get": {"tags": ["rules-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_20", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesConfig"}}}}}}, "/api/v1/layoutrule/rulesconfig/{column_name}/{column_value}": {"get": {"tags": ["rules-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_20", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RulesConfig"}}}}}, "delete": {"tags": ["rules-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_20", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/layoutrule/rulesconfig/{id}": {"put": {"tags": ["rules-config-controller"], "summary": "update", "operationId": "updateUsingPUT_20", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/RulesConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RulesConfig"}}}}, "delete": {"tags": ["rules-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_20", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/maestro/taskupdateconfig": {"get": {"tags": ["task-update-config-controller"], "summary": "list", "operationId": "listUsingGET_23", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TaskUpdateConfig"}}}}}, "post": {"tags": ["task-update-config-controller"], "summary": "create", "operationId": "createUsingPOST_23", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TaskUpdateConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TaskUpdateConfig"}}}}}, "/api/v1/maestro/taskupdateconfig/mulitquery": {"get": {"tags": ["task-update-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_23", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TaskUpdateConfig"}}}}}}, "/api/v1/maestro/taskupdateconfig/{column_name}/{column_value}": {"get": {"tags": ["task-update-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_23", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TaskUpdateConfig"}}}}}, "delete": {"tags": ["task-update-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_23", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/maestro/taskupdateconfig/{id}": {"put": {"tags": ["task-update-config-controller"], "summary": "update", "operationId": "updateUsingPUT_23", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TaskUpdateConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TaskUpdateConfig"}}}}, "delete": {"tags": ["task-update-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_23", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/maestro/ticketcreationconfig": {"get": {"tags": ["ticket-creation-config-controller"], "summary": "list", "operationId": "listUsingGET_34", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TicketCreationConfig"}}}}}, "post": {"tags": ["ticket-creation-config-controller"], "summary": "create", "operationId": "createUsingPOST_34", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TicketCreationConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TicketCreationConfig"}}}}}, "/api/v1/maestro/ticketcreationconfig/mulitquery": {"get": {"tags": ["ticket-creation-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_34", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TicketCreationConfig"}}}}}}, "/api/v1/maestro/ticketcreationconfig/saveall": {"post": {"tags": ["ticket-creation-config-controller"], "summary": "saveAllData", "operationId": "saveAllDataUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "ticketCreation", "description": "ticketCreation", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/TicketCreationConfig"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TicketCreationConfig"}}}}}}, "/api/v1/maestro/ticketcreationconfig/{column_name}/{column_value}": {"get": {"tags": ["ticket-creation-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_34", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TicketCreationConfig"}}}}}, "delete": {"tags": ["ticket-creation-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_34", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/maestro/ticketcreationconfig/{id}": {"put": {"tags": ["ticket-creation-config-controller"], "summary": "update", "operationId": "updateUsingPUT_34", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TicketCreationConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TicketCreationConfig"}}}}, "delete": {"tags": ["ticket-creation-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_34", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/orchestrator/phase": {"get": {"tags": ["phase-controller"], "summary": "list", "operationId": "listUsingGET_15", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/Phase"}}}}}, "post": {"tags": ["phase-controller"], "summary": "create", "operationId": "createUsingPOST_15", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/Phase"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Phase"}}}}}, "/api/v1/orchestrator/phase/mulitquery": {"get": {"tags": ["phase-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_15", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/Phase"}}}}}}, "/api/v1/orchestrator/phase/{column_name}/{column_value}": {"get": {"tags": ["phase-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_15", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/Phase"}}}}}, "delete": {"tags": ["phase-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_15", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/orchestrator/phase/{id}": {"put": {"tags": ["phase-controller"], "summary": "update", "operationId": "updateUsingPUT_15", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/Phase"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Phase"}}}}, "delete": {"tags": ["phase-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_15", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbainquiryconfig": {"get": {"tags": ["tba-inquiry-config-controller"], "summary": "list", "operationId": "listUsingGET_24", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaInquiryConfig"}}}}}, "post": {"tags": ["tba-inquiry-config-controller"], "summary": "create", "operationId": "createUsingPOST_24", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaInquiryConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaInquiryConfig"}}}}}, "/api/v1/tba/tbainquiryconfig/create": {"post": {"tags": ["tba-inquiry-config-controller"], "summary": "saveTbainquiryConfig", "operationId": "saveTbainquiryConfigUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaInquiryConfigDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaInquiryConfig"}}}}}, "/api/v1/tba/tbainquiryconfig/mulitquery": {"get": {"tags": ["tba-inquiry-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_24", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaInquiryConfig"}}}}}}, "/api/v1/tba/tbainquiryconfig/{column_name}/{column_value}": {"get": {"tags": ["tba-inquiry-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_24", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaInquiryConfig"}}}}}, "delete": {"tags": ["tba-inquiry-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_24", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbainquiryconfig/{id}": {"put": {"tags": ["tba-inquiry-config-controller"], "summary": "update", "operationId": "updateUsingPUT_24", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaInquiryConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaInquiryConfig"}}}}, "delete": {"tags": ["tba-inquiry-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_24", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbainquiryjsonkey": {"get": {"tags": ["tba-inquiry-json-key-controller"], "summary": "list", "operationId": "listUsingGET_25", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaInquiryJsonKey"}}}}}, "post": {"tags": ["tba-inquiry-json-key-controller"], "summary": "create", "operationId": "createUsingPOST_25", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaInquiryJsonKey"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaInquiryJsonKey"}}}}}, "/api/v1/tba/tbainquiryjsonkey/mulitquery": {"get": {"tags": ["tba-inquiry-json-key-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_25", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaInquiryJsonKey"}}}}}}, "/api/v1/tba/tbainquiryjsonkey/{column_name}/{column_value}": {"get": {"tags": ["tba-inquiry-json-key-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_25", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaInquiryJsonKey"}}}}}, "delete": {"tags": ["tba-inquiry-json-key-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_25", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbainquiryjsonkey/{id}": {"put": {"tags": ["tba-inquiry-json-key-controller"], "summary": "update", "operationId": "updateUsingPUT_25", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaInquiryJsonKey"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaInquiryJsonKey"}}}}, "delete": {"tags": ["tba-inquiry-json-key-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_25", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbametadata": {"get": {"tags": ["tba-meta-data-controller"], "summary": "list", "operationId": "listUsingGET_27", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaMetaData"}}}}}, "post": {"tags": ["tba-meta-data-controller"], "summary": "create", "operationId": "createUsingPOST_27", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaMetaData"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaMetaData"}}}}}, "/api/v1/tba/tbametadata/mulitquery": {"get": {"tags": ["tba-meta-data-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_27", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaMetaData"}}}}}}, "/api/v1/tba/tbametadata/{column_name}/{column_value}": {"get": {"tags": ["tba-meta-data-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_27", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaMetaData"}}}}}, "delete": {"tags": ["tba-meta-data-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_27", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbametadata/{id}": {"put": {"tags": ["tba-meta-data-controller"], "summary": "update", "operationId": "updateUsingPUT_27", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaMetaData"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaMetaData"}}}}, "delete": {"tags": ["tba-meta-data-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_27", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbaprocessinquiry": {"get": {"tags": ["tba-process-inquiry-controller"], "summary": "list", "operationId": "listUsingGET_28", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaProcessInquiry"}}}}}, "post": {"tags": ["tba-process-inquiry-controller"], "summary": "create", "operationId": "createUsingPOST_28", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaProcessInquiry"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaProcessInquiry"}}}}}, "/api/v1/tba/tbaprocessinquiry/mulitquery": {"get": {"tags": ["tba-process-inquiry-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_28", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaProcessInquiry"}}}}}}, "/api/v1/tba/tbaprocessinquiry/{column_name}/{column_value}": {"get": {"tags": ["tba-process-inquiry-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_28", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaProcessInquiry"}}}}}, "delete": {"tags": ["tba-process-inquiry-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_28", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbaprocessinquiry/{id}": {"put": {"tags": ["tba-process-inquiry-controller"], "summary": "update", "operationId": "updateUsingPUT_28", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaProcessInquiry"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaProcessInquiry"}}}}, "delete": {"tags": ["tba-process-inquiry-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_28", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbaupdateactivity": {"get": {"tags": ["tba-update-activity-controller"], "summary": "list", "operationId": "listUsingGET_29", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateActivity"}}}}}, "post": {"tags": ["tba-update-activity-controller"], "summary": "create", "operationId": "createUsingPOST_29", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateActivity"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateActivity"}}}}}, "/api/v1/tba/tbaupdateactivity/mulitquery": {"get": {"tags": ["tba-update-activity-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_29", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateActivity"}}}}}}, "/api/v1/tba/tbaupdateactivity/{column_name}/{column_value}": {"get": {"tags": ["tba-update-activity-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_29", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateActivity"}}}}}, "delete": {"tags": ["tba-update-activity-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_29", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbaupdateactivity/{id}": {"put": {"tags": ["tba-update-activity-controller"], "summary": "update", "operationId": "updateUsingPUT_29", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateActivity"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateActivity"}}}}, "delete": {"tags": ["tba-update-activity-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_29", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbaupdateconfig": {"get": {"tags": ["tba-update-config-controller"], "summary": "list", "operationId": "listUsingGET_30", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateConfig"}}}}}, "post": {"tags": ["tba-update-config-controller"], "summary": "create", "operationId": "createUsingPOST_30", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateConfig"}}}}}, "/api/v1/tba/tbaupdateconfig/create": {"post": {"tags": ["tba-update-config-controller"], "summary": "saveUpdateconfig", "operationId": "saveUpdateconfigUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateConfigDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateConfig"}}}}}, "/api/v1/tba/tbaupdateconfig/mulitquery": {"get": {"tags": ["tba-update-config-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_30", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateConfig"}}}}}}, "/api/v1/tba/tbaupdateconfig/{column_name}/{column_value}": {"get": {"tags": ["tba-update-config-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_30", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateConfig"}}}}}, "delete": {"tags": ["tba-update-config-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_30", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbaupdateconfig/{id}": {"put": {"tags": ["tba-update-config-controller"], "summary": "update", "operationId": "updateUsingPUT_30", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateConfig"}}}}, "delete": {"tags": ["tba-update-config-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_30", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbaupdatejsonkey": {"get": {"tags": ["tba-update-json-key-controller"], "summary": "list", "operationId": "listUsingGET_31", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateJsonKey"}}}}}, "post": {"tags": ["tba-update-json-key-controller"], "summary": "create", "operationId": "createUsingPOST_31", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateJsonKey"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateJsonKey"}}}}}, "/api/v1/tba/tbaupdatejsonkey/mulitquery": {"get": {"tags": ["tba-update-json-key-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_31", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateJsonKey"}}}}}}, "/api/v1/tba/tbaupdatejsonkey/{column_name}/{column_value}": {"get": {"tags": ["tba-update-json-key-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_31", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateJsonKey"}}}}}, "delete": {"tags": ["tba-update-json-key-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_31", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbaupdatejsonkey/{id}": {"put": {"tags": ["tba-update-json-key-controller"], "summary": "update", "operationId": "updateUsingPUT_31", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateJsonKey"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateJsonKey"}}}}, "delete": {"tags": ["tba-update-json-key-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_31", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbaupdatemetadata": {"get": {"tags": ["tba-update-meta-data-controller"], "summary": "list", "operationId": "listUsingGET_32", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateMetaData"}}}}}, "post": {"tags": ["tba-update-meta-data-controller"], "summary": "create", "operationId": "createUsingPOST_32", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateMetaData"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateMetaData"}}}}}, "/api/v1/tba/tbaupdatemetadata/mulitquery": {"get": {"tags": ["tba-update-meta-data-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_32", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateMetaData"}}}}}}, "/api/v1/tba/tbaupdatemetadata/{column_name}/{column_value}": {"get": {"tags": ["tba-update-meta-data-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_32", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateMetaData"}}}}}, "delete": {"tags": ["tba-update-meta-data-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_32", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbaupdatemetadata/{id}": {"put": {"tags": ["tba-update-meta-data-controller"], "summary": "update", "operationId": "updateUsingPUT_32", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateMetaData"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateMetaData"}}}}, "delete": {"tags": ["tba-update-meta-data-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_32", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/api/v1/tba/tbaupdateprocess": {"get": {"tags": ["tba-update-process-controller"], "summary": "list", "operationId": "listUsingGET_33", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateProcess"}}}}}, "post": {"tags": ["tba-update-process-controller"], "summary": "create", "operationId": "createUsingPOST_33", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateProcess"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateProcess"}}}}}, "/api/v1/tba/tbaupdateprocess/mulitquery": {"get": {"tags": ["tba-update-process-controller"], "summary": "findByMultiColumnCondition", "operationId": "findByMultiColumnConditionUsingGET_33", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "query", "description": "column_name", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_condition", "in": "query", "description": "column_condition", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "column_value", "in": "query", "description": "column_value", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateProcess"}}}}}}, "/api/v1/tba/tbaupdateprocess/{column_name}/{column_value}": {"get": {"tags": ["tba-update-process-controller"], "summary": "findByColumn", "operationId": "findByColumnUsingGET_33", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/TbaUpdateProcess"}}}}}, "delete": {"tags": ["tba-update-process-controller"], "summary": "deleteByColumn", "operationId": "deleteByColumnUsingDELETE_33", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "column_name", "in": "path", "description": "column_name", "required": true, "type": "string"}, {"name": "column_value", "in": "path", "description": "column_value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}}}, "/api/v1/tba/tbaupdateprocess/{id}": {"put": {"tags": ["tba-update-process-controller"], "summary": "update", "operationId": "updateUsingPUT_33", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "entity", "description": "entity", "required": true, "schema": {"$ref": "#/definitions/TbaUpdateProcess"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TbaUpdateProcess"}}}}, "delete": {"tags": ["tba-update-process-controller"], "summary": "delete", "operationId": "deleteUsingDELETE_33", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/error": {"get": {"tags": ["basic-error-controller"], "summary": "errorHtml", "operationId": "errorHtmlUsingGET", "consumes": ["application/json"], "produces": ["text/html"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ModelAndView"}}}}, "head": {"tags": ["basic-error-controller"], "summary": "errorHtml", "operationId": "errorHtmlUsingHEAD", "consumes": ["application/json"], "produces": ["text/html"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ModelAndView"}}}}, "post": {"tags": ["basic-error-controller"], "summary": "errorHtml", "operationId": "errorHtmlUsingPOST", "consumes": ["application/json"], "produces": ["text/html"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ModelAndView"}}}}, "put": {"tags": ["basic-error-controller"], "summary": "errorHtml", "operationId": "errorHtmlUsingPUT", "consumes": ["application/json"], "produces": ["text/html"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ModelAndView"}}}}, "delete": {"tags": ["basic-error-controller"], "summary": "errorHtml", "operationId": "errorHtmlUsingDELETE", "consumes": ["application/json"], "produces": ["text/html"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ModelAndView"}}}}, "options": {"tags": ["basic-error-controller"], "summary": "errorHtml", "operationId": "errorHtmlUsingOPTIONS", "consumes": ["application/json"], "produces": ["text/html"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ModelAndView"}}}}, "patch": {"tags": ["basic-error-controller"], "summary": "errorHtml", "operationId": "errorHtmlUsingPATCH", "consumes": ["application/json"], "produces": ["text/html"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ModelAndView"}}}}}}, "definitions": {"KsdFileDetails": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "fileFormatType": {"type": "string"}, "fileName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "maxThreshold": {"type": "integer", "format": "int32"}, "minThreshold": {"type": "integer", "format": "int32"}, "sendr": {"type": "string"}, "subj": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}, "variation": {"type": "integer", "format": "int32"}}}, "LayoutConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "dateFormat": {"type": "string"}, "fieldNo": {"type": "integer", "format": "int32"}, "fieldTemplate": {"type": "string"}, "fieldType": {"type": "string"}, "fileName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "length": {"type": "integer", "format": "int32"}, "mandatory": {"type": "string"}, "mfFieldName": {"type": "string"}, "processJobMappingConfig": {"$ref": "#/definitions/ProcessJobMapping"}, "recordFormat": {"type": "string"}, "recordIdentifier": {"type": "string"}, "recordType": {"type": "string"}, "startPos": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "ProcessFeaturesMapping": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "Process": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "processName": {"type": "string"}, "processPhaseMappings": {"type": "array", "items": {"$ref": "#/definitions/ProcessPhaseMapping"}}, "processType": {"type": "string"}, "roleConfigList": {"type": "array", "items": {"$ref": "#/definitions/RoleConfig"}}}}, "NotificationMailConfig": {"type": "object", "properties": {"appendSubject": {"type": "string"}, "attachmentName": {"type": "string"}, "ccList": {"type": "string"}, "condition": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "interestedFields": {"type": "string"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "remarks": {"type": "string"}, "subject": {"type": "string"}, "toList": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaMetaData": {"type": "object", "properties": {"clientId": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "metaData": {"type": "string"}, "panelId": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "ProcessPhaseMapping": {"type": "object", "properties": {"createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "processphasemapping": {"$ref": "#/definitions/Phase"}, "sequence": {"type": "integer", "format": "int32"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "ProcessControlConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "application": {"type": "string"}, "correctiveAction": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "ruleName": {"type": "string"}, "tbaFieldName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "RulesDefinition": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "conversionType": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "fileName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "json": {"type": "string"}, "primaryFieldName": {"type": "string"}, "ruleName": {"type": "string"}, "rulesConfig": {"$ref": "#/definitions/RulesConfig"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}, "validationType": {"$ref": "#/definitions/ValidationType"}}}, "AllClientsJobs": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "childJobName": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "ksdMasterConfig": {"$ref": "#/definitions/KsdMasterConfig"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "CustomClientDetailsBO": {"type": "object", "properties": {"buOpsId": {"type": "integer", "format": "int64"}, "clientCode": {"type": "string"}, "clientId": {"type": "integer", "format": "int64"}, "clientName": {"type": "string"}, "opsCode": {"type": "string"}, "opsName": {"type": "string"}}}, "TbaUpdateConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "activityId": {"type": "integer", "format": "int32"}, "baseKey": {"type": "string"}, "classId": {"type": "integer", "format": "int32"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "eventName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "jsonKey": {"type": "string"}, "metadata": {"type": "string"}, "panelId": {"type": "integer", "format": "int32"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "subKey": {"type": "string"}, "tbaFieldName": {"type": "string"}, "transId": {"type": "integer", "format": "int32"}, "updateName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}, "value": {"type": "string"}}}, "HolidayCalendar": {"type": "object", "properties": {"clientDetails": {"$ref": "#/definitions/ClientDetails"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "nonBusinessDays": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "CustomProcessBO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "processName": {"type": "string"}, "processType": {"type": "string"}}}, "JobSchedule": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "dayOfMonth": {"type": "integer", "format": "int32"}, "daysOfWeek": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "index": {"type": "string"}, "interval": {"type": "integer", "format": "int32"}, "month": {"type": "integer", "format": "int32"}, "timeZone": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "BusinessUnit": {"type": "object", "properties": {"businessUnitClients": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnitClient"}}, "businessUnitOps": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnitOps"}}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "roleConfigList": {"type": "array", "items": {"$ref": "#/definitions/RoleConfig"}}, "unitCode": {"type": "string"}, "unitName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "CustomProcessJobMappingBO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "jobName": {"type": "string"}}}, "Link": {"type": "object", "properties": {"href": {"type": "string"}, "templated": {"type": "boolean"}}}, "TaskUpdateConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "attachement": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "interestedFileds": {"type": "string"}, "maestroTaskName": {"type": "string"}, "newDiscussion": {"type": "string"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaUpdateJsonKey": {"type": "object", "properties": {"baseKey": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "jsonKey": {"type": "string"}, "panelId": {"type": "integer", "format": "int32"}, "subKey": {"type": "string"}, "tbaFieldName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TicketCreationConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "assignee": {"type": "string"}, "attachemet": {"type": "string"}, "billingNumDesc": {"type": "string"}, "billingNumber": {"type": "string"}, "businessArea": {"type": "string"}, "complexity": {"type": "string"}, "controlAccount": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "division": {"type": "string"}, "dueDays": {"type": "integer", "format": "int32"}, "estimatedWorkHours": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "interestedFields": {"type": "string"}, "interestedParties": {"type": "string"}, "iteration": {"type": "boolean"}, "newDiscussion": {"type": "string"}, "percentComplete": {"type": "string"}, "priority": {"type": "string"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "projectId": {"type": "string"}, "responsibleParty": {"type": "string"}, "sdlcDiscipline": {"type": "string"}, "serviceGroup": {"type": "string"}, "status": {"type": "string"}, "taskOwner": {"type": "string"}, "taskType": {"type": "string"}, "ticketType": {"type": "string"}, "title": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}, "workPackage": {"type": "string"}}}, "BusinessOps": {"type": "object", "properties": {"createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "opsCode": {"type": "string"}, "opsName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "Phase": {"type": "object", "properties": {"exclusive": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "phase": {"type": "integer", "format": "int32"}, "phaseName": {"type": "string"}, "pluginsInParallel": {"type": "integer", "format": "int32"}, "subPhaseInParallel": {"type": "integer", "format": "int32"}, "timeout": {"type": "integer", "format": "int64"}}}, "TbaUpdateConfigDto": {"type": "object", "properties": {"eventName": {"type": "string"}, "fieldName": {"type": "string"}, "identifier": {"type": "string"}, "panelId": {"type": "integer", "format": "int32"}, "phase": {"type": "integer", "format": "int64"}, "processJobMappingId": {"type": "integer", "format": "int64"}, "remarks": {"type": "string"}, "tbaFieldType": {"type": "string"}, "tbakey": {"type": "string"}}}, "TbaInquiryConfigDto": {"type": "object", "properties": {"identifier": {"type": "string"}, "inquiryName": {"type": "string"}, "mfFieldName": {"type": "string"}, "panelId": {"type": "integer", "format": "int32"}, "phase": {"type": "integer", "format": "int64"}, "processJobMappingId": {"type": "integer", "format": "int64"}, "remarks": {"type": "string"}, "tbaFieldType": {"type": "string"}, "tbakey": {"type": "string"}}}, "ModelAndView": {"type": "object", "properties": {"empty": {"type": "boolean"}, "model": {"type": "object"}, "modelMap": {"type": "object", "additionalProperties": {"type": "object"}}, "reference": {"type": "boolean"}, "status": {"type": "string", "enum": ["100 CONTINUE", "101 SWITCHING_PROTOCOLS", "102 PROCESSING", "103 CHECKPOINT", "200 OK", "201 CREATED", "202 ACCEPTED", "203 NON_AUTHORITATIVE_INFORMATION", "204 NO_CONTENT", "205 RESET_CONTENT", "206 PARTIAL_CONTENT", "207 MULTI_STATUS", "208 ALREADY_REPORTED", "226 IM_USED", "300 MULTIPLE_CHOICES", "301 MOVED_PERMANENTLY", "302 FOUND", "302 MOVED_TEMPORARILY", "303 SEE_OTHER", "304 NOT_MODIFIED", "305 USE_PROXY", "307 TEMPORARY_REDIRECT", "308 PERMANENT_REDIRECT", "400 BAD_REQUEST", "401 UNAUTHORIZED", "402 PAYMENT_REQUIRED", "403 FORBIDDEN", "404 NOT_FOUND", "405 METHOD_NOT_ALLOWED", "406 NOT_ACCEPTABLE", "407 PROXY_AUTHENTICATION_REQUIRED", "408 REQUEST_TIMEOUT", "409 CONFLICT", "410 GONE", "411 LENGTH_REQUIRED", "412 PRECONDITION_FAILED", "413 PAYLOAD_TOO_LARGE", "413 REQUEST_ENTITY_TOO_LARGE", "414 URI_TOO_LONG", "414 REQUEST_URI_TOO_LONG", "415 UNSUPPORTED_MEDIA_TYPE", "416 REQUESTED_RANGE_NOT_SATISFIABLE", "417 EXPECTATION_FAILED", "418 I_AM_A_TEAPOT", "419 INSUFFICIENT_SPACE_ON_RESOURCE", "420 METHOD_FAILURE", "421 DESTINATION_LOCKED", "422 UNPROCESSABLE_ENTITY", "423 LOCKED", "424 FAILED_DEPENDENCY", "426 UPGRADE_REQUIRED", "428 PRECONDITION_REQUIRED", "429 TOO_MANY_REQUESTS", "431 REQUEST_HEADER_FIELDS_TOO_LARGE", "451 UNAVAILABLE_FOR_LEGAL_REASONS", "500 INTERNAL_SERVER_ERROR", "501 NOT_IMPLEMENTED", "502 BAD_GATEWAY", "503 SERVICE_UNAVAILABLE", "504 GATEWAY_TIMEOUT", "505 HTTP_VERSION_NOT_SUPPORTED", "506 VARIANT_ALSO_NEGOTIATES", "507 INSUFFICIENT_STORAGE", "508 LOOP_DETECTED", "509 BANDWIDTH_LIMIT_EXCEEDED", "510 NOT_EXTENDED", "511 NETWORK_AUTHENTICATION_REQUIRED"]}, "view": {"$ref": "#/definitions/View"}, "viewName": {"type": "string"}}}, "ErrorTicketTypeConfig": {"type": "object", "properties": {"createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "ticketType": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "ProcessFeatureConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "businessOpsName": {"type": "string"}, "businessUnitName": {"type": "string"}, "clientCode": {"type": "string"}, "clientName": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "jobCopied": {"type": "string"}, "jobName": {"type": "string"}, "phaseNames": {"type": "string"}, "pjmIdCopied": {"type": "integer", "format": "int64"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "processName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaMatchConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "correctiveAction": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "fileName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "mfFieldName": {"type": "string"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "reportIdentifier": {"type": "string"}, "ruleName": {"type": "string"}, "tbaFieldName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaProcessInquiry": {"type": "object", "properties": {"createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "inquiryName": {"type": "string"}, "panelId": {"type": "string"}, "processName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaUpdateActivity": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int32"}, "basicInfo": {"type": "string"}, "classId": {"type": "integer", "format": "int32"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "panelId": {"type": "integer", "format": "int32"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaInquiryJsonKey": {"type": "object", "properties": {"createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "jsonKey": {"type": "string"}, "panelId": {"type": "integer", "format": "int32"}, "subJsonKey": {"type": "string"}, "tbaFieldName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaUpdateMetaData": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int32"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "metaData": {"type": "string"}, "panelId": {"type": "integer", "format": "int32"}, "transId": {"type": "integer", "format": "int32"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "BusinessUnitClient": {"type": "object", "properties": {"clientDetails": {"$ref": "#/definitions/ClientDetails"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "ClientDetails": {"type": "object", "properties": {"businessUnitClients": {"type": "array", "items": {"$ref": "#/definitions/BusinessUnitClient"}}, "clientCode": {"type": "string"}, "clientName": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "roleConfigList": {"type": "array", "items": {"$ref": "#/definitions/RoleConfig"}}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "RoleConfig": {"type": "object", "properties": {"active": {"type": "boolean"}, "adid": {"type": "string"}, "businessOps": {"$ref": "#/definitions/BusinessOps"}, "id": {"type": "integer", "format": "int64"}, "ownedBy": {"type": "string"}, "process": {"$ref": "#/definitions/Process"}, "role": {"type": "string"}, "type": {"type": "string"}}}, "CustomBusinessUnitBO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "unitCode": {"type": "string"}, "unitName": {"type": "string"}}}, "BusinessUnitOps": {"type": "object", "properties": {"businessOps": {"$ref": "#/definitions/BusinessOps"}, "businessUnit": {"$ref": "#/definitions/BusinessUnit"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "RulesConfig": {"type": "object", "properties": {"active": {"type": "string"}, "activeFlag": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "rulesDefinitions": {"type": "array", "items": {"$ref": "#/definitions/RulesDefinition"}}, "startDate": {"type": "string", "format": "date"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "MatchConditions": {"type": "object", "properties": {"conditionName": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "DroolFileDetails": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "clientCode": {"type": "integer", "format": "int32"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "droolContent": {"type": "array", "items": {"type": "string", "format": "byte"}}, "id": {"type": "integer", "format": "int64"}, "keytName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "ValidationType": {"type": "object", "properties": {"createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}, "valTypeCode": {"type": "string"}, "valTypeName": {"type": "string"}}}, "Map«string,Link»": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Link"}}, "ProcessJobMapping": {"type": "object", "properties": {"businessUnitOps": {"$ref": "#/definitions/BusinessUnitOps"}, "clientDetails": {"$ref": "#/definitions/ClientDetails"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "jobName": {"type": "string"}, "process": {"$ref": "#/definitions/Process"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "View": {"type": "object", "properties": {"contentType": {"type": "string"}}}, "Date": {"type": "object", "properties": {"date": {"type": "integer", "format": "int32"}, "day": {"type": "integer", "format": "int32"}, "hours": {"type": "integer", "format": "int32"}, "minutes": {"type": "integer", "format": "int32"}, "month": {"type": "integer", "format": "int32"}, "seconds": {"type": "integer", "format": "int32"}, "time": {"type": "integer", "format": "int64"}, "timezoneOffset": {"type": "integer", "format": "int32"}, "year": {"type": "integer", "format": "int32"}}}, "KsdMasterConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "allClientsJobs": {"type": "array", "items": {"$ref": "#/definitions/AllClientsJobs"}}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "dailyTaskReportSubjectNameOutLook": {"type": "string"}, "eftName": {"type": "string"}, "frequency": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "jobCutOffTime": {"type": "string"}, "jobScheduleTime": {"type": "string"}, "jobSchedules": {"type": "array", "items": {"$ref": "#/definitions/JobSchedule"}}, "jobStream": {"type": "string"}, "ksdFileDetails": {"type": "array", "items": {"$ref": "#/definitions/KsdFileDetails"}}, "maestroTaskName": {"type": "string"}, "primaryJobName": {"type": "string"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "turnaroundTime": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "NotificationReportConfig": {"type": "object", "properties": {"appendSubject": {"type": "string"}, "application": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "file": {"type": "string"}, "fileType": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "path": {"type": "string"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "remarks": {"type": "string"}, "reportName": {"type": "string"}, "subject": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaUpdateProcess": {"type": "object", "properties": {"activityId": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "eventName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "panelId": {"type": "string"}, "processName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}, "TbaInquiryConfig": {"type": "object", "properties": {"activeFlag": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string", "format": "date"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "inquiryName": {"type": "string"}, "jsonKey": {"type": "string"}, "metaData": {"type": "string"}, "panelId": {"type": "integer", "format": "int32"}, "processJobMapping": {"$ref": "#/definitions/ProcessJobMapping"}, "remarks": {"type": "string"}, "subJsonKey": {"type": "string"}, "tbaFieldName": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string", "format": "date"}}}}}