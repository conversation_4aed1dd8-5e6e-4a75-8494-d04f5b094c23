insert into configmanager.properties(application, profile , label ,key , value) values ('<PERSON><PERSON><PERSON>SAPPCONFIG-SERVICE', 'dev', 'latest','server.port' , '5555');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eureka.instance.appname' , 'HOLMESAPPCONFIG-SERVICE');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eureka.client.fetchRegistry' , 'true');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eureka.client.serviceUrl.defaultZone' , 'http://10.64.213.15:9001/eureka');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.businessUnit.URL' , 'http://10.64.213.15:7777/dbservice/api/v1/common/businessunit/byAlightId');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.client.businessOps.URL' , 'http://10.64.213.15:7777/dbservice/api/v1/common/businessunit/getDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.process.URL' , 'http://10.64.213.15:7777/dbservice/api/v1/common/businessunit/getRequiredDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.Job.URL' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','process.config.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.config.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processfeatureconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.ksdMaster.path' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdMaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','update.ksdconfig.path' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','fileformatter' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksdFileDetails' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','maestrotsk' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','maestrotkt' , 'taskUpdateConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','notification' , 'notificationMailConfig,notificationReportConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrl' , 'http://10.64.213.15:7777/dbservice/api/v1/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','layoutConfig' , 'layoutrule/layoutconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','mimictron' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','filevalidator' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaMatchConfig' , 'common/tbamatchconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processcontrol' , 'tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.business.rule.getByPjmId.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','sourcematch' , 'tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaInquiryConfig' , 'tba/tbainquiryconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaUpdateConfig' , 'tba/tbaupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','rulesConfig' , 'layoutrule/rulesconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControlConfig' , 'common/processcontrolconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','taskUpdateConfig' , 'maestro/taskupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','notificationMailConfig' , 'common/notificationmailconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','notificationReportConfig' , 'common/notificationReportConfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getReporteetypeBymanager' , 'http://10.64.213.15:7777/dbservice/api/v1/common/reporteeType/findRecords/manager_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessunitByName' , 'http://10.64.213.15:7777/dbservice/api/v1/common/businessunit/findRecords/unit_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getClientdetailsByName' , 'http://10.64.213.15:7777/dbservice/api/v1/common/clientdetails/findRecords/client_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessOpsByName' , 'http://10.64.213.15:7777/dbservice/api/v1/common/businessOps/findRecords/ops_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveroleconfig' , 'http://10.64.213.15:7777/dbservice/api/v1/common/roleconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getroleConfigByadid' , 'http://10.64.213.15:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','roleConfiglMultiColumn' , 'http://10.64.213.15:7777/dbservice/api/v1/common/roleconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveallroleconfig' , 'http://10.64.213.15:7777/dbservice/api/v1/common/roleconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessunitById' , 'http://10.64.213.15:7777/dbservice/api/v1/common/businessunit/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getClientdetailsById' , 'http://10.64.213.15:7777/dbservice/api/v1/common/clientdetails/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessOpsById' , 'http://10.64.213.15:7777/dbservice/api/v1/common/businessOps/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessTypeById' , 'http://10.64.213.15:7777/dbservice/api/v1/common/process/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessTypeByName' , 'http://10.64.213.15:7777/dbservice/api/v1/common/process/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getClientdetailsByClientCode' , 'http://10.64.213.15:7777/dbservice/api/v1/common/clientdetails/findRecords/client_code/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getHolidayCalendarById' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/client_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllClientdetails' , 'http://10.64.213.15:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','holidayCalendarPost' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/holiday/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','base.droolfiles.dir' , '../config/rules//hrisfileinterfaces');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','base.template.dir' , '../config/templates');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','redis.set.url' , 'http://10.64.213.15:7777/redis/rule_engine/set');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','redis.get.url' , 'http://10.64.213.15:7777/redis/rule_engine/get');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.getByPjmId.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.tbaFieldNameUri' , 'http://10.64.213.15:7777/dbservice/api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getProcessControlDataUri' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.mismatchUri' , 'http://10.64.213.15:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.createProcessControlUri' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processcontrolconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getApplicationUri' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getLayoutConfigUri' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.ruleNameUri' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.drool.file' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/droolFileDetails/key_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getProcessControlMultiColumn' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processcontrolconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','emailoperation' , 'ksdFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.transformation.rule.url' , 'http://10.64.213.15:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.transformationupdate.url' , 'http://10.64.213.15:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.delete.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.deleteAll.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.getByRuleConfigId.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layout.getByFileName' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/layoutconfig/file_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.saveAll.url' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/rulesconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.common.condition' , 'http://10.64.213.15:7777/dbservice/api/v1/common/matchconditions');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','maestroUpdateUri' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layout.getByFileName.detailRec' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layoutconfig.recType' , 'Detail%20Record');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrlMail' , 'http://10.64.213.15:7777/dbservice/api/v1/common/notificationmailconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrlReport' , 'http://10.64.213.15:7777/dbservice/api/v1/common/notificationReportConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.layout.post' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/layoutconfig/savelayoutData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.ksd.base.url' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.roleconfig.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/roleconfig/get/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.reportee.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/reporteeType/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.filterconfig.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processfeatureconfig/processFeatures');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.config.id.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','update.phase.names.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksdConfigandchilds.path' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksdFileDtl.path' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.client.process.pjmId' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaProcessInquiry' , 'api/v1/tba/tbaprocessinquiry/getInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaInquiryJsonKey' , 'api/v1/tba/tbainquiryjsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaMetadata' , 'api/v1/tba/tbametadata/getTbaMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTbainquiryconfig' , 'api/v1/tba/tbainquiryconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.ksdfiledeatils' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.drool.file.delete' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/droolFileDetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.drool.file.get' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/droolFileDetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.tba.getFileName' , 'http://10.64.213.15:7777/dbservice/api/v1/tba/tbainquiryconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllMaestroTaskUpdate' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getByColumnTaskUpdateConfig' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getMaestroTaskNameFrmKsdConfigDBUrl' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/ksdconfigmaestro');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteMaestroAppConfig' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/ticketcreationconfig/{id}');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllMaestroAppConfig' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getByColumnMaestroAppConfig' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlFileNameandField' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/layoutconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlTbaFieldForMatchTba' , 'http://10.64.213.15:7777/dbservice/api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrl.get.matchtbaconfig' , 'http://10.64.213.15:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrl.create.matchtba' , 'http://10.64.213.15:7777/dbservice/api/v1/common/tbamatchconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.match.tba.delete.url' , 'http://10.64.213.15:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlMatchTbaForRule' , 'http://10.64.213.15:7777/dbservice/api/v1/common/rulesdefinition/getRuleName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchProcessFeatureConfig' , 'api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchKsdConfig' , 'api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTicketCreationConfig' , 'api/v1/maestro/ticketcreationconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTaskUpdateConfig' , 'api/v1/maestro/taskupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchNotificationReportConfig' , 'api/v1/common/notificationReportConfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchNotificationMailConfig' , 'api/v1/common/notificationmailconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchKsdFileDetails' , 'api/v1/ksdconfig/ksdfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.holidaycalendar' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','config.server.path' , '../templates/test.xlsx');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','createupdateMaestroTaskConfig' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/taskupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchLayoutConfig' , 'api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','createupdateMaestroTicketConfig' , 'http://10.64.213.15:7777/dbservice/api/v1/maestro/ticketcreationconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlMatchTbaFormismatch' , 'http://10.64.213.15:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateMetadata' , 'api/v1/tba/tbaupdatemetadata/getTbaUpdateMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateProcess' , 'api/v1/tba/tbaupdateprocess/getUpdateInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateJsonKey' , 'api/v1/tba/tbaupdatejsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchRuleConfig' , 'api/v1/layoutrule/rulesconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaMatchConfig' , 'api/v1/common/tbamatchconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchProcessControlConfig' , 'api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getRoleConfig' , 'api/v1/common/roleconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','filevalidation' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','url', 'http://10.64.213.15:7777/dbservice/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateMaestroTaskNameKsdConfigDBUrl' , 'http://10.64.213.15:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/maestroTaskModify');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.EFT.URL' , 'api/v1/common/processjobmapping/getEftNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.KsdMaster.multiquery' , 'api/v1/ksdconfig/ksdMaster/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.businessOps' , '/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getRoleConfigPJMID' , 'api/v1/common/processjobmapping/getPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processfeatureconfig.get.path' , 'http://10.64.213.15:7777/dbservice/api/v1/common/processfeatureconfig/updateprocessconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.recIdentifier' , 'http://10.64.213.15:7777/dbservice/api/v1/layoutrule/layoutIdentifier');