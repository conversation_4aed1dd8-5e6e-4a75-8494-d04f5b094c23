-- dev profile

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','server.port' , '5555');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eureka.instance.appname' , 'HOLMESAPPCONFIG-SERVICE');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eureka.client.fetchRegistry' , 'true');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eureka.client.serviceUrl.defaultZone' , 'http://holmes-eureka-service:9001/eureka');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.businessUnit.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/byAlightId');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.client.businessOps.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.process.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getRequiredDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.Job.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','process.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.ksdMaster.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','update.ksdconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','fileformatter' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksdFileDetails' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','maestrotsk' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','maestrotkt' , 'taskUpdateConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','notification' , 'notificationMailConfig,notificationReportConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','layoutConfig' , 'layoutrule/layoutconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','mimictron' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','filevalidator' , 'layoutConfig,processLog');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaMatchConfig' , 'common/tbamatchconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processcontrol' , 'tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.business.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','sourcematch' , 'tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig,tbaEventHistInqConfig,tbaNoticeInqConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaInquiryConfig' , 'tba/tbainquiryconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaUpdateConfig' , 'tba/tbaupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','rulesConfig' , 'layoutrule/rulesconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControlConfig' , 'common/processcontrolconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','taskUpdateConfig' , 'maestro/taskupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','notificationMailConfig' , 'common/notificationmailconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','notificationReportConfig' , 'common/notificationReportConfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getReporteetypeBymanager' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/manager_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessunitByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/unit_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getClientdetailsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessOpsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/ops_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getroleConfigByadid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','roleConfiglMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveallroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessunitById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getClientdetailsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessOpsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessTypeById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessTypeByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getClientdetailsByClientCode' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_code/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getHolidayCalendarById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/client_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllClientdetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','holidayCalendarPost' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','base.droolfiles.dir' , '../config/rules/hrisfileinterfaces');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','base.template.dir' , '../config/templates');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','redis.set.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/set');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','redis.get.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/get');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.tbaFieldNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getProcessControlDataUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.mismatchUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.createProcessControlUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getApplicationUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getLayoutConfigUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.ruleNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.drool.file' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.getProcessControlMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','emailoperation' , 'ksdFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.transformation.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.transformationupdate.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.deleteAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.getByRuleConfigId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layout.getByFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/file_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.rule.saveAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.common.condition' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/matchconditions');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','maestroUpdateUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layout.getByFileName.detailRec' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layoutconfig.recType' , 'Detail%20Record');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.layout.post' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/savelayoutData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.ksd.base.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.roleconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/get/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.reportee.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.filterconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/processFeatures');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.config.id.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','update.phase.names.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksdConfigandchilds.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksdFileDtl.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.client.process.pjmId' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaProcessInquiry' , 'api/v1/tba/tbaprocessinquiry/getInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaInquiryJsonKey' , 'api/v1/tba/tbainquiryjsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaMetadata' , 'api/v1/tba/tbametadata/getTbaMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTbainquiryconfig' , 'api/v1/tba/tbainquiryconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.ksdfiledeatils' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.drool.file.delete' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.drool.file.get' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/key_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.tba.getFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllMaestroTaskUpdate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getByColumnTaskUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getMaestroTaskNameFrmKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/ksdconfigmaestro');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/{id}');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getByColumnMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlFileNameandField' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlTbaFieldForMatchTba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrl.get.matchtbaconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbUrl.create.matchtba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.match.tba.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlMatchTbaForRule' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/rulesdefinition/getRuleName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchProcessFeatureConfig' , 'api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchKsdConfig' , 'api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTicketCreationConfig' , 'api/v1/maestro/ticketcreationconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTaskUpdateConfig' , 'api/v1/maestro/taskupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchNotificationReportConfig' , 'api/v1/common/notificationReportConfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchNotificationMailConfig' , 'api/v1/common/notificationmailconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchKsdFileDetails' , 'api/v1/ksdconfig/ksdfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.holidaycalendar' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','config.server.path' , '../templates/test.xlsx');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','createupdateMaestroTaskConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchLayoutConfig' , 'api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','createupdateMaestroTicketConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','UrlMatchTbaFormismatch' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateMetadata' , 'api/v1/tba/tbaupdatemetadata/getTbaUpdateMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateProcess' , 'api/v1/tba/tbaupdateprocess/getUpdateInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateJsonKey' , 'api/v1/tba/tbaupdatejsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchRuleConfig' , 'api/v1/layoutrule/rulesconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaMatchConfig' , 'api/v1/common/tbamatchconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchProcessControlConfig' , 'api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getRoleConfig' , 'api/v1/common/roleconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','filevalidation' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','url', 'http://holmes-zuulproxygateway:7777/dbservice/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateMaestroTaskNameKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/maestroTaskModify');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.EFT.URL' , 'api/v1/common/processjobmapping/getEftNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.KsdMaster.multiquery' , 'api/v1/ksdconfig/ksdMaster/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.businessOps' , '/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getRoleConfigPJMID' , 'api/v1/common/processjobmapping/getPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','logging.config','logback-spring.xml');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processfeatureconfig.get.path','http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.recIdentifier' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutIdentifier');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','holidaycalendar.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getHolidayCalendarByDate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/holiday_date/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.clientDetails.fromRoleConfig' , 'api/v1/common/roleconfig/getCustomClient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.businessOps.fromRoleConfig' , 'api/v1/common/roleconfig/getBopsDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.process.fromRoleConfig' , 'api/v1/common/roleconfig/getProcessDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.opsObject.fromIDList' , 'api/v1/common/businessOps/getBusinessOpsByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.processObject.fromIDList' , 'api/v1/common/process/getProcessByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getClientdetailsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getBusinessOpsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessTypeRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','roleConfigByAdid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processjobmapping.jobname' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getReporteetypeAll' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllbusinessUnit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getbusinessOpsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitops/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getclientUnitUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitclient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','dbgetprocessUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/getprocessbo/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.pjmid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getPjmIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','delete.fromAllConfigs.byPJMID' , 'api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTypeFromUserDetails' , 'api/v1/common/userdetails/getType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getADIDWithoutAdmin' , 'api/v1/common/userdetails/getAdidWithoutAdmin');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getOnlyReporteeByManager' , 'api/v1/common/reporteeType/getReportees/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getRoleConfigByNullProcessAndOps' , 'api/v1/common/roleconfig/getRoleConfigByNullProcessAndOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getRoleConfigByNullProcess' , 'api/v1/common/roleconfig/getRoleConfigByNullProcess/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.businessOps','/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllAdidFromUserDetails' , 'api/v1/common/userdetails/getAllAdid');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layout.deletefile' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.recordsbyclientid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateConfigStatusDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.historyInquiry' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/eventhistinqconfig/process_job_mapping_id/');
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/' where key ='db.drool.file.delete';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getEventHistoryMasterByClientID' , 'api/v1/tba/eventhistorymaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eventHistInqConfig.submit' , 'api/v1/tba/eventhistinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','eventHistInqConfig.getAll' , 'api/v1/tba/eventhistinqconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaReRun.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaReRun.getTbaReRunData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaReRun.getEventHistoryMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaNotice.getNoticeMaesterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaNotice.getNoticeConfigData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaNotice.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','save.entity.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.duplicaterecords.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/checkForDuplicate/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbGetUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbGetUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbSaveUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbSaveUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbUpdateUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbUpdateUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbDeleteUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'dbDeleteUrlMail', 'http://holmes-zuulproxygateway:7777/api/v1/common/notificationmailconfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaEventHistInqConfig' , 'tba/eventhistinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaNoticeInqConfig' , 'tba/tbaNoticeinqconfig/process_job_mapping_id/');
update configmanager.properties set value ='tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig,tbaEventHistInqConfig,tbaNoticeInqConfig' where key ='sourcematch' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaEventHistInqConfig' , 'api/v1/tba/eventhistinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchTbaNoticeInqConfig' , 'api/v1/tba/tbaNoticeinqconfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','excelcalculator' , 'ksdOutputFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processLog' , 'common/processLog/findRecords/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTbaPendingEventConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTbaPendingEventConfigNew' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/newsaveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getPendingEventDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/mulitquery?'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getPendingEventMasterDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/getpendingenventmasterdata/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'db.get.commentInquiry', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'db.get.pendingEvent', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaComment.save', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tba.getCommentDataUri', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'processControl.getConditionName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ruledef/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','copyToAllConfigUrl' , 'api/v1/common/copyJobPFC/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getKsdMasterByJobAndEft' , 'api/v1/ksdconfig/ksdMaster/getKsdMasterDetails/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','globalUpdatePFC' , 'api/v1/common/processfeatureconfig/updateProcessFeatureConfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.KSD.URL' , 'api/v1/common/processjobmapping/getKsdNamesAndIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateActivity' , 'api/v1/tba/tbaupdateactivity/getTbaUpdateActivity/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.ksdfiledeatilsSaveAllEntities' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getActivityId' , '/api/v1/tba/tbaupdateprocess/getActivityId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaUpdateJsonKeys' , 'api/v1/tba/tbaupdatejsonkey/getTbaUpdateJsonKey/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateApproveConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyApprovedConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/getConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.phasenames.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.get.tbaUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/process_job_mapping_id/');
------- Added after 30 jul 2020
update configmanager.properties set value ='notificationMailConfig,notificationReportConfig,processLog' where key ='notification' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig' where key ='processcontrol' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='layoutConfig,processLog' where key ='filevalidation' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'tbaPendEventInqConfig', 'tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'tbaCommentsInqConfig', 'tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateParticipantRecordIdentifierDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/participantrecordidentifier/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getKsdNameList' , 'api/v1/common/processjobmapping/getksdName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessJobMapping' , 'api/v1/common/processjobmapping/getprocessJobMapping');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessFeature' , 'api/v1/common/processfeatureconfig/getprocessFeature');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getProcessJobMappingByKsdName' , 'api/v1/common/processjobmapping/getprocessJobMappingDetailsByKsd');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','hws.business.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBusinessHWSExcel');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','hwsbusiness.report.load.path' , '../config/dailyreports/hwsbusinessreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','hws.report.load.path' , '../config/dailyreports/hwsreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','hws.ksdconfig.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBossHWSExcel'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'db.output.getFileName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest', 'outputReport.getDataElementWoutSpace' ,'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','outputReport.getDataElement' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1//layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','batchKsdOutputFileDetails' , 'api/v1/layoutrule/ksdoutputfiledetails/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTemplateReportUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithoutReport');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/deleteFile/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','downloadTemplateUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/fileDownload/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/fileformatter/imputedreport/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/savetemplateData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getOutputLayoutsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTemplateForClientsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithallclients/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','orchKsdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
update configmanager.properties set value ='tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig,orchKsdOutputFileDetails' where key ='processcontrol' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value='layoutrule/ksdoutputfiledetails/loadEntityLazy/process_job_mapping_id/' where key='orchKsdOutputFileDetails' and application='HOLMESAPPCONFIG-SERVICE';
-- security implementation process config scripts
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getclientdetails' , '/api/v1/common/roleconfig/getclientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','get.pfcrecordsbyrole.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutput/');

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','orchKsdConfigByPJMID' , 'ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','orchKsdFileDetailsByPJMID' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','processControl.templateReportLayout' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/');

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','setRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/set');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/get');
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/newsaveAllEntities' where key ='saveTbaPendingEventConfig' and application='HOLMESAPPCONFIG-SERVICE';

--06/10/2020
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deletemultiplerows');

--13/10/2020
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getListOfSSN' , 'http://holmes-zuulproxygateway:7777/fileformatter/getlistofcolumn/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getTbaEditsMaster' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaeditsmaster/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','getAllData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbacommon/getallTbaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','updateTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/update/');

--15/10/202
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaUpdateMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaInquiryMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaInquiryMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteMatchTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/deletemultiplerows');
--20/10/2020

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaEventHistoryMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/eventhistinqconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaCommentMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaComment' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteTbaNoticeMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaNotice' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','editTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/saveIfNotDuplicate');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','editTbainquiryconfig' , 'api/v1/tba/tbainquiryconfig/saveIfNotDuplicate');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','saveIfNotDuplicateTbaPendingEventConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/saveIfNotDuplicate');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaReRun.update' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/saveIfNotDuplicate');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','tbaReRun.save' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/saveIfNotDuplicate');

update configmanager.properties set value ='layoutConfig,processLog,rulesConfig' where key ='filevalidator' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteRulesMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','deleteOutputReport' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutputdata/');
--Add New
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.dataBaseConfigMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/databaseconfig/databasedata');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.layout.deleteAllFiles' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/delete');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','sharedservice' , 'ksdFileDetails,orchKsdOutputFileDetails,notificationReportConfig,templateReportUpload');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','templateReportUpload' , 'common/template/loadEntityLazy/id/');
<<<<<<< HEAD

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','db.service.ksdfiledetailsNewSaveAllEntities' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails/newsaveAllEntities');

--06/11/2020
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/redis/cache/set' where key ='setRedisCache' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/findRecords/' where key ='tbaNotice.getNoticeMaesterData' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/' where key ='tbaReRun.getEventHistoryMasterData' and application='HOLMESAPPCONFIG-SERVICE';

=======
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'dev', 'latest','ksd.delete.allconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/deletebyfiletype');
>>>>>>> feature/release_fi_sep15
-- Test profile
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','server.port' , '5555');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','eureka.instance.appname' , 'HOLMESAPPCONFIG-SERVICE');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','eureka.client.fetchRegistry' , 'true');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','eureka.client.serviceUrl.defaultZone' , 'http://holmes-eureka-service:9001/eureka');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.businessUnit.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/byAlightId');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.client.businessOps.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.process.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getRequiredDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.Job.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','process.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.ksdMaster.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','update.ksdconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','fileformatter' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','ksdFileDetails' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','maestrotsk' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','maestrotkt' , 'taskUpdateConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','notification' , 'notificationMailConfig,notificationReportConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','dbUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','layoutConfig' , 'layoutrule/layoutconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','mimictron' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','filevalidator' , 'layoutConfig,processLog');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaMatchConfig' , 'common/tbamatchconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processcontrol' , 'tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.business.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','sourcematch' , 'tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig,tbaEventHistInqConfig,tbaNoticeInqConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaInquiryConfig' , 'tba/tbainquiryconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaUpdateConfig' , 'tba/tbaupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','rulesConfig' , 'layoutrule/rulesconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControlConfig' , 'common/processcontrolconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','taskUpdateConfig' , 'maestro/taskupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','notificationMailConfig' , 'common/notificationmailconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','notificationReportConfig' , 'common/notificationReportConfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getReporteetypeBymanager' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/manager_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getBusinessunitByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/unit_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getClientdetailsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getBusinessOpsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/ops_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getroleConfigByadid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','roleConfiglMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveallroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getBusinessunitById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getClientdetailsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getBusinessOpsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getProcessTypeById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getProcessTypeByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getClientdetailsByClientCode' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_code/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getHolidayCalendarById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/client_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllClientdetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','holidayCalendarPost' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','base.droolfiles.dir' , '../config/rules/hrisfileinterfaces');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','base.template.dir' , '../config/templates');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','redis.set.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/set');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','redis.get.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/get');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.tbaFieldNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.getProcessControlDataUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.mismatchUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.createProcessControlUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.getApplicationUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.getLayoutConfigUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.ruleNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.drool.file' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.getProcessControlMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','emailoperation' , 'ksdFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.transformation.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.transformationupdate.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.rule.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.rule.deleteAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.rule.getByRuleConfigId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.layout.getByFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/file_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.rule.saveAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.common.condition' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/matchconditions');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','maestroUpdateUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.layout.getByFileName.detailRec' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.layoutconfig.recType' , 'Detail%20Record');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.layout.post' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/savelayoutData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.ksd.base.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.roleconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/get/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.reportee.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.filterconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/processFeatures');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.config.id.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','update.phase.names.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','ksdConfigandchilds.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','ksdFileDtl.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.client.process.pjmId' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaProcessInquiry' , 'api/v1/tba/tbaprocessinquiry/getInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaInquiryJsonKey' , 'api/v1/tba/tbainquiryjsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaMetadata' , 'api/v1/tba/tbametadata/getTbaMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveTbainquiryconfig' , 'api/v1/tba/tbainquiryconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.ksdfiledeatils' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.drool.file.delete' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.drool.file.get' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/key_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.tba.getFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllMaestroTaskUpdate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getByColumnTaskUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getMaestroTaskNameFrmKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/ksdconfigmaestro');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/{id}');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getByColumnMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','UrlFileNameandField' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','UrlTbaFieldForMatchTba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','dbUrl.get.matchtbaconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','dbUrl.create.matchtba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.match.tba.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','UrlMatchTbaForRule' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/rulesdefinition/getRuleName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchProcessFeatureConfig' , 'api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchKsdConfig' , 'api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchTicketCreationConfig' , 'api/v1/maestro/ticketcreationconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchTaskUpdateConfig' , 'api/v1/maestro/taskupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchNotificationReportConfig' , 'api/v1/common/notificationReportConfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchNotificationMailConfig' , 'api/v1/common/notificationmailconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchKsdFileDetails' , 'api/v1/ksdconfig/ksdfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.holidaycalendar' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','config.server.path' , '../templates/test.xlsx');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','createupdateMaestroTaskConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchLayoutConfig' , 'api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','createupdateMaestroTicketConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','UrlMatchTbaFormismatch' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaUpdateMetadata' , 'api/v1/tba/tbaupdatemetadata/getTbaUpdateMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaUpdateProcess' , 'api/v1/tba/tbaupdateprocess/getUpdateInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaUpdateJsonKey' , 'api/v1/tba/tbaupdatejsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchRuleConfig' , 'api/v1/layoutrule/rulesconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchTbaMatchConfig' , 'api/v1/common/tbamatchconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchProcessControlConfig' , 'api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getRoleConfig' , 'api/v1/common/roleconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','filevalidation' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','url', 'http://holmes-zuulproxygateway:7777/dbservice/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateMaestroTaskNameKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/maestroTaskModify');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.EFT.URL' , 'api/v1/common/processjobmapping/getEftNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.KsdMaster.multiquery' , 'api/v1/ksdconfig/ksdMaster/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.businessOps' , '/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getRoleConfigPJMID' , 'api/v1/common/processjobmapping/getPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','logging.config','logback-spring.xml');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processfeatureconfig.get.path','http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.recIdentifier' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutIdentifier');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getClientdetailsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getBusinessOpsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getProcessTypeRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','roleConfigByAdid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processjobmapping.jobname' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getReporteetypeAll' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllbusinessUnit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getbusinessOpsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitops/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getclientUnitUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitclient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','dbgetprocessUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/getprocessbo/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','holidaycalendar.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getHolidayCalendarByDate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/holiday_date/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.clientDetails.fromRoleConfig' , 'api/v1/common/roleconfig/getCustomClient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.businessOps.fromRoleConfig' , 'api/v1/common/roleconfig/getBopsDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.process.fromRoleConfig' , 'api/v1/common/roleconfig/getProcessDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.opsObject.fromIDList' , 'api/v1/common/businessOps/getBusinessOpsByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.processObject.fromIDList' , 'api/v1/common/process/getProcessByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.pjmid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getPjmIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','delete.fromAllConfigs.byPJMID' , 'api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTypeFromUserDetails' , 'api/v1/common/userdetails/getType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getADIDWithoutAdmin' , 'api/v1/common/userdetails/getAdidWithoutAdmin');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getOnlyReporteeByManager' , 'api/v1/common/reporteeType/getReportees/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getRoleConfigByNullProcessAndOps' , 'api/v1/common/roleconfig/getRoleConfigByNullProcessAndOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getRoleConfigByNullProcess' , 'api/v1/common/roleconfig/getRoleConfigByNullProcess/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.businessOps','/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllAdidFromUserDetails' , 'api/v1/common/userdetails/getAllAdid');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.layout.deletefile' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.recordsbyclientid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateConfigStatusDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.historyInquiry' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/eventhistinqconfig/process_job_mapping_id/');
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/' where key ='db.drool.file.delete';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getEventHistoryMasterByClientID' , 'api/v1/tba/eventhistorymaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','eventHistInqConfig.submit' , 'api/v1/tba/eventhistinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','eventHistInqConfig.getAll' , 'api/v1/tba/eventhistinqconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaReRun.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaReRun.getTbaReRunData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaReRun.getEventHistoryMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaNotice.getNoticeMaesterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaNotice.getNoticeConfigData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaNotice.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','save.entity.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.duplicaterecords.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/checkForDuplicate/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbGetUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbGetUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbSaveUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbSaveUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbUpdateUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbUpdateUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbDeleteUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'dbDeleteUrlMail', 'http://holmes-zuulproxygateway:7777/api/v1/common/notificationmailconfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaEventHistInqConfig' , 'tba/eventhistinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaNoticeInqConfig' , 'tba/tbaNoticeinqconfig/process_job_mapping_id/');
update configmanager.properties set value ='tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig,tbaEventHistInqConfig,tbaNoticeInqConfig' where key ='sourcematch' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchTbaEventHistInqConfig' , 'api/v1/tba/eventhistinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchTbaNoticeInqConfig' , 'api/v1/tba/tbaNoticeinqconfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','ksdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','excelcalculator' , 'ksdOutputFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processLog' , 'common/processLog/findRecords/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveTbaPendingEventConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/saveAllEntities'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getPendingEventDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/mulitquery?'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getPendingEventMasterDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/getpendingenventmasterdata/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'db.get.commentInquiry', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'db.get.pendingEvent', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tbaComment.save', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','tba.getCommentDataUri', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'processControl.getConditionName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ruledef/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','copyToAllConfigUrl' , 'api/v1/common/copyJobPFC/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getKsdMasterByJobAndEft' , 'api/v1/ksdconfig/ksdMaster/getKsdMasterDetails/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','globalUpdatePFC' , 'api/v1/common/processfeatureconfig/updateProcessFeatureConfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.KSD.URL' , 'api/v1/common/processjobmapping/getKsdNamesAndIds/');
-- only for test env
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','test.prefix' , 'test');
------------------
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.ksdfiledeatilsSaveAllEntities' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaUpdateActivity' , 'api/v1/tba/tbaupdateactivity/getTbaUpdateActivity/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaUpdateJsonKeys' , 'api/v1/tba/tbaupdatejsonkey/getTbaUpdateJsonKey/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getActivityId' , '/api/v1/tba/tbaupdateprocess/getActivityId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateApproveConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyApprovedConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/getConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.phasenames.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.get.tbaUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/process_job_mapping_id/');
----- Added after 30 jul 2020
update configmanager.properties set value ='notificationMailConfig,notificationReportConfig,processLog' where key ='notification' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig' where key ='processcontrol' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='layoutConfig,processLog' where key ='filevalidation' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'tbaPendEventInqConfig', 'tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'tbaCommentsInqConfig', 'tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateParticipantRecordIdentifierDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/participantrecordidentifier/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getKsdNameList' , 'api/v1/common/processjobmapping/getksdName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getProcessJobMapping' , 'api/v1/common/processjobmapping/getprocessJobMapping');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getProcessFeature' , 'api/v1/common/processfeatureconfig/getprocessFeature');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getProcessJobMappingByKsdName' , 'api/v1/common/processjobmapping/getprocessJobMappingDetailsByKsd');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','hws.business.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBusinessHWSExcel');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','hwsbusiness.report.load.path' , '../config/dailyreports/hwsbusinessreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','hws.report.load.path' , '../config/dailyreports/hwsreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','hws.ksdconfig.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBossHWSExcel');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'db.output.getFileName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest', 'outputReport.getDataElementWoutSpace' ,'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','outputReport.getDataElement' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1//layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','batchKsdOutputFileDetails' , 'api/v1/layoutrule/ksdoutputfiledetails/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveTemplateReportUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithoutReport');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/deleteFile/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','downloadTemplateUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/fileDownload/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/fileformatter/imputedreport/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','saveTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/savetemplateData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getOutputLayoutsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTemplateForClientsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithallclients/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','orchKsdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
update configmanager.properties set value ='tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig,orchKsdOutputFileDetails' where key ='processcontrol' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value='layoutrule/ksdoutputfiledetails/loadEntityLazy/process_job_mapping_id/' where key='orchKsdOutputFileDetails' and application='HOLMESAPPCONFIG-SERVICE';


---security implementation process config scripts
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getclientdetails' , '/api/v1/common/roleconfig/getclientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','get.pfcrecordsbyrole.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutput/');

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','orchKsdConfigByPJMID' , 'ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','orchKsdFileDetailsByPJMID' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','processControl.templateReportLayout' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/');


insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','setRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/set/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/get');
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/newsaveAllEntities' where key ='saveTbaPendingEventConfig' and application='HOLMESAPPCONFIG-SERVICE';

--06/10/2020
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteTbaEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deletemultiplerows');

--13/10/2020
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getListOfSSN' , 'http://holmes-zuulproxygateway:7777/fileformatter/getlistofcolumn/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getTbaEditsMaster' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaeditsmaster/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','getAllData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbacommon/getallTbaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','updateTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/update/');

--15/10/202
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteTbaUpdateMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteTbaInquiryMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/deletemultiplerows');
update configmanager.properties set value ='layoutConfig,processLog,rulesConfig' where key ='filevalidator' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteRulesMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteMatchTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/deletemultiplerows');

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','deleteOutputReport' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutputdata/');
--Add New
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.dataBaseConfigMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/databaseconfig/databasedata');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.layout.deleteAllFiles' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/delete');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','sharedservice' , 'ksdFileDetails,orchKsdOutputFileDetails,notificationReportConfig,templateReportUpload');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','templateReportUpload' , 'common/template/loadEntityLazy/id/');
<<<<<<< HEAD

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','db.service.ksdfiledetailsNewSaveAllEntities' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails/newsaveAllEntities');

--06/11/2020
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/redis/cache/set' where key ='setRedisCache' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/findRecords/' where key ='tbaNotice.getNoticeMaesterData' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/' where key ='tbaReRun.getEventHistoryMasterData' and application='HOLMESAPPCONFIG-SERVICE';

=======
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'test', 'latest','ksd.delete.allconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/deletebyfiletype');
>>>>>>> feature/release_fi_sep15
-- QA profile

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','server.port' , '5555');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','eureka.instance.appname' , 'HOLMESAPPCONFIG-SERVICE');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','eureka.client.fetchRegistry' , 'true');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','eureka.client.serviceUrl.defaultZone' , 'http://holmes-eureka-service:9001/eureka');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.businessUnit.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/byAlightId');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.client.businessOps.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.process.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getRequiredDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.Job.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','process.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.ksdMaster.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','update.ksdconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','fileformatter' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','ksdFileDetails' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','maestrotsk' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','maestrotkt' , 'taskUpdateConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','notification' , 'notificationMailConfig,notificationReportConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','dbUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','layoutConfig' , 'layoutrule/layoutconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','mimictron' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','filevalidator' , 'layoutConfig,processLog');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaMatchConfig' , 'common/tbamatchconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processcontrol' , 'tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.business.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','sourcematch' , 'tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig,tbaEventHistInqConfig,tbaNoticeInqConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaInquiryConfig' , 'tba/tbainquiryconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaUpdateConfig' , 'tba/tbaupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','rulesConfig' , 'layoutrule/rulesconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControlConfig' , 'common/processcontrolconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','taskUpdateConfig' , 'maestro/taskupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','notificationMailConfig' , 'common/notificationmailconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','notificationReportConfig' , 'common/notificationReportConfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getReporteetypeBymanager' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/manager_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getBusinessunitByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/unit_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getClientdetailsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getBusinessOpsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/ops_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getroleConfigByadid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','roleConfiglMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveallroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getBusinessunitById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getClientdetailsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getBusinessOpsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getProcessTypeById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getProcessTypeByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getClientdetailsByClientCode' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_code/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getHolidayCalendarById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/client_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getAllClientdetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','holidayCalendarPost' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','base.droolfiles.dir' , '../config/rules/hrisfileinterfaces');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','base.template.dir' , '../config/templates');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','redis.set.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/set');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','redis.get.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/get');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.tbaFieldNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.getProcessControlDataUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.mismatchUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.createProcessControlUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.getApplicationUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.getLayoutConfigUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.ruleNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.drool.file' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.getProcessControlMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','emailoperation' , 'ksdFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.transformation.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.transformationupdate.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.rule.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.rule.deleteAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.rule.getByRuleConfigId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.layout.getByFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/file_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.rule.saveAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.common.condition' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/matchconditions');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','maestroUpdateUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.layout.getByFileName.detailRec' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.layoutconfig.recType' , 'Detail%20Record');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.layout.post' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/savelayoutData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.ksd.base.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.roleconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/get/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.reportee.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.filterconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/processFeatures');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.config.id.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','update.phase.names.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','ksdConfigandchilds.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','ksdFileDtl.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.client.process.pjmId' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getAllTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaProcessInquiry' , 'api/v1/tba/tbaprocessinquiry/getInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaInquiryJsonKey' , 'api/v1/tba/tbainquiryjsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaMetadata' , 'api/v1/tba/tbametadata/getTbaMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveTbainquiryconfig' , 'api/v1/tba/tbainquiryconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.ksdfiledeatils' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.drool.file.delete' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.drool.file.get' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/key_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.tba.getFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getAllMaestroTaskUpdate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getByColumnTaskUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getMaestroTaskNameFrmKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/ksdconfigmaestro');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/{id}');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getAllMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getByColumnMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','UrlFileNameandField' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','UrlTbaFieldForMatchTba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','dbUrl.get.matchtbaconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','dbUrl.create.matchtba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.match.tba.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','UrlMatchTbaForRule' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/rulesdefinition/getRuleName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchProcessFeatureConfig' , 'api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchKsdConfig' , 'api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchTicketCreationConfig' , 'api/v1/maestro/ticketcreationconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchTaskUpdateConfig' , 'api/v1/maestro/taskupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchNotificationReportConfig' , 'api/v1/common/notificationReportConfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchNotificationMailConfig' , 'api/v1/common/notificationmailconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchKsdFileDetails' , 'api/v1/ksdconfig/ksdfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.holidaycalendar' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','config.server.path' , '../templates/test.xlsx');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','createupdateMaestroTaskConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchLayoutConfig' , 'api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','createupdateMaestroTicketConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','UrlMatchTbaFormismatch' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getAllTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaUpdateMetadata' , 'api/v1/tba/tbaupdatemetadata/getTbaUpdateMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaUpdateProcess' , 'api/v1/tba/tbaupdateprocess/getUpdateInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaUpdateJsonKey' , 'api/v1/tba/tbaupdatejsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchRuleConfig' , 'api/v1/layoutrule/rulesconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchTbaMatchConfig' , 'api/v1/common/tbamatchconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchProcessControlConfig' , 'api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getRoleConfig' , 'api/v1/common/roleconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','filevalidation' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','url', 'http://holmes-zuulproxygateway:7777/dbservice/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateMaestroTaskNameKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/maestroTaskModify');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.EFT.URL' , 'api/v1/common/processjobmapping/getEftNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.KsdMaster.multiquery' , 'api/v1/ksdconfig/ksdMaster/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.businessOps' , '/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getRoleConfigPJMID' , 'api/v1/common/processjobmapping/getPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','logging.config','logback-spring.xml');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processfeatureconfig.get.path','http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.recIdentifier' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutIdentifier');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','holidaycalendar.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getHolidayCalendarByDate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/holiday_date/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.clientDetails.fromRoleConfig' , 'api/v1/common/roleconfig/getCustomClient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.businessOps.fromRoleConfig' , 'api/v1/common/roleconfig/getBopsDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.process.fromRoleConfig' , 'api/v1/common/roleconfig/getProcessDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.opsObject.fromIDList' , 'api/v1/common/businessOps/getBusinessOpsByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.processObject.fromIDList' , 'api/v1/common/process/getProcessByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getClientdetailsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getBusinessOpsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getProcessTypeRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','roleConfigByAdid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processjobmapping.jobname' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getReporteetypeAll' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getAllbusinessUnit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getbusinessOpsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitops/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getclientUnitUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitclient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','dbgetprocessUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/getprocessbo/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.pjmid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getPjmIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','delete.fromAllConfigs.byPJMID' , 'api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTypeFromUserDetails' , 'api/v1/common/userdetails/getType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getADIDWithoutAdmin' , 'api/v1/common/userdetails/getAdidWithoutAdmin');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getOnlyReporteeByManager' , 'api/v1/common/reporteeType/getReportees/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getRoleConfigByNullProcessAndOps' , 'api/v1/common/roleconfig/getRoleConfigByNullProcessAndOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getRoleConfigByNullProcess' , 'api/v1/common/roleconfig/getRoleConfigByNullProcess/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.businessOps','/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getAllAdidFromUserDetails' , 'api/v1/common/userdetails/getAllAdid');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.layout.deletefile' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.recordsbyclientid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateConfigStatusDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.historyInquiry' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/eventhistinqconfig/process_job_mapping_id/');
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/' where key ='db.drool.file.delete';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getEventHistoryMasterByClientID' , 'api/v1/tba/eventhistorymaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','eventHistInqConfig.submit' , 'api/v1/tba/eventhistinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','eventHistInqConfig.getAll' , 'api/v1/tba/eventhistinqconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaReRun.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaReRun.getTbaReRunData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaReRun.getEventHistoryMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaNotice.getNoticeMaesterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaNotice.getNoticeConfigData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaNotice.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','save.entity.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.duplicaterecords.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/checkForDuplicate/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbGetUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbGetUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbSaveUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbSaveUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbUpdateUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbUpdateUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbDeleteUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'dbDeleteUrlMail', 'http://holmes-zuulproxygateway:7777/api/v1/common/notificationmailconfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaEventHistInqConfig' , 'tba/eventhistinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaNoticeInqConfig' , 'tba/tbaNoticeinqconfig/process_job_mapping_id/');
update configmanager.properties set value ='tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig,tbaEventHistInqConfig,tbaNoticeInqConfig' where key ='sourcematch' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchTbaEventHistInqConfig' , 'api/v1/tba/eventhistinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchTbaNoticeInqConfig' , 'api/v1/tba/tbaNoticeinqconfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','ksdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','excelcalculator' , 'ksdOutputFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processLog' , 'common/processLog/findRecords/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveTbaPendingEventConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/saveAllEntities'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getPendingEventDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/mulitquery?'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getPendingEventMasterDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/getpendingenventmasterdata/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.commentInquiry', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.pendingEvent', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tbaComment.save', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','tba.getCommentDataUri', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.getConditionName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ruledef/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','copyToAllConfigUrl' , 'api/v1/common/copyJobPFC/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getKsdMasterByJobAndEft' , 'api/v1/ksdconfig/ksdMaster/getKsdMasterDetails/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','globalUpdatePFC' , 'api/v1/common/processfeatureconfig/updateProcessFeatureConfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.KSD.URL' , 'api/v1/common/processjobmapping/getKsdNamesAndIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.ksdfiledeatilsSaveAllEntities' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaUpdateActivity' , 'api/v1/tba/tbaupdateactivity/getTbaUpdateActivity/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTbaUpdateJsonKeys' , 'api/v1/tba/tbaupdatejsonkey/getTbaUpdateJsonKey/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getActivityId' , '/api/v1/tba/tbaupdateprocess/getActivityId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateApproveConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyApprovedConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/getConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.phasenames.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.get.tbaUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutput/');


-------Added after 30 jul 2020---------
update configmanager.properties set value ='notificationMailConfig,notificationReportConfig,processLog' where key ='notification' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig' where key ='processcontrol' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='layoutConfig,processLog' where key ='filevalidation' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'tbaPendEventInqConfig', 'tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'tbaCommentsInqConfig', 'tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateParticipantRecordIdentifierDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/participantrecordidentifier/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getKsdNameList' , 'api/v1/common/processjobmapping/getksdName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getProcessJobMapping' , 'api/v1/common/processjobmapping/getprocessJobMapping');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getProcessFeature' , 'api/v1/common/processfeatureconfig/getprocessFeature');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','hws.business.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBusinessHWSExcel');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','hwsbusiness.report.load.path' , '../config/dailyreports/hwsbusinessreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','hws.report.load.path' , '../config/dailyreports/hwsreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getProcessJobMappingByKsdName' , 'api/v1/common/processjobmapping/getprocessJobMappingDetailsByKsd');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','hws.ksdconfig.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBossHWSExcel');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'db.output.getFileName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest', 'outputReport.getDataElementWoutSpace' ,'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','outputReport.getDataElement' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1//layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','batchKsdOutputFileDetails' , 'api/v1/layoutrule/ksdoutputfiledetails/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveTemplateReportUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','updateTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithoutReport');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/deleteFile/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','downloadTemplateUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/fileDownload/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/fileformatter/imputedreport/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','saveTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/savetemplateData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getOutputLayoutsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getTemplateForClientsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithallclients/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','orchKsdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
update configmanager.properties set value ='tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig,orchKsdOutputFileDetails' where key ='processcontrol' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value='layoutrule/ksdoutputfiledetails/loadEntityLazy/process_job_mapping_id/' where key='orchKsdOutputFileDetails' and application='HOLMESAPPCONFIG-SERVICE';


---security implementation process config scripts
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getclientdetails' , '/api/v1/common/roleconfig/getclientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','get.pfcrecordsbyrole.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','orchKsdConfigByPJMID' , 'ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','orchKsdFileDetailsByPJMID' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','processControl.templateReportLayout' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/');


insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','setRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/set/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','getRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/get');
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/newsaveAllEntities' where key ='saveTbaPendingEventConfig' and application='HOLMESAPPCONFIG-SERVICE';

--06/10/2020
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deletemultiplerows');

--15/10/202
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaUpdateMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaInquiryMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteMatchTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/deletemultiplerows');
--15/10/202
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaUpdateMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaInquiryMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/deletemultiplerows');
update configmanager.properties set value ='layoutConfig,processLog,rulesConfig' where key ='filevalidator' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteRulesMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteOutputReport' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutputdata/');
--Add New
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.dataBaseConfigMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/databaseconfig/databasedata');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.layout.deleteAllFiles' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/delete');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','sharedservice' , 'ksdFileDetails,orchKsdOutputFileDetails,notificationReportConfig,templateReportUpload');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','templateReportUpload' , 'common/template/loadEntityLazy/id/');
<<<<<<< HEAD

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','db.service.ksdfiledetailsNewSaveAllEntities' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails/newsaveAllEntities');

--06/11/2020
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/redis/cache/set' where key ='setRedisCache' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/findRecords/' where key ='tbaNotice.getNoticeMaesterData' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/' where key ='tbaReRun.getEventHistoryMasterData' and application='HOLMESAPPCONFIG-SERVICE';


=======
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','ksd.delete.allconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/deletebyfiletype');
>>>>>>> feature/release_fi_sep15
---------------- QC profile------------------
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','server.port' , '5555');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','eureka.instance.appname' , 'HOLMESAPPCONFIG-SERVICE');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','eureka.client.fetchRegistry' , 'true');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','eureka.client.serviceUrl.defaultZone' , 'http://holmes-eureka-service:9001/eureka');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.businessUnit.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/byAlightId');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.client.businessOps.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.process.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/getRequiredDetailsBy');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.Job.URL' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','process.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.config.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.ksdMaster.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','update.ksdconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','fileformatter' , 'layoutConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','ksdFileDetails' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','maestrotsk' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','maestrotkt' , 'taskUpdateConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','notification' , 'notificationMailConfig,notificationReportConfig,processLog');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','dbUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','layoutConfig' , 'layoutrule/layoutconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','mimictron' , '');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','filevalidator' , 'layoutConfig,processLog');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaMatchConfig' , 'common/tbamatchconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processcontrol' , 'tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.business.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','sourcematch' , 'tbaUpdateConfig,rulesConfig,tbaMatchConfig,tbaInquiryConfig,layoutConfig,tbaEventHistInqConfig,tbaNoticeInqConfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaInquiryConfig' , 'tba/tbainquiryconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaUpdateConfig' , 'tba/tbaupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','rulesConfig' , 'layoutrule/rulesconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControlConfig' , 'common/processcontrolconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','taskUpdateConfig' , 'maestro/taskupdateconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','notificationMailConfig' , 'common/notificationmailconfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','notificationReportConfig' , 'common/notificationReportConfig/loadEntityLazy/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getReporteetypeBymanager' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/manager_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getBusinessunitByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/unit_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getClientdetailsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getBusinessOpsByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/ops_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','saveroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getroleConfigByadid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','roleConfiglMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','saveallroleconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getBusinessunitById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getClientdetailsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getBusinessOpsById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getProcessTypeById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/findRecords/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getProcessTypeByName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getClientdetailsByClientCode' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/findRecords/client_code/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getHolidayCalendarById' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/client_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getAllClientdetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','holidayCalendarPost' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','base.droolfiles.dir' , '../config/rules/hrisfileinterfaces');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','base.template.dir' , '../config/templates');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','redis.set.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/set');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','redis.get.url' , 'http://holmes-zuulproxygateway:7777/redis/rule_engine/get');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.rule.getByPjmId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.tbaFieldNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.getProcessControlDataUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.mismatchUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.createProcessControlUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.getApplicationUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.getLayoutConfigUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.ruleNameUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.drool.file' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.getProcessControlMultiColumn' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processcontrolconfig/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','emailoperation' , 'ksdFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.transformation.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.transformationupdate.url' , 'http://holmes-zuulproxygateway:7777/dbservice/createtransformationrules');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.service.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.rule.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.rule.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.rule.deleteAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.rule.getByRuleConfigId.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.layout.getByFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/file_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.rule.saveAll.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.common.condition' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/matchconditions');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','maestroUpdateUri' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.layout.getByFileName.detailRec' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.layoutconfig.recType' , 'Detail%20Record');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.service.layout.post' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig/savelayoutData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.service.ksd.base.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.roleconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/get/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.reportee.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.filterconfig.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/processFeatures');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.config.id.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','update.phase.names.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','ksdConfigandchilds.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','ksdFileDtl.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.client.process.pjmId' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getAllTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaProcessInquiry' , 'api/v1/tba/tbaprocessinquiry/getInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaInquiryJsonKey' , 'api/v1/tba/tbainquiryjsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaMetadata' , 'api/v1/tba/tbametadata/getTbaMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','saveTbainquiryconfig' , 'api/v1/tba/tbainquiryconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.service.ksdfiledeatils' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.drool.file.delete' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.drool.file.get' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/droolFileDetails/key_name/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.tba.getFileName' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getAllMaestroTaskUpdate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getByColumnTaskUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getMaestroTaskNameFrmKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/ksdconfigmaestro');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','deleteMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/{id}');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getAllMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getByColumnMaestroAppConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','UrlFileNameandField' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','UrlTbaFieldForMatchTba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','dbUrl.get.matchtbaconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','dbUrl.create.matchtba' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.match.tba.delete.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','UrlMatchTbaForRule' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/rulesdefinition/getRuleName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','updateTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','deleteTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchProcessFeatureConfig' , 'api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchKsdConfig' , 'api/v1/ksdconfig/ksdmasterconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchTicketCreationConfig' , 'api/v1/maestro/ticketcreationconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchTaskUpdateConfig' , 'api/v1/maestro/taskupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchNotificationReportConfig' , 'api/v1/common/notificationReportConfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchNotificationMailConfig' , 'api/v1/common/notificationmailconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchKsdFileDetails' , 'api/v1/ksdconfig/ksdfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.service.holidaycalendar' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','config.server.path' , '../templates/test.xlsx');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','createupdateMaestroTaskConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/taskupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchLayoutConfig' , 'api/v1/layoutrule/layoutconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','createupdateMaestroTicketConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/maestro/ticketcreationconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','UrlMatchTbaFormismatch' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','updateTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','deleteTbaInquiryConfig' , 'api/v1/tba/tbainquiryconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getAllTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaUpdateMetadata' , 'api/v1/tba/tbaupdatemetadata/getTbaUpdateMetaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaUpdateProcess' , 'api/v1/tba/tbaupdateprocess/getUpdateInquiryNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaUpdateJsonKey' , 'api/v1/tba/tbaupdatejsonkey/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','saveTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchTbaUpdateConfig' , 'api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchRuleConfig' , 'api/v1/layoutrule/rulesconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchTbaMatchConfig' , 'api/v1/common/tbamatchconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchProcessControlConfig' , 'api/v1/common/processcontrolconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getRoleConfig' , 'api/v1/common/roleconfig/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','filevalidation' , 'layoutConfig,processLog');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','url', 'http://holmes-zuulproxygateway:7777/dbservice/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','updateMaestroTaskNameKsdConfigDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdmasterconfig/maestroTaskModify');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.EFT.URL' , 'api/v1/common/processjobmapping/getEftNames/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.KsdMaster.multiquery' , 'api/v1/ksdconfig/ksdMaster/mulitquery');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.businessOps' , '/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getRoleConfigPJMID' , 'api/v1/common/processjobmapping/getPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','logging.config','logback-spring.xml');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processfeatureconfig.get.path','http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.service.recIdentifier' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/layoutIdentifier');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','holidaycalendar.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getHolidayCalendarByDate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/holiday/findRecords/holiday_date/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.clientDetails.fromRoleConfig' , 'api/v1/common/roleconfig/getCustomClient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.businessOps.fromRoleConfig' , 'api/v1/common/roleconfig/getBopsDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.process.fromRoleConfig' , 'api/v1/common/roleconfig/getProcessDetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.opsObject.fromIDList' , 'api/v1/common/businessOps/getBusinessOpsByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.processObject.fromIDList' , 'api/v1/common/process/getProcessByIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getClientdetailsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/clientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getBusinessOpsRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getProcessTypeRC' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/process/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','roleConfigByAdid' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/findRecords/adid/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processjobmapping.jobname' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/JobName/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getReporteetypeAll' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/reporteeType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getAllbusinessUnit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunit/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getbusinessOpsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitops/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getclientUnitUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/businessunitclient/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','dbgetprocessUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processjobmapping/getprocessbo/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.pjmid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getPjmIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','delete.fromAllConfigs.byPJMID' , 'api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTypeFromUserDetails' , 'api/v1/common/userdetails/getType/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getADIDWithoutAdmin' , 'api/v1/common/userdetails/getAdidWithoutAdmin');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getOnlyReporteeByManager' , 'api/v1/common/reporteeType/getReportees/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getRoleConfigByNullProcessAndOps' , 'api/v1/common/roleconfig/getRoleConfigByNullProcessAndOps/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getRoleConfigByNullProcess' , 'api/v1/common/roleconfig/getRoleConfigByNullProcess/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.businessOps','/api/v1/common/businessOps/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getAllAdidFromUserDetails' , 'api/v1/common/userdetails/getAllAdid');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.layout.deletefile' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/update/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.recordsbyclientid.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','updateConfigStatusDBUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.historyInquiry' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/eventhistinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getEventHistoryMasterByClientID' , 'api/v1/tba/eventhistorymaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','eventHistInqConfig.submit' , 'api/v1/tba/eventhistinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','eventHistInqConfig.getAll' , 'api/v1/tba/eventhistinqconfig');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaReRun.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaReRun.getTbaReRunData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaReRun.getEventHistoryMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaNotice.getNoticeMaesterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaNotice.getNoticeConfigData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaNotice.submit' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaNoticeinqconfig/create');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','save.entity.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.duplicaterecords.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/checkForDuplicate/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbGetUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbGetUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/getRowsByPjmId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbSaveUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbSaveUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/saveThisEntity');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbUpdateUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbUpdateUrlMail', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationmailconfig/optimise/performance/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbDeleteUrlReport', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/notificationReportConfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'dbDeleteUrlMail', 'http://holmes-zuulproxygateway:7777/api/v1/common/notificationmailconfig/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaEventHistInqConfig' , 'tba/eventhistinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaNoticeInqConfig' , 'tba/tbaNoticeinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchTbaEventHistInqConfig' , 'api/v1/tba/eventhistinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','batchTbaNoticeInqConfig' , 'api/v1/tba/tbaNoticeinqconfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','ksdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','excelcalculator' , 'ksdOutputFileDetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processLog' , 'common/processLog/findRecords/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','saveTbaPendingEventConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/saveAllEntities'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getPendingEventDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/mulitquery?'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getPendingEventMasterDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/getpendingenventmasterdata/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.commentInquiry', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.pendingEvent', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tbaComment.save', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','tba.getCommentDataUri', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbacommentinqconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','processControl.getConditionName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ruledef/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','copyToAllConfigUrl' , 'api/v1/common/copyJobPFC/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getKsdMasterByJobAndEft' , 'api/v1/ksdconfig/ksdMaster/getKsdMasterDetails/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','globalUpdatePFC' , 'api/v1/common/processfeatureconfig/updateProcessFeatureConfig/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.KSD.URL' , 'api/v1/common/processjobmapping/getKsdNamesAndIds/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.service.ksdfiledeatilsSaveAllEntities' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdfiledetails/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaUpdateActivity' , 'api/v1/tba/tbaupdateactivity/getTbaUpdateActivity/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getTbaUpdateJsonKeys' , 'api/v1/tba/tbaupdatejsonkey/getTbaUpdateJsonKey/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getActivityId' , '/api/v1/tba/tbaupdateprocess/getActivityId/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','updateApproveConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/modifyApprovedConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','getConfigStatusDBUrl', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/getConfigStatus');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','get.phasenames.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/processfeatureconfig/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest','db.get.tbaUpdateConfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/process_job_mapping_id/');
-------Added after 30 jul 2020---------
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'tbaPendEventInqConfig', 'tba/tbapendingevent/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc',, 'latest', 'tbaCommentsInqConfig', 'tba/tbacommentinqconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','saveKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','updateKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getKsdOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getOutPutFileDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','updateOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','updateParticipantRecordIdentifierDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/participantrecordidentifier/saveAllEntities');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getKsdNameList' , 'api/v1/common/processjobmapping/getksdName');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getProcessJobMapping' , 'api/v1/common/processjobmapping/getprocessJobMapping');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getProcessFeature' , 'api/v1/common/processfeatureconfig/getprocessFeature');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getProcessJobMappingByKsdName' , 'api/v1/common/processjobmapping/getprocessJobMappingDetailsByKsd');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','hws.business.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBusinessHWSExcel');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','hwsbusiness.report.load.path' , '../config/dailyreports/hwsbusinessreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','hws.report.load.path' , '../config/dailyreports/hwsreports/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','hws.ksdconfig.report.url' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/ksdconfig/ksdMaster/getBossHWSExcel');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest', 'db.output.getFileName', 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest', 'outputReport.getDataElementWoutSpace' ,'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/outputreport/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','outputReport.getDataElement' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1//layoutrule/ksdoutputfiledetails/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','batchKsdOutputFileDetails' , 'api/v1/layoutrule/ksdoutputfiledetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','saveTemplateReportUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','updateTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/saveall');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithoutReport');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','deleteTemplate' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/deleteFile/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','downloadTemplateUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/fileDownload/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/fileformatter/imputedreport/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','saveTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/savetemplateData');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getOutputLayoutsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/'); 
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getTemplateForClientsUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/template/getallwithallclients/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','orchKsdOutputFileDetails' , 'layoutrule/ksdoutputfiledetails/process_job_mapping_id/');
update configmanager.properties set value ='tbaInquiryConfig,tbaUpdateConfig,rulesConfig,processControlConfig,tbaMatchConfig,layoutConfig,tbaNoticeInqConfig,tbaEventHistInqConfig,tbaPendEventInqConfig,tbaCommentsInqConfig,orchKsdOutputFileDetails' where key ='processcontrol' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value='layoutrule/ksdoutputfiledetails/loadEntityLazy/process_job_mapping_id/' where key='orchKsdOutputFileDetails' and application='HOLMESAPPCONFIG-SERVICE';
---security implementation process config scripts
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getclientdetails' , '/api/v1/common/roleconfig/getclientdetails/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','get.pfcrecordsbyrole.path' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/roleconfig/getRowsByClientCode/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','deleteOutPutReportDetails' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutput/');

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','orchKsdConfigByPJMID' , 'ksdconfig/ksdmasterconfig/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','orchKsdFileDetailsByPJMID' , 'ksdconfig/ksdfiledetails/process_job_mapping_id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','processControl.templateReportLayout' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/getallTemplateReport/');


insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','setRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache');

insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','setRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/set/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getRedisCache' , 'http://holmes-zuulproxygateway:7777/redis/cache/get');
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/newsaveAllEntities' where key ='saveTbaPendingEventConfig' and application='HOLMESAPPCONFIG-SERVICE';

--06/10/2020
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','deleteTbaEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deleteRow/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','deleteTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbapendingevent/deletemultiplerows');

--13/10/2020
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getListOfSSN' , 'http://holmes-zuulproxygateway:7777/fileformatter/getlistofcolumn/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getTbaEditsMaster' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaeditsmaster/mulitquery?');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','getAllData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbacommon/getallTbaData/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','updateTemplateLayoutUrl' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/templatereportlayout/update/');

--15/10/202
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaUpdateMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbaupdateconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qa', 'latest','deleteTbaInquiryMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbainquiryconfig/deletemultiplerows');

update configmanager.properties set value ='layoutConfig,processLog,rulesConfig' where key ='filevalidator' and application='HOLMESAPPCONFIG-SERVICE';
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','deleteRulesMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/layoutrule/rulesconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','deleteMatchTbaMultipleEntity' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/tbamatchconfig/deletemultiplerows');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','deleteOutputReport' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/updateksdoutputdata/');
--Add New
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','db.service.dataBaseConfigMasterData' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/databaseconfig/databasedata');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','db.layout.deleteAllFiles' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/delete');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','sharedservice' , 'ksdFileDetails,orchKsdOutputFileDetails,notificationReportConfig,templateReportUpload');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','templateReportUpload' , 'common/template/loadEntityLazy/id/');
insert into configmanager.properties(application, profile , label ,key , value) values ('HOLMESAPPCONFIG-SERVICE', 'qc', 'latest','ksd.delete.allconfig' , 'http://holmes-zuulproxygateway:7777/dbservice/api/v1/common/deletebyfiletype');
--06/11/2020
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/redis/cache/set' where key ='setRedisCache' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbanoticemaster/findRecords/' where key ='tbaNotice.getNoticeMaesterData' and application='HOLMESAPPCONFIG-SERVICE';
update configmanager.properties set value ='http://holmes-zuulproxygateway:7777/dbservice/api/v1/tba/tbareruneventmaster/findRecords/' where key ='tbaReRun.getEventHistoryMasterData' and application='HOLMESAPPCONFIG-SERVICE';
