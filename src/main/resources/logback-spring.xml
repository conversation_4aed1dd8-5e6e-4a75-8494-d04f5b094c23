<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<springProperty scope="context" name="springAppName"
		source="spring.application.name" />
	<springProperty scope="context" name="serverName"
		source="HOSTNAME" />

	<appender name="CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>
				%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [Traceid: %X{X-B3-TraceId:-}][SpanId:
				%X{X-B3-SpanId:-}][ParentSpan: %X{X-B3-ParentSpanId:-}][Span Export:
				%X{X-Span-Export:-}] %m%n
			</Pattern>
		</layout>
	</appender>
	<appender name="FIPC_APP_CONFIG_SERVICE_APPENDER"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>../logs/fipc_appconfig.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>../logs/fipc_appconfig.%d{yyyy-MM-dd}.%i.log
			</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>50MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>10</maxHistory>
		</rollingPolicy>
		<encoder
			class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
			<providers>
				<pattern>
					<pattern>
						{
						"timestamp": "%d",
						"severity": "%level",
						"service": "${springAppName}",
						"trace": "%X{X-B3-TraceId:-}",
						"span": "%X{X-B3-SpanId:-}",
						"parent": "%X{X-B3-ParentSpanId:-}",
						"exportable": "%X{X-Span-Export:-}",
						"pid": "${PID:-}",
						"thread": "%thread",
						"class": "%logger{40}",
						"method": "%M",
						"Exception": "%ex",
						"LogMessage": "%message",
						"serverName" : "${serverName:-${HOSTNAME}}"
						}

					</pattern>
				</pattern>
			</providers>
		</encoder>
	</appender>

	<logger name="com.wipro.fipc" level="info" additivity="false">
		<appender-ref ref="FIPC_APP_CONFIG_SERVICE_APPENDER" />
	</logger>
	<logger name="org.springframework" level="INFO">
        <appender-ref ref="FIPC_APP_CONFIG_SERVICE_APPENDER" />
    </logger>
	
	<include resource="logback-common-utils.xml"/>
	
</configuration>
