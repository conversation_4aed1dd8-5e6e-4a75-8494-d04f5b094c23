#!/usr/bin/env groovy
def gv
@Library('alight-library')_
pipeline {

 agent { label 'deployDV||deployFipcQC'  }

  stages {
        stage("load vars") {
            steps {
                script {
                   gv = load "env.groovy"
                   gv.var()
                }
            }
         }
        stage('Pull Source') {
            steps {
               git branch: "${GIT_BRANCH}", credentialsId: 'githubtest' , url: "${gv.git_url}"
        }
      }
    stage('Maven Clean Dev') {
      when { branch 'develop' }
      steps {
        sh "if [ -f \"pom.xml\" ];then mvn_fipc_dev clean install;else echo \"This is not a Java Project\";fi"
       }
    }
    stage('Maven Clean QA') {
      when { branch 'qa' }
      steps {
        sh "if [ -f \"pom.xml\" ];then mvn_fipc clean install;else echo \"This is not a Java Project\";fi"
       }
    }
    stage('Maven Clean QC') {
            when {
                beforeAgent true
                branch 'qc'
            }
      agent { label 'deployFipcQC' }
      steps {
        sh "if [ -f \"pom.xml\" ];then mvn clean install;else echo \"This is not a Java Project\";fi"
       }
    }
    stage('Maven Clean Prod') {
      when {
        beforeAgent true
        branch 'prod' 
      }
      agent { label 'deployFipcQC' }
      steps {
        sh "if [ -f \"pom.xml\" ];then mvn_fipc_prod clean install;else echo \"This is not a Java Project\";fi"
       }
    }

    stage('Docker Build Dev') {
      when { branch 'develop' }
      steps {
        sh '''docker login -u AWS https://643650824530.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --profile fipc --region us-east-1)'''       
        sh "docker build --no-cache --build-arg JAVA_BASE_IMAGE=${gv.jfrog_repo_develop}/${gv.base_image} -t ${gv.jfrog_repo_develop}/${gv.image_name}:${gv.image_tag_develop} ."
      }
    }
    stage('Docker Build QA') {
      when { branch 'qa' }
      steps {
        sh '''docker login -u AWS https://966085516613.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --profile fipcqa --region us-east-1)'''
       sh "docker build --no-cache --build-arg JAVA_BASE_IMAGE=${gv.jfrog_repo_qa}/${gv.base_image} -t ${gv.jfrog_repo_qa}/${gv.image_name}:${gv.image_tag_qa} ."
      }
    }
    stage('Docker Build QC') {
      when {
        beforeAgent true
        branch 'qc' 
      }
      agent { label 'deployFipcQC' }
      steps {
        sh '''docker login -u AWS https://851661745492.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --profile fipcqc --region us-east-1)'''
        sh "docker build --no-cache --build-arg JAVA_BASE_IMAGE=${gv.jfrog_repo_qc}/${gv.base_image} -t ${gv.jfrog_repo_qc}/${gv.image_name}:${gv.image_tag_qc} ."
      }
    }
    stage('Docker Build Prod') {
     when {
        beforeAgent true
        branch 'prod' 
      }
      agent { label 'deployFipcQC' }
      
      steps {
        sh '''docker login -u AWS https://570098832650.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --profile fipcprod --region us-east-1)'''
        sh "docker build --no-cache --build-arg JAVA_BASE_IMAGE=${gv.jfrog_repo_prod}/${gv.base_image} -t ${gv.jfrog_repo_prod}/${gv.image_name}:${gv.image_tag_prod} ."
      }
    }

    stage('Docker Push Dev') {
        when { branch 'develop' }
      steps {
        sh '''docker login -u AWS https://643650824530.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --region us-east-1)''' 
        sh "docker push ${gv.jfrog_repo_develop}/${gv.image_name}:${gv.image_tag_develop}"
        }
    }
    stage('Docker Push QA') {
        when { branch 'qa' }
      steps {
        sh '''docker login -u AWS https://966085516613.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --profile=fipcqa --region us-east-1)''' 
        sh "docker push ${gv.jfrog_repo_qa}/${gv.image_name}:${gv.image_tag_qa}"
        }
    }
    stage('Docker Push QC') {
     when {
        beforeAgent true
        branch 'qc' 
      }
      agent { label 'deployFipcQC' }
      steps {
        sh '''docker login -u AWS https://851661745492.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --profile fipcqc --region us-east-1)'''
        sh "docker push ${gv.jfrog_repo_qc}/${gv.image_name}:${gv.image_tag_qc}"
        }
    }
    stage('Docker Push Prod') {
      when {
        beforeAgent true
        branch 'prod' 
      }
      agent { label 'deployFipcQC' }
      steps {
        sh '''docker login -u AWS https://570098832650.dkr.ecr.us-east-1.amazonaws.com -p $(aws ecr get-login-password --profile fipcprod --region us-east-1)'''
        sh "docker push ${gv.jfrog_repo_prod}/${gv.image_name}:${gv.image_tag_prod}"
        }
    }

      stage('Trigger Deployment Job Dev') {
    when { branch 'develop' }
      steps {
         build job: 'FIPC_Deployment_Dev/ECS-Deployment', parameters: [string(name: 'Service', value: "${gv.image_name_develop}")]
        }
      }
      stage('Trigger Deployment Job QA') {
    when { branch 'qa' }
      steps {
         build job: 'FIPC_Deployment_QA/ECS-Deployment', parameters: [string(name: 'Service', value: "${gv.image_name}")]
        }
      }
      stage('Trigger Deployment Job QC') {
      when {
        beforeAgent true
        branch 'qc' 
      }
      agent { label 'deployFipcQC' }
      steps {
         build job: 'FIPC_QC_Deployment/ECS-Deployment', parameters: [string(name: 'Service', value: "${gv.image_name}")]
        }
      }
}
post {
    always {
        mail to: '<EMAIL>',
        subject: "Build Status:${currentBuild.fullDisplayName}- ${currentBuild.result}",
        body: "Check console log at : ${env.BUILD_URL}"
      deleteDir() /* cleanup the workspace */
    }
  }
}
